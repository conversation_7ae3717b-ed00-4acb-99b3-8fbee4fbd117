package com.extracme.evcard.membership.core.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class IdentityCertInfo implements Serializable {
    private Long id;

    private String mid;

    /**
     * 提交渠道
     */
    private String appKey;

    /**
     * 证件编号
     */
    private String identityNo;

    /**
     * 证件类型： 1:居民身份证 2:外籍护照 3:港澳通行证 4:台湾通行证 5:军人身份证
     */
    private Integer identityType;

    /**
     * 姓名
     */
    private String name;

    /**
     * 到期时间类别： 1非长期 2长期
     */
    private Integer expireType;

    /**
     * 到期日期，格式yyyy-MM-dd
     */
    private String expirationDate;

    /**
     * 证件正面图片(完整路径)
     */
    private String identityCardImgUrl;

    /**
     * 证件反面图片(完整路径)
     */
    private String reverseIdentityCardImgUrl;
    /**
     * 手持证件图片(完整路径)
     */
    private String holdCardImgUrl;

    /**
     * 人脸照片(完整路径)
     */
    private String faceImgUrl;

    /**
     * 证件录入方式：1:OCR(不需要人工审核) 2:人工修改(需要人工审核)
     */
    private Integer certInputType;

    /**
     * 身份认证状态： 1未认证(未上传) 2未刷脸 3待认证(待人工认证) 4已认证 5认证不通过
     */
    private Integer authenticationStatus;

    /**
     * 证件提交时间
     */
    private Date submitTime;

    /**
     * 证件审核人
     */
    private String reviewUser;

    /**
     * 证件审核时间
     */
    private Date reviewTime;

    /**
     * 证件审核不通过项：
     * 共7位，每一位：0未审核 1：审核通过 2：审核不通过
     * 从左往右： 证件编号、姓名 、过期日期、身份证正页(护照/通行证)、身份证副页、手持证件照、人脸照片
     */
    private String reviewItems;

    /**
     * 审核不通过概要编号, 多个以逗号分隔
     * 多个以英文逗号拼接，如2,6,3
     * 1.证件编号、2.姓名 、3.过期日期、4.身份证正页(护照/通行证)、5.身份证副页、6.手持证件照、7.人脸照片
     */
    private String reviewIds;

    /**
     * 证件审核不通原因明细：
     * 工作人员选择的详细不通过项，与review_remark一起作为最终不通过原因
     */
    private String reviewItemNames;

    /**
     * 不通过原因
     */
    private String reviewRemark;

    /**
     * 审核方式  1手动审核  2自动审核
     */
    private Integer reviewMode;

    /**
     * 状态汇总处理：认证状态 1:未认证、2:未刷脸、3:待认证、4:已认证、5:认证失败、6:即将过期（剩余90天）、7:已过期
     */
    private int state;

    /**
     * 审核项汇总处理
     * 证件编号、姓名 、过期日期、身份证正页(护照/通行证)、身份证副页、手持证件照、人脸照片
     */
    private String auditIsError;
}
