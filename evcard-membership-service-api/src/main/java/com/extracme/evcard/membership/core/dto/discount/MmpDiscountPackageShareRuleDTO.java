package com.extracme.evcard.membership.core.dto.discount;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class MmpDiscountPackageShareRuleDTO implements Serializable {
    private Long id;

    /**
     * 企业id
     */
    private String agencyId;

    /**
     * 不共享套餐列表
     */
    private List<String> packageTypeList;

    private Integer shareType;

    private String miscDesc;

    private Integer status;

    private Date createTime;

    private Long createOperId;

    private String createOperName;

    private Date updateTime;

    private Long updateOperId;

    private String updateOperName;
}