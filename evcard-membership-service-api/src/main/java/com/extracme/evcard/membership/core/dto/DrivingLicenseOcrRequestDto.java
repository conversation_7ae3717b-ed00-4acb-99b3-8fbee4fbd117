package com.extracme.evcard.membership.core.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class DrivingLicenseOcrRequestDto implements Serializable {

    /**
     * 图片信息
     * 与图片路径二选一
     */
    private byte[] image;

    /**
     * 图片完整URL, 与图片路径二选一
     */
    private String url;

    /**
     * 是否检测朝向
     * 默认 false
     */
    private Boolean detect_direction;

    /**
     * 是否进行归一化处理
     * 默认 false
     */
    private Boolean unified_valid_period;

    /**
     * - front：默认值，识别驾驶证正页
     * - back：识别驾驶证副页
     */
    private String driving_license_side;
}
