package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 列表页组件数据
 * </p>
 *
 * <AUTHOR>
 * @since 2017/07/27
 */
public class ListPageDto implements Serializable ,Comparable<ListPageDto> {

    private static final long serialVersionUID = -4960284724500466503L;
    /** 押金 */
    private BigDecimal depositAmount;
    /** 基础押金 */
    private BigDecimal baseDeposit;
    /** 车辆押金 */
    private BigDecimal vehicleDeposit;
    /** 押金当前级别描述 */
    private String depositLevelDesc;
    /** 押金当前级别 */
    private Integer depositLevel;
    /** 是否可以勾选  0 否 1 是 */
    private Integer isEnableSelect = 0;
    /** 标准车辆押金值 */
    private BigDecimal standardVehicleDeposit;

    public BigDecimal getDepositAmount() {
        return depositAmount;
    }

    public void setDepositAmount(BigDecimal depositAmount) {
        this.depositAmount = depositAmount;
    }

    public BigDecimal getBaseDeposit() {
        return baseDeposit;
    }

    public void setBaseDeposit(BigDecimal baseDeposit) {
        this.baseDeposit = baseDeposit;
    }

    public BigDecimal getVehicleDeposit() {
        return vehicleDeposit;
    }

    public void setVehicleDeposit(BigDecimal vehicleDeposit) {
        this.vehicleDeposit = vehicleDeposit;
    }

    public String getDepositLevelDesc() {
        return depositLevelDesc;
    }

    public void setDepositLevelDesc(String depositLevelDesc) {
        this.depositLevelDesc = depositLevelDesc;
    }

    public Integer getDepositLevel() {
        return depositLevel;
    }

    public void setDepositLevel(Integer depositLevel) {
        this.depositLevel = depositLevel;
    }

    public Integer getIsEnableSelect() {
        return isEnableSelect;
    }

    public void setIsEnableSelect(Integer isEnableSelect) {
        this.isEnableSelect = isEnableSelect;
    }

    public BigDecimal getStandardVehicleDeposit() {
        return standardVehicleDeposit;
    }

    public void setStandardVehicleDeposit(BigDecimal standardVehicleDeposit) {
        this.standardVehicleDeposit = standardVehicleDeposit;
    }

    @Override
    public int compareTo(ListPageDto o) {
        return o.getIsEnableSelect().compareTo(this.getIsEnableSelect()) ;
    }
}
