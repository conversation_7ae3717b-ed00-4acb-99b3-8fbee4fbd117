package com.extracme.evcard.membership.core.exception;


import com.extracme.evcard.rpc.exception.BusinessException;

/**
 * 会员注销异常
 */
public class UnregisterException extends BusinessException {

    public UnregisterException(int code, String message) {
        super(code, message);
    }

    public static UnregisterException PARAM_ERROR = new UnregisterException(300001, "传入参数错误");

    //安全相关
    public static UnregisterException MEM_PWD_MOBILE_UNSTABLE_EXP = new UnregisterException(300007, "最近14个自然日内修改过密码/手机号");
    public static UnregisterException MEM_IMEI_CHANGED_EXP = new UnregisterException(300008, "最近14个自然日内更换过设备");

    //会员状态
    public static UnregisterException MEM_BLACK_LIST_EXP = new UnregisterException(300002, "会员在黑名单中");
    public static UnregisterException MEM_CARD_NOT_ACTIVE_EXP = new UnregisterException(300003, "会员卡未激活或已暂停");
    public static UnregisterException MEM_ECOUNT_NOT_EMPTY_EXP = new UnregisterException(300004, "剩余E币数量大于0");
    public static UnregisterException MEM_BASIC_DEPOSIT_EXIST_EXP = new UnregisterException(300005, "基础押金(现金及预授权)大于0");
    public static UnregisterException MEM_VEH_DEPOSIT_EXIST_EXP = new UnregisterException(300006, "车辆押金(现金及预授权)大于0");
    public static UnregisterException MEM_SESAME_CREDIT_EXP = new UnregisterException(300012, "芝麻信用代扣授权中");

    //订单相关
    public static UnregisterException MEM_OUT_STANDING_ORDER_EXP = new UnregisterException(300010, "存在未完成的订单");
    public static UnregisterException MEM_LASTEST_ORDER_EXP = new UnregisterException(300016, "末次订单完成未超过指定时间间隔");

    //关联事项
    public static UnregisterException MEM_REF_RISKORDER_EXP = new UnregisterException(300011, "存在未处理的违章/违章违约金/维修/逾期租车费等风控订单");
    public static UnregisterException MEM_REF_PAY_TASK_EXP = new UnregisterException(300013, "存在未处理的关联付款申请单");
    public static UnregisterException MEM_REF_ACCIDNET_TASK_EXP = new UnregisterException(300014, "存在未处理的调度关联事故任务");
    public static UnregisterException MEM_REF_REPAIR_TASK_EXP = new UnregisterException(300015, "存在未处理的关联的维修任务");
    public static UnregisterException ORDER_DEPOSIT_NOT_EMPTY = new UnregisterException(300016, "订单押金押金担保中");
    public static UnregisterException DEPOSIT_BALANCE_NOT_EMPTY = new UnregisterException(300017, "押金余额押金担保中");
    public static UnregisterException ZHIMA_DEPOSIT_NOT_EMPTY = new UnregisterException(300018, "芝麻免押押金担保中");
    public static UnregisterException MEMBER_NOT_EXIST = new UnregisterException(300000, "会员不存在");
    public static UnregisterException MEMBER_UNREGISTERED = new UnregisterException(300098, "会员已注销");
    public static UnregisterException UNREGISTER_FAILED = new UnregisterException(300099, "注销失败");


    /**
     * 账号恢复相关
     */
    public static UnregisterException UNREGISTER_RECOVER_FAILED = new UnregisterException(300100, "账号信息恢复失败");
    public static UnregisterException MEMBER_NOT_UNREGISTERED = new UnregisterException(300101, "账号未注销，无需恢复");
    public static UnregisterException MEMBER_OVER_FROZEN = new UnregisterException(300102, "账号不存在或已过冻结期，请重新注册");
    public static UnregisterException MEMBER_MOBILE_USED = new UnregisterException(300103, "手机号已被使用，账号恢复失败");
    public static UnregisterException MEMBER_NOT_ACTIVE = new UnregisterException(300104, "账号不存在或已过冻结期");

}
