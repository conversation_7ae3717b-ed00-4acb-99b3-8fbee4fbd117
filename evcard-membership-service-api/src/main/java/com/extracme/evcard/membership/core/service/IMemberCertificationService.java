package com.extracme.evcard.membership.core.service;


import com.extracme.evcard.membership.core.dto.*;
import com.extracme.evcard.membership.core.dto.input.AuditIdCardInput;
import com.extracme.evcard.membership.core.dto.input.MemberReAuditInput;
import com.extracme.evcard.membership.core.dto.input.OfcSubmitFacePicInput;
import com.extracme.evcard.membership.core.dto.input.SubmitFaceImgSepInput;
import com.extracme.evcard.membership.core.exception.AuthenticationException;
import com.extracme.evcard.membership.core.input.*;
import com.extracme.evcard.membership.core.dto.UpdateLicenceReviewDto;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.dto.PageBeanDto;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;

/**
 * 用户认证服务
 * 身份证及驾照认证
 * @since 2022.9
 */
public interface IMemberCertificationService {

    /**
     * 大陆/军官身份证OCR
     */
    IdCardOcrRespDto idCardOcr(IdCardOcrInput input);

    /**
     * 大陆/军官驾照OCR
     */
    DriverLicenseOcrRespDto driverLicenseOcr(DriverLicenseOcrInput input);

    /**
     * 获取身份认证信息
     */
    IdentityCertInfo getIdentityCertInfo(String mid);

    /**
     * 获取驾照认证信息
     */
    DriverLicenseCertInfo getDriverLicenseInfo(String mid);

    /**
     * 提交身份证件
     * 大陆/大陆军官&外籍&港澳台
     * @remark Deprecated IMembershipService.submitUserIDCardPic
     */
    SubmitIdCardRespDto submitUserIdCard(SubmitIdCardInput input) throws AuthenticationException;


    /**
     * 提交驾照
     * 大陆/大陆军官&外籍&港澳台
     * @remark Deprecated IMembershipService.submitUserIDCardPic
     */
    SubmitDriverLicenseRespDto submitDriverLicense(SubmitDriverLicenseInput input) throws AuthenticationException;


    /**
     * 身份证人工审核通过
     */
    DefaultServiceRespDTO passAuditIdentity(AuditIdCardInput input);


    /**
     * 身份证人工审核不通过
     */
    DefaultServiceRespDTO notPassAuditIdentity(AuditIdCardInput input);


    /**
     * 身份证人工审核重新审核
     */
    DefaultServiceRespDTO reAuditIdentity(AuditIdCardInput input);


    /**
     * 驾照人工审核通过/不通过/重新审核
     */
    BaseResponse updateDrivingLicenceReviewStatus(UpdateLicenceReviewDto updateAuthIdItemsDTO);

    /**
     *
     * 身份证、驾驶证重新认证
     * @param input
     * @return
     */
    DefaultServiceRespDTO reAuditMember(MemberReAuditInput input);

    /**
     * 身份证/驾照认证日志
     */

    PageBeanDto<MemberAuthLogDTO> queryAuditLogs(int type, Integer pageSize, Integer pageNumber, String mid);

    /**
     * 根据会员的身份证认证和驾驶证审核状态，返回是否能进行相关交易（目前充值押金交易会调用）
     * 门店：身份证处于待认证和已认证的状态下
     * 大库：身份证和驾照都处于已认证的状态下
     *
     * @param authId         证件号
     * @param membershipType 会员类型：0-外部会员、1-内部员工
     * @param cityName 定位城市
     * @return
     */
    JudgeIfCanTradeResp judgeIfCanTrade(String authId, Short membershipType,String cityName);

    /**
     * 第三方渠道人脸认证后提交审核
     * 用于兼容evcard-mas中第三方认证渠道的人脸照片提交业务
     * 渠道： 微信小程序、支付宝小程序
     * @param input
     * @return
     * @throws AuthenticationException
     */
    BaseResponse thirdSubmitFaceImgToReview(SubmitFaceImgSepInput input) throws AuthenticationException;


    /**
     * 履约小程序
     * 提交人脸识别图片 图片流加解密  上传人脸图片  人脸图片身份校验  更新用户人脸图片
     *
     * @param input  forceSuccess true 强制人脸成功
     * @return
     * @throws AuthenticationException
     */
    BaseResponse ofcSubmitFacePic(OfcSubmitFacePicInput input) throws AuthenticationException;


    /**
     * ocr识别 再提交身份证件
     * ocr失败，会给默认值
     */
    OcrAndSubmitIdentityRespDto ocrAndSubmitIdentity(OcrAndSubmitIdentityInput input);

    /**
     * ocr识别 再提交 驾照
     * ocr失败，会给默认值
     */
    OcrAndSubmitLicenseRespDto ocrAndSubmitLicense(OcrAndSubmitLicenseInput input);
}
