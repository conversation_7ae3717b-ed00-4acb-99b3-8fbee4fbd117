package com.extracme.evcard.membership.credit.dto;

import com.extracme.evcard.rpc.dto.BaseResponse;
import lombok.Data;

/**
 * 积分发放结果
 * <AUTHOR>
 * @Discription
 * @date 2020/8/12
 */
@Data
public class MemberPointsOfferResp extends BaseResponse {
    /**
     * 积分发放编号
     */
    private String payOrderId;

    /**
     * 是否发放成功
     */
    private Boolean success;

    /**
     * 发放状态 1成功 2失败
     */
    private String offerStatus;

    /**
     * 是否需要重试  0不需要 1需要重试
     */
    private Integer retry;

    /**
     * 发放的积分数量
     */
    private Integer gainPoints;

    /**
     * 积分过期时间, yyyyMMddHHmmss
     */
    private String expireDate;
}
