package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class FeedbackBaseDto implements Serializable, Comparable<FeedbackBaseDto> {
    private static final long serialVersionUID = -7909032724550409394L;

    //id
    private Long id;

    private String authId;

    private String name;

    private String mobilePhone;

    //上报类型 0 网点推荐 1 车辆故障 2 充电桩故障 3 意见反馈
    private Integer reportType;

    //网点地名 推荐建网点使用
    private String shopAddressName;

    //网点地址详细描述 推荐建网点使用
    private String shopAddressDesc;

    //上报信息描述
    private String feedbackDesc;

    //上报图片
    private String imageUrls;

    //车牌号
    private String vehicleNo;

    //网点编号
    private Integer shopSeq;

    //网点名称
    private String shopName;

    //车位号
    private String parkNum;

    //订单号
    private String orderSeq;

    //上报状态  0 未处理 1 已处理 2 无需处理
    private Integer reportStatus;

    //消息类型 0 用户上报信息 1 回复信息
    private Integer infoType;

    //上报时间
    private String createTime;

    //回复消息
    private List<FeedbackReplyMess> replyMessage;


    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public Integer getReportType() {
        return reportType;
    }

    public void setReportType(Integer reportType) {
        this.reportType = reportType;
    }

    public String getShopAddressName() {
        return shopAddressName;
    }

    public void setShopAddressName(String shopAddressName) {
        this.shopAddressName = shopAddressName;
    }

    public String getShopAddressDesc() {
        return shopAddressDesc;
    }

    public void setShopAddressDesc(String shopAddressDesc) {
        this.shopAddressDesc = shopAddressDesc;
    }

    public String getFeedbackDesc() {
        return feedbackDesc;
    }

    public void setFeedbackDesc(String feedbackDesc) {
        this.feedbackDesc = feedbackDesc;
    }

    public String getImageUrls() {
        return imageUrls;
    }

    public void setImageUrls(String imageUrls) {
        this.imageUrls = imageUrls;
    }

    public String getVehicleNo() {
        return vehicleNo;
    }

    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }

    public Integer getShopSeq() {
        return shopSeq;
    }

    public void setShopSeq(Integer shopSeq) {
        this.shopSeq = shopSeq;
    }

    public String getParkNum() {
        return parkNum;
    }

    public void setParkNum(String parkNum) {
        this.parkNum = parkNum;
    }

    public String getOrderSeq() {
        return orderSeq;
    }

    public void setOrderSeq(String orderSeq) {
        this.orderSeq = orderSeq;
    }

    public Integer getReportStatus() {
        return reportStatus;
    }

    public void setReportStatus(Integer reportStatus) {
        this.reportStatus = reportStatus;
    }

    public Integer getInfoType() {
        return infoType;
    }

    public void setInfoType(Integer infoType) {
        this.infoType = infoType;
    }

    public List<FeedbackReplyMess> getReplyMessage() {
        return replyMessage;
    }

    public void setReplyMessage(List<FeedbackReplyMess> replyMessage) {
        this.replyMessage = replyMessage;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    @Override
    public int compareTo(FeedbackBaseDto o) {
        if(this.getCreateTime().compareTo(o.getCreateTime()) > 0){
            return -1;
        }else if(this.getCreateTime().compareTo(o.getCreateTime()) < 0){
            return 1;
        }else {
            return 0;
        }
    }
}
