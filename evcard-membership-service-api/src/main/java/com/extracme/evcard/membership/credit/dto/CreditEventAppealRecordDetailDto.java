package com.extracme.evcard.membership.credit.dto;

import com.extracme.evcard.rpc.dto.BaseResponse;

import java.util.Date;

/**
 * Created by Elin on 2017/11/22.
 * 事件申诉记录详情
 */
public class CreditEventAppealRecordDetailDto extends BaseResponse {

    private static final long serialVersionUID = 2077919426541175867L;

    /**
     * 事件编号
     */
    private Long eventId;

    /**
     * 会员id
     */
    private String authId;

    /**
     * 申诉编号
     */
    private Long appealId;

    /**
     * 申诉时间
     */
    private Date appealTime;

    /**
     * 申诉状态（0-未申诉 1 已申诉）
     */
    private Integer appealStatus;

    /**
     * 申诉描述
     */
    private String appealDesc;

    /**
     * 申诉凭证上传图片路径，多张以逗号分隔
     */
    private String appealImagePath;

    /**
     * 申诉凭证 文件路径
     */
    private String appealFilePath;

    /**
     * 处理人id
     */
    private Long handleUserId;
    /**
     * 处理人姓名
     */
    private String handleUser;
    /**
     * 处理时间
     */
    private Date handleTime;

    /**
     * 通知话术
     */
    private String handleRemark;

    /**
     * 处理结果0-未处理 1-同意 2-驳回
     */
    private Integer handleResult;

    public Long getEventId() {
        return eventId;
    }

    public void setEventId(Long eventId) {
        this.eventId = eventId;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public Long getAppealId() {
        return appealId;
    }

    public void setAppealId(Long appealId) {
        this.appealId = appealId;
    }

    public Date getAppealTime() {
        return appealTime;
    }

    public void setAppealTime(Date appealTime) {
        this.appealTime = appealTime;
    }

    public Integer getAppealStatus() {
        return appealStatus;
    }

    public void setAppealStatus(Integer appealStatus) {
        this.appealStatus = appealStatus;
    }

    public String getAppealDesc() {
        return appealDesc;
    }

    public void setAppealDesc(String appealDesc) {
        this.appealDesc = appealDesc;
    }

    public String getAppealImagePath() {
        return appealImagePath;
    }

    public void setAppealImagePath(String appealImagePath) {
        this.appealImagePath = appealImagePath;
    }

    public String getAppealFilePath() {
        return appealFilePath;
    }

    public void setAppealFilePath(String appealFilePath) {
        this.appealFilePath = appealFilePath;
    }

    public Long getHandleUserId() {
        return handleUserId;
    }

    public void setHandleUserId(Long handleUserId) {
        this.handleUserId = handleUserId;
    }

    public String getHandleUser() {
        return handleUser;
    }

    public void setHandleUser(String handleUser) {
        this.handleUser = handleUser;
    }

    public Date getHandleTime() {
        return handleTime;
    }

    public void setHandleTime(Date handleTime) {
        this.handleTime = handleTime;
    }

    public String getHandleRemark() {
        return handleRemark;
    }

    public void setHandleRemark(String handleRemark) {
        this.handleRemark = handleRemark;
    }

    public Integer getHandleResult() {
        return handleResult;
    }

    public void setHandleResult(Integer handleResult) {
        this.handleResult = handleResult;
    }
}
