package com.extracme.evcard.membership.core.dto.agency;

import com.baosight.imap.json.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class DiscountRuleDTO implements Serializable {
    private static final long serialVersionUID = -9026823818805545106L;
    /**
     * 数据库主键ID，执行save之后赋值传递给个人折扣规则表做关联
     */
    private Long id;
    /**
     * 企业会员机构id
     */
    private String agencyId;

    /**
     * 分段折扣，2-标识广铁会员，1或null表示普通
     */
    private Integer discountRule;

    /**
     * 0-企业折扣；1-个人折扣
     */
    private Integer discountType;

    /**
     * 折扣率（淡季折扣 通用折扣）
     */
    private Double discountRate;

    /**
     *  旺季折扣率
     */
    private Double peakSeasonDiscountRate;

    /**
     * 受益人数
     */
    private Integer beneficiaryNumber;

    /**
     * 生效时间
     */
    @JsonFormat(timezone="GMT+8", pattern="yyyy-MM-dd")
    private Date validStartTime;

    /**
     * 结束时间
     */
    @JsonFormat(timezone="GMT+8", pattern="yyyy-MM-dd")
    private Date validEndTime;

    /**
     * 个人折扣分级
     */
    private List<PersonalDiscountDTO> personalRules;


    public Double getPeakSeasonDiscountRate() {
        return peakSeasonDiscountRate;
    }

    public void setPeakSeasonDiscountRate(Double peakSeasonDiscountRate) {
        this.peakSeasonDiscountRate = peakSeasonDiscountRate;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAgencyId() {
        return agencyId;
    }

    public void setAgencyId(String agencyId) {
        this.agencyId = agencyId;
    }

    public Integer getDiscountRule() {
        return discountRule;
    }

    public void setDiscountRule(Integer discountRule) {
        this.discountRule = discountRule;
    }

    public Integer getDiscountType() {
        return discountType;
    }

    public void setDiscountType(Integer discountType) {
        this.discountType = discountType;
    }

    public Double getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(Double discountRate) {
        this.discountRate = discountRate;
    }

    public Integer getBeneficiaryNumber() {
        return beneficiaryNumber;
    }

    public void setBeneficiaryNumber(Integer beneficiaryNumber) {
        this.beneficiaryNumber = beneficiaryNumber;
    }

    public Date getValidStartTime() {
        return validStartTime;
    }

    public void setValidStartTime(Date validStartTime) {
        this.validStartTime = validStartTime;
    }

    public Date getValidEndTime() {
        return validEndTime;
    }

    public void setValidEndTime(Date validEndTime) {
        this.validEndTime = validEndTime;
    }

    public List<PersonalDiscountDTO> getPersonalRules() {
        return personalRules;
    }

    public void setPersonalRules(List<PersonalDiscountDTO> personalRules) {
        this.personalRules = personalRules;
    }

    @Override
    public String toString() {
        return "DiscountRuleDTO{" +
                "id=" + id +
                ", agencyId='" + agencyId + '\'' +
                ", discountRule=" + discountRule +
                ", discountType=" + discountType +
                ", discountRate=" + discountRate +
                ", beneficiaryNumber=" + beneficiaryNumber +
                ", validStartTime=" + validStartTime +
                ", validEndTime=" + validEndTime +
                ", personalRules=" + personalRules +
                '}';
    }
}
