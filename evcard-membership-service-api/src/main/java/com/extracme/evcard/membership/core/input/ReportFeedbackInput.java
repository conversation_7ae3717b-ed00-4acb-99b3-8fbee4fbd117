package com.extracme.evcard.membership.core.input;

import java.io.Serializable;

/**
 * <p>
 * <p> 问题上报 基础信息
 * </p>
 *
 * <AUTHOR>
 * @Date 2018/9/11 10:36
 */
public class ReportFeedbackInput implements Serializable {

    private static final long serialVersionUID = -6316820903875481135L;

    /**
     * 用户authId
     */
    private String authId;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 手机号码
     */
    private String mobilePhone;

    /**
     * 上报信息描述
     */
    private String feedbackDesc;

    /**
     * 上报图片
     */
    private String imageUrls;

    /**
     * 订单号 多个订单时,逗号分隔
     */
    private String orderSeq;

    /**
     * 纬度
     */
    private Long latitude;

    /**
     * 经度
     */
    private Long longitude;

    /**
     * 上报人
     */
    private String createOperName;

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getFeedbackDesc() {
        return feedbackDesc;
    }

    public void setFeedbackDesc(String feedbackDesc) {
        this.feedbackDesc = feedbackDesc;
    }

    public String getImageUrls() {
        return imageUrls;
    }

    public void setImageUrls(String imageUrls) {
        this.imageUrls = imageUrls;
    }

    public String getOrderSeq() {
        return orderSeq;
    }

    public void setOrderSeq(String orderSeq) {
        this.orderSeq = orderSeq;
    }

    public Long getLatitude() {
        return latitude;
    }

    public void setLatitude(Long latitude) {
        this.latitude = latitude;
    }

    public Long getLongitude() {
        return longitude;
    }

    public void setLongitude(Long longitude) {
        this.longitude = longitude;
    }

    public String getCreateOperName() {
        return createOperName;
    }

    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }
}
