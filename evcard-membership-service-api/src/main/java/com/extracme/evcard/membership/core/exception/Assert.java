package com.extracme.evcard.membership.core.exception;


import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;

public class Assert {

    public static void isTrue(boolean expression, String message) {
        if (!expression) {
            throw new RuntimeException(message);
        }
    }

    public static void isFalse(boolean expression, String message) {
        if (expression) {
            throw new RuntimeException(message);
        }
    }

    public static void isNull(Object object, String message) {
        if (object != null) {
            throw new RuntimeException(message);
        }
    }

    public static void isNotNull(Object object, String message) {
        if (object == null) {
            throw new RuntimeException(message);
        }
    }

    public static void isNotEmpty(String str, String message) {
        if (StringUtils.isEmpty(str)) {
            throw new RuntimeException(message);
        }
    }

    public static void isNotEmpty(Collection<?> col, String message) {
        if (CollectionUtils.isEmpty(col)) {
            throw new RuntimeException(message);
        }
    }

    public static void isNotBlank(String str, String message) {
        if (StringUtils.isBlank(str)) {
            throw new RuntimeException(message);
        }
    }

    public static void isNotEmpty(Object[] array, String message) {
        if (ArrayUtils.isEmpty(array)) {
            throw new RuntimeException(message);
        }
    }

    public static void isEmpty(Object[] array, String message) {
        if (ArrayUtils.isNotEmpty(array)) {
            throw new RuntimeException(message);
        }
    }

    public static void noNullElements(Object[] array, String message) {
        if (array != null) {
            for (int i = 0; i < array.length; i++) {
                if (array[i] == null) {
                    throw new RuntimeException(message);
                }
            }
        }
    }

    public static void notEquals(String str1, String str2, String message) {
        if (StringUtils.equals(str1, str2)) {
            throw new RuntimeException(message);
        }
    }

    public static void equals(String str1, String str2, String message) {
        if (!StringUtils.equals(str1, str2)) {
            throw new RuntimeException(message);
        }
    }

    public static void notEquals(int a1, int a2, String message) {
        if (a1 == a2) {
            throw new RuntimeException(message);
        }
    }

    public static void equals(int a1, int a2, String message) {
        if (a1 != a2) {
            throw new RuntimeException(message);
        }
    }
}
