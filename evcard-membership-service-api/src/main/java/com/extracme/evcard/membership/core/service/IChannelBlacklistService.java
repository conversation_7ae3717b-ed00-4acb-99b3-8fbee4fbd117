package com.extracme.evcard.membership.core.service;

import com.extracme.evcard.membership.core.dto.blacklist.*;

import java.util.List;

public interface IChannelBlacklistService {

    /**
     * 获取黑名单列表
     * @param dto
     * @return
     */
    ListChannelBlacklistResponse listChannelBlacklist(ListChannelBlacklistDto dto);

    /**
     * 插入黑名单
     * @param dto
     * @return
     */
    Integer insert(UpdateChannelBlacklistDto dto);

    /**
     * 更新黑名单
     * @param dto
     * @return
     */
    Integer updateById(UpdateChannelBlacklistDto dto);
    /**
     * 更新黑名单-通过证件号和手机号，仅用于更新是否启用
     * @param dto
     * @return
     */
    Integer updateByCertificateNumAndPhone(UpdateChannelBlacklistDto dto);

    /**
     * 根据id获取黑名单
     * @param id
     * @return
     */
    ChannelBlacklistDto getById(Integer id);

    /**
     * 判断是否在黑名单
     * @param mobilePhone
     * @param certificateNum
     * @return
     */
    Boolean judgeBlacklist(String mobilePhone, String certificateNum, String contractId, int type, String userName);

    /**
     * 获取黑白名单日志（分页）
     * @param dto
     * @return
     */
    ListChannelBlacklistLogResponse listChannelBlacklistLog(ListChannelBlacklistLogDto dto);

    Integer insertOrUpdate(UpdateChannelBlacklistDto dto);
}
