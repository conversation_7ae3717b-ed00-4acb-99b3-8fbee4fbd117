package com.extracme.evcard.membership.core.dto.input;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2018/7/13
 */
public class QueryUpgradeDepositInputDTO implements Serializable{

    private static final long serialVersionUID = 2267852973140868605L;

    private BigDecimal baseDepositAmount;

    private BigDecimal vehicleDepositAmount;

    private BigDecimal drivingDepositAmount;

    private Integer vehicleModelSeq;

    public QueryUpgradeDepositInputDTO() {
    }

    public QueryUpgradeDepositInputDTO(BigDecimal baseDepositAmount, BigDecimal vehicleDepositAmount) {
        this.baseDepositAmount = baseDepositAmount;
        this.vehicleDepositAmount = vehicleDepositAmount;
    }

    public QueryUpgradeDepositInputDTO(BigDecimal baseDepositAmount, BigDecimal vehicleDepositAmount, Integer vehicleModelSeq) {
        this.baseDepositAmount = baseDepositAmount;
        this.vehicleDepositAmount = vehicleDepositAmount;
        this.vehicleModelSeq = vehicleModelSeq;
    }

    public BigDecimal getDrivingDepositAmount() {
        return drivingDepositAmount;
    }

    public void setDrivingDepositAmount(BigDecimal drivingDepositAmount) {
        this.drivingDepositAmount = drivingDepositAmount;
    }

    public BigDecimal getBaseDepositAmount() {
        return baseDepositAmount;
    }

    public void setBaseDepositAmount(BigDecimal baseDepositAmount) {
        this.baseDepositAmount = baseDepositAmount;
    }

    public BigDecimal getVehicleDepositAmount() {
        return vehicleDepositAmount;
    }

    public void setVehicleDepositAmount(BigDecimal vehicleDepositAmount) {
        this.vehicleDepositAmount = vehicleDepositAmount;
    }

    public Integer getVehicleModelSeq() {
        return vehicleModelSeq;
    }

    public void setVehicleModelSeq(Integer vehicleModelSeq) {
        this.vehicleModelSeq = vehicleModelSeq;
    }

    @Override
    public String toString() {
        return "QueryUpgradeDepositInputDTO{" +
                "baseDepositAmount=" + baseDepositAmount +
                ", vehicleDepositAmount=" + vehicleDepositAmount +
                ", vehicleModelSeq=" + vehicleModelSeq +
                '}';
    }
}
