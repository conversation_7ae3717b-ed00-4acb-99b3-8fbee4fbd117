package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * 会员折扣信息
 * <AUTHOR>
 * @date 2019/5/10
 */
public class MemberDiscountDTO implements Serializable {
    private static final long serialVersionUID = -8072401805297980671L;
    /**
     * 折扣记录id
     */
    private Long id;

    /**
     * 所属企业会员-机构id
     */
    private String agencyId;

    /**
     * 0-企业折扣；1-个人折扣
     */
    private Integer discountType;

    /**
     * 折扣率（单位%）
     */
    private Double discountRate;

    /**
     * 分段折扣，1 或 null表示普通 ， 2-标识广铁会员
     */
    private Integer discountRule;

    /**
     * 车牌限制
     */
    private String vehicleNoLimit;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAgencyId() {
        return agencyId;
    }

    public void setAgencyId(String agencyId) {
        this.agencyId = agencyId;
    }

    public Integer getDiscountRule() {
        return discountRule;
    }

    public void setDiscountRule(Integer discountRule) {
        this.discountRule = discountRule;
    }

    public Integer getDiscountType() {
        return discountType;
    }

    public void setDiscountType(Integer discountType) {
        this.discountType = discountType;
    }

    public Double getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(Double discountRate) {
        this.discountRate = discountRate;
    }

    public String getVehicleNoLimit() {
        return vehicleNoLimit;
    }

    public void setVehicleNoLimit(String vehicleNoLimit) {
        this.vehicleNoLimit = vehicleNoLimit;
    }

    @Override
    public String toString() {
        return "MemberDiscountDTO{" +
                "id=" + id +
                ", agencyId='" + agencyId + '\'' +
                ", discountType=" + discountType +
                ", discountRate=" + discountRate +
                ", discountRule=" + discountRule +
                ", vehicleNoLimit=" + vehicleNoLimit +
                '}';
    }
}
