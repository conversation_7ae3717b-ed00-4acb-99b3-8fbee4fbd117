package com.extracme.evcard.membership.core.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/21
 * @remark
 */
@Data
public class UserTitlePointsQueryDto implements Serializable {
    /**
     * 用户id
     */
    private String authId;

    /**
     * 称号大类： 0节碳称号
     */
    private int titleClass = 0;

    /**
     * 升级记录,
     * 每项记录中id非必须
     */
    private List<UserTitleUpgradeDto> upgradeRecords;
}
