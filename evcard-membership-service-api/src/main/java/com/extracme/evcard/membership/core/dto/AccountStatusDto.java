package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;
import java.util.Date;

public class AccountStatusDto implements Serializable {
        /**
         * 会员主键
         */
        private Long pkId;

        /**
         * 会员编号
         */
        private String authId;

        /**
         * 享道统一用户ID
         */
        private String uid;

        /**
         * 会员类别
         */
        private Short membershipType;

        /**
         * 审核状态(0:待审核 1：审核通过 2: 审核不通过 3：用户无效）
         */
        private Integer reviewStatus;

        /**
         * 会员状态(0：有效  1：无效)
         */
        private Integer userstatus;

        /**
         * 账号状态 0Normal  1已注销(冻结期内) 2已注销(冻结期外)
         */
        private Integer accountStatus;

        /**
         * 注销时间: yyyyMMddHHmmss
         */
        private String unregisterTime;

        /**
         * 手机号
         */
        private String mobilePhone;

        /**
         * 驾照号
         */
        private String driverCode;


        private String mid;

        public String getMid() {
                return mid;
        }

        public void setMid(String mid) {
                this.mid = mid;
        }

        public String getAuthId() {
                return authId;
        }

        public void setAuthId(String authId) {
                this.authId = authId;
        }

        public Short getMembershipType() {
                return membershipType;
        }

        public void setMembershipType(Short membershipType) {
                this.membershipType = membershipType;
        }

        public Integer getAccountStatus() {
                return accountStatus;
        }

        public void setAccountStatus(Integer accountStatus) {
                this.accountStatus = accountStatus;
        }

        public String getUnregisterTime() {
                return unregisterTime;
        }

        public void setUnregisterTime(String unregisterTime) {
                this.unregisterTime = unregisterTime;
        }

        public Long getPkId() {
                return pkId;
        }

        public void setPkId(Long pkId) {
                this.pkId = pkId;
        }

        public Integer getReviewStatus() {
                return reviewStatus;
        }

        public void setReviewStatus(Integer reviewStatus) {
                this.reviewStatus = reviewStatus;
        }

        public Integer getUserstatus() {
                return userstatus;
        }

        public void setUserstatus(Integer userstatus) {
                this.userstatus = userstatus;
        }

        public String getMobilePhone() {
                return mobilePhone;
        }

        public void setMobilePhone(String mobilePhone) {
                this.mobilePhone = mobilePhone;
        }

        public String getDriverCode() {
                return driverCode;
        }

        public void setDriverCode(String driverCode) {
                this.driverCode = driverCode;
        }

        public String getUid() {
                return uid;
        }

        public void setUid(String uid) {
                this.uid = uid;
        }
}
