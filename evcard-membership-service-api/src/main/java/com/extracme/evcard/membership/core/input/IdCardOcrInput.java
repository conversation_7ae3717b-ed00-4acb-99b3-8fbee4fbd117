package com.extracme.evcard.membership.core.input;

import lombok.Data;

import java.io.Serializable;

@Data
public class IdCardOcrInput implements Serializable {
    /**
     * 会员id
     */
    private String mid;

    /**
     * 渠道key
     */
    private String appKey;

    /**
     * 图片信息， 图片信息与与图片路径二选一
     */
    private byte[] image;

    /**
     * 图片完整URL, 若未提供图片信息，则使用此url
     * oss文件名 or 可下载的完整的oss地址
     */
    private String url;

    /**
     * 正反面类型 1:正面 2:反面
     */
    private int imageType;

    /**
     * 城市运营模式 0：大库 1：门店
     */
    private String operationModel;

    /**
     * 证件类型 1：居民身份证 2：军人身份证
     */
    private int identityType;
}
