package com.extracme.evcard.membership.core.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/11/20
 */
@Data
public class ProvisionDetailDto implements Serializable {
    /**
     * 条款记录id
     */
    private Long id;

    /**
     * 条款类型：（1：会员守则 2：隐私政策 3：Q&A）
     */
    private Integer provisionType;

    /**
     * 版本号
     */
    private String version;

    /**
     * 条款文件地址(pdf)-oss相对路径
     * 会员守则与隐私条款为pdf文件， /member_provision/SZ0092.pdf
     * 常见问题为html
     */
    private String provisionAddress;

    /**
     * word文件上传地址-oss相对路径
     * /member_provision/SZ0071.docx
     */
    private String wordAddress;

    /**
     * pdf转图片多张以，分隔-oss相对路径
     * /member_provision/SZ0086/SZ00860.png,/member_provision/SZ0086/SZ00861.png
     */
    private String provisionPic;

    /**
     * 条款状态（1：待发布 2：使用中 3：已停止）
     */
    private Integer provisionStatus;

    /**
     * 生效起始时间
     */
    private Date startTime;

    /**
     * 生效结束时间
     */
    private Date endTime;


    /**
     * 发布人ID
     */
    private Long publisherId;

    /**
     * 更新内容描述
     */
    private String content;
}
