package com.extracme.evcard.membership.core.input;

import com.extracme.evcard.rpc.dto.Page;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2019/6/20
 */
public class QuerySimpleMemberInput implements Serializable{

    private static final long serialVersionUID = -2587701739300196934L;

    private String authId;

    private String driverCode;

    private String name;

    private String mobilePhone;

    private Page page;

    // 平台id
    private int platFormId;

    public int getPlatFormId() {
        return platFormId;
    }

    public void setPlatFormId(int platFormId) {
        this.platFormId = platFormId;
    }

    public String getDriverCode() {
        return driverCode;
    }

    public void setDriverCode(String driverCode) {
        this.driverCode = driverCode;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }
}
