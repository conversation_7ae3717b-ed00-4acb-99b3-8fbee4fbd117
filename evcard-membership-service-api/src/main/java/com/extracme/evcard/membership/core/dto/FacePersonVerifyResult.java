package com.extracme.evcard.membership.core.dto;

import com.extracme.evcard.rpc.dto.BaseResponse;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Discription
 * @date 2021/3/17
 */
@Data
@NoArgsConstructor
public class FacePersonVerifyResult extends BaseResponse {

    public FacePersonVerifyResult(int code, String message) {
        super(code, message);
    }

    /**
     * 人脸图像与身份证小图的相似度，
     * 取值范围[0, 100]
     * 推荐阈值80即认为判断为同一人
     */
    private Double score;

    /**
     * 人脸图像
     */
    private String image;
}
