package com.extracme.evcard.membership.core.dto.input;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/9/24
 */
@Data
public class ProvisionUserTagInput implements Serializable {
    /**
     * 点赞记录id，取消点赞时必须
     */
    private Long tagId;
    /**
     * 必须，条款项/问题项id
     */
    private String nodeId;

    /**
     * 条款类型 1：会员守则 2：隐私政策 3：Q&A
     */
    private int type;

    /**
     * 标签 -1取消标签 0：有帮助 1：没啥用
     */
    private int tag;
}
