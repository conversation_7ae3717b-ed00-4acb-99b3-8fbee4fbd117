package com.extracme.evcard.membership.core.input;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/9/2 15:05
 * @Description: 微信返回值对象
 */
@Data
public class MmpWeChatRequestDto {
    //通过 URL Link 进入的小程序页面路径，必须是已经发布的小程序存在的页面，不可携带 query 。path 为空时会跳转小程序主页
    private String path;
    //通过 URL Link 进入小程序时的query，最大1024个字符，只支持数字，大小写英文以及部分特殊字符：!#$&'()*+,/:;=?@-._~%
    private String query;
    //默认值"release"。要打开的小程序版本
    private String envVersion;
    //默认值0.小程序 URL Link 失效类型，失效时间：0，失效间隔天数：1
    private Integer expireType;
    //到期失效的 URL Link 的失效时间，为 Unix 时间戳。生成的到期失效 URL Link 在该时间前有效。最长有效期为30天。expire_type 为 0 必填
    private Long expireTime;
    //到期失效的URL Link的失效间隔天数。生成的到期失效URL Link在该间隔时间到达前有效。最长间隔天数为30天。expire_type 为 1 必填
    private Integer expireInterval;
    //尝试次数
    private Integer tryNum;

}
