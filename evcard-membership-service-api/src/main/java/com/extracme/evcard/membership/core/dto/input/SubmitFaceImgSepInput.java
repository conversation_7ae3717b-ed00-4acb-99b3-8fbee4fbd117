package com.extracme.evcard.membership.core.dto.input;

import lombok.Data;

import java.io.Serializable;


@Data
public class SubmitFaceImgSepInput implements Serializable {
    /**
     * 会员id
     */
    private String mid;
    /**
     * 人脸识别图片
     */
    private String faceRecImg;
    /**
     * 证件号码
     */
    private String idCardNo;
    /**
     * 渠道
     */
    private String appKey;
    /**
     * 人脸认证来源
     */
    private String refKey2;

    /**
     * 是否校验认证状态
     */
    private boolean checkFaceAuthStatus;
}
