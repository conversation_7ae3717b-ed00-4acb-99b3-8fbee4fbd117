package com.extracme.evcard.membership.core.service.agency;

import com.extracme.evcard.membership.core.dto.CommBaseResponse;
import com.extracme.evcard.membership.core.dto.OperatorDto;
import com.extracme.evcard.membership.core.dto.agency.*;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.dto.CommonAddRespDto;

/**
 * 企业 服务
 */
public interface IMembershipAgencyService {

    CommonAddRespDto addAgencySecondAppKeyRelation(AddAgencySecondAppKeyRelationDTO addAgencySecondAppKeyRelationDTO);

    BaseResponse deleteAgencySecondAppKeyRelation(String agencyId, OperatorDto operator);

    CommBaseResponse<AgencySecondAppKeyRelationDTO> queryAgencySecondAppKeyRelation(String agencyId);

    CommBaseResponse<BindAgencyBySecondAppKeyRespDto> bindAgencyBySecondAppKey(BindAgencyBySecondAppKeyRelationDTO bindAgencyBySecondAppKeyRelationDTO);

    CommBaseResponse<BindAgencyBySecondAppKeyRespDto> bindAgencyByShortLinkRandomCode(BindAgencyBySecondAppKeyRelationDTO bindAgencyBySecondAppKeyRelationDTO);

    CommBaseResponse<BindAgencyBySecondAppKeyRespDto> switchAgency(SwitchAgencyDTO switchAgencyDTO);

    CommBaseResponse<BindAgencyBySecondAppKeyRespDto> switchAgencyByShortLinkRandomCode(SwitchAgencyDTO switchAgencyDTO);
}
