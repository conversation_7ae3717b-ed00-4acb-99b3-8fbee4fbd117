package com.extracme.evcard.membership.credit.dto;

import com.extracme.evcard.rpc.dto.BaseResponse;

import java.util.Date;

/**
 * Created by Elin on 2017/11/22.
 * 新增事件记录
 */
public class CreditEventRecordFullDto extends BaseResponse {

    private static final long serialVersionUID = -3328044412268641275L;

    /**
     * 事件编号 新增不需要传
     */
    private Long id;

    /**
     * 会员id
     */
    private String authId;

    /**
     * 订单编号
     */
    private String orderSeq;

    /**
     * 事件类型id
     */
    private Long eventTypeId;

    /**
     * 事件记录详情
     */
    private String eventDesc;

    /**
     *操作来源
     */
    private String eventSource;

    /**
     *事件类型名称
     */
    private String eventName;

    /**
     *事件凭证图片地址 多张逗号分隔
     */
    private String eventImagePath;

    /**
     * 事件凭证 文件路径
     */
    private String eventFilePath;

    /**
     *事件类型性质 1-正面 0-负面
     */
    private String eventNature;
    /**
     *分值
     */
    private Integer amount;
    /**
     *是否直接加入黑名单 false-否 true-是
     */
    private Boolean blackList;
    /**
     *备注
     */
    private String miscDesc;
    /**
     *事件状态（0-撤销 1-正常）
     */
    private Integer status;
    /**
     *创建时间
     */
    private Date createTime;
    /**
     *创建人id
     */
    private Long createOperId;
    /**
     *创建人姓名
     */
    private String createOperName;
    /**
     *修改时间
     */
    private Date updateTime;
    /**
     *修改人id
     */
    private Long updateOperId;
    /**
     *修改人姓名
     */
    private String updateOperName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public String getOrderSeq() {
        return orderSeq;
    }

    public void setOrderSeq(String orderSeq) {
        this.orderSeq = orderSeq;
    }

    public Long getEventTypeId() {
        return eventTypeId;
    }

    public void setEventTypeId(Long eventTypeId) {
        this.eventTypeId = eventTypeId;
    }

    public String getEventDesc() {
        return eventDesc;
    }

    public void setEventDesc(String eventDesc) {
        this.eventDesc = eventDesc;
    }

    public String getEventSource() {
        return eventSource;
    }

    public void setEventSource(String eventSource) {
        this.eventSource = eventSource;
    }

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public String getEventImagePath() {
        return eventImagePath;
    }

    public void setEventImagePath(String eventImagePath) {
        this.eventImagePath = eventImagePath;
    }

    public String getEventFilePath() {
        return eventFilePath;
    }

    public void setEventFilePath(String eventFilePath) {
        this.eventFilePath = eventFilePath;
    }

    public String getEventNature() {
        return eventNature;
    }

    public void setEventNature(String eventNature) {
        this.eventNature = eventNature;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public Boolean getBlackList() {
        return blackList;
    }

    public void setBlackList(Boolean blackList) {
        this.blackList = blackList;
    }

    public String getMiscDesc() {
        return miscDesc;
    }

    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateOperId() {
        return createOperId;
    }

    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    public String getCreateOperName() {
        return createOperName;
    }

    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdateOperId() {
        return updateOperId;
    }

    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    public String getUpdateOperName() {
        return updateOperName;
    }

    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }
}
