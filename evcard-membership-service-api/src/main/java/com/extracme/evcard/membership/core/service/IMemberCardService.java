package com.extracme.evcard.membership.core.service;

import com.extracme.evcard.membership.core.dto.*;
import com.extracme.evcard.membership.core.exception.CardOperateException;
import com.extracme.evcard.membership.core.input.CardInfoHistoryInputDTO;
import com.extracme.evcard.membership.core.input.QueryCardInfoConditionInput;
import com.extracme.evcard.membership.core.input.QueryCardInfoHistoryListIConditionInput;
import com.extracme.evcard.rpc.dto.Page;
import com.extracme.evcard.rpc.dto.PageBeanDto;
import com.extracme.framework.core.bo.PageBO;
import com.extracme.framework.core.model.PageBean;

import java.util.List;

/**
 * 会员卡服务
 */
public interface IMemberCardService {

	/**
	 * 暂停会员卡
	 * 
	 * @param cardOperateDto
	 *            暂停会员卡信息.<br>
	 *            必须 cardNo 必须 authId 可选 membershipType 会员类别：0：外部会员(缺省值) 1:内部
	 *            2:企业. 必须 remark 二选一 pauseTimeType 0暂停7天 1暂停30天 2永久 二选一
	 *            recoverTime yyyy-mm-dd
	 * @param updateUserDto
	 *            修改人信息.<br>
	 * <AUTHOR>
	 * @since 2.1.0
	 */
	void cardPause(CardOperateDto cardOperateDto, UpdateUserDto updateUserDto) throws CardOperateException;

	/**
	 * 恢复会员卡
	 * 
	 * @param cardOperateDto
	 *            恢复会员卡信息.<br>
	 *            必须 cardNo 必须 authId 可选 membershipType 会员类别：0：外部会员(缺省值) 1:内部
	 *            2:企业. 可选 remark
	 * @param updateUserDto
	 *            修改人信息.<br>
	 * <AUTHOR>
	 * @since 2.1.0
	 */
	void cardRecover(CardOperateDto cardOperateDto, UpdateUserDto updateUserDto) throws CardOperateException;

	/**
	 * 注销会员卡
	 * 
	 * @param cardOperateDto
	 *            会员卡操作信息.<br>
	 * @param updateUserDto
	 *            修改人信息.<br>
	 * @return boolean 操作结果
	 * <AUTHOR>
	 * @since 2.1.0
	 * @remark 此接口暂不提供
	 */
	void cardCancel(CardOperateDto cardOperateDto, UpdateUserDto updateUserDto) throws CardOperateException;

	/**
	 * 查询会员卡暂停记录(最新一条)
	 * 
	 * @param cardNo
	 *            会员卡号
	 * @return
	 */
	CardPauseQueryDto queryCardPauseLogByCardNo(String cardNo);

	/**
	 * 查询会员卡状态
	 * 
	 * @param cardNo
	 *            会员卡号
	 * @return
	 */
	Integer queryCardStatus(String cardNo);


	/**
	 * 查询卡号信息
	 * @param cardNo
	 * @return
	 * <AUTHOR>
	 */
	CardInfoDTO queryCardInfo(String cardNo);

	/**
	 * 查询卡详情列表
	 * @param authId
	 * @return
	 * <AUTHOR>
	 */
	List<CardInfoDTO> queryCardInfoList(String authId);

	/**
	 * 查询卡详情历史列表
	 * @param authId
	 * @return
	 * <AUTHOR>
	 */
	List<CardInfoHistoryDTO> queryCardInfoHistoryList(String authId);
	
	/**
	 * 根据authId查询会员暂停日志
	 * 
	 * @param authId
	 * @param rowStart
	 * @param pageSize
	 * @return
	 */
	List<CardPauseLogDTO> queryCardPauseLogByAuthId(String authId, int rowStart, int pageSize);

	/**
	 * 更新会员卡历史记录
	 * @param cardInfoHistoryDTO
	 * @return
	 */
	boolean updateCardInfoHistory(CardInfoHistoryInputDTO cardInfoHistoryDTO);

	/**
	 * 插入会员卡历史记录
	 * @param cardInfoHistoryDTO
	 * @return
	 */
	boolean insertCardInfoHistory(CardInfoHistoryInputDTO cardInfoHistoryDTO);


	/**
	 * 根据条件查询会员卡信息列表
	 *  供调度系统查询使用
	 * @param queryCardInfoConditionInput
	 * @return
	 */
	List<CardInfoDTO> queryCardInfoByCondition(QueryCardInfoConditionInput queryCardInfoConditionInput);


	/**
	 * 更新会员卡记录信息
	 * @param cardInfoDTO
	 * @return
	 */
	 boolean updateCardInfo(CardInfoDTO cardInfoDTO);


	/**
	 * 插入会员卡记录
	 * @param cardInfoDTO
	 * @return
	 */
	 boolean insertCardInfo(CardInfoDTO cardInfoDTO);

	/**
	 * 更新会员卡日志
	 * @param cardPauseLogDTO
	 * @return
	 */
	 boolean updateCardPauseLog(CardPauseLogDTO cardPauseLogDTO);


	/**
	 * 查询卡详情历史列表
	 *  供调度系统使用
	 * @param conditionInput
	 * @return
	 */
	PageBeanDto<CardInfoHistoryDTO> queryCardInfoHistoryListByCondition(QueryCardInfoHistoryListIConditionInput conditionInput);

}
