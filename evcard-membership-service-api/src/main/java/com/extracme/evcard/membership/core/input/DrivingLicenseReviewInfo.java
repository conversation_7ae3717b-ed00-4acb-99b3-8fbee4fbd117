package com.extracme.evcard.membership.core.input;

import lombok.Data;

import java.io.Serializable;

@Data
public class DrivingLicenseReviewInfo implements Serializable {
    /**
     * 驾照三要素认证状态 0待认证 1认证通过 2认证不通过 3查证中(待重新认证)
     */
    private Integer authenticateStatus;

    /**
     * 驾照状态 0待认证  1认证通过  2认证不通过（驾照有效期/准驾车型/驾照状态不符合用车条件）
     */
    private Integer licenseStatus;

    /**
     * 驾照状态描述: "超分","暂扣","撤销","吊销","注销","停止使用"等
     */
    private String licenseStatusMsg;

    /**
     * elementsReviewItems: 驾照号/姓名/档案编号
     * 0不一致 1一致
     * 如:010 111
     */
    private String elementsReviewItems;

    /**
     * 操作日志
     */
    private String operatorContent;
}
