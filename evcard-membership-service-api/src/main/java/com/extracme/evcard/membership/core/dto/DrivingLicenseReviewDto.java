package com.extracme.evcard.membership.core.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class DrivingLicenseReviewDto implements Serializable {

    /** 驾照号 */
    private String driverCode;

    /** 姓名 */
    private String name;

    /** 档案编号	 */
    private String fileNo;

    /** 初次领证日期 */
    private String  firstGetLicenseDate;

    /** 到期日期 */
    private String  expirationDate;

    /** 驾照类型 */
    private String drivingLicenseType;

    /** 国籍 */
    private String national;

    /** 驾照图片 */
    private String drivingLicenseImgUrl;

    /** 驾照副页照片地址 */
    private String fileNoImgUrl;
}
