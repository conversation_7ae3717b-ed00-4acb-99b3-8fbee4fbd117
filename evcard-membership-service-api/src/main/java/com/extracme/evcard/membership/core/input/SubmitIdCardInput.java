package com.extracme.evcard.membership.core.input;

import lombok.Data;

import java.io.Serializable;

/**
 * 提交身份认证信息-入参
 */
@Data
public class SubmitIdCardInput implements Serializable {
    /**
     * 会员id
     */
    private String mid;

    /**
     * 渠道key
     *
     * 履约 ： ofc
     */
    private String appKey;

    /** 证件输入类型  1驾照ocr识别   2手动输入(需提交人工审核) */
    private Integer certInputType;

    /**
     * 身份证件类型  1:居民身份证 2:外籍护照 3:港澳通行证 4:台湾通行证 5:军人身份证
     */
    private Integer idType;

    /** 证件号 */
    private String idCardNumber;

    /** 姓名 */
    private String name;

    /**
     * 证件时间类型： 1非长期 2长期
     */
    private Integer expireType = 1;

    /** 到期日期 yyyy-MM-dd*/
    private String expirationDate;

    /**
     * 身份证证件正页照片,身份证/护照/港澳通行证正页
     */
    private String idcardPicUrl;

    /**
     * 身份证背面照片，大陆及军官身份证提供(oss对象名)
     */
    private String idcardPicBackUrl;

    /**
     * 手持证件图片，外籍&港澳台用户提供(oss对象名)
     */
    private String holdIdcardPicUrl;

    /**
     * 人脸照片，外籍&港澳台用户提供(oss对象名)
     */
    private String facePicUrl;

    /**
     * 城市运营模式 0：大库 1：门店
     */
    private String operationModel;

    /**
     * 被替换手机号
     */
    private String occupiedMid;

}
