package com.extracme.evcard.membership.core.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 用户荣誉称号对象
 * <AUTHOR>
 * @date 2021/4/21
 * @remark
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserTitleDto implements Serializable {
    /**
     * 称号类别: 0新手 1新秀 2先锋 3精英 4大使
     */
    private int titleName;
    /**
     * 称号级别: 0无  1LV1 2LV2 3LV3 4LV4
     */
    private int titleLevel;

    public String buildTitle(){
        return "T" + titleName + "L" + titleLevel;
    }
}
