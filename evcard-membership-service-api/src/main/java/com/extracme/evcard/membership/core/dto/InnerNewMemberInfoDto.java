package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: Elin
 * \* Date: 2018/6/8
 * \* Time: 15:07
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 内部新员工信息
 * \
 */
public class InnerNewMemberInfoDto implements Serializable {

    private static final long serialVersionUID = -721860778685360793L;

    /**
     * 姓名
     */
    private String name;

    /**
     * 会员手机号
     */
    private String mobilePhone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 地区id
     */
    private String areaid;

    /**
     * 区域id
     */
    private Long regionid;

    /**
     * 组织机构
     */
    private String orgId;

    /**
     * 驾驶证号
     */
    private String driverCode;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }


    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAreaid() {
        return areaid;
    }

    public void setAreaid(String areaid) {
        this.areaid = areaid;
    }

    public Long getRegionid() {
        return regionid;
    }

    public void setRegionid(Long regionid) {
        this.regionid = regionid;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getDriverCode() {
        return driverCode;
    }

    public void setDriverCode(String driverCode) {
        this.driverCode = driverCode;
    }
}