package com.extracme.evcard.membership.core.input;

import com.extracme.evcard.rpc.dto.Page;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2019/8/23
 */
@Data
public class QueryMemberListInputDTO implements Serializable{
    private static final long serialVersionUID = 8228408324375955947L;

    /**
     * 驾照号
     */
    private String driverCode;

    /** 会员类型 */
    private Integer memberType;

    /** 会员id集合 */
    private List<String> authIdList;

    /** 人员状态  0：正常  1：休息 2：离职 */
    private List<Integer> personnelState;

    /** 运营区域Id */
    private Long regionId;

    /** 机构Id */
    private String orgId;

    /** 是否需要模糊查询机构  null/0 否  1 左模糊 2 右模糊 3 左右模糊 */
    private Integer isLikeQueryOrgId;

    /** 手机号 */
    private String mobilePhone;

    /** 是否需要手机号 null/0 否  1 左模糊 2 右模糊 3 左右模糊 */
    private Integer isLikeQueryMobilePhone;

    /** 姓名 */
    private String name;

    /** 是否需要手机号 null/0 否  1 左模糊 2 右模糊 3 左右模糊 */
    private Integer isLikeQueryName;

    /** 分页数据 */
    private Page page;

}
