package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;

/**
 * 会员操作日志（老）
 *
 * <AUTHOR>
 * @date 2019/8/30 10:11
 */
public class UserOperatorLogDTO implements Serializable {

	private static final long serialVersionUID = -106800359200345928L;

	/**
	 * 操作内容
	 */
	private String operatorContent;
	/**
	 * 创建时间
	 */
	private String createdTime;
	/**
	 * 创建用户
	 */
	private String createdUser;

	/** 外键 */
	private String foreignKey;

	/** 外键2 */
	private String foreignKey2;

	/** 操作人机构名 **/
	private String orgName;

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getForeignKey() {
		return foreignKey;
	}

	public void setForeignKey(String foreignKey) {
		this.foreignKey = foreignKey;
	}

	public String getForeignKey2() {
		return foreignKey2;
	}

	public void setForeignKey2(String foreignKey2) {
		this.foreignKey2 = foreignKey2;
	}

	/**
	 * 操作内容
	 */
	public String getOperatorContent() {
		return operatorContent;
	}

	/**
	 * 操作内容
	 */
	public void setOperatorContent(String operatorContent) {
		this.operatorContent = operatorContent;
	}

	/**
	 * 创建时间
	 */
	public String getCreatedTime() {
		return createdTime;
	}

	/**
	 * 创建时间
	 */
	public void setCreatedTime(String createdTime) {
		this.createdTime = createdTime;
	}

	/**
	 * 创建用户
	 */
	public String getCreatedUser() {
		return createdUser;
	}

	/**
	 * 创建用户
	 */
	public void setCreatedUser(String createdUser) {
		this.createdUser = createdUser;
	}
}
