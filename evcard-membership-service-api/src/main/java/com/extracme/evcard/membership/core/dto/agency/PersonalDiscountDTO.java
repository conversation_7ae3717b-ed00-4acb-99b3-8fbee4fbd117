package com.extracme.evcard.membership.core.dto.agency;

import java.io.Serializable;

public class PersonalDiscountDTO implements Serializable {
    private static final long serialVersionUID = 1027788082767803643L;
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 折扣规则ID
     */
    private Long discountRuleId;
    /**
     * 个人折扣级别：1-一级；2-二级；3-三级；4-四级；5-五级（可能扩展）
     */
    private Integer discountType;
    /**
     * 个人折扣率
     */
    private Double discountRate;
    /**
     * 折扣开始时间
     */
    private Integer discountStartTime;
    /**
     * 折扣结束时间
     */
    private Integer discountEndTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDiscountRuleId() {
        return discountRuleId;
    }

    public void setDiscountRuleId(Long discountRuleId) {
        this.discountRuleId = discountRuleId;
    }

    public Integer getDiscountType() {
        return discountType;
    }

    public void setDiscountType(Integer discountType) {
        this.discountType = discountType;
    }

    public Double getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(Double discountRate) {
        this.discountRate = discountRate;
    }

    public Integer getDiscountStartTime() {
        return discountStartTime;
    }

    public void setDiscountStartTime(Integer discountStartTime) {
        this.discountStartTime = discountStartTime;
    }

    public Integer getDiscountEndTime() {
        return discountEndTime;
    }

    public void setDiscountEndTime(Integer discountEndTime) {
        this.discountEndTime = discountEndTime;
    }

    @Override
    public String toString() {
        return "PersonalDiscountDTO{" +
                "id=" + id +
                ", discountRuleId=" + discountRuleId +
                ", discountType=" + discountType +
                ", discountRate=" + discountRate +
                ", discountStartTime=" + discountStartTime +
                ", discountEndTime=" + discountEndTime +
                '}';
    }
}
