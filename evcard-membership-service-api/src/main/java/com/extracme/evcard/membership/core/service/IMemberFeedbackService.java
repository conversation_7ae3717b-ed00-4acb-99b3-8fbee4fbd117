package com.extracme.evcard.membership.core.service;

import com.extracme.evcard.membership.core.dto.FeedbackBaseDto;
import com.extracme.evcard.membership.core.dto.input.DealFeedbackInput;
import com.extracme.evcard.membership.core.input.*;

import java.util.List;

public interface IMemberFeedbackService {

    /**
     * 车辆故障上报
     * @param input
     * <AUTHOR>
     */
    void reportVehicleMalfunction(ReportVehicleMalfunctionInput input);

    /**
     * 充电桩故障上报
     * @param input
     * <AUTHOR>
     */
    void reportElectricPileMalfunction(ReportElectricPileMalfunctionInput input);

    /**
     * 推荐建网点
     * @param input
     * <AUTHOR>
     */
    void reportRecommendBuildShop(ReportRecommendBuildShopInput input);


    /**
     * 其他问题上报
     * @param input
     * <AUTHOR>
     */
    void reportOtherProblem(ReportOtherProblemInput input);

    /**
     * 查询异常上报信息
     * @param feedbackBaseInput
     * @return
     */
    List<FeedbackBaseDto> queryFeedBackInfo(FeedbackBaseInput feedbackBaseInput);

    /**
     * 分页查询反馈信息
     * @param queryFeedbackInput
     * @return
     */
    List<FeedbackBaseDto> queryFeedBackInfoPage(QueryFeedbackInput queryFeedbackInput);

    /**
     * 处理消息反馈
     * @param dealFeedbackInput
     */
    void dealFeedback(DealFeedbackInput dealFeedbackInput);

    /**
     * 更新消息为已读
     */
    void updateFeedbackRead(FeedbackReadInput readInput);

    /**
     * 查询未读的反馈回复数量
     * @param authId 个人会员时，传入authId;企业会员时，传入orgId
     * @param name 个人会员时，不用传入;企业会员时，传入登录名称
     * <AUTHOR>
     * @return
     */
    int queryUnReadFeedbackReplyCount(String authId, String name);

}
