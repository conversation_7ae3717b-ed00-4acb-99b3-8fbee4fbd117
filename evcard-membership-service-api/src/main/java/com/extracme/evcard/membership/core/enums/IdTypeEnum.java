package com.extracme.evcard.membership.core.enums;

import java.util.Arrays;
import java.util.List;

public enum IdTypeEnum {
    //1:居民身份证 2:外籍护照 3:港澳通行证 4:台湾通行证 5:军人身份证
    ID_CARD(1, "中国"),
    PASSPORT(2, "外国"),
    HKM_PASSPORT(3, "港澳台"),
    TW_PASSPORT(4, "港澳台"),
    SOLDIER_ID_CARD(5, "中国（军人）");

    private Integer value;

    private String national;

    public static final List UN_NATIVE_TYPES = Arrays.asList(PASSPORT.getValue(),
            HKM_PASSPORT.getValue(), TW_PASSPORT.getValue());

    IdTypeEnum(Integer value, String national) {
        this.value = value;
        this.national = national;
    }

    public static final boolean isMainlandId(Integer type) {
        return ID_CARD.value.equals(type) || SOLDIER_ID_CARD.value.equals(type);
    }

    public static IdTypeEnum getByType(Integer type) {
        for (IdTypeEnum item : IdTypeEnum.values()) {
            if (item.getValue().equals(type)) {
                return item;
            }
        }
        return IdTypeEnum.ID_CARD;
    }

    public Integer getValue() {
        return value;
    }

    public String getNational() {
        return national;
    }

    public boolean eq(Integer value) {
        return this.value.equals(value);
    }
}
