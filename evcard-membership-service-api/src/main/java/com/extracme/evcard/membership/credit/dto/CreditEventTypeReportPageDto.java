package com.extracme.evcard.membership.credit.dto;

import com.extracme.evcard.rpc.dto.BaseResponse;

/**
 * Created by Elin on 2017/11/22.
 * 事件类型报表分析字段
 */
public class CreditEventTypeReportPageDto extends BaseResponse {

    private static final long serialVersionUID = 2287398496764751481L;

    /**
     * 编号
     */
    private Long id;

    /**
     * 年份
     */
    private String yearNum;

    /**
     * 所属公司id
     */
    private String orgId;

    /**
     * 查询类型 0-各事件类型次数 1-各事件类型会员数
     */
    private Integer type;

    /**
     * 事件类型id
     */
    private Integer eventTypeId;

    /**
     * 事件类型
     */
    private String eventName;

    /**
     * 月份 1-12
     */
    private Integer month;

    /**
     * 数量
     */
    private Integer total;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getYearNum() {
        return yearNum;
    }

    public void setYearNum(String yearNum) {
        this.yearNum = yearNum;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getEventTypeId() {
        return eventTypeId;
    }

    public void setEventTypeId(Integer eventTypeId) {
        this.eventTypeId = eventTypeId;
    }

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public Integer getMonth() {
        return month;
    }

    public void setMonth(Integer month) {
        this.month = month;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }
}