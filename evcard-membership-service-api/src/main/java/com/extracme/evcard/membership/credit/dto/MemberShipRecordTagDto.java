package com.extracme.evcard.membership.credit.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 会员用车以及邀请记录统计.<br>
 * <AUTHOR>
 *
 */
public class MemberShipRecordTagDto implements Serializable {
	
	private static final long serialVersionUID = 7559731537154233392L;

	/**
	 * 有效消费金额.<br>
	 */
	private BigDecimal consumAmount;
	
	/**
	 * 消费次数.<br>
	 */
	private int consumTimes;
	
	/**
	 * 消费时长(用车).<br>
	 */
	private int consumLength;
	
	/**
	 * 邀请奖励次数.<br>
	 */
	private int invitAwardsTimes;

	public BigDecimal getConsumAmount() {
		return consumAmount;
	}

	public void setConsumAmount(BigDecimal consumAmount) {
		this.consumAmount = consumAmount;
	}

	public int getConsumTimes() {
		return consumTimes;
	}

	public void setConsumTimes(int consumTimes) {
		this.consumTimes = consumTimes;
	}

	public int getConsumLength() {
		return consumLength;
	}

	public void setConsumLength(int consumLength) {
		this.consumLength = consumLength;
	}

	public int getInvitAwardsTimes() {
		return invitAwardsTimes;
	}

	public void setInvitAwardsTimes(int invitAwardsTimes) {
		this.invitAwardsTimes = invitAwardsTimes;
	}
	

}
