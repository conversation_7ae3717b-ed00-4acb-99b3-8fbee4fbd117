package com.extracme.evcard.membership.credit.dto;

import com.extracme.evcard.rpc.dto.BaseResponse;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/8/12
 */
@Data
public class MemberPointsAccountDto extends BaseResponse implements Serializable {
    /**
     * 用户积分数
     */
    private Integer credits;

    /**
     * 积分状态 0正常 1冻结
     */
    private String status;

    /**
     * 最早过期时间
     */
    private String minExpireDate;

    /**
     * 最晚过期时间
     */
    private String maxExpireDate;

    /**
     * 用户状态 0正常 1禁用
     */
    private String userStatus;
}
