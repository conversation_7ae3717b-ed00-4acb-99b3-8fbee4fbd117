package com.extracme.evcard.membership.core.input;

import java.io.Serializable;

/**
 * 阿里人机认证的参数.
 * <AUTHOR>
 */
public class AliAfs implements Serializable {
	
	private static final long serialVersionUID = 1L;

	/**
	 * 会话ID。必填参数，从前端获取，不可更改。
	 */
	private String sessionId;
	
    /**
     * 签名串。必填参数，从前端获取，不可更改。
     */
	private String sig;
	
    /**
     * 请求唯一标识。必填参数，从前端获取，不可更改。
     */
	private String token;
	
	/**
	 * 场景标识。必填参数，从前端获取，不可更改
	 */
    private String scene;
    
    /**
     * 客户端ip.
     */
    private String remoteIp;
    
    /**
     * 秘钥key。
     * 和阿里的accessKey不等同，是人机验证专用key.
     */
    private String appKey;

	public String getSessionId() {
		return sessionId;
	}

	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}

	public String getSig() {
		return sig;
	}

	public void setSig(String sig) {
		this.sig = sig;
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	public String getScene() {
		return scene;
	}

	public void setScene(String scene) {
		this.scene = scene;
	}

	public String getRemoteIp() {
		return remoteIp;
	}

	public void setRemoteIp(String remoteIp) {
		this.remoteIp = remoteIp;
	}

	public String getAppKey() {
		return appKey;
	}

	public void setAppKey(String appKey) {
		this.appKey = appKey;
	}
    
}
