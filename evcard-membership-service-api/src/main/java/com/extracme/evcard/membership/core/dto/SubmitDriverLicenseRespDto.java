package com.extracme.evcard.membership.core.dto;

import com.extracme.evcard.membership.core.enums.AuthStateCodeEnum;
import com.extracme.evcard.rpc.dto.BaseResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
public class SubmitDriverLicenseRespDto extends BaseResponse implements Serializable {
    /**
     * 提交结果
     * 1:成功 2:操作频繁 3:驾照已被上个注销账号使用 4:驾照号错误 5:驾照已过期 6:驾照类型不支持租车 7:驾照号与身份证号不一致
     * 8:有效期不在原有效期之后(更新时才有)
     * 9:驾照已被提交认证
     * 10：驾照到期时间格式不正确
     * 11：该身份证号的驾照已被其他账号认证
     * 15：驾照与身份证号码不一致
     * 16：驾照与身份证姓名不一致
     *
     */
    private int state;

    /**
     * 参数
     */
    private String msg;

    /**
     * 被占用的手机号
     */
    private String occupiedMobilePhone;

    /**
     * 被占用人的mid
     */
    private String occupiedMid;

    public SubmitDriverLicenseRespDto () {
        super(0, StringUtils.EMPTY);
    }

    public SubmitDriverLicenseRespDto (AuthStateCodeEnum stateCodeEnum) {
        super(0, StringUtils.EMPTY);
        this.state = stateCodeEnum.getState();
        this.msg = stateCodeEnum.getMsg();
    }

    public SubmitDriverLicenseRespDto (AuthStateCodeEnum stateCodeEnum, String msg) {
        super(0, StringUtils.EMPTY);
        this.state = stateCodeEnum.getState();
        this.msg = msg;
    }
}
