package com.extracme.evcard.membership.core.service;

import com.extracme.evcard.membership.core.dto.*;
import com.extracme.evcard.membership.core.dto.input.FaceMatchInput;
import com.extracme.evcard.membership.core.dto.input.FacePersonVerifyInput;

import java.util.List;

/**
 * <AUTHOR>
 * @Discription
 * @date 2021/3/17
 */
public interface IMemberFaceVerifyService {

    /**
     * 人脸实名认证
     * /rest/2.0/face/v3/person/verify
     * @param input
     * @return
     */
    FacePersonVerifyResult personVerify(FacePersonVerifyInput input);


    /**
     * 增强级人脸实名认证
     *
     * /rest/2.0/face/v3/person/verifySec
     * @param input
     * @return
     */
    FacePersonVerifyResult personVerifySec(FacePersonVerifyInput input);

    /**
     * 人脸对比
     * @param
     * @return
     */
    FacePersonVerifyResult faceMatch(FaceMatchInput input);

    /**
     * 增强级人脸比对接口
     * @param input
     * @return
     */
    FacePersonVerifyResult enhancerFaceMatch(FaceMatchInput input);


    /**
     * 生成随机码，用于视频的动作识别
     * @return
     */
    FaceLiveSessionCode getFaceLiveSessionCode();

    /**
     * 视频活体检测
     * @param input
     * @return
     */
    FaceLiveVerifyResult faceLiveVerify(FaceLiveVerifyInput input);
}
