package com.extracme.evcard.membership.core.service;

import com.extracme.evcard.membership.core.dto.UserInstantActivityRecordDto;

import java.math.BigDecimal;
import java.util.List;

/**
 * 会员活动相关服务
 * <AUTHOR> chennian.
 * @Date ：Created in 10:44 2021/2/1
 */
public interface IMemberActivityService {

    /**
     * 获取用户参与立减活动记录
     * @param authId
     * @param activityId
     * @return
     */
    List<UserInstantActivityRecordDto> getInstantActivityRecord(String authId, Long activityId);


    /**
     * 判断用户是否满足首单奖励条件
     *
     * @return
     */
    boolean checkFirstAuditRewards(String authId, String mid);

}
