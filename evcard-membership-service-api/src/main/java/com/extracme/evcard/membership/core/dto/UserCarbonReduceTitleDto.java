package com.extracme.evcard.membership.core.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Discription
 * @date 2021/5/6
 */
@Data
public class UserCarbonReduceTitleDto implements Serializable {
    /**
     * 称号描述
     */
    private String titleDesc;

    /**
     * 总里程 单位：km
     */
    private BigDecimal totalMileage;

    /**
     * 碳减排量 单位：kg
     */
    private Integer totalCarbonEmissions;

    /**
     * 累计碳减排量转树的数量
     */
    private Integer treeNum;

    /**
     * 节碳称号 0节碳新手 1节碳新秀 2节碳先锋 3节碳精英 4节碳大使
     */
    private Integer titleName;

    /**
     * 称号等级 1:LV1 2:LV2 3:LV3
     */
    private Integer titleLevel;

    /**
     * 打败用户百分比
     */
    private Integer rankPercentage;
}
