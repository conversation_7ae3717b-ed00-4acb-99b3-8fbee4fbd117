package com.extracme.evcard.membership.core.service;

import com.extracme.evcard.membership.core.dto.*;

import java.util.List;
import java.util.Set;


public interface IMembershipWrapService {

	/**
	 * 根据会员pkId获取会员附加信息
	 * @param pkId
	 * @return
	 * @since 3.20.0
	 * @remark 目前包含从证件中抽取的年龄的信息，后续自行添加其他
	 */
	MemberWrapInfoDto getMemberWrapInfoByPkId(Long pkId);


	/**
	 * 根据手机号查询会员.<br>
	 * @param mobilePhone		手机号.<br>
	 * @param membershipType	会员类型.<br>
	 * @return	当用户注销180天以上的时候，该函数无法查询出会员.<br>
	 */
	MembershipDetailInfo getMembershipByPhone(String mobilePhone, Integer membershipType);
	
	/**
	 * 根据会员id查询会员.<br>
	 * @param authId		会员id.<br>
	 * @param membershipType	会员类型.<br>
	 * @return	当用户注销180天以上的时候，该函数无法查询出会员.<br>
	 */
	MembershipDetailInfo getMembershipByAuthid(String authId, Integer membershipType);


	/**
	 * 获取用户最近的驾照三要素认证日志明细
	 * @param authId
	 * @param logType 日志类型 0三要素 1驾照分数
	 * @param num 查询前num条认证操作的日志
	 * @return 解析供应商结果
	 * @since 2.16.0
	 */
	List<LicenseAuthLogDTO> getUserLastLicenseAuthLogs(String authId, Integer logType, Integer num);

	/**
	 * 获取用户的节碳称号
	 * @param authId
	 * @return
	 * @since 3.19.0
	 */
	UserCarbonReduceTitleDto getCarbonReduceTitle(String authId);

	/**
	 * 批量获取用户信息
	 * @param userIds
	 * @return
	 * @since 3.26.0
	 */
	List<MembershipBasicInfo> getMembersByUserIds (Set<Long> userIds);

	/**
	 * 批量获取用户信息
	 * @param authIds
	 * @return
	 * @since 3.26.2
	 */
	List<MembershipBasicInfo> getMembersByAuthIds (Set<String> authIds);

	/**
	 * 批量获取用户信息
	 * @param mobilePhones
	 * @return
	 * @since 4.12.4
	 */
	List<MembershipBasicInfo> getMembersByMobilePhones(Set<String> mobilePhones);

	/**
	 * 根据mid获取用户信息
	 */
	MembershipBasicInfo getMemberByMid (String mid);

	/**
	 * 根据mid获取用户信息及与用户关联的机构信息
	 */
	MemberOrgInfoDto getMemberWithOrgInfoByMid (String mid);

	/**
	 * 根据mid获取用户信息及订单附加信息
	 */
	MembershipDetailInfo getMemberWithAdditionByMid (String mid);

}
