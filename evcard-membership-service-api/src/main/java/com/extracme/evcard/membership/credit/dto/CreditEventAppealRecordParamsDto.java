package com.extracme.evcard.membership.credit.dto;

import com.extracme.evcard.rpc.dto.BaseResponse;

/**
 * Created by Elin on 2017/11/22.
 * 事件申诉记录列表查询字段
 */
public class CreditEventAppealRecordParamsDto extends BaseResponse {

    private static final long serialVersionUID = 350382288034148802L;

    /**
     * 申诉编号
     */
    private Long appealId;

    /**
     * 申诉开始时间
     */
    private String appealStartTime;

    /**
     * 申诉结束时间
     */
    private String appealEndTime;

    /**
     * 手机号
     */
    private String mobilePhone;

    /**
     * 处理状态 0-待处理 1-已处理
     */
    private Integer handleResult;

    /**
     * 事件类型
     */
    private String eventName;

    public Long getAppealId() {
        return appealId;
    }

    public void setAppealId(Long appealId) {
        this.appealId = appealId;
    }

    public String getAppealStartTime() {
        return appealStartTime;
    }

    public void setAppealStartTime(String appealStartTime) {
        this.appealStartTime = appealStartTime;
    }

    public String getAppealEndTime() {
        return appealEndTime;
    }

    public void setAppealEndTime(String appealEndTime) {
        this.appealEndTime = appealEndTime;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public Integer getHandleResult() {
        return handleResult;
    }

    public void setHandleResult(Integer handleResult) {
        this.handleResult = handleResult;
    }

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }
}
