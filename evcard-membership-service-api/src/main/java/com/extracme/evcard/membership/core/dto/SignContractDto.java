package com.extracme.evcard.membership.core.dto;

import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2018/2/8
 */
public class SignContractDto implements Serializable {

    private static final long serialVersionUID = -1723176156209425025L;

    private String authId;

    private List<ContractVersionDto> contractVersionDtoList;

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public List<ContractVersionDto> getContractVersionDtoList() {
        return contractVersionDtoList;
    }

    public void setContractVersionDtoList(List<ContractVersionDto> contractVersionDtoList) {
        this.contractVersionDtoList = contractVersionDtoList;
    }

    public void setContractVersionDto(ContractVersionDto contractVersionDto){
        if(CollectionUtils.isEmpty(contractVersionDtoList)){
            contractVersionDtoList = new ArrayList<>();
        }
        if(contractVersionDto != null ){
            contractVersionDtoList.add(contractVersionDto);
        }
    }

    @Override
    public String toString() {
        return "SignContractDto{" +
                "authId='" + authId + '\'' +
                ", contractVersionDtoList=" + contractVersionDtoList +
                '}';
    }
}
