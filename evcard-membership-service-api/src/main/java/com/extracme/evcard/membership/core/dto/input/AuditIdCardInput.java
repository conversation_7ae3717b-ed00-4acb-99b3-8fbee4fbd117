package com.extracme.evcard.membership.core.dto.input;

import lombok.Data;

import java.io.Serializable;

@Data
public class AuditIdCardInput implements Serializable {

    private static final long serialVersionUID = -7272571696645644982L;
    /**
     *  需要被审核用户 id
     *  必传
     */
    private String mid;

    /**
     * 调用方 0：会员系统 1：门店小程序
     *
     * 必传
     */
    private Integer operateSourceType;


    /**
     * 必传
     * 勾选的审核项： 7位
     * identityNo,username,expireType,identityCardImgUrl,reverseIdentityCardImgUrl,holdCardImgUrl,faceImgUrl
     *  证件编号、姓名 、过期日期、身份证正页(护照/通行证)、身份证副页、手持证件照、人脸照片
     * 初始值为全0. 0000000
     *
     * (0：未审核、 1：审核通过、 2：不通过）
     **/
    private String reviewItems;


    /**
     *
     * 审核 操作人员id
     *
     *  必传
     *
     */
    private String operatorId;


    /**
     *
     * 审核 操作人员姓名
     *
     *  必传
     *
     */
    private String operatorUserName;


    /** 审核失败 参数 start  **/


    /**
     * 审核不通过时，必须
     * 审核不通过原因详细描述，分号分隔
     **/
    private String reviewRemark;

    /**
     * 审核不通过时，非必传；不传的情况下 通过 reviewItems 字段。
     *
     * 审核不通过概要编号，分号分隔(1.姓名不正确 2.驾驶证号不正确 3.驾驶证照片不正确 4. 准驾车型不正确
     * 5.初次领证日期不正确 6.驾照到期日期不正确 7.邮寄地址不正确 8.身份证件照片不正确 9.手持身份证件照片不正确
     * A.驾驶证档案编号不正确 B.身份证件编号不正确 C.人脸照片不正确 0.用户意愿))
     **/
    private String reviewItemIds;

    /**
     * 审核不通过时，非必传；不传的情况下 通过 reviewItems 字段。
     * 审核不通过原因概要，分号分隔
     **/
    private String reviewItemName;

    /** 审核失败 参数 end  **/
}
