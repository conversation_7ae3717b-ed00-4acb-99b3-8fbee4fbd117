package com.extracme.evcard.membership.core.input;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2019/3/8
 */
public class QueryMemberOperateLogInput implements Serializable {

    private static final long serialVersionUID = 8202555090087726364L;
    private String authId;

    private String foreignKey2;

    private Integer pageNum;

    private Integer pageSize;

    public QueryMemberOperateLogInput() {
    }

    public QueryMemberOperateLogInput(String authId, String foreignKey2) {
        this.authId = authId;
        this.foreignKey2 = foreignKey2;
    }

    public QueryMemberOperateLogInput(String authId, Integer pageNum, Integer pageSize) {
        this.authId = authId;
        this.pageNum = pageNum;
        this.pageSize = pageSize;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public String getForeignKey2() {
        return foreignKey2;
    }

    public void setForeignKey2(String foreignKey2) {
        this.foreignKey2 = foreignKey2;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
