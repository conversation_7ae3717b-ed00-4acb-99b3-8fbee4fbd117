package com.extracme.evcard.membership.core.service;

import com.extracme.evcard.membership.core.dto.CreateWxQrCodeResp;
import com.extracme.evcard.membership.core.dto.MmpShortLinkManagementDTO;
import com.extracme.evcard.membership.core.dto.OperatorDto;
import com.extracme.evcard.membership.core.dto.input.CreateWxQrCodeDto;
import com.extracme.evcard.membership.core.dto.input.CreateWxQrCodeInput;
import com.extracme.evcard.membership.core.dto.input.WxQrCodeDto;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.framework.core.bo.PageBeanBO;

import java.util.List;
import java.util.Map;

/**
 * 短链接相关接口
 * <AUTHOR>
 * @date 2024.07.01
 */
public interface IShortlinkManagementService {

    public MmpShortLinkManagementDTO insertOrUpdateShortlink(MmpShortLinkManagementDTO mmpShortLinkManagementDTO, OperatorDto operatorDto);
    public PageBeanBO<MmpShortLinkManagementDTO> getShortlinkList(MmpShortLinkManagementDTO mmpShortLinkManagementDTO);

    public String getLongUrl(String randomCode);
    public String getLongUrlParams(String randomCode);

    public MmpShortLinkManagementDTO reCreateQRCode(MmpShortLinkManagementDTO mmpShortLinkManagementDTO);

    public String createQrCode(String shortUrl,String randomCode);

    String createWxQrCode(String shortLinkName, String originalUrl, String weChatPage, int isHidden, String dir, OperatorDto operatorDto);
    String createWxQrCodeRetry(String shortLinkName, String originalUrl, String weChatPage, int isHidden, String dir, OperatorDto operatorDto,int retryNum);

    Map<String, WxQrCodeDto> batchCreateWxQrCode(List<CreateWxQrCodeDto> createWxQrCodeDtos, String dir, OperatorDto operatorDto) throws BusinessException;

    List<MmpShortLinkManagementDTO> getMmpShortLinkManagementByOriginUrl(String originUrl);
}
