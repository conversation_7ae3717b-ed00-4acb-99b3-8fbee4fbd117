package com.extracme.evcard.membership.core.input;

import java.io.Serializable;

public class UploadServiceVerInput implements Serializable{
    private static final long serialVersionUID = 1971256649947712856L;

    private String authId;

    private Integer memberType;

    private String updateUser;

    @Deprecated
    private String token;

    /**
     * 会员条款版本号
     */
    private String membershipPolicyVersion;

    /**
     * 隐私政策版本号
     */
    private String privacyPolicyVersion;

    public String getMembershipPolicyVersion() {
        return membershipPolicyVersion;
    }

    public void setMembershipPolicyVersion(String membershipPolicyVersion) {
        this.membershipPolicyVersion = membershipPolicyVersion;
    }

    public String getPrivacyPolicyVersion() {
        return privacyPolicyVersion;
    }

    public void setPrivacyPolicyVersion(String privacyPolicyVersion) {
        this.privacyPolicyVersion = privacyPolicyVersion;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public Integer getMemberType() {
        return memberType;
    }

    public void setMemberType(Integer memberType) {
        this.memberType = memberType;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }
    @Deprecated
    public String getToken() {
        return token;
    }
    @Deprecated
    public void setToken(String token) {
        this.token = token;
    }
}
