package com.extracme.evcard.membership.core.input;


import java.io.Serializable;
import java.util.Date;

/**
 *
 */
public class UserOperationLogInput implements Serializable {

    private static final long serialVersionUID = 7870495345300006500L;

    private Long userId;

    private String authId;

    private Integer membershipType = 0;

    /**  0 OCR识别 1 人脸认证 2 审核  3 账户注销  4 账户恢复 5 密码修改 6 密码重置 7 密码找回 8 修改手机号 9 更换设备绑定 10 注销失败 */
    private Long operationType;

    private String operationContent;

    private String refKey1;

    private String refKey2;

    private String refKey3;

    private String miscDesc;

    private String operator;

    private Long operatorId;

    private Date operationTime;

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public Long getOperationType() {
        return operationType;
    }

    public void setOperationType(Long operationType) {
        this.operationType = operationType;
    }

    public void setOperationContent(String operationContent) {
        this.operationContent = operationContent;
    }

    public String getOperationContent() {
        return operationContent;
    }

    public String getRefKey1() {
        return refKey1;
    }

    public void setRefKey1(String refKey1) {
        this.refKey1 = refKey1;
    }

    public String getRefKey2() {
        return refKey2;
    }

    public void setRefKey2(String refKey2) {
        this.refKey2 = refKey2;
    }

    public String getRefKey3() {
        return refKey3;
    }

    public void setRefKey3(String refKey3) {
        this.refKey3 = refKey3;
    }

    public String getMiscDesc() {
        return miscDesc;
    }

    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Date getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(Date operationTime) {
        this.operationTime = operationTime;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public Integer getMembershipType() {
        return membershipType;
    }

    public void setMembershipType(Integer membershipType) {
        this.membershipType = membershipType;
    }
}