package com.extracme.evcard.membership.core.dto.blacklist;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *  新增/修改渠道黑名单信息
 */
@Data
public class UpdateChannelBlacklistDto implements Serializable {

    /**
     * 主键
     *
     */
    private Integer id;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 手机号
     */
    private String mobilePhone;

    /**
     * 黑名单类型：1=黑名单
     */
    private Integer blacklistType;

    /**
     * 证件类型: 1=身份证 2=其他
     */
    private Integer certificateType;

    /**
     * 证件号码
     */
    private String certificateNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 启用状态:1= 启用 2=禁用
     */
    private Integer enableStatus;

    /**
     * 操作人
     */
    private String operateUser;
    private Long operateUserId;

    /**
     * 黑名单来源：1=手动添加，2=会员系统同步
     */
    private Integer blacklistSource;

}
