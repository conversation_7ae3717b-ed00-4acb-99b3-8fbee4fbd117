package com.extracme.evcard.membership.core.input;

import java.io.Serializable;

public class SMSVerifyBeanInput implements Serializable {
    private static final long serialVersionUID = -4800544418132315857L;

    /** 手机号 **/
    private String mobilePhone;

    /** 类型 0:通用类型验证码 1：找回密码 2：注册 3：设备切换 4:修改手机号 5 登录 6 退款校验 7 注销账户 8 车机使用 9 预付款退款校验 **/
    private int type = -1;

    /** 验证码类型  0  短信验证码 1 语音验证码 */
    private int verifyCodeType = 0;

    private String orgId;

    private String verifyCode;

    /**
     * 是否判断用户存在
     */
    private Boolean userExist=true;
    /**
     * 渠道.
     */
    private String appKey;
    
    /**
     * 操作人(建议传接口名称)
     */
    private String optUser;

    /** 设备号 */
    private String imei;

    public String getVerifyCode() {
        return verifyCode;
    }

    public void setVerifyCode(String verifyCode) {
        this.verifyCode = verifyCode;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getVerifyCodeType() {
        return verifyCodeType;
    }

    public void setVerifyCodeType(int verifyCodeType) {
        this.verifyCodeType = verifyCodeType;
    }

    public Boolean getUserExist() {
        return userExist;
    }

    public void setUserExist(Boolean userExist) {
        this.userExist = userExist;
    }

	public String getAppKey() {
		return appKey;
	}

	public void setAppKey(String appKey) {
		this.appKey = appKey;
	}

	public String getOptUser() {
		return optUser;
	}

	public void setOptUser(String optUser) {
		this.optUser = optUser;
	}

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }
}
