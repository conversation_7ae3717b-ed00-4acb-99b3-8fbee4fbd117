package com.extracme.evcard.membership.core.dto.input;

import java.io.Serializable;

public class CRGTMemberInputDto implements Serializable {
    private static final long serialVersionUID = 4112069421318350702L;

    private String authId;
    //姓名
    private String name;

    //手机号
    private String phoneNumber;

    //驾照号码
    private String licenseNo;

    //准驾车型
    private String licenseLevel;

    //驾照领取时间
    private String licenseStartTime;

    //驾照到期时间
    private String licenseEndTime;

    //驾照第一页url
    private String licensePicFirst;

    //驾照第二页url
    private String licensePicSecond;

    //实名认证视频
    private String faceVideo;

    //实名认证截图
    private String facePic;

    //驾驶证档案编号
    private String licenseId;

    private String appKey;

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getLicenseNo() {
        return licenseNo;
    }

    public void setLicenseNo(String licenseNo) {
        this.licenseNo = licenseNo;
    }

    public String getLicenseLevel() {
        return licenseLevel;
    }

    public void setLicenseLevel(String licenseLevel) {
        this.licenseLevel = licenseLevel;
    }

    public String getLicenseStartTime() {
        return licenseStartTime;
    }

    public void setLicenseStartTime(String licenseStartTime) {
        this.licenseStartTime = licenseStartTime;
    }

    public String getLicenseEndTime() {
        return licenseEndTime;
    }

    public void setLicenseEndTime(String licenseEndTime) {
        this.licenseEndTime = licenseEndTime;
    }

    public String getLicensePicFirst() {
        return licensePicFirst;
    }

    public void setLicensePicFirst(String licensePicFirst) {
        this.licensePicFirst = licensePicFirst;
    }

    public String getLicensePicSecond() {
        return licensePicSecond;
    }

    public void setLicensePicSecond(String licensePicSecond) {
        this.licensePicSecond = licensePicSecond;
    }

    public String getFaceVideo() {
        return faceVideo;
    }

    public void setFaceVideo(String faceVideo) {
        this.faceVideo = faceVideo;
    }

    public String getFacePic() {
        return facePic;
    }

    public void setFacePic(String facePic) {
        this.facePic = facePic;
    }

    public String getLicenseId() {
        return licenseId;
    }

    public void setLicenseId(String licenseId) {
        this.licenseId = licenseId;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }
}
