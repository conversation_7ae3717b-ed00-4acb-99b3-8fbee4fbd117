package com.extracme.evcard.membership.credit.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 积分发放参数
 * <AUTHOR>
 * @Discription
 * @date 2020/8/12
 */
@Data
public class MemberPointsOfferDto implements Serializable {
    /**
     * 会员id，必须
     */
    private String authId;

    /**
     * 积分类别: 缺省为01标准积分
     */
    private String pointsType;

    /**
     * 积分推送事件类型，必须
     * 参考 enum MemPointsPushEnum
     */
    private Integer eventType;

    /**
     * 发放的积分数量，非必须
     * 事件类型为固定发放时，必须
     * 事件为抽奖积分时，必须
     */
    private Integer pointsNum;

    /**
     * 事件关联编号:
     * 如对于
     * 订单支付事件为订单编号
     * e币充值为充值记录编号
     * 碳减排订单分享积分为订单编号
     */
    private String eventRefSeq;

    /**
     * 涉及的金额：
     * 订单实付金额/押金、e币充值金额等 必须，默认为0.
     */
    private BigDecimal amount;

    /**
     * 发放原因，非必须
     * 事件类型为固定发放时，必须
     */
    private String remark;

    /**
     * 触发时间
     */
    private Date createTime;

    /**
     * 积分发放参数，非必传
     */
    //private Map<String, Object> properties;
    private String details;

    /**
     * 推送记录ID, 非必传
     */
    private Long recordId;
    /**
     * 推送流水号: 自动生成, 非必传
     */
    private String requestKey;

    /**
     * 消息通知标识，非必传； 1=站内消息
     */
    private int messageFlag;
}
