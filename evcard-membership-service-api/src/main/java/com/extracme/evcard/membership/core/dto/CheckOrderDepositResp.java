package com.extracme.evcard.membership.core.dto;

import com.extracme.evcard.membership.core.dto.input.CheckDepositResult;

import java.io.Serializable;
import java.math.BigDecimal;

public class CheckOrderDepositResp extends CheckDepositResult implements Serializable {
    /**
     * 会员ID
     */
    private String authId;


    /**
     * 押金状态 0：不免押、 1：免押
     */
    private Integer depositStatus;

    /**
     * 车型等级
     */
    private Integer vehicleModelType;

    /**
     * 车型名称
     */
    private String vehicleModelInfo;

    /**
     * 网点/门店对应的机构编号
     */
    private String orgCode;


    /**
     * 会员基础押金
     */
    private BigDecimal deposit = BigDecimal.ZERO;

    /**
     * 会员车辆押金
     */
    private BigDecimal vehicleDeposit = BigDecimal.ZERO;

    /**
     * 车型对应基础押金
     */
    private BigDecimal needDeposit = BigDecimal.ZERO;

    /**
     * 车型对应车辆押金
     */
    private BigDecimal needVehicleDeposit = BigDecimal.ZERO;

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public Integer getDepositStatus() {
        return depositStatus;
    }

    public void setDepositStatus(Integer depositStatus) {
        this.depositStatus = depositStatus;
    }

    public Integer getVehicleModelType() {
        return vehicleModelType;
    }

    public void setVehicleModelType(Integer vehicleModelType) {
        this.vehicleModelType = vehicleModelType;
    }

    public String getVehicleModelInfo() {
        return vehicleModelInfo;
    }

    public void setVehicleModelInfo(String vehicleModelInfo) {
        this.vehicleModelInfo = vehicleModelInfo;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public BigDecimal getDeposit() {
        return deposit;
    }

    public void setDeposit(BigDecimal deposit) {
        this.deposit = deposit;
    }

    public BigDecimal getVehicleDeposit() {
        return vehicleDeposit;
    }

    public void setVehicleDeposit(BigDecimal vehicleDeposit) {
        this.vehicleDeposit = vehicleDeposit;
    }

    public BigDecimal getNeedDeposit() {
        return needDeposit;
    }

    public void setNeedDeposit(BigDecimal needDeposit) {
        this.needDeposit = needDeposit;
    }

    public BigDecimal getNeedVehicleDeposit() {
        return needVehicleDeposit;
    }

    public void setNeedVehicleDeposit(BigDecimal needVehicleDeposit) {
        this.needVehicleDeposit = needVehicleDeposit;
    }
}
