package com.extracme.evcard.membership.core.input;

import com.extracme.evcard.rpc.dto.Page;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


public class QueryCardInfoHistoryListIConditionInput implements Serializable {

    private String authId;

    private Integer memberType;

    private List<OrderColumn> orderCondition;

    private Page page;

    public QueryCardInfoHistoryListIConditionInput() {
        orderCondition = new ArrayList<>();
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public Integer getMemberType() {
        return memberType;
    }

    public void setMemberType(Integer memberType) {
        this.memberType = memberType;
    }

    public List<OrderColumn> getOrderCondition() {
        return orderCondition;
    }

    public void setOrderCondition(List<OrderColumn> orderCondition) {
        this.orderCondition = orderCondition;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }

    public void addOrderCondition(OrderColumnNameEnum columnName, boolean desc) {
        OrderColumn orderColumn = new OrderColumn(columnName, desc);
        this.orderCondition.add(orderColumn);
    }

    private static class OrderColumn implements Serializable{

        private String columnName;

        private boolean desc;

        public OrderColumn() {
        }

        public OrderColumn(OrderColumnNameEnum columnName, boolean desc) {
            this.columnName = columnName.getColumnName();
            this.desc = desc;
        }

        public String getColumnName() {
            return columnName;
        }

        public void setColumnName(String columnName) {
            this.columnName = columnName;
        }

        public boolean isDesc() {
            return desc;
        }

        public void setDesc(boolean desc) {
            this.desc = desc;
        }
    }

    public enum  OrderColumnNameEnum{

        CARD_STATUS("CARD_STATUS"),
        CREATE_TIME("CREATED_TIME");

        private String columnName;

        OrderColumnNameEnum(String columnName) {
            this.columnName = columnName;
        }

        public String getColumnName() {
            return columnName;
        }
    }
}
