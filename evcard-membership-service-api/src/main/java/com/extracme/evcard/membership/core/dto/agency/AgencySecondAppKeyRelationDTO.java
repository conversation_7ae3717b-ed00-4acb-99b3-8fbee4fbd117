package com.extracme.evcard.membership.core.dto.agency;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 查询二级渠道与代理商关系响应参数
 */
@Data
public class AgencySecondAppKeyRelationDTO implements Serializable {
    private static final long serialVersionUID = 6030752027677050752L;


    /**
     * 主键ID
     */
    private Long id;

    /**
     * 企业ID
     */
    private String agencyId;

    /**
     * 企业名称
     */
    private String agencyName;


    /**
     * 二级渠道AppKey
     */
    private String secondAppKey;

    /**
     * 二级渠道名称
     */
    private String secondAppKeyName;

    /**
     * 一级渠道AppKey
     */
    private String appKey;
    /**
     * 一级渠道名称
     */
    private String appKeyName;

    /**
     * 平台ID
     */
    private Long platformId;
    /**
     * 平台名称
     */
    private String platformName;


    /**
     * 规则ID
     */
    private Long agencyRoleId;
    /**
     * 规则名称
     */
    private String agencyRoleName;
    /**
     * 免押金额
     */
    private String exemptionAmount;

    /**
     * 支付方式（0：个人支付  1：企业支付）
     */
    private Integer agencyPayType;


    /**
     * 微信太阳码图片URL地址
     */
    private String wechatQrPicUrl;



    /**
     * 状态（0=正常 1=已删除）
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人名称
     */
    private String createOperName;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人名称
     */
    private String updateOperName;
}
