package com.extracme.evcard.membership.core.input;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2019/8/23
 */
@Data
public class QueryMemberInputDTO implements Serializable{
    private static final long serialVersionUID = 8228408324375955947L;

    /** 会员类型 */
    private Integer memberType;

    /** 会员id */
    private String authId;

    /** 证件号 */
    private String driverCode;

    /** 机构Id */
    private String orgId;

    /** 手机号 */
    private String mobilePhone;

    /** 人员状态  0：正常  1：休息 2：离职 */
    private List<Integer> personnelState;

    /** 姓名 */
    private String name;

    /** 邮箱 */
    private String email;


}
