package com.extracme.evcard.membership.core.exception;


public class UserMessageException extends Exception {
    private static final long serialVersionUID = 7550368561301879058L;

    protected int code;
    protected String message;

    public UserMessageException() {
        super();
    }
    public UserMessageException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    @Override
    public Throwable fillInStackTrace() {
        return null;
    }

    public int getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public static UserMessageException SAVE_USER_MESSAGE_EXP = new UserMessageException(1,"保存用户信息异常");
    public static UserMessageException SAVE_CARD_ACTION_EXP = new UserMessageException(2,"保存用户刷卡行为异常");
    public static UserMessageException PARAM_ERROR = new UserMessageException(3,"传入参数错误");
    public static UserMessageException MOBILE_PHONE_ERROR = new UserMessageException(-1,"手机格式不正确");

    public static UserMessageException IMEI_EMPTY = new UserMessageException(-1,"无法登陆，请授权EVCARD获取设备IMEI信息");
    public static UserMessageException USER_NO_EXIST = new UserMessageException(-1,"用户不存在");
    public static UserMessageException CHANGE_DEVICE_MAX = new UserMessageException(3,"您本月已更换登录设备次数以达上限，请使用上一次登录的设备或以游客身份登录");
    public static UserMessageException CHANGE_DEVICE_MAX2 = new UserMessageException(3,"您本月更换设备次数已达上限，仅能以游客身份登录");
    public static UserMessageException CHANGE_DEVICE = new UserMessageException(2,"您的账号与上次登录该设备账号不一致");
    public static UserMessageException VERIFY_CODE_EMPTY = new UserMessageException(-1,"验证码为空");
    public static UserMessageException GET_VERIFY_CODE = new UserMessageException(-1,"请获取验证码");
    public static UserMessageException VERIFY_CODE_ERROR = new UserMessageException(-1,"验证码错误");
    public static UserMessageException ERROR_USER = new UserMessageException(-1,"非法用户");
    public static UserMessageException USER_EXITED = new UserMessageException(-1,"该手机号已被注册");
    public static UserMessageException USER_NOT_EXITED = new UserMessageException(2,"该手机号未注册");
    public static UserMessageException ACCOUNT_LOGOUT_ERROR = new UserMessageException(-1,"该手机号已被注销");
    public static UserMessageException ACCOUNT_LOGOUT_ERROR_1 = new UserMessageException(-1,"该手机号还在注销冻结期中，暂时无法修改为新手机号码");
    public static UserMessageException ACCOUNT_LOGOUT_FREEZE_ERROR = new UserMessageException(3, "该账号%s还在注销冻结期中，暂时无法注册");
    public static UserMessageException VERIFY_CODE_MAX = new UserMessageException(-1,"不可频繁获取验证码!");
    public static UserMessageException ONE_MIN_CODE_MAX = new UserMessageException(-1,"1分钟内不可以频繁获取短信验证码");
    public static UserMessageException PASSWORD_WRONG = new UserMessageException(-1,"用户名或密码错误");
    public static UserMessageException ORG_USER_NO_PHONE = new UserMessageException(-1,"该用户没有设置手机号，无法找回密码，请联系客户!");
    public static UserMessageException ORG_USER_PHONE_ERROR = new UserMessageException(-1,"该用户的手机号不符合要求，无法下发验证码，无法找回密码，请联系客户!");
    public static UserMessageException MEMBER_NOT_EXIST = new UserMessageException(1000,"会员不存在");
    public static UserMessageException MOBILE_PHONE_EMPTY = new UserMessageException(1001,"请输入手机号");
    public static UserMessageException NEW_PASSWORD_EMPTY = new UserMessageException(1002,"请输入新密码！");
    public static UserMessageException SYSTEM_BUSY = new UserMessageException(1004,"系统繁忙");
    public static UserMessageException VERIFY_CODE_INVALID = new UserMessageException(1004,"验证码错误或者已失效");
    public static UserMessageException OLD_PASSWORD_EMPTY = new UserMessageException(1005,"修改前的密码不可以为空！");
    public static UserMessageException OLD_PASSWORD_EQUEAL_NEW_PASSWORD = new UserMessageException(1007,"新密码和原密码不能相同");
    public static UserMessageException OLD_PASSWORD_ERROR = new UserMessageException(1008,"原密码错误");
    public static UserMessageException FIRST_CHECK_AUTHID = new UserMessageException(1009,"请先完成身份校验");
    public static UserMessageException MOBILE_PHONE_NUM_ERROR = new UserMessageException(1010,"手机号格式不正确");
    public static UserMessageException MODIFY_MOBILE_ERROR = new UserMessageException(1011,"修改手机号失败");
}
