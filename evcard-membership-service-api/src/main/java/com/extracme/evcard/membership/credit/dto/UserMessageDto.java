package com.extracme.evcard.membership.credit.dto;

import java.io.Serializable;
import java.math.BigDecimal;

public class UserMessageDto implements Serializable {
    private static final long serialVersionUID = 1039636318175213444L;

    /** 用户消息SEQ(SEQ:MESSAGE_SEQ) */
    private Long messageSeq;
    /** 证件号 */
    private String authId;
    /** 会员类型(0:外部会员 1：内部员工） */
    private BigDecimal membershipType;
    /** 消息内容 */
    private String messageContent;
    /** 消息标题 */
    private String messageTitle;
    /** 消息类型 1：网点消息 2：订单消息 3 : 系统消息 */
    private BigDecimal type;
    /** 发送类型:(0:短信 app 1:短信 2:app) */
    private BigDecimal sendType;
    /** 广告URL */
    private String url;
    /** 来源 */
    private String infoOrigin;
    /** 创建用户 */
    private String createdUser;
    /** 创建时间 */
    private String createdTime;
    /** 更新用户 */
    private String updatedUser;
    /** 更新时间 */
    private String updatedTime;

    public Long getMessageSeq() {
        return messageSeq;
    }

    public void setMessageSeq(Long messageSeq) {
        this.messageSeq = messageSeq;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public BigDecimal getMembershipType() {
        return membershipType;
    }

    public void setMembershipType(BigDecimal membershipType) {
        this.membershipType = membershipType;
    }

    public String getMessageContent() {
        return messageContent;
    }

    public void setMessageContent(String messageContent) {
        this.messageContent = messageContent;
    }

    public String getMessageTitle() {
        return messageTitle;
    }

    public void setMessageTitle(String messageTitle) {
        this.messageTitle = messageTitle;
    }

    public BigDecimal getType() {
        return type;
    }

    public void setType(BigDecimal type) {
        this.type = type;
    }

    public BigDecimal getSendType() {
        return sendType;
    }

    public void setSendType(BigDecimal sendType) {
        this.sendType = sendType;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getInfoOrigin() {
        return infoOrigin;
    }

    public void setInfoOrigin(String infoOrigin) {
        this.infoOrigin = infoOrigin;
    }

    public String getCreatedUser() {
        return createdUser;
    }

    public void setCreatedUser(String createdUser) {
        this.createdUser = createdUser;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public String getUpdatedUser() {
        return updatedUser;
    }

    public void setUpdatedUser(String updatedUser) {
        this.updatedUser = updatedUser;
    }

    public String getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(String updatedTime) {
        this.updatedTime = updatedTime;
    }
}
