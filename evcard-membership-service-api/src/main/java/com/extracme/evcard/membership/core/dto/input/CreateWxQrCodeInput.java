package com.extracme.evcard.membership.core.dto.input;

import com.extracme.evcard.membership.core.dto.OperatorDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class CreateWxQrCodeInput implements Serializable {
    // <文件夹, 地址列表>
    Map<String, List<CreateWxQrCodeDto>> createWxQrCodeDtoMap;

    String zipName;

    OperatorDto operatorDto;
}
