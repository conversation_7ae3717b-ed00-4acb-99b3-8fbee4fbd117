package com.extracme.evcard.membership.credit.service;

import java.math.BigDecimal;
import java.util.List;

import com.extracme.evcard.membership.credit.dto.*;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.dto.CommonAddRespDto;
import com.extracme.evcard.rpc.dto.Page;
import com.extracme.evcard.rpc.dto.PageBeanDto;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;

/**
 * Created by Elin on 2017/11/22.
 */
public interface IMemberShipTagServ {

    /**
     * 获取所有信用分事件类型.<br>
     * 返回list对象信息如下：
     * @param id 事件类型编号
     * @param eventName 事件类型名称
     * @param eventNature 事件类型性质 1-正面 0-负面
     * @param amount 分值
     * @param blackList 是否直接加入黑名单 false-否 true-是
     * @return
     */
    List<CreditEventTypeDto> getCreditEventTypes();

    /**
     * 新增信用分事件类型.<br>
     * @param creditEventTypeFullDto
     * @param eventName 事件类型名称
     * @param eventNature 事件类型性质 1-正面 0-负面
     * @param amount 分值
     * @param createOperId 创建人id
     * @param createOperName 创建人姓名
     * @return id 事件类型编号
     */
    CommonAddRespDto addCreditEventType(CreditEventTypeFullDto creditEventTypeFullDto);

    /**
     * 修改信用分事件类型.<br>
     * @param creditEventTypeFullDto
     * @param id 事件类型编号
     * @param amount 分值
     * @param updateOperId 修改人id
     * @param updateOperName 修改人姓名
     * @return id 事件类型编号
     */
    CommonAddRespDto updateCreditEventType(CreditEventTypeFullDto creditEventTypeFullDto);

    /**
     * 查询信用分事件类型列表分页.<br>
     * 查询条件：paramsDto
     * @param eventName 事件类型名称
     * @param eventNature 事件类型性质 1-正面 0-负面
     * @param eventWay 触发方式 0-自动 1-手动
     * 分页对象：
     * @param page 分页条件
     * 如需分页 ，则需要传入 pageNo ,
     *                     pageSize（不传入默认20）
     * 如果需要查询总条数，需传入参数 countFlag=true (是否查询总条数)，不需要则可不传
     * @return PageBeanDto<CreditEventTypeFullDto>
     * page.pageNo
     * page.pageSize
     * page.count 总条数
     * 返回list对象信息如下：
     * @param id 事件类型编号
     * @param eventName 事件类型名称
     * @param eventNature 事件类型性质 1-正面 0-负面
     * @param amount 分值
     * @param eventWay 触发方式 0-自动 1-手动
     * @param blackList 是否直接加入黑名单 false-否 true-是
     * @param createTime 创建时间
     * @param createOperId 创建人id
     * @param createOperName 创建人
     */
    PageBeanDto<CreditEventTypePageDto> getCreditEventTypeListPage(CreditEventTypeParamsDto paramsDto, Page page);

    /**
     * 新增信用事件记录.<br>
     * @param creditEventRecordFullDto
     * @param authId 会员id
     * @param orderSeq 订单编号
     * @param eventTypeId 事件类型
     * @param eventDesc 事件记录详情
     * @param eventSource 操作来源
     * @param eventName 事件类型名称
     * @param eventImagePath 图片地址 多张逗号分隔
     * @param eventFilePath 事件凭证 文件路径
     * @param eventNature 事件类型性质 1-正面 0-负面
     * @param amount 分值
     * @param blackList 是否直接加入黑名单 false-否 true-是
     * @param createOperId 创建人id
     * @param createOperName 创建人姓名
     * @return id 事件编号
     */
    CommonAddRespDto saveCreditEventRecord(CreditEventRecordFullDto creditEventRecordFullDto);

    /**
     * 获取会员负面记录.<br>
     * @param authId 会员id
     * @param page
     * 分页对象：
     * @param page 分页条件
     * 如需分页 ，则需要传入 pageNo ,
     *                     pageSize（不传入默认20）
     * 如果需要查询总条数，需传入参数 countFlag=true (是否查询总条数)，不需要则可不传
     * @return PageBeanDto<AuthCreditEventRecordPageDto>
     * page.pageNo
     * page.pageSize
     * page.count 总条数
     * 返回list对象信息如下：
     * @param eventId 事件编号
     * @param authId 会员id
     * @param eventTypeId 事件类型id
     * @param eventName 事件类型名称
     * @param blackList 是否直接加入黑名单 false-否 true-是
     * @param eventSource 操作来源
     * @param eventStatus 事件状态（0-撤销 1-正常）
     * @param appealStatus 申诉状态（0-未申诉 1 已申诉）
     * @param createTime 创建时间
     * @param createOperId 创建人id
     * @param createOperName 创建人姓名
     * @return
     */
    PageBeanDto<AuthCreditEventRecordPageDto> getAuthCreditEventRecordListPage(String authId , Page page);

    /**
     * 获取信用事件详情.<br>
     * @param authId 会员id
     * @param eventId 事件编号
     * 返回对象如下：
     * @param authId 会员id
     * @param authName 会员姓名
     * @param mobilePhone 手机号
     * @param orgName 会员所属
     * @param eventId 事件编号
     * @param eventName 事件类型名称
     * @param eventStatus 事件状态（0-撤销 1-正常）
     * @param blackList 是否直接加入黑名单 false-否 true-是
     * @param eventCreateTime 创建时间
     * @param eventCreateOperId 创建人id
     * @param eventCreateOperName 创建人姓名
     * @param eventSource 操作来源
     * @param eventDesc 事件记录详情
     * @param eventImagePath 图片地址 多张逗号分隔
     * @param eventFilePath 事件凭证 文件路径
     * @param eventNature 事件类型性质 1-正面 0-负面
     * @param appealId 申诉编号id
     * @param appealTime 申诉时间
     * @param appealStatus 申诉状态（0-未申诉 1 已申诉）
     * @param appealDesc 申诉描述
     * @param appealImagePath 申诉凭证上传图片路径，多张以逗号分隔
     * @param appealFilePath 申诉凭证 文件路径
     * @param handleUserId 处理人id
     * @param handleUser 处理人
     * @param handleTime 处理时间
     * @param handleRemark 通知话术
     * @param handleResult 处理结果0-未处理 1-同意 2-驳回
     * @return
     */
    CreditEventRecordDetailDto getCreditEventRecordDetail(String authId, Long eventId);

    /**
     * 获取申诉事件列表.<br>
     * @param paramsDto 查询条件
     * @param appealId 申诉编号
     * @param appealStartTime 申诉开始时间
     * @param appealEndTime 申诉结束时间
     * @param mobilePhone 手机号
     * @param handleResult 处理状态 0-待处理 1-已处理
     * @param eventName 事件类型
     * @param page 分页条件
     * 如需分页 ，则需要传入 pageNo ,
     *                     pageSize（不传入默认20）
     * 如果需要查询总条数，需传入参数 countFlag=true (是否查询总条数)，不需要则可不传
     * @return PageBeanDto<CreditEventAppealRecordPageDto>
     * page.pageNo
     * page.pageSize
     * page.count 总条数
     * 返回list对象信息如下：
     * @param authId 会员id
     * @param eventId 事件编号
     * @param appealId 申诉编号
     * @param appealTime 申诉时间
     * @param appealStatus 申诉状态（0-未申诉 1 已申诉）
     * @param mobilePhone 手机号
     * @param handleResult 处理状态 0-待处理 1-已处理
     * @param authName 申诉人
     * @param eventName 事件类型
     * @return
     */
    PageBeanDto<CreditEventAppealRecordPageDto> getCreditEventAppealRecordPage(CreditEventAppealRecordParamsDto paramsDto,
                                                                               Page page);

    /**
     * 获取信用分事件记录列表.<br>
     * @param paramsDto 查询条件
     * @param eventId 事件编号
     * @param authName 姓名
     * @param mobilePhone  手机号
     * @param orgId 会员所属公司
     * @param eventName   事件类型
     * @param eventNature 事件类型性质 1-正面 0-负面
     * @param eventStatus 事件状态（0-撤销 1-正常）
     * @param eventStartTime 事件操作时间开始
     * @param eventEndTime 事件操作时间结束
     * @param page            分页条件
     * 如需分页 ，则需要传入 pageNo ,
     *                     pageSize（不传入默认20）
     * 如果需要查询总条数，需传入参数 countFlag=true (是否查询总条数)，不需要则可不传
     * @return PageBeanDto<CreditEventAppealRecordPageDto>
     * page.pageNo
     * page.pageSize
     * page.count 总条数
     * 返回list对象信息如下：
     * @param eventId     事件编号
     * @param authId      会员id
     * @param authName    姓名
     * @param mobilePhone 手机号
     * @param orgId       会员所属公司id
     * @param orgName     会员所属公司
     * @param eventName   事件类型
     * @param eventNature 事件类型性质 1-正面 0-负面
     * @param eventStatus 事件状态（0-撤销 1-正常）
     * @param createTime  创建时间（发生时间）
     * @return
     */
    PageBeanDto<CreditEventRecordPageDto> getCreditEventRecordPage(CreditEventRecordParamsDto paramsDto,
                                                                   Page page);
    /**
     * 处理信用分申诉事件 同意 拒绝.<br>
     * @param creditEventAppealHandleDto
     * @param eventId  事件编号
     * @param appealId 申诉编号
     * @param authId   会员id
     * @param handleUserId 处理人id
     * @param handleUser 处理人姓名
     * @param handleResult  处理状态 1-同意  2-拒绝
     * @param handleRemark 通知话术
     * @param updateOperId 修改人id
     * @param updateOperName  修改人姓名
     * @return
     */
    BaseResponse handleAppealEventStatus(CreditEventAppealHandleDto creditEventAppealHandleDto);


    /**
     * 进行信用分事件申诉.<br>
     * @param addCreditEventAppealDto
     * @param eventId 事件编号 必须
     * @param authId 会员id    必须
     * @param appealDesc 申诉描述  申诉凭证和申诉描述至少有一项
     * @param appealImagePath 申诉凭证上传图片路径，多张以逗号分隔
     * @param appealFilePath 申诉凭证文件路径 申诉凭证和申诉描述至少有一项
     * @param createOperId 创建人id 必须
     * @param createOperName 创建人 必须
     * @return id 申诉编号
     */
    CommonAddRespDto saveAppealEvent(AddCreditEventAppealDto addCreditEventAppealDto);


    /**
     * 获取信用分事件类型分析列表.<br>
     * @param paramsDto
     * @param yearNum 年份
     * @param orgId 所属公司id
     * @param type 查询类型 0-各事件类型次数 1-各事件类型会员
     * @param page
     * 如需分页 ，则需要传入 pageNo ,
     *                     pageSize（不传入默认20）
     * 如果需要查询总条数，需传入参数 countFlag=true (是否查询总条数)，不需要则可不传
     * @return PageBeanDto<CreditEventTypeReportPageDto>
     * page.pageNo
     * page.pageSize
     * page.count 总条数
     * 返回list对象信息如下：
     * @param id 编号
     * @param yearNum 年份
     * @param orgId 所属公司id
     * @param type 查询类型 0-各事件类型次数 1-各事件类型会员
     * @param eventName 事件类型
     * @param month 月份 1-12
     * @param total 数量
     */
    PageBeanDto<CreditEventTypeReportPageDto> getCreditEventTypeReportPage(CreditEventTypeReportParamsDto paramsDto,
                                                                           Page page);

    /**
     * 信用分事件类型分析数据导出.<br>
     * @param paramsDto
     * @param yearNum 年份
     * @param orgId 所属公司id
     * @param type 查询类型 0-各事件类型次数 1-各事件类型会员
     */
    PageBeanDto<CreditEventTypeReportPageDto> getCreditEventTypeReportExport(CreditEventTypeReportParamsDto paramsDto);


    /**
     * 信用分事件类型分析数据保存.<br>
     * @param addCreditEventTypeReportDto
     * @param yearNum 年份
     * @param type 查询类型 0-各事件类型次数 1-各事件类型会员数
     * @param orgId 所属公司id
     * @param eventName 事件名称
     * @param month 1-12 月
     * @param total 数量
     * @return
     */
    CommonAddRespDto saveCreditEventTypeReport(AddCreditEventTypeReportDto addCreditEventTypeReportDto);
    
    
    /**
	 * 获取用户的有效消费金额.<br>
	 * MemberShipRecordTag 代替了该方法.<br>
	 * @param authId
	 * @return
	 */
    @Deprecated
	BigDecimal consumAmount(String authId);
	
    /**
     * 获取会员的用车以及邀请奖励记录统计.<br>
     * @param authId
     * @return	
     */
    MemberShipRecordTagDto memberShipRecordTag(String authId);

}
