package com.extracme.evcard.membership.core.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Discription
 * @date 2021/3/12
 */
@Data
@NoArgsConstructor
public class UserContractInfo implements Serializable {
    /**
     * 协议签署记录id
     */
    private Long id;

    /**
     * 会员id
     */
    private String authId;

    /**
     * 会员编号-法大大
     */
    private String customerId;

    /**
     * 交易id-法大大
     */
    private String transactionId;
    /**
     * 合约id-法大大
     */
    private String contractId;

    /**
     * 下载地址-法大大
     */
    private String downloadUrl;
    /**
     * 查询地址法大大
     */
    private String viewpdfUrl;

    /**
     * 条款-版本id， 如0043
     */
    private String versionId;

    /**
     * 条款版本, 如SZ0043
     */
    private String templateId;

    /**
     * 新增：供应商类别（0法大大 1esign）
     */
    private Integer supplier;

    /**
     * 新增：oss文件地址
     */
    private String archiveUrl;


    private byte[] stream;

    //public
}
