package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;

import lombok.Data;

/**
*
*<AUTHOR>
*/
@Data
public class CardPauseLogDTO implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 8013202751383869423L;
	/**
	 * 会员卡号
	 */
	private String cardNo;
	/**
	 * 会员id
	 */
	private String authId;
	/**
	 * 暂停原因
	 */
	private String pauseReason;
	/**
	 * 会员姓名
	 */
	private String name;
	/**
	 * 会员手机号
	 */
	private String mobilePhone;
	/**
	 * 暂停时间
	 */
	private String createdTime;
	/**
	 * 暂停人
	 */
	private String createdUser;
	/**
	 * 暂停状态(0:已暂停，1:已恢复，2:永久暂停)
	 */
	private Integer pauseStatus;
	/**
	 * 预计恢复时间
	 */
	private Long recoverTime;
	/**
	 * 恢复人
	 */
	private String updatedUser;
	/**
	 * 实际恢复时间
	 */
	private String updatedTime;
}
