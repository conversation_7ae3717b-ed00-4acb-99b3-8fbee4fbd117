package com.extracme.evcard.membership.core.exception;


public class AgencyException extends Exception {
    private static final long serialVersionUID = 7550368561301879058L;

    protected int code;
    protected String message;

    public AgencyException() {
        super();
    }
    public AgencyException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    @Override
    public Throwable fillInStackTrace() {
        return null;
    }

    public int getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public static AgencyException EXIST_AGENCY_NAME = new AgencyException(1001,"机构名重复");
    public static AgencyException CREATE_AGENCY_FAILED = new AgencyException(1002,"新增机构失败");
    public static AgencyException NOT_EXIST_AGENCY = new AgencyException(1003,"机构信息不存在");
    public static AgencyException UPDATE_AGENCY_FAILED = new AgencyException(1004,"更新机构信息失败");

}
