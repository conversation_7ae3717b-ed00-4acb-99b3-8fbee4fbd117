package com.extracme.evcard.membership.contract.service;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 *
 */
@Data
@NoArgsConstructor
public class AddRentCarContractRequest implements Serializable {
    // mid
    private String mid;
    // pdfUrl(未签字)
    private String pdfUrl;
    // 用户签字图片url
    private String userSignPicUrl;
    // 1环球 2赛可
    private int type;
    // 合同号
    private String contractId;

    // 操作类型 1 租车合同 5：结算单 6：租车单
    private int operateType = 1;

    // 用来填充租车合同的 甲方信息
    private String operOrgCode; // 运营公司CODE

    private String operOrgName; // 运营公司名

    // 门店电话
    private String storeMobile;
    // 门店店长电话
    private String storeManagerMobile;
}
