package com.extracme.evcard.membership.credit.dto;

import com.extracme.evcard.rpc.dto.BaseResponse;

/**
 * Created by Elin on 2017/11/22.
 * 进行事件申诉
 */
public class AddCreditEventAppealDto extends BaseResponse {

    private static final long serialVersionUID = -6321239519565147448L;

    /**
     * 事件编号
     */
    private Long eventId;

    /**
     * 会员id
     */
    private String authId;

    /**
     * 申诉描述
     */
    private String appealDesc;

    /**
     * 申诉凭证上传图片路径，多张以逗号分隔
     */
    private String appealImagePath;

    /**
     * 申诉凭证 文件路径
     */
    private String appealFilePath;

    /**
     * 创建人id
     */
    private String createOperId;

    /**
     * 创建人
     */
    private String createOperName;

    public Long getEventId() {
        return eventId;
    }

    public void setEventId(Long eventId) {
        this.eventId = eventId;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public String getAppealDesc() {
        return appealDesc;
    }

    public void setAppealDesc(String appealDesc) {
        this.appealDesc = appealDesc;
    }

    public String getAppealImagePath() {
        return appealImagePath;
    }

    public void setAppealImagePath(String appealImagePath) {
        this.appealImagePath = appealImagePath;
    }

    public String getAppealFilePath() {
        return appealFilePath;
    }

    public void setAppealFilePath(String appealFilePath) {
        this.appealFilePath = appealFilePath;
    }

    public String getCreateOperId() {
        return createOperId;
    }

    public void setCreateOperId(String createOperId) {
        this.createOperId = createOperId;
    }

    public String getCreateOperName() {
        return createOperName;
    }

    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }
}
