package com.extracme.evcard.membership.credit.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/8/12
 */
@Data
public class MemberPointsHistoryDto implements Serializable {
    /**
     * 操作流水号
     */
    private String recordNo;

    /**
     * 收单号
     */
    private String payOrderId;

    /**
     * 变动积分数
     */
    private Integer credits;

    /**
     * 积分变动操作 1消费 2获取
     */
    private String optionType;

    /**
     * 变动日期
     */
    private String optionDate;

    /**
     * 变动原因
     */
    private String changeReason;

    /**
     * 积分到期日
     * if optionType=获取积分 则为获得的积分的到期日期
     */
    private String expireDate;

    /**
     * 变动后剩余积分数
     */
    private Integer balanceCredits;

    /**
     * 若为积分发放， 积分发放事件记录id
     */
    private Long pushRecordId;

    /**
     * 若为积分发放， 积分发放事件记录id
     */
    private String pushDetails;

    /**
     * 操作来源类别
     * 消费： 01商品购物
     * 获取： 0固定积分 1完成认证 2补全资料 3...
     */
    private String causeType;

    /**
     * 操作来源名称
     */
    private String causeTypeName;

    /**
     * 若为发放，则发放事件推送信息
     */
    private MemberPointsPushRecordDto pushRecord;
}
