package com.extracme.evcard.membership.core.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2018/2/8
 */
@Data
public class ContractVersionDto implements Serializable {

    private static final long serialVersionUID = -1723176156209425025L;

    private String templateId;

    private String docTitle;

    private String versionId;

    private Integer supplier;

    private String templateFileId;
    private String ossPdfUrl;

    public ContractVersionDto( String templateId, String versionId, String docTitle) {
        this.templateId = templateId;
        this.docTitle = docTitle;
        this.versionId = versionId;
    }
    public ContractVersionDto( String templateId, String versionId, String docTitle,String ossPdfUrl) {
        this.templateId = templateId;
        this.docTitle = docTitle;
        this.versionId = versionId;
        this.ossPdfUrl=ossPdfUrl;
    }
}
