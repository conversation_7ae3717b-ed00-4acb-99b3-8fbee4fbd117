package com.extracme.evcard.membership.core.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 驾照认证加要素日志详情对象
 * 解析认证详情
 */
@Data
public class LicenseAuthLogDTO implements Serializable {
    /**
     * 活动id
     */
    private Long id;
    /**
     * 驾照认证日志记录id
     */
    private Long recordId;
    /**
     * 姓名
     */
    private String name;
    /**
     * 驾照编号
     */
    private String driverCode;
    /**
     * 文档编号
     */
    private String fileNo;
    /**
     * 认证类别
     */
    private Integer logType;
    /**
     * 供应商类别
     */
    private Integer supplier;

    private String supplierName;
    /**
     * 认证结果
     * 1认证一致 2不一致 3查无记录/无法核查 4异常情况 （驾驶证扣分）
     */
    private String result;
    /**
     * 认证接口返回结果认证code码
     */
    private String resultCode;
    /**
     * 认证接口返回结果信息
     */
    private String resultMsg;
    /**
     * 驾照认证结果(详情)
     */
    private String details;

    private String licenseStatusMsg;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 操作人id
     */
    private Long createOperId;
    /**
     * 操作人姓名
     */
    private String createOperName;
}
