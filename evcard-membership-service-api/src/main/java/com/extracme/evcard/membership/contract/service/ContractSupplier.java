package com.extracme.evcard.membership.contract.service;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.math.NumberUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Discription
 * @date 2021/3/4
 */
@Data
@NoArgsConstructor
public class ContractSupplier implements Serializable {
    /**
     * 供应商类别 0 法大大 1 e签宝
     */
    private Integer type;

    private String name;

    private String key;

    public ContractSupplier(Integer type, String name, String key){
        this.type = type;
        this.name = name;
        this.key = key;
    }

    public static final ContractSupplier FDD = new ContractSupplier(0, "法大大", "fdd");
    public static final ContractSupplier ESIGN = new ContractSupplier(1, "e签宝", "esign");

    public static ContractSupplier getInstance(Integer type) {
        if(NumberUtils.INTEGER_ZERO.equals(type)) {
            return FDD;
        }
        return ESIGN;
    }
}
