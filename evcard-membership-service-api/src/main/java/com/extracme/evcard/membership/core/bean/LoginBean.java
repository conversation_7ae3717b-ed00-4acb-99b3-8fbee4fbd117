package com.extracme.evcard.membership.core.bean;

public class LoginBean  extends BaseBean{

    private String displayName;

    private String authId;

    private int memberType;

    // 0:个人用户 1：企业用户
    private int orgUser;

    private String orgName;

    // 0:未过期 1：已过期
    private int serviceExp;

    private String agencyId;

    private int privacyPolicyExp;

    public int getPrivacyPolicyExp() {
        return privacyPolicyExp;
    }

    public void setPrivacyPolicyExp(int privacyPolicyExp) {
        this.privacyPolicyExp = privacyPolicyExp;
    }

    /**
     * @return the authId
     */
    public String getAuthId() {
        return authId;
    }

    /**
     * @param authId the authId to set
     */
    public void setAuthId(String authId) {
        this.authId = authId;
    }

    /**
     * @return the displayName
     */
    public String getDisplayName() {
        return displayName;
    }

    /**
     * @param displayName the displayName to set
     */
    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }



    /**
     * @return the memberType
     */
    public int getMemberType() {
        return memberType;
    }

    /**
     * @param memberType the memberType to set
     */
    public void setMemberType(int memberType) {
        this.memberType = memberType;
    }


    /**
     * @return the orgUser
     */
    public int getOrgUser() {
        return orgUser;
    }

    /**
     * @param orgUser the orgUser to set
     */
    public void setOrgUser(int orgUser) {
        this.orgUser = orgUser;
    }

    /**
     * @return the orgName
     */
    public String getOrgName() {
        return orgName;
    }



    /**
     * @return the serviceExp
     */
    public int getServiceExp() {
        return serviceExp;
    }

    /**
     * @param serviceExp the serviceExp to set
     */
    public void setServiceExp(int serviceExp) {
        this.serviceExp = serviceExp;
    }

    /**
     * @param orgName the orgName to set
     */
    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }


    public String getAgencyId() {
        return agencyId;
    }

    public void setAgencyId(String agencyId) {
        this.agencyId = agencyId;
    }

    public void print() {
        System.out.println("status:" + this.getStatus());
        System.out.println("message:" + this.getMessage());
        System.out.println("token:" + this.getToken());
        System.out.println("getOrgUser:" + this.getOrgUser());
        if(getStatus() ==0) {
            System.out.println("displayName:" + this.getDisplayName());
            System.out.println("authId:" + this.getAuthId());
        }

    }

}
