package com.extracme.evcard.membership.core.dto.agency;
import lombok.Data;

import java.io.Serializable;


@Data
public class SwitchAgencyDTO implements Serializable {
    private static final long serialVersionUID = -7610266492743565376L;

    /**
     * 用户mid
     */
    private String mid;

    /**
     * 企业ID
     */
    private String oldAgencyId;


    /**
     * 企业ID
     */
    private String newAgencyId;

    /**
     * 二级渠道AppKey
     */
    //private String secondAppKey;

    /**
     * 关联关系id
     */
    private Long relationId;


    /**
     * 短链的随机码
     *  可以查询出 newAgencyId、secondAppKey relationId
     */
    private String randomCode;
}
