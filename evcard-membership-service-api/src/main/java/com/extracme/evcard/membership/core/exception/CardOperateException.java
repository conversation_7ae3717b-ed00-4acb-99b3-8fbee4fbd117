package com.extracme.evcard.membership.core.exception;

import com.extracme.evcard.rpc.exception.BusinessException;

public class CardOperateException extends BusinessException {

    public CardOperateException(int code, String message) {
        super(code, message);
    }

    @Override
    public int getCode() {
        return code;
    }

    public static CardOperateException PARAM_ERROR = new CardOperateException(400000, "传入参数错误");
    public static CardOperateException EMPTY_AUTHID = new CardOperateException(400001, "会员id不能为空");
    public static CardOperateException EMPTY_CARD_ID = new CardOperateException(400002, "卡号不能为空");
    public static CardOperateException EMPTY_MEM_TYPE = new CardOperateException(400003, "会员类别不能为空");
    public static CardOperateException EMPTY_CARD_OPTION = new CardOperateException(400004, "卡操作不能为空");
    public static CardOperateException EMPTY_REMARK = new CardOperateException(400005, "备注不能为空");
    public static CardOperateException UNVALID_REMARK = new CardOperateException(400006, "备注不能超过100个中文字符");
    public static CardOperateException EARLY_RECOVERTIME = new CardOperateException(400007, "恢复时间不能早于当前时间");
    public static CardOperateException ERROR_RECOVERTIME = new CardOperateException(400008, "恢复时间格式不正确");
    public static CardOperateException FAILED_PAUSE_CARD = new CardOperateException(400009, "暂停卡失败");
    public static CardOperateException FAILED_RECOVER_CARD = new CardOperateException(400010, "恢复卡失败");
    public static CardOperateException FAILED_CANCEL_CARD = new CardOperateException(400011, "注销卡失败");

    public static CardOperateException MEMBER_NOT_EXESIT = new CardOperateException(4000094, "会员信息不存在");
    public static CardOperateException FAILED_ALREADY_STOPED = new CardOperateException(400095, "会员卡已暂停或已注销");
    public static CardOperateException FAILED_NOT_NEED_RECOVER = new CardOperateException(400096, "会员卡当前无需恢复");
    public static CardOperateException CARDNO_AUTHID_NOTMATCH = new CardOperateException(4000097, "会员编号与卡号绑定不一致");
    public static CardOperateException CARDNO_NOT_EXESIT = new CardOperateException(4000098, "卡信息不存在");
    public static CardOperateException FAILED_UPDATED_CARD_STATUS = new CardOperateException(400099, "会员卡信息更新失败");

}
