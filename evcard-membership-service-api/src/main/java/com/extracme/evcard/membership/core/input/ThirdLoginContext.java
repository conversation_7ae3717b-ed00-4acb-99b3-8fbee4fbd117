package com.extracme.evcard.membership.core.input;

import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import lombok.Data;

import java.io.Serializable;

@Data
public class ThirdLoginContext implements Serializable {
    ThirdLoginInput input;
    ThirdLoginOtherDto otherDto;
    // TODO 用哪个对象
    MembershipBasicInfo membershipBasicInfo;

    boolean isNewUser = false;

    public ThirdLoginContext() {
        input = new ThirdLoginInput();
        otherDto = new ThirdLoginOtherDto();
        membershipBasicInfo = new MembershipBasicInfo();
    }
}
