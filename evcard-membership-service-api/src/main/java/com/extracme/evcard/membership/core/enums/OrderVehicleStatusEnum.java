package com.extracme.evcard.membership.core.enums;

import org.apache.commons.lang3.StringUtils;

public enum OrderVehicleStatusEnum {

    /**
     * 成功
     */
    SUCCESS(0,"成功"),
    /**
     * 当前押金不足
     */
    DEPOSIT_LESS(3,"当前押金不足"),
    /**
     * 当前押金不足
     */
    BASE_APPLY_DRAWBACK(4,"当前押金不足"),
    /**
     * 当前押金不足
     */
    VEHICLE_APPLY_DRAWBACK(7,"当前押金不足");


    OrderVehicleStatusEnum(int errCode, String errMsg) {
        this.errCode = errCode;
        this.errMsg = errMsg;
    }

    /** 异常code */
    private int errCode;
    /** 异常描述信息 */
    private String errMsg;

    public int getErrCode() {
        return errCode;
    }

    public void setErrCode(int errCode) {
        this.errCode = errCode;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    public static String getMessageByCode(int errCode){
        String message = StringUtils.EMPTY;
        for(OrderVehicleStatusEnum faceRecognitionEnum : OrderVehicleStatusEnum.values()){
            if(errCode == faceRecognitionEnum.getErrCode()){
                message = faceRecognitionEnum.getErrMsg();
                break;
            }
        }
        return message;
    }
}
