package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;

/**
 * 注册入参
 * <AUTHOR>
 * @since 2018/1/31
 */
public class RegisterDto implements Serializable{

    private static final long serialVersionUID = 3615764714926464129L;

    /** 手机号  必填*/
    private String mobilePhone;
    /** 短信验证码  */
    private String smsVerifyCode;
    /** 密码  */
    private String password;
    /** 省  必填*/
    private String province;
    /** 市  必填*/
    private String city;
    /** 区  必填*/
    private String area;
    /** 详细地址 */
    private String address;
    /** 注册来源  0：网点注册 1：网站注册 2：管理平台注册 3:手机APP 4:第三方  5、6 e享天开  7:CRM */
    private int registerOrigin = -1;
    /** 第三方注册 appKey 必填*/
    private String appKey;
    /** 二级渠道key*/
    private String secondAppKey;
    /** 分享人ID */
    private String shareUid;
    /** 设备号 */
    private String imeiNo;
    /** 操作人 */
    private String operateUser;
    /** 邀请码 */
    private String invitationCode;
    /** 机构ID */
    private String orgId;
    /** 匿名id 埋点使用 */
    private String anonymousId;
    /** 埋点平台 */
    private String trackPlatForm;
    /** 享道统一账户ID */
    private String uid;
    /** 会员条款版本号 */
    private String membershipPolicyVersion;
    /** 隐私政策版本号 */
    private String privacyPolicyVersion;

    /**是否同意条款true：同意了*/
    private Boolean agreePolicy= true;

    /**
     * 1:手机号 2：微信 3：支付宝 4：apple id
     */
    private int registerWay;

    /**
     * 城市运营模式
     *  0 大库  1门店
     */
    private String  operationModel;

    /**
     * 手机厂商
     */
    private String manufacturer;

    private int isFictional;

    public int getIsFictional() {
        return isFictional;
    }

    public void setIsFictional(int isFictional) {
        this.isFictional = isFictional;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public int getRegisterWay() {
        return registerWay;
    }

    public void setRegisterWay(int registerWay) {
        this.registerWay = registerWay;
    }

    public String getOperationModel() {
        return operationModel;
    }

    public void setOperationModel(String operationModel) {
        this.operationModel = operationModel;
    }

    public String getAnonymousId() {
        return anonymousId;
    }

    public void setAnonymousId(String anonymousId) {
        this.anonymousId = anonymousId;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getInvitationCode() {
        return invitationCode;
    }

    public void setInvitationCode(String invitationCode) {
        this.invitationCode = invitationCode;
    }

    public RegisterDto(){

    }


    public RegisterDto(String mobilePhone, String smsVerifyCode, String password, String province, String city, String area, String address, int registerOrigin) {
        this.mobilePhone = mobilePhone;
        this.smsVerifyCode = smsVerifyCode;
        this.password = password;
        this.province = province;
        this.city = city;
        this.area = area;
        this.address = address;
        this.registerOrigin = registerOrigin;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getSmsVerifyCode() {
        return smsVerifyCode;
    }

    public void setSmsVerifyCode(String smsVerifyCode) {
        this.smsVerifyCode = smsVerifyCode;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public int getRegisterOrigin() {
        return registerOrigin;
    }

    public void setRegisterOrigin(int registerOrigin) {
        this.registerOrigin = registerOrigin;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getShareUid() {
        return shareUid;
    }

    public void setShareUid(String shareUid) {
        this.shareUid = shareUid;
    }

    public String getImeiNo() {
        return imeiNo;
    }

    public void setImeiNo(String imeiNo) {
        this.imeiNo = imeiNo;
    }

    public String getOperateUser() {
        return operateUser;
    }

    public void setOperateUser(String operateUser) {
        this.operateUser = operateUser;
    }

    public String getTrackPlatForm() {
        return trackPlatForm;
    }

    public void setTrackPlatForm(String trackPlatForm) {
        this.trackPlatForm = trackPlatForm;
    }
    
    public String getUid() {
		return uid;
	}

	public void setUid(String uid) {
		this.uid = uid;
	}

    public String getMembershipPolicyVersion() {
        return membershipPolicyVersion;
    }

    public void setMembershipPolicyVersion(String membershipPolicyVersion) {
        this.membershipPolicyVersion = membershipPolicyVersion;
    }

    public String getPrivacyPolicyVersion() {
        return privacyPolicyVersion;
    }

    public void setPrivacyPolicyVersion(String privacyPolicyVersion) {
        this.privacyPolicyVersion = privacyPolicyVersion;
    }

    public Boolean getAgreePolicy() {
        return agreePolicy;
    }

    public void setAgreePolicy(Boolean agreePolicy) {
        this.agreePolicy = agreePolicy;
    }

    public String getSecondAppKey() {
        return secondAppKey;
    }

    public void setSecondAppKey(String secondAppKey) {
        this.secondAppKey = secondAppKey;
    }

    @Override
    public String toString() {
        return "RegisterDto{" +
                "mobilePhone='" + mobilePhone + '\'' +
                ", smsVerifyCode='" + smsVerifyCode + '\'' +
                ", password='" + password + '\'' +
                ", province='" + province + '\'' +
                ", city='" + city + '\'' +
                ", area='" + area + '\'' +
                ", address='" + address + '\'' +
                ", registerOrigin=" + registerOrigin +
                ", appKey='" + appKey + '\'' +
                ", secondAppKey='" + secondAppKey + '\'' +
                ", shareUid='" + shareUid + '\'' +
                ", imeiNo='" + imeiNo + '\'' +
                ", operateUser='" + operateUser + '\'' +
                ", invitationCode='" + invitationCode + '\'' +
                ", orgId='" + orgId + '\'' +
                ", anonymousId='" + anonymousId + '\'' +
                ", trackPlatForm='" + trackPlatForm + '\'' +
                ", uid='" + uid + '\'' +
                ", membershipPolicyVersion='" + membershipPolicyVersion + '\'' +
                ", privacyPolicyVersion='" + privacyPolicyVersion + '\'' +
                '}';
    }
}
