package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 会员基础信息.<br>
 * <AUTHOR>
 *
 */
public class MembershipBasicInfo implements Serializable {

	private static final long serialVersionUID = -9085430868070972674L;

	/**
	 * 主键
	 */
	private Long pkId;

	/**
	 * 用户唯一标识mid
	 */
	private String mid;
	
	/**
     * 会员编号
     */
    private String authId;

	/**
	 * 享道统一用户ID
	 */
	private String uid;

	/**
	 * 驾照编号
	 */
	private String driverCode;
    
    /**
     * 会员类别
     */
    private Short membershipType;
    
    /**
     * 手机号码
     */
    private String mobilePhone;
    
    /**
     * 姓名
     */
    private String name;

    private String password;
    
    /**
     * 注册时间  yyyyMMddHHmmss
     */
    private String regTime;
    		  
    /**
     * 租车机构ID 默认00
     */
    private String agencyId;

    /** 租车机构名称 */
    private String agencyName;

    /** 所属运营机构Id */
    private String orgId;

	/** 所属运营机构名称 */
    private String orgName;
    
    /**
     * 驾照到期时间
     */
    private String licenseExpirationTime;
    
    /**
     * 审核状态(0:待审核 1：审核通过 2: 审核不通过 3：用户无效 4:重新审核）
     */
    private Integer reviewStatus;
    
    /**
     * 会员卡号
     */
    private String cardNo;
    
    /**
     * 会员来源(0：网点注册 1：网站注册 2：管理平台注册 3:手机APP 4:第三方 5：e享天开 6：e享天开/evcard共同会员 7：CRM)
     */
    private Integer dataOrigin;


    /** 备注来源 */
    private String infoOrigin;
    
    /**
     * 状态(0：有效  1：无效)
     */
    private Integer status;
    
    /**
     * 黑名单理由
     */
    private String blacklistReason;
    
    /**
     * 审核人
     */
    private String reviewUser;
    
    /**
     * 审核项
     */
    private String reviewItems;
    
    /**
     * 押金
     */
    private BigDecimal deposit;
    
    /**
     * E币
     */
    private BigDecimal rentMins;
    
    /**
     * 是否免押金 0：不免押金 1：免押金
     */
    private Integer exemptDeposit;
    
    /**
     * 创建时间.
     */
    private String createdTime;
    
    /**
     * 创建人.
     */
    private String createdUser;
    
    /**
     * 更新时间.
     */
    private String updatedTime;
    
    /**
     * 更新用户
     */
    private String updatedUser;
    
    /**
     * 人员状态 0：正常  1：休息 2：离职
     */
    private Integer personnelState;
    
    /**
     * -1：资料不全 6：快递中 7：快递已签收
     */
    private Integer applyStatus;
    
    /**
     * 服务条款版本号
     */
    private String serviceVer;
    
    /**
     * 服务条款时间
     */
    private String serviceVerTime;
    
    /**
     * 第三方接入的APP key
     */
    private String appKey;

    /** 渠道来源 */
    private String appKeyOrigin;
    
    /**
     * 推送的通道id
     */
    private String channelId;
    
    /**
     * 运营区域ID
     */
    private Long regionid;
    
    /**
     * 车辆押金
     */
    private BigDecimal depositVehicle;
    
    /**
     * （用户所属）省
     */
    private String provinceOfOrigin;
    
    /**
     * （用户所属）市
     */
    private String cityOfOrigin;
    
    /**
     * （用户所属）区
     */
    private String areaOfOrigin;
    
    /**
     * 审核时间
     */
    private String reviewTime;
    
    /**
     * APP提交审核时间
     */
    private String appReviewTime;
    
    /**
     * 审核不通过原因
     */
    private String reviewRemark;
    
    /**
     * 审核不通过概要编号(1:姓名不正确，2：邮寄地址不正确，3：照片不正确)
     */
    private String reviewItemIds;
    
    /**
     * 审核不通过概要原因
     */
    private String reviewItemName;
    
    /**
     * 审核方式 1:手动审核 2:自动审核
     */
    private Integer reviewMode;
    
    /**
     * 人脸识别照片url
     */
    private String faceRecognitionImgUrl;
    
    /**
     * 驾照类型
     */
    private String drivingLicenseType;
    
    /**
     * 国籍
     */
    private String national;
    /**
     * 认证状态 0 未认证 1 未刷脸/未上传 2 已认证
     */
    private Integer authenticationStatus;
    
    /**
     * 1 ocr提取  2 手动输入
     */
    private Integer driverLicenseInputType;
    
    /**
     * 法大大用户编号
     */
    private String customerId;
    
    /**
     * 是否外籍 0：外籍； 1：非外籍
     */
    private Integer foreignNationality;

	/**
	 * 账号状态标识，0正常 1冻结中(注销冻结) 2已删除(注销完成)
	 */
	private Integer accountStatus;

	/**
	 * 注销时间: yyyyMMddHHmmss
	 * @remark 2019.11.7 添加
	 */
	private String unregisterTime;

	/** 手持身份证照片 */
	private String holdIdcardPicUrl;

	/** 驾照领取时间 */
	private String obtainDriverTimer;

	/** 邮箱 */
	private String mail;

	/** 审核状态描述 */
	private String reviewStatusDesc;

	/** 护照号 */
	private String passportNo;

	/** 档案编号 */
	private String fileNo;

	/** 驾照附页照片地址 */
	private String fileNoImgUrl;

	/** 1：身份证 2:护照 3：香港澳门通行证 4 台湾通行证 */
	private Integer idType;

	private String area;

	private String areaName;

	private String regionName;

	private String internalNo;

	private String drivingLicenseImgUrl;
	/** 驾照认证状态 0待认证 1认证通过 2认证不通过 */
	private Integer licenseAuthStatus;
	/** 驾照三要素认证状态：0待认证 1认证通过 2认证不通过 3待重新认证(查证中) */
	private Integer licenseElementsAuthStatus;

	private String idCardNumber;
	
	//身份证照片
	private String idcardPicUrl;

	private Long identityId;
	private Date identityFirstAuthTime;

	/**
	 * 新的驾驶证审核状态 (1:未认证 2:待认证 3:已认证 4:认证不通过)
	 */
	private Integer licenseReviewStatus;

	private Date licenseFirstAuthTime;
	private String licenseSubmitAppkey;
	private Integer licenseImgType;

	/**
	 * 证件时间类型 1：非长期 2：长期
	 */
	private Integer expireType;

	/**
	 * 证件有效期（yyyy-MM-dd)，如果expireType为2-长期，那么此字段为空
	 */
	private String expirationDate;
	/**
	 * 新的身份认证状态 1未认证(未上传) 2未刷脸 3待认证(待人工认证) 4已认证 5认证不通过
	 */
	private Integer authenticationStatusNew;

	/**
	 * 是否已是完成人脸认证
	 * 综合旧认证状态&&新认证状态
	 */
	private boolean alreadyFaceAuthed = false;

	// app2.0擎路  二级渠道appkey
	private String secondAppKey;

	public String getSecondAppKey() {
		return secondAppKey;
	}

	public void setSecondAppKey(String secondAppKey) {
		this.secondAppKey = secondAppKey;
	}

	public Integer getExpireType() {
		return expireType;
	}

	public void setExpireType(Integer expireType) {
		this.expireType = expireType;
	}

	public String getExpirationDate() {
		return expirationDate;
	}

	public void setExpirationDate(String expirationDate) {
		this.expirationDate = expirationDate;
	}

	public Integer getAuthenticationStatusNew() {
		return authenticationStatusNew;
	}

	public void setAuthenticationStatusNew(Integer authenticationStatusNew) {
		this.authenticationStatusNew = authenticationStatusNew;
	}

	public Long getIdentityId() {
		return identityId;
	}

	public void setIdentityId(Long identityId) {
		this.identityId = identityId;
	}

	public Date getIdentityFirstAuthTime() {
		return identityFirstAuthTime;
	}

	public void setIdentityFirstAuthTime(Date identityFirstAuthTime) {
		this.identityFirstAuthTime = identityFirstAuthTime;
	}

	public Integer getLicenseReviewStatus() {
		return licenseReviewStatus;
	}

	public void setLicenseReviewStatus(Integer licenseReviewStatus) {
		this.licenseReviewStatus = licenseReviewStatus;
	}

	public Date getLicenseFirstAuthTime() {
		return licenseFirstAuthTime;
	}

	public void setLicenseFirstAuthTime(Date licenseFirstAuthTime) {
		this.licenseFirstAuthTime = licenseFirstAuthTime;
	}

	public String getLicenseSubmitAppkey() {
		return licenseSubmitAppkey;
	}

	public void setLicenseSubmitAppkey(String licenseSubmitAppkey) {
		this.licenseSubmitAppkey = licenseSubmitAppkey;
	}

	public Integer getLicenseImgType() {
		return licenseImgType;
	}

	public void setLicenseImgType(Integer licenseImgType) {
		this.licenseImgType = licenseImgType;
	}

	public String getAreaName() {
		return areaName;
	}

	public void setAreaName(String areaName) {
		this.areaName = areaName;
	}

	public String getRegionName() {
		return regionName;
	}

	public void setRegionName(String regionName) {
		this.regionName = regionName;
	}

	public String getInternalNo() {
		return internalNo;
	}

	public void setInternalNo(String internalNo) {
		this.internalNo = internalNo;
	}

	public String getReviewStatusDesc() {
		return reviewStatusDesc;
	}

	public void setReviewStatusDesc(String reviewStatusDesc) {
		this.reviewStatusDesc = reviewStatusDesc;
	}

	public String getMail() {
		return mail;
	}

	public void setMail(String mail) {
		this.mail = mail;
	}

	public String getObtainDriverTimer() {
		return obtainDriverTimer;
	}

	public void setObtainDriverTimer(String obtainDriverTimer) {
		this.obtainDriverTimer = obtainDriverTimer;
	}

	public String getAppKeyOrigin() {
		return appKeyOrigin;
	}

	public void setAppKeyOrigin(String appKeyOrigin) {
		this.appKeyOrigin = appKeyOrigin;
	}

	public String getInfoOrigin() {
		return infoOrigin;
	}

	public void setInfoOrigin(String infoOrigin) {
		this.infoOrigin = infoOrigin;
	}

	public String getAgencyName() {
		return agencyName;
	}

	public void setAgencyName(String agencyName) {
		this.agencyName = agencyName;
	}

	public String getOrgId() {
		return orgId;
	}

	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getHoldIdcardPicUrl() {
		return holdIdcardPicUrl;
	}

	public void setHoldIdcardPicUrl(String holdIdcardPicUrl) {
		this.holdIdcardPicUrl = holdIdcardPicUrl;
	}

	public Long getPkId() {
		return pkId;
	}

	public void setPkId(Long pkId) {
		this.pkId = pkId;
	}

	public String getAuthId() {
		return authId;
	}

	public void setAuthId(String authId) {
		this.authId = authId;
	}

	public Short getMembershipType() {
		return membershipType;
	}

	public void setMembershipType(Short membershipType) {
		this.membershipType = membershipType;
	}

	public String getMobilePhone() {
		return mobilePhone;
	}

	public void setMobilePhone(String mobilePhone) {
		this.mobilePhone = mobilePhone;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getRegTime() {
		return regTime;
	}

	public void setRegTime(String regTime) {
		this.regTime = regTime;
	}

	public String getAgencyId() {
		return agencyId;
	}

	public void setAgencyId(String agencyId) {
		this.agencyId = agencyId;
	}

	public String getLicenseExpirationTime() {
		return licenseExpirationTime;
	}

	public void setLicenseExpirationTime(String licenseExpirationTime) {
		this.licenseExpirationTime = licenseExpirationTime;
	}

	public Integer getReviewStatus() {
		return reviewStatus;
	}

	public void setReviewStatus(Integer reviewStatus) {
		this.reviewStatus = reviewStatus;
	}

	public String getCardNo() {
		return cardNo;
	}

	public void setCardNo(String cardNo) {
		this.cardNo = cardNo;
	}

	public Integer getDataOrigin() {
		return dataOrigin;
	}

	public void setDataOrigin(Integer dataOrigin) {
		this.dataOrigin = dataOrigin;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getBlacklistReason() {
		return blacklistReason;
	}

	public void setBlacklistReason(String blacklistReason) {
		this.blacklistReason = blacklistReason;
	}

	public String getReviewUser() {
		return reviewUser;
	}

	public void setReviewUser(String reviewUser) {
		this.reviewUser = reviewUser;
	}

	public String getReviewItems() {
		return reviewItems;
	}

	public void setReviewItems(String reviewItems) {
		this.reviewItems = reviewItems;
	}

	public BigDecimal getDeposit() {
		return deposit;
	}

	public void setDeposit(BigDecimal deposit) {
		this.deposit = deposit;
	}

	public BigDecimal getRentMins() {
		return rentMins;
	}

	public void setRentMins(BigDecimal rentMins) {
		this.rentMins = rentMins;
	}

	public Integer getExemptDeposit() {
		return exemptDeposit;
	}

	public void setExemptDeposit(Integer exemptDeposit) {
		this.exemptDeposit = exemptDeposit;
	}

	public String getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(String createdTime) {
		this.createdTime = createdTime;
	}

	public String getCreatedUser() {
		return createdUser;
	}

	public void setCreatedUser(String createdUser) {
		this.createdUser = createdUser;
	}

	public String getUpdatedTime() {
		return updatedTime;
	}

	public void setUpdatedTime(String updatedTime) {
		this.updatedTime = updatedTime;
	}

	public String getUpdatedUser() {
		return updatedUser;
	}

	public void setUpdatedUser(String updatedUser) {
		this.updatedUser = updatedUser;
	}

	public Integer getPersonnelState() {
		return personnelState;
	}

	public void setPersonnelState(Integer personnelState) {
		this.personnelState = personnelState;
	}

	public Integer getApplyStatus() {
		return applyStatus;
	}

	public void setApplyStatus(Integer applyStatus) {
		this.applyStatus = applyStatus;
	}

	public String getServiceVer() {
		return serviceVer;
	}

	public void setServiceVer(String serviceVer) {
		this.serviceVer = serviceVer;
	}

	public String getServiceVerTime() {
		return serviceVerTime;
	}

	public void setServiceVerTime(String serviceVerTime) {
		this.serviceVerTime = serviceVerTime;
	}

	public String getAppKey() {
		return appKey;
	}

	public void setAppKey(String appKey) {
		this.appKey = appKey;
	}

	public String getChannelId() {
		return channelId;
	}

	public void setChannelId(String channelId) {
		this.channelId = channelId;
	}

	public Long getRegionid() {
		return regionid;
	}

	public void setRegionid(Long regionid) {
		this.regionid = regionid;
	}

	public BigDecimal getDepositVehicle() {
		return depositVehicle;
	}

	public void setDepositVehicle(BigDecimal depositVehicle) {
		this.depositVehicle = depositVehicle;
	}

	public String getProvinceOfOrigin() {
		return provinceOfOrigin;
	}

	public void setProvinceOfOrigin(String provinceOfOrigin) {
		this.provinceOfOrigin = provinceOfOrigin;
	}

	public String getCityOfOrigin() {
		return cityOfOrigin;
	}

	public void setCityOfOrigin(String cityOfOrigin) {
		this.cityOfOrigin = cityOfOrigin;
	}

	public String getAreaOfOrigin() {
		return areaOfOrigin;
	}

	public void setAreaOfOrigin(String areaOfOrigin) {
		this.areaOfOrigin = areaOfOrigin;
	}

	public String getReviewTime() {
		return reviewTime;
	}

	public void setReviewTime(String reviewTime) {
		this.reviewTime = reviewTime;
	}

	public String getAppReviewTime() {
		return appReviewTime;
	}

	public void setAppReviewTime(String appReviewTime) {
		this.appReviewTime = appReviewTime;
	}

	public String getReviewRemark() {
		return reviewRemark;
	}

	public void setReviewRemark(String reviewRemark) {
		this.reviewRemark = reviewRemark;
	}

	public String getReviewItemIds() {
		return reviewItemIds;
	}

	public void setReviewItemIds(String reviewItemIds) {
		this.reviewItemIds = reviewItemIds;
	}

	public String getReviewItemName() {
		return reviewItemName;
	}

	public void setReviewItemName(String reviewItemName) {
		this.reviewItemName = reviewItemName;
	}

	public Integer getReviewMode() {
		return reviewMode;
	}

	public void setReviewMode(Integer reviewMode) {
		this.reviewMode = reviewMode;
	}

	public String getFaceRecognitionImgUrl() {
		return faceRecognitionImgUrl;
	}

	public void setFaceRecognitionImgUrl(String faceRecognitionImgUrl) {
		this.faceRecognitionImgUrl = faceRecognitionImgUrl;
	}

	public String getDrivingLicenseType() {
		return drivingLicenseType;
	}

	public void setDrivingLicenseType(String drivingLicenseType) {
		this.drivingLicenseType = drivingLicenseType;
	}

	public String getNational() {
		return national;
	}

	public void setNational(String national) {
		this.national = national;
	}


	public Integer getAuthenticationStatus() {
		return authenticationStatus;
	}

	public void setAuthenticationStatus(Integer authenticationStatus) {
		this.authenticationStatus = authenticationStatus;
	}

	public Integer getDriverLicenseInputType() {
		return driverLicenseInputType;
	}

	public void setDriverLicenseInputType(Integer driverLicenseInputType) {
		this.driverLicenseInputType = driverLicenseInputType;
	}

	public String getCustomerId() {
		return customerId;
	}

	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}

	public Integer getForeignNationality() {
		return foreignNationality;
	}

	public void setForeignNationality(Integer foreignNationality) {
		this.foreignNationality = foreignNationality;
	}

	public Integer getAccountStatus() {
		return accountStatus;
	}

	public void setAccountStatus(Integer accountStatus) {
		this.accountStatus = accountStatus;
	}

	public String getDriverCode() {
		return driverCode;
	}

	public void setDriverCode(String driverCode) {
		this.driverCode = driverCode;
	}

	public String getPassportNo() {
		return passportNo;
	}

	public void setPassportNo(String passportNo) {
		this.passportNo = passportNo;
	}

	public String getFileNo() {
		return fileNo;
	}

	public void setFileNo(String fileNo) {
		this.fileNo = fileNo;
	}

	public String getFileNoImgUrl() {
		return fileNoImgUrl;
	}

	public void setFileNoImgUrl(String fileNoImgUrl) {
		this.fileNoImgUrl = fileNoImgUrl;
	}

	public Integer getIdType() {
		return idType;
	}

	public void setIdType(Integer idType) {
		this.idType = idType;
	}

	public String getArea() {
		return area;
	}

	public void setArea(String area) {
		this.area = area;
	}

	public String getUnregisterTime() {
		return unregisterTime;
	}

	public void setUnregisterTime(String unregisterTime) {
		this.unregisterTime = unregisterTime;
	}

	public String getDrivingLicenseImgUrl() {
		return drivingLicenseImgUrl;
	}

	public void setDrivingLicenseImgUrl(String drivingLicenseImgUrl) {
		this.drivingLicenseImgUrl = drivingLicenseImgUrl;
	}

	public Integer getLicenseAuthStatus() {
		return licenseAuthStatus;
	}

	public void setLicenseAuthStatus(Integer licenseAuthStatus) {
		this.licenseAuthStatus = licenseAuthStatus;
	}

	public Integer getLicenseElementsAuthStatus() {
		return licenseElementsAuthStatus;
	}

	public void setLicenseElementsAuthStatus(Integer licenseElementsAuthStatus) {
		this.licenseElementsAuthStatus = licenseElementsAuthStatus;
	}

	public String getIdCardNumber() {
		return idCardNumber;
	}

	public void setIdCardNumber(String idCardNumber) {
		this.idCardNumber = idCardNumber;
	}

	public String getUid() {
		return uid;
	}

	public void setUid(String uid) {
		this.uid = uid;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getIdcardPicUrl() {
		return idcardPicUrl;
	}

	public void setIdcardPicUrl(String idcardPicUrl) {
		this.idcardPicUrl = idcardPicUrl;
	}

	public String getMid() {
		return mid;
	}

	public void setMid(String mid) {
		this.mid = mid;
	}

	public boolean isAlreadyFaceAuthed() {
		return alreadyFaceAuthed;
	}

	public void setAlreadyFaceAuthed(boolean alreadyFaceAuthed) {
		this.alreadyFaceAuthed = alreadyFaceAuthed;
	}
}
