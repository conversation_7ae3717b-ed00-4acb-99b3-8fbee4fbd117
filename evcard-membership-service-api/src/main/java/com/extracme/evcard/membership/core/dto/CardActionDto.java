package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;

public class CardActionDto implements Serializable{
    private static final long serialVersionUID = -6102656203675369489L;
    /**
     * 行为编号.<br>
     */
    private Integer actionSeq;
    /**
     * 卡编号.<br>
     */
    private String cardNo;
    /**
     * 车辆VIN。<br>
     */
    private String vin;
    /**
     * 车门状态.<br>
     */
    private Integer doorStatus;
    /**
     * 创建人.<br>
     */
    private String createdUser;
    /**
     * 返回的错误码.<br>
     */
    private int returnCode;
    
	public Integer getActionSeq() {
		return actionSeq;
	}
	public void setActionSeq(Integer actionSeq) {
		this.actionSeq = actionSeq;
	}
	public String getCardNo() {
		return cardNo;
	}
	public void setCardNo(String cardNo) {
		this.cardNo = cardNo;
	}
	public String getVin() {
		return vin;
	}
	public void setVin(String vin) {
		this.vin = vin;
	}
	public Integer getDoorStatus() {
		return doorStatus;
	}
	public void setDoorStatus(Integer doorStatus) {
		this.doorStatus = doorStatus;
	}
	public String getCreatedUser() {
		return createdUser;
	}
	public void setCreatedUser(String createdUser) {
		this.createdUser = createdUser;
	}
	public int getReturnCode() {
		return returnCode;
	}
	public void setReturnCode(int returnCode) {
		this.returnCode = returnCode;
	}

}
