package com.extracme.evcard.membership.core.dto.input;

import java.io.Serializable;

public class DealFeedbackInput implements Serializable{
    private static final long serialVersionUID = -7866178792391613225L;

    //处理消息类型 0 网点推荐 1 车辆故障 2 充电桩故障 3 意见反馈
    private Integer feedbackType;

    //上报消息id 必填
    private Integer id;

    // 1：创建任务(添加处理)2:不与处理
    private Integer type;

    //不予处理时必填理由,
    private String desc;

    //创建任务时任务id
    private String taskId;

    //处理人,必填
    private String updateUser;

    //处理人id
    private Integer updateUserId;

    public Integer getFeedbackType() {
        return feedbackType;
    }

    public void setFeedbackType(Integer feedbackType) {
        this.feedbackType = feedbackType;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Integer getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(Integer updateUserId) {
        this.updateUserId = updateUserId;
    }
}
