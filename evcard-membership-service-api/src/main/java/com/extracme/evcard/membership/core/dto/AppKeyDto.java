package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * 渠道信息
 */
public class AppKeyDto implements Serializable{
    /**
	 * 
	 */
	private static final long serialVersionUID = -7266273397444727952L;

	private String appKey;

    private String appSecret;

    private String requestAppKey;

    private String platName;

    private String requestAppSecret;

    private String postUrl;

    private String className;

    private String orgId;

    private String remark;

    private Double status;

    private Double autoRegist;

    private Double loginRestrict;

    private Double autoPay;

    private Double enjoyBenefit;

    private Double uploadOrder;

    private Byte type;

    private Date createdTime;

    private Long createOperId;

    private String createdUser;

    private Date updatedTime;

    private Long updateOperId;

    private String updatedUser;

    private String platAppKey;

    private String platAppSecret;

    /**
                 * 渠道所属平台id
     */
    private Long platformId;
    /**
     * 所属平台名称.
     */
    private String platformName;
    
    /**
     * 渠道用途，id以逗号分隔
     */
    private String channelPurpose;

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getRequestAppKey() {
        return requestAppKey;
    }

    public void setRequestAppKey(String requestAppKey) {
        this.requestAppKey = requestAppKey;
    }

    public String getPlatName() {
        return platName;
    }

    public void setPlatName(String platName) {
        this.platName = platName;
    }

    public String getRequestAppSecret() {
        return requestAppSecret;
    }

    public void setRequestAppSecret(String requestAppSecret) {
        this.requestAppSecret = requestAppSecret;
    }

    public String getPostUrl() {
        return postUrl;
    }

    public void setPostUrl(String postUrl) {
        this.postUrl = postUrl;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Double getStatus() {
        return status;
    }

    public void setStatus(Double status) {
        this.status = status;
    }

    public Double getAutoRegist() {
        return autoRegist;
    }

    public void setAutoRegist(Double autoRegist) {
        this.autoRegist = autoRegist;
    }

    public Double getLoginRestrict() {
        return loginRestrict;
    }

    public void setLoginRestrict(Double loginRestrict) {
        this.loginRestrict = loginRestrict;
    }

    public Double getAutoPay() {
        return autoPay;
    }

    public void setAutoPay(Double autoPay) {
        this.autoPay = autoPay;
    }

    public Double getEnjoyBenefit() {
        return enjoyBenefit;
    }

    public void setEnjoyBenefit(Double enjoyBenefit) {
        this.enjoyBenefit = enjoyBenefit;
    }

    public Double getUploadOrder() {
        return uploadOrder;
    }

    public void setUploadOrder(Double uploadOrder) {
        this.uploadOrder = uploadOrder;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    public Long getCreateOperId() {
        return createOperId;
    }

    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    public String getCreatedUser() {
        return createdUser;
    }

    public void setCreatedUser(String createdUser) {
        this.createdUser = createdUser;
    }

    public Date getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    public Long getUpdateOperId() {
        return updateOperId;
    }

    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    public String getUpdatedUser() {
        return updatedUser;
    }

    public void setUpdatedUser(String updatedUser) {
        this.updatedUser = updatedUser;
    }

    public String getPlatAppKey() {
        return platAppKey;
    }

    public void setPlatAppKey(String platAppKey) {
        this.platAppKey = platAppKey;
    }

    public String getPlatAppSecret() {
        return platAppSecret;
    }

    public void setPlatAppSecret(String platAppSecret) {
        this.platAppSecret = platAppSecret;
    }

    public Long getPlatformId() {
        return platformId;
    }

    public void setPlatformId(Long platformId) {
        this.platformId = platformId;
    }

    public String getChannelPurpose() {
        return channelPurpose;
    }

    public void setChannelPurpose(String channelPurpose) {
        this.channelPurpose = channelPurpose;
    }

	public String getPlatformName() {
		return platformName;
	}

	public void setPlatformName(String platformName) {
		this.platformName = platformName;
	}

}
