package com.extracme.evcard.membership.core.exception;

import com.extracme.evcard.rpc.enums.StatusCode;

public class VerifyCodeException extends RuntimeException {
	private static final long serialVersionUID = 5100882953811575191L;

	private int code;

	private String message;

	public VerifyCodeException(){

	}

	public VerifyCodeException(StatusCode statusCode){
		this.code = statusCode.getCode();
		this.message = statusCode.getMsg();
	}

	public VerifyCodeException(int code, String message) {
		this.code = code;
		this.message = message;
	}

	public VerifyCodeException(String message, int code, String message1) {
		super(message);
		this.code = code;
		this.message = message1;
	}

	public VerifyCodeException(String message, Throwable cause, int code, String message1) {
		super(message, cause);
		this.code = code;
		this.message = message1;
	}

	public VerifyCodeException(Throwable cause, int code, String message) {
		super(cause);
		this.code = code;
		this.message = message;
	}

	public VerifyCodeException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace, int code, String message1) {
		super(message, cause, enableSuppression, writableStackTrace);
		this.code = code;
		this.message = message1;
	}

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	@Override
    public String getMessage() {
		return message;
	}


	public void setMessage(String message) {
		this.message = message;
	}

}
