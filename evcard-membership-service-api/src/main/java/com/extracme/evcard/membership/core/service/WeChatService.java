package com.extracme.evcard.membership.core.service;


import com.extracme.evcard.membership.core.input.MmpWeChatRequestDto;
import com.extracme.evcard.membership.core.input.MmpWeChatResponse;

/**
 * 微信小程序对接相关实现类
 */
public interface WeChatService {

    //获取微信的token
    public String getToken(Boolean refresh);

    //获取不限制的小程序码
    public MmpWeChatResponse getwxacodeunlimit(String page, String scene, Integer tryNum, String dir, String name);

    //获取小程序链接
    public MmpWeChatResponse getgenerateUrllink(MmpWeChatRequestDto mmpWeChatRequestDto);
}
