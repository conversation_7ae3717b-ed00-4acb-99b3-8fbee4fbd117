package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @since 2018/09/14
 */
public class UpdateUserDto implements Serializable{

	private static final long serialVersionUID = -4725296692413855064L;
	/**
     * 用户id
     */
    private Long userId;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 修改时间
     */
    private Date updateTime;

    private String remark;

    private String originSystem;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public static UpdateUserDto buildUserOperator(String authId){
        UpdateUserDto operator = new UpdateUserDto();
        operator.setUserId(-1L);
        operator.setUserName(authId);
        operator.setUpdateTime(new Date());
        return operator;
    }

    public static UpdateUserDto buildUserOperator(String userName, String remark){
        UpdateUserDto operator = new UpdateUserDto();
        operator.setUserId(-1L);
        operator.setUserName(userName);
        operator.setRemark(remark);
        operator.setUpdateTime(new Date());
        return operator;
    }

    public static UpdateUserDto buildSysOperator(String remark){
        return buildUserOperator("system", remark);
    }

    public static final UpdateUserDto SYSTEM = buildUserOperator("system");

    public String getOriginSystem() {
        return originSystem;
    }

    public void setOriginSystem(String originSystem) {
        this.originSystem = originSystem;
    }
}
