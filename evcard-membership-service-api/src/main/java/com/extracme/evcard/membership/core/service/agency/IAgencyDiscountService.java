package com.extracme.evcard.membership.core.service.agency;

import com.extracme.evcard.membership.core.dto.MemberDiscountDTO;
import com.extracme.evcard.membership.core.dto.agency.AgencyDiscountConfig;
import com.extracme.evcard.membership.core.dto.agency.AgencyDiscountDTO;
import com.extracme.evcard.membership.core.dto.agency.DiscountRuleDTO;
import com.extracme.evcard.membership.core.dto.discount.MmpDiscountPackageShareRuleDTO;
import com.extracme.evcard.rpc.dto.BaseResponse;

import java.util.Date;
import java.util.List;

/**
 * 会员|企业会员折扣服务
 */
public interface IAgencyDiscountService {

    /**
     * 获取会员最优折扣信息（当前）
     * @param authId
     * @param membershipType
     * @return 当前最高折扣
     * @since 2.4.2
     * @remark 企业折扣优先，阶梯折扣取最高折扣（不考虑时段限制）。
     * @remark 修改后非立即生效，缓存有效期10分钟。
     */
    MemberDiscountDTO getMemberDiscount(String authId, Integer membershipType);

    /**
     * 获取折扣配置信息(当前)
     * @param agencyId 机构id
     * @param discountType 折扣类别： 0企业 1个人
     * @param validOnly 是否只返回当前生效的折扣规则
     * @return
     */
    DiscountRuleDTO getDiscount(String agencyId, Integer discountType, boolean validOnly);

    /**
     * 根据机构ID和折扣类型从数据库中查询折扣
     * @param agencyId
     * @param discountType
     * @return
     * @remark 从数据库中查询
     */
    List<DiscountRuleDTO> find(String agencyId, Integer discountType);

    /**
     * 企业折扣与套餐不同享规则查询
     * @param authId
     * @param time
     * @return
     * @since 2.18.0
     */
    MmpDiscountPackageShareRuleDTO findMemberDisCountPackageShareRule(String authId, Date time);

    /**
     * 查询指定时刻生效的企业折扣与套餐不同享规则
     * @param agencyId 机构id
     * @param time
     * @return
     * @since 2.18.0
     */
    MmpDiscountPackageShareRuleDTO findDisCountPackageShareRule(String agencyId, Date time);


    /**
     * 保存企业折扣与套餐(不)同享规则
     * @param discountPackageShareRuleDTO
     * @return
     * @since 2.18.0
     */
    BaseResponse saveDiscountPackageShareRule(MmpDiscountPackageShareRuleDTO discountPackageShareRuleDTO);

    /**
     * 校验企业折扣与车牌限制
     * @param agencyId 企业id
     * @param vehicleNo 车牌
     * @return
     * @since 2.19.0
     */
    BaseResponse checkVehicleNoLimit(String agencyId, String vehicleNo);

    /**
     * 校验企业折扣与车牌限制
     * @param agencyId 企业id
     * @param time 时间（为null时，取当前配置）
     * @param vehicleNo 车牌
     * @return
     * @since 2.20.0
     */
    BaseResponse checkVehicleNoLimit(String agencyId, Date time, String vehicleNo);

    /**
     * 获取指定时刻点的企业折扣相关配置信息：
     *   企业状态、个人id、企业折扣id
     *   车牌限制、不共享套餐
     * @param agencyId 机构id
     * @param time 时间（为null时，查询当前配置）
     * @param validOnly true 则仅返回 状态为合作中的企业的折扣配置信息.
     * @return
     * @since 2.20.0
     * @remark 此接口只包含折扣记录id，不包含具体折扣信息
     */
    AgencyDiscountConfig getAgencyDiscountConfig(String agencyId, Date time, boolean validOnly);

    /**
     * 获取指定时刻点的企业折扣完整信息：
     *   企业状态
     *   个人折扣详情、企业折扣详情
     *   车牌限制、不共享套餐
     * @param agencyId 机构id
     * @param time 时间（为null时，查询当前配置）
     * @param validOnly true 则仅返回 状态为合作中的企业的折扣配置信息以及生效中的折扣记录.
     * @return
     * @since 2.20.0
     */
    AgencyDiscountDTO getAgencyDiscountDetail(String agencyId, Date time, boolean validOnly);
}
