package com.extracme.evcard.membership.contract.service;

import com.extracme.evcard.rpc.util.DateUtil;
import lombok.Data;
import lombok.NoArgsConstructor;


import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 */
@Data
@NoArgsConstructor
public class OfcQueryContractRequest implements Serializable {
    private static final long serialVersionUID = -8736925034085247483L;
    // mid
    private String mid;

    // 类型 5：结算单 6：租车单
    private int type;


    // 订单信息
    private OrderBaseInfo orderBaseInfo;

    // 押金信息
    private DepositBaseInfo depositBaseInfo;

    // 费用信息
    private List<OrderFeesBaseInfo> orderFeesBaseInfos;


    public OfcQueryContractBo toOfcQueryContractBo() {
        OfcQueryContractBo bo = new OfcQueryContractBo();
        bo.setType(type);
        bo.setTodayDate(DateUtil.dateToString(new Date(),DateUtil.DATE_TYPE7));
        bo.setOrderFeesBaseInfos(orderFeesBaseInfos);
        bo.setOrderBaseInfo(orderBaseInfo);
        bo.setDepositBaseInfo(depositBaseInfo);
        return bo;
    }
}
