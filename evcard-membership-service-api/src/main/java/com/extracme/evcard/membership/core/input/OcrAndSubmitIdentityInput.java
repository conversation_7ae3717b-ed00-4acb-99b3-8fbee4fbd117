package com.extracme.evcard.membership.core.input;

import lombok.Data;

import java.io.Serializable;

@Data
public class OcrAndSubmitIdentityInput implements Serializable {

    private static final long serialVersionUID = 464929355983069569L;
    /**
     * 会员id
     */
    private String mid;

    /**
     * 渠道key
     */
    private String appKey;

    /**
     * 二级渠道key
     */
    private String secondAppKey;

    // 身份证件编号
    private String identityNo;
    // （全路径）
    /**
     * 身份证件正面图片
     * 图片完整URL, 若未提供图片信息，则使用此url
     * oss文件名 or 可下载的完整的oss地址
     */
    private String identityCardImgUrl;
    /**
     * 身份证件反面图片
     * 图片完整URL, 若未提供图片信息，则使用此url
     * oss文件名 or 可下载的完整的oss地址
     */
    private String reverseIdentityCardImgUrl;

    /**
     * 证件类型 1：居民身份证 2：军人身份证
     */
    private int identityType;

    /**
     * 城市运营模式 0：大库 1：门店
     */
    private String operationModel;
}
