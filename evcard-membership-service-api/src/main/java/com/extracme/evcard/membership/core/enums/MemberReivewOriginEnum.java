package com.extracme.evcard.membership.core.enums;

/**
 * 会员审核状态变更场景/原因
 * <AUTHOR>
 * @Discription
 * @date 2020/5/14
 */
public enum MemberReivewOriginEnum {
    //变更来源： 1自动审核通过  2人工审核通过 3人工审核不通过 4自动审核不通过-登录校准 5自动审核不通过-后台查无校准

    APP_AUTO_REVIEW(1, "认证后，APP自动变更审核状态", "APP自动审核"),
    MANUAL_REVIEW_PASS(2, "人工审核通过", "人工审核通过"),
    MANUAL_REVIEW_NOT_PASS(3, "人工审核不通过", "人工审核不通过"),
    SYS_AUTO_REVIEW_FAILED_LOGIN(4, "后台自动审核不通过-登录校准", "登录校准驾照状态"),
    SYS_AUTO_REVIEW_FAILED_RETRY(5, "后台自动审核不通过-三要素查无重试", "查无任务校准驾照状态"),
    SYS_AUTO_REVIEW_FAILED_SUPPLY(6, "后台自动审核不通过-补全驾照信息", "补全驾照信息"),
    APP_AUTO_REVIEW_ONLY(7, "更新驾照有效期，APP自动审核通过", "更新驾照有效期"),
    SYS_AUTO_REVIEW_FAILED_FACEFIRST(8, "后台自动审核不通过-提交驾照信息", "资料不全已认证提交驾照信息"),
    SYS_AUTO_REVIEW_FAILED_FACEPASS(9, "后台自动审核不通过-刷脸通过", "刷脸通过");

    MemberReivewOriginEnum(int code, String operate,  String origin) {
        this.code = code;
        this.operate = operate;
        this.origin = origin;
    }

    private int code;
    private String operate;
    private String origin;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getOperate() {
        return operate;
    }

    public void setOperate(String operate) {
        this.operate = operate;
    }

    public String getOrigin() {
        return origin;
    }

    public void setOrigin(String origin) {
        this.origin = origin;
    }
}
