package com.extracme.evcard.membership.core.dto.agency;
import lombok.Data;

import java.io.Serializable;


@Data
public class BindAgencyBySecondAppKeyRelationDTO implements Serializable {
    private static final long serialVersionUID = 5934803733513611615L;

    /**
     * 用户mid
     */
    private String mid;

    /**
     * 二级渠道AppKey
     */
    //private String secondAppKey;

    private Long relationId;

    /**
     * 短链的随机码
     * 可以查询出 relationId
     */
    private String randomCode;
}
