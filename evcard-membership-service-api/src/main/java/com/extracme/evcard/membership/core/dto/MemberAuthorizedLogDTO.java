package com.extracme.evcard.membership.core.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/1/10
 */
@Data
public class MemberAuthorizedLogDTO implements Serializable {
    /**
     * 授权记录id
     */
    private Long id;

    /**
     * 会员id
     */
    private String authId;

    /**
     * 授权操作类别：0 芝麻授权 1 代扣授权 2 取消代扣
     */
    private Integer authorizedType;

    /**
     * 操作结果：0 失败 1 成功
     */
    private Integer authorizedResult;

    /**
     * 操作结果原因描述
     */
    private String authorizedReason;

    /**
     * 授权操作时间
     */
    private Date authorizedDateTime;

    /**
     * 授权时芝麻信用门槛分
     */
    private Integer authorizedFraction;

    private Integer status;

    private String miscDesc;

    private Date createTime;

    private Long createOperId;

    private String createOperName;

    private Date updateTime;

    private Long updateOperId;

    private String updateOperName;
}
