package com.extracme.evcard.membership.core.service.stub;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.extracme.evcard.membership.core.dto.*;
import com.extracme.evcard.membership.core.dto.input.FaceMatchInput;
import org.apache.commons.lang3.StringUtils;

import com.extracme.evcard.membership.core.bean.BaseBean;
import com.extracme.evcard.membership.core.dto.input.SubmitDepositSecurityInput;
import com.extracme.evcard.membership.core.exception.AuthenticationException;
import com.extracme.evcard.membership.core.exception.ChangeMobileException;
import com.extracme.evcard.membership.core.exception.FindPasswordException;
import com.extracme.evcard.membership.core.exception.MemberException;
import com.extracme.evcard.membership.core.exception.ModifyPasswordException;
import com.extracme.evcard.membership.core.exception.RegisterException;
import com.extracme.evcard.membership.core.exception.UnregisterException;
import com.extracme.evcard.membership.core.exception.UserMessageException;
import com.extracme.evcard.membership.core.exception.VerifyCodeException;
import com.extracme.evcard.membership.core.input.AliAfs;
import com.extracme.evcard.membership.core.input.QueryMemberClauseRecordInput;
import com.extracme.evcard.membership.core.input.QueryMemberOperateLogInput;
import com.extracme.evcard.membership.core.input.QuerySimpleMemberInput;
import com.extracme.evcard.membership.core.input.SMSVerifyBeanInput;
import com.extracme.evcard.membership.core.input.SaveDriverElementsAuthenticateInput;
import com.extracme.evcard.membership.core.input.SaveDriverElementsAuthenticateLogInput;
import com.extracme.evcard.membership.core.input.SubmitDrivingLicenseInput;
import com.extracme.evcard.membership.core.input.SubmitFaceInfoInput;
import com.extracme.evcard.membership.core.input.SubmitFacePicInput;
import com.extracme.evcard.membership.core.input.SubmitUserIdCardPicInput;
import com.extracme.evcard.membership.core.input.UpdateFacePicInput;
import com.extracme.evcard.membership.core.input.UpdateFileNoInput;
import com.extracme.evcard.membership.core.input.UpdateMemberInfoFromFlyingPigInputDto;
import com.extracme.evcard.membership.core.input.UploadServiceVerInput;
import com.extracme.evcard.membership.core.input.UserOperationLogInput;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.exception.BusinessRuntimeException;

public class MemberShipServiceStub implements IMemberShipService {

	IMemberShipService memberShipService;

	public MemberShipServiceStub(IMemberShipService memberShipService) {
		this.memberShipService = memberShipService;
	}

	@Override
	public String register(RegisterDto registerDto) throws MemberException {
		return this.memberShipService.register(registerDto);
	}

	@Override
	public String registerV1(RegisterDto registerDto) throws MemberException {
		return this.memberShipService.registerV1(registerDto);
	}

	@Override
	public String registerV1(RegisterDto registerDto, boolean checkVerifyCode) throws MemberException {
		return this.memberShipService.registerV1(registerDto, checkVerifyCode);
	}

	@Override
	public String register(RegisterDto registerDto, boolean checkVerifyCode)
			throws MemberException {
		return this.memberShipService.register(registerDto, checkVerifyCode);
	}

	@Override
	public String registerV2(RegisterDto registerDto, boolean checkVerifyCode) throws RegisterException {
		return this.memberShipService.registerV2(registerDto, checkVerifyCode);
	}

	@Override
	public void changeBindMobilePhone(String mobilePhone,
			String newMobilePhone, String authId, String verifyCode, String imei) throws ChangeMobileException {
		if(StringUtils.isBlank(mobilePhone)
				|| StringUtils.isBlank(authId)
				|| StringUtils.isBlank(newMobilePhone)
				|| StringUtils.isBlank(verifyCode)
				|| StringUtils.isBlank(imei)) {
			throw BusinessRuntimeException.PARAM_EXEPTION;
		}
		this.memberShipService.changeBindMobilePhone(mobilePhone, newMobilePhone, authId, verifyCode, imei);
	}


	@Override
	public void changeBindMobilePhoneV2(ChangeMobileDto changeMobileDto) throws ChangeMobileException {
		String driverCode = changeMobileDto.getDriverCode();
		String imei = changeMobileDto.getImei();
		String mobilePhone = changeMobileDto.getMobilePhone();
		String newMobilePhone = changeMobileDto.getNewMobilePhone();
		String verifyCode = changeMobileDto.getVerifyCode();
		if(StringUtils.isBlank(mobilePhone)
				|| StringUtils.isBlank(driverCode)
				|| StringUtils.isBlank(newMobilePhone)
				|| StringUtils.isBlank(verifyCode)
				|| StringUtils.isBlank(imei)) {
			throw BusinessRuntimeException.PARAM_EXEPTION;
		}
		this.memberShipService.changeBindMobilePhone(mobilePhone, newMobilePhone, driverCode, verifyCode, imei);
	}

	@Override
	public BigDecimal consumAmount(String authId) {
		return this.memberShipService.consumAmount(authId);
	}

	@Override
	public boolean keywordScan(String content) {
		return this.memberShipService.keywordScan(content);
	}

	@Override
	public void autoSignContract(SignContractDto signContractDto)
			throws MemberException {
		this.memberShipService.autoSignContract(signContractDto);
	}

	@Override
	@Deprecated
	public void saveCardAction(CardActionDto cardAction) {
		this.memberShipService.saveCardAction(cardAction);
	}

	@Override
	public MembershipSecretInfo getMembershipSecretInfo(String authId,
			int messageType, String optUser, String systemFlag) {
		return this.memberShipService.getMembershipSecretInfo(authId, messageType, optUser, systemFlag);
	}

	@Override
	public MembershipSecretInfo getMembershipSecretInfo(String authId,
			int membershipType, int messageType, String optUser,
			String systemFlag) {
		return this.memberShipService.getMembershipSecretInfo(authId, membershipType, messageType, optUser, systemFlag);
	}

	@Override
	public String addInnerMembershipInfo(
			InnerNewMemberInfoDto innerMemberInfoDto, String createdUser) {
		return this.memberShipService.addInnerMembershipInfo(innerMemberInfoDto, createdUser);
	}

	@Override
	public void handleUserInfo(Map<String, Object> properties, String authId) {
		this.memberShipService.handleUserInfo(properties, authId);
	}

	@Override
	@Deprecated
	public UserInfoDto getUserInfoByToken(String authId)
			throws UserMessageException {
		return this.memberShipService.getUserInfoByToken(authId);
	}

	@Override
	public UserInfoDto getUserInfoByToken(String authId, String appKey) throws UserMessageException {
		return this.memberShipService.getUserInfoByToken(authId,appKey);
	}

	@Override
	public MemberRuleVersionDTO queryMemberRuleVersion() {
		return memberShipService.queryMemberRuleVersion();
	}

	@Override
	public void uploadServiceVer(UploadServiceVerInput uploadServiceVerInput) {
		this.memberShipService.uploadServiceVer(uploadServiceVerInput);
	}

	@Override
	public BaseBean getSMSVerifyCode(SMSVerifyBeanInput smsVerifyBeanInput)
			throws UserMessageException {
		return this.memberShipService.getSMSVerifyCode(smsVerifyBeanInput);
	}

	@Override
	public BaseBean getSMSVerifyCodeV2(SMSVerifyBeanInput smsVerifyBeanInput) throws VerifyCodeException {
		return this.memberShipService.getSMSVerifyCodeV2(smsVerifyBeanInput);
	}

	@Override
	public BaseBean getSMSVerifyCodeV3(SMSVerifyBeanInput smsVerifyBeanInput, AliAfs aliAfs) throws VerifyCodeException {
		return this.memberShipService.getSMSVerifyCodeV3(smsVerifyBeanInput, aliAfs);
	}

	@Override
	public boolean changeMail(String oldMail, String newMail, String authId, UpdateUserDto updateUserDto) {
		return this.memberShipService.changeMail(oldMail, newMail, authId, updateUserDto);
	}

	@Override
	public boolean changeAddress(String authId, String address, String province, String city, String area,
			UpdateUserDto updateUserDto) {
		return this.memberShipService.changeAddress(authId, address, province, city, area, updateUserDto);
	}

	@Override
	public boolean resetPassword(String authId, String password, UpdateUserDto updateUserDto) {
		return this.memberShipService.resetPassword(authId, password, updateUserDto);
	}

	@Override
	public boolean cardPause(CardPauseDto cardPauseDto, UpdateUserDto updateUserDto) {
		return this.memberShipService.cardPause(cardPauseDto, updateUserDto);
	}

//	@Override
//	public boolean cardOperate(CardOperateDto cardOperateDto, UpdateUserDto updateUserDto) throws UnregisterException {
//		return this.cardOperate(cardOperateDto, updateUserDto);
//	}

	@Override
	public BaseResponse checkUserCard(String authId, Integer membershipType) {
		return this.memberShipService.checkUserCard(authId, membershipType);
	}

	@Override
	public void unregister(UnregisterDto unregisterDto) throws UnregisterException {
		this.unregister(unregisterDto);
	}

	@Override
	public void unregisterRecover(UnregisterDto unregisterDto) throws UnregisterException {
		this.unregisterRecover(unregisterDto);
	}

	@Override
	public void unregisterRecoverByAuthId(AccountRecoverDto accountRecoverDto) throws UnregisterException {
		this.unregisterRecoverByAuthId(accountRecoverDto);
	}

	@Override
	public AccountStatusDto getAccountStatusByMobile(String mobilePhone, Integer membershipType) {
		return this.getAccountStatusByMobile(mobilePhone, membershipType);
	}

	@Override
	public AccountStatusDto getAccountStatusByMobileV2(String mobilePhone, Integer membershipType, String orgId) {
		return this.getAccountStatusByMobileV2(mobilePhone, membershipType,orgId);
	}

	@Override
	public AccountStatusDto getAccountStatusByDriverCode(String driverCode, Integer membershipType) {
		return this.getAccountStatusByDriverCode(driverCode, membershipType);
	}

	public IMemberShipService getMemberShipService() {
		return memberShipService;
	}

	public void setMemberShipService(IMemberShipService memberShipService) {
		this.memberShipService = memberShipService;
	}

	@Override
	public List<String> getAllAuthId(String authId) {
		return this.memberShipService.getAllAuthId(authId);
	}

	@Override
	public AgencyMinsDto getAgencyMinsByid(String orgId) {
		return this.memberShipService.getAgencyMinsByid(orgId);
	}

	@Override
	public int queryInsideFlag(String agencyId) {
		return this.memberShipService.queryInsideFlag(agencyId);
	}

	@Override
	public AgencyInfoDto queryAgencyInfoByAgencyId(String agencyId) {
		return this.memberShipService.queryAgencyInfoByAgencyId(agencyId);
	}

	@Override
	public int getOrgCardStatusById(String orgId, String cardNo) {
		return this.memberShipService.getOrgCardStatusById(orgId, cardNo);
	}

	@Override
	public MembershipBasicInfo getUserBasicInfo(String mid) {
		return this.memberShipService.getUserBasicInfo(mid);
	}

	@Override
	public MembershipBasicInfo getUserBasicInfo(String authId,
			Short membershipType) {
		return this.memberShipService.getUserBasicInfo(authId, membershipType);
	}

	@Override
	public MembershipBasicInfo getUserBasicInfoByPkId(Long pkId) {
		return memberShipService.getUserBasicInfoByPkId(pkId);
	}

	@Override
	public MembershipBasicInfo getMembershipByPhone(String phone,
			int membershipType) {
		if(StringUtils.isBlank(phone)) {
			throw BusinessRuntimeException.PARAM_EXEPTION;
		}
		return this.memberShipService.getMembershipByPhone(phone, membershipType);
	}


	@Override
	public MembershipBlobDTO getMembershipByAuthId(String authId, int membershipType) {
		return this.memberShipService.getMembershipByAuthId(authId, membershipType);
	}

	@Override
	public MembershipBlobDTO getMemberByUid(String uid) {
		return this.memberShipService.getMemberByUid(uid);
	}

	@Override
	public int updateMemberUidByPkId(Long pkId, String uid) {
		return this.memberShipService.updateMemberUidByPkId(pkId, uid);
	}

	@Override
	public EnterpriseAgencyInfoDto queryAgencyInfoByName(String name) {
		return this.memberShipService.queryAgencyInfoByName(name);
	}

	@Override
	public DriverLicenseValidResultDto checkDriverLicenseValid(String name, String cardNo, String archviesNo) {
		return this.memberShipService.checkDriverLicenseValid(name,cardNo,archviesNo);
	}

	@Override
	public ReviewAndCarStatusDto reviewAndCardStatus(String authId,
			Integer membershipType) {
		return this.memberShipService.reviewAndCardStatus(authId, membershipType);
	}

	@Override
	public Boolean checkCardValid(String cardNo) {
		return this.memberShipService.checkCardValid(cardNo);
	}

	@Override
	public void saveUserOperationLog(UserOperationLogInput userOperationLogInput) {
		this.memberShipService.saveUserOperationLog(userOperationLogInput);
	}

	@Override
	public void findPassword(String mobilePhone, String verifyCode, String newPassword, UpdateUserDto updateUserDto)
			throws UserMessageException {
		this.memberShipService.findPassword(mobilePhone,  verifyCode,  newPassword,  updateUserDto);
	}

	@Override
	public void findPasswordV2(FindPasswordDto findPasswordDto) throws FindPasswordException {
		this.memberShipService.findPasswordV2(findPasswordDto);
	}

	@Override
	public void modifyPassword(ModifyPasswordDTO modifyPasswordDTO)
			throws UserMessageException {
		this.memberShipService.modifyPassword(modifyPasswordDTO);
	}

	@Override
	public void modifyPasswordV2(ModifyPasswordDTO modifyPasswordDTO) throws ModifyPasswordException {
		this.memberShipService.modifyPasswordV2(modifyPasswordDTO);
	}

	@Override
	public void resetPassword(String authId, String password, String optUserName, Date updateTime) throws UserMessageException {
		this.memberShipService.resetPassword(authId, password, optUserName, updateTime);
	}

	@Override
	public void modifyMobilePhone(String authId, String newMobilePhone, String verifyCode,
								  String oldMobile, UpdateUserDto updateUserDto) throws UserMessageException {
		this.memberShipService.modifyMobilePhone(authId, newMobilePhone, verifyCode, oldMobile, updateUserDto);
	}

	@Override
	public void modifyMobilePhoneV2(ModifyMobilePhoneDto modifyMobilePhoneDto) throws ChangeMobileException {
		this.modifyMobilePhoneV2(modifyMobilePhoneDto);
	}

	@Override
	public void updateDbMobilePhone(String authId, Long userPkId, String newMobile, String oldMobile, UpdateUserDto updateUserDto) throws ChangeMobileException {
		this.updateDbMobilePhone(authId, userPkId, newMobile, oldMobile, updateUserDto);
	}

	@Override
	public MembershipRegionInfo getUserRegionInfo(String authId, Short membershipType) {
		return this.memberShipService.getUserRegionInfo(authId,membershipType);
	}

	@Override
	public AppKeyDto getMemberAuthOrigin(String authId, Integer membershipType, Long operateType) {
		return this.memberShipService.getMemberAuthOrigin(authId, membershipType, operateType);
	}

	@Override
	public List<ExpressInfoDTO> queryExpressInfo(String authId) {
		return memberShipService.queryExpressInfo(authId);
	}

	@Override
	public List<MemberClauseRecord> queryMemberClauseRecord(QueryMemberClauseRecordInput input) {
		return memberShipService.queryMemberClauseRecord(input);
	}

	@Override
	public List<MemberOperateLogDTO> queryMemberOperateLog(QueryMemberOperateLogInput input) {
		return memberShipService.queryMemberOperateLog(input);
	}

	@Override
	public List<SimpleMembershipInfoDTO> querySimpleMembershipInfoList(QuerySimpleMemberInput querySimpleMemberInput) {
		return memberShipService.querySimpleMembershipInfoList(querySimpleMemberInput);
	}

	@Override
	public List<UserOperatorLogDTO> queryUserOperatorLog(QueryMemberOperateLogInput input) {
		return memberShipService.queryUserOperatorLog(input);
	}

	@Override
	public StsTokenDto getStsToken(String bucketName) {
		return memberShipService.getStsToken(bucketName);
	}

	@Override
	public String getStsUrlTokenGet(String bucketName, String objectName) {
		return memberShipService.getStsUrlTokenGet(bucketName, objectName);
	}

	@Override
	public void submitDrivingLicenseInfo(String authId, String appKey, SubmitDrivingLicenseInput input) throws AuthenticationException {
		memberShipService.submitDrivingLicenseInfo(authId, appKey, input);
	}

	@Override
	public Boolean updateUserFileNo(UpdateFileNoInput updateFileNoInput) throws AuthenticationException {
		return memberShipService.updateUserFileNo(updateFileNoInput);
	}

	@Override
	public void submitUserIDCardPic(SubmitUserIdCardPicInput picInput) throws AuthenticationException {
		memberShipService.submitUserIDCardPic(picInput);
	}

	@Override
	public void submitFaceRecognitionPic(SubmitFacePicInput input) throws AuthenticationException {
		memberShipService.submitFaceRecognitionPic(input);
	}

	@Override
	public void submitOnlyFaceRecognitionPic(SubmitFaceInfoInput input) throws AuthenticationException {
		memberShipService.submitOnlyFaceRecognitionPic(input);
	}

	@Override
	public Map<String, String> faceAuthentication(String name, String idCardNumber, byte[] fileByte, String liveDelta, String authId) throws AuthenticationException {
		return memberShipService.faceAuthentication(name, idCardNumber, fileByte, liveDelta, authId);
	}

	@Override
	public Map<String, String> faceAuthentication(String name, String idCardNumber, byte[] fileByte, String liveDelta, String authId, String appKey) throws AuthenticationException {
		return memberShipService.faceAuthentication(name, idCardNumber, fileByte, liveDelta, authId, appKey);
	}

	@Override
	public Map<String, String> faceAuthentication(String name, String idCardNumber, byte[] fileByte, String liveDelta, String authId, String appKey, String imageUrl) throws AuthenticationException {
		return memberShipService.faceAuthentication(name, idCardNumber, fileByte, liveDelta, authId, appKey, imageUrl);
	}

	@Override
	public Map<String, String> sensetimeFaceAuthentication(String name, String idCardNumber, byte[] fileByte, String liveDelta, String authId, String appKey) throws AuthenticationException {

		return memberShipService.sensetimeFaceAuthentication(name,idCardNumber,fileByte,liveDelta,authId,appKey);
	}

	@Override
	public Map<String, String> baiDuFaceAuthentication(String name, String idCardNumber, String imageUrl, String authId, String appKey) throws AuthenticationException {
		return memberShipService.baiDuFaceAuthentication(name, idCardNumber, imageUrl, authId, appKey);
	}

    @Override
    public Map<String, String> baiDuFaceAuthenticationSec(String name, String idCardNumber, String image, Integer appType, String authId, String appKey) throws AuthenticationException {
        return memberShipService.baiDuFaceAuthenticationSec(name, idCardNumber, image, appType, authId, appKey);
    }

    @Override
	public FacePersonVerifyResult baiduEnhancerFaceMatch(FaceMatchInput input) {
		return memberShipService.baiduEnhancerFaceMatch(input);
	}

    @Override
    public FacePersonVerifyResult baiduFaceMatch(FaceMatchInput faceMatchInput) {
        return memberShipService.baiduFaceMatch(faceMatchInput);
    }

    @Override
    public FaceLiveSessionCode getFaceLiveSessionCode() {
        return memberShipService.getFaceLiveSessionCode();
    }

    @Override
    public FaceLiveVerifyResult faceLiveVerify(FaceLiveVerifyInput input) {
        return memberShipService.faceLiveVerify(input);
    }

    @Override
	public void updateFaceRecognitionPic(UpdateFacePicInput input) throws AuthenticationException {
		memberShipService.updateFaceRecognitionPic(input);
	}

	@Override
	public void orderVehicleUpdateFace(UpdateFacePicInput input) throws AuthenticationException {
		memberShipService.orderVehicleUpdateFace(input);
	}

	@Override
	public DriverLicenseQueryResultDTO queryDriverLicenseDetail(String authId, UpdateUserDto operator) {
		return memberShipService.queryDriverLicenseDetail(authId, operator);
	}

	@Override
	public void driverLicenseElementsAuthenticate(String authId) {
		memberShipService.driverLicenseElementsAuthenticate(authId);
	}

	@Override
	public void driverLicenseElementsAuthenticate(String authId, UpdateUserDto operator) {
		memberShipService.driverLicenseElementsAuthenticate(authId, operator);
	}

	@Override
	public void saveDriverLicenseElementsAuthenticateRecord(SaveDriverElementsAuthenticateInput input) throws AuthenticationException {
		memberShipService.saveDriverLicenseElementsAuthenticateRecord(input);
	}

	@Override
	public void saveDriverLicenseElementsAuthenticateLog(SaveDriverElementsAuthenticateLogInput input) {
		memberShipService.saveDriverLicenseElementsAuthenticateLog(input);
	}

	@Override
	public String getSMSVerifyCodeWithoutSendSMS(
			SMSVerifyBeanInput smsVerifyBeanInput) throws VerifyCodeException {
		return memberShipService.getSMSVerifyCodeWithoutSendSMS(smsVerifyBeanInput);
	}

	@Override
	public DriverLicenseAuthResultDTO getLastDriverLicenseAuthResult(String authId) {
		return memberShipService.getLastDriverLicenseAuthResult(authId);
	}

	@Override
	public DriverLicenseAuthResultDTO getLastAuthenticateRecordByUser(String authId, Integer membershipType) {
		return memberShipService.getLastAuthenticateRecordByUser(authId,membershipType);
	}

	@Override
	public DriverLicenseElementsAuthenticateLogDTO getLastAuthenticateLogByRecordId(Long recordId, Integer logType) {
		return memberShipService.getLastAuthenticateLogByRecordId(recordId,logType);
	}

	@Override
	public UploadMemberImageDto uploadMemberImage(Integer type, byte[] byteArray, String authId) throws BusinessException {
		return memberShipService.uploadMemberImage(type, byteArray, authId);
	}

	@Override
	public MmpUserTagDto queryUserTagByAuthId(String authId) {
		return memberShipService.queryUserTagByAuthId(authId);
	}

	@Override
	public List<MmpUserTagDto> queryUserTagListByAuthIds(List<String> authIds) {
		return memberShipService.queryUserTagListByAuthIds(authIds);
	}

	@Override
	public int updateUserTagByAuthId(MmpUserTagDto userTagDto) {
		return memberShipService.updateUserTagByAuthId(userTagDto);
	}


	@Override
	public List<QueryOrgCardInfoDto> queryOrgCardList(String orgId, Integer cardStatus) {
		return memberShipService.queryOrgCardList(orgId, cardStatus);
	}

	@Override
	public String ocrDriverLicense(byte[] fileByte) throws AuthenticationException {
		return memberShipService.ocrDriverLicense(fileByte);
	}

	@Override
	public void updateMemberInfoFromFlyingPig(UpdateMemberInfoFromFlyingPigInputDto inputDto) throws AuthenticationException {
		memberShipService.updateMemberInfoFromFlyingPig(inputDto);
	}

	@Override
	public Boolean checkImeiBlackList(String imei) {
		return memberShipService.checkImeiBlackList(imei);
	}

	@Override
	public MembershipAdditionalDto getAdditionalInfo(String authId) {
		return memberShipService.getAdditionalInfo(authId);
	}

	@Override
	public List<DriverLicenseElementsAuthenticateLogDTO> getLastAuthenticateLogsByUser(String authId, Integer logType, Integer num) {
		return memberShipService.getLastAuthenticateLogsByUser(authId, logType, num);
	}

	@Override
	public boolean insertUserOperateLog(UserOperatorLogDTO userOperatorLogDTO) {
		return memberShipService.insertUserOperateLog(userOperatorLogDTO);
	}

	@Override
	public Boolean isExistMemberClauseLog(String authId) {
		return memberShipService.isExistMemberClauseLog(authId);
	}

	@Override
	public void setVirtualCard(String authId) throws AuthenticationException {
		memberShipService.setVirtualCard(authId);
	}

	@Override
	public void setVirtualCard(String authId,int memberType) throws AuthenticationException {
		memberShipService.setVirtualCard(authId);
	}

    @Override
    public HealthCodeDto queryHealthCode(String authId, Integer type) {
        return memberShipService.queryHealthCode(authId,type);
    }

	@Override
	public List<MembershipBasicInfo> queryEffectiveMemberForCq(String orgId, String createdTime, String updatedTime) {
		return memberShipService.queryEffectiveMemberForCq(orgId,createdTime,updatedTime);
	}

	@Override
	public void getSMSAliAFS(AliAfs aliAfs) throws VerifyCodeException {
		memberShipService.getSMSAliAFS(aliAfs);
	}

	@Override
	public ModifyPhoneResultDto modifyMobilePhoneUnLogin(String newMobilePhone, String verifyCode,
												  String oldMobile, UpdateUserDto updateUserDto,String driverCode, String passWord, String channel) throws  ChangeMobileException{
		return memberShipService.modifyMobilePhoneUnLogin(newMobilePhone,verifyCode,oldMobile,updateUserDto,driverCode, passWord, channel);
	}

	@Override
	public void submitDepositSecurity(SubmitDepositSecurityInput depositSecurityInput){
		memberShipService.submitDepositSecurity(depositSecurityInput);
	}

	@Override
	public void checkInvitationCode(String invitationCode) {
		memberShipService.checkInvitationCode(invitationCode);

	}

	@Override
	public void saveUserOperationLogOldTable(String operatorContent, String foreignKey, String foreignKey2,
			String createUser) {
		memberShipService.saveUserOperationLogOldTable(operatorContent, foreignKey, foreignKey2, createUser);
	}

	@Override
	public UserOrgInfoDto getMemberOrgInfo(String authId, Integer type) {
		return memberShipService.getMemberOrgInfo(authId, type);
	}

	@Override
	public void updateUserStudentCardUrl(String authId, String studentCardUrl) {
		memberShipService.updateUserStudentCardUrl(authId, studentCardUrl);
	}

	@Override
	public Integer getUserAge(Long pkId) {
		return memberShipService.getUserAge(pkId);
	}

	@Override
	public DrivingLicenseOcrRespDto getDrivingLicenseOcr(DrivingLicenseOcrRequestDto drivingLicenseOcrRequestDto) {
		return memberShipService.getDrivingLicenseOcr(drivingLicenseOcrRequestDto);
	}

	@Override
	public int insertSelective(UserFaceContrastResultDto record) {
		return memberShipService.insertSelective(record);
	}

	@Override
	public String addMembershipInfoForMmp(MembershipForMmpDTO membershipForMmpDTO) {
		return memberShipService.addMembershipInfoForMmp(membershipForMmpDTO);
	}

	@Override
	public void getFaceContrastResult(String holdIdImg, String faceImg, String authId) throws AuthenticationException {
		memberShipService.getFaceContrastResult(holdIdImg, faceImg, authId);
	}

	@Override
	public GetMemberByTokenDto getMembershipBasicInfoByToken(String token) throws BusinessException {
		return memberShipService.getMembershipBasicInfoByToken(token);
	}

	@Override
	public void ofcSubmitOnlyFaceRecognitionPic(SubmitFaceInfoInput input) throws AuthenticationException {
		memberShipService.ofcSubmitOnlyFaceRecognitionPic(input);
	}

	@Override
	public Map<String, String> baiDuFaceAuthenticationSec2(String name, String idCardNumber, String imageSrc, int imageType, Integer appType, String authId, String appKey) throws AuthenticationException {
		return memberShipService.baiDuFaceAuthenticationSec2(name, idCardNumber, imageSrc, imageType,appType, authId, appKey);
	}

	@Override
	public Map<String, String> baiDuFaceAuthentication2(String name, String idCardNumber, String imageSrc, int imageType, String authId, String appKey) throws AuthenticationException {
		return memberShipService.baiDuFaceAuthentication2(name, idCardNumber, imageSrc, imageType, authId, appKey);
	}

	@Override
	public void updateSecondAppKey(String authId, String secondAppKey) {
		memberShipService.updateSecondAppKey(authId, secondAppKey);
	}
}
