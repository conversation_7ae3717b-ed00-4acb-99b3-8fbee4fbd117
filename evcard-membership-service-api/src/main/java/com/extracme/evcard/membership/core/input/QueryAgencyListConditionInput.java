package com.extracme.evcard.membership.core.input;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class QueryAgencyListConditionInput implements Serializable {

    /**
     * 机构id
     */
    private String orgId;

    /** 是否需要模糊查询机构  null/0 否  1 左模糊 2 右模糊 3 左右模糊 */
    private Integer isLikeQueryOrgId;

    /**
     * agencyId 集合
     */
    private List<String> agencyIds;

    /**
     * in查询条件  null/true = in    false = not in
     */
    private Boolean isInQueryAgencyIds;

    /** 状态 0 无效 1 有效 */
    private Integer status;

}
