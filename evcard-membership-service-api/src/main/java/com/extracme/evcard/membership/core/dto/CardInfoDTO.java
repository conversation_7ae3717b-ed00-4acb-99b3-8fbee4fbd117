package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;

/**
 * .卡号详情
 * <AUTHOR>
 */
public class CardInfoDTO implements Serializable{
    private static final long serialVersionUID = 4622154155575324326L;
    private String cardNo;
    private String internalNo;
    private String validityTime;
    private String rwKeytAa;
    private String rwKeytBb;
    private Integer cardType;
    private Integer activateStatus;
    private String activateTime;
    /** 卡号类型（1:虚拟卡号，2:物理卡号） */
    private Integer cardNoType;
    private String createdTime;
    private String createdUser;
    private String updatedTime;
    private String updatedUser;
    /** 证件号 */
    private String authId;
    /** 状态：1有效 0无效 */
    private Integer status;

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getInternalNo() {
        return internalNo;
    }

    public void setInternalNo(String internalNo) {
        this.internalNo = internalNo;
    }

    public String getValidityTime() {
        return validityTime;
    }

    public void setValidityTime(String validityTime) {
        this.validityTime = validityTime;
    }

    public String getRwKeytAa() {
        return rwKeytAa;
    }

    public void setRwKeytAa(String rwKeytAa) {
        this.rwKeytAa = rwKeytAa;
    }

    public String getRwKeytBb() {
        return rwKeytBb;
    }

    public void setRwKeytBb(String rwKeytBb) {
        this.rwKeytBb = rwKeytBb;
    }

    public Integer getCardType() {
        return cardType;
    }

    public void setCardType(Integer cardType) {
        this.cardType = cardType;
    }

    public Integer getActivateStatus() {
        return activateStatus;
    }

    public void setActivateStatus(Integer activateStatus) {
        this.activateStatus = activateStatus;
    }

    public String getActivateTime() {
        return activateTime;
    }

    public void setActivateTime(String activateTime) {
        this.activateTime = activateTime;
    }

    public Integer getCardNoType() {
        return cardNoType;
    }

    public void setCardNoType(Integer cardNoType) {
        this.cardNoType = cardNoType;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public String getCreatedUser() {
        return createdUser;
    }

    public void setCreatedUser(String createdUser) {
        this.createdUser = createdUser;
    }

    public String getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(String updatedTime) {
        this.updatedTime = updatedTime;
    }

    public String getUpdatedUser() {
        return updatedUser;
    }

    public void setUpdatedUser(String updatedUser) {
        this.updatedUser = updatedUser;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "CardInfoDTO{" +
                "cardNo='" + cardNo + '\'' +
                ", internalNo='" + internalNo + '\'' +
                ", validityTime='" + validityTime + '\'' +
                ", rwKeytAa='" + rwKeytAa + '\'' +
                ", rwKeytBb='" + rwKeytBb + '\'' +
                ", cardType=" + cardType +
                ", activateStatus=" + activateStatus +
                ", activateTime='" + activateTime + '\'' +
                ", cardNoType=" + cardNoType +
                ", createdTime='" + createdTime + '\'' +
                ", createdUser='" + createdUser + '\'' +
                ", updatedTime='" + updatedTime + '\'' +
                ", updatedUser='" + updatedUser + '\'' +
                ", authId='" + authId + '\'' +
                ", status=" + status +
                '}';
    }
}