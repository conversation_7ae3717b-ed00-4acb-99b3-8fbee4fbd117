package com.extracme.evcard.membership.core.service;

import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.input.CancelCardInputDTO;
import com.extracme.evcard.membership.core.input.CreateCardInputDTO;
import com.extracme.evcard.membership.core.input.QueryMemberInputDTO;
import com.extracme.evcard.membership.core.input.QueryMemberListInputDTO;
import com.extracme.evcard.membership.core.input.UpdateMemberInputDTO;

import java.util.List;

/**
 * <p>
 * 调度系统 会员相关服务
 * </p>
 *
 * <AUTHOR>
 * @date 2019/8/23
 */
public interface IMemberForIdsService {


   /**
    * 根据条件查询会员基本信息
    * @param queryMemberInputDTO
    * @return
    */
   MembershipBasicInfo queryMemberInfo(QueryMemberInputDTO queryMemberInputDTO);


   /**
    * 根据条件查询会员列表
    * @param queryMemberListInputDTO
    * @return
    */
   List<MembershipBasicInfo> queryMemberList(QueryMemberListInputDTO queryMemberListInputDTO);


   /**
    * 模糊查询内部会员名称
    * @param name
    * @return
    */
   List<String> queryInnerMemberNames(String name);


   /**
    * 模糊查询内部会员手机号
    * @param mobile
    * @return
    */
   List<String> queryInnerMemberMobiles(String mobile);



   /**
    * 更新内部会员信息
    * @param updateMemberInputDTO 更新项 更新条件 authId memberType
    * @return
    */
   boolean updateMemberInfo(UpdateMemberInputDTO updateMemberInputDTO);




}
