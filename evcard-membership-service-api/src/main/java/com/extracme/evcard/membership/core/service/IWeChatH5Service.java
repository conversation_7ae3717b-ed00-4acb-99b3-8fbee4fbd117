package com.extracme.evcard.membership.core.service;

import com.extracme.evcard.membership.core.bean.LoginBeanResult;
import com.extracme.evcard.membership.core.dto.WeixinSign;
import com.extracme.evcard.rpc.exception.BusinessException;

/**
 * 微信公众号服务.<br>
 * <AUTHOR>
 *
 */
public interface IWeChatH5Service {
	
	/**
	 * 微信公众号验证码登录.<br>
	 * @param openId	微信公众号openId
	 * @param v			openId的签名.
	 * @param mobile	手机号.
	 * @param authcode	验证码.
	 * @param access_token 微信公众号access_token
	 * @param channel	渠道.
	 * @return
	 */
	LoginBeanResult weChatLogin(String openId, String v, String mobile, String authcode, String access_token, String appKey) throws BusinessException;
	
	/**
	 * 微信公众号授权登录.<br>
	 * @param openId
	 * @param v
	 * @return
	 */
	LoginBeanResult weChatLogin(String openId, String v, String appKey) throws BusinessException;
	
	/**
	 * 微信url签名.
	 * @param url
	 * @return
	 */
	WeixinSign weChatH5Sign(String url);

}
