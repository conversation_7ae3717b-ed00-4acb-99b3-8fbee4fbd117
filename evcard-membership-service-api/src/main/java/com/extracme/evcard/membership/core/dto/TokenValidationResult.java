package com.extracme.evcard.membership.core.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * Token验证结果
 * 
 * <AUTHOR>
 * @since 2025-08-20
 */
@Data
public class TokenValidationResult implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 验证是否成功
     */
    private boolean valid;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 错误代码
     */
    private String errorCode;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户MID
     */
    private String mid;
    
    /**
     * 应用标识
     */
    private String appKey;
    
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 设备类型
     */
    private String deviceType;
    
    /**
     * Token过期时间
     */
    private Date expireTime;
    
    /**
     * Token签发时间
     */
    private Date issuedAt;
    
    /**
     * 用户权限信息
     */
    private Map<String, Object> permissions;
    
    /**
     * 其他自定义声明
     */
    private Map<String, Object> customClaims;
    
    /**
     * 创建成功的验证结果
     */
    public static TokenValidationResult success() {
        TokenValidationResult result = new TokenValidationResult();
        result.setValid(true);
        return result;
    }
    
    /**
     * 创建失败的验证结果
     */
    public static TokenValidationResult failure(String errorCode, String errorMessage) {
        TokenValidationResult result = new TokenValidationResult();
        result.setValid(false);
        result.setErrorCode(errorCode);
        result.setErrorMessage(errorMessage);
        return result;
    }
}
