package com.extracme.evcard.membership.core.dto;


import lombok.Data;

import java.io.Serializable;

@Data
public class MemberStatusDto implements Serializable {
    private String mid;

    private String mobilePhone;

    private String authId;
    /**
     * 账号状态 0正常 1注销冻结中 2已注销
     */
    private Integer accountStatus;
    /**
     * 用户身份认证状态
     */
    private Integer identityReviewStatus;
    /**
     * 用户驾照认证状态
     */
    private Integer licenseReviewStatus;
    /**
     * 原审核状态
     * 目标值：审核状态(-1:资料不全 0:待审核 1：审核通过 2: 审核不通过 3：用户无效 4:重新审核）
     */
    private Integer reviewStatus;

    /**
     * 原身份认证状态
     * 目标值：认证状态 0待认证 1认证通过 2认证不通过 3查证中(待重新认证)
     */
    private Integer authenticateStatus;
}
