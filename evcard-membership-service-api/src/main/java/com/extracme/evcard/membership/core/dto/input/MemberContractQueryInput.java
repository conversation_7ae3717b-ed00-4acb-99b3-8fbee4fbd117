package com.extracme.evcard.membership.core.dto.input;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/11/20
 */
@Data
public class MemberContractQueryInput implements Serializable {

    /**
     * 会员id, 必传
     */
    private String authId;

    /**
     * 条款类型 （1：会员守则 2：隐私政策）， 必传
     */
    private Integer provisionType;

    /**
     * 日期, 必传
     */
    private Date date;

    /**
     * 版本号，如0045， 可选
     */
    private String versionId;

}
