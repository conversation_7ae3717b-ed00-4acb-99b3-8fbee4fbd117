package com.extracme.evcard.membership.core.dto.input;

import com.extracme.evcard.membership.core.enums.OrderVehicleStatusEnum;

import java.io.Serializable;
import java.math.BigDecimal;

public class CheckDepositResult implements Serializable{

    private static final long serialVersionUID = -1327371029648149330L;
    /** 押金不足时需要支付的押金 */
    private BigDecimal needDepositAmount;
    /** 结果状态 */
    private int resultStatus;
    /** 结果描述 */
    private String resultDesc;
    /** 押金类型  0  无 (押金不足) 1:押金 ，2:免押 ，3:预授权 ，4:芝麻信用*/
    private int depositType;
    /** 芝麻授权状态 0 无法使用  1 未授权 2 授权信用未达标 3 已授权 4 未开通*/
    private int zhimaStatus;
    /**  代扣授权状态  0 否 1 是 */
    private int withHoldSignStatus;
    /**芝麻信用message*/
    private String zhimaMessage;

    public BigDecimal getNeedDepositAmount() {
        return needDepositAmount;
    }

    public void setNeedDepositAmount(BigDecimal needDepositAmount) {
        this.needDepositAmount = needDepositAmount;
    }

    public int getResultStatus() {
        return resultStatus;
    }

    public void setResultStatus(int resultStatus) {
        this.resultStatus = resultStatus;
    }

    public String getResultDesc() {
        return resultDesc;
    }

    public void setResultDesc(String resultDesc) {
        this.resultDesc = resultDesc;
    }

    public int getDepositType() {
        return depositType;
    }

    public void setDepositType(int depositType) {
        this.depositType = depositType;
    }

    public int getZhimaStatus() {
        return zhimaStatus;
    }

    public void setZhimaStatus(int zhimaStatus) {
        this.zhimaStatus = zhimaStatus;
    }

    public int getWithHoldSignStatus() {
        return withHoldSignStatus;
    }

    public void setWithHoldSignStatus(int withHoldSignStatus) {
        this.withHoldSignStatus = withHoldSignStatus;
    }

    public void setResult(OrderVehicleStatusEnum orderVehicleStatusEnum){
        this.setResultStatus(orderVehicleStatusEnum.getErrCode());
        this.setResultDesc(orderVehicleStatusEnum.getErrMsg());
    }

    public String getZhimaMessage() {
        return zhimaMessage;
    }

    public void setZhimaMessage(String zhimaMessage) {
        this.zhimaMessage = zhimaMessage;
    }
}
