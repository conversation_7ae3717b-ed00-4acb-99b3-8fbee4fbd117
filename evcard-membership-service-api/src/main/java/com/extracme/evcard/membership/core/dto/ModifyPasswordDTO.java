package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class ModifyPasswordDTO implements Serializable{

	private static final long serialVersionUID = 8121774038075243699L;
	
	/**
	 * 会员ID
	 */
	private String authId;
	/**
	 * 原密码
	 */
	private String oldPassword; 
	/**
	 * 新密码
	 */
	private String newPassword;
	/**
	 * 0:个人用户 1：企业用户
	 */
	private Integer orgUser;
	/**
	 * 企业用户姓名
	 */
	private String orgUserName;
	/**
	 * 操作人ID
	 */
	private Long optUserId;
	/**
	 * 操作人姓名
	 */
	private String optUserName;
	/**
	 * 操作时间
	 */
	private Date updateTime;

	/**
	 * 用户机构id
	 */
	private String orgId;

	public String getOrgId() {
		return orgId;
	}

	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}

	public String getAuthId() {
		return authId;
	}
	public void setAuthId(String authId) {
		this.authId = authId;
	}
	public String getOldPassword() {
		return oldPassword;
	}
	public void setOldPassword(String oldPassword) {
		this.oldPassword = oldPassword;
	}
	public String getNewPassword() {
		return newPassword;
	}
	public void setNewPassword(String newPassword) {
		this.newPassword = newPassword;
	}
	public Integer getOrgUser() {
		return orgUser;
	}
	public void setOrgUser(Integer orgUser) {
		this.orgUser = orgUser;
	}
	public String getOrgUserName() {
		return orgUserName;
	}
	public void setOrgUserName(String orgUserName) {
		this.orgUserName = orgUserName;
	}
	public Long getOptUserId() {
		return optUserId;
	}
	public void setOptUserId(Long optUserId) {
		this.optUserId = optUserId;
	}
	public String getOptUserName() {
		return optUserName;
	}
	public void setOptUserName(String optUserName) {
		this.optUserName = optUserName;
	}
	public Date getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Override
	public String toString() {
		return "ModifyPasswordDTO{" +
				"authId='" + authId + '\'' +
				", oldPassword='" + oldPassword + '\'' +
				", newPassword='" + newPassword + '\'' +
				", orgUser=" + orgUser +
				", orgUserName='" + orgUserName + '\'' +
				", optUserId=" + optUserId +
				", optUserName='" + optUserName + '\'' +
				", updateTime=" + updateTime +
				", orgId='" + orgId + '\'' +
				'}';
	}
}
