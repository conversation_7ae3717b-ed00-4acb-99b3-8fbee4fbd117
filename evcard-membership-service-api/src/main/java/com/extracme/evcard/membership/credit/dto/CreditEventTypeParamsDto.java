package com.extracme.evcard.membership.credit.dto;

import com.extracme.evcard.rpc.dto.BaseResponse;

/**
 * Created by Elin on 2017/11/22.
 * 事件类型列表查询参数
 */
public class CreditEventTypeParamsDto extends BaseResponse {

    private static final long serialVersionUID = 115283408870827585L;

    /**
     * 事件类型名称
     */
    private String eventName;

    /**
     * 事件类型性质 1-正面 0-负面
     */
    private String eventNature;

    /**
     * 触发方式 0-自动 1-手动
     */
    private Integer eventWay;

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public String getEventNature() {
        return eventNature;
    }

    public void setEventNature(String eventNature) {
        this.eventNature = eventNature;
    }

    public Integer getEventWay() {
        return eventWay;
    }

    public void setEventWay(Integer eventWay) {
        this.eventWay = eventWay;
    }
}
