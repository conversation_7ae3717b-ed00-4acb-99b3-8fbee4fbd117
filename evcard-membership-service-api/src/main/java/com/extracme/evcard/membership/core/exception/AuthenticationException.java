package com.extracme.evcard.membership.core.exception;

import com.extracme.evcard.rpc.enums.StatusCode;
import com.extracme.evcard.rpc.exception.BusinessException;

/**
 * 认证异常
 * <AUTHOR>
 */
public class AuthenticationException extends BusinessException {

	private static final long serialVersionUID = 9047885777924504459L;

	public AuthenticationException(int code) {
		super(code);
	}

	public AuthenticationException(int code, String message) {
		super(code, message);
	}

	public AuthenticationException(StatusCode statusCode) {
		super(statusCode.getCode(), statusCode.getMsg());
	}

}
