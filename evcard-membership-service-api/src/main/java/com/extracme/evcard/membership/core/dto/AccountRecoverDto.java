package com.extracme.evcard.membership.core.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/4/16
 */
@Data
public class AccountRecoverDto implements Serializable {
    /**
     * 会员编号， 必填。
     */
    private String authId;

    /**
     * 会员类型， 必填。
     */
    private Short membershipType;

    /**
     * 恢复通道: 非必填
     * 暂定： 待确认。 0：无 1：会员系统 2：客服系统 3:手机APP(默认值)  4：支付宝小程序 。
     */
    private String origin;

    /**
     * 操作人： 非必填
     * 通过管理平台（非本人操作）时必须。
     */
    private Long operateUserId;

    /**
     * 操作人姓名： 非必填
     * 通过管理平台（非本人操作）时必须。
     */
    private String operateUserName;

    /**
     * 恢复原因：非必填。
     */
    private String reason;
}
