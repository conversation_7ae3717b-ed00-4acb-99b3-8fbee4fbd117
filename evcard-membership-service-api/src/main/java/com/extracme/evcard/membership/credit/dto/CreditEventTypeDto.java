package com.extracme.evcard.membership.credit.dto;

import com.extracme.evcard.rpc.dto.BaseResponse;

/**
 * Created by Elin on 2017/11/22.
 * 获取事件类型
 */
public class CreditEventTypeDto extends BaseResponse {

    private static final long serialVersionUID = 919552836449969370L;

    /**
     * 事件类型编号
     */
    private Long id;

    /**
     * 事件类型名称
     */
    private String eventName;

    /**
     * 事件类型性质 1-正面 0-负面
     */
    private String eventNature;

    /**
     * 分值
     */
    private Integer amount;

    /**
     * 是否直接加入黑名单 false-否 true-是
     */
    private Boolean blackList;

    /**
     * 触发方式 0-自动 1-手动
     */
    private Integer eventWay;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public String getEventNature() {
        return eventNature;
    }

    public void setEventNature(String eventNature) {
        this.eventNature = eventNature;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public Boolean getBlackList() {
        return blackList;
    }

    public void setBlackList(Boolean blackList) {
        this.blackList = blackList;
    }

    public Integer getEventWay() {
        return eventWay;
    }

    public void setEventWay(Integer eventWay) {
        this.eventWay = eventWay;
    }
}
