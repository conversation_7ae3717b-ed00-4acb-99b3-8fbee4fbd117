package com.extracme.evcard.membership.core.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName: MmpUserTagDto
 * @Author: wudi
 * @Date: 2019/9/16 14:40
 */
@Data
public class MmpUserTagDto implements Serializable {
    private static final long serialVersionUID = 721484113316003552L;

    private Long id;

    private String authId;

    private BigDecimal realAmount;

    private BigDecimal effectiveContd;

    private Double creditAmount;

    private String remark;

    private Date createTime;

    private Long createOperId;

    private String createOperName;

    private Date updateTime;

    private Long updateOperId;

    private String updateOperName;

    private String spare1;

    private String spare2;

    private String spare3;

    private String spare4;

    private String spare5;

    private String spare6;

    private String spare7;

    private String spare8;

    private String spare9;

    private String spare10;

    private String shopLimit;

    private Integer  profession;

    private Integer  educational;

    private Integer ownCar;

    private String studentCardUrl;

    // 支付宝订单中心授权0：未授权 1：已授权
    private Integer alipayOrderAuthority;
}
