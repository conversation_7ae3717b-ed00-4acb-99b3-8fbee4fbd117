package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;

/**
 * 微信api签名.
 * <AUTHOR>
 *
 */
public class WeixinSign implements Serializable {

	private static final long serialVersionUID = 8144078163484135540L;
	
	/**
	 * 完整的链接地址
	 */
	private String url;
	
	/**
	 * 随机字符串
	 */
	private String nonceStr;
	
	/**
	 * 时间戳
	 */
	private String timestamp;
	
	/**
	 * 签名
	 */
	private String signature;
	
	/**
	 * 
	 */
	private String jsapi_ticket;
	
	private String appId;

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getNonceStr() {
		return nonceStr;
	}

	public void setNonceStr(String nonceStr) {
		this.nonceStr = nonceStr;
	}

	public String getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(String timestamp) {
		this.timestamp = timestamp;
	}

	public String getSignature() {
		return signature;
	}

	public void setSignature(String signature) {
		this.signature = signature;
	}

	public String getJsapi_ticket() {
		return jsapi_ticket;
	}

	public void setJsapi_ticket(String jsapi_ticket) {
		this.jsapi_ticket = jsapi_ticket;
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}
	
}
