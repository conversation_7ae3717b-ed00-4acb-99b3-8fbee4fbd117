package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 * @since 2018/9/17
 */
public class CardPauseDto implements Serializable{
	private static final long serialVersionUID = -599747187877571465L;
	/**
	 * 会员卡号
	 */
	private String cardNo;
	/**
	 * 会员id
	 */
	private String authId;
	/**
	 * 暂停原因
	 */
	private String remark;
	/**
	 * 姓名
	 */
	private String name;
	/**
	 * 会员手机号
	 */
	private String mobilePhone;
	/**
	 * 暂停时间类型
	 */
	private String pauseTimeType;
	/**
	 * 恢复时间
	 */
	private String recoverTime;
	/**
	 * 状态 0-有效 1-无效
	 */
	private String status;
	
	public String getCardNo() {
		return cardNo;
	}
	public void setCardNo(String cardNo) {
		this.cardNo = cardNo;
	}
	public String getAuthId() {
		return authId;
	}
	public void setAuthId(String authId) {
		this.authId = authId;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getMobilePhone() {
		return mobilePhone;
	}
	public void setMobilePhone(String mobilePhone) {
		this.mobilePhone = mobilePhone;
	}
	public String getPauseTimeType() {
		return pauseTimeType;
	}
	public void setPauseTimeType(String pauseTimeType) {
		this.pauseTimeType = pauseTimeType;
	}
	public String getRecoverTime() {
		return recoverTime;
	}
	public void setRecoverTime(String recoverTime) {
		this.recoverTime = recoverTime;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
}
