package com.extracme.evcard.membership.core.dto.input;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;


@Data
public class OfcSubmitFacePicInput implements Serializable {
    /**
     * 会员mid
     */
    private String mid;

    /**
     * 图片信息
     * imageType 0  imageSrc 图片base64 （不要前缀 data:image/png;base64,）
     * imageType 1  imageSrc 图片文件url
     */
    private String imageSrc;

    /**
     * 图片信息类型 0,1
     */
    private int imageType = 0;
    
    /**
     * 渠道
     */
    private String appKey;

    /**
     * 平台 Android  还是 ios
     */
    private String appType;

    /**
     * 是否要求强制成功
     */
    private boolean forceSuccess;


    /**
     * 打印参数
     * @param input
     * @return
     */
    public static String getToString(OfcSubmitFacePicInput input){
        try {
            if (input == null) {
                return StringUtils.EMPTY;
            }
            String src = StringUtils.EMPTY;
            if(input.getImageType() == 0){
                src = input.getImageSrc().substring(0,10);
            }else{
                src = input.getImageSrc();
            }
            return "OfcSubmitFacePicInput{" +
                    "mid='" + input.getMid() + '\'' +
                    ", imageType=" + input.getImageType() +
                    ", appKey='" + input.getAppKey() + '\'' +
                    ", appType='" + input.getAppType() + '\'' +
                    ", imageSrc='" + src + '\'' +
                    ", forceSuccess=" + input.isForceSuccess() +
                    '}';
        } catch (Exception e) {
            return StringUtils.EMPTY;
        }
    }


}
