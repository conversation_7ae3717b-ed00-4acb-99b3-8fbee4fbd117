package com.extracme.evcard.membership.contract.service;

import com.extracme.evcard.membership.core.dto.SignContractDto;
import com.extracme.evcard.membership.core.dto.UserContractInfo;
import com.extracme.evcard.rpc.dto.BaseResponse;


/**
 * \* Created with IntelliJ IDEA.
 * \* User: Elin
 * \* Date: 2018/1/11
 * \* Time: 16:43
 * \* To change this template use File | Settings | File Templates.
 * \* Description:法大大合同服务
 * \
 */
public interface IMemberShipContractServ {

    /**
     * 调用法大大个人CA申请接口获得客户编号.<br>
     *
     * @param authName  姓名.<br>
     * @param email     邮箱.<br>
     * @param idCard    身份证证件.<br>
     * @param iDentType 0-身份证（默认值）.<br>
     * @param mobile    手机号.<br>
     * @return
     */
    String invokeSyncPersonAuto(String authName, String email, String idCard, String iDentType, String mobile);



    /**
     * 调用法大大合同模板传输接口.<br>
     *
     * @param templateId 指定条款模板编号.<br>
     * @param docUrl     模板文件地址
     * @return
     */
    @Deprecated
    BaseResponse invokeUploadTemplate(String templateId, String docUrl);

    /**
     * 自动签署合约
     * @param signContractDto
     * @return
     */
    BaseResponse autoSignContract(SignContractDto signContractDto);

    /**
     * 获取当前的供应商开关
     * @return
     */
    ContractSupplier getCurrentContractSupplier();

    /**
     * 调用法大大合同模板传输接口.<br>
     *
     * @param templateId 指定条款模板编号.<br>
     * @param docUrl     模板文件地址
     * @return
     */
    UpdateTemplateResponse uploadTemplate(String templateId, String docUrl);

    /**
     * 获取对应的协议信息
     * @param authId
     * @param templateId
     * @return
     */
    UserContractInfo gainContractUrl(String authId, String templateId);

    /**
     * 归档合同
     * @param contract
     * @remark
     */
    void archiveSignFiles(UserContractInfo contract);

    /**
     *
     * 签订租车合同
     * @return
     */
    AddRentCarContractResponse addRentCarContract(AddRentCarContractRequest request);


    /**
     * 查询租车单、结算单
     * 1：渲染html页面
     * 2：把新产生的html文件，上传至阿里云
     *
     * @param request
     * @return
     */
    OfcQueryContractResponse ofcQueryContract(OfcQueryContractRequest request);


}