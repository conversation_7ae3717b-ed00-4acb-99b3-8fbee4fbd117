package com.extracme.evcard.membership.core.input;

import java.io.Serializable;

/**
 * @ClassName: UpdateFileNoInput
 * @Author: wudi
 * @Date: 2019/8/14 10:51
 */
public class UpdateFileNoInput implements Serializable {
    private static final long serialVersionUID = -541803251735887081L;

    private String fileNo;
    private String fileNoImgUrl;
    private String authId;
    private String appkey;
    /** 输入类型   0 无 1 驾照ocr识别 2 手动输入 */
    private Integer driverLicenseInputType;

    public String getFileNo() {
        return fileNo;
    }

    public void setFileNo(String fileNo) {
        this.fileNo = fileNo;
    }

    public String getFileNoImgUrl() {
        return fileNoImgUrl;
    }

    public void setFileNoImgUrl(String fileNoImgUrl) {
        this.fileNoImgUrl = fileNoImgUrl;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public String getAppkey() {
        return appkey;
    }

    public void setAppkey(String appkey) {
        this.appkey = appkey;
    }

    public Integer getDriverLicenseInputType() {
        return driverLicenseInputType;
    }

    public void setDriverLicenseInputType(Integer driverLicenseInputType) {
        this.driverLicenseInputType = driverLicenseInputType;
    }

    /** 驾照认证状态 0待认证 1认证通过 2认证不通过 */
    private Integer licenseAuthStatus;
    /** 驾照三要素认证状态：0待认证 1认证通过 2认证不通过 3待重新认证(查证中) */
    private Integer licenseElementsAuthStatus;

    public Integer getLicenseAuthStatus() {
        return licenseAuthStatus;
    }

    public void setLicenseAuthStatus(Integer licenseAuthStatus) {
        this.licenseAuthStatus = licenseAuthStatus;
    }

    public Integer getLicenseElementsAuthStatus() {
        return licenseElementsAuthStatus;
    }

    public void setLicenseElementsAuthStatus(Integer licenseElementsAuthStatus) {
        this.licenseElementsAuthStatus = licenseElementsAuthStatus;
    }
}
