package com.extracme.evcard.membership.core.dto;

import com.extracme.evcard.membership.core.bean.BaseBean;

import java.io.Serializable;

public class AccountInfo extends BaseBean implements Serializable {
    private static final long serialVersionUID = -4995361607626785167L;


    // 总订单数
    private int orderCount;

    // 总租金
    private float totalRent;

    // 账户余额
    private String accountBalance = "0";


    public int getOrderCount() {
        return orderCount;
    }

    public void setOrderCount(int orderCount) {
        this.orderCount = orderCount;
    }

    public float getTotalRent() {
        return totalRent;
    }

    public void setTotalRent(float totalRent) {
        this.totalRent = totalRent;
    }

    public String getAccountBalance() {
        return accountBalance;
    }

    public void setAccountBalance(String accountBalance) {
        this.accountBalance = accountBalance;
    }
}
