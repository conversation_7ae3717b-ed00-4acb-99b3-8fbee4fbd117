package com.extracme.evcard.membership.core.input;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @Date 2019/8/20 16:49
 */
public class SaveDriverElementsAuthenticateLogInput implements Serializable {

    private static final long serialVersionUID = -1371827488926915622L;

    /**
     * 三要素记录id
     */
    private Long recordId;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 驾照号
     */
    private String driverCode;

    /**
     * 档案编号
     */
    private String fileNo;

    /**
     * 省份或城市名称
     */
    private String province;

    /**
     * 日志类型 0三要素 1驾照分数 2驾照信息查询
     */
    private Integer logType;

    /**
     * 供应商 0聚合 1小视
     */
    private Integer supplier;

    /**
     * 接口名称
     */
    private String serviceName;

    /**
     * 接口结果 三要素结果/驾驶证扣分
     */
    private String result;

    /**
     * 接口返回状态
     */
    private String resultCode;

    /**
     * 接口返回message
     */
    private String resultMsg;

    /**
     * 接口请求ID
     */
    private String requestId;

    /**
     * 接口返回数据
     */
    private String response;

    /**
     * 驾照状态信息：吊销、撤销、注销、停止使用、锁定等异常状态
     */
    private String licenseStatusMsg;

    /**
     * elementsReviewItems: 驾照号/姓名/档案编号
     * 0不一致 1一致
     */
    private String elementsReviewItems;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人id
     */
    private Long createOperId;

    /**
     * 创建人
     */
    private String createOperName;

    public Long getRecordId() {
        return recordId;
    }

    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDriverCode() {
        return driverCode;
    }

    public void setDriverCode(String driverCode) {
        this.driverCode = driverCode;
    }

    public String getFileNo() {
        return fileNo;
    }

    public void setFileNo(String fileNo) {
        this.fileNo = fileNo;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public Integer getLogType() {
        return logType;
    }

    public void setLogType(Integer logType) {
        this.logType = logType;
    }

    public Integer getSupplier() {
        return supplier;
    }

    public void setSupplier(Integer supplier) {
        this.supplier = supplier;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getResultMsg() {
        return resultMsg;
    }

    public void setResultMsg(String resultMsg) {
        this.resultMsg = resultMsg;
    }

    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateOperName() {
        return createOperName;
    }

    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    public Long getCreateOperId() {
        return createOperId;
    }

    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    public String getLicenseStatusMsg() {
        return licenseStatusMsg;
    }

    public void setLicenseStatusMsg(String licenseStatusMsg) {
        this.licenseStatusMsg = licenseStatusMsg;
    }

    public String getElementsReviewItems() {
        return elementsReviewItems;
    }

    public void setElementsReviewItems(String elementsReviewItems) {
        this.elementsReviewItems = elementsReviewItems;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }
}
