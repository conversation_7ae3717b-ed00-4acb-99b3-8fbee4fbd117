package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;

public class CommBaseResponse<T> implements Serializable {
    private int code = 0;
    private String message;
    private T data;
    public static final CommBaseResponse SUCCESS = new CommBaseResponse();

    public CommBaseResponse() {
    }

    public CommBaseResponse(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public CommBaseResponse(int code, String message, T output) {
        this.code = code;
        this.message = message;
        this.data =  output;
    }

    public static <T> CommBaseResponse<T> getSuccessVO(T data) {
        CommBaseResponse<T> defaultWebRespVO = new CommBaseResponse();
        defaultWebRespVO.setData(data);
        return defaultWebRespVO;
    }

    public static  CommBaseResponse getFailVO(int code, String message) {
        CommBaseResponse defaultWebRespVO = new CommBaseResponse();
        defaultWebRespVO.setCode(code);
        defaultWebRespVO.setMessage(message);
        return defaultWebRespVO;
    }

    public static  CommBaseResponse getFailVO(String message) {
        CommBaseResponse defaultWebRespVO = new CommBaseResponse();
        defaultWebRespVO.setCode(-1);
        defaultWebRespVO.setMessage(message);
        return defaultWebRespVO;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}