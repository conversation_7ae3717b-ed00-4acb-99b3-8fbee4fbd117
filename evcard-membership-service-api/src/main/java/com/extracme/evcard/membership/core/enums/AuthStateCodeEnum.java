package com.extracme.evcard.membership.core.enums;

public enum AuthStateCodeEnum {
    /**
     * 提交结果
     *
     * 身份证
     * 认证结果 1:成功 2:操作频繁 3:身份证已被上个注销账号使用 4:身份证已被提交认证 5:身份证格式错误 6:身份证已过期 7:手持护照与人脸照片不一致
     * 8:有效期不在原认证有效期之后 9:身份证编号与原认证不一致(8,9更新即将过期、过期身份证判断)
     * 10:姓名与原认证不一致
     *
     * 驾照
     * 1:成功 2:操作频繁 3:驾照已被上个注销账号使用 4:驾照号错误 5:驾照已过期 6:驾照类型不支持租车 7:驾照号与身份证号不一致
     * 8:有效期不在原有效期之后(更新时才有)
     *
     */
    SUCCESS(0, "成功", 1),
    SUBMIT_TIMES_LIMIT (31001, "操作频繁", 2),

    ACCOUNT_FROZEN(31002, "身份证件已被上个注销账号使用", 3),
    SAME_ID_NO(31003, "身份证件已被提交认证", 4),
    INVALID_ID_NO(31004, "身份证件编号格式错误", 5),
    ID_EXPIRED(31005, "身份证已过期", 6),
    FACE_ID_NOT_MATCH(31006, "手持证件与人脸照片不一致", 7),
    EARLY_EXPIRE_DATE(31007, "有效期不在原认证有效期之后", 8),
    ID_NO_NOT_MATCH(31008, "身份证编号与原认证不一致", 9),
    NAME_NOT_MATCH(31009, "姓名与原认证不一致", 10),
    UPDATE_ONLY_AUTHED_USER(31010, "更新的身份证号非认证用户", 11),
    ID_EXPIRE_DATE_ERROR(31005, "身份证到期时间格式不正确", 12),

    DRIVER_LICENSE_IN_USE(31101, "驾照已被上个注销账号使用", 3),
    DRIVER_LICENSE_ERROR(31102, "驾照号错误", 4),
    DRIVER_LICENSE_EXPIRE(31103, "驾照已过期", 5),
    DRIVER_LICENSE_TYPE_NOT_ALLOW(31104, "驾照类型不支持租车", 6),
    LICENSE_ID_NOT_MATCH(31105, "驾照与身份证不一致", 7),
    LICENSE_EARLY_EXPIRE_DATE(31106, "有效期不在原有效期之后", 8), //请输入正确的有效期
    SAME_LICENSE_ID(31107, "驾照已被提交认证", 9),
    LICENSE_EXPIRE_DATE_ERROR(31108, "驾照到期时间格式不正确", 10),
    IDCARD_HAS_EXIST_ERROR(31109, "该身份证号的驾照已被其他账号认证", 11),
    LICENSE_ID_NO_NOT_MATCH(31110, "驾照与身份证号码不一致", 15),
    LICENSE_ID_NAME_NOT_MATCH(31111, "驾照与身份证姓名不一致", 16),
    ID_EXSIT_UNFINISH_ORDER(31112, "该身份证存在未完成的订单", 17),
    DRIVER_EXSIT_UNFINISH_ORDER(31112, "该驾驶证存在未完成的订单", 18),
    SAME_MOBILE_AND_ID(31113, "该证件号已被这个手机号提交过了", 19);

    AuthStateCodeEnum(Integer code, String msg, Integer state) {
        this.code = code;
        this.msg = msg;
        this.state = state;
    }

    /** 状态值 */
    private Integer code;

    /** 描述 */
    private String msg;

    /** 状态 */
    private Integer state;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }
}
