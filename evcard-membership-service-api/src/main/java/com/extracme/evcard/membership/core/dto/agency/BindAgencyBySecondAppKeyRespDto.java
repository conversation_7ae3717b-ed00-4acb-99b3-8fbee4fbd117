package com.extracme.evcard.membership.core.dto.agency;

import lombok.Data;

import java.io.Serializable;

@Data
public class BindAgencyBySecondAppKeyRespDto implements Serializable {
    private static final long serialVersionUID = 7208373905407795890L;

    /**
     * 1:绑定失败  2:绑定成功  3:需要用户选择，未绑定 4:企业相同无需绑定
     */
    private int bindResultFlag;
    /**
     * 绑定失败原因
     */
    private String bindResultMsg;

    /**
     * 1:无企业  2:有企业 ,不相同 3:有企业，且相同 4:有企业，,不相同,但原企业已经不满足条件
     */
    private int bindType;

    /**
     * bindType 为2时，有值
     */
    private AgencyDiscountDTO oldAgencyInfo;

    private AgencyDiscountDTO newAgencyInfo;

}
