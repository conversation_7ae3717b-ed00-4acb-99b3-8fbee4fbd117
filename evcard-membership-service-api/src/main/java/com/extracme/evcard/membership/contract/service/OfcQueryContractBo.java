package com.extracme.evcard.membership.contract.service;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 *
 */
@Data
@NoArgsConstructor
public class OfcQueryContractBo implements Serializable {

    private static final long serialVersionUID = 2154457965197654988L;
    // 姓名
    private String name;

    // 联系电话
    private String mobile;

    // 证件类型
    private int idCardType;

    // 证件号码
    private String idCardNo;

    // 类型 5：结算单 6：租车单
    private int type;

    // 当前日期
    private String todayDate;

    // 订单信息
    private OrderBaseInfo orderBaseInfo;

    // 押金信息
    private DepositBaseInfo depositBaseInfo;

    // 费用信息
    private List<OrderFeesBaseInfo> orderFeesBaseInfos;
}
