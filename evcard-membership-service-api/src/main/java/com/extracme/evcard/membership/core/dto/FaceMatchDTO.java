package com.extracme.evcard.membership.core.dto;

import lombok.Data;

@Data
public class FaceMatchDTO {


    /**
     * 图片信息(总数据大小应小于10M)，图片上传方式根据image_type来判断。 两张图片通过json格式上传，格式参考表格下方示例
     */
    private String image;

    /**
     * 图片类型
     * 0: BASE64:图片的base64值，base64编码后的图片数据，编码后的图片大小不超过2M；
     * 1: URL:图片的 URL地址( 可能由于网络等原因导致下载图片时间过长)；
     * 2: FACE_TOKEN: 人脸图片的唯一标识，调用人脸检测接口时，会为每个人脸图片赋予一个唯一的FACE_TOKEN，同一张图片多次检测得到的FACE_TOKEN是同一个。
     */
    private Integer imageType;



    /**
     * 图片信息(总数据大小应小于10M)，图片上传方式根据image_type来判断。 两张图片通过json格式上传，格式参考表格下方示例
     */
    private String registerImage;

    /**
     * 图片类型
     * 0: BASE64:图片的base64值，base64编码后的图片数据，编码后的图片大小不超过2M；
     * 1: URL:图片的 URL地址( 可能由于网络等原因导致下载图片时间过长)；
     * 2: FACE_TOKEN: 人脸图片的唯一标识，调用人脸检测接口时，会为每个人脸图片赋予一个唯一的FACE_TOKEN，同一张图片多次检测得到的FACE_TOKEN是同一个。
     */
    private Integer registerImageType;

}
