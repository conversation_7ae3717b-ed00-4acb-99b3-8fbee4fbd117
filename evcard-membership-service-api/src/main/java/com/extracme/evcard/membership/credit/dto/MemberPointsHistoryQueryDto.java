package com.extracme.evcard.membership.credit.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/8/12
 */
@Data
public class MemberPointsHistoryQueryDto implements Serializable {
    /**
     * 会员id
     */
    private String authId;

    /**
     * 积分类别: 缺省为01标准积分
     */
    private String pointsType;

    /**
     * 查询时间范围：开始时间yyyyMMddHHmmss
     */
    private String startDate;

    /**
     * 查询时间范围：结束时间yyyyMMddHHmmss
     */
    private String endDate;

    /**
     * 积分变动类别 1消费 2获取
     */
    private String optionType;

    /**
     * 1过期 2不过期 缺省则查询全部
     */
    private Short expireType;

    /**
     * 页码，默认1
     */
    private Integer pageNum;

    /**
     * 页面大小，默认10
     */
    private Integer pageSize;
}
