package com.extracme.evcard.membership.core.input;

import lombok.Data;

import java.io.Serializable;

@Data
public class ThirdLoginInput implements Serializable {

    private static final long serialVersionUID = -4459268753718403239L;
    /**
     * 手机号码
     */
    private String mobilePhone;

    /**
     * 身份证号码
     */
    private String idCardNo;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 注册城市
     */
    private String cityId;

    /**
     * 来源
     */
    private String appKey;

    /**
     * 是否需要token
     */
    private boolean needToken;

    /**
     * 支付宝车生活 openid
     */
    private String openId;

    /**
     * 支付宝车生活 zfbUserId
     */
    private String zfbUserId;

    /**
     * 二级渠道 secondAppKey
     */
    private String secondAppKey;
    /**
     * 证件类型 1:居民身份证 2:外籍护照 3:港澳通行证 4:台湾通行证 5:军人身份证
      */
    private int identityType;

    /** 会员条款版本号 */
    private String membershipPolicyVersion;
    /** 隐私政策版本号 */
    private String privacyPolicyVersion;

    /**是否同意条款*/
    private boolean agreePolicy;
}
