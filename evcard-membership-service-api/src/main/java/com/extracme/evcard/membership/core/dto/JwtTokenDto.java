package com.extracme.evcard.membership.core.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * JWT Token DTO
 * 
 * <AUTHOR>
 * @since 2025-08-20
 */
@Data
public class JwtTokenDto implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 访问令牌
     */
    private String accessToken;
    
    /**
     * 刷新令牌
     */
    private String refreshToken;
    
    /**
     * 访问令牌过期时间
     */
    private Date accessTokenExpireTime;
    
    /**
     * 刷新令牌过期时间
     */
    private Date refreshTokenExpireTime;
    
    /**
     * 令牌类型，通常为 "Bearer"
     */
    private String tokenType = "Bearer";
    
    /**
     * 访问令牌有效期（秒）
     */
    private Long expiresIn;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户MID
     */
    private String mid;
    
    /**
     * 应用标识
     */
    private String appKey;
    
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 设备类型
     */
    private String deviceType;
}
