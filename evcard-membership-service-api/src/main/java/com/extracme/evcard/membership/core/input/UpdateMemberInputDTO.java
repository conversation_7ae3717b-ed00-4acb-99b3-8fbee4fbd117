package com.extracme.evcard.membership.core.input;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2019/8/23
 */
@Data
public class UpdateMemberInputDTO implements Serializable{
    private static final long serialVersionUID = 8228408324375955947L;


    /** 会员id */
    private String authId;
    /** 会员类型 */
    private Integer memberType;

    /** 机构Id */
    private String orgId;

    /**区域Id */
    private Long regionId;

    /** 区/县  */
    private String area;

    /** 市  */
    private String city;

    /** 省  */
    private String province;

    /** 手机号 */
    private String mobilePhone;

    /** 人员状态  0：正常  1：休息 2：离职 */
    private Integer personnelState;

    /** 姓名 */
    private String name;

    /** 邮件 */
    private String email;

    /** 卡号  (注销传空串,不要传null)*/
    private String cardNo;

    /** 更新时间 yyyyMMddHHmmssSSS */
    private String updateTime;

    /** 更新人 */
    private String updateUser;

    /**
     * 驾驶证号
     */
    private String driverCode;
    public UpdateMemberInputDTO(String authId, Integer memberType) {
        this.authId = authId;
        this.memberType = memberType;
    }
}
