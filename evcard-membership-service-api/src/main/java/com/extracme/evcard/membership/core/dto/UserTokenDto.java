package com.extracme.evcard.membership.core.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class UserTokenDto implements Serializable {
    private Long id;


    private Long userId;

    private String appKey;


    private String token;


    private Date expiresTime;


    private Date startTime;


    private Date createTime;


    private Long createOperId;


    private String createOperName;


    private Date updateTime;


    private Long updateOperId;

    private String updateOperName;


    private Integer isDeleted;
}
