package com.extracme.evcard.membership.core.dto;

import com.extracme.evcard.rpc.dto.BaseResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


@Data
@EqualsAndHashCode(callSuper = true)
public class DriverLicenseOcrRespDto extends BaseResponse implements Serializable {
    /**
     * 驾照编号
     */
    private String driverCode;

    /**
     * 准驾车型
     */
    private String driveType;
    /**
     * 姓名
     */
    private String name;

    /**
     * 档案编号（副页）
     */
    private String fileNo;

    /**
     * 初次领证时间 yyyy-MM-dd
     */
    private String firstObtainTime;

    /**
     * 证件到期时间类别： 1非长期  2长期
     */
    private int expireType;
    /**
     * 证件到期日期，yyyy-MM-dd (长期时，不返回)
     */
    private String expirationDate;

    /**
     * normal/copy/temporary/screen/ps/unknown
     */
    private String riskType;

    /**
     * 若识别到为非法账号，则给予此提示
     */
    private String riskTypeDesc;

    /**
     * 1:成功 2:识别失败 3:不支持复印件 4:不支持翻拍 5:不支持PS  6其他
     */
    private int state = 1;

    /**
     * 签发机构
     */
    private String issueBy;

    /**
     * 有效期开始日期，格式yyyyMMdd
     */
    private String startDate;
    /**
     * 出生日期，格式yyyyMMdd
     */
    private String birthday;
    /**
     * 地址
     */
    private String address;
    /**
     * 性别
     */
    private String gender;
    /**
     * 民族
     */
    private String national;

    public void setCodeMessage(int code, String message) {
        super.setCode(code);
        super.setMessage(message);
    }
}
