package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class CardPauseQueryDto implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -1662654368607839225L;

	private String pauseReason;

	private Integer pauseStatus;

	private Long recoverTime;

	private String createdUser;

	private String createdTime;

	public String getPauseReason() {
		return pauseReason;
	}

	public void setPauseReason(String pauseReason) {
		this.pauseReason = pauseReason;
	}

	public Integer getPauseStatus() {
		return pauseStatus;
	}

	public void setPauseStatus(Integer pauseStatus) {
		this.pauseStatus = pauseStatus;
	}

	public Long getRecoverTime() {
		return recoverTime;
	}

	public void setRecoverTime(Long recoverTime) {
		this.recoverTime = recoverTime;
	}

	public String getCreatedUser() {
		return createdUser;
	}

	public void setCreatedUser(String createdUser) {
		this.createdUser = createdUser;
	}

	public String getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(String createdTime) {
		this.createdTime = createdTime;
	}

}
