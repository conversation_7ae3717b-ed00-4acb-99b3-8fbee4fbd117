package com.extracme.evcard.membership.core.dto;

import com.extracme.evcard.rpc.dto.BaseResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/8/30
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class JudgeIfCanTradeResp extends BaseResponse implements Serializable {
    /**
     * 0-不可交易、1-可交易
     */
    private int state;
}
