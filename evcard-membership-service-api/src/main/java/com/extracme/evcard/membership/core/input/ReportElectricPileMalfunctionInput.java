package com.extracme.evcard.membership.core.input;

import java.io.Serializable;

/**
 * <p>
 * <p> 上报充电桩故障
 * </p>
 *
 * <AUTHOR>
 * @Date 2018/9/11 15:42
 */
public class ReportElectricPileMalfunctionInput extends ReportFeedbackInput implements Serializable {

    private static final long serialVersionUID = -1548895996263006592L;

    /**
     * 网点编号
     */
    private Integer shopSeq;

    /**
     * 网点名称
     */
    private String shopName;

    /**
     * 车位号
     */
    private String parkNum;

    public Integer getShopSeq() {
        return shopSeq;
    }

    public void setShopSeq(Integer shopSeq) {
        this.shopSeq = shopSeq;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getParkNum() {
        return parkNum;
    }

    public void setParkNum(String parkNum) {
        this.parkNum = parkNum;
    }
}
