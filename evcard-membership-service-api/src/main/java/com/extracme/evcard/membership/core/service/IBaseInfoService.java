package com.extracme.evcard.membership.core.service;

import com.extracme.evcard.membership.core.dto.*;
import com.extracme.evcard.membership.core.dto.input.AgencyInfoInput;
import com.extracme.evcard.membership.core.exception.AgencyException;
import com.extracme.evcard.membership.core.input.QueryAgencyInfoBlackConditionInput;
import com.extracme.evcard.membership.core.input.QueryAgencyInfoInput;
import com.extracme.evcard.membership.core.input.QueryAgencyListConditionInput;
import com.extracme.evcard.membership.core.input.QueryAgencyOperationLogInput;
import com.extracme.evcard.rpc.dto.PageBeanDto;

import java.util.List;

/**
 * <p>
 * 基础信息服务（企业 、 机构 、省市区、字典数据等等）
 * </p>
 *
 * <AUTHOR>
 * @date 2019/3/7
 */
public interface IBaseInfoService {

    /**
     * 根据orgId查询所属公司信息
     * @param orgId
     * @return
     * @auhtor zhangrenhua
     */
    OrgInfoDto queryOrgInfoByOrgId(String orgId);


    /**
     * 根据agencyId查询关联企业信息
     * @param agencyId
     * @return
     * @auhtor zhangrenhua
     */
    AgencyInfoDto queryAgencyInfoByAgencyId(String agencyId);


    /**
     * 新增租赁机构信息
     * @param agencyInfoInput
     * @return
     * <AUTHOR>
     */
    void createAgencyInfo(AgencyInfoInput agencyInfoInput) throws AgencyException;


    /**
     * 新增租赁机构信息(新建就是合作状态)
     * 简版系统使用
     * @param agencyInfoInput
     * @return
     * <AUTHOR>
     */
    void createAgencyInfoAndBegin(AgencyInfoInput agencyInfoInput) throws AgencyException;


    /**
     * 修改租赁机构信息
     * @param agencyInfoInput
     * <AUTHOR>
     */
    void modifyAgencyInfo(AgencyInfoInput agencyInfoInput) throws AgencyException;


    /**
     * 暂停/恢复 机构合作状态
     * @param agencyId
     * @param isStop true 暂停 false 恢复
     * @param updatedUser
     * @param updatedTime
     * @return
     */
    void modifyAgencyCooperationStatus(String agencyId,boolean isStop,String updatedUser,String updatedTime) throws AgencyException;


    /**
     * 查询租赁机构信息列表(分页)
     * @param queryAgencyInfoInput
     * @return
     * @auhtor zhangrenhua
     */
    PageBeanDto<AgencyInfoDto> queryAgencyInfoPage(QueryAgencyInfoInput queryAgencyInfoInput);

    /**
     * 查询租赁机构操作日志(分页)
     * @param queryAgencyOperationLogInput
     * @return
     */
    PageBeanDto<MmpOperationLogDTO> queryAgencyOperationLog(QueryAgencyOperationLogInput queryAgencyOperationLogInput);


    /**
     *  根据条件查询机构信息列表
     *   供调度系统使用
     * @param queryAgencyListConditionInput
     * @return list
     * <AUTHOR>
     * @since 2019/10/29
     */
    List<AgencyInfoDto> queryAgencyListByCondition(QueryAgencyListConditionInput queryAgencyListConditionInput);


    /**
     * 通过agencyId查询黑名单机构信息
     *  供调度系统使用
     * @param agencyId
     * @return
     */
    AgencyInfoBlackDTO queryAgencyInfoBlackById(String agencyId);


    /**
     * 通过agencyId删除黑名单机构信息
     *  供调度系统使用
     * @param agencyId
     * @return
     */
    boolean deleteAgencyInfoBlackById(String agencyId);

    /**
     * 插入黑名单机构信息
     *  供调度系统使用
     * @param agencyInfoBlackDTO
     * @return
     */
    boolean insertAgencyInfoBlack(AgencyInfoBlackDTO agencyInfoBlackDTO);

    /**
     * 通过条件查询黑名单机构信息
     * 供调度系统使用
     * @param queryAgencyInfoBlackConditionInput
     * @return
     */
    List<AgencyInfoBlackDTO> queryAgencyInfoBlackByCondition(QueryAgencyInfoBlackConditionInput queryAgencyInfoBlackConditionInput);


    /**
     * 通过appKey 查询
     * 供调度系统使用
     * @param appKey
     * @return
     */
    AppKeyManagerDTO queryAppKeyManagerByAppKey(String appKey);
}
