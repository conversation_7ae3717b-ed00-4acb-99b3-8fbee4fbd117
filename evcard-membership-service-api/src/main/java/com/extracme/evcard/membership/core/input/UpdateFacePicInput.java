package com.extracme.evcard.membership.core.input;

import java.io.File;
import java.io.Serializable;

/**
 * @ClassName: UpdateFacePicInput
 * @Author: wudi
 * @Date: 2019/8/15 19:00
 */
public class UpdateFacePicInput implements Serializable {
    private static final long serialVersionUID = 7028760792987971576L;

    private String authId;

    //图片文件
    private byte[] file;

    //人脸图片
    private String faceRecognitionPic;

    //参数
    private String liveDelta;

    //用于写日志
    private String appkey;

    private String refKey2;

    private Boolean openDoor;

    private Integer faceOrigin;

    public Integer getFaceOrigin() {
        return faceOrigin;
    }

    public void setFaceOrigin(Integer faceOrigin) {
        this.faceOrigin = faceOrigin;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public byte[] getFile() {
        return file;
    }

    public void setFile(byte[] file) {
        this.file = file;
    }

    public String getFaceRecognitionPic() {
        return faceRecognitionPic;
    }

    public void setFaceRecognitionPic(String faceRecognitionPic) {
        this.faceRecognitionPic = faceRecognitionPic;
    }

    public String getLiveDelta() {
        return liveDelta;
    }

    public void setLiveDelta(String liveDelta) {
        this.liveDelta = liveDelta;
    }

    public String getAppkey() {
        return appkey;
    }

    public void setAppkey(String appkey) {
        this.appkey = appkey;
    }

    public String getRefKey2() {
        return refKey2;
    }

    public void setRefKey2(String refKey2) {
        this.refKey2 = refKey2;
    }

    public Boolean getOpenDoor() {
        return openDoor;
    }

    public void setOpenDoor(Boolean openDoor) {
        this.openDoor = openDoor;
    }
}
