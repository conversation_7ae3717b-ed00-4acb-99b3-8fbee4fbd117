package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2019/4/24
 */
public class FindPasswordDto implements Serializable {
    private static final long serialVersionUID = -4091562925106430310L;

    private String mobilePhone;

    private String verifyCode;

    private String newPassword;

    private String orgId;

    private UpdateUserDto updateUserDto;

    private String imeiNo;


    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getVerifyCode() {
        return verifyCode;
    }

    public void setVerifyCode(String verifyCode) {
        this.verifyCode = verifyCode;
    }

    public String getNewPassword() {
        return newPassword;
    }

    public void setNewPassword(String newPassword) {
        this.newPassword = newPassword;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public UpdateUserDto getUpdateUserDto() {
        return updateUserDto;
    }

    public void setUpdateUserDto(UpdateUserDto updateUserDto) {
        this.updateUserDto = updateUserDto;
    }

    public String getImeiNo() {
        return imeiNo;
    }

    public void setImeiNo(String imeiNo) {
        this.imeiNo = imeiNo;
    }
}
