package com.extracme.evcard.membership.core.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 会员审核日志详情对象
 *
 */
@Data
public class MemberAuthLogDTO implements Serializable {

    /**
     *  日志类型
     */
    private Integer logType;

    /**
     * 操作类型
     */
    private String operateType;
    /**
     * 来源
     */
    private String supplier;
    /**
     * 操作详情
     */
    private String details;

    /**
     * 操作时间
     */
    private String operateTime;
    /**
     * 操作人姓名
     */
    private String operatorName;
    /**
     * 操作人公司
     */
    private String operatorOrgName;

    /**
     * 操作人id
     *
     */
    private  Long operatorId;
}
