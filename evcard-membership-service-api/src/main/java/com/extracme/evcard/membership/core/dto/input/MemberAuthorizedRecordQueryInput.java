package com.extracme.evcard.membership.core.dto.input;

import com.extracme.evcard.rpc.dto.Page;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/1/10
 */
@Data
public class MemberAuthorizedRecordQueryInput implements Serializable {
    /**
     * 会员id
     * 必填
     */
    private String authId;

    /**
     * 操作类别列表
     * 授权操作类别：0 芝麻授权 1 代扣授权 2 取消代扣
     * 非必填
     */
    private List<Integer> authorizedTypes;

    /**
     * 操作结果：0 失败 1 成功
     * 非必填
     */
    private Integer authorizedResult;

    /**
     * 分页参数
     */
    private Page page;
}
