package com.extracme.evcard.membership.core.dto.agency;

import com.extracme.evcard.membership.core.dto.OperatorDto;
import lombok.Data;

import java.io.Serializable;

/**
 * 创建二级渠道与代理商关系请求参数
 */
@Data
public class AddAgencySecondAppKeyRelationDTO implements Serializable {
    private static final long serialVersionUID = -3489471817503021497L;

    /**
     * 企业ID
     */
    private String agencyId;

    /**
     * 二级渠道AppKey
     */
    private String secondAppKey;

    /**
     * 规则ID
     */
    private Long agencyRoleId;


    /**
     * 操作人信息
     */
    private OperatorDto operator;
}
