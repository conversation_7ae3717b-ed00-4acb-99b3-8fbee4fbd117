package com.extracme.evcard.membership.core.enums;

/**
 * Token类型枚举
 * 
 * <AUTHOR>
 * @since 2025-08-20
 */
public enum TokenTypeEnum {
    
    /**
     * 访问令牌
     */
    ACCESS_TOKEN("ACCESS_TOKEN", "访问令牌"),
    
    /**
     * 刷新令牌
     */
    REFRESH_TOKEN("REFRESH_TOKEN", "刷新令牌");
    
    private final String code;
    private final String description;
    
    TokenTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    public static TokenTypeEnum fromCode(String code) {
        for (TokenTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown token type code: " + code);
    }
}
