package com.extracme.evcard.membership.core.exception;

import com.extracme.evcard.rpc.enums.StatusCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2018/2/2
 */
public class MemberException extends RuntimeException{
    private static final long serialVersionUID = 5100882953811575191L;

    private int code;

    private String message;

    public MemberException(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public MemberException() {
    }

    public MemberException(StatusCode statusCode){
        this.code = statusCode.getCode();
        this.message = statusCode.getMsg();
    }

    public MemberException(String message, int code, String message1) {
        super(message);
        this.code = code;
        this.message = message1;
    }

    public MemberException(String message, Throwable cause, int code, String message1) {
        super(message, cause);
        this.code = code;
        this.message = message1;
    }

    public MemberException(Throwable cause, int code, String message) {
        super(cause);
        this.code = code;
        this.message = message;
    }

    public MemberException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace, int code, String message1) {
        super(message, cause, enableSuppression, writableStackTrace);
        this.code = code;
        this.message = message1;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return message;
    }


    public void setMessage(String message) {
        this.message = message;
    }
}
