package com.extracme.evcard.membership.core.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 升级记录
 * <AUTHOR>
 * @date 2021/4/21
 * @remark
 */
@Data
public class UserTitleUpgradeDto implements Serializable {
    /**
     * 发放时，必须
     * 查询时，非必须
     */
    private Long id;

    /**
     * 原称号
     */
    private UserTitleDto originTitle;
    /**
     * 新称号
     */
    private UserTitleDto newTitle;

    /**
     * 授予时间
     */
    private Date issueTime;
}
