package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR> chennian.
 * @Date ：Created in 10:47 2021/2/1
 */
public class UserInstantActivityRecordDto implements Serializable {

    /**
     * 记录id
     */
    private Long id;

    /**
     * 用户id
     */
    private String authId;

    /**
     * 订单号
     */
    private String orderSeq;

    /**
     * 立减活动id
     */
    private Long reduceAmountActivityId;

    /**
     * 订单立减金额
     */
    private BigDecimal orderReduceAmount;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public String getOrderSeq() {
        return orderSeq;
    }

    public void setOrderSeq(String orderSeq) {
        this.orderSeq = orderSeq;
    }

    public Long getReduceAmountActivityId() {
        return reduceAmountActivityId;
    }

    public void setReduceAmountActivityId(Long reduceAmountActivityId) {
        this.reduceAmountActivityId = reduceAmountActivityId;
    }

    public BigDecimal getOrderReduceAmount() {
        return orderReduceAmount;
    }

    public void setOrderReduceAmount(BigDecimal orderReduceAmount) {
        this.orderReduceAmount = orderReduceAmount;
    }
}
