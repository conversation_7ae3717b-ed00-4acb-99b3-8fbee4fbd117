package com.extracme.evcard.membership.core.input;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <p>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @Date 2019/10/17 14:48
 */
@Data
public class SubmitFaceInfoInput implements Serializable {

    private static final long serialVersionUID = 3288958346348995993L;

    /**
     * 用户authId
     */
    private String authId;

    /**
     * 人脸图片url
     */
    private String faceRecognitionPic;

    /**
     * appKey
     * 用于写日志
     */
    private String appKey;

    /**
     * 供应商名称
     * 用于写日志
     */
    private String refKey2;

    /**
     * 认证结果
     * 0成功 1失败 2身份证号和姓名是正常的，但公安那边的数据源没有他的照片
     */
    private String faceAuthenticationResult;

    /**
     * 相似度
     */
    private  String faceAuthenticationSimilarity;

    /**
     * 请求来源
     * 0  ：其它，非履约
     * 1 ：履约
     *
     */
    private int origin = 0;

    /**
     * 强制刷脸成功
     * 0  ：不强制
     * 1  ：强制
     */
    private int forceSuccess = 0;

    /**
     *  用户类型
     */
    private int membershipType = 0;
}
