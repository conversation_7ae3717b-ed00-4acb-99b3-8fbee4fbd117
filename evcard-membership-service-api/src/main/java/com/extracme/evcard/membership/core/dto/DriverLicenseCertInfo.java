package com.extracme.evcard.membership.core.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class DriverLicenseCertInfo implements Serializable {
    /**
     * 用户id
     */
    private String mid;
    /**
     * 提交渠道key
     */
    private String appKey;
    /**
     * 驾照号
     */
    private String driverCode;
    /**
     * 姓名
     */
    private String name;

    /**
     * 驾照类型 1:电子驾照  2:纸质驾照
     */
    private int driverLicenseType;
    /**
     * 驾照正面图片/电子驾照图片(完整路径)
     */
    private String driverLicenseImgUrl;
    /**
     * 驾照反面图片(完整路径)
     */
    private String reverseDriverLicenseImgUrl;
    /**
     * 准驾车型 C1、C2等
     */
    private String licenseType;
    /**
     * 初次领证日期 yyyy-MM-dd
     */
    private String firstObtainTime;
    /**
     * 证件时间类型 1：非长期 2：长期
     */
    private int expireType;
    /**
     * 过期时间 yyyy-MM-dd (长期时为null)
     */
    private String expirationDate;
    /**
     * 档案编号
     */
    private String fileNo;

    /**
     * APP提交审核时间，格式 yyyyMMddhhmmss
     */
    private String appReviewTime;

    /**
     * 证件审核人
     */
    private String reviewUser;

    /**
     * 证件审核时间，格式 yyyyMMddhhmmss
     */
    private String reviewTime;

    /**
     * 证件审核不通过项：
     * 共12位，每一位：0未审核 1：审核通过 2：审核不通过
     * 从左往右： 姓名 、驾照号、驾驶证照片、准驾车型、初次领证日期、到期时间 、邮寄地址(废弃)、
     * 身份证/护照 、手持身份证/护照、驾驶证档案编号、身份证件编号、人脸照片
     */
    private String reviewItems;

    /**
     * 审核不通过概要编号, 多个以逗号分隔
     * 多个以英文逗号拼接，如2,6,3
     * 分号分隔(1.姓名不正确 2.驾驶证号不正确 3.驾驶证照片不正确
     * 4. 准驾车型不正确 5.初次领证日期不正确 6.驾照到期日期不正确 7.邮寄地址不正确
     * 8.身份证件照片不正确 9.手持身份证件照片不正确 A.驾驶证档案编号不正确
     * B.身份证件编号不正确 C.人脸照片不正确 0.用户意愿
     */
    private String reviewIds;

    /**
     * 证件审核不通原因明细：
     * 工作人员选择的详细不通过项，与review_remark一起作为最终不通过原因
     */
    private String reviewItemName;

    /**
     * 不通过原因
     */
    private String reviewRemark;

    /**
     * 审核方式  1手动审核  2自动审核
     */
    private Integer reviewMode;

    /**
     * 驾照认证状态  1:未认证、2:待认证、3:已认证、4:认证失败
     */
    private Integer licenseReviewStatus;

    /**
     * 状态汇总处理：1:未认证、2:待认证、3:已认证、4:认证失败、5:即将过期（剩余90天）、6:已过期
     */
    private int state;

    /**
     * 审核项汇总处理
     * 姓名、准驾车型、驾照编号 、初次领证日期、过期日期、档案编号、驾照照片(不区分正副页)
     */
    private String auditIsError;
}
