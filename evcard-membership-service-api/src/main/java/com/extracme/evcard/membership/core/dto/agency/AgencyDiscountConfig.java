package com.extracme.evcard.membership.core.dto.agency;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/4/1
 */
@Data
public class AgencyDiscountConfig implements Serializable {

    private String agencyId;

    /**
     * 机构名称（当前名称）
     * @remark 查询历史折扣配置时无此字段(履历表中未记录)
     */
    private String agencyName;

    /**
     * 企业合作开始时间
     */
    private Date cooperateStartTime;
    /**
     * 企业合作结束时间（目前此字段无值）
     */
    private Date cooperateEndTime;
    /**
     * 企业合作状态：0-未开始；1-合作中；2-已暂停
     */
    private Integer cooperateStatus;
    /**
     * 企业合作状态（旧）：0-暂停中，1-合作中，2-未开始
     */
    private Integer status;

    /**
     * 折扣规则（1：取还车时间点 2：用车时长分段）
     * 广铁使用
     */
    private Integer discountRule;

    /**
     * 当前最新的企业折扣id
     */
    private Long discountId;

    /**
     * 当前最新的个人折扣id
     */
    private Long discountPersonalId;

    /**
     * 车牌限制： 限制仅此列表中的车牌可用，若为空，则全部不可用。
     */
    private String vehicleNo;

    /**
     * 不同享套餐id列表
     */
    private List<String> packageTypeList;
}
