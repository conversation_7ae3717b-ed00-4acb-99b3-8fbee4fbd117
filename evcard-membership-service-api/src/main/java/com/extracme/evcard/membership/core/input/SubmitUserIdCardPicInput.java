package com.extracme.evcard.membership.core.input;

import java.io.Serializable;

/**
 * @ClassName: SubmitUserIdCardPicInput
 * @Author: wudi
 * @Date: 2019/8/14 14:03
 */
public class SubmitUserIdCardPicInput implements Serializable {
    private static final long serialVersionUID = -4275732922961160486L;

    private String holdIdcardPicUrl;

    private String idcardPicUrl;

    private Integer idType;

    private String passportNo;

    private String facePicUrl;

    private String authId;

    private String appKey;

    public String getHoldIdcardPicUrl() {
        return holdIdcardPicUrl;
    }

    public void setHoldIdcardPicUrl(String holdIdcardPicUrl) {
        this.holdIdcardPicUrl = holdIdcardPicUrl;
    }

    public String getIdcardPicUrl() {
        return idcardPicUrl;
    }

    public void setIdcardPicUrl(String idcardPicUrl) {
        this.idcardPicUrl = idcardPicUrl;
    }

    public Integer getIdType() {
        return idType;
    }

    public void setIdType(Integer idType) {
        this.idType = idType;
    }

    public String getPassportNo() {
        return passportNo;
    }

    public void setPassportNo(String passportNo) {
        this.passportNo = passportNo;
    }

    public String getFacePicUrl() {
        return facePicUrl;
    }

    public void setFacePicUrl(String facePicUrl) {
        this.facePicUrl = facePicUrl;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }
}
