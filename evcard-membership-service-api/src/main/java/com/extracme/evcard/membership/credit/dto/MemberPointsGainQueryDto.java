package com.extracme.evcard.membership.credit.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/12/8
 */
@Data
public class MemberPointsGainQueryDto implements Serializable {
    /**
     * 会员id
     */
    private String authId;
    /**
     * 积分类别: 缺省为01标准积分
     */
    private String pointsType;

    /**
     * 事件类型:
     * 1身份认证通过  3签到成功
     * 10订单评价 15补全邮箱 16补全邮寄地址 17补全学历 18补全职业 19补全有无私家车
     * 14.成就任务   ...
     * 参考 enum MemPointsPushEnum
     */
    private Integer feeType;

    /**
     * 事件关联键， 非必传
     * 碳减排订单分享事件必传： 订单编号
     */
    private String eventRefSeq;

    /**
     * 输入积分，非必传
     * feeType  对于25/26订单取还车评价积分：   阶梯积分 分别传 1(第一档) 2(第二档)
     */
    private Integer gainCredits;

}
