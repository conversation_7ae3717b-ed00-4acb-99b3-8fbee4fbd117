package com.extracme.evcard.membership.core.dto;

import com.extracme.evcard.rpc.dto.BaseResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OcrAndSubmitLicenseRespDto extends BaseResponse implements Serializable {

    private static final long serialVersionUID = 8782369950570654062L;

    /**
     * 提交结果
     * 1:成功
     * 2:操作频繁
     * 3:驾照已被上个注销账号使用
     * 4:驾照号错误
     * 5:驾照已过期
     * 6:驾照类型不支持租车
     * 7:驾照号与身份证号不一致
     * 8:有效期不在原有效期之后(更新时才有)
     * 9:驾照已被提交认证
     * 10：驾照到期时间格式不正确
     * 11：该身份证号的驾照已被其他账号认证
     * 15：驾照与身份证号码不一致
     * 16：驾照与身份证姓名不一致
     * 17:ocr失败
     */
    private int state;

    /**
     * 参数
     */
    private String msg;

    // 证件信息

    /**
     * 驾照编号
     */
    private String driverCode;

    /**
     * 准驾车型
     */
    private String driveType;
    /**
     * 姓名
     */
    private String name;

    /**
     * 档案编号（副页）
     */
    private String fileNo;

    /**
     * 初次领证时间 yyyy-MM-dd
     */
    private String firstObtainTime;

    /**
     * 证件到期时间类别： 1非长期  2长期
     */
    private int expireType;
    /**
     * 证件到期日期，yyyy-MM-dd (长期时，不返回)
     */
    private String expirationDate;

    /**
     * 签发机构
     */
    private String issueBy;

    /**
     * 有效期开始日期，格式yyyyMMdd
     */
    private String startDate;
    /**
     * 出生日期，格式yyyyMMdd
     */
    private String birthday;
    /**
     * 地址
     */
    private String address;
    /**
     * 性别
     */
    private String gender;
    /**
     * 民族
     */
    private String national;

    public OcrAndSubmitLicenseRespDto(int code, String message) {
        super(code, message);
    }

    public void setCodeMessage(int code, String message) {
        super.setCode(code);
        super.setMessage(message);
    }
}
