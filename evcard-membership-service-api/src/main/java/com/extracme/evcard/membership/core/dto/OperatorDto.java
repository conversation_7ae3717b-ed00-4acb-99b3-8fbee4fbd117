package com.extracme.evcard.membership.core.dto;

import lombok.Data;

import java.io.Serializable;


@Data
public class OperatorDto implements Serializable {
    /**
     * 操作人id
     */
    private Long operatorId;
    /**
     * 操作人姓名
     */
    private String operatorName;
    /**
     * 备注
     */
    private String remark;
    /**
     * 来源系统
     */
    private String originSystem;

    public static OperatorDto buildUserOperator(String mid){
        OperatorDto operator = new OperatorDto();
        operator.setOperatorId(-1L);
        operator.setOperatorName(mid);
        operator.setOriginSystem("USER");
        return operator;
    }

    public static OperatorDto buildUserOperator(String userName, String remark){
        OperatorDto operator = new OperatorDto();
        operator.setOperatorId(-1L);
        operator.setOperatorName(userName);
        operator.setRemark(remark);
        operator.setOriginSystem("USER");
        return operator;
    }

    public static OperatorDto buildSysOperator(String remark){
        return buildUserOperator("system", remark);
    }

    public static final OperatorDto SYSTEM = buildUserOperator("system");
}

