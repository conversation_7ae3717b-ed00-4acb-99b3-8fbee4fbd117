package com.extracme.evcard.membership.credit.dto;

import com.extracme.evcard.rpc.dto.BaseResponse;

/**
 * Created by Elin on 2017/11/28.
 * 事件类型分析查询参数
 */
public class CreditEventTypeReportParamsDto extends BaseResponse {

    private static final long serialVersionUID = -5607568446745345595L;

    /**
     * 年份
     */
    private String yearNum;

    /**
     * 所属公司id
     */
    private String orgId;

    /**
     * 查询类型 0-各事件类型次数 1-各事件类型会员数
     */
    private Integer type;

    public String getYearNum() {
        return yearNum;
    }

    public void setYearNum(String yearNum) {
        this.yearNum = yearNum;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}