package com.extracme.evcard.membership.core.input;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class QueryFeedbackInput implements Serializable {
    private static final long serialVersionUID = -8712922157046826708L;

    //姓名
    private String name;

    private String authId;

    //手机号
    private String mobilePhone;

    //处理状态 0 未处理 1 生成任务(添加处理) 2 不予处理 3.无需处理'
    private Integer reportStatus;

    //上报类型 0 网点推荐 1 车辆故障 2 充电桩故障 3 意见反馈' 不传为查看全部
    private List<Integer> types;

    private String vehicleNo;

    //开始时间
    private Date startTime;

    //截止时间
    private Date endTime;

    //分页每一页数量
    private Integer pageNum = 1;

    private Integer pageSize = 20;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public Integer getReportStatus() {
        return reportStatus;
    }

    public void setReportStatus(Integer reportStatus) {
        this.reportStatus = reportStatus;
    }

    public String getVehicleNo() {
        return vehicleNo;
    }

    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }

    public List<Integer> getTypes() {
        return types;
    }

    public void setTypes(List<Integer> types) {
        this.types = types;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }
}
