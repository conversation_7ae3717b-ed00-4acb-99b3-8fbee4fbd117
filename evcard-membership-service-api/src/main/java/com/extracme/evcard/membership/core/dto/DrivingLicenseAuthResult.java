package com.extracme.evcard.membership.core.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/4/27
 */
@Data
public class DrivingLicenseAuthResult implements Serializable {
    /**
     * 认证记录id
     */
    private Long id;

    /**
     * 认证状态 0待认证 1认证通过 2认证不通过 3查证中(待重新认证)
     */
    private Integer authenticateStatus;

    /**
     * 驾照状态 0待认证  1认证通过  2认证不通过（驾照有效期/准驾车型/驾照状态不符合用车条件）
     */
    private Integer licenseStatus;

    /**
     * 驾照状态描述
     */
    private String licenseStatusMsg;

    /**
     * elementsReviewItems: 驾照号/姓名/档案编号
     * 0不一致 1一致
     * 如:010 111
     */
    private String elementsReviewItems;
}
