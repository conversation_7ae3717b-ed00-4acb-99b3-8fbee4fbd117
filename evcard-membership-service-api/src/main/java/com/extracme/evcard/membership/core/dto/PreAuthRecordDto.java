package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;
import java.math.BigDecimal;

public class PreAuthRecordDto implements Serializable {

    /** 可用金额 */
    private BigDecimal amount = BigDecimal.ZERO;

    /** 有效期  */
    private String  termOfValidity;

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getTermOfValidity() {
        return termOfValidity;
    }

    public void setTermOfValidity(String termOfValidity) {
        this.termOfValidity = termOfValidity;
    }
}
