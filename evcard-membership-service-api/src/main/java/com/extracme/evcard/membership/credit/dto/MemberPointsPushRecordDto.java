package com.extracme.evcard.membership.credit.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/8/20
 */
@Data
public class MemberPointsPushRecordDto implements Serializable {
    /**
     * 积分推送事件记录id
     */
    private Long id;

    /**
     * 会员id
     */
    private String authId;

    /**
     * 事件类型
     */
    private Integer eventType;

    /**
     * 积分数量： 固定发放使用
     */
    private Integer pointsNum;

    /**
     * 事件关联id:
     * 订单支付事件为 订单号
     * e币充值为交易记录号
     */
    private String eventRefSeq;

    /**
     * 涉及金额：
     * 订单实付金额/押金充值金额/e币充值金额
     */
    private BigDecimal amount;

    /**
     * 事件详情
     */
    private String details;

    /**
     * 备注
     */
    private String remark;

    /**
     * 事件触发事件
     */
    private Date createTime;

    private Long createOperId;

    private String createOperName;
}
