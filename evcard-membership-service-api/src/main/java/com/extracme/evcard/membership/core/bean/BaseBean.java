package com.extracme.evcard.membership.core.bean;

import java.io.Serializable;

public class BaseBean implements Serializable{

    private static final long serialVersionUID = -6263656579232222962L;
    // 流水号
    private int serialNum;
    private int status;
    private String message = "";
    private String serviceName; // 调用服务名称

    private String token;

    public int getSerialNum() {
        return serialNum;
    }

    public void setSerialNum(int serialNum) {
        this.serialNum = serialNum;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }
}
