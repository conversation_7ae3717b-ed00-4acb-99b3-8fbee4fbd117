package com.extracme.evcard.membership.core.input;

import com.baosight.imap.rest.domain.LoginInfo;

import java.io.Serializable;

public class LoginInput extends LoginInfo implements Serializable{
    private static final long serialVersionUID = -3052006384704760377L;

    // 登录来源 0： app 1:门户网站 2:车门控制用户
    private int loginOrigin = 0;

    // 版本号
    private String version = "";

    // APP类型 0：android 1：ios
    private int appType = -1;

    // IMEI 编号
    private String imei;

    //token
    private String token;

    //	推送通道id
    private String channelId;

    /** 验证码 */
    private String verifyCode;

    //检查类型 0：检查 1：不检查 2：新版本不检查imei
    private Integer checkType;

    /**
     * 租户所属机构
     */
    private String orgId;

    //是否绑定imei 0：不绑定 1：绑定
    private Integer bindImei;

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public int getLoginOrigin() {
        return loginOrigin;
    }

    public void setLoginOrigin(int loginOrigin) {
        this.loginOrigin = loginOrigin;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public int getAppType() {
        return appType;
    }

    public void setAppType(int appType) {
        this.appType = appType;
    }

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getVerifyCode() {
        return verifyCode;
    }

    public void setVerifyCode(String verifyCode) {
        this.verifyCode = verifyCode;
    }

    public Integer getCheckType() {
        return checkType;
    }

    public void setCheckType(Integer checkType) {
        this.checkType = checkType;
    }

    public Integer getBindImei() {
        return bindImei;
    }

    public void setBindImei(Integer bindImei) {
        this.bindImei = bindImei;
    }
}
