package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName: DepositDetailDto
 * @Author: wudi
 * @Date: 2020/8/19 15:54
 */
public class DepositDetailDto implements Serializable {
    private static final long serialVersionUID = -9213311587981223681L;

    //金额
    private BigDecimal amount;

    //时间
    private String time;

    private String message;

    //1：免押 2：预授权 3：充值 4：扣除押金 5:芝麻免押
    private Integer type;

    /**
     * 账户类型	1:企业监管 2；个人监管
     */
    private Integer accountType;

    /**
     * 个人监管账户状态 0:休眠 1:正常 2、冻结
     */
    private Integer accountStatus;

    public DepositDetailDto() {
    }

    public DepositDetailDto(BigDecimal amount, String time, String message, Integer type) {
        this.amount = amount;
        this.time = time;
        this.message = message;
        this.type = type;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getAccountType() {
        return accountType;
    }

    public void setAccountType(Integer accountType) {
        this.accountType = accountType;
    }

    public Integer getAccountStatus() {
        return accountStatus;
    }

    public void setAccountStatus(Integer accountStatus) {
        this.accountStatus = accountStatus;
    }
}
