package com.extracme.evcard.membership.credit.dto;

import com.extracme.evcard.rpc.dto.BaseResponse;

import java.util.Date;

/**
 * Created by Elin on 2017/11/22.
 * 事件类型列表显示字段
 */
public class CreditEventTypePageDto extends BaseResponse {

    private static final long serialVersionUID = 2265161294585704195L;

    /**
     * 事件类型编号
     */
    private Long id;

    /**
     * 事件类型名称
     */
    private String eventName;

    /**
     * 事件类型性质 1-正面 0-负面
     */
    private String eventNature;

    /**
     * 分值
     */
    private Integer amount;

    /**
     * 触发方式 0-自动 1-手动
     */
    private Integer eventWay;
    /**
     * 是否直接加入黑名单 false-否 true-是
     */
    private Boolean blackList;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人id
     */
    private Long createOperId;

    /**
     * 创建人姓名
     */
    private String createOperName;

    /**
     * 显示状态，控制有些不需要修改的类型，如非法用车 、行业黑名单 0-不可修改 1-可作修改
     * 不用库中status字段，status可用作后续删除等状态控制
     * */
    private String typeStatus;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public String getEventNature() {
        return eventNature;
    }

    public void setEventNature(String eventNature) {
        this.eventNature = eventNature;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public Integer getEventWay() {
        return eventWay;
    }

    public void setEventWay(Integer eventWay) {
        this.eventWay = eventWay;
    }

    public Boolean getBlackList() {
        return blackList;
    }

    public void setBlackList(Boolean blackList) {
        this.blackList = blackList;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateOperId() {
        return createOperId;
    }

    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    public String getCreateOperName() {
        return createOperName;
    }

    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    public String getTypeStatus() {
        return typeStatus;
    }

    public void setTypeStatus(String typeStatus) {
        this.typeStatus = typeStatus;
    }
}
