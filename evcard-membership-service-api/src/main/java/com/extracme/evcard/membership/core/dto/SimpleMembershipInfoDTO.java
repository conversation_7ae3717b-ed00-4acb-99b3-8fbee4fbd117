package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class SimpleMembershipInfoDTO implements Serializable{

	private static final long serialVersionUID = -133465054131570252L;
	
	private String authId;

	/**
	 * 享道统一用户ID
	 */
	private String uid;
	
	private String name;

	private String mobilePhone;

	private String driverCode;

	private String orgName;

	// 一级渠道appkey
	private String firstAppKey;
	// 一级渠道 名称
	private String firstAppKeyName;
	// 2级渠道 appkey
	private String secondAppKey;
	// 2级渠道 名称
	private String secondAppKeyName;

	private String mid;

	public String getMid() {
		return mid;
	}

	public void setMid(String mid) {
		this.mid = mid;
	}

	public String getFirstAppKey() {
		return firstAppKey;
	}

	public void setFirstAppKey(String firstAppKey) {
		this.firstAppKey = firstAppKey;
	}

	public String getFirstAppKeyName() {
		return firstAppKeyName;
	}

	public void setFirstAppKeyName(String firstAppKeyName) {
		this.firstAppKeyName = firstAppKeyName;
	}

	public String getSecondAppKey() {
		return secondAppKey;
	}

	public void setSecondAppKey(String secondAppKey) {
		this.secondAppKey = secondAppKey;
	}

	public String getSecondAppKeyName() {
		return secondAppKeyName;
	}

	public void setSecondAppKeyName(String secondAppKeyName) {
		this.secondAppKeyName = secondAppKeyName;
	}

	public String getAuthId() {
		return authId;
	}

	public void setAuthId(String authId) {
		this.authId = authId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getMobilePhone() {
		return mobilePhone;
	}

	public void setMobilePhone(String mobilePhone) {
		this.mobilePhone = mobilePhone;
	}

	public String getDriverCode() {
		return driverCode;
	}

	public void setDriverCode(String driverCode) {
		this.driverCode = driverCode;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getUid() {
		return uid;
	}

	public void setUid(String uid) {
		this.uid = uid;
	}

	@Override
	public String toString() {
		return "SimpleMembershipInfoDTO{" +
				"authId='" + authId + '\'' +
				", name='" + name + '\'' +
				", mobilePhone='" + mobilePhone + '\'' +
				", driverCode='" + driverCode + '\'' +
				", orgName='" + orgName + '\'' +
				'}';
	}
}
