package com.extracme.evcard.membership.core.input;

import java.io.Serializable;

/**
 * <p>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @Date 2019/8/13 16:02
 */
public class SubmitDrivingLicenseInput implements Serializable {

    private static final long serialVersionUID = -7129194932809941024L;

    /** 驾照号 */
    private String driverCode;

    /** 初次领证日期 */
    private String  firstGetLicenseDate;

    /** 到期日期 */
    private String  expirationDate;

    /** 驾照类型 */
    private String drivingLicenseType;

    /** 国籍 */
    private String national;

    /** 驾照图片 */
    private String drivingLicenseImgUrl;

    /** 姓名 */
    private String name;

    /** 地址 */
    private String address;

    /** 输入类型   0 无 1 驾照ocr识别 2 手动输入 */
    private Integer driverLicenseInputType;

    /** 提交类型 0.提交审核 1.更新驾照到期时间 2.更新档案编号 */
    private Integer submitType = 0;

    /** 档案编号	 */
    private String fileNo;

    /** 驾照副页照片地址 */
    private String fileNoImgUrl;

    //军官身份证件号
    private String officerIdNumber;

    public String getDriverCode() {
        return driverCode;
    }

    public void setDriverCode(String driverCode) {
        this.driverCode = driverCode;
    }

    public String getFirstGetLicenseDate() {
        return firstGetLicenseDate;
    }

    public void setFirstGetLicenseDate(String firstGetLicenseDate) {
        this.firstGetLicenseDate = firstGetLicenseDate;
    }

    public String getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(String expirationDate) {
        this.expirationDate = expirationDate;
    }

    public String getDrivingLicenseType() {
        return drivingLicenseType;
    }

    public void setDrivingLicenseType(String drivingLicenseType) {
        this.drivingLicenseType = drivingLicenseType;
    }

    public String getNational() {
        return national;
    }

    public void setNational(String national) {
        this.national = national;
    }

    public String getDrivingLicenseImgUrl() {
        return drivingLicenseImgUrl;
    }

    public void setDrivingLicenseImgUrl(String drivingLicenseImgUrl) {
        this.drivingLicenseImgUrl = drivingLicenseImgUrl;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getDriverLicenseInputType() {
        return driverLicenseInputType;
    }

    public void setDriverLicenseInputType(Integer driverLicenseInputType) {
        this.driverLicenseInputType = driverLicenseInputType;
    }

    public Integer getSubmitType() {
        return submitType;
    }

    public void setSubmitType(Integer submitType) {
        this.submitType = submitType;
    }

    public String getFileNo() {
        return fileNo;
    }

    public void setFileNo(String fileNo) {
        this.fileNo = fileNo;
    }

    public String getFileNoImgUrl() {
        return fileNoImgUrl;
    }

    public void setFileNoImgUrl(String fileNoImgUrl) {
        this.fileNoImgUrl = fileNoImgUrl;
    }

    public String getOfficerIdNumber() {
        return officerIdNumber;
    }

    public void setOfficerIdNumber(String officerIdNumber) {
        this.officerIdNumber = officerIdNumber;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
}
