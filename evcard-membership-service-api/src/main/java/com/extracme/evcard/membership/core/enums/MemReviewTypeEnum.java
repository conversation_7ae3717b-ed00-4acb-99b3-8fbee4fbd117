package com.extracme.evcard.membership.core.enums;

public enum MemReviewTypeEnum {
    APP_AUTO_REVIEW(0L, "APP自动审核"),
    MANUAL_REVIEW(1L, "人工审核"),
    //后台自动变更审核状态
    SYSTEM_AUTO_REVIEW(2L, "后台自动审核"),
    //如因驾照过期，变更会员审核认证状态
    UPDATE_REVIEW_STATUS(3L, "变更会员审核认证状态"),
    //更新驾照有效期后，变更会员审核状态
    APP_AUTO_REVIEW_ONLY(4L, "更新驾照有效期，APP自动审核通过");

    MemReviewTypeEnum(Long code, String operate) {
        this.code = code;
        this.operate = operate;
    }

    private Long code;
    private String operate;

    public Long getCode() {
        return code;
    }

    public void setCode(Long code) {
        this.code = code;
    }

    public String getOperate() {
        return operate;
    }

    public void setOperate(String operate) {
        this.operate = operate;
    }
}
