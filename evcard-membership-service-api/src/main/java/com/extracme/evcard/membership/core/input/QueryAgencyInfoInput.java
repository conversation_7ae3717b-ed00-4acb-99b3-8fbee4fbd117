package com.extracme.evcard.membership.core.input;

import com.extracme.evcard.rpc.dto.Page;

import java.io.Serializable;

/**
 * <p>
 * 查询租赁机构信息输入
 * </p>
 *
 * <AUTHOR>
 * @date 2019/3/21
 */
public class QueryAgencyInfoInput implements Serializable{
    private static final long serialVersionUID = -346322751321210855L;

    /** 租赁机构*/
    private String agencyName;

    /** 运营公司ID */
    private String orgId;

    /** 合作状态：0-未开始；1-合作中；2-已暂停 */
    private Integer cooperateStatus;

    /** 分页信息 */
    private Page page;

    public String getAgencyName() {
        return agencyName;
    }

    public void setAgencyName(String agencyName) {
        this.agencyName = agencyName;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public Integer getCooperateStatus() {
        return cooperateStatus;
    }

    public void setCooperateStatus(Integer cooperateStatus) {
        this.cooperateStatus = cooperateStatus;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }

    @Override
    public String toString() {
        return "QueryAgencyInfoInput{" +
                "agencyName='" + agencyName + '\'' +
                ", orgId='" + orgId + '\'' +
                ", cooperateStatus=" + cooperateStatus +
                ", page=" + page +
                '}';
    }
}
