package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;

/**
 * 类名称：MembershipForMmpDTO
 * 类描述：新会员管理系统增修改会员DTO
 * 创建人：lidong
 */
public class MembershipForMmpDTO implements Serializable {
    /** 会员主键 **/
    private Long pkId;
    /** 会员id */
    private String authId;
    /** 姓名 */
    private String name;
    /** 手机号 */
    private String mobilePhone;
    /** 用户类型（0：外籍用户，1：内地用户，2：港澳台用户  3:大陆军人） */
    private String userType;

    private String userTypeName;

    /** 驾驶证号 */
    private String drivingLicense;
    /** 驾照扫描图片URL */
    private String drivingLicenseImgUrl;
    /** 身份证扫描图片URL */
    private String idcardPicUrl;
    /** 手持身份证照 */
    private String holdIdcardPicUrl;
    /** 省 */
    private String provinceOfOrigin;
    /** 市 */
    private String cityOfOrigin;
    /** 区 */
    private String areaOfOrigin;
    /** 邮寄地址 */
    private String address;
    /** 备注来源 */
    private String infoOrigin;
    /** 操作原因 */
    private String subReason;
    /** 会员状态（-1:资料不全 0:待审核 1：审核通过 2: 审核不通过 3：用户无效 4:重新审核） */
    private Integer reviewStatus;
    /** app提交审核时间 **/
    private String appReviewTime;

    String agencyId;

    /** 领取驾照时间 */
    private String obtainDriverTimer;
    /** 驾照到期时间 */
    private String licenseExpirationTime;
    /** 邮箱 */
    private String mail;
    /** 驾照类型 */
    private String drivingLicenseType;
    /** 是否勾选人脸识别（0：是，1：否） */
    private Integer isFlag;
    /** 认证状态*/
    private Integer authenticationStatus;
    /**人脸识别*/
    private String faceRecognitionImgUrl;

    /**驾驶证档案编号*/
    private String fileNo;
    /**驾驶证副页照片显示*/
    private String fileNoImgUrl;
    /**证件类型*/
    private String idType;
    /**证件编号*/
    private String passportNo;

    private String registerArea;
    private String national;
    private String authenticationStatusName;
    private String reviewStatusName;
    private Integer accountStatus;
    /**
     * 驾照认证状态
     */
    private Integer licAuthStatus;
    /**
     * 驾照认证状态
     */
    private Integer licenseElementsAuthStatus;

    /** 创建人ID */
    private String createOperId;
    /** 创建人姓名 */
    private String createOperName;

    /**
     * 身份证副页
     */
    private String idCardcBackPicUrl;

    /**
     * 证件到期日期
     */
    private String idCardExpirationTime;

    /**
     *  身份证 证件 有效期 是否长期  1：非长期 2：长期
     */
    private Integer idCardExpirationType;

    public Integer getIdCardExpirationType() {
        return idCardExpirationType;
    }

    public void setIdCardExpirationType(Integer idCardExpirationType) {
        this.idCardExpirationType = idCardExpirationType;
    }

    public String getIdCardcBackPicUrl() {
        return idCardcBackPicUrl;
    }

    public String getIdCardExpirationTime() {
        return idCardExpirationTime;
    }

    public void setIdCardExpirationTime(String idCardExpirationTime) {
        this.idCardExpirationTime = idCardExpirationTime;
    }

    public void setIdCardcBackPicUrl(String idCardcBackPicUrl) {
        this.idCardcBackPicUrl = idCardcBackPicUrl;
    }

    public String getFileNo() {
        return fileNo;
    }

    public void setFileNo(String fileNo) {
        this.fileNo = fileNo;
    }

    public String getFileNoImgUrl() {
        return fileNoImgUrl;
    }

    public void setFileNoImgUrl(String fileNoImgUrl) {
        this.fileNoImgUrl = fileNoImgUrl;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getPassportNo() {
        return passportNo;
    }

    public void setPassportNo(String passportNo) {
        this.passportNo = passportNo;
    }

    public Integer getIsFlag() {
        return isFlag;
    }

    public void setIsFlag(Integer isFlag) {
        this.isFlag = isFlag;
    }

    public String getObtainDriverTimer() {
        return obtainDriverTimer;
    }

    public void setObtainDriverTimer(String obtainDriverTimer) {
        this.obtainDriverTimer = obtainDriverTimer;
    }

    public String getLicenseExpirationTime() {
        return licenseExpirationTime;
    }

    public void setLicenseExpirationTime(String licenseExpirationTime) {
        this.licenseExpirationTime = licenseExpirationTime;
    }

    public String getMail() {
        return mail;
    }

    public void setMail(String mail) {
        this.mail = mail;
    }

    public String getDrivingLicenseType() {
        return drivingLicenseType;
    }

    public void setDrivingLicenseType(String drivingLicenseType) {
        this.drivingLicenseType = drivingLicenseType;
    }

    public Integer getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(Integer reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getDrivingLicense() {
        return drivingLicense;
    }

    public void setDrivingLicense(String drivingLicense) {
        this.drivingLicense = drivingLicense;
    }

    public String getDrivingLicenseImgUrl() {
        return drivingLicenseImgUrl;
    }

    public void setDrivingLicenseImgUrl(String drivingLicenseImgUrl) {
        this.drivingLicenseImgUrl = drivingLicenseImgUrl;
    }

    public String getIdcardPicUrl() {
        return idcardPicUrl;
    }

    public void setIdcardPicUrl(String idcardPicUrl) {
        this.idcardPicUrl = idcardPicUrl;
    }

    public String getHoldIdcardPicUrl() {
        return holdIdcardPicUrl;
    }

    public void setHoldIdcardPicUrl(String holdIdcardPicUrl) {
        this.holdIdcardPicUrl = holdIdcardPicUrl;
    }

    public String getProvinceOfOrigin() {
        return provinceOfOrigin;
    }

    public void setProvinceOfOrigin(String provinceOfOrigin) {
        this.provinceOfOrigin = provinceOfOrigin;
    }

    public String getCityOfOrigin() {
        return cityOfOrigin;
    }

    public void setCityOfOrigin(String cityOfOrigin) {
        this.cityOfOrigin = cityOfOrigin;
    }

    public String getAreaOfOrigin() {
        return areaOfOrigin;
    }

    public void setAreaOfOrigin(String areaOfOrigin) {
        this.areaOfOrigin = areaOfOrigin;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getInfoOrigin() {
        return infoOrigin;
    }

    public void setInfoOrigin(String infoOrigin) {
        this.infoOrigin = infoOrigin;
    }

    public String getSubReason() {
        return subReason;
    }

    public void setSubReason(String subReason) {
        this.subReason = subReason;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public Integer getAuthenticationStatus() {
        return authenticationStatus;
    }

    public void setAuthenticationStatus(Integer authenticationStatus) {
        this.authenticationStatus = authenticationStatus;
    }

    public String getFaceRecognitionImgUrl() {
        return faceRecognitionImgUrl;
    }

    public void setFaceRecognitionImgUrl(String faceRecognitionImgUrl) {
        this.faceRecognitionImgUrl = faceRecognitionImgUrl;
    }

    public String getRegisterArea() {
        return registerArea;
    }

    public void setRegisterArea(String registerArea) {
        this.registerArea = registerArea;
    }

    public String getNational() {
        return national;
    }

    public void setNational(String national) {
        this.national = national;
    }

    public String getAuthenticationStatusName() {
        return authenticationStatusName;
    }

    public void setAuthenticationStatusName(String authenticationStatusName) {
        this.authenticationStatusName = authenticationStatusName;
    }

    public String getReviewStatusName() {
        return reviewStatusName;
    }

    public void setReviewStatusName(String reviewStatusName) {
        this.reviewStatusName = reviewStatusName;
    }

    public Long getPkId() {
        return pkId;
    }

    public void setPkId(Long pkId) {
        this.pkId = pkId;
    }

    public Integer getAccountStatus() {
        return accountStatus;
    }

    public void setAccountStatus(Integer accountStatus) {
        this.accountStatus = accountStatus;
    }

    public Integer getLicenseElementsAuthStatus() {
        return licenseElementsAuthStatus;
    }

    public void setLicenseElementsAuthStatus(Integer licenseElementsAuthStatus) {
        this.licenseElementsAuthStatus = licenseElementsAuthStatus;
    }

    public Integer getLicAuthStatus() {
        return licAuthStatus;
    }

    public void setLicAuthStatus(Integer licAuthStatus) {
        this.licAuthStatus = licAuthStatus;
    }

    public String getAppReviewTime() {
        return appReviewTime;
    }

    public void setAppReviewTime(String appReviewTime) {
        this.appReviewTime = appReviewTime;
    }

    public String getUserTypeName() {
        return userTypeName;
    }

    public void setUserTypeName(String userTypeName) {
        this.userTypeName = userTypeName;
    }

    public String getCreateOperId() {
        return createOperId;
    }

    public void setCreateOperId(String createOperId) {
        this.createOperId = createOperId;
    }

    public String getCreateOperName() {
        return createOperName;
    }

    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    public String getAgencyId() {
        return agencyId;
    }

    public void setAgencyId(String agencyId) {
        this.agencyId = agencyId;
    }
}
