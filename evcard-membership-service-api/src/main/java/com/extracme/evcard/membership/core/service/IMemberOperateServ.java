package com.extracme.evcard.membership.core.service;

import com.extracme.evcard.membership.core.bean.LoginBeanResult;
import com.extracme.evcard.membership.core.dto.UpdateUserDto;
import com.extracme.evcard.membership.core.exception.CheckLoginException;
import com.extracme.evcard.membership.core.exception.UserMessageException;
import com.extracme.evcard.membership.core.input.LoginInput;

public interface IMemberOperateServ {


    /**
     * 检查用户密码登录.
     * @param loginInput
     * <AUTHOR>
     * @since 1.6.0
     * @return
     * @throws UserMessageException
     */
    LoginBeanResult checkLogin(LoginInput loginInput) throws UserMessageException;

    /**
     * 检查用户密码登录.
     * @param loginInput
     * <AUTHOR>
     * @since 3.6.0
     * @return
     * @throws UserMessageException
     */
    LoginBeanResult checkLoginChange(LoginInput loginInput) throws UserMessageException;


    /**
     * 检查用户密码登录. v2
     * @param loginInput
     * @return
     * @throws CheckLoginException
     * @remark  TODO 关联iplatSchema，确认此方法是否在使用
     */
    LoginBeanResult checkLoginV2(LoginInput loginInput) throws CheckLoginException;


    /**
     * 检查用户验证码登录.
     * @param loginInput
     * <AUTHOR>
     * @since 1.6.0
     * @return
     * @throws UserMessageException
     */
    LoginBeanResult checkVerifyCodeLogin(LoginInput loginInput) throws UserMessageException;

    /**
     * app改造用户验证码登录
     * @param loginInput
     * @return
     * @since 3.6.0
     * @Date 2019/11/18
     * @throws UserMessageException
     */
    public LoginBeanResult checkVerifyCodeLoginChange(LoginInput loginInput) throws UserMessageException;

        /**
         * 检查用户验证码登录. v2
         * @param loginInput
         * <AUTHOR>
         * @since 1.6.0
         * @return
         * @throws UserMessageException
         */
    LoginBeanResult checkVerifyCodeLoginV2(LoginInput loginInput) throws CheckLoginException;
    
    /**
     * 校验验证码.<br>
     * @param mobilePhone	手机号.<br>
     * @param verifyCode	验证码.<br>
     * @param delete		验证通过之后是否直接删除.<br>
     * @return	校验结果.<br>
     */
    boolean checkVerifyCode(String mobilePhone, String verifyCode, Boolean delete);
    
    /**
     * 校验验证码.<br>
     * @param mobilePhone	手机号.<br>
     * @param verifyCode	验证码.<br>
     * @param type			验证码类型.<br>
     * @param delete		验证通过之后是否直接删除.<br>
     * @return	校验结果.<br>
     * @deprecated checkVerifyCode(String mobilePhone, String verifyCode, Boolean delete) 不需要再传type值了。
     */
    @Deprecated 
    boolean checkVerifyCode(String mobilePhone, String verifyCode, Integer type, Boolean delete);
    
    /**
     * 绑定imei,该函数不扣减绑定次数,即便绑定次数超限也能成功，直接把imei强制绑定到会员，如果已经绑定过则覆盖.<br>
     * @param imei
     * @param mobilePhone
     * @param authId
     */
    void bindImei(String imei, String mobilePhone, String authId);

    /**
     * 修改imei
     * @param imei
     * @param mobilePhone
     * @param authId
     * @param type
     */
    void changeImei(String imei, String mobilePhone, String authId,Integer type);
    
    /**
     * 绑定imei，如果已经绑定过则覆盖.<br>
     * @param imei		设备编号.<br>
     * @param mobilePhone	手机号.<br>
     * @param authId		会员id.<br>
     * @param subtractTimes	是否扣减次数.<br>
     * @return false 绑定次数超限; true：绑定成功.<br>
     */
    boolean bindImei(String imei, String mobilePhone, String authId, boolean subtractTimes);
    
    /**
     * 绑定用户的推送通道.EVWORK使用，不推荐其他场景使用.<br>
     * @param ChannelId	通道id.<br>
     * @param authId	会员id.<br>
     * @param membershipType	会员类型.<br>
     * @since 2.21.1
     * <AUTHOR>
     */
    void bindChannel(String ChannelId, String authId, int membershipType);
    
    /**
     * 获取绑定设备次数.<br>
     * @param authId		会员id.<br>
     * @param mobilePhone	手机号.<br>
     * @return	绑定设备的次数.
     */
    Integer getBindImeiTimes(String authId, String mobilePhone);

    /**
     * 修改用户图像
     * @param authId
     * @param orgId
     * @param imageUrl
     * @param updateUserDto
     * @throws CheckLoginException
     */
    void changeUserImage(String authId, String orgId, String imageUrl, UpdateUserDto updateUserDto) throws CheckLoginException;

}
