package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;

public class ReviewAndCarStatusDto implements Serializable {
    private static final long serialVersionUID = -6491402378042521776L;

    //会员卡id
    private String cardNo;

    // 审核状态(0:待审核 1：审核通过 2: 审核不通过 3：用户无效）
    private Integer reviewStatus;

    //认证状态
    private Integer authenticationStatus;

    // 会员状态(0：有效  1：无效)
    private Integer userstatus;

    //会员卡状态(0：有效 1：失效  2:已注销)
    private Integer carStatus;

    // 卡激活状态 0:未激活 1：已激活
    private Integer activeStatus;

    public Integer getAuthenticationStatus() {
        return authenticationStatus;
    }

    public void setAuthenticationStatus(Integer authenticationStatus) {
        this.authenticationStatus = authenticationStatus;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public Integer getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(Integer reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public Integer getUserstatus() {
        return userstatus;
    }

    public void setUserstatus(Integer userstatus) {
        this.userstatus = userstatus;
    }

    public Integer getCarStatus() {
        return carStatus;
    }

    public void setCarStatus(Integer carStatus) {
        this.carStatus = carStatus;
    }

    public Integer getActiveStatus() {
        return activeStatus;
    }

    public void setActiveStatus(Integer activeStatus) {
        this.activeStatus = activeStatus;
    }
}
