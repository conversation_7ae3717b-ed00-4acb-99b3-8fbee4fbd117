package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class AgencyInfoDto implements Serializable {

    private static final long serialVersionUID = 78897846172064036L;
    private String agencyId;

    private String agencyName;

    private Double discountInner;

    private Double discountOuter;

    private String createdUser;

    private String createdTime;

    private String updatedUser;

    private String updatedTime;
    /** 状态：1有效 0无效 */
    private Double status;

    private String pickupWeeks;

    private String returnWeeks;

    private String pickupTime;

    private String returnTime;

    private String maxUserHour;

    private Integer exemptDeposit;

    private String contact;

    private String tel;

    private String mobilePhone;

    private String mail;

    private String licenseNo;

    private String fax;

    private String licenseNoImgUrl;

    private String contractImgUrl;

    private String taxRegistrationImgUrl;

    private String orgCodeImgUrl;

    private String remark;

    private Double payWay;

    private Double deposit;

    private Double rentMins;

    private String checkDate;

    private Integer checkAlert;

    private Byte orgProperty;

    private Integer insideFlag;

    private String address;

    private Date lastRemindDate;

    private Integer discountRule;

    private String orgId;

    private String orgName;

    private BigDecimal maxUnitPrice;

    private String appKey;

    private Date cooperateStartTime;

    private Date cooperateEndTime;

    /** 0-未开始；1-合作中；2-已暂停*/
    private Integer cooperateStatus;

    private Double vehicleThreshold;

    private Long discountId;

    private Long discountPersonalId;

    private Double lineLimitMonthly;

    /**
     * 失效时间
     */
    private Date expireTime;

    /**
     * 创建方式 1会员系统 2政企框架合同
     */
    private byte createWay;

    /**
     * 长租框架合同编号
     *
     */
    private String longRentContractId;

    /**
     * 押金担保方 1企业支付 2个人支付
     */
    private byte depositGuarantor;

    /**
     * 订单支付方 1企业支付、2个人支付
     */
    private byte orderPayer;

    /**
     * 违约承担方 1企业承担 2个人承担
     */
    private byte defaultingParty;

    /**
     * 业务来源 1=政企客户 2=异业合作 3=短租门店
     */
    private byte businessSource;

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    public byte getCreateWay() {
        return createWay;
    }

    public void setCreateWay(byte createWay) {
        this.createWay = createWay;
    }

    public String getLongRentContractId() {
        return longRentContractId;
    }

    public void setLongRentContractId(String longRentContractId) {
        this.longRentContractId = longRentContractId;
    }

    public byte getDepositGuarantor() {
        return depositGuarantor;
    }

    public void setDepositGuarantor(byte depositGuarantor) {
        this.depositGuarantor = depositGuarantor;
    }

    public byte getOrderPayer() {
        return orderPayer;
    }

    public void setOrderPayer(byte orderPayer) {
        this.orderPayer = orderPayer;
    }

    public byte getDefaultingParty() {
        return defaultingParty;
    }

    public void setDefaultingParty(byte defaultingParty) {
        this.defaultingParty = defaultingParty;
    }

    public byte getBusinessSource() {
        return businessSource;
    }

    public void setBusinessSource(byte businessSource) {
        this.businessSource = businessSource;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getAgencyId() {
        return agencyId;
    }

    public void setAgencyId(String agencyId) {
        this.agencyId = agencyId;
    }

    public String getAgencyName() {
        return agencyName;
    }

    public void setAgencyName(String agencyName) {
        this.agencyName = agencyName;
    }

    public Double getDiscountInner() {
        return discountInner;
    }

    public void setDiscountInner(Double discountInner) {
        this.discountInner = discountInner;
    }

    public Double getDiscountOuter() {
        return discountOuter;
    }

    public void setDiscountOuter(Double discountOuter) {
        this.discountOuter = discountOuter;
    }

    public String getCreatedUser() {
        return createdUser;
    }

    public void setCreatedUser(String createdUser) {
        this.createdUser = createdUser;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public String getUpdatedUser() {
        return updatedUser;
    }

    public void setUpdatedUser(String updatedUser) {
        this.updatedUser = updatedUser;
    }

    public String getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(String updatedTime) {
        this.updatedTime = updatedTime;
    }

    public Double getStatus() {
        return status;
    }

    public void setStatus(Double status) {
        this.status = status;
    }

    public String getPickupWeeks() {
        return pickupWeeks;
    }

    public void setPickupWeeks(String pickupWeeks) {
        this.pickupWeeks = pickupWeeks;
    }

    public String getReturnWeeks() {
        return returnWeeks;
    }

    public void setReturnWeeks(String returnWeeks) {
        this.returnWeeks = returnWeeks;
    }

    public String getPickupTime() {
        return pickupTime;
    }

    public void setPickupTime(String pickupTime) {
        this.pickupTime = pickupTime;
    }

    public String getReturnTime() {
        return returnTime;
    }

    public void setReturnTime(String returnTime) {
        this.returnTime = returnTime;
    }

    public String getMaxUserHour() {
        return maxUserHour;
    }

    public void setMaxUserHour(String maxUserHour) {
        this.maxUserHour = maxUserHour;
    }

    public Integer getExemptDeposit() {
        return exemptDeposit;
    }

    public void setExemptDeposit(Integer exemptDeposit) {
        this.exemptDeposit = exemptDeposit;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getMail() {
        return mail;
    }

    public void setMail(String mail) {
        this.mail = mail;
    }

    public String getLicenseNo() {
        return licenseNo;
    }

    public void setLicenseNo(String licenseNo) {
        this.licenseNo = licenseNo;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getLicenseNoImgUrl() {
        return licenseNoImgUrl;
    }

    public void setLicenseNoImgUrl(String licenseNoImgUrl) {
        this.licenseNoImgUrl = licenseNoImgUrl;
    }

    public String getContractImgUrl() {
        return contractImgUrl;
    }

    public void setContractImgUrl(String contractImgUrl) {
        this.contractImgUrl = contractImgUrl;
    }

    public String getTaxRegistrationImgUrl() {
        return taxRegistrationImgUrl;
    }

    public void setTaxRegistrationImgUrl(String taxRegistrationImgUrl) {
        this.taxRegistrationImgUrl = taxRegistrationImgUrl;
    }

    public String getOrgCodeImgUrl() {
        return orgCodeImgUrl;
    }

    public void setOrgCodeImgUrl(String orgCodeImgUrl) {
        this.orgCodeImgUrl = orgCodeImgUrl;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Double getPayWay() {
        return payWay;
    }

    public void setPayWay(Double payWay) {
        this.payWay = payWay;
    }

    public Double getDeposit() {
        return deposit;
    }

    public void setDeposit(Double deposit) {
        this.deposit = deposit;
    }

    public Double getRentMins() {
        return rentMins;
    }

    public void setRentMins(Double rentMins) {
        this.rentMins = rentMins;
    }

    public String getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(String checkDate) {
        this.checkDate = checkDate;
    }

    public Integer getCheckAlert() {
        return checkAlert;
    }

    public void setCheckAlert(Integer checkAlert) {
        this.checkAlert = checkAlert;
    }

    public Byte getOrgProperty() {
        return orgProperty;
    }

    public void setOrgProperty(Byte orgProperty) {
        this.orgProperty = orgProperty;
    }

    public Integer getInsideFlag() {
        return insideFlag;
    }

    public void setInsideFlag(Integer insideFlag) {
        this.insideFlag = insideFlag;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Date getLastRemindDate() {
        return lastRemindDate;
    }

    public void setLastRemindDate(Date lastRemindDate) {
        this.lastRemindDate = lastRemindDate;
    }

    public Integer getDiscountRule() {
        return discountRule;
    }

    public void setDiscountRule(Integer discountRule) {
        this.discountRule = discountRule;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public BigDecimal getMaxUnitPrice() {
        return maxUnitPrice;
    }

    public void setMaxUnitPrice(BigDecimal maxUnitPrice) {
        this.maxUnitPrice = maxUnitPrice;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public Date getCooperateStartTime() {
        return cooperateStartTime;
    }

    public void setCooperateStartTime(Date cooperateStartTime) {
        this.cooperateStartTime = cooperateStartTime;
    }

    public Date getCooperateEndTime() {
        return cooperateEndTime;
    }

    public void setCooperateEndTime(Date cooperateEndTime) {
        this.cooperateEndTime = cooperateEndTime;
    }

    public Integer getCooperateStatus() {
        return cooperateStatus;
    }

    public void setCooperateStatus(Integer cooperateStatus) {
        this.cooperateStatus = cooperateStatus;
    }

    public Double getVehicleThreshold() {
        return vehicleThreshold;
    }

    public void setVehicleThreshold(Double vehicleThreshold) {
        this.vehicleThreshold = vehicleThreshold;
    }

    public Long getDiscountId() {
        return discountId;
    }

    public void setDiscountId(Long discountId) {
        this.discountId = discountId;
    }

    public Long getDiscountPersonalId() {
        return discountPersonalId;
    }

    public void setDiscountPersonalId(Long discountPersonalId) {
        this.discountPersonalId = discountPersonalId;
    }

    public Double getLineLimitMonthly() {
        return lineLimitMonthly;
    }

    public void setLineLimitMonthly(Double lineLimitMonthly) {
        this.lineLimitMonthly = lineLimitMonthly;
    }
}