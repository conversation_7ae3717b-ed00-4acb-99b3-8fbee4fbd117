package com.extracme.evcard.membership.credit.dto;

import com.extracme.evcard.rpc.dto.BaseResponse;

public class AddCreditEventTypeReportDto  extends BaseResponse {

    private static final long serialVersionUID = -8338488577769077561L;
    /**
     * 年份
     */
    private String yearNum;

    /**
     * 所属公司id
     */
    private String orgId;

    /**
     * 查询类型 0-各事件类型次数 1-各事件类型会员数
     */
    private Integer type;

    /**
     * 事件类型id
     */
    private Long eventTypeId;

    /**
     * 事件类型名称
     */
    private String eventName;

    /**
     * 月份1-12
     */
    private Integer month;

    /**
     * 数量
     */
    private Integer total;

    public String getYearNum() {
        return yearNum;
    }

    public void setYearNum(String yearNum) {
        this.yearNum = yearNum;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getEventTypeId() {
        return eventTypeId;
    }

    public void setEventTypeId(Long eventTypeId) {
        this.eventTypeId = eventTypeId;
    }

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public Integer getMonth() {
        return month;
    }

    public void setMonth(Integer month) {
        this.month = month;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }
}