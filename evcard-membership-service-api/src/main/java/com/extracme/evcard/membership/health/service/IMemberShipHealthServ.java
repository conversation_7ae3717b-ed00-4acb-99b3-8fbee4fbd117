package com.extracme.evcard.membership.health.service;

import com.extracme.evcard.rpc.dto.BaseResponse;

/**
 * <AUTHOR>
 * @Description 随申码相关接口
 */
public interface IMemberShipHealthServ {

    /**
     * 获取会员健康码信息
     * 该接口采用黑名单的形式、
     * 只有实际异常用户会返回异常状态
     * 若用户不存在，或者相关基础信息错误，不会进行校验
     * @param name 会员姓名
     * @param authId 会员证件号
     * @return 健康信息
     */
    BaseResponse getMemberHealth(String name, String authId,String auth);
}
