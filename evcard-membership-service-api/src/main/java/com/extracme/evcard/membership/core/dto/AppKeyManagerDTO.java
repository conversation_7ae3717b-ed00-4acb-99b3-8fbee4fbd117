package com.extracme.evcard.membership.core.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class AppKeyManagerDTO implements Serializable {
    private String appKey;

    private String appSecret;

    private String requestAppKey;

    private String platName;

    private String requestAppSecret;

    private String postUrl;

    private String className;

    private String orgId;

    private String remark;

    private Double status;

    private Double autoRegist;

    private Double loginRestrict;

    private Double autoPay;

    private Double enjoyBenefit;

    private Double uploadOrder;

    private Byte type;

    private Date createdTime;

    private Long createOperId;

    private String createdUser;

    private Date updatedTime;

    private Long updateOperId;

    private String updatedUser;

    private String platAppKey;

    private String platAppSecret;


}