package com.extracme.evcard.membership.core.dto;

/**
 * 会员详情.主要应用场景是客服.<br>
 * <AUTHOR>
 *
 */
public class MembershipDetailInfo extends MembershipBasicInfo {

	private static final long serialVersionUID = -390631272407981916L;
	
	/**
	 * 取消订单次数.<br>
	 */
	private Integer cancelOrderTimes;
	
	/**
	 * 本月更换设备次数.<br>
	 */
	private Integer bindImeiTimes;
	
	/**
	 * 当天免单次数.<br>
	 */
	private Integer freeOrderTimes;
	

	public Integer getCancelOrderTimes() {
		return cancelOrderTimes;
	}

	public void setCancelOrderTimes(Integer cancelOrderTimes) {
		this.cancelOrderTimes = cancelOrderTimes;
	}

	public Integer getBindImeiTimes() {
		return bindImeiTimes;
	}

	public void setBindImeiTimes(Integer bindImeiTimes) {
		this.bindImeiTimes = bindImeiTimes;
	}

	public Integer getFreeOrderTimes() {
		return freeOrderTimes;
	}

	public void setFreeOrderTimes(Integer freeOrderTimes) {
		this.freeOrderTimes = freeOrderTimes;
	}


}
