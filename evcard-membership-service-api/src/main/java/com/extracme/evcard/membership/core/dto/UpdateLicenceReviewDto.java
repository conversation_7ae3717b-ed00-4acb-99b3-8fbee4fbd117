package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;
import java.util.Date;

public class UpdateLicenceReviewDto implements Serializable {
    private String mid;   //用户id

    private int auditStatus;  // 审核状态 1:成功 2:失败

    // 审核失败字段 11111111 按照字段定义顺序 1:代表审核通过 2:代表审核失败
    // identityType,identityNo,username,expireType,identityCardImgUrl,
    // reverseIdentityCardImgUrl,holdCardImgUrl,faceImgUrl
    private  String reviewItems;

    private String failReason;  // 失败原因

    private int operateSourceType;  //  0：管理后台 1：履约小程序
    private String operatorUserName;   //操作人

    private String operatorId;   //操作人id
    private Date operationTime;
    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {this.mid = mid;}

    public int getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(int auditStatus) {
        this.auditStatus = auditStatus;
    }

    public void setReviewItems(String reviewItems) {
        this.reviewItems = reviewItems;
    }

    public String getReviewItems() {
        return reviewItems;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    public int getOperateSourceType() {
        return operateSourceType;
    }

    public void setOperateSourceType(int operateSourceType) {
        this.operateSourceType = operateSourceType;
    }

    public String getOperatorUserName() {
        return operatorUserName;
    }

    public void setOperatorUserName(String operatorUserName) {
        this.operatorUserName = operatorUserName;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public Date getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(Date operationTime) {
        this.operationTime = operationTime;
    }
}
