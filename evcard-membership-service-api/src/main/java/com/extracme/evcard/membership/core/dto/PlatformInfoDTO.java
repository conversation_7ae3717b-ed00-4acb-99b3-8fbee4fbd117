package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class PlatformInfoDTO implements Serializable {

	private static final long serialVersionUID = 2645760714367365246L;

	/**
	 * 平台ID
	 */
	private Long id;

	/**
	 * 平台名称
	 */
	private String platformName;

	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getPlatformName() {
		return platformName;
	}
	public void setPlatformName(String platformName) {
		this.platformName = platformName;
	}

	@Override
	public String toString() {
		return "PlatformInfoDTO{" +
				"id=" + id +
				", platformName='" + platformName + '\'' +
				'}';
	}
}
