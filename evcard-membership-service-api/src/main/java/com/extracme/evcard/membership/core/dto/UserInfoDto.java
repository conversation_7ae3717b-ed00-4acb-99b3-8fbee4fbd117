package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;
import java.math.BigDecimal;

public class UserInfoDto implements Serializable {
    private static final long serialVersionUID = 8629654882815802783L;

    /** 手机号 */
    private String mobilePhone;
    /** 证件号(先用做用户唯一编号) */
    private String authId;
    /** 用户名 */
    private String userName;
    /** 邮箱 */
    private String email;
    /** 地址 */
    private String address;
    /** 邮政编码 */
    private String zip;
    /** 用户图像图片url */
    private String imageUrl;
    /** 审核状态( -1 资料不全(未认证) 0:待审核 1：审核通过 2: 审核不通过  3：用户无效 4:重新审核 5 未完成认证状态(已提交驾照信息未刷脸) ） */
    private int reviewStatus;
    private String orgId;
    /** 驾照认证状态 0待认证 1认证通过 2认证不通过 */
    private Integer licenseAuthStatus;
    /** 驾照三要素认证状态：0待认证 1认证通过 2认证不通过 3待重新认证(查证中) */
    private Integer licenseElementsAuthStatus;
    /**
     * elementsReviewItems: 驾照号/姓名/档案编号
     * 0不一致 1一致
     * 例:010
     */
    private String elementsReviewItems;
    /** 驾照状态信息：吊销、撤销、注销、停止使用、锁定等异常状态 */
    private String licenseStatusMsg;

    /** 会员卡号 */
    private String cardNo;
    /** 储备金 */
    private String reserveAmount;
    /** 时间账户（分钟）*/
    private float rentMins;
    /**  省 */
    private String province;
    /** 市 */
    private String city;
    /** 区 */
    private String area;
    /** 驾照图片 */
    private String drivingLicenseImgUrl;
    /** 4位字符串 1111 从左往右依次代码 姓名 ，手机号，邮寄地址，证件号或驾照图片 0：未审核 1：审核通过 2：审核不通过 */
    private String reviewResult;
    /** 手持身份证图片 */
    private String holdIdcardPicUrl;
    /**人脸识别图片url*/
    private String faceRecognitionImgUrl;
    /** 身份证图片 */
    private String idcardPicUrl;
    /** 驾照号 */
    private String driverCode;
    /** 初次领证日期 */
    private String  firstGetLicenseDate;
    /** 到期日期 */
    private String  expirationDate;
    /** 驾照类型 */
    private String drivingLicenseType;
    /** 审核状态描述 */
    private String reviewStatusDesc;
    /** 是否外籍 */
    private int isForeign;
    /** 是否老用户 */
    private int isOldUser;
    /** 认证状态 0 未认证 1 未刷脸 2 已认证 */
    private int authenticationStatus;
    /** 是否可编辑 */
    private int isEdit;
    /** 老用户认证截止时间 */
    private String oldUserAuthenticationEndDate;
    /** 审核方式 */
    private int reviewMode;
    /** 驾照提取方式  0 无 1 ocr提取  2 手动输入 */
    private int driverLicenseInputType;

    /** 驾照是否过期  0 未过期 ，1 已过期， 2 即将过期 */
    private int isExpire;
    private String agencyId;

    /**会员卡id卡激活状态 0:未激活 1：已激活*/
    private int activityStatus;

    /**卡id状态状态(0：有效 1：失效  2:已注销)*/
    private int cardStatus;

    /**会员状态状态(0：有效  1：无效)*/
    private Short status;

    //身份证号
    private String idCardNumber;

    //护照号
    private String passportNo;

    //档案编号
    private String fileNo;

    //驾照附页照片地址
    private String fileNoImgUrl;

    //1：身份证 2:护照 3：香港澳门通行证 4 台湾通行证
    private Integer idType;
    private String cityOfOrigin;

    private double cityLon;
    private double citylat;

    private Integer  profession;

    private Integer  educational;

    private Integer ownCar;

    /** 是否显示会员积分 0否 1是 */
    private Integer vipPointsDisplayFlag;

    /**  会员积分 */
    private Integer vipPoints;

    /** 驾照地址 */
    private String drivingLicenseAddress;

    public String getDrivingLicenseAddress() {
        return drivingLicenseAddress;
    }

    public void setDrivingLicenseAddress(String drivingLicenseAddress) {
        this.drivingLicenseAddress = drivingLicenseAddress;
    }

    public Integer getVipPointsDisplayFlag() {
        return vipPointsDisplayFlag;
    }

    public void setVipPointsDisplayFlag(Integer vipPointsDisplayFlag) {
        this.vipPointsDisplayFlag = vipPointsDisplayFlag;
    }

    public Integer getVipPoints() {
        return vipPoints;
    }

    public void setVipPoints(Integer vipPoints) {
        this.vipPoints = vipPoints;
    }

    public String getPassportNo() {
        return passportNo;
    }

    public void setPassportNo(String passportNo) {
        this.passportNo = passportNo;
    }

    public String getFileNo() {
        return fileNo;
    }

    public void setFileNo(String fileNo) {
        this.fileNo = fileNo;
    }

    public String getFileNoImgUrl() {
        return fileNoImgUrl;
    }

    public void setFileNoImgUrl(String fileNoImgUrl) {
        this.fileNoImgUrl = fileNoImgUrl;
    }

    public Integer getIdType() {
        return idType;
    }

    public void setIdType(Integer idType) {
        this.idType = idType;
    }

    public Short getStatus() {
        return status;
    }

    public void setStatus(Short status) {
        this.status = status;
    }

    public int getActivityStatus() {
        return activityStatus;
    }

    public void setActivityStatus(int activityStatus) {
        this.activityStatus = activityStatus;
    }

    public int getCardStatus() {
        return cardStatus;
    }

    public void setCardStatus(int cardStatus) {
        this.cardStatus = cardStatus;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getZip() {
        return zip;
    }

    public void setZip(String zip) {
        this.zip = zip;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public int getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(int reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getReserveAmount() {
        return reserveAmount;
    }

    public void setReserveAmount(String reserveAmount) {
        this.reserveAmount = reserveAmount;
    }

    public float getRentMins() {
        return rentMins;
    }

    public void setRentMins(float rentMins) {
        this.rentMins = rentMins;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getDrivingLicenseImgUrl() {
        return drivingLicenseImgUrl;
    }

    public void setDrivingLicenseImgUrl(String drivingLicenseImgUrl) {
        this.drivingLicenseImgUrl = drivingLicenseImgUrl;
    }

    public String getReviewResult() {
        return reviewResult;
    }

    public void setReviewResult(String reviewResult) {
        this.reviewResult = reviewResult;
    }

    public String getHoldIdcardPicUrl() {
        return holdIdcardPicUrl;
    }

    public void setHoldIdcardPicUrl(String holdIdcardPicUrl) {
        this.holdIdcardPicUrl = holdIdcardPicUrl;
    }

    public String getIdcardPicUrl() {
        return idcardPicUrl;
    }

    public void setIdcardPicUrl(String idcardPicUrl) {
        this.idcardPicUrl = idcardPicUrl;
    }

    public String getDriverCode() {
        return driverCode;
    }

    public void setDriverCode(String driverCode) {
        this.driverCode = driverCode;
    }

    public String getFirstGetLicenseDate() {
        return firstGetLicenseDate;
    }

    public void setFirstGetLicenseDate(String firstGetLicenseDate) {
        this.firstGetLicenseDate = firstGetLicenseDate;
    }

    public String getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(String expirationDate) {
        this.expirationDate = expirationDate;
    }

    public String getDrivingLicenseType() {
        return drivingLicenseType;
    }

    public void setDrivingLicenseType(String drivingLicenseType) {
        this.drivingLicenseType = drivingLicenseType;
    }

    public String getReviewStatusDesc() {
        return reviewStatusDesc;
    }

    public void setReviewStatusDesc(String reviewStatusDesc) {
        this.reviewStatusDesc = reviewStatusDesc;
    }

    public int getIsForeign() {
        return isForeign;
    }

    public void setIsForeign(int isForeign) {
        this.isForeign = isForeign;
    }

    public int getIsOldUser() {
        return isOldUser;
    }

    public void setIsOldUser(int isOldUser) {
        this.isOldUser = isOldUser;
    }

    public int getAuthenticationStatus() {
        return authenticationStatus;
    }

    public void setAuthenticationStatus(int authenticationStatus) {
        this.authenticationStatus = authenticationStatus;
    }

    public int getIsEdit() {
        return isEdit;
    }

    public void setIsEdit(int isEdit) {
        this.isEdit = isEdit;
    }

    public String getOldUserAuthenticationEndDate() {
        return oldUserAuthenticationEndDate;
    }

    public void setOldUserAuthenticationEndDate(String oldUserAuthenticationEndDate) {
        this.oldUserAuthenticationEndDate = oldUserAuthenticationEndDate;
    }

    public int getReviewMode() {
        return reviewMode;
    }

    public void setReviewMode(int reviewMode) {
        this.reviewMode = reviewMode;
    }

    public int getDriverLicenseInputType() {
        return driverLicenseInputType;
    }

    public void setDriverLicenseInputType(int driverLicenseInputType) {
        this.driverLicenseInputType = driverLicenseInputType;
    }

    public int getIsExpire() {
        return isExpire;
    }

    public void setIsExpire(int isExpire) {
        this.isExpire = isExpire;
    }

    public String getAgencyId() {
        return agencyId;
    }

    public void setAgencyId(String agencyId) {
        this.agencyId = agencyId;
    }

    public String getFaceRecognitionImgUrl() {
        return faceRecognitionImgUrl;
    }

    public void setFaceRecognitionImgUrl(String faceRecognitionImgUrl) {
        this.faceRecognitionImgUrl = faceRecognitionImgUrl;
    }

    public String getIdCardNumber() {
        return idCardNumber;
    }

    public void setIdCardNumber(String idCardNumber) {
        this.idCardNumber = idCardNumber;
    }

    public Integer getLicenseAuthStatus() {
        return licenseAuthStatus;
    }

    public void setLicenseAuthStatus(Integer licenseAuthStatus) {
        this.licenseAuthStatus = licenseAuthStatus;
    }

    public Integer getLicenseElementsAuthStatus() {
        return licenseElementsAuthStatus;
    }

    public void setLicenseElementsAuthStatus(Integer licenseElementsAuthStatus) {
        this.licenseElementsAuthStatus = licenseElementsAuthStatus;
    }

    public String getLicenseStatusMsg() {
        return licenseStatusMsg;
    }

    public void setLicenseStatusMsg(String licenseStatusMsg) {
        this.licenseStatusMsg = licenseStatusMsg;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getCityOfOrigin() {
        return cityOfOrigin;
    }

    public void setCityOfOrigin(String cityOfOrigin) {
        this.cityOfOrigin = cityOfOrigin;
    }

    public String getElementsReviewItems() {
        return elementsReviewItems;
    }

    public void setElementsReviewItems(String elementsReviewItems) {
        this.elementsReviewItems = elementsReviewItems;
    }

    public double getCityLon() {
        return cityLon;
    }

    public void setCityLon(double cityLon) {
        this.cityLon = cityLon;
    }

    public double getCitylat() {
        return citylat;
    }

    public void setCitylat(double citylat) {
        this.citylat = citylat;
    }

    public Integer getProfession() {
        return profession;
    }

    public void setProfession(Integer profession) {
        this.profession = profession;
    }

    public Integer getEducational() {
        return educational;
    }

    public void setEducational(Integer educational) {
        this.educational = educational;
    }

    public Integer getOwnCar() {
        return ownCar;
    }

    public void setOwnCar(Integer ownCar) {
        this.ownCar = ownCar;
    }
}
