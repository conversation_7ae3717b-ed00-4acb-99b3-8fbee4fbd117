package com.extracme.evcard.membership.credit.dto;

import com.extracme.evcard.rpc.dto.BaseResponse;

import java.util.Date;

/**
 * Created by Elin on 2017/11/22.
 * 新增修改事件类型
 */
public class CreditEventTypeFullDto extends BaseResponse {

    private static final long serialVersionUID = -5583622740833591465L;

    /**
     * 事件类型编号 新增不需要传参
     */
    private Long id;

    /**
     * 事件类型名称
     */
    private String eventName;

    /**
     * 事件类型性质 1-正面 0-负面
     */
    private String eventNature;

    /**
     * 分值
     */
    private Integer amount;

    /**
     * 事件类型描述
     */
    private String eventDesc;

    /**
     * 触发方式 0-自动 1-手动
     */
    private Integer eventWay;

    /**
     * 是否直接加入黑名单 false-否 true-是
     */
    private Boolean blackList;

    /**
     * 备注
     */
    private String miscDesc;

    /**
     * 是否有效（0 无效 1 有效）
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人id
     */
    private Long createOperId;

    /**
     * 创建人姓名
     */
    private String createOperName;
    /**
     * 修改人姓名
     */
    private Date updateTime;

    /**
     * 修改人id
     */
    private Long updateOperId;

    /**
     * 修改时间
     */
    private String updateOperName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public String getEventNature() {
        return eventNature;
    }

    public void setEventNature(String eventNature) {
        this.eventNature = eventNature;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public String getEventDesc() {
        return eventDesc;
    }

    public void setEventDesc(String eventDesc) {
        this.eventDesc = eventDesc;
    }

    public Integer getEventWay() {
        return eventWay;
    }

    public void setEventWay(Integer eventWay) {
        this.eventWay = eventWay;
    }

    public Boolean getBlackList() {
        return blackList;
    }

    public void setBlackList(Boolean blackList) {
        this.blackList = blackList;
    }

    public String getMiscDesc() {
        return miscDesc;
    }

    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateOperId() {
        return createOperId;
    }

    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    public String getCreateOperName() {
        return createOperName;
    }

    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdateOperId() {
        return updateOperId;
    }

    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    public String getUpdateOperName() {
        return updateOperName;
    }

    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }
}
