package com.extracme.evcard.membership.core.service;

import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.dto.input.CRGTMemberInputDto;
import com.extracme.evcard.membership.core.input.*;
import com.extracme.evcard.rpc.exception.BusinessException;

import java.io.IOException;

public interface IThirdPartMemberService {

    /**
     * 国铁第三方会员插入
     */
    MembershipBasicInfo insertCRGTmMember(CRGTMemberInputDto crgtMemberInputDto);

    /**
     */
    String getImageStrFromUrl(String imgUrl, Integer type, String phone) throws IOException;

    /**
     * 支付宝 车生活登录
     *  新车生活登录注册 见qingLuLogin
     * @param input
     * @return
     */
    ThirdLoginDto carLifeLogin(CarLifeLoginInput input) throws BusinessException;

    /**
     * 擎路 登录
     *
     * @param input
     * @return
     */
    ThirdLoginDto qingLuLogin(ThirdLoginInput input) throws BusinessException;

    /**
     * 长短租融合 登录
     * @param input
     * @return
     * @throws BusinessException
     */
    ThirdLoginDto longShortMixLogin(ThirdLoginInput input) throws BusinessException;

    /**
     * 赛乐通 登录
     * @param input
     * @return
     * @throws BusinessException
     */
    ThirdLoginDto saicTravelLogin(ThirdLoginInput input) throws BusinessException;

    /**
     *  第三方注册登录  获取mid
     *
     * @return
     * @throws BusinessException
     */
    ThirdLoginDto thirdLoginGetMid(ThirdLoginInput input)throws BusinessException;

    /**
     *  第三方获取登录token
     *
     * @param input
     * @return
     * @throws BusinessException
     */
    EvcardTokenDto getThirdAccessToken(GetThirdAccessTokenInput input)throws BusinessException;

    /**
     * 随身行 绑定
     * @param
     * @return
     * @throws BusinessException
     */
    void suiShenXingBind(String secondAppKey,Long userId) throws BusinessException;

    /**
     * 用户登录成功通知
     *  目前只针对 随身行 上汽职工之家
     * @param
     * @return
     * @throws BusinessException
     */
    void userLoginNotify(NotifyLoginDto notifyLoginDto) throws BusinessException;

    /**
     * 通用用户的注册登录
     *
     * @param input
     * @return
     */
    ThirdLoginDto commonUserLogin(ThirdLoginInput input) throws BusinessException;
}
