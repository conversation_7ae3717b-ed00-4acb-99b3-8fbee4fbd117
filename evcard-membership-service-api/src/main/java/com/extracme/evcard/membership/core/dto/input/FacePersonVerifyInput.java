package com.extracme.evcard.membership.core.dto.input;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Discription
 * @date 2021/3/17
 */
@Data
public class FacePersonVerifyInput implements Serializable {

    public static final Integer BASE64 = 0;

    /**
     * 图片信息，图片上传方式根据image_type来指定
     * 必须
     */
    private String image;

    /**
     * 图片类型(推荐使用类型 2 FACE_TOKEN)
     * 0. 图片base64值
     * 1. 图片下载url
     * 2. 人脸图片唯一标识，FACE_TOKEN（人脸检测时，为每个人连赋予的一个唯一face_token）
     * 必须
     */
    private Integer imageType;

    /**
     * 采集SDK类型， 实名认证增强版使用
     * 1: ios
     * 2: android
     */
    private Integer app;

    /**
     * 会员id， 必须
     */
    private String authId;

    /**
     * 身份证号
     * 必须
     */
    private String idCardNo;

    /**
     * 姓名(UTF-8编码)
     * 必须
     */
    private String name;

    /**
     * 图片质量控制
     * NONE/LOW/NORMAL/HIGH/NONE
     * 默认NORMAL
     * 必须
     */
    private String qualityControl;

    /**
     * 活体检测控制
     * NONE/LOW/NORMAL/HIGH/NONE
     * 默认NONE
     * 必须
     */
    private String livenessControl;

    /**
     * 合成图控制
     * NONE/LOW/NORMAL/HIGH/NONE
     * 默认NORMAL
     * 必须
     */
    private String spoofingControl;

}
