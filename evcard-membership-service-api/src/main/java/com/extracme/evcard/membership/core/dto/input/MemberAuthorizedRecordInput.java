package com.extracme.evcard.membership.core.dto.input;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户芝麻授权记录
 * <AUTHOR>
 * @Discription
 * @date 2020/1/10
 */
@Data
public class MemberAuthorizedRecordInput implements Serializable {
    /**
     * 会员id
     * 必填
     */
    private String authId;

    /**
     * 授权操作类别：0 芝麻授权 1 代扣授权 2 取消代扣
     * 必填
     */
    private Integer authorizedType;

    /**
     * 操作结果：0 失败 1 成功
     * 必填
     */
    private Integer authorizedResult;

    /**
     * 操作结果原因描述
     * 非必填
     */
    private String authorizedReason;

    /**
     * 授权操作时间
     * 非必填，缺省为当前时间
     */
    private Date authorizedDateTime;

    /**
     * 授权时芝麻信用门槛分
     * 授权时，必填
     */
    private Integer authorizedFraction;

    /**
     * 操作人id
     * 非必填
     */
    private Long operatorId;

    /**
     * 操作人名称
     * 非必填
     */
    private String operatorName;
}
