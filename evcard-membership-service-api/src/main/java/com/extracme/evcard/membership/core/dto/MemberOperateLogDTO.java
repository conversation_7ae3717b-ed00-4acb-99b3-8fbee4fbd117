package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2019/3/8
 */
public class MemberOperateLogDTO implements Serializable {
    private static final long serialVersionUID = -3503561981136092598L;


    private Long operationType;

    private String operationContent;

    private Date createTime;

    private String createOperName;

    public Long getOperationType() {
        return operationType;
    }

    public void setOperationType(Long operationType) {
        this.operationType = operationType;
    }

    public String getOperationContent() {
        return operationContent;
    }

    public void setOperationContent(String operationContent) {
        this.operationContent = operationContent;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateOperName() {
        return createOperName;
    }

    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    @Override
    public String toString() {
        return "MemberOperateLogDTO{" +
                "operationType=" + operationType +
                ", operationContent='" + operationContent + '\'' +
                ", createTime=" + createTime +
                ", createOperName='" + createOperName + '\'' +
                '}';
    }
}
