package com.extracme.evcard.membership.core.input;

import lombok.Data;

import java.io.Serializable;

@Data
public class OcrAndSubmitLicenseInput implements Serializable {

    private static final long serialVersionUID = 1091645815130697216L;
    /**
     * 会员id
     */
    private String mid;

    /**
     * 渠道key
     */
    private String appKey;

    /**
     * 二级渠道key
     */
    private String secondAppKey;

    /**
     * 驾照正面图片/电子驾照图片
     */
    private String driverLicenseImgUrl;
    /**
     * 驾照反面图片
     */
    private String reverseDriverLicenseImgUrl;
    // 驾照号
    private String licenseNo;

    /**
     * 驾照类型 1:电子驾照 2:纸质驾照
     */
    private Integer driverLicenseType;

    /**
     * 城市运营模式 0：大库 1：门店
     */
    private String operationModel;
}
