package com.extracme.evcard.membership.core.dto.input;

import lombok.Data;

import java.io.Serializable;

@Data
public class FaceMatchInput implements Serializable {


    /**
     * 用户 authId
     */
    private String authId;

    /**
     * 非必填： 默认选择APP场景
     * APP ： APP场景，与采集SDK一起使用
     *
     * 1: APP
     */
    private Integer sceneType;

    /**
     * 必填, 采集发生在人脸比对时。
     * APP场景： 采集SDK4.1版本上传的加密图片Base64信息
     */
    private String image;

    /**
     * 必填
     * APP: 图片类型 BASE64:图片的base64值，base64编码后的图片数据，编码后的图片大小不超过2M；
     * 0. 图片base64值
     * 1. 图片下载url
     * 2. 人脸图片唯一标识，FACE_TOKEN（人脸检测时，为每个人连赋予的一个唯一face_token）
     */
    private Integer imageType;

    /**
     * scene_type 等于 APP 时必填。
     * 1: ios
     * 2: android
     */
    private Integer app;

    /**
     * 必填， 这个是底图。
     * 图片信息(总数据大小应小于10M)，图片上传方式根据image_type来判断。本图片特指客户服务器上传图片，非加密图片Base64值
     */
    private String registerImage;

    /**
     * 必填：
     * 图片类型
     * BASE64:图片的base64值，base64编码后的图片数据，编码后的图片大小不超过2M；
     * URL:图片的 URL地址( 可能由于网络等原因导致下载图片时间过长)；
     * FACE_TOKEN: 人脸图片的唯一标识，调用人脸检测接口时，会为每个人脸图片赋予一个唯一的FACE_TOKEN，同一张图片多次检测得到的FACE_TOKEN是同一个。
     *
     * 0. 图片base64值
     * 1. 图片下载url
     * 2. 人脸图片唯一标识，FACE_TOKEN（人脸检测时，为每个人连赋予的一个唯一face_token）
     */
    private Integer registerImageType;
}
