package com.extracme.evcard.membership.invitation.service;

import com.extracme.evcard.membership.core.dto.MemberInviteInfoDto;
import com.extracme.evcard.rpc.dto.BaseResponse;


/**
 * \* Created with IntelliJ IDEA.
 * \* User: Elin
 * \* Date: 2018/1/9
 * \* Time: 15:47
 * \* To change this template use File | Settings | File Templates.
 * \* Description:好友邀请
 * \
 */
public interface IMemberShipInvitationServ {

    /**
     * 赠送新手礼包.<br>
     *
     * @param authId   会员id
     * @param mobilePhone 会员手机号
     * @param optOrgId 操作人机构id
     * @param createUser  操作人
     * @return
     */
    BaseResponse giveAnewGiftBag(String authId, String mobilePhone, String optOrgId, String createUser);

    /**
     * 赠送好友邀请礼包.<br>
     * @param authId 会员id
     * @param mobilePhone 会员手机号
     * @param optOrgId 操作人机构id
     * @param createUser 操作人
     * @return
     */
    BaseResponse giveFriendInvitationGiftBag(String authId, String mobilePhone, String optOrgId, String createUser);

    /**
     * 根据会员authid和会员类型获取会员邀请信息
     * @param authId
     * @param membershipType
     * @return
     * <AUTHOR>
     * @since  2.3.0
     */
    MemberInviteInfoDto getMemberInviteInfo(String authId, Short membershipType);

}