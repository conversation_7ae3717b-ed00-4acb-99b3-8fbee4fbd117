package com.extracme.evcard.membership.contract.service;

import com.extracme.evcard.rpc.dto.BaseResponse;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Discription
 * @date 2021/3/5
 */
@Data
@NoArgsConstructor
public class UpdateTemplateResponse extends BaseResponse {
    private ContractSupplier supplier;
    private String templateId;

    public UpdateTemplateResponse(int code, String message) {
        super(code, message);
    }

    public ContractSupplier getSupplier() {
        return supplier;
    }

    public void setSupplier(ContractSupplier supplier) {
        this.supplier = supplier;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }
}
