package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;

/**
 * <p>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @Date 2019/8/9 16:52
 */
public class StsTokenDto implements Serializable {

    private static final long serialVersionUID = 8404105074215535297L;

    /**
     * 访问key
     */
    private String accessKeyId;

    /**
     * 访问密钥
     */
    private String accessKeySecret;

    /**
     * sts标识
     */
    private String stsToken;

    /**
     * bucket名
     */
    private String bucketName;

    /**
     * 根目录
     */
    private String endpoint;

    /**
     * 环境变量
     */
    private String env;

    public String getAccessKeyId() {
        return accessKeyId;
    }

    public void setAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
    }

    public String getAccessKeySecret() {
        return accessKeySecret;
    }

    public void setAccessKeySecret(String accessKeySecret) {
        this.accessKeySecret = accessKeySecret;
    }

    public String getStsToken() {
        return stsToken;
    }

    public void setStsToken(String stsToken) {
        this.stsToken = stsToken;
    }

    public String getBucketName() {
        return bucketName;
    }

    public void setBucketName(String bucketName) {
        this.bucketName = bucketName;
    }

    public String getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }

    public String getEnv() {
        return env;
    }

    public void setEnv(String env) {
        this.env = env;
    }
}
