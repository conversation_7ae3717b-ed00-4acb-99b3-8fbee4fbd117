package com.extracme.evcard.membership.contract.service;

import lombok.Data;

import java.io.Serializable;

@Data
public class OrderBaseInfo implements Serializable {

    private static final long serialVersionUID = -4473846643947550115L;

    // 车牌号
    private String vehicleNo;
    // 车辆信息
    private String carInfoName;
    // 取车时间
    private String pickUpDate;
    // 还车时间 2023-08-23 08:33
    private String returnDate;
    // 租期 1天3小时
    private String rentDay;
    // 取车门店/地点
    private String pickUpStoreName;
    // 还车门店/地点
    private String returnStoreName;
    // 取车方式
    private String pickCarWay;
    // 还车方式
    private String returnCarWay;
    // 燃油类型
    private int oilType;
    // 额定油箱容量
    private String oilCapacity;
    // 额定电池包容量
    private String electricCapacity;

    // 订单号
    private String orderNo;
    // 渠道订单号
    private String thirdOrderNo;

    // 人脸活体检测结果  0：未认证 1：成功 2：失败
    private int faceLiveResult;

    // 人脸活体检测时间 2023-08-23 08:33:11
    private String faceLiveTime;

}
