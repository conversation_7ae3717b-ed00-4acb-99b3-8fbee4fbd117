package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;

public class MemberInviteInfoDto implements Serializable {
    /**
     * 主键
     */
    private Long pkId;

    /**
     * 会员编号
     */
    private String authId;

    /**
     * 享道统一用户ID
     */
    private String uid;

    /**
     * 会员类别
     */
    private Short membershipType;

    /**
     * 手机号码
     */
    private String mobilePhone;

    /**
     * 姓名
     */
    private String name;

    /**
     * 注册时间
     */
    private String regTime;

    /**
     * 邀请链接
     */
    private String shareUid;

    /**
     * 归属地
     */
    private String cityOfOrigin;

    /**
     * 归属机构id
     */
    private String orgId;

    /**
     * 归属机构名称
     */
    private String orgName;

    /**
     * 渠道
     */
    private String appKey;

    /**
     * 审核状态
     */
    private Integer reviewStatus;

    /**
     * 审核时间
     */
    private String reviewTime;

    public Long getPkId() {
        return pkId;
    }

    public void setPkId(Long pkId) {
        this.pkId = pkId;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public Short getMembershipType() {
        return membershipType;
    }

    public void setMembershipType(Short membershipType) {
        this.membershipType = membershipType;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRegTime() {
        return regTime;
    }

    public void setRegTime(String regTime) {
        this.regTime = regTime;
    }

    public String getShareUid() {
        return shareUid;
    }

    public void setShareUid(String shareUid) {
        this.shareUid = shareUid;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getCityOfOrigin() {
        return cityOfOrigin;
    }

    public void setCityOfOrigin(String cityOfOrigin) {
        this.cityOfOrigin = cityOfOrigin;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public Integer getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(Integer reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public String getReviewTime() {
        return reviewTime;
    }

    public void setReviewTime(String reviewTime) {
        this.reviewTime = reviewTime;
    }
}
