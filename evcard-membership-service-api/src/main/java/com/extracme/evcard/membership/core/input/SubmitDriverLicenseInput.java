package com.extracme.evcard.membership.core.input;


import com.extracme.evcard.membership.core.dto.OperatorDto;
import lombok.Data;

import java.io.Serializable;

/**
 * 提交驾照信息-入参
 */
@Data
public class SubmitDriverLicenseInput implements Serializable {
    /**
     * 用户id
     */
    private String mid ;
    /**
     * 渠道key
     */
    private String appKey;
    /**
     * 驾照号
     */
    private String driverCode;
    /**
     * 姓名
     */
    private String name;

    /**
     * 驾照类型 1:电子驾照 2:纸质驾照
     */
    private Integer driverLicenseType;
    /**
     * 驾照正面图片/电子驾照图片
     */
    private String driverLicenseImgUrl;
    /**
     * 驾照反面图片
     */
    private String reverseDriverLicenseImgUrl;
    /**
     * 准驾车型 C1、C2等
     */
    private String licenseType;
    /**
     * 初次领证日期 yyyy-MM-dd
    */
    private String firstObtainTime;
    /**
     * 证件时间类型 1：非长期 2：长期
     */
    private Integer expireType;
    /**
     * 过期时间 yyyy-MM-dd (长期时为null)
     */
    private String expirationDate;
    /**
     * 档案编号
     */
    private String fileNo;

    /**
     * 操作人信息
     */
    private OperatorDto operator;

    /**
     * 城市运营模式 0：大库 1：门店
     */
    private String operationModel;

    /**
     * 被占用人的mid
     */
    private String occupiedMid;
}
