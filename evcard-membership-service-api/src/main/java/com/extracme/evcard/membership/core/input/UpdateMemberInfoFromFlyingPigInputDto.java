package com.extracme.evcard.membership.core.input;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @Date 2019/10/31 8:52
 */
@Data
public class UpdateMemberInfoFromFlyingPigInputDto implements Serializable {
    private static final long serialVersionUID = 6657943432635055978L;

    /**
     * 用户id
     */
    private Long pkId;

    /** 姓名 */
    private String name;

    /** 驾照号 */
    private String driverCode;

    /** 初次领证时间 */
    private String obtainDriverTimer;

    /** 驾照有效期到期时间 */
    private String licenseExpirationTime;

    /** 驾照类型 */
    private String drivingLicenseType;

    /** 驾照图片 正页 */
    private String drivingLicenseImgUrl;

    /** 驾照副页照片地址 */
    private String fileNoImgUrl;

    /** 驾驶证档案编号	 */
    private String fileNo;

    /**
     * 人脸图片 活体检查人脸图片
     */
    private String faceRecognitionImgUrl;

    /**
     * 身份证号码
     */
    private String idCardNumber;

    /**
     * 身份证图片 正面
     */
    private String idCardPicUrl;

    /** 输入类型   0 无 1 驾照ocr识别 2 手动输入 */
    private Integer driverLicenseInputType;

    /**
     * 审核状态(0:待审核 1：审核通过 2: 审核不通过 3：用户无效 4:重新审核）
     */
    private Integer reviewStatus;
    /**
     * 认证状态 0 未认证 1 未刷脸/未上传 2 已认证
     */
    private Integer authenticationStatus;
}
