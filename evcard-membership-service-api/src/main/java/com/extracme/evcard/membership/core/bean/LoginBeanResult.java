package com.extracme.evcard.membership.core.bean;

import java.io.Serializable;

public class LoginBeanResult implements Serializable{

    private static final long serialVersionUID = 1817465158468880178L;

    //会员主键
    private Long pkId;

    //卡id
    private String cardNo;

    // 会员类型(0:外部会员 1：内部员工）
    private int memberType;

    //企业名称
    private String orgName;

    // 0:个人用户 1：企业用户
    private int orgUser;

    //user类型
    private int userType;

    //手机号码
    private String mobilePhone;

    //机构id
    private String agencyId;

    //token
    private String token;

    //	 APP类型 0：android 1：ios
    private int appType ;

    //0:未过期 1：已过期
    private int serviceExp = 0;

    //个人条款
    private int privacyPolicyExp = 0;

    //企业
    private String orgId="";

    //企业名称
    private String displayName;

    //会员id
    private String authId;

    public Long getPkId() {
        return pkId;
    }

    public void setPkId(Long pkId) {
        this.pkId = pkId;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public int getMemberType() {
        return memberType;
    }

    public void setMemberType(int memberType) {
        this.memberType = memberType;
    }

    public int getOrgUser() {
        return orgUser;
    }

    public void setOrgUser(int orgUser) {
        this.orgUser = orgUser;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public int getUserType() {
        return userType;
    }

    public void setUserType(int userType) {
        this.userType = userType;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getAgencyId() {
        return agencyId;
    }

    public void setAgencyId(String agencyId) {
        this.agencyId = agencyId;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public int getAppType() {
        return appType;
    }

    public void setAppType(int appType) {
        this.appType = appType;
    }

    public int getServiceExp() {
        return serviceExp;
    }

    public void setServiceExp(int serviceExp) {
        this.serviceExp = serviceExp;
    }

    public int getPrivacyPolicyExp() {
        return privacyPolicyExp;
    }

    public void setPrivacyPolicyExp(int privacyPolicyExp) {
        this.privacyPolicyExp = privacyPolicyExp;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

}
