package com.extracme.evcard.membership.core.dto;

import com.extracme.evcard.membership.core.input.DrivingLicenseReviewInfo;
import com.extracme.evcard.rpc.enums.StatusCode;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> @Discription
 * @date 2020/4/20
 */
@Data
public class DrivingLicenseReviewResult extends DrivingLicenseReviewInfo {
    /**
     * code=0
     * 提交驾照时， 表示数据提交成功且三要素认证通过，可进入下一步刷脸
     */
    private int code = 0;

    private String message;

    public void buildErrorCode(StatusCode statusEnum) {
        this.setCode(statusEnum.getCode());
        this.setMessage(statusEnum.getMsg());
    }
}
