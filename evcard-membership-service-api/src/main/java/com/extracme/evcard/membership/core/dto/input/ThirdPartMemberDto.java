package com.extracme.evcard.membership.core.dto.input;

import java.io.Serializable;

public class ThirdPartMemberDto implements Serializable{
    private static final long serialVersionUID = -2189975889952821071L;

    private String driverCode;

    private String name;

    private String mobilePhone;

    private String authId;

    private String drivingLicense;

    private String obtainDriverTimer;

    private String licenseExpirationTime;

    private String userImgUrl;

    private String drivingLicenseImgUrl;

    private String appKey;

    //人脸识别照片
    private String faceRecognitionImgUrl;

    private String idCardNumber;

    /**
     * 驾照类型
     */
    private String drivingLicenseType;

    public ThirdPartMemberDto() {
    }

    public ThirdPartMemberDto(String driverCode, String name, String mobilePhone, String authId, String drivingLicense, String obtainDriverTimer, String licenseExpirationTime, String userImgUrl, String drivingLicenseImgUrl, String appKey, String faceRecognitionImgUrl, String idCardNumber,String drivingLicenseType) {
        this.driverCode = driverCode;
        this.name = name;
        this.mobilePhone = mobilePhone;
        this.authId = authId;
        this.drivingLicense = drivingLicense;
        this.obtainDriverTimer = obtainDriverTimer;
        this.licenseExpirationTime = licenseExpirationTime;
        this.userImgUrl = userImgUrl;
        this.drivingLicenseImgUrl = drivingLicenseImgUrl;
        this.appKey = appKey;
        this.faceRecognitionImgUrl = faceRecognitionImgUrl;
        this.idCardNumber = idCardNumber;
        this.drivingLicenseType = drivingLicenseType;
    }

    public String getDriverCode() {
        return driverCode;
    }

    public void setDriverCode(String driverCode) {
        this.driverCode = driverCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public String getDrivingLicense() {
        return drivingLicense;
    }

    public void setDrivingLicense(String drivingLicense) {
        this.drivingLicense = drivingLicense;
    }

    public String getObtainDriverTimer() {
        return obtainDriverTimer;
    }

    public void setObtainDriverTimer(String obtainDriverTimer) {
        this.obtainDriverTimer = obtainDriverTimer;
    }

    public String getLicenseExpirationTime() {
        return licenseExpirationTime;
    }

    public void setLicenseExpirationTime(String licenseExpirationTime) {
        this.licenseExpirationTime = licenseExpirationTime;
    }

    public String getUserImgUrl() {
        return userImgUrl;
    }

    public void setUserImgUrl(String userImgUrl) {
        this.userImgUrl = userImgUrl;
    }

    public String getDrivingLicenseImgUrl() {
        return drivingLicenseImgUrl;
    }

    public void setDrivingLicenseImgUrl(String drivingLicenseImgUrl) {
        this.drivingLicenseImgUrl = drivingLicenseImgUrl;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getFaceRecognitionImgUrl() {
        return faceRecognitionImgUrl;
    }

    public void setFaceRecognitionImgUrl(String faceRecognitionImgUrl) {
        this.faceRecognitionImgUrl = faceRecognitionImgUrl;
    }

    public String getIdCardNumber() {
        return idCardNumber;
    }

    public void setIdCardNumber(String idCardNumber) {
        this.idCardNumber = idCardNumber;
    }

    public String getDrivingLicenseType() {
        return drivingLicenseType;
    }

    public void setDrivingLicenseType(String drivingLicenseType) {
        this.drivingLicenseType = drivingLicenseType;
    }
}
