package com.extracme.evcard.membership.credit.dto;

import com.extracme.evcard.rpc.dto.BaseResponse;

/**
 * Created by Elin on 2017/11/22.
 * 事件记录列表查询字段
 */
public class CreditEventRecordParamsDto extends BaseResponse {


    private static final long serialVersionUID = 9103978369109483521L;

    /**
     * 事件编号
     */
    private Long eventId;

    /**
     * 姓名
     */
    private String authName;

    /**
     * 手机号
     */
    private String mobilePhone;

    /**
     * 会员所属公司
     */
    private String orgId;

    /**
     * 事件类型名称
     */
    private String eventName;

    /**
     * 事件类型性质 1-正面 0-负面
     */
    private String eventNature;

    /**
     * 事件状态（0-撤销 1-正常）
     */
    private Integer eventStatus;

    /**
     * 事件操作时间开始
     */
    private String eventStartTime;

    /**
     * 事件操作时间结束
     */
    private String eventEndTime;

    public Long getEventId() {
        return eventId;
    }

    public void setEventId(Long eventId) {
        this.eventId = eventId;
    }

    public String getAuthName() {
        return authName;
    }

    public void setAuthName(String authName) {
        this.authName = authName;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public String getEventNature() {
        return eventNature;
    }

    public void setEventNature(String eventNature) {
        this.eventNature = eventNature;
    }

    public Integer getEventStatus() {
        return eventStatus;
    }

    public void setEventStatus(Integer eventStatus) {
        this.eventStatus = eventStatus;
    }

    public String getEventStartTime() {
        return eventStartTime;
    }

    public void setEventStartTime(String eventStartTime) {
        this.eventStartTime = eventStartTime;
    }

    public String getEventEndTime() {
        return eventEndTime;
    }

    public void setEventEndTime(String eventEndTime) {
        this.eventEndTime = eventEndTime;
    }
}