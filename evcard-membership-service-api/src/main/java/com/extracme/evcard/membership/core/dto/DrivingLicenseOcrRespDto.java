package com.extracme.evcard.membership.core.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class DrivingLicenseOcrRespDto implements Serializable {

    /**
     * 证件号
     */
    private String license_number;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别
     */
    private String gender;

    /**
     * 地址
     */
    private String address;

    /**
     * 出生日期
     */
    private String birthday;

    /**
     * 准驾车型
     */
    private String drivetype;

    /**
     * 国籍
     */
    private String nationality;

    /**
     * 有效期限（失效）
     */
    private String valid_date;

    /**
     * 有效起始日期
     */
    private String valid_from;

    /**
     * 有效期限（几年）
     */
    private String valid_for;

    /**
     * 初次领证日期
     */
    private String issue_date;

    /**
     * 发证机关
     */
    private String issued_by;

    /**
     * 档案编号 （副页）
     */
    private String file_number;

    /**
     * 记录
     */
    private String record;

    private Long logId;
    private Integer wordsResultNum;
    private Integer direction;
}
