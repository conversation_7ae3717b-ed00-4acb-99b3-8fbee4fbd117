package com.extracme.evcard.membership.core.service;

import com.extracme.evcard.membership.core.dto.*;
import com.extracme.evcard.membership.core.dto.input.*;
import com.extracme.evcard.membership.core.input.CheckDepositInput;
import com.extracme.evcard.membership.core.input.CheckDepositOrderInput;
import com.extracme.evcard.rpc.dto.PageBeanDto;

import java.util.List;

/**
 * <p>
 *  会员押金服务
 * </p>
 *
 * <AUTHOR>
 * @since 2018/7/13
 */
public interface IMemberDepositService {
    /**
     * 查询押金信息
     * @param inputDTO
     * @return
     */
    DepositInfoDto queryDepositInfo(QueryDepositInfoInputDTO inputDTO);

    /**
     * 查询押金明细
     * @param authId
     * @return
     */
    List<DepositDetailDto> queryDepositDetailList(String authId);


    /**
     * 计算押金等级和升级押金列表
     * @param inputDTO
     * @return
     */
    UpgradeDepositDto queryUpgradeDepositList(QueryUpgradeDepositInputDTO inputDTO);

    /**
     * 预约车辆时查询订单用户押金详情(网点订单使用)
     * @param checkDepositInput
     * @return
     * @since 2.24.0
     * <AUTHOR>
     */
    CheckOrderDepositResult checkDepositOrder(CheckDepositInput checkDepositInput);

    /**
     * 预约车辆时查询订单用户押金详情(门店/网点订单通用)
     * @param checkDepositOrderInput
     * @return
     */
    CheckOrderDepositResp checkDepositOrderCommon(CheckDepositOrderInput checkDepositOrderInput);

    /**
     * 下单查询押金信息
     * @param checkDepositInput
     * @return
     */
    @Deprecated
    CheckDepositResult checkDepositForOrderVehicle(CheckDepositInput checkDepositInput);

    /**
     * 获取会员押金预授权信息(基础/车辆押金预授权、芝麻信用代扣是否开启)
     * @param inputDTO
     * @return
     * @since 1.9.1
     * @remark 注销接口使用，暂未查询现金押金余额即押金等级，有需要可添加。
     */
    DepositAuthorizonDto queryAuthorizedDepositInfo(QueryDepositInfoInputDTO inputDTO);

    /**
     * 获取用户芝麻免押信息
     * @param shopSeq
     * @param authId
     * @return
     */
    Integer queryAuthZhiMaRefund(Integer shopSeq,String authId);

    /**
     * 网点是否支持芝麻信用
     * @param shopSeq
     * @return
     */
    Integer getZhiMaCreditFlagByShopSeq(int shopSeq);

    /**
     * 城市是否支持芝麻信用
     * @param cityName
     * @return 0：否 1：是
     */
    Integer getZhiMaCreditFlagByCityName(String cityName);

    /**
     * 查询可用芝麻信用城市
     * @return
     */
    List<CityDto>  canUseZhimaCitys();

    /**
     * 保存芝麻信用授权操作记录
     * @param input
     * @sicne 2.18.0
     */
    void saveZhimaAuthorizedRecord(MemberAuthorizedRecordInput input);

    /**
     * 查询芝麻免押授权及取消代扣授权记录-分页
     * @param queryInput
     * @return
     * @sicne 2.18.0
     */
    PageBeanDto<MemberAuthorizedLogDTO> queryZhimaAuthorizedRecordPage(MemberAuthorizedRecordQueryInput queryInput);

    /**
     * 查询会员芝麻免押授权及取消代扣授权记录
     * @param authId
     * @param authorizedTypes  操作类别列表
     *                         授权操作类别：0 芝麻授权 1 代扣授权 2 取消代扣
     * @param authorizedResult 操作结果
     *                         0 失败 1 成功
     * @return
     * @sicne 2.18.0
     */
    List<MemberAuthorizedLogDTO> queryUserZhimaAuthorizedRecords(String authId, List<Integer> authorizedTypes, Integer authorizedResult);
}
