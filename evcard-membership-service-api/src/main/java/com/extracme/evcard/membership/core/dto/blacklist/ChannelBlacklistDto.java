package com.extracme.evcard.membership.core.dto.blacklist;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *  渠道黑名单信息
 */
@Data
public class ChannelBlacklistDto implements Serializable {

    /**
     * 主键
     *
     */
    private Long id;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 手机号
     */
    private String mobilePhone;

    /**
     * 黑名单类型：1=黑名单
     */
    private Integer blacklistType;

    /**
     * 证件类型: 1=身份证 2=其他
     */
    private Integer certificateType;

    /**
     * 证件号码
     */
    private String certificateNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 启用状态:1= 启用 2=禁用
     */
    private Integer enableStatus;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 更新人
     */
    private String updateBy;
}
