package com.extracme.evcard.membership.core.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Discription
 * @date 2019/12/25
 */
@Data
public class DriverLicenseDetailDTO implements Serializable {
    /**
     * 驾照号
     */
    private String driverCode;

    /**
     * 驾照证状态
     */
    private String licenseStatus;

    /**
     * 档案编号
     */
    private String fileNo;
    /**
     * 用户姓名
     */
    private String name;
    /**
     * 准驾车型
     */
    private String quasiDrivingType;
    /**
     * 累计计分
     */
    private String scoring;
    /**
     * 初次领证日期
     */
    private String firstIssueDate;
    /**
     * 有效期至
     */
    private String expireDate;

    /**
     * 有效期
     */
    private String startDate;

    /**
     * 签发机关
     */
    private String issuingAuthority;

}
