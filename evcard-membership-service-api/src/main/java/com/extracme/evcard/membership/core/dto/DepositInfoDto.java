package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 用户押金信息(押金金额)
 * </p>
 *
 * <AUTHOR>
 * @since 2018/07/13
 */
public class DepositInfoDto implements Serializable{

    private static final long serialVersionUID = 3661548817837161163L;

    /** 免押等级  0 不免押  1 免基础  2 免标准  3 免中端  4 免高端  */
    private Integer freeDepositLevel = 0;
    /** 基础押金 */
    private BigDecimal baseDeposit = BigDecimal.ZERO;
    /** 基础押金充值类型 1 现金 2 预授权   */
    private Integer baseDepositType;
    /** 车辆押金 */
    private BigDecimal vehicleDeposit = BigDecimal.ZERO;
    /** 车辆押金押金充值类型 1 现金 2 预授权   */
    private Integer vehicleDepositType;
    /**
     * 行车押金
     */
    private BigDecimal drivingDeposit = BigDecimal.ZERO;
    /** 押金当前级别描述 */
    private String depositLevelDesc;
    /** 押金当前级别 */
    private Integer depositLevel = 0;
    /** 免基础押金 */
    private BigDecimal freeBaseDeposit = BigDecimal.ZERO;
    /** 免车辆押金 */
    private BigDecimal freeVehicleDeposit = BigDecimal.ZERO ;
    /**
     * 免行车押金
     */
    private BigDecimal freeDrivingDeposit = BigDecimal.ZERO ;
    /**
     * 现金充值押金金额
     */
    private BigDecimal cashDeposit = BigDecimal.ZERO ;
    /** 总押金金额 */
    private BigDecimal totalDepositAmount = BigDecimal.ZERO;
    /** 标准基础押金 */
    private BigDecimal standardBaseDeposit;

    private List<ListPageDto> upgradeDepositList;

    /** 预授权记录 */
    private List<PreAuthRecordDto> preAuthorizationRecordList;

    /** 当前城市是否支持芝麻免押 0否 1是 */
    private Integer citySupportZhiMaFree;

    /** 是否已芝麻免押 0否 1是 */
    private Integer freeZhiMaFlag;

    /** 已冻结押金 */
    private BigDecimal frozenDeposit = BigDecimal.ZERO;

    /**
     * 押金冻结状态
     * 1长期未发生资金变动,已被冻结,去解冻 2账户证件已过期,已被冻结,去解冻 3新证件信息错误,解冻失败,去查看 4由于其他原因已被冻结,去联系客服 5证件即将到期，去更新
     */
    private Integer frozenStatue;

    /** 押金冻结状态描述	 */
    private String frozenDesc;

    // app5.8 新增押余额总金额
    private BigDecimal newTotalDeposit = BigDecimal.ZERO;

    public BigDecimal getNewTotalDeposit() {
        return newTotalDeposit;
    }

    public void setNewTotalDeposit(BigDecimal newTotalDeposit) {
        this.newTotalDeposit = newTotalDeposit;
    }

    public BigDecimal getCashDeposit() {
        return cashDeposit;
    }

    public void setCashDeposit(BigDecimal cashDeposit) {
        this.cashDeposit = cashDeposit;
    }

    public Integer getCitySupportZhiMaFree() {
        return citySupportZhiMaFree;
    }

    public void setCitySupportZhiMaFree(Integer citySupportZhiMaFree) {
        this.citySupportZhiMaFree = citySupportZhiMaFree;
    }

    public Integer getFreeZhiMaFlag() {
        return freeZhiMaFlag;
    }

    public void setFreeZhiMaFlag(Integer freeZhiMaFlag) {
        this.freeZhiMaFlag = freeZhiMaFlag;
    }

    public List<PreAuthRecordDto> getPreAuthorizationRecordList() {
        return preAuthorizationRecordList;
    }

    public void setPreAuthorizationRecordList(List<PreAuthRecordDto> preAuthorizationRecordList) {
        this.preAuthorizationRecordList = preAuthorizationRecordList;
    }

    public BigDecimal getDrivingDeposit() {
        return drivingDeposit;
    }

    public void setDrivingDeposit(BigDecimal drivingDeposit) {
        this.drivingDeposit = drivingDeposit;
    }

    public BigDecimal getFreeDrivingDeposit() {
        return freeDrivingDeposit;
    }

    public void setFreeDrivingDeposit(BigDecimal freeDrivingDeposit) {
        this.freeDrivingDeposit = freeDrivingDeposit;
    }

    public List<ListPageDto> getUpgradeDepositList() {
        return upgradeDepositList;
    }

    public void setUpgradeDepositList(List<ListPageDto> upgradeDepositList) {
        this.upgradeDepositList = upgradeDepositList;
    }

    public BigDecimal getStandardBaseDeposit() {
        return standardBaseDeposit;
    }

    public void setStandardBaseDeposit(BigDecimal standardBaseDeposit) {
        this.standardBaseDeposit = standardBaseDeposit;
    }

    public Integer getFreeDepositLevel() {
        return freeDepositLevel;
    }

    public void setFreeDepositLevel(Integer freeDepositLevel) {
        this.freeDepositLevel = freeDepositLevel;
    }

    public BigDecimal getBaseDeposit() {
        return baseDeposit;
    }

    public void setBaseDeposit(BigDecimal baseDeposit) {
        this.baseDeposit = baseDeposit;
    }

    public Integer getBaseDepositType() {
        return baseDepositType;
    }

    public void setBaseDepositType(Integer baseDepositType) {
        this.baseDepositType = baseDepositType;
    }

    public BigDecimal getVehicleDeposit() {
        return vehicleDeposit;
    }

    public void setVehicleDeposit(BigDecimal vehicleDeposit) {
        this.vehicleDeposit = vehicleDeposit;
    }

    public Integer getVehicleDepositType() {
        return vehicleDepositType;
    }

    public void setVehicleDepositType(Integer vehicleDepositType) {
        this.vehicleDepositType = vehicleDepositType;
    }

    public String getDepositLevelDesc() {
        return depositLevelDesc;
    }

    public void setDepositLevelDesc(String depositLevelDesc) {
        this.depositLevelDesc = depositLevelDesc;
    }

    public Integer getDepositLevel() {
        return depositLevel;
    }

    public void setDepositLevel(Integer depositLevel) {
        this.depositLevel = depositLevel;
    }

    public BigDecimal getFreeBaseDeposit() {
        return freeBaseDeposit;
    }

    public void setFreeBaseDeposit(BigDecimal freeBaseDeposit) {
        this.freeBaseDeposit = freeBaseDeposit;
    }

    public BigDecimal getFreeVehicleDeposit() {
        return freeVehicleDeposit;
    }

    public void setFreeVehicleDeposit(BigDecimal freeVehicleDeposit) {
        this.freeVehicleDeposit = freeVehicleDeposit;
    }

    public BigDecimal getTotalDepositAmount() {
        return totalDepositAmount;
    }

    public void setTotalDepositAmount(BigDecimal totalDepositAmount) {
        this.totalDepositAmount = totalDepositAmount;
    }

    public BigDecimal getFrozenDeposit() {
        return frozenDeposit;
    }

    public void setFrozenDeposit(BigDecimal frozenDeposit) {
        this.frozenDeposit = frozenDeposit;
    }

    public Integer getFrozenStatue() {
        return frozenStatue;
    }

    public void setFrozenStatue(Integer frozenStatue) {
        this.frozenStatue = frozenStatue;
    }

    public String getFrozenDesc() {
        return frozenDesc;
    }

    public void setFrozenDesc(String frozenDesc) {
        this.frozenDesc = frozenDesc;
    }
}
