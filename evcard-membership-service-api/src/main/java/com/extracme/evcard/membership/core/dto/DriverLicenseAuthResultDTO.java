package com.extracme.evcard.membership.core.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 驾照三要素识别结果
 * <AUTHOR>
 * @date 2019/8/21
 */
@Data
public class DriverLicenseAuthResultDTO implements Serializable {
    /**
     * 认证记录id
     */
    private Long id;

    /**
     * 会员pk_id
     */
    private Long userId;

    /**
     * 认证时姓名
     */
    private String name;

    /**
     * 认证时驾照编号
     */
    private String driverCode;

    /**
     * 认证时驾照副本编号
     */
    private String fileNo;

    /**
     * 认证状态 0待认证 1认证通过 2认证不通过 3查证中(待重新认证)
     */
    private Integer authenticateStatus;

    /**
     * 驾照状态 0待认证  1认证通过  2认证不通过（驾照有效期/准驾车型/驾照状态不符合用车条件）
     */
    private Integer licenseStatus;

    /**
     * 驾照状态描述
     */
    private String licenseStatusMsg;

    private Date createTime;

    private Long createOperId;

    private String createOperName;

    private Date updateTime;

    private Long updateOperId;

    private String updateOperName;
}
