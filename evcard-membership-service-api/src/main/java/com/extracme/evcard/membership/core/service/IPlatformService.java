package com.extracme.evcard.membership.core.service;

import com.extracme.evcard.membership.core.dto.AppKeyDto;
import com.extracme.evcard.membership.core.dto.PlatformInfoDTO;

import java.util.List;

public interface IPlatformService {
    /**
     * 查询全部平台列表
     * @return
     * @since 2.3.0
     */
    List<PlatformInfoDTO> getAllPlatforms();

    /**
     * 查询指定平台关联的渠道列表
     * @param platformId
     * @param filterUnabled 是否过滤被禁用渠道
     * @return 渠道appKey列表
     * @since 2.3.0
     */
    List<String> getAppKeysByPlatformId(Long platformId, Boolean filterUnabled);

    /**
     * 查询渠道列表
     * @param platformId 所属平台id
     * @param channelPurpose 渠道用途： 1注册拉新 2营销活动 3订单
     * @param filterUnabled 是否过滤被禁用渠道
     * @return 2.3.1
     */
    List<AppKeyDto> getAppKeys(Long platformId, String channelPurpose, Boolean filterUnabled);

    /**
     * 根据key获取渠道信息.
     * @param appKey
     * @return
     */
    AppKeyDto getAppKey(String appKey);

    /**
     * 获取所有生效的appkey信息.
     * @return
     */
    List<AppKeyDto> getEffectiveAppKeyInfo();
}
