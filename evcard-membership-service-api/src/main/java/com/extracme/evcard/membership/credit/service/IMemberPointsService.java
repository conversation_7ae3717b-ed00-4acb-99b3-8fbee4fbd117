package com.extracme.evcard.membership.credit.service;

import com.extracme.evcard.membership.core.dto.UserTitlePointsOfferDto;
import com.extracme.evcard.membership.core.dto.UserTitlePointsOfferSingleDto;
import com.extracme.evcard.membership.core.dto.UserTitlePointsQueryDto;
import com.extracme.evcard.membership.core.dto.UserTitleUpgradePointsDto;
import com.extracme.evcard.membership.core.exception.MemberException;
import com.extracme.evcard.membership.credit.dto.*;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.dto.PageBeanDto;

import java.util.List;

/**
 * 会员积分服务
 * <AUTHOR>
 * @Discription
 * @date 2020/8/12
 */
public interface IMemberPointsService {

    /**
     * 会员积分发放异步-mq
     * 采用独立的积分事件，若发放失败，可对积分消息单独做重新消费
     * @param offerDto
     * @return
     * @remark 目前全部积分采用此mqPush接口
     */
    BaseResponse asyncOfferPoints(MemberPointsOfferDto offerDto);

    /**
     * 会员积分发放（同步）
     * @param offerDto
     * @return
     */
    MemberPointsOfferResp offerPoints(MemberPointsOfferDto offerDto) throws MemberException;

    /**
     * 会员积分变更履历
     * @param queryDto
     * @return
     */
    PageBeanDto<MemberPointsHistoryDto> queryUserHistoryPage(MemberPointsHistoryQueryDto queryDto);


    /**
     * 会员个人积分账户详情
     * @param queryDto
     * @return
     * @ramrk 缓存5min，最晚5min钟后更新
     */
    MemberPointsAccountDto queryUserAccount(MemberPointsAccountQueryDto queryDto);

    /**
     * 获取指定操作将发放的积分数
     * @param queryDto
     * @return
     * @remark 添加缓存
     * @since 3.19.0
     */
    MemberPointsGainQueryResp getGainCredits(MemberPointsGainQueryDto queryDto);

    /**
     * 查询指定操作将发放的积分数
     * @param queryDto
     * @return
     * @remark 直接请求供应商接口，无缓存
     */
    MemberPointsGainQueryResp queryGainCredits(MemberPointsGainQueryDto queryDto);

    /**
     * 查询用户荣誉称号可获得的积分值
     * @param queryDto
     * @return
     * @since 3.19.0
     */
    List<UserTitleUpgradePointsDto> queryGainCredits(UserTitlePointsQueryDto queryDto);

    /**
     * 批量发放用户荣誉称号奖励积分
     * @param offerDto
     * @return
     * @throws MemberException
     * @since 3.19.0
     * @remark 推送mq消息
     */
    BaseResponse offerTitleRewardPoints(UserTitlePointsOfferDto offerDto) throws MemberException;

    /**
     * 同步调用供应商接口，发放用户荣誉称号奖励积分
     * @param offerDto
     */
    MemberPointsOfferResp offerTitleRewardPointsSync(UserTitlePointsOfferSingleDto offerDto)  throws MemberException;
}
