package com.extracme.evcard.membership.core.service;

import com.extracme.evcard.membership.core.dto.*;
import com.extracme.evcard.membership.core.dto.input.MemberContractQueryInput;
import com.extracme.evcard.membership.core.dto.input.ProvisionUserFeedbackInput;
import com.extracme.evcard.membership.core.dto.input.ProvisionUserTagInput;
import com.extracme.evcard.membership.core.exception.MemberException;
import com.extracme.evcard.rpc.dto.Page;
import com.extracme.evcard.rpc.dto.PageBeanDto;
import com.extracme.evcard.rpc.exception.BusinessRuntimeException;

import java.util.List;

/**
 * 会员条款
 * <AUTHOR>
 * @Discription
 * @date 2020/9/23
 * @remak 目前仅包含问题及问题分类的h5查询接口
 * @history 增加指定时刻点后最近签署的协议接口
 */
public interface IMemberProvisionService {

    /**
     * 查询问题分类列表
     * @param type 1：会员守则 2：隐私政策 3：Q&A
     * @return
     * @since 3.6.9
     * @remark app-常见问题h5页面使用，有序
     */
    ProvisionGroupViewDto getProvisionGroups(int type);

    /**
     * 查询指定问题分类下的问题列表
     * @param groupId 当前选中的分组id
     * @return
     * @since 3.6.9
     * @remark app-常见问题h5页面使用
     */
    List<ProvisionNodeDto> getProvisionNodesOfGroup(Long groupId);

    /**
     * 用户为注常见问题项添加标签
     * @param authId 非必传，不提供时自动产生匿名id号
     * @param input
     *          tag:  标签 -1取消标签 0：有帮助 1：没啥用
     *          tagId: 标签记录ID(添加时返回)， 取消标签时，tagId必传
     * @return  标签记录id
     * @throws MemberException
     * @since 3.8.0
     * @remark app-常见问题h5页面使用
     */
    Long addNodeTag(String authId, ProvisionUserTagInput input) throws MemberException;

    /**
     * 用户为注常见问题项提交建议
     * @param authId
     * @param input
     * @throws MemberException
     * @since 3.6.9
     * @remark app-常见问题h5页面使用
     */
    void submitNodeFeedback(String authId, ProvisionUserFeedbackInput input) throws MemberException;

    /**
     * 查询指定常见问题的用户标注统计
     * @param provisionId
     * @param nodeId
     * @return
     */
    ProvisionUserTagDto queryUserTags(Long provisionId, String nodeId);

    /**
     * 查询指定常见问题的用户建议信息
     * @param input
     * @param page
     * @return
     */
    PageBeanDto<ProvisionUserFeedbackDto> queryUserFeedbacks(ProvisionFeedbackQueryDto input, Page page);

    /**
     * 查询指定时刻点用户签署的最新的条款
     * @param input
     *        authId
     *        provisionType 条款类型 （1：会员守则 2：隐私政策）
     *        date 此时刻点前，签署的最后一份协议
     * @return
     * @since 3.10.0
     * @remark 车管系统使用，查询订单关联的用户合同
     */
    UserContractViewDto queryMemberContract(MemberContractQueryInput input) throws BusinessRuntimeException;

    /**
     * 获取会员指定版本协议的实际归档路径
     * @param authId
     * @param templateId 如 SZ0098
     * @return 其中， archiveUrl为归档地址全路径
     * @since 3.20.1
     */
    UserContractInfo getUserContractArchiveUrl(String authId, String templateId);

    /**
     * 查询指定时刻点后用户签署的第一份条款(最临近条款)
     * @param input
     *        authId
     *        provisionType 条款类型 （1：会员守则 2：隐私政策）
     *        date 签署时间>=此时间
     *        versionId 版本号>=此版本号
     * @return
     * @since 3.26.0
     * @remark 车管系统使用
     */
    UserContractViewDto getMemberCloseContract(MemberContractQueryInput input) throws BusinessRuntimeException;
}
