package com.extracme.evcard.membership.core.enums;

import org.apache.commons.lang3.StringUtils;

public enum MemOperateTypeEnum {
    /**
     * 0 OCR识别 1 人脸认证 2 审核  3 账户注销  4 账户恢复
     * 5 密码修改 6 密码重置 7 密码找回 8 修改手机号
     * 9 更换设备绑定 10 注销失败 11.清除绑定设备*/

    OCR(0L, "OCR识别"),
    FACE_REC(1L, "人脸认证"),
    REVIEW(2L, "审核"),
    UNREGISTER(3L, "账户注销"),
    RECOVER(4L, "账户恢复"),
    CHANGE_PWD(5L, "密码修改"),
    RESET_PWD(6L, "密码重置"),
    FIND_PWD(7L, "密码找回"),
    CHANGE_MOBILE(8L, "修改手机号"),
    BIND_IMEI(9L, "更换设备绑定"),
    UNREGISTER_FAILED(10L, "注销失败"),
    CLEAN_BIND_IMEI(11L, "清除绑定设备"),
    UPDATE_EMAL(12L, "修改会员邮箱"),
    UPDATE_ADDR(13L, "修改会员地址"),
    CARD_PAUSE(14L, "暂停会员卡"),
    CARD_RESUME(15L, "恢复会员卡"),
    CARD_CANCEL(16L, "注销会员卡"),
    FACE_REC_FAILED(17L, "人脸认证失败"),
    FACE_REC_OPEN(18L, "刷脸开门人脸认证"),
    UPDATE_FILE_NO(19L, "档案编号更新"),
    FACE_REC_CALL(20L, "调用上汽人脸认证"),
    REVIEW_NEW_LICENSE(21L, "新驾照审核"),
    LIC_AUTH(22L, "驾照三要素认证"),
    UPDATE_MEM_STATUS(23L, "驾照过期变更会员审核状态"),
    SUBMIT_LICENSE(24L, "提交驾照信息"),
    UPDATE_LICENSE_EXPIRE_DATE(25L, "更新驾照有效期"),
    UPDATE_LICENSE_AUTH_STAUTS(26L, "更新驾照认证状态"),
    LICENSE_FRAUDULENT(27L, "驾照冒用"),
    UPDATE_SAIC_UID(1000L, "更新享道统一用户ID"),
    UN_LOGIN_SUBMIT_CHANGE_MOBILE(28L, "提交修改手机号（未登录）"),
    LOGIN_SUBMIT_CHANGE_MOBILE(29L, "提交修改手机号（已登录）"),

    IDCARD_SUBMIT_DATA(100L, "身份证件-提交资料"),
    IDCARD_AUTO_AUDIT_PASS(101L, "身份证件自动认证通过(实名认证)"),
    IDCARD_AUTO_AUDIT_NOT_PASS(102L, "身份证件实名认证不通过"),
    IDCARD_MANUAL_AUDIT_PASS(103L, "身份证件人工审核通过"),
    IDCARD_MANUAL_AUDIT_NOT_PASS(104L, "身份证件人工审核不通过"),
    IDCARD_MANUAL_REAUDIT(105L, "身份证件人工重新审核"),
    SUBMIT_IDCARD_AUTO_AUDIT_PASS(106L, "身份证件自动认证通过(提交资料)"),
    SUBMIT_REVIEW_AFTER_FACE_REC(107L, "刷脸完成-身份证件提交人工审核"),

    LICENSE_SUBMIT_DATA(110L, "驾驶证-提交资料"),
    LICENSE_MANUAL_AUDIT_PASS(111L, "驾驶证人工审核通过"),
    LICENSE_MANUAL_AUDIT_NOT_PASS(112L, "驾驶证人工审核不通过"),
    LICENSE_MANUAL_REAUDIT(113L, "驾驶证人工重新审核"),

    BIND_THIRD_ACCOUNT(114L, "绑定第三方账号"),
    UNBIND_THIRD_ACCOUNT(115L, "解绑第三方账号"),

    IDCARD_MEMBER_REAUDIT(116L, "身份证件重新认证"),

    LICENSE_MEMBER_REAUDIT(117L, "驾驶证重新认证");

    //UPDATE_MEM_STATUS(27L, "驾照冒用");


    MemOperateTypeEnum(Long code, String operate) {
        this.code = code;
        this.operate = operate;
    }

    private Long code;
    private String operate;

    public Long getCode() {
        return code;
    }

    public void setCode(Long code) {
        this.code = code;
    }

    public String getOperate() {
        return operate;
    }

    public void setOperate(String operate) {
        this.operate = operate;
    }

    public static String getOperateByCode(Long code) {
        if (code == null) {
            return StringUtils.EMPTY;
        }
        for (MemOperateTypeEnum value : MemOperateTypeEnum.values()) {
            if (value.getCode() == code) {
                return value.getOperate();
            }
        }
        return StringUtils.EMPTY;
    }
}
