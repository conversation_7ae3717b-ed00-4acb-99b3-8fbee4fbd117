package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 会员条款记录
 * </p>
 *
 * <AUTHOR>
 * @date 2019/3/8
 */
public class MemberClauseRecord implements Serializable {
    private static final long serialVersionUID = 3905275771962929886L;

    private String version;

    /** 类型（1：会员守则 2：隐私政策 3：Q&A） */
    private Integer type;

    private Date startTime;

    private Date endTime;

    private Date createTime;

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
