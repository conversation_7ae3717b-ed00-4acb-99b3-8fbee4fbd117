package com.extracme.evcard.membership.core.input;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2019/3/8
 */
public class QueryMemberClauseRecordInput implements Serializable {

    private static final long serialVersionUID = 7798780076084852228L;
    private String authId;

    private Integer pageNum;

    private Integer pageSize;

    public QueryMemberClauseRecordInput() {
    }

    public QueryMemberClauseRecordInput(String authId, Integer pageNum, Integer pageSize) {
        this.authId = authId;
        this.pageNum = pageNum;
        this.pageSize = pageSize;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public Integer getPageNum() {
        if(pageNum == null || pageNum < 1){
            return 1;
        }
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        if(pageSize == null || pageSize < 0){
            return 10;
        }
        return  pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}
