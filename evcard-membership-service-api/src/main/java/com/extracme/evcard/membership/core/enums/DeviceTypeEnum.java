package com.extracme.evcard.membership.core.enums;

/**
 * 设备类型枚举
 * 
 * <AUTHOR>
 * @since 2025-08-20
 */
public enum DeviceTypeEnum {
    
    /**
     * 移动端
     */
    MOBILE("MOBILE", "移动端"),
    
    /**
     * 网页端
     */
    WEB("WEB", "网页端"),
    
    /**
     * 小程序
     */
    MINI_PROGRAM("MINI_PROGRAM", "小程序"),
    
    /**
     * API接口
     */
    API("API", "API接口"),
    
    /**
     * 其他
     */
    OTHER("OTHER", "其他");
    
    private final String code;
    private final String description;
    
    DeviceTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    public static DeviceTypeEnum fromCode(String code) {
        for (DeviceTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return OTHER; // 默认返回其他类型
    }
}
