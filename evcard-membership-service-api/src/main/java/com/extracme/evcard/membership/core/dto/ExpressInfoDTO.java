package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;

/**
 * 物流信息
 * <AUTHOR>
 */
public class ExpressInfoDTO implements Serializable{

    private static final long serialVersionUID = 2081401482612291823L;
    private String expressNo;

    private String mobilePhone;

    private String expressName;

    private Integer supplementsFlag;

    private Integer expireFlag;

    private String expressAddress;

    private String createdTime;

    private String createdUser;

    private String authId;

    public String getExpressNo() {
        return expressNo;
    }

    public void setExpressNo(String expressNo) {
        this.expressNo = expressNo;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getExpressName() {
        return expressName;
    }

    public void setExpressName(String expressName) {
        this.expressName = expressName;
    }

    public Integer getSupplementsFlag() {
        return supplementsFlag;
    }

    public void setSupplementsFlag(Integer supplementsFlag) {
        this.supplementsFlag = supplementsFlag;
    }

    public Integer getExpireFlag() {
        return expireFlag;
    }

    public void setExpireFlag(Integer expireFlag) {
        this.expireFlag = expireFlag;
    }

    public String getExpressAddress() {
        return expressAddress;
    }

    public void setExpressAddress(String expressAddress) {
        this.expressAddress = expressAddress;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public String getCreatedUser() {
        return createdUser;
    }

    public void setCreatedUser(String createdUser) {
        this.createdUser = createdUser;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    @Override
    public String toString() {
        return "ExpressInfoDTO{" +
                "expressNo='" + expressNo + '\'' +
                ", mobilePhone='" + mobilePhone + '\'' +
                ", expressName='" + expressName + '\'' +
                ", supplementsFlag=" + supplementsFlag +
                ", expireFlag=" + expireFlag +
                ", expressAddress='" + expressAddress + '\'' +
                ", createdTime='" + createdTime + '\'' +
                ", createdUser='" + createdUser + '\'' +
                ", authId='" + authId + '\'' +
                '}';
    }
}