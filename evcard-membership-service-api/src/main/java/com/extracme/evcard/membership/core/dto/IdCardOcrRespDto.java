package com.extracme.evcard.membership.core.dto;

import com.extracme.evcard.rpc.dto.BaseResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class IdCardOcrRespDto extends BaseResponse implements Serializable {
    /**
     * 身份证id
     */
    private String idCardNo;
    /**
     * 姓名
     */
    private String name;
    /**
     * 证件到期时间类别： 1非长期  2长期
     */
    private int expireType;
    /**
     * 证件到期日期，yyyy-MM-dd (长期时，不返回)
     */
    private String expirationDate;

    /**
     * normal/copy/temporary/screen/ps/unknown
     */
    private String riskType;

    /**
     * 若识别到为非法账号，则给予此提示
     */
    private String riskTypeDesc;

    /**
     * 1:成功 2:识别失败 3:不支持复印件 4:不支持翻拍 5:不支持PS  6其他
     */
    private int state = 1;

    /**
     * 签发机构
     */
    private String issueBy;

    /**
     * 签发日期，yyyyMMdd
     */
    private String issueDate;
    /**
     * 出生日期，yyyyMMdd
     */
    private String birthday;
    /**
     * 地址
     */
    private String address;
    /**
     * 性别
     */
    private String gender;
    /**
     * 民族
     */
    private String nation;

    public IdCardOcrRespDto(int code, String message) {
        super(code, message);
    }

    public void setCodeMessage(int code, String message) {
        super.setCode(code);
        super.setMessage(message);
    }
}
