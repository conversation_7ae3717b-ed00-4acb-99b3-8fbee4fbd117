package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;

/**
 * 会员注销-入参
 */
public class UnregisterDto implements Serializable{
    /**
     * 会员编号， 必填。
     */
    private String authId;

    /**
     * 会员类型， 必填。
     */
    private Short membershipType;

    /**
     * 会员手机号， 恢复时，必填。
     */
    private String mobile;

    /**
     * 注销通道: 非必填
     * 暂定： 待确认。 0：无 1：会员系统 2：客服系统 3:手机APP(默认值)  4：支付宝小程序 。
    */
    private String unregisterOrigin;

    /**
     * 操作人： 非必填
     * 通过管理平台（非本人操作）时必须。
     */
    private Long operateUserId;

    /**
     * 操作人姓名： 非必填
     * 通过管理平台（非本人操作）时必须。
     */
    private String operateUserName;

    /**
     * 注销原因：非必填。
     * 通过管理平台（非本人操作）时必须。
     */
    private String unregisteReason;

    /**
     * 是否二次确认标记。
     * true 特定检查项将二次确认，包括E币余额为0检查项
     * false 不再确认强制注销，
     * 缺省为true
     * @remark 版本4.1.0追加
     */
    private Boolean confirmFlag = true;

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public Short getMembershipType() {
        return membershipType;
    }

    public void setMembershipType(Short membershipType) {
        this.membershipType = membershipType;
    }

    public String getUnregisterOrigin() {
        return unregisterOrigin;
    }

    public void setUnregisterOrigin(String unregisterOrigin) {
        this.unregisterOrigin = unregisterOrigin;
    }

    public Long getOperateUserId() {
        return operateUserId;
    }

    public void setOperateUserId(Long operateUserId) {
        this.operateUserId = operateUserId;
    }

    public String getOperateUserName() {
        return operateUserName;
    }

    public void setOperateUserName(String operateUserName) {
        this.operateUserName = operateUserName;
    }

    public String getUnregisteReason() {
        return unregisteReason;
    }

    public void setUnregisteReason(String unregisteReason) {
        this.unregisteReason = unregisteReason;
    }

    public String getMobile() { return mobile; }

    public void setMobile(String mobile) { this.mobile = mobile; }

    public Boolean getConfirmFlag() {
        return confirmFlag;
    }

    public void setConfirmFlag(Boolean confirmFlag) {
        this.confirmFlag = confirmFlag;
    }
}
