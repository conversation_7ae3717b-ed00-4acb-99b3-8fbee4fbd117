package com.extracme.evcard.membership.core.dto;

import com.extracme.evcard.rpc.dto.BaseResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OcrAndSubmitIdentityRespDto extends BaseResponse implements Serializable {

    private static final long serialVersionUID = 8278331292781143826L;
    /**
     * 提交结果
     * 认证结果 1:成功 2:操作频繁 3:身份证已被上个注销账号使用 4:身份证已被提交认证 5:身份证格式错误 6:身份证已过期 7:手持护照与人脸照片不一致
     * 8:有效期不在原认证有效期之后 9:身份证编号与原认证不一致(8,9更新即将过期、过期身份证判断)
     * 10:姓名与原认证不一致
     * 11:ocr失败
     */
    private int state;

    /**
     * 提交结果描述
     */
    private String msg;

    // 证件信息

    /**
     * 身份证id
     */
    private String idCardNo;
    /**
     * 姓名
     */
    private String name;
    /**
     * 证件到期时间类别： 1非长期  2长期
     */
    private int expireType;
    /**
     * 证件到期日期，yyyy-MM-dd (长期时，不返回)
     */
    private String expirationDate;
    /**
     * 签发日期，yyyyMMdd
     */
    private String issueDate;
    /**
     * 出生日期，yyyyMMdd
     */
    private String birthday;
    /**
     * 地址
     */
    private String address;
    /**
     * 性别
     */
    private String gender;
    /**
     * 民族
     */
    private String nation;

    public OcrAndSubmitIdentityRespDto(int code, String message) {
        super(code, message);
    }

    public void setCodeMessage(int code, String message) {
        super.setCode(code);
        super.setMessage(message);
    }
}
