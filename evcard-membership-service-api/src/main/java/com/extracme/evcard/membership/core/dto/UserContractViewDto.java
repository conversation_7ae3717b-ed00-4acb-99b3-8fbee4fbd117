package com.extracme.evcard.membership.core.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/9/24
 */
@Data
public class UserContractViewDto implements Serializable {
    /**
     * 协议签署记录id
     */
    private Long id;

    /**
     * 会员id
     */
    private String authId;

    /**
     * 会员编号-法大大
     */
    private String customerId;

    /**
     * 交易id-法大大
     */
    private String transactionId;
    /**
     * 合约id-法大大
     */
    private String contractId;

    /**
     * 会员合约供应商
     * 0 法大大 1 e签宝
     * @since 3.19.1
     */
    private Integer supplier;

    /**
     * 会员oss地址(归档地址)
     * @since 3.19.1
     * @remark 后续法大大合约文件供应商将删除，
     *         请使用此地址
     */
    private String archiveUrl;

    /**
     * 下载地址-法大大
     */
    @Deprecated
    private String downloadUrl;
    /**
     * 查询地址法大大
     */
    @Deprecated
    private String viewpdfUrl;

    /**
     * 条款-版本id， 如0043
     */
    private String versionId;

    /**
     * 条款版本, 如SZ0043
     */
    private String templateId;

    /**
     * 签署时间
     */
    private Date createTime;

    /**
     * 条款信息
     */
    private ProvisionDetailDto provisionInfo;
}
