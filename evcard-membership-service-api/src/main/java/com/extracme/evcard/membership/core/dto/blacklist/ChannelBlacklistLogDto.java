package com.extracme.evcard.membership.core.dto.blacklist;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *  渠道黑名单日志信息
 */
@Data
public class ChannelBlacklistLogDto implements Serializable {

    /**
     * 主键
     *
     */
    private Long id;

    /**
     * 渠道黑名单id
     */
    private Long channelBlacklistId;

    /**
     * 日志内容
     */
    private String content;

    /**
     * 创建时间
     */
    private Date createTime;

    private String operateTime;

    /**
     * 创建人
     */
    private String createBy;

}
