package com.extracme.evcard.membership.core.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/9/25
 */
@Data
public class ProvisionUserFeedbackDto implements Serializable {
    private Long id;

    private String nodeId;

    /**
     * 创建该问题项的条款id
     */
    private Long provisionId;

    /**
     * 用户id
     */
    private String authId;

    /**
     * 建议内容
     */
    private String content;

    /**
     * 提交时间
     */
    private Date createTime;

    private Long createOperId;

    private String createOperName;
}
