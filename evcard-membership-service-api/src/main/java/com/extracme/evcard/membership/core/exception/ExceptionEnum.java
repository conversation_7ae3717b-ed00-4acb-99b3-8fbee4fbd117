package com.extracme.evcard.membership.core.exception;

/**
 *
 * <pre>
 * 异常枚举
 * </pre>
 *
 * <AUTHOR>
 * @since 2017/7/7
 * @version 1.0.0
 */
public enum ExceptionEnum {

    /**
     * 检查参数正确及完整性
     */
    PARAM_ERROR(10001,"检查参数正确及完整性"),

    /**
     * 会员不存在
     */
    MEMBER_NOT_EXIST(20001,"会员不存在"),
    /**
     * 押金等级基础信息查询失败
     */
    DEPOSIT_BASE_INFO_NOT_EXIST(20002,"押金等级基础信息查询失败"),

    /**
     * 网点不存在
     */
    SHOP_NOT_EXIST(-1,"网点不存在"),

    // 企业折扣错误
    BIND_ROLE_NOT_BELONG_TO_AGENCY(-1000, "绑定角色不属于该机构"),

    // 企业折扣错误
    ENTERPRISE_DISCOUNT_EXPIRED(-1002, "不满足企业折扣有效期限制"),
    ENTERPRISE_DISCOUNT_EXCEEDS_QUOTA(-1003, "超过企业折扣人数限制"),

    // 个人折扣错误
    PERSONAL_DISCOUNT_EXPIRED(-1004, "不满足个人折扣有效期限制"),
    PERSONAL_DISCOUNT_EXCEEDS_QUOTA(-1005, "超过个人折扣人数限制"),
    AGENCY_STATUS_STOP(-1006, "企业合作已终止"),

    AGENCY_DISCOUNT_EXPIRED(-1007, "企业折扣有效期已结束"),
    NEW_AGENCY_DISCOUNT_EXPIRED(-1008, "新企业折扣有效期已结束"),
    AGENCY_DISCOUNT_EXCEEDS_QUOTA(-1009, "企业折扣收益人数已满"),
    NEW_AGENCY_DISCOUNT_EXCEEDS_QUOTA(-1010, "新企业折扣收益人数已满"),
    BIND_AGENCY_FAIL(-1011, "绑定企业失败")

    ;



    ExceptionEnum(int errCode, String errMsg) {
        this.errCode = errCode;
        this.errMsg = errMsg;
    }

    /** 异常code */
    private int errCode;
    /** 异常描述信息 */
    private String errMsg;
    
    public int getErrCode() {
        return errCode;
    }

    public void setErrCode(int errCode) {
        this.errCode = errCode;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }
}
