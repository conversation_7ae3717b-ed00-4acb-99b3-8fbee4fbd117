package com.extracme.evcard.membership.core.input;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @Date 2019/8/20 16:41
 */
public class SaveDriverElementsAuthenticateInput implements Serializable {

    private static final long serialVersionUID = -7781940152114452881L;

    /**
     * 会员pk_id
     */
    private Long userId;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 驾照号
     */
    private String driverCode;

    /**
     * 档案编号
     */
    private String fileNo;

    /**
     * 驾照正页照片url
     */
    private String driverLicenseImgUrl;

    /**
     * 驾照副页照片url
     */
    private String fileNoImgUrl;

    /**
     * 认证状态 0待认证 1认证通过 2认证不通过 3待重新认证
     */
    private Integer authenticateStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人id
     */
    private Long createOperId;

    /**
     * 创建人
     */
    private String createOperName;

    private String appKey;

    private String miscDesc;

    public SaveDriverElementsAuthenticateInput() {
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDriverCode() {
        return driverCode;
    }

    public void setDriverCode(String driverCode) {
        this.driverCode = driverCode;
    }

    public String getFileNo() {
        return fileNo;
    }

    public void setFileNo(String fileNo) {
        this.fileNo = fileNo;
    }

    public String getDriverLicenseImgUrl() {
        return driverLicenseImgUrl;
    }

    public void setDriverLicenseImgUrl(String driverLicenseImgUrl) {
        this.driverLicenseImgUrl = driverLicenseImgUrl;
    }

    public String getFileNoImgUrl() {
        return fileNoImgUrl;
    }

    public void setFileNoImgUrl(String fileNoImgUrl) {
        this.fileNoImgUrl = fileNoImgUrl;
    }

    public Integer getAuthenticateStatus() {
        return authenticateStatus;
    }

    public void setAuthenticateStatus(Integer authenticateStatus) {
        this.authenticateStatus = authenticateStatus;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateOperName() {
        return createOperName;
    }

    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    public Long getCreateOperId() {
        return createOperId;
    }

    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    public String getMiscDesc() {
        return miscDesc;
    }

    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc;
    }
}
