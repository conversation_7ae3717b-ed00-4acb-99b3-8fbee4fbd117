package com.extracme.evcard.membership.core.dto;

import com.extracme.evcard.membership.core.enums.AuthStateCodeEnum;
import com.extracme.evcard.rpc.dto.BaseResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class SubmitIdCardRespDto extends BaseResponse implements Serializable {
    /**
     * 提交结果
     * 认证结果 1:成功 2:操作频繁 3:身份证已被上个注销账号使用 4:身份证已被提交认证 5:身份证格式错误 6:身份证已过期 7:手持护照与人脸照片不一致
     * 8:有效期不在原认证有效期之后 9:身份证编号与原认证不一致(8,9更新即将过期、过期身份证判断)
     * 10:姓名与原认证不一致
     */
    private int state;

    /**
     * 参数
     */
    private String msg;

    /**
     * 被占用的手机号
     */
    private String occupiedMobilePhone;

    /**
     * 被占用人的mid
     */
    private String occupiedMid;

    /**
     * 被占用人的authId
     */
    private String occupiedAuthId;


    public SubmitIdCardRespDto (AuthStateCodeEnum stateCodeEnum) {
        super(0, StringUtils.EMPTY);
        this.state = stateCodeEnum.getState();
        this.msg = stateCodeEnum.getMsg();
    }

    public SubmitIdCardRespDto (AuthStateCodeEnum stateCodeEnum, String msg) {
        super(0, StringUtils.EMPTY);
        this.state = stateCodeEnum.getState();
        this.msg = msg;
    }
}
