package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;

/**
 * 会员卡操作-入参
 */
public class CardOperateDto implements Serializable{
	/**
	 * 会员卡号， 必须.
	 */
	private String cardNo;
	/**
	 * 会员id， 必须.
	 */
	private String authId;

	/**
	 * 会员类别：0：外部会员(缺省值) 1:内部会员 2:企业会员.
	 */
	private Integer membershipType;

	/**
	 * 操作原因： 暂停时必须.
	 */
	private String remark;

	/**
	 * 【停卡操作使用】停卡时长类型: 0-7天、1-30天、2-永久.
	 */
	private Integer pauseTimeType;

	/**
	 * 【停卡操作使用】恢复时间: 与停卡时长二选一.
	 * 时间格式为 {@code yyyy-MM-dd}.
	 */
	private String recoverTime;

	/**
	 * 操作类别：0：暂停 1:恢复 2:注销： 非必须.
	 */
	private Integer optionType;

	public String getCardNo() {
		return cardNo;
	}

	public void setCardNo(String cardNo) {
		this.cardNo = cardNo;
	}

	public String getAuthId() {
		return authId;
	}

	public void setAuthId(String authId) {
		this.authId = authId;
	}

	public Integer getOptionType() {
		return optionType;
	}

	public void setOptionType(Integer optionType) {
		this.optionType = optionType;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Integer getMembershipType() { return membershipType; }

	public void setMembershipType(Integer membershipType) { this.membershipType = membershipType; }

	public String getRecoverTime() {
		return recoverTime;
	}

	public void setRecoverTime(String recoverTime) {
		this.recoverTime = recoverTime;
	}

	public Integer getPauseTimeType() {
		return pauseTimeType;
	}

	public void setPauseTimeType(Integer pauseTimeType) {
		this.pauseTimeType = pauseTimeType;
	}
}
