package com.extracme.evcard.membership.core.service;

import com.extracme.evcard.membership.core.dto.DrivingLicenseAuthResult;
import com.extracme.evcard.membership.core.dto.DrivingLicenseReviewResult;
import com.extracme.evcard.membership.core.dto.UpdateUserDto;
import com.extracme.evcard.membership.core.dto.input.MemberReviewAdditionalInput;
import com.extracme.evcard.membership.core.exception.AuthenticationException;
import com.extracme.evcard.membership.core.input.SaveDriverElementsAuthenticateInput;
import com.extracme.evcard.membership.core.input.SaveDriverElementsAuthenticateLogInput;
import com.extracme.evcard.membership.core.input.SubmitDrivingLicenseInput;
import com.extracme.evcard.membership.core.input.UpdateFileNoInput;

/**
 * 会员审核认证服务
 * TODO 迁移如下功能
 * APP自动审核通过&制卡(刷脸|提交驾照信息)/
 * 审核通过/审核不通过/重新审核/新驾照审核通过/驾照冒用等
 * 2022.09.16 下线驾照三要素认证
 */
@Deprecated
public interface IMemberReviewService {

    /***
     * 新驾照审核通过
     * @param memberReviewInput
     * @throws AuthenticationException
     */
    void reviewPassAdditional(MemberReviewAdditionalInput memberReviewInput, UpdateUserDto operator) throws AuthenticationException;

    /**
     * 提交驾照信息
     * @param authId
     * @param appKey
     * @param input
     *        submitType 提交类型  0.提交审核 1.更新驾照到期时间 2.更新档案编号
     *        driverLicenseInputType 输入类型   0 无 1 驾照ocr识别 2 手动输入
     * @throws AuthenticationException  提交驾照信息不合法，数据未提交
     * @return 数据提交成功&认证成功code=0,
     *         其余code!=0，并返回各驾照状态值
     * @since 3.2.0
     * @remark 代替 membershipService.submitDrivingLicenseInfo
     */
    DrivingLicenseReviewResult submitDrivingLicenseInfo(String authId, String appKey, SubmitDrivingLicenseInput input) throws AuthenticationException;

    /**
     * 更新用户档案编号
     * @param updateFileNoInput
     * @return
     * @throws AuthenticationException
     * @remark since 3.2.0后，不建议单独使用此接口，直接使用submitDriverLicense替换。
     * driverLicenseInputType缺省时，默认需要走人工审核
     */
    @Deprecated
    DrivingLicenseReviewResult updateUserFileNo(UpdateFileNoInput updateFileNoInput) throws AuthenticationException;

    /**
     * 更新(校准)用户驾照状态
     * @param authId
     * @param appKey
     * @return
     * @Since 3.2.0
     * @remark 登录后调用，用于修正会员审核状态
     * @throws AuthenticationException 更新会员状态失败
     * @remark 2012.
     */
    @Deprecated
    DrivingLicenseReviewResult refreshDrivingLicenseStatus(String authId, String appKey) throws AuthenticationException;

    /**
     * 调用第三方接口进行驾驶证三要素认证
     * 仅本籍且当前驾照三要素认证状态为待认证的用户，进行三要素认证
     * @param authId
     * @param operator
     * @since 3.2.0
     * @remark 只校验当前状态为待认证/查证中(待重新认证)状态下的用户，需要重新认证
     * @remark 代替 membershipService.drivingLicenseAuthenticate
     */
    @Deprecated
    DrivingLicenseAuthResult drivingLicenseAuthenticate(String authId, UpdateUserDto operator);

    /**
     * 调用第三方接口进行驾驶证三要素认证
     * 仅本籍且当前驾照三要素认证状态为待认证的用户，进行三要素认证
     * @param authId
     * @since 3.2.0
     * @remark 只校验当前状态为待认证/查证中(待重新认证)状态下的用户，需要重新认证
     * @remark 代替 membershipService.drivingLicenseAuthenticate
     */
    @Deprecated
    DrivingLicenseAuthResult drivingLicenseAuthenticate(String authId);

    /**
     * 新增驾照三要素认证待认证记录（即变更会员三要素认证状态为待认证）
     * 变更会员驾照信息时使用(如，会员系统后台修改会员信息)
     * @param input
     * @throws AuthenticationException
     * @since 3.2.0
     * @remark 代替 membershipService.saveDriverLicenseElementsAuthenticateRecord
     */
    @Deprecated
    void saveDriverLicenseElementsAuthenticateRecord(SaveDriverElementsAuthenticateInput input) throws AuthenticationException;

    /**
     * 新增驾照三要素认证待认证记录（此接口不变更会员表三要素认证状态字段）
     * 专门提供给会员系统后台变更会员驾照信息时使用
     * @param authId
     * @param input
     * @throws AuthenticationException
     * @since 3.2.0
     * @remark ****  此接口不单独使用 *****，修改会员服务化之后，不再需要此接口。
     */
    @Deprecated
    void saveDriverLicenseElementsAuthenticateRecordOnly(String authId, SaveDriverElementsAuthenticateInput input) throws AuthenticationException;

    /**
     * 新增驾照三要素认证待认证记录（即变更会员三要素认证状态为待认证）
     * 变更会员驾照信息时使用(如，会员系统后台修改会员信息)
     * @param authId
     * @param input
     * @throws AuthenticationException
     * @since 3.2.0
     */
    void saveDriverLicenseElementsAuthenticateRecord(String authId, SaveDriverElementsAuthenticateInput input) throws AuthenticationException;

    /**
     * 新增驾照三要素认证待认证记录日志
     * 此接口不建议单独使用，确认无调用之后可以移除
     * @param input
     * @throws AuthenticationException
     * @since 3.2.0
     * @remark 代替 membershipService.saveDriverLicenseElementsAuthenticateLog
     */
    @Deprecated
    void saveDriverLicenseElementsAuthenticateLog(SaveDriverElementsAuthenticateLogInput input);

    /**
     * 手动将会员的驾照三要素状态设置为通过
     * （由操作人保证驾照信息真实性，用户会员系统手动三要素认证通过功能(风险功能，高权限控制)）
     * @param authId
     * @param operator
     * @throws AuthenticationException
     * @since 3.2.0
     * @remark  备注必须填写，本籍且会员驾照三要素当前状态当前不为通过
     */
    void manualLicenseElementsAuthenticateSuccess(String authId, UpdateUserDto operator) throws AuthenticationException;

    /**
     * 驾照三要素-手动重新验证
     * @param authId
     * @param operator
     * @return
     * @remark 新增：会员系统使用
     */
    DrivingLicenseAuthResult drivingLicenseManualReAuthenticate(String authId, UpdateUserDto operator)  throws AuthenticationException;
}
