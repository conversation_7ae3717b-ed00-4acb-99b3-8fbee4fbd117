package com.extracme.evcard.membership.core.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

@Data
public class MmpShortLinkManagementDTO implements Serializable {
    private static final long serialVersionUID = 7280627482762956016L;
    private Long id;

    private Date createTime;

    private Long createOperId;

    private String createOperName;

    private Integer status;

    private String shortLinkName;

    private String shortLinkUrl;

    private String randomCode;

    private String originalUrl;

    @JsonFormat(timezone="GMT+8", pattern="yyyy-MM-dd")
    private LocalDate effectiveTime;

    @JsonFormat(timezone="GMT+8", pattern="yyyy-MM-dd")
    private LocalDate failureTime;

    private String qrcodePath;

    private List<MmpShortLinkLogDTO> mmpShortLinkLogList;

    private Integer pageNum;
    private Integer pageSize;
    private Integer isAll;
    private Integer isHidden;


}