package com.extracme.evcard.membership.core.service;

import com.extracme.evcard.membership.core.dto.JwtTokenDto;
import com.extracme.evcard.membership.core.dto.TokenValidationResult;
import com.extracme.evcard.membership.core.exception.BusinessException;

import java.util.List;

/**
 * Token服务接口
 * 
 * <AUTHOR>
 * @since 2025-08-20
 */
public interface ITokenService {
    
    /**
     * 生成JWT令牌对（访问令牌和刷新令牌）
     * 
     * @param userId 用户ID
     * @param mid 用户MID
     * @param appKey 应用标识
     * @param deviceId 设备ID
     * @param deviceType 设备类型
     * @param deviceInfo 设备信息
     * @param loginIp 登录IP
     * @param loginLocation 登录地理位置
     * @return JWT令牌对
     * @throws BusinessException 业务异常
     */
    JwtTokenDto generateTokenPair(Long userId, String mid, String appKey, String deviceId, 
                                  String deviceType, String deviceInfo, String loginIp, String loginLocation) 
                                  throws BusinessException;
    
    /**
     * 验证访问令牌
     * 
     * @param accessToken 访问令牌
     * @return 验证结果
     */
    TokenValidationResult validateAccessToken(String accessToken);
    
    /**
     * 验证刷新令牌
     * 
     * @param refreshToken 刷新令牌
     * @return 验证结果
     */
    TokenValidationResult validateRefreshToken(String refreshToken);
    
    /**
     * 使用刷新令牌生成新的访问令牌
     * 
     * @param refreshToken 刷新令牌
     * @return 新的JWT令牌对
     * @throws BusinessException 业务异常
     */
    JwtTokenDto refreshAccessToken(String refreshToken) throws BusinessException;
    
    /**
     * 撤销令牌（加入黑名单）
     * 
     * @param token 要撤销的令牌
     * @param reason 撤销原因
     * @throws BusinessException 业务异常
     */
    void revokeToken(String token, String reason) throws BusinessException;
    
    /**
     * 撤销用户的所有令牌
     * 
     * @param userId 用户ID
     * @param reason 撤销原因
     * @throws BusinessException 业务异常
     */
    void revokeAllUserTokens(Long userId, String reason) throws BusinessException;
    
    /**
     * 撤销设备的所有令牌
     * 
     * @param deviceId 设备ID
     * @param reason 撤销原因
     * @throws BusinessException 业务异常
     */
    void revokeAllDeviceTokens(String deviceId, String reason) throws BusinessException;
    
    /**
     * 用户登出（撤销指定会话的令牌）
     * 
     * @param accessToken 访问令牌
     * @param reason 登出原因
     * @throws BusinessException 业务异常
     */
    void logout(String accessToken, String reason) throws BusinessException;
    
    /**
     * 用户登出所有设备
     * 
     * @param userId 用户ID
     * @param reason 登出原因
     * @throws BusinessException 业务异常
     */
    void logoutAllDevices(Long userId, String reason) throws BusinessException;
    
    /**
     * 获取用户的活跃会话列表
     * 
     * @param userId 用户ID
     * @return 活跃会话列表
     * @throws BusinessException 业务异常
     */
    List<JwtTokenDto> getUserActiveSessions(Long userId) throws BusinessException;
    
    /**
     * 踢出指定设备的用户会话
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @param reason 踢出原因
     * @throws BusinessException 业务异常
     */
    void kickoutDevice(Long userId, String deviceId, String reason) throws BusinessException;
    
    /**
     * 更新会话的最后活跃时间
     * 
     * @param accessToken 访问令牌
     * @throws BusinessException 业务异常
     */
    void updateLastActiveTime(String accessToken) throws BusinessException;
    
    /**
     * 清理过期的会话记录
     * 
     * @return 清理的记录数
     */
    int cleanupExpiredSessions();
    
    /**
     * 检查令牌是否即将过期
     * 
     * @param accessToken 访问令牌
     * @param minutesBeforeExpiry 过期前多少分钟
     * @return true表示即将过期
     */
    boolean isTokenExpiringSoon(String accessToken, int minutesBeforeExpiry);
}
