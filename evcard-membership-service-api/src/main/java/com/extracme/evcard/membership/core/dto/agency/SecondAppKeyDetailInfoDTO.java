package com.extracme.evcard.membership.core.dto.agency;

import lombok.Data;

/**
 * 二级渠道 dto
 */
@Data
public class SecondAppKeyDetailInfoDTO {

    /**
     *二级渠道appkey
     */
    private String secondAppKey;

    /**
     * 二级渠道APP_SECRET
     */
    private String secondAppSecret;

    /**
     * 二级渠道名称
     */
    private String secondAppKeyName;


    /**
     * 一级渠道appkey
     */
    private String firstAppKey;

    /** 一级渠道名称 */
    private String firstAppKeyName;

    /**
     * 平台ID
     */
    private Long platformId;

    /** 平台名称 */
    private String platName;

    /**
     *第三方平台标记（1：不是，2：是）默认1
     */
    private int thirdPlatformFlag;

    /**
     *租车平台标记（1：不是，2：是）默认1
     */
    private int rentPlatformFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * '0:有效 1：无效'
     */
    private Integer status;

    /** 创建时间 */
    private String createTime;
    /** 创建人 */
    private String createUser;
    /** 创建人id */
    private Long createUserId;



}