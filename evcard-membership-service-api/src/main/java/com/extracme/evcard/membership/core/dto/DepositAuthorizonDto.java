package com.extracme.evcard.membership.core.dto;

import com.extracme.evcard.membership.core.dto.ListPageDto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 用户押金预授权信息(基础/车辆押金预授权、芝麻信用代扣是否开启)
 * </p>
 *
 */
public class DepositAuthorizonDto implements Serializable {

    /** 基础押金充值类型 1 现金 2 预授权   */
    private Integer baseDepositType;

    /** 基础押金 */
    private BigDecimal baseDeposit = BigDecimal.ZERO;

    /** 车辆押金押金充值类型 1 现金 2 预授权   */
    private Integer vehicleDepositType;

    /** 车辆押金 */
    private BigDecimal vehicleDeposit = BigDecimal.ZERO;

    /**  代扣授权状态  0 否 1 是 */
    private int withHoldSignStatus = 0;

    public Integer getBaseDepositType() {
        return baseDepositType;
    }

    public void setBaseDepositType(Integer baseDepositType) {
        this.baseDepositType = baseDepositType;
    }

    public BigDecimal getBaseDeposit() {
        return baseDeposit;
    }

    public void setBaseDeposit(BigDecimal baseDeposit) {
        this.baseDeposit = baseDeposit;
    }

    public Integer getVehicleDepositType() {
        return vehicleDepositType;
    }

    public void setVehicleDepositType(Integer vehicleDepositType) {
        this.vehicleDepositType = vehicleDepositType;
    }

    public BigDecimal getVehicleDeposit() {
        return vehicleDeposit;
    }

    public void setVehicleDeposit(BigDecimal vehicleDeposit) {
        this.vehicleDeposit = vehicleDeposit;
    }

    public int getWithHoldSignStatus() {
        return withHoldSignStatus;
    }

    public void setWithHoldSignStatus(int withHoldSignStatus) {
        this.withHoldSignStatus = withHoldSignStatus;
    }
}
