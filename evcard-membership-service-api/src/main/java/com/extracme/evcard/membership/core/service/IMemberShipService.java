package com.extracme.evcard.membership.core.service;

import com.extracme.evcard.membership.core.bean.BaseBean;
import com.extracme.evcard.membership.core.dto.*;
import com.extracme.evcard.membership.core.dto.input.FaceMatchInput;
import com.extracme.evcard.membership.core.dto.input.SubmitDepositSecurityInput;
import com.extracme.evcard.membership.core.exception.*;
import com.extracme.evcard.membership.core.input.*;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.exception.BusinessException;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 会员服务
 *
 * <AUTHOR>
 * @since 2018/1/31
 *
 */
public interface IMemberShipService {

    /**
     * 会员注册
	 * @param registerDto
	 * <p>
	 *    手机号  mobilePhone 必填
	      短信验证码  smsVerifyCode 必填
	      密码  password 必填
	      省  province 必填
	      市 city 必填
	      区 area 必填
	      注册来源 registerOrigin  0：网点注册 1：网站注册 2：管理平台注册 3:手机APP 4:第三方  5、6 e享天开  7:CRM 必填
	      第三方注册 appKey
	      分享人ID  shareUid;
	      设备号 imeiNo;
	      操作人 operateUser;
	 * </p>
     * @return
     * @throws MemberException
     * <AUTHOR>
     * @since 2018/2/8
     */
    String register(RegisterDto registerDto) throws MemberException;

    /**
     * app改造注册，删除部分参数判断
     * @param registerDto
     * @return
     * @throws MemberException
     * <AUTHOR>
     * @since 2019/11/18
     */
    String registerV1(RegisterDto registerDto) throws MemberException;

    /**
     * app改造注册，删除部分参数判断
     * @param registerDto
     * @param checkVerifyCode	是否校验验证码.<br>
     * @return
     * @throws MemberException
     * <AUTHOR>
     * @since 2019/11/18
     */
    String registerV1(RegisterDto registerDto, boolean checkVerifyCode) throws MemberException;

    /**
     * 会员注册.<br>
     * @param registerDto	会员信息
     * @param checkVerifyCode	是否校验验证码.<br>
     * @return	会员authId.<br>
     * @throws MemberException
     */
    String register(RegisterDto registerDto, boolean checkVerifyCode) throws MemberException;

    /**
     * 会员注册.<br>
     * @param registerDto	会员信息
     * @param checkVerifyCode	是否校验验证码.<br>
     * @return	会员authId.<br>
     * @throws RegisterException
     */
    String registerV2(RegisterDto registerDto, boolean checkVerifyCode) throws RegisterException;

    /**
     * 更换手机号并绑定IMEI.<br>
     * @param mobilePhone		旧手机号.<br>
     * @param newMobilePhone	新手机号.<br>
     * @param authId		会员.<br>
     * @param verifyCode		验证码.<br>
     * @param imei	设备编号.<br>
     */
    void changeBindMobilePhone(String mobilePhone, String newMobilePhone, String authId, String verifyCode, String imei) throws ChangeMobileException;

    /**
     * 更换手机号并绑定IMEI.<br>
     *   @param   changeMobileDto
     * param mobilePhone		旧手机号.<br>
     * param newMobilePhone	新手机号.<br>
     * param authId		会员.<br>
     * param verifyCode		验证码.<br>
     * param imei	设备编号.<br>
     */
    void changeBindMobilePhoneV2(ChangeMobileDto changeMobileDto) throws ChangeMobileException;


    /**
     * 获取用户的有效消费金额.<br>
     * @param authId
     * @return
     */
    BigDecimal consumAmount(String authId);

    /**
     * 敏感词检测
     * @param content
     * @return
     * <AUTHOR>
     * @since 2018/2/8
     */
    boolean keywordScan(String content);

    /**
     * 法大大文档签署接口
     *
     * @param signContractDto
     * @remark 建议直接使用IMemberShipContractServ.autoSignContract
     */
    @Deprecated
    void autoSignContract(SignContractDto signContractDto) throws MemberException;

    /**
     * 记录用户刷卡行为.<br>
     * @param cardAction	用户刷卡的信息.<br>
     */
    @Deprecated
    void saveCardAction(CardActionDto cardAction);

    /**
     * 获取外部会员敏感信息.<br>
     * @param authId		会员id.<br>
     * @param messageType	字段类型。 1：手机号; 2:驾照号;3：驾照图片;4：人脸图片; 5:邮寄地址; 6：姓名; 7：身份证号（IdCardNumber不是18位，则查PassportNo还不是18位，则查询driverCode，否则null）
     *                      8:驾照副页 9: 身份照片 10：手持身份证照片 11：身份证件编号（返回PassportNo，但是这个字段有可能存储的是护照号..） 12 用户头像
     *                      16：身份证 正面照片   17：身份证 反面照片 <br>
     * @param optUser		操作人，请传递登录系统使用的username，不要传递中文姓名.<br>
     * @param systemFlag	系统标识.<br>
     * @return
     */
    MembershipSecretInfo getMembershipSecretInfo(String authId, int messageType, String optUser, String systemFlag);

    /**
     * 获取会员敏感信息.<br>
     * @param authId		会员id.<br>
     * @param membershipType 0:外部会员,1：内部会员.<br>
     * @param messageType	字段类型。 1：手机号; 2:驾照号;3：驾照图片;4：人脸图片; 5:邮寄地址; 6：姓名; 7：身份证号（IdCardNumber不是18位，则查PassportNo还不是18位，则查询driverCode，否则null）
     *                      8:驾照副页 9: 身份照片 10：手持身份证照片 11：身份证件编号（返回PassportNo，但是这个字段有可能存储的是护照号..）
     *                      15:学生证照片
     *                      <br>
     * @param optUser		操作人，请传递登录系统使用的username，不要传递中文姓名.<br>
     * @param systemFlag	系统标识.<br>
     * @return
     */
    MembershipSecretInfo getMembershipSecretInfo(String authId, int membershipType, int messageType, String optUser, String systemFlag);

    /**
     * 获取新驾照信息
     * @param authId 会员id
     * @return
     * @since 2.6.2
     */
    MembershipAdditionalDto getAdditionalInfo(String authId);

    /**
     * 新增内部会员并制虚拟卡.<br>
     *
     * @param innerMemberInfoDto 内部会员注册信息（必须）.<br>
     * @param createdUser 创建人（必须）.<br>
     * @return 会员id.<br>
     */
    String addInnerMembershipInfo(InnerNewMemberInfoDto innerMemberInfoDto, String createdUser);

    /**
     * 处理用户表需要的数据(埋点使用)
     * @param properties
     * @param authId
     */
    void handleUserInfo(Map<String,Object> properties, String authId);

    /**
     * 获取用户基本信息.
     * @param authId 会员id
     * @return
     * <AUTHOR>
     * @since 1.6.0
     */
    @Deprecated
    UserInfoDto getUserInfoByToken(String authId) throws UserMessageException;

    /**
     * 获取用户基本信息.
     * @param authId 会员id
     * @return
     * <AUTHOR>
     * @since 1.6.0
     */
    @Deprecated
    UserInfoDto getUserInfoByToken(String authId,String appKey) throws UserMessageException;

    /**
     * 根据authId查询该用户的所有账号（内部会员存在两个账号的情况）
     * @param authId
     * @return
     * <AUTHOR>
     * @since 1.7.0
     */
    List<String> getAllAuthId(String authId);

    /**
     * 根据orgId查询机构的账户信息
     * @param orgId
     * @return
     */
    AgencyMinsDto getAgencyMinsByid(String orgId);

    /**
     * 查询企业机构是否为小循环
     * @param agencyId
     * @return
     * <AUTHOR>
     * @since 1.7.0
     */
    int queryInsideFlag(String agencyId);

    /**
     * 根据angencyId查询企业部分信息
     * @param agencyId
     * <AUTHOR>
     * @since 1.7.0
     * @return
     */
    AgencyInfoDto queryAgencyInfoByAgencyId(String agencyId);

    /**
     * 根据企业id和卡id判断是否可用
     * @param orgId
     * @param cardNo
     * <AUTHOR>
     * @since 1.7.0
     */
    int getOrgCardStatusById(String orgId,String cardNo);

    /**
     * 通过mid查询会员信息
     * @param mid
     * @return
     */
    MembershipBasicInfo getUserBasicInfo(String mid);

    /**
     * 根据会员authid和会员类型获取会员基本信息
     * @param authId
     * @param membershipType
     * @return
     * <AUTHOR>
     * @since  1.7.0
     */
    MembershipBasicInfo getUserBasicInfo(String authId, Short membershipType);

    /**
     * 根据会员pkId获取会员基本信息
     * @param pkId
     * @return
     * <AUTHOR>
     */
    MembershipBasicInfo getUserBasicInfoByPkId(Long pkId);

    /**
     * 根据手机号查询会员.<br>
     * @param phone				手机号.<br>
     * @param membershipType	会员类型.<br>
     * @return
     * <AUTHOR>
     */
    MembershipBasicInfo getMembershipByPhone(String phone, int membershipType);


    /**
     * 根据根据享道统一用户ID查询用户.<br>
     * @param uid 享道统一用户ID.<br>
     * @return
     * @since 3.9.0
     */
    MembershipBlobDTO getMemberByUid(String uid);

    /**
     * 更新指定pkId的会员的享道统一用户id
     * @param pkId
     * @param uid
     */
    int updateMemberUidByPkId(Long pkId, String uid);

    /**
     * 查询会员状态和卡状态（包括内部和外部会员）
     * @param authId
     * @param membershipType
     * <AUTHOR>
     * @since 1.7.0
     */
    ReviewAndCarStatusDto reviewAndCardStatus(String authId,Integer membershipType);

    /**
     * 查询会员条款和隐私政策版本信息
     * @return
     */
    MemberRuleVersionDTO queryMemberRuleVersion();

    /**
     * 会员同意条款更新
     * @param uploadServiceVerInput
     * <AUTHOR>
     * @since 1.6.0
     */
    void uploadServiceVer(UploadServiceVerInput uploadServiceVerInput);

    /**
     * 会员获取验证码
     * @param smsVerifyBeanInput
     * <AUTHOR>
     * @since 1.6.0
     * @deprecated 不再推荐使用，后续所有的获取验证码需要先验证滑动验证码去阿里做人机验证，通过后再发送验证码，替代方法  getSMSVerifyCodeV3
     */
    BaseBean getSMSVerifyCode(SMSVerifyBeanInput smsVerifyBeanInput)throws UserMessageException;

    /**
     * 会员获取验证码
     * @param smsVerifyBeanInput
     * <AUTHOR>
     * @since 1.6.0
     * @deprecated 不再推荐使用，后续所有的获取验证码需要先验证滑动验证码去阿里做人机验证，通过后再发送验证码，替代方法  getSMSVerifyCodeV3
     */
    BaseBean getSMSVerifyCodeV2(SMSVerifyBeanInput smsVerifyBeanInput)throws VerifyCodeException;

    /**
     * 会员获取验证码，需要去阿里做人机验证.
     * @param smsVerifyBeanInput
     * @param aliAfs	阿里人机验证需要的参数.
     * <AUTHOR>
     * @since 2020-05-13
     */
    BaseBean getSMSVerifyCodeV3(SMSVerifyBeanInput smsVerifyBeanInput, AliAfs aliAfs)throws VerifyCodeException;

    /**
     * 获取验证码阿里人机验证
     *
     * @param aliAfs
     */
    void getSMSAliAFS(AliAfs aliAfs) throws VerifyCodeException;

    /**
     * 会员获取验证码，不发短信，直接返回
     * @param smsVerifyBeanInput
     * @return 验证码
     * <AUTHOR>
     */
    String getSMSVerifyCodeWithoutSendSMS(SMSVerifyBeanInput smsVerifyBeanInput)throws VerifyCodeException;

    /**
     * 检查卡号是否有效
     * @param cardNo
     * @return
     * @zhangrenhua
     */
    Boolean  checkCardValid(String cardNo);

    /**
     * 修改邮箱并增加日志.<br>
     * @param oldMail      旧邮箱.<br>
     * @param newMail      新邮箱.<br>
     * @param authId        会员id.<br>
     * @param updateUserDto 修改人信息.<br>
     * @return boolean
     * <AUTHOR>
     * @date 2018年9月14日
     * @since 1.7.1
     * @remark orgin-callcenter
     */
    boolean changeMail(String oldMail, String newMail, String authId, UpdateUserDto updateUserDto);

    /**
     * 修改地址并添加日志.<br>
     * @param authId		会员id.<br>
     * @param address		联系地址.<br>
     * @param province		省（自治区）.<br>
     * @param city			市（盟）.<br>
     * @param area			区.<br>
     * @param updateUserDto 修改人信息.<br>
     * @return boolean
     * <AUTHOR>
     * @date 2018年9月14日
     * @since 1.7.1
     * @remark orgin-callcenter
     */
    boolean changeAddress(String authId, String address, String province, String city, String area, UpdateUserDto updateUserDto);

    /**
     * 重置会员密码并添加日志
     * @param authId		会员id.<br>
     * @param password		密码.<br>
     * @param updateUserDto	修改人信息.<br>
     * @return boolean
     * <AUTHOR>
     * @date 2018年9月14日
     * @since 1.7.1
     * @remark orgin-callcenter
     */
    boolean resetPassword(String authId, String password, UpdateUserDto updateUserDto);

    /**
     * 暂停会员卡
     * @param cardPauseDto  暂停会员卡信息.<br>
     * @param updateUserDto 修改人信息.<br>
     * @return boolean 操作结果
     * <AUTHOR>
     * @date 2018年9月14日
     * @since 1.7.1
     * @remark orgin-callcenter
     * @deprecated 将在2.1.0之后的版本中，被IMemberCardService.cardPause替代。
     */
    boolean cardPause(CardPauseDto cardPauseDto, UpdateUserDto updateUserDto);

    /**
     * 根据用户authid查询用户卡状态
     * @param authId
     * @param membershipType
     * @return code (0:审核通过 1：待审核 2： 未激活 3：用户已被暂停 4 卡失效 5 制作中)
     * @data 2018.12.12
     * @since 2.25.0
     * <AUTHOR>
     */
    BaseResponse checkUserCard(String authId,Integer membershipType);


    /**
     * 会员注销
     * @param unregisterDto 注销参数
     * <p>
     *    authId 会员编号， 必填。
     *    membershipType 会员类型， 必填。
     *
     *    unregisterOrigin 注销通道,  非必填,
     * 			  暂定： 待确认。 0：无 1：会员系统 2：客服系统 3:手机APP(默认值)  4：支付宝小程序 。
     *    operateUserId 操作人ID, 非必填,
     *            通过管理平台（非本人操作）时必须。
     *    operateUserName 操作人姓名, 非必填,
     *            通过管理平台（非本人操作）时必须。
     *    unregisteReason 注销原因,非必填,
     *            通过管理平台（非本人操作）时必须。
     *
     *    新增参数：
     *    confirmFlag 二次确认标记
     *                true 特定检查项将二次确认，包括E币余额为0检查项
                      false 不再确认强制注销，缺省为true
     * </p>
     * @return
     * @throws UnregisterException
     * <AUTHOR>
     * @since 1.9.1
     */
     void unregister(UnregisterDto unregisterDto) throws UnregisterException;


    /**
     * 用户账号恢复(注销冻结期内)
     * @param unregisterDto
     * @throws UnregisterException
     * <AUTHOR>
     * @since 1.9.1
     * @remark 根据mobilePhone恢复账号
     */
     void unregisterRecover(UnregisterDto unregisterDto) throws UnregisterException;

    /**
     * 用户账号恢复(注销冻结期内)
     * @param accountRecoverDto
     * @throws UnregisterException
     * <AUTHOR>
     * @since 3.1.0
     * @remark 根据authId恢复账号
     */
    void unregisterRecoverByAuthId(AccountRecoverDto accountRecoverDto) throws UnregisterException;

    /**
     * 查询会员状态
     * @param mobilePhone 手机号
     * @param membershipType 会员类别
     * @return
     * <AUTHOR>
     * @since 1.9.1
     */
    AccountStatusDto getAccountStatusByMobile(String mobilePhone, Integer membershipType);

    /**
     * 查询会员状态
     * @param mobilePhone 手机号
     * @param membershipType 会员类别
     * @param orgId 会员所属机构
     * @return
     * <AUTHOR>
     * @since 1.9.1
     */
    AccountStatusDto getAccountStatusByMobileV2(String mobilePhone, Integer membershipType,String orgId);

    /**
     * 查询会员状态
     * @param driverCode 驾驶证号
     * @param membershipType 会员类别
     * <AUTHOR>
     * @since 1.9.1
     */
    AccountStatusDto getAccountStatusByDriverCode(String driverCode, Integer membershipType);


    /**
     * 保存用户操作日志
     * @param userOperationLogInput
     */
    void saveUserOperationLog(UserOperationLogInput userOperationLogInput);

    /**
     * 保存用户操作日志.(旧表 该服务会在表迁移完成后进行废弃)
     * @param operatorContent
     * @param foreignKey
     * @param foreignKey2
     * @param createUser
     */
    void saveUserOperationLogOldTable(String operatorContent, String foreignKey, String foreignKey2, String createUser);
    
    /**
     * 找回密码
     * @param mobilePhone 手机号
     * @param verifyCode 验证码
     * @param newPassword 新密码
     * @param updateUserDto
     * @throws UserMessageException
     * <AUTHOR>
     * @since 1.9.1
     */
    void findPassword(String mobilePhone, String verifyCode, String newPassword, UpdateUserDto updateUserDto) throws UserMessageException;

    /**
     * 找回密码
     * @param findPasswordDto
     * @throws UserMessageException
     * <AUTHOR>
     * @since 1.9.1
     */
    void findPasswordV2(FindPasswordDto findPasswordDto) throws FindPasswordException;


    /**
     * 修改密码
     * @param modifyPasswordDTO
     * @throws UserMessageException
     * @since 1.9.1
     * <AUTHOR>
     * @since 1.9.1
     */
    void modifyPassword(ModifyPasswordDTO modifyPasswordDTO) throws UserMessageException;


    /**
     * 修改密码
     * @param modifyPasswordDTO
     * @throws ModifyPasswordException
     * @since 1.9.1
     * <AUTHOR>
     * @since 1.9.1
     */
    void modifyPasswordV2(ModifyPasswordDTO modifyPasswordDTO) throws ModifyPasswordException;


    /**
     * 密码重置
     * @param authId 会员ID
     * @param password 密码 ,非必填
     * @throws UserMessageException
     * <AUTHOR>
     * @since 1.9.1
     */
    void resetPassword(String authId, String password, String optUserName, Date updateTime) throws UserMessageException;

    /**
     * 更换手机号
     * @param authId 会员ID
     * @param newMobilePhone 新手机号
     * @param verifyCode 验证码
     * @param oldMobile 旧手机号
     * @param updateUserDto
     * @throws UserMessageException
     * <AUTHOR>
     * @since 1.9.1
     */
    void modifyMobilePhone(String authId, String newMobilePhone, String verifyCode,
                           String oldMobile, UpdateUserDto updateUserDto) throws UserMessageException;

    /**
     * 未登录更换手机号
     * @param newMobilePhone
     * @param verifyCode
     * @param oldMobile
     * @param updateUserDto
     * @param passWord
     * @param channel
     * @throws UserMessageException
     */
    ModifyPhoneResultDto modifyMobilePhoneUnLogin(String newMobilePhone, String verifyCode,
                           String oldMobile, UpdateUserDto updateUserDto,String driverCode, String passWord, String channel) throws  ChangeMobileException;


    /**
     * 更换手机号
     * @param modifyMobilePhoneDto
     * @throws UserMessageException
     * <AUTHOR>
     * @since 1.9.1
     */
    void modifyMobilePhoneV2(ModifyMobilePhoneDto modifyMobilePhoneDto) throws ChangeMobileException;

    /**
     * 修改手机号 仅做更新，不做任何判断
     * @param authId
     * @param userPkId
     * @param newMobile
     * @param oldMobile
     * @param updateUserDto
     * @throws ChangeMobileException
     */
    void updateDbMobilePhone(String authId, Long userPkId, String newMobile, String oldMobile, UpdateUserDto updateUserDto) throws ChangeMobileException;

    /**
     * 根据会员authid和会员类型获取信息（包含运营区域信息）
     * @param authId
     * @param membershipType
     * @return
     * <AUTHOR>
     * @since 1.9.1
     */
    MembershipRegionInfo getUserRegionInfo(String authId, Short membershipType);

    /**
     * 获取会员的认证渠道来源(0.OCR识别 1.人脸认证 2.审核)
     * @param authId
     * @param membershipType
     * @param operateType  0.OCR识别 1.人脸认证 2.审核
     * @return
     * <AUTHOR>
     * @since 1.9.1
     */
    AppKeyDto getMemberAuthOrigin(String authId, Integer membershipType, Long operateType);


    /**
     * 查询会员快递信息
     * @param authId
     * @return
     * <AUTHOR>
     */
    List<ExpressInfoDTO> queryExpressInfo(String authId);


    /**
     * 查询会员条款记录
     * @param input
     * @return
     * <AUTHOR>
     */
    List<MemberClauseRecord> queryMemberClauseRecord(QueryMemberClauseRecordInput input);


    /**
     * 查询会员日志列表
     * @param input
     * @return
     * <AUTHOR>
     */
    List<MemberOperateLogDTO> queryMemberOperateLog(QueryMemberOperateLogInput input);


    /**
     * 客服系统用来综合查询会员使用
     * @param querySimpleMemberInput
     * @return
     */
    List<SimpleMembershipInfoDTO> querySimpleMembershipInfoList(QuerySimpleMemberInput querySimpleMemberInput);

	/**
	 * 查询老会员日志列表
	 *
	 * @param input 查询会员日志对象
	 * @return 老会员操作日志列表
	 * <AUTHOR>
	 */
	List<UserOperatorLogDTO> queryUserOperatorLog(QueryMemberOperateLogInput input);
    /**
     * 获取sts临时凭证的token
     * @param bucketName
     * @return
     */
    StsTokenDto getStsToken(String bucketName);

    /**
     * 使用签名URL进行临时授权
     * 生成以GET方法访问的签名URL
     * 有效期：5分钟
     * @param bucketName 用户驾照敏感信息bucket：evcard-sensitive
     * @param objectName 图片路径 例如：dev/sensitiveBucket/driving_license/18019267744103157913/1565774072471.jpg
     * @return
     */
    String getStsUrlTokenGet(String bucketName, String objectName);

    /**
     * 提交驾照信息
     * @param authId
     * @param appKey
     * @param input
     * @throws AuthenticationException
     * @remark 此接口已废弃， since 3.2.0后，使用memberReviewService.submitDrivingLicenseInfo
     */
    @Deprecated
    void submitDrivingLicenseInfo(String authId, String appKey, SubmitDrivingLicenseInput input) throws AuthenticationException;

    /**
     * 更新用户档案编号
     * @param updateFileNoInput
     * @return
     * @throws AuthenticationException
     * @remark since 3.2.0后，不建议单独使用此接口
     * driverLicenseInputType缺省时，默认需要走人工审核
     */
    @Deprecated
    Boolean updateUserFileNo(UpdateFileNoInput updateFileNoInput) throws AuthenticationException;

    /**
     * 提交用户身份认证信息
     * 外籍提交认证信息
     * @param picInput
     * @throws AuthenticationException
     */
    void submitUserIDCardPic(SubmitUserIdCardPicInput picInput) throws AuthenticationException;


    /**
     * 提交人脸图片信息
     * 包含调用上汽人脸对比逻辑
     * 本籍提交人脸认证信息
     * @param input
     * @throws AuthenticationException
     */
    void submitFaceRecognitionPic(SubmitFacePicInput input) throws AuthenticationException;

    /**
     * 提交人脸图片信息
     * 不包含调用上汽人脸对比逻辑，使用直接传入的人脸认证结果和相似度
     * @param input
     * @throws AuthenticationException
     */
    void submitOnlyFaceRecognitionPic(SubmitFaceInfoInput input) throws AuthenticationException;

    /**
     * 履约提交 人脸图片信息
     * 参考submitOnlyFaceRecognitionPic
     * @param input
     * @throws AuthenticationException
     */
    void ofcSubmitOnlyFaceRecognitionPic(SubmitFaceInfoInput input) throws AuthenticationException;

    /**
     * 调用上汽人脸对比api
     * @param name 姓名
     * @param idCardNumber 驾照号
     * @param fileByte 人脸图片文件
     * @param liveDelta 如果不是sdk调用，就传入空字符串
     * @param authId 有authId就传authId,没有就传操作人
     * @return
     *          faceAuthenticationResult 对比结果
     *                  0成功
     *                  2身份证号和姓名是正常的，但公安那边的数据源没有他的照片
     *                  1上汽身份核查服务失败(name idCard不符)
     *          faceAuthenticationSimilarity 相似度
     * @throws AuthenticationException
     */
    Map<String, String> faceAuthentication(String name, String idCardNumber, byte[] fileByte, String liveDelta, String authId) throws AuthenticationException;

    /**
     * 调用上汽人脸对比api
     * @param name 姓名
     * @param idCardNumber 驾照号
     * @param fileByte 人脸图片文件
     * @param liveDelta 如果不是sdk调用，就传入空字符串
     * @param authId 有authId就传authId,没有就传操作人
     * @param appKey 来源
     * @return
     *          faceAuthenticationResult 对比结果
     *                  0成功
     *                  2身份证号和姓名是正常的，但公安那边的数据源没有他的照片
     *                  1上汽身份核查服务失败(name idCard不符)
     *          faceAuthenticationSimilarity 相似度
     * @throws AuthenticationException
     */
    Map<String, String> faceAuthentication(String name, String idCardNumber, byte[] fileByte, String liveDelta, String authId, String appKey) throws AuthenticationException;

    /**
     * 调用百度/商汤人脸对比api
     * @param name 姓名
     * @param idCardNumber 驾照号
     * @param fileByte 人脸图片文件
     * @param liveDelta 如果不是sdk调用，就传入空字符串
     * @param authId 有authId就传authId,没有就传操作人
     * @param appKey 来源
     * @return
     *          faceAuthenticationResult 对比结果
     *                  0成功
     *                  2身份证号和姓名是正常的，但公安那边的数据源没有他的照片
     *                  1上汽身份核查服务失败(name idCard不符)
     *          faceAuthenticationSimilarity 相似度
     * @throws AuthenticationException
     */
    Map<String, String> faceAuthentication(String name, String idCardNumber, byte[] fileByte, String liveDelta, String authId, String appKey, String imageUrl) throws AuthenticationException;

    /**
     * 调用商汤人脸对比api
     * @param name 姓名
     * @param idCardNumber 驾照号
     * @param fileByte 人脸图片文件
     * @param liveDelta 如果不是sdk调用，就传入空字符串
     * @param authId 有authId就传authId,没有就传操作人
     * @param appKey 来源
     * @return
     *          faceAuthenticationResult 对比结果
     *                  0成功
     *                  2身份证号和姓名是正常的，但公安那边的数据源没有他的照片
     *                  1上汽身份核查服务失败(name idCard不符)
     *          faceAuthenticationSimilarity 相似度
     * @throws AuthenticationException
     */
    Map<String, String> sensetimeFaceAuthentication(String name, String idCardNumber, byte[] fileByte, String liveDelta, String authId, String appKey) throws AuthenticationException;

    /**
     * 调用百度人脸对比api
     * @param name 姓名
     * @param idCardNumber 驾照号
     * @param imageUrl 图片访问地址
     * @param authId 有authId就传authId,没有就传操作人
     * @param appKey 来源
     * @return
     *          faceAuthenticationResult 对比结果
     *                  0成功
     *                  2身份证号和姓名是正常的，但公安那边的数据源没有他的照片
     *                  1上汽身份核查服务失败(name idCard不符)
     *          faceAuthenticationSimilarity 相似度
     * @throws AuthenticationException
     */
    Map<String, String> baiDuFaceAuthentication(String name, String idCardNumber, String imageUrl, String authId, String appKey) throws AuthenticationException;

    /**
     * 百度人脸认证2
     * baiDuFaceAuthentication，图片支持base64和url
     */
    Map<String, String> baiDuFaceAuthentication2(String name, String idCardNumber, String imageSrc,int imageType, String authId, String appKey) throws AuthenticationException;


    /**
     * 百度人脸认证增强版
     * @param appType
     */
    Map<String, String> baiDuFaceAuthenticationSec(String name, String idCardNumber, String image, Integer appType, String authId, String appKey) throws AuthenticationException;

    /**
     * 百度人脸认证增强版2
     * 兼容baiDuFaceAuthenticationSec，图片支持base64和url
     * @param appType
     */
    Map<String, String> baiDuFaceAuthenticationSec2(String name, String idCardNumber, String imageSrc,int imageType, Integer appType, String authId, String appKey) throws AuthenticationException;

    /**
     * 百度人脸比对（增强版）
     * 增强版需要配合 SDK 使用
     * @param input
     * @return
     */
    FacePersonVerifyResult baiduEnhancerFaceMatch(FaceMatchInput input);

    /**
     * 百度人脸对比
     * 适用于 H5 场景
     */
    FacePersonVerifyResult baiduFaceMatch(FaceMatchInput input);

    /**
     * 视频活体检测 -> 获取随机验证码
     * @see #faceLiveVerify
     */
    FaceLiveSessionCode getFaceLiveSessionCode();

    /**
     * 视频活体检测 -> 活体检测
     * @see #getFaceLiveSessionCode
     */
    FaceLiveVerifyResult faceLiveVerify(FaceLiveVerifyInput input);

    /**
     * 更新人脸识别图片
     * @param input
     * @throws AuthenticationException
     */
    void updateFaceRecognitionPic(UpdateFacePicInput input) throws AuthenticationException;

    /**
     * 约车时更新人脸
     * @param input
     * @throws AuthenticationException
     */
    void orderVehicleUpdateFace(UpdateFacePicInput input) throws AuthenticationException ;

    /**
     * 驾驶证三要素认证
     * @param authId
     * @since 3.2.0
     * @remark since 3.2.0后，建议使用memberReviewService.drivingLicenseAuthenticate
     *         since 4.11.+后，下线驾照三要素校验功能
     */
    @Deprecated
    void driverLicenseElementsAuthenticate(String authId);

    /**
     * 驾驶证三要素认证
     * @param authId
     * @since 3.2.0
     * @remark since 3.2.0后，建议使用memberReviewService.drivingLicenseAuthenticate
     */
    @Deprecated
    void driverLicenseElementsAuthenticate(String authId, UpdateUserDto operator);

    /**
     * 查询会员驾照信息详情
     * @param authId
     * @return
     * @since 2.16.1
     * @remark 目前未做三要素认证的会员，不做查询
     */
    DriverLicenseQueryResultDTO queryDriverLicenseDetail(String authId, UpdateUserDto operator);

    /**
     * 保存驾驶证认证记录
     * @param input
     * @throws AuthenticationException
     * @remark 建议使用memberReviewService中同名接口替换，另外建议提供authId.
     */
    @Deprecated
    void saveDriverLicenseElementsAuthenticateRecord(SaveDriverElementsAuthenticateInput input) throws AuthenticationException;

    /**
     * 保存驾驶证认证日志
     * @param input
     * @remark 此接口建议不再单独调用
     */
    @Deprecated
    void saveDriverLicenseElementsAuthenticateLog(SaveDriverElementsAuthenticateLogInput input);

    /**
     * 获取外部会员驾照三要素最新认证结果
     * @param authId
     * @return
     * @since 2.6.1
     */
    DriverLicenseAuthResultDTO getLastDriverLicenseAuthResult(String authId);

    /**
     * 获取会员驾照三要素最新认证通过记录
     * @param authId
     * @return
     * @since 2.7.0
     */
    DriverLicenseAuthResultDTO getLastAuthenticateRecordByUser(String authId,Integer membershipType);

    /**
     * 获取会员驾照三要素认证日志
     * @param recordId
     * @param logType 0三要素 1驾照分数
     * @return
     * @since 2.7.0
     */
    DriverLicenseElementsAuthenticateLogDTO getLastAuthenticateLogByRecordId(Long recordId, Integer logType);

    /**
     * 获取会员最新的n条驾照三要素认证明细
     * @param authId
     * @param logType
     * @param num
     * @return
     * @remark 最多查询前20条认证记录
     */
    List<DriverLicenseElementsAuthenticateLogDTO> getLastAuthenticateLogsByUser(String authId, Integer logType, Integer num);


    /**
     * 上传会员图片
     * @param type 类型 1驾照图片 2手持证件照 3证件照 4人脸照片 5 开门人脸图片 6 修改手机号人脸图片7身份证正面和反面
     * @param byteArray 上传图片
     * @param authId 会员authId
     * @return
     * @throws BusinessException
     */
    UploadMemberImageDto uploadMemberImage(Integer type, byte[] byteArray, String authId) throws BusinessException;

    /**
     * 根据userid查询用户tag
     * @param authId
     * @return
     */
    MmpUserTagDto queryUserTagByAuthId(String authId);


    /**
     * 根据userid查询用户tag
     * @param authIds
     * @return
     */
    List<MmpUserTagDto> queryUserTagListByAuthIds(List<String> authIds);

    /**
     * 根据userid更新用户tag
     * @param userTagDto
     * @return
     */
    int updateUserTagByAuthId(MmpUserTagDto userTagDto);

    /**
     * 插入日志
     * @param userOperatorLogDTO
     * @return
     */
    boolean insertUserOperateLog(UserOperatorLogDTO userOperatorLogDTO);

    /**
     * 获取企业会员卡列表
     * @param orgId
     * @param cardStatus 卡使用状态 0:空闲 1:使用中 -1:全部
     * @return
     */
    List<QueryOrgCardInfoDto> queryOrgCardList(String orgId, Integer cardStatus);

    /**
     * ocr提取驾照信息
     * @param fileByte
     * @return
     * @throws AuthenticationException
     */
    String ocrDriverLicense(byte[] fileByte) throws AuthenticationException;

    /**
     * 飞猪更新用户信息
     * @param inputDto
     * @throws AuthenticationException
     */
    @Deprecated
    void updateMemberInfoFromFlyingPig(UpdateMemberInfoFromFlyingPigInputDto inputDto) throws AuthenticationException;

    /**
     * 检查设备是否在黑名单内
     * @param imei
     * @return
     */
    Boolean checkImeiBlackList(String imei);

    /**
     * 根据手机号查询会员, 包含图片字段.<br>
     * @param authId				手机号.<br>
     * @param membershipType	会员类型.<br>
     * @return
     * @since 2.13.0
     */
    MembershipBlobDTO getMembershipByAuthId(String authId, int membershipType);

    /**
     * 根据企业用户登录名查询企业信息
     * @param name
     * @return
     * @remark TODO 关联iplatSchema，确认此方法是否在使用
     */
    EnterpriseAgencyInfoDto queryAgencyInfoByName(String name);

    /**
     * 检验驾照真实性和有效性
    * @Title: checkDriverLicenseValid
    * @Description: 检验驾照真实性和有效性
    * @param @param name 姓名
    * @param @param cardNo 驾驶证号
    * @param @param archviesNo 档案编号
    * @param @return    参数
    * @return DriverLicenseValidResultDto    返回类型
    * @throws
     */
    DriverLicenseValidResultDto checkDriverLicenseValid(String name,String cardNo,String archviesNo);


    Boolean isExistMemberClauseLog(String authId);


    /**
     * 检查会员是否已经制卡，无则制卡
     * @param authId
     * @throws AuthenticationException
     * @since 3.2.0
     */
    void setVirtualCard(String authId) throws AuthenticationException;

    void setVirtualCard(String authId,int memberType) throws AuthenticationException;

    /**
     * 根据authId查询用户健康码颜色
     * @param authId
     * @param type 1:查询是否存在 2：查询健康码颜色
     * @return
     */
    HealthCodeDto queryHealthCode(String authId,Integer type);

    /**
     * 查询重庆会员-认证通过的会员
     * @param orgId 会员机构
     * @param createdTime 创建时间
     * @param updatedTime 更新时间
     * @return
     */
    List<MembershipBasicInfo> queryEffectiveMemberForCq(String orgId, String createdTime, String updatedTime);

    /**
     *提交押金监管协议
     */
    void submitDepositSecurity(SubmitDepositSecurityInput depositSecurityInput);



    /**
     * 判断邀请码是否有效
     * @param invitationCode
     */
    void checkInvitationCode(String invitationCode);

    /**
     * 获取用户的所属机构
     * @param authId
     * @return
     * @remark 无对应归属机构时，返回环球
     * @since 3.13.0
     */
    UserOrgInfoDto getMemberOrgInfo(String authId, Integer type);

    /**
     * 更新用户学生证url
     * @param authId
     * @param studentCardUrl
     */
    void updateUserStudentCardUrl(String authId, String studentCardUrl);

    /**
     * 获取用户年龄
     * 审核通过 + 三要素认证 + 大陆居民（包含军人），根据从身份证中提取的出生日期计算
     * @param pkId
     * @return
     * @remark 不推荐使用仅单个值的服务，使用IMembershipWrapService.getMemberWrapInfoByPkId替代
     */
    @Deprecated
    Integer getUserAge(Long pkId);

    /**
     * 驾驶证OCR识别（百度云）
     * @param drivingLicenseOcrRequestDto
     * @return
     * @remark 服务废弃，使用IMemberCertificationService替代。
     */
    @Deprecated
    DrivingLicenseOcrRespDto getDrivingLicenseOcr(DrivingLicenseOcrRequestDto drivingLicenseOcrRequestDto);

    /**
     * 保存用户修改手机号人脸比对结果
     * @param record
     * @return
     */
    int insertSelective(UserFaceContrastResultDto record);

    /**
     * 会员管理系统(外部)新增会员
     *
     * @param MembershipForMmpDTO
     * @return
     */
    String addMembershipInfoForMmp(MembershipForMmpDTO membershipForMmpDTO);

    /**
     * 手持证件照片与人脸相似度
     * @param holdIdImg
     * @param faceImg
     * @param authId
     * @throws AuthenticationException
     */
    void getFaceContrastResult(String holdIdImg,String faceImg,String authId)
            throws AuthenticationException;

    /**
     * 通过 token 获取用户信息
     * @param token
     * @return
     * @throws BusinessException
     */
    GetMemberByTokenDto getMembershipBasicInfoByToken(String token) throws BusinessException;

    /**
     * 更新会员二级渠道
     * @param authId
     * @param secondAppKey
     */
    void updateSecondAppKey(String authId, String secondAppKey);
}
