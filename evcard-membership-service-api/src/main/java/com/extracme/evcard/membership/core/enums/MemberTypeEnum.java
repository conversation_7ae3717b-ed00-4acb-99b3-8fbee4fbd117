package com.extracme.evcard.membership.core.enums;

public enum MemberTypeEnum {
    //会员类型(0:外部会员 1：内部员工 2：虚拟用户）
    OUT_USER(0, "外部用户"),
    INNER_USER(1, "内部用户"),
    QINGLU_USER(2, "虚拟用户");

    private Integer value;

    private String desc;

    MemberTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }


    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public boolean eq(Integer value) {
        return this.value.equals(value);
    }
}
