package com.extracme.evcard.membership.core.dto.input;

import lombok.Data;

import java.io.Serializable;

@Data
public class MemberReviewAdditionalInput implements Serializable {
    /** 用户ID **/
    private String authId;

    /** 操作日志 **/
    private String operatorContent;

    /** 勾选的审核项 **/
    private String reviewItems;

    private String mobilePhone;

    private String mail;

    private String sendMsg;

    /** 审核不通过详情 **/
    private String reviewRemark;

    private String imeiChangeNum;

    /** 审核不通过概要编号(1:姓名不正确，2：邮寄地址不正确，3：照片不正确) **/
    private String reviewItemIds;

    /** 审核不通过原因 **/
    private String reviewItemName;

    /** 认证状态 认证状态 0 未认证 1 未刷脸 2 已认证 */
    private String authenticationStatus;
    /** 1 ocr提取  2 手动输入*/
    private String driverLicenseInputType;
}
