package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;

/**
 * .卡号历史详情
 */
public class CardInfoHistoryDTO implements Serializable{
    private static final long serialVersionUID = 4622154155575324326L;

    /** 会员卡号 */
    private String cardNo;
    /**  证件号 */
    private String authId;
    /** 内部编号 */
    private String internalNo;
    /** 会员卡状态 0:未激活 1:已激活 2:失效 3:注销 */
    private Integer cardStatus;
    /** 卡号类型（1:虚拟卡号，2:物理卡号 */
    private Integer cardNoType;
    /** 备注 */
    private String remark;
    /**  创建时间 */
    private String createdTime;
    /**  创建用户  */
    private String createdUser;
    /**  更新时间 */
    private String updatedTime;
    /**  更新用户 */
    private String updatedUser;

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public String getInternalNo() {
        return internalNo;
    }

    public void setInternalNo(String internalNo) {
        this.internalNo = internalNo;
    }

    public Integer getCardStatus() {
        return cardStatus;
    }

    public void setCardStatus(Integer cardStatus) {
        this.cardStatus = cardStatus;
    }

    public Integer getCardNoType() {
        return cardNoType;
    }

    public void setCardNoType(Integer cardNoType) {
        this.cardNoType = cardNoType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public String getCreatedUser() {
        return createdUser;
    }

    public void setCreatedUser(String createdUser) {
        this.createdUser = createdUser;
    }

    public String getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(String updatedTime) {
        this.updatedTime = updatedTime;
    }

    public String getUpdatedUser() {
        return updatedUser;
    }

    public void setUpdatedUser(String updatedUser) {
        this.updatedUser = updatedUser;
    }

    @Override
    public String toString() {
        return "CardInfoHistoryDTO{" +
                "cardNo='" + cardNo + '\'' +
                ", authId='" + authId + '\'' +
                ", internalNo='" + internalNo + '\'' +
                ", cardStatus=" + cardStatus +
                ", cardNoType=" + cardNoType +
                ", remark='" + remark + '\'' +
                ", createdTime='" + createdTime + '\'' +
                ", createdUser='" + createdUser + '\'' +
                ", updatedTime='" + updatedTime + '\'' +
                ", updatedUser='" + updatedUser + '\'' +
                '}';
    }
}