package com.extracme.evcard.membership.core.dto.input;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName: SubmitDepositSecurityInput
 * @Author: wudi
 * @Date: 2020/9/14 18:40
 */
public class SubmitDepositSecurityInput implements Serializable {
    private static final long serialVersionUID = -4552318668149682897L;

    private String authId;

    private String version;

    private String url;

    private Date createTime;


    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
