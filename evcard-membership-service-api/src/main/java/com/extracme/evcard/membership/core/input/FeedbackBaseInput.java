package com.extracme.evcard.membership.core.input;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class FeedbackBaseInput implements Serializable {
    private static final long serialVersionUID = 6676796007932032431L;

    //用户名
    private String authId;

    //企业用户需要传入name
    private String name;

    //上报类型 0 网点推荐 1 车辆故障 2 充电桩故障 3 意见反馈' 不传为查看全部
    private Integer reportType;

    //上报类型 0 网点推荐 1 车辆故障 2 充电桩故障 3 意见反馈' 不传为查看全部
    private List<Integer> types;

    //车牌号
    private String vehicleNo;

    //订单号
    private String orderSeq;

    //手机号
    private String mobilePhone;

    //处理状态 0 未处理 1 生成任务(添加处理) 2 不予处理 3.无需处理'
    private Integer reportStatus;

    //开始时间
    private Date startTime;

    //截止时间
    private Date endTime;

    //查询时间
    private Date queryDate;

    public Date getQueryDate() {
        return queryDate;
    }

    public void setQueryDate(Date queryDate) {
        this.queryDate = queryDate;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public Integer getReportType() {
        return reportType;
    }

    public void setReportType(Integer reportType) {
        this.reportType = reportType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVehicleNo() {
        return vehicleNo;
    }

    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }

    public String getOrderSeq() {
        return orderSeq;
    }

    public void setOrderSeq(String orderSeq) {
        this.orderSeq = orderSeq;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public Integer getReportStatus() {
        return reportStatus;
    }

    public void setReportStatus(Integer reportStatus) {
        this.reportStatus = reportStatus;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public List<Integer> getTypes() {
        return types;
    }

    public void setTypes(List<Integer> types) {
        this.types = types;
    }
}
