package com.extracme.evcard.membership.core.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 会员所属机构信息
 * <AUTHOR>
 * @Discription
 * @date 2021/1/14
 */
@Data
public class UserOrgInfoDto implements Serializable {
    /**
     * 会员编号
     */
    private Long pkId;

    /**
     * 会员来源城市, 会员city_of_origin
     */
    private String cityOfOrigin;

    /**
     * 会员归属城市ID
     */
    private Long cityId;

    /**
     * 会员归属城市名称
     */
    private String city;

    /**
     * 会员所属企业编号
     */
    private Long orgSeq;

    /**
     * 会员所属企业ID
     */
    private String orgId;

    /**
     * 会员所属企业名称
     */
    private String orgName;

    /**
     * 会员关联企业
     */
    private String agencyId;

    /**
     * 会员归属企业名称
     */
    private String agencyName;
}
