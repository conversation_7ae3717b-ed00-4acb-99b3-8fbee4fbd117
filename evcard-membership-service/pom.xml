<?xml version="1.0"?>
<project
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
        xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.extracme.evcard</groupId>
        <artifactId>evcard-membership-rpc</artifactId>
        <version>4.12.62</version>
    </parent>
    <artifactId>evcard-membership-service</artifactId>
    <name>evcard-membership-service</name>
    <url>http://maven.apache.org</url>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<jedis.version>2.9.0</jedis.version>
    </properties>
    <dependencies>
		<dependency>
			<groupId>com.extracme.evcard</groupId>
			<artifactId>evcard-core-rpc</artifactId>
			<version>3.0.0</version>
		</dependency>
		<dependency>
			<groupId>com.extracme.evcard</groupId>
			<artifactId>evcard-membership-service-api</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>

		<!--引入spring-boot-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
				<exclusion>
					<artifactId>jackson-databind</artifactId>
					<groupId>com.fasterxml.jackson.core</groupId>
				</exclusion>
			</exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-actuator</artifactId>
        </dependency>

		<!--引入spring-boot mybatis相关-->
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjweaver</artifactId>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>com.alibaba</groupId>-->
<!--			<artifactId>druid</artifactId>-->
<!--		</dependency>-->
		<!--使用统一的mysql客户端版本-->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.freemarker</groupId>
			<artifactId>freemarker</artifactId>
			<version>2.3.31</version> <!-- 使用最新版本 -->
		</dependency>

		<dependency>
			<groupId>com.extracme.evcard</groupId>
			<artifactId>evcard-vipcard-service-api</artifactId>
			<version>2.2.62</version>
		</dependency>

		<dependency>
			<groupId>org.mybatis.spring.boot</groupId>
			<artifactId>mybatis-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.pagehelper</groupId>
			<artifactId>pagehelper-spring-boot-starter</artifactId>
			<version>1.2.13</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>org.junit.vintage</groupId>
					<artifactId>junit-vintage-engine</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!--apollo配置中心-->
		<dependency>
			<groupId>com.ctrip.framework.apollo</groupId>
			<artifactId>apollo-client</artifactId>
			<version>1.8.0</version>
		</dependency>

		<!--新版本redis工具包-->
		<dependency>
			<groupId>com.extracme.evcard</groupId>
			<artifactId>evcard-redis</artifactId>
			<version>2.8.0</version>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>com.extracme.evcard</groupId>-->
<!--			<artifactId>evcard-redis</artifactId>-->
<!--			<version>2.2.0</version>-->
<!--			<exclusions>-->
<!--				<exclusion>-->
<!--					<artifactId>evcard-protobuf</artifactId>-->
<!--					<groupId>com.extracme.evcard</groupId>-->
<!--				</exclusion>-->
<!--			</exclusions>-->
<!--		</dependency>-->


		<!-- dangdang-定时任务 -->
<!--		<dependency>-->
<!--			<groupId>com.dangdang</groupId>-->
<!--			<artifactId>elastic-job-lite-spring</artifactId>-->
<!--			<version>2.1.5-dubbo</version>-->
<!--		</dependency>-->
		<dependency>
			<groupId>com.extracme.evcard</groupId>
			<artifactId>evcard-elastic-job</artifactId>
			<version>1.1.0</version>
		</dependency>
		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>core</artifactId>
			<version>3.3.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>pdfbox</artifactId>
			<version>2.0.25</version>
		</dependency>
		<!-- dubbo&zk-->
		<dependency>
			<groupId>org.apache.dubbo</groupId>
			<artifactId>dubbo-spring-boot-starter</artifactId>
				<exclusions>
					<exclusion>
						<groupId>log4j</groupId>
						<artifactId>log4j</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.apache.curator</groupId>
						<artifactId>curator-client</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.apache.curator</groupId>
						<artifactId>curator-framework</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.alibaba</groupId>
						<artifactId>fastjson</artifactId>
					</exclusion>
				</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.dubbo</groupId>
			<artifactId>dubbo-registry-zookeeper</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
				<exclusion>
					<groupId>log4j</groupId>
					<artifactId>log4j</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>curator-recipes</artifactId>
					<groupId>org.apache.curator</groupId>
				</exclusion>
				<exclusion>
					<artifactId>fastjson</artifactId>
					<groupId>com.alibaba</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- 指定guava版本，解决elasticJob 和swagger 依赖版本冲突 -->
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>20.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-framework</artifactId>
			<version>4.0.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-client</artifactId>
			<version>4.0.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.curator</groupId>
			<artifactId>curator-recipes</artifactId>
			<version>4.0.1</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.zookeeper</groupId>
					<artifactId>zookeeper</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
		</dependency>
		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-core</artifactId>
		</dependency>
		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-classic</artifactId>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>jcl-over-slf4j</artifactId>
		</dependency>

		<!-- 阿里云消息队列 -->
		<dependency>
			<groupId>com.aliyun.openservices</groupId>
			<artifactId>ons-client</artifactId>
			<version>1.8.8.Final</version>
		</dependency>
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>ons20190214</artifactId>
			<version>1.0.0</version>
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <artifactId>okhttp</artifactId>-->
<!--                    <groupId>com.squareup.okhttp3</groupId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
        </dependency>


		<!-- 阿里云-对象存储oss -->
<!--		&lt;!&ndash;后续考虑切换为oss-starter，由于aliyun-java-sdk-core版本冲突-->
<!--			aliyun-java-sdk-green切换为高版本如 3.6.5-->
<!--			aliyun-java-sdk-core切换为高本版本如 4.4.5&ndash;&gt;-->
<!--		alibaba.cloud.oss.endpoint-->
<!--		alibaba.cloud.access-key-->
<!--		alibaba.cloud.secret-key-->
<!--		<dependency>-->
<!--			<groupId>com.alibaba.cloud</groupId>-->
<!--			<artifactId>aliyun-oss-spring-boot-starter</artifactId>-->
<!--			<version>1.0.0</version>-->
<!--		</dependency>-->
		<dependency>
			<groupId>com.aliyun.oss</groupId>
			<artifactId>aliyun-sdk-oss</artifactId>
			<version>3.5.0</version><!--$NO-MVN-MAN-VER$-->
		</dependency>
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-sts</artifactId>
			<version>2.1.6</version>
		</dependency>

		<!--内容安全服务提供的内容检测API SDK-->
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-green</artifactId>
			<version>3.0.0</version>
		</dependency>
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-core</artifactId>
			<version>3.0.7</version><!--$NO-MVN-MAN-VER$-->
		</dependency>

		<!-- 阿里云-人机交互认证 -->
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-afs</artifactId>
			<version>1.0.1</version>
		</dependency>


		<dependency>
			<groupId>com.extracme.evcard</groupId>
			<artifactId>evcard-mq-bean</artifactId>
			<version>3.0.0.53</version>
			<exclusions>
				<exclusion>
					<artifactId>jackson-annotations</artifactId>
					<groupId>com.fasterxml.jackson.core</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.extracme.evcard</groupId>
			<artifactId>evcard-protobuf</artifactId>
			<version>2.1.0</version>
			<exclusions>
				<exclusion>
					<groupId>javassist</groupId>
					<artifactId>javassist</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
		    <groupId>cglib</groupId>
		    <artifactId>cglib</artifactId>
		    <version>3.2.6</version>
		</dependency>
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-httpclient</groupId>
			<artifactId>commons-httpclient</artifactId>
			<version>3.1</version>
		</dependency>
		<dependency>
			<groupId>commons-dbcp</groupId>
			<artifactId>commons-dbcp</artifactId>
			<version>1.4</version>
		</dependency>
		<dependency>
			<groupId>com.ctc.sms</groupId>
			<artifactId>dahantc-http-json-api</artifactId>
			<version>1.0.0</version>
		</dependency>
		<dependency>
			<groupId>com.ctc.sms</groupId>
			<artifactId>ctc-smscloud-voice-jsonhttp</artifactId>
			<version>1.0.0</version>
		</dependency>
		<dependency>
			<groupId>net.sf.json-lib</groupId>
			<artifactId>json-lib</artifactId>
			<version>2.4</version>
			<classifier>jdk15</classifier>
		</dependency>
		<dependency>
			<groupId>com.baosight</groupId>
			<artifactId>iplat4j</artifactId>
			<version>3.9</version>
		</dependency>
		<dependency>
			<groupId>servlets.com</groupId>
			<artifactId>cos</artifactId>
			<version>05Nov2002</version>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpmime</artifactId>
			<version>4.5.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpcore</artifactId>
			<version>4.4.8</version>
		</dependency>
		<dependency>
			<groupId>com.sensorsdata.analytics.javasdk</groupId>
			<artifactId>SensorsAnalyticsSDK</artifactId>
			<version>3.0.0</version>
			<exclusions>
				<exclusion>
					<artifactId>jackson-databind</artifactId>
					<groupId>com.fasterxml.jackson.core</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- 法大大 合同生成包-->
		<dependency>
			<groupId>fadada</groupId>
			<artifactId>api_sdk</artifactId>
			<version>v20170829</version>
		</dependency>

		<!-- Servlet javax/servlet/SessionCookieConfig -->
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<version>3.0.1</version>
			<scope>provided</scope>
		</dependency>

		<!--优惠券服务-->
		<dependency>
			<groupId>com.extracme.evcard</groupId>
			<artifactId>evcard-coupon-service-api</artifactId>
			<version>1.1.2</version>
		</dependency>
		<!--消息推送服务-->
		<dependency>
			<groupId>com.extracme.evcard</groupId>
			<artifactId>evcard-messagepush-service-api</artifactId>
			<version>2.6.2</version>
		</dependency>
		<!-- 车辆服务 -->
		<dependency>
			<groupId>com.extracme.evcard</groupId>
			<artifactId>evcard-vehicle-service-api</artifactId>
			<version>2.24.0</version>
			<exclusions>
				<exclusion>
					<artifactId>evcard-sts-service-provider-api</artifactId>
					<groupId>com.extracme.evcard</groupId>
				</exclusion>
				<exclusion>
					<artifactId>evcard-sts-service-api</artifactId>
					<groupId>com.extracme.evcard</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- 企业会员服务 -->
		<dependency>
			<groupId>com.extracme.evcard</groupId>
			<artifactId>evcard-bvm-service-api</artifactId>
			<version>3.0.17</version>
			<exclusions>
				<exclusion>
					<groupId>javax.servlet</groupId>
					<artifactId>javax.servlet-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- 财务服务 -->
		<dependency>
		<groupId>com.extracme.evcard</groupId>
		<artifactId>evcard-pay-service-api</artifactId>
		<version>3.7.2</version>
			<exclusions>
				<exclusion>
					<artifactId>extracme-framework-core</artifactId>
					<groupId>com.extracme</groupId>
				</exclusion>
				<exclusion>
					<artifactId>evcard-mq-bean</artifactId>
					<groupId>com.extracme.evcard</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.extracme</groupId>
			<artifactId>extracme-framework-core</artifactId>
			<version>1.0.1</version>
		</dependency>

		<dependency>
			<groupId>net.dubboclub</groupId>
			<artifactId>restful</artifactId>
			<version>0.0.1</version>
		</dependency>

		<dependency>
			<groupId>com.extracme.evcard</groupId>
			<artifactId>evcard-shop-service-api</artifactId>
			<version>1.5.0</version>
		</dependency>

		<dependency>
			<groupId>com.extracme.evcard</groupId>
			<artifactId>evcard-activity-service-api</artifactId>
			<version>1.23.0</version>
			<exclusions>
				<exclusion>
					<artifactId>fastjson</artifactId>
					<groupId>com.alibaba</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.extracme.evcard</groupId>
			<artifactId>evcard-order-service-api</artifactId>
			<version>3.5.1</version>
		</dependency>

		<dependency>
			<groupId>com.extracme.evcard</groupId>
			<artifactId>evcard-ids-provider-api</artifactId>
			<version>2.2.0</version>
			<exclusions>
				<exclusion>
					<artifactId>evcard-redis</artifactId>
					<groupId>com.extracme.evcard</groupId>
				</exclusion>
				<exclusion>
					<artifactId>jackson-annotations</artifactId>
					<groupId>com.fasterxml.jackson.core</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.extracme.evcard</groupId>
			<artifactId>evcard-riskorder-service-api</artifactId>
			<version>2.19.1</version>
		</dependency>

		<dependency>
			<groupId>com.extracme.evcard</groupId>
			<artifactId>evcard-ccs-provider-api</artifactId>
			<version>1.0.0</version>
		</dependency>

		<dependency>
			<groupId>com.extracme.evcard</groupId>
			<artifactId>evcard-mtc-provider-api</artifactId>
			<version>1.4.0</version>
		</dependency>

		<dependency>
			<groupId>net.sourceforge.javacsv</groupId>
			<artifactId>javacsv</artifactId>
			<version>2.0</version>
		</dependency>

		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcprov-jdk15on</artifactId>
			<version>1.56</version>
		</dependency>

		<!-- JWT 相关依赖 -->
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-api</artifactId>
			<version>0.11.5</version>
		</dependency>
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-impl</artifactId>
			<version>0.11.5</version>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt-jackson</artifactId>
			<version>0.11.5</version>
			<scope>runtime</scope>
		</dependency>

		<!-- 单点 -->
		<dependency>
			<groupId>com.extracme.evcard</groupId>
			<artifactId>evcard-sso-service-provider-api</artifactId>
			<version>1.4.2</version>
			<exclusions>
				<exclusion>
					<artifactId>jackson-annotations</artifactId>
					<groupId>com.fasterxml.jackson.core</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.extracme.evcard</groupId>
			<artifactId>evcard-saic-service-api</artifactId>
			<version>1.0.0</version>
		</dependency>

		<dependency>
			<groupId>net.coobird</groupId>
			<artifactId>thumbnailator</artifactId>
			<version>0.4.8</version>
		</dependency>

		<dependency>
			<groupId>com.extracme.evcard</groupId>
			<artifactId>evcard-flow-service-api</artifactId>
			<version>1.7.0</version>
		</dependency>

		<dependency>
			<groupId>com.extracme.evcard</groupId>
			<artifactId>evcard-third-party-api</artifactId>
			<version>1.1.2</version>
			<exclusions>
				<exclusion>
					<artifactId>jackson-databind</artifactId>
					<groupId>com.fasterxml.jackson.core</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
			<version>4.5.0</version>
		</dependency>

		<dependency>
			<groupId>com.tencentcloudapi</groupId>
			<artifactId>tencentcloud-sdk-java</artifactId>
			<version>3.1.572</version>
		</dependency>

		<!-- itext7html转pdf  -->
		<dependency>
			<groupId>com.itextpdf</groupId>
			<artifactId>html2pdf</artifactId>
			<version>3.0.2</version>
		</dependency>
		<!-- 中文字体支持 -->
		<dependency>
			<groupId>com.itextpdf</groupId>
			<artifactId>font-asian</artifactId>
			<version>7.1.13</version>
		</dependency>


		<dependency>
			<groupId>com.esigntech</groupId>
			<artifactId>smUtil</artifactId>
			<version>1.3.3.2160</version>
		</dependency>
		<dependency>
			<groupId>com.esigntech</groupId>
			<artifactId>tech-sdk</artifactId>
			<version>2.1.60</version>
		</dependency>
		<dependency>
			<groupId>com.esigntech</groupId>
			<artifactId>tgtext</artifactId>
			<version>3.3.64.2160</version>
		</dependency>
		<dependency>
			<groupId>com.esigntech</groupId>
			<artifactId>utils</artifactId>
			<version>3.0.6.2160</version>
		</dependency>
	</dependencies>

    <build>
		<finalName>evcard-membership-rpc</finalName>
        <plugins>
            <!-- 该插件会把应用打包为一个jar + 一个lib（放置依赖包） -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.3.12.RELEASE</version>
				<configuration>
					<mainClass>com.extracme.evcard.membership.App</mainClass>
					<outputDirectory>../target</outputDirectory>
				</configuration>
            </plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<version>3.2.0</version>
			</plugin>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.2</version>
                <configuration>
                    <!--配置文件的路径-->
                    <configurationFile>${basedir}/src/main/resources/generatorMybatisConfig.xml</configurationFile>
                    <overwrite>true</overwrite>
                </configuration>
            </plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-deploy-plugin</artifactId>
				<configuration>
					<skip>true</skip>
				</configuration>
			</plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <testFailureIgnore>true</testFailureIgnore>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                </includes>
                <!-- 是否替换资源中的属性 -->
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
<!--				<includes>-->
<!--					<include>**/*.properties</include>-->
<!--					<include>**/*.xml</include>-->
<!--				</includes>-->
<!--				<filtering>false</filtering>-->
            </resource>
        </resources>
    </build>
</project>
