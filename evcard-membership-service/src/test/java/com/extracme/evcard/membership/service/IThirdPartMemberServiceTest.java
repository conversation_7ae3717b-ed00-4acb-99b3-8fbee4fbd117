package com.extracme.evcard.membership.service;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.App;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.dto.RegisterDto;
import com.extracme.evcard.membership.core.input.NotifyLoginDto;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.membership.core.service.IMembershipWrapService;
import com.extracme.evcard.membership.core.service.IThirdPartMemberService;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs;
import com.extracme.evcard.rpc.exception.BusinessException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/2/8
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class IThirdPartMemberServiceTest {

    @Autowired
    private IMembershipWrapService membershipWrapService;

    @Autowired
    private IMemberShipService memberShipService;
    @Autowired
    private IThirdPartMemberService thirdPartMemberService;


    @Test
    public void userLoginNotify() {
        NotifyLoginDto notifyLoginDto = new NotifyLoginDto();
        notifyLoginDto.setUserId(6454L);
        notifyLoginDto.setSecondAppKey(ComUtil.SAIC_EH_SECOND_APP_KEY);
        try {
            thirdPartMemberService.userLoginNotify(notifyLoginDto);
        } catch (BusinessException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void registerV1() {
        String s = "{\"agreePolicy\":true,\"anonymousId\":\"ALPb3a1b4ad0b4014c37761b4dc402209c2b3a1\",\"appKey\":\"zhifubao_02\",\"area\":\"洪山区\",\"city\":\"武汉市\",\"invitationCode\":\"\",\"mobilePhone\":\"***********\",\"operationModel\":\"1\",\"province\":\"湖北省\",\"registerOrigin\":8,\"registerWay\":0,\"trackPlatForm\":\"Alipay\",\"uid\":\"Cf67d43f9e7194623abf557d2f1999451\"}";
        try {
            RegisterDto registerDto = JSON.parseObject(s, RegisterDto.class);
            String s2 = memberShipService.registerV1(registerDto, false);
            System.out.println(s2);
        } catch (Exception e) {

        }

        try {
            String s4 = "{\"agreePolicy\":true,\"anonymousId\":\"ALPb3a1b4ad0b4014c37761b4dc402209c2b3a1\",\"appKey\":\"second_suishenxing\",\"area\":\"洪山区\",\"city\":\"武汉市\",\"invitationCode\":\"\",\"mobilePhone\":\"15327347341\",\"operationModel\":\"1\",\"province\":\"湖北省\",\"registerOrigin\":8,\"registerWay\":0,\"trackPlatForm\":\"Alipay\",\"uid\":\"Cf67d43f9e7194623abf557d2f1999455\"}";
            RegisterDto registerDto2 = JSON.parseObject(s4, RegisterDto.class);
            String s5 = memberShipService.registerV1(registerDto2, false);
            System.out.println(s5);
        } catch (Exception e) {

        }


        try {
            String s6 = "{\"agreePolicy\":true,\"anonymousId\":\"ALPb3a1b4ad0b4014c37761b4dc402209c2b3a1\",\"appKey\":\"second_saic_eh\",\"area\":\"洪山区\",\"city\":\"武汉市\",\"invitationCode\":\"\",\"mobilePhone\":\"15327347344\",\"operationModel\":\"1\",\"province\":\"湖北省\",\"registerOrigin\":8,\"registerWay\":0,\"trackPlatForm\":\"Alipay\",\"uid\":\"Cf67d43f9e7194623abf557d2f1999454\"}";
            RegisterDto registerDto3 = JSON.parseObject(s6, RegisterDto.class);
            String uid = memberShipService.registerV1(registerDto3, false);
            System.out.println(uid);
        } catch (Exception e) {

        }
    }

    @Test
    public void idCardOcr() {
        Set<String> mobilePhones = new HashSet<>();
        mobilePhones.add("10222822123");
        mobilePhones.add("10223220176");
        mobilePhones.add("10223355635");
        mobilePhones.add("10224588868");
        mobilePhones.add("10225253527");
        List<MembershipBasicInfo> resultList = membershipWrapService.getMembersByMobilePhones(mobilePhones);
        System.out.println(1);
    }




}
