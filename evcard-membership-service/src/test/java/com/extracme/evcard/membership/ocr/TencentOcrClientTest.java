package com.extracme.evcard.membership.ocr;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.App;
import com.extracme.evcard.membership.core.enums.CommonSideEnums;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2022/8/18
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class TencentOcrClientTest {

    @Autowired
    private TencentOcrClient tencentOcrClient;

    @Test
    public void drivingLicenseOcrFront0() {
        CommonDrivingLicenseOcrReq req = new CommonDrivingLicenseOcrReq();
        req.setMid("123");
        req.setUrl("https://evcard-sensitive.oss-cn-shanghai.aliyuncs.com/test/sensitiveBucket/newDriverCardImg/1661739778779.jpg?Expires=**********&OSSAccessKeyId=TMP.3KdcYrpeuzstDcgMpSseT72V8dHzUwX8qDVEbQwq8wfx48GSroZBus9YszDA2WrxpMFbmUEpzfaKuGG56fZRihVXh2SCfp&Signature=h0zsqpBVQO7JEOGV4AZ%2BpWjHxcM%3D");
        req.setCommonSide(CommonSideEnums.FRONT);
        CommonDrivingLicenseOcrResp resp = tencentOcrClient.drivingLicenseOcr(req);
        System.out.println(JSON.toJSONString(resp));
    }

    @Test
    public void drivingLicenseOcrFront() {
        CommonDrivingLicenseOcrReq req = new CommonDrivingLicenseOcrReq();
        req.setMid("123");
        req.setUrl("https://evcard-sensitive.oss-cn-shanghai.aliyuncs.com/test/sensitiveBucket/newDriverCardImg/1661764898596.jpg?Expires=**********&OSSAccessKeyId=TMP.3KdcYrpeuzstDcgMpSseT72V8dHzUwX8qDVEbQwq8wfx48GSroZBus9YszDA2WrxpMFbmUEpzfaKuGG56fZRihVXh2SCfp&Signature=py5lu82fPGUPvJwm7WJyfd7vdPk%3D");
        req.setCommonSide(CommonSideEnums.FRONT);
        CommonDrivingLicenseOcrResp resp = tencentOcrClient.drivingLicenseOcr(req);
        System.out.println(JSON.toJSONString(resp));
    }

    @Test
    public void drivingLicenseOcrBack() {
        CommonDrivingLicenseOcrReq req = new CommonDrivingLicenseOcrReq();
        req.setMid("123");
        req.setUrl("https://evcard-sensitive.oss-cn-shanghai.aliyuncs.com/test/sensitiveBucket/newDriverCardImg/1661765021952.jpg?Expires=**********&OSSAccessKeyId=TMP.3KdcYrpeuzstDcgMpSseT72V8dHzUwX8qDVEbQwq8wfx48GSroZBus9YszDA2WrxpMFbmUEpzfaKuGG56fZRihVXh2SCfp&Signature=83Y4Z69kbSF6K%2FLx3ZmAJGEAT2M%3D");
        req.setCommonSide(CommonSideEnums.BACK);
        CommonDrivingLicenseOcrResp resp = tencentOcrClient.drivingLicenseOcr(req);
        System.out.println(JSON.toJSONString(resp));
    }

    // 由于使用url调用供应商，所以url不再支持本地路径，所以下面的单元测试注释了
//    @Test
//    public void drivingLicenseOcrFront() {
//        CommonDrivingLicenseOcrReq req = new CommonDrivingLicenseOcrReq();
//        req.setMid("123");
//        req.setUrl("d:/驾驶证正页.jpeg");
//        req.setCommonSide(CommonSideEnums.FRONT);
//        CommonDrivingLicenseOcrResp resp = tencentOcrClient.drivingLicenseOcr(req);
//        System.out.println(JSON.toJSONString(resp));
//    }
//
//    @Test
//    public void drivingLicenseOcrFront2() {
//        CommonDrivingLicenseOcrReq req = new CommonDrivingLicenseOcrReq();
//        req.setMid("123");
//        req.setUrl("https://evcard-sensitive.oss-cn-shanghai.aliyuncs.com/test/sensitiveBucket/newDriverCardImg/1661750571794.jpg?Expires=**********&OSSAccessKeyId=TMP.3KfLVJ2G46QBJxaQSyX3VLDh6SyG42dnKMwMcqtEE4bK5id36wRkdjGFs6CtMChEZNLXaTEk8pdowcSUupC5Z1Ft9ErqJc&Signature=odtzOfSXKd9Ntij5Crw7hA%2BrSK4%3D");
//        req.setCommonSide(CommonSideEnums.FRONT);
//        CommonDrivingLicenseOcrResp resp = tencentOcrClient.drivingLicenseOcr(req);
//        System.out.println(JSON.toJSONString(resp));
//    }
//
//    @Test
//    public void drivingLicenseOcrBack() {
//        CommonDrivingLicenseOcrReq req = new CommonDrivingLicenseOcrReq();
//        req.setMid("123");
//        req.setUrl("d:/驾驶证副页2.jpg");
//        req.setCommonSide(CommonSideEnums.BACK);
//        CommonDrivingLicenseOcrResp resp = tencentOcrClient.drivingLicenseOcr(req);
//        System.out.println(JSON.toJSONString(resp));
//    }
//
//    @Test
//    public void drivingLicenseOcrFrontCopy() {
//        CommonDrivingLicenseOcrReq req = new CommonDrivingLicenseOcrReq();
//        req.setMid("123");
//        req.setUrl("d:/驾驶证正页_复印件.jpg");
//        req.setCommonSide(CommonSideEnums.FRONT);
//        CommonDrivingLicenseOcrResp resp = tencentOcrClient.drivingLicenseOcr(req);
//        System.out.println(JSON.toJSONString(resp));
//    }
//
//    @Test
//    public void drivingLicenseOcrBackCopy() {
//        CommonDrivingLicenseOcrReq req = new CommonDrivingLicenseOcrReq();
//        req.setMid("123");
//        req.setUrl("d:/驾驶证副页2_复印件.jpg");
//        req.setCommonSide(CommonSideEnums.BACK);
//        CommonDrivingLicenseOcrResp resp = tencentOcrClient.drivingLicenseOcr(req);
//        System.out.println(JSON.toJSONString(resp));
//    }
//
//    @Test
//    public void drivingLicenseOcrFrontForever() {
//        CommonDrivingLicenseOcrReq req = new CommonDrivingLicenseOcrReq();
//        req.setMid("123");
//        req.setUrl("d:/驾驶证正页_长期.jpg");
//        req.setCommonSide(CommonSideEnums.FRONT);
//        CommonDrivingLicenseOcrResp resp = tencentOcrClient.drivingLicenseOcr(req);
//        System.out.println(JSON.toJSONString(resp));
//    }
//
//    @Test
//    public void drivingLicenseOcrFrontForeverCopy() {
//        CommonDrivingLicenseOcrReq req = new CommonDrivingLicenseOcrReq();
//        req.setMid("123");
//        req.setUrl("d:/驾驶证正页_长期_复印件.jpg");
//        req.setCommonSide(CommonSideEnums.FRONT);
//        CommonDrivingLicenseOcrResp resp = tencentOcrClient.drivingLicenseOcr(req);
//        System.out.println(JSON.toJSONString(resp));
//    }
}
