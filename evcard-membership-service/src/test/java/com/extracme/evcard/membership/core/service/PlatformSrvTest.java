package com.extracme.evcard.membership.core.service;

import com.extracme.evcard.membership.common.MidGenerator;
import com.extracme.evcard.membership.contract.esign.MemberContractEsignService;
import com.extracme.evcard.membership.contract.service.AddRentCarContractRequest;
import com.extracme.evcard.membership.contract.service.IMemberShipContractServ;
import com.extracme.evcard.membership.core.dto.AppKeyDto;
import com.extracme.evcard.membership.core.dto.PlatformInfoDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import com.extracme.evcard.membership.App;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class PlatformSrvTest {
    @Autowired
    IPlatformService platformService;
    @Autowired
    IMemberShipContractServ memberShipContractServ;

    public static final ExecutorService executor =  new ThreadPoolExecutor(4, 9, 60, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(5),new ThreadPoolExecutor.CallerRunsPolicy());



    @Test
    public void getcache() throws Exception {
        executor.execute(() -> {
            System.out.println("---------------------------" + platformService.getAllPlatforms());
        });
        List<PlatformInfoDTO> list = platformService.getAllPlatforms();
        Thread.sleep(6000);
        executor.execute(() -> {
            System.out.println(platformService.getAllPlatforms());
        });
        //System.out.println(platformService.getAppKeysByPlatformId(1L, null));
    }

    @Test
    public void testGetAppKeys1() throws Exception {
        Long platformId = null;
        String channelPurpose = "3";
        List<AppKeyDto> list = platformService.getAppKeys(platformId, channelPurpose, false);
        System.out.println(list.size());
    }

    @Test
    public void testGetAppKeys2() throws Exception {
        Long platformId = 4L;
        String channelPurpose = "3";
        List<AppKeyDto> list = platformService.getAppKeys(platformId, null, false);
        System.out.println(list.size());
    }

    @Test
    public void testGetAppKeys3() throws Exception {
        Long platformId = 4L;
        List<String> list = platformService.getAppKeysByPlatformId(platformId, false);
        System.out.println(list.size());
    }


    @Resource
    private MidGenerator midGenerator;
    @Test
    public void aa() throws Exception {
        String aaa = midGenerator.getMid();
        System.out.println(aaa);
    } @Test
    public void sss() throws Exception {

        AddRentCarContractRequest addRentCarContractRequest = new AddRentCarContractRequest();
        addRentCarContractRequest.setOperateType(1);
        addRentCarContractRequest.setType(1);
        addRentCarContractRequest.setPdfUrl("https://evcard.oss-cn-shanghai.aliyuncs.com/hqContract4.html");
        addRentCarContractRequest.setContractId("MC240515100003501");
        addRentCarContractRequest.setMid("2404031500002");
        addRentCarContractRequest.setUserSignPicUrl("http://evcard.oss-cn-shanghai.aliyuncs.com/files/_ali_e01ba12124ca42c4a5d0f85cc17ce6e8.md/dev/3a2856.png");
        // addRentCarContractRequest.set("2021-01-01");
        memberShipContractServ.addRentCarContract(addRentCarContractRequest);

    }

}
