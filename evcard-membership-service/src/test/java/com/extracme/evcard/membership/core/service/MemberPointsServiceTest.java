package com.extracme.evcard.membership.core.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.extracme.evcard.membership.core.dto.*;
import com.extracme.evcard.membership.credit.dto.*;
import com.extracme.evcard.membership.credit.service.IMemberPointsService;
import com.extracme.evcard.membership.vipcredits.anyolife.AnyolifeRestClient;
import com.extracme.evcard.membership.vipcredits.anyolife.entity.*;
import com.extracme.evcard.mq.bean.MemPointsPushEnum;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.dto.PageBeanDto;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import com.extracme.evcard.membership.App;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/8/17
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class MemberPointsServiceTest {
    @Autowired
    private IMemberPointsService memberPointsService;

    @Test
    public void queryCreditsAccount() throws Exception {
        MemberPointsAccountQueryDto input = new MemberPointsAccountQueryDto();
        input.setAuthId("12111103248155153329");
        input.setPointsType("01");
        MemberPointsAccountDto resp = memberPointsService.queryUserAccount(input);
        System.out.println(JSON.toJSONString(resp));
    }

    @Test
    public void queryCreditsHistory() throws Exception {
        MemberPointsHistoryQueryDto input = new MemberPointsHistoryQueryDto();
        input.setAuthId("15893645101133034730");
        input.setPointsType("01");
        input.setOptionType("02");
        input.setStartDate("**************");
        input.setEndDate("**************");
        input.setExpireType(null);
        input.setPageNum(1);
        input.setPageSize(50);
        PageBeanDto<MemberPointsHistoryDto> resp = memberPointsService.queryUserHistoryPage(input);
        System.out.println(JSON.toJSONString(resp));
    }


    @Test
    public void gainCredits() throws Exception {
        MemberPointsOfferDto input = new MemberPointsOfferDto();
        input.setAuthId("12111103248155153329");
        input.setPointsType("01");
        input.setEventType(23);
        input.setEventRefSeq("C2021071613490000025");
        input.setCreateTime(new Date());
        BaseResponse resp = memberPointsService.asyncOfferPoints(input);

        input = new MemberPointsOfferDto();
        input.setAuthId("12111103248155153329");
        input.setPointsType("01");
        input.setEventType(24);
        input.setPointsNum(100);
        input.setCreateTime(new Date());
        resp = memberPointsService.asyncOfferPoints(input);
    }

    @Test
    public void queryGainCredits() throws Exception {
        MemberPointsGainQueryDto input = new MemberPointsGainQueryDto();
        /*input.setAuthId("12111103248155153329");
        input.setPointsType("01");
        input.setFeeType(23);
        input.setEventRefSeq("C2021071613490000025");
        MemberPointsGainQueryResp resp = memberPointsService.queryGainCredits(input);


        input = new MemberPointsGainQueryDto();
        input.setAuthId("17666000001143255887");
        input.setPointsType("01");
        input.setFeeType(23);
        input.setEventRefSeq("C2021071613490000020");
        resp = memberPointsService.queryGainCredits(input);*/

        input = new MemberPointsGainQueryDto();
        input.setAuthId("17666000001143255887");
        input.setFeeType(18);
        MemberPointsGainQueryResp resp1 = memberPointsService.queryGainCredits(input);
        System.out.println(JSON.toJSONString(resp1));
    }

    @Test
    public void queryGainTitleCredits() throws Exception {
        UserTitlePointsQueryDto input = new UserTitlePointsQueryDto();
        input.setAuthId("12111103248155153329");
        input.setTitleClass(0);
        List<UserTitleUpgradeDto> data = new ArrayList<>();
        UserTitleUpgradeDto dto = new UserTitleUpgradeDto();
        dto.setIssueTime(new Date());
        dto.setOriginTitle(new UserTitleDto(0, 0));
        dto.setNewTitle(new UserTitleDto(1, 2));
        data.add(dto);

        dto = new UserTitleUpgradeDto();
        dto.setIssueTime(new Date());
        dto.setOriginTitle(new UserTitleDto(2, 1));
        dto.setNewTitle(new UserTitleDto(2, 3));
        data.add(dto);

        input.setUpgradeRecords(data);
        List<UserTitleUpgradePointsDto> resp = memberPointsService.queryGainCredits(input);
        System.out.println(JSON.toJSONString(resp));
    }

    @Test
    public void offerTitleRewardPoints() throws Exception {
        UserTitlePointsOfferDto input = new UserTitlePointsOfferDto();
        input.setAuthId("12111103248155153329");
        input.setTitleClass(0);
        List<UserTitleUpgradeDto> data = new ArrayList<>();
        UserTitleUpgradeDto dto = new UserTitleUpgradeDto();
        dto.setId(3L);
        dto.setIssueTime(new Date());
        dto.setOriginTitle(new UserTitleDto(0, 0));
        dto.setNewTitle(new UserTitleDto(1, 2));
        data.add(dto);
        dto = new UserTitleUpgradeDto();
        dto.setId(4L);
        dto.setIssueTime(new Date());
        dto.setOriginTitle(new UserTitleDto(2, 1));
        dto.setNewTitle(new UserTitleDto(2, 3));
        data.add(dto);

        input.setUpgradeRecords(data);
        BaseResponse resp = memberPointsService.offerTitleRewardPoints(input);
        System.out.println(JSON.toJSONString(resp));
    }

    @Test
    public void offerTitleRewardPointsSingle() throws Exception {
        UserTitlePointsOfferSingleDto input = new UserTitlePointsOfferSingleDto();
        input.setAuthId("12111103248155153329");
        input.setTitleClass(0);
        input.setId(4L);
        input.setIssueTime(new Date());
        input.setOriginTitle(new UserTitleDto(2, 1));
        input.setNewTitle(new UserTitleDto(2, 3));
        BaseResponse resp = memberPointsService.offerTitleRewardPointsSync(input);
        System.out.println(JSON.toJSONString(resp));
    }
}
