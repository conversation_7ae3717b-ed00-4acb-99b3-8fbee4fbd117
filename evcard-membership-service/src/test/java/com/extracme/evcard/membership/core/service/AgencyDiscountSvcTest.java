package com.extracme.evcard.membership.core.service;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.App;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.dao.CityMapper;
import com.extracme.evcard.membership.core.dto.MemberDiscountDTO;
import com.extracme.evcard.membership.core.dto.agency.AgencyDiscountDTO;
import com.extracme.evcard.membership.core.dto.discount.MmpDiscountPackageShareRuleDTO;
import com.extracme.evcard.membership.core.service.agency.AgencyDiscountServiceImpl;
import com.extracme.evcard.membership.core.service.agency.IAgencyDiscountService;
import com.extracme.evcard.rpc.base.service.IOrgService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;


import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;

//@RunWith(SpringJUnit4ClassRunner.class)
//@ContextConfiguration({"classpath:applicationContext.xml"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class AgencyDiscountSvcTest {

    @Autowired
    private AgencyDiscountServiceImpl agencyDiscountService;

    @Autowired
    private IOrgService orgService;

    @Resource
    private CityMapper cityMapper;

    @Test
    public void queryAgencyDiscount() throws Exception {
        cityMapper.selectCity("上海");

        //agency
        MemberDiscountDTO discountDTO1 = agencyDiscountService.queryAgencyCurrentDiscount("000201");

        orgService.selectByOrgid("000T");

        //personal
        MemberDiscountDTO discountDTO2 = agencyDiscountService.queryAgencyCurrentDiscount("0002");
    }

    @Test
    public void queryMemberDiscount() throws Exception {
        //agency 12111101499134806414
        MemberDiscountDTO discountDTO1 = agencyDiscountService.getMemberDiscount("15502107032162040633", 0);
    }

    @Test
    public void testUnvalidRule() throws Exception {
        MemberDiscountDTO discountDTO2 = agencyDiscountService.queryAgencyCurrentDiscount("0004");
    }

    @Autowired
    IAgencyDiscountService iAgencyDiscountService;

    @Test
    public void saveDiscountPackage(){
        Date date = new Date();
        MmpDiscountPackageShareRuleDTO discountPackageShareRuleDTO = new MmpDiscountPackageShareRuleDTO();
        discountPackageShareRuleDTO.setAgencyId("00");
        discountPackageShareRuleDTO.setPackageTypeList(Arrays.asList("1,4"));
        discountPackageShareRuleDTO.setShareType(0);
        discountPackageShareRuleDTO.setStatus(1);
        discountPackageShareRuleDTO.setCreateTime(date);
        discountPackageShareRuleDTO.setCreateOperId(123L);
        discountPackageShareRuleDTO.setCreateOperName("test");
        discountPackageShareRuleDTO.setUpdateTime(date);
        discountPackageShareRuleDTO.setUpdateOperId(123L);
        discountPackageShareRuleDTO.setUpdateOperName("test");
        iAgencyDiscountService.saveDiscountPackageShareRule(discountPackageShareRuleDTO);
    }

    @Test
    public void find(){
        System.out.println(JSON.toJSONString(iAgencyDiscountService.findDisCountPackageShareRule("00", new Date())));
    }

    @Test
    public void checkVehicleNo(){
        System.out.println(JSON.toJSONString(iAgencyDiscountService.checkVehicleNoLimit("003V", "苏")));
        System.out.println(JSON.toJSONString(iAgencyDiscountService.checkVehicleNoLimit("003V", "苏E")));

    }

    @Test
    public void getDiscount(){
        //System.out.println(JSON.toJSONString(iAgencyDiscountService.getAgencyDiscountConfig("0047", null, true)));
        AgencyDiscountDTO result = iAgencyDiscountService.getAgencyDiscountDetail("0047", null, false);
        System.out.println(JSON.toJSONString(result));

        Calendar calendar = Calendar.getInstance(ComUtil.timeZoneChina);
        calendar.set(Calendar.HOUR_OF_DAY, 9);
        result = iAgencyDiscountService.getAgencyDiscountDetail("0047", calendar.getTime(), false);
        System.out.println(JSON.toJSONString(result));
    }

}


