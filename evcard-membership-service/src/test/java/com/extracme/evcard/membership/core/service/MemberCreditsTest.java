package com.extracme.evcard.membership.core.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.extracme.evcard.membership.mq.MemberRawCreditsListener;
import com.extracme.evcard.membership.vipcredits.anyolife.AnyolifeRestClient;
import com.extracme.evcard.membership.vipcredits.anyolife.entity.*;
import com.extracme.evcard.mq.bean.MemPointsPushEnum;
import com.extracme.evcard.mq.bean.event.ChargeDeposit;
import com.extracme.evcard.mq.bean.event.OrderPay;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import com.extracme.evcard.membership.App;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/8/17
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class MemberCreditsTest {
    @Autowired
    private AnyolifeRestClient anyolifeRestClient;

    @Autowired
    private MemberRawCreditsListener memberRawCreditsListener;

    @Test
    public void depositRechargePushTest() throws Exception {
        ChargeDeposit chargeDeposit = new ChargeDeposit();
        chargeDeposit.setAuthId("101");
        chargeDeposit.setTradePlat("alipay");
        chargeDeposit.setChargeType(1);
        chargeDeposit.setAmount(new BigDecimal(1000));
        memberRawCreditsListener.handleChargeDepositEvent(chargeDeposit);
    }

    @Test
    public void orderPayPushTest() throws Exception {
        OrderPay orderPay = new OrderPay();
        orderPay.setAuthId("101");
        orderPay.setRealAmount(new BigDecimal(100));
        orderPay.setOrderSeq("C4983789561513515");
        orderPay.setOrderType(0);
        memberRawCreditsListener.handleOrderPayEvent(orderPay);
    }

    @Test
    public void queryCreditsAccount() throws Exception {
        BaseCreditsRequest input = new BaseCreditsRequest("01", "101");
        GetUserCreditsResponse response = anyolifeRestClient.getUserCreditsAccount(input);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void queryCreditsBefore() throws Exception {
        QueryCreditsBeforeRequest input = new QueryCreditsBeforeRequest("01", "15737920402171439617");
        input.setFeeType(17);
        QueryCreditsBeforeResponse response = anyolifeRestClient.queryGainCredits(input);
        System.out.println(JSON.toJSONString(response));

        input = new QueryCreditsBeforeRequest("01", "15737920402171439617");
        input.setFeeType(25);
        response = anyolifeRestClient.queryGainCredits(input);
        System.out.println(JSON.toJSONString(response));
    }


    @Test
    public void gainCreditsPoints() throws Exception {
        //固定积分发放
        GainCreditsRequest input = new GainCreditsRequest("01", "13917351715175801139");
        input.setFeeType(MemPointsPushEnum.POINTS_REWARD.getCode());
        input.setOrderNo("test_003");// + UUID.randomUUID());
        input.setGainCredits(new BigDecimal(100));
        JSONObject object = new JSONObject();
        object.put("remark", "账户初始化，初始发放100积分"); //"账户初始化，初始发放100积分"
        //input.setFeeDetail("{\"remark\":\"账户初始化，初始发放100积分\"}");
        input.setFeeDetail(object.toJSONString());
        GainCreditsResponse response = anyolifeRestClient.gainCredits(input);
        System.out.println(JSON.toJSONString(response));
        queryCreditsAccount();

        //充值押金
//        input = new GainCreditsRequest("01", "101");
//        input.setFeeType(MemPointsPushEnum.RECHARGE_DEPOSIT.getCode());
//        input.setOrderNo(UUID.randomUUID().toString());
//        input.setGainCredits(new BigDecimal(1000));
//        //input.setFeeDetail("{\"rechargeId\":\"0001\",\"amount\":\"1000\"}");
//        response = anyolifeRestClient.gainCredits(input);
//        System.out.println(JSON.toJSONString(response));
//        queryCreditsAccount();
    }

    @Test
    public void queryCreditsHistory() throws Exception {
        QueryCreditsHistoryRequest input = new QueryCreditsHistoryRequest("01", "12111103248155153329");
        input.setCreditType("02");
        input.setBegDate("**************");
        input.setEndDate("**************");
        input.setExpireType(null);
        input.setPage(1);
        input.setPageSize(50);
        QueryCreditsHistoryResponse response = anyolifeRestClient.queryUserCreditsHistory(input);
        System.out.println(JSON.toJSONString(response));
    }

//    Logger logger = (Logger) LoggerFactory.getLogger(MemberDepositServiceImpl.class);
//    public static final ExecutorService EXECUTOR =  new ThreadPoolExecutor(4, 300, 60, TimeUnit.SECONDS,
//            new ArrayBlockingQueue<>(5),new ThreadPoolExecutor.CallerRunsPolicy());
//    @Test
//    public void threadQueryCreditsAccount() throws Exception {
//        System.out.println("===========================");
//        String[] ids = {
//                "18870245758150447287",
//                "18870111705105811522",
//                "18870113383082709155",
//                "101",
//                "18870111705105811522",
//                "18870113383082709155",
//                "18870245758150447287",
//                "18870111705105811522",
//                "101",
//        };
//
//        for(int cnt = 0; cnt < 2; cnt++) {
//            for(int i = 0; i < 10; i ++) {
//                EXECUTOR.execute(()-> {
//                    try {
//                        long s = System.currentTimeMillis();
//                        String uid = ids[new Long(s % ids.length).intValue()];
//                        BaseCreditsRequest input = new BaseCreditsRequest("01", uid);
//                        GetUserCreditsResponse response = anyolifeRestClient.getUserCreditsAccount(input);
//                        long tm = (System.currentTimeMillis() - s);
//                        logger.info("============cost=" + tm + "， uid=" + uid + ", credits=" + response.getCredits());
//                        System.out.println("============cost=" + tm + "， uid=" + uid + ", credits=" + response.getCredits());
//                        System.out.println("===========result=" + JSON.toJSONString(response));
//                    }catch (Exception ex) {
//                        ex.printStackTrace();
//                    }
//                });
//            }
//            System.out.println("============cost= " + cnt + "==================");
//            Thread.sleep(20*1000);
//        }
//    }



    @Test
    public void queryCreditsBeforeTitle() throws Exception {
        UserTitlePointsQueryRequest input = new UserTitlePointsQueryRequest();
        input.setUid("1zzz11");
        List<TitleUpgradeInfo> data = new ArrayList<>();
        TitleUpgradeInfo info = new TitleUpgradeInfo();
        info.setBeforeClass("null/null");
        info.setAfterClass("T1/L1");
        info.setUpgradeTime("2021-04-23 09:00:00");
        data.add(info);

        TitleUpgradeInfo info1 = new TitleUpgradeInfo();
        info1.setBeforeClass("T1/L2");
        info1.setAfterClass("T2/L1");
        info1.setUpgradeTime("2021-04-23 11:00:00");
        data.add(info);
        input.setDatalist(data);
        TitlePointsQueryResponse response = anyolifeRestClient.queryCarbonReduceTitlePoints(input);
        System.out.println(JSON.toJSONString(response));
    }


    @Test
    public void offerCarbonReduceTitlePoints() throws Exception {
        //固定积分发放
        UserTitlePointsOfferRequest input = new UserTitlePointsOfferRequest();
        input.setUid("uid");
        input.setBeforeClass("T1/L2");
        input.setAfterClass("T1/L2");
        input.setUpgradeTime("2021-04-23 11:00:00");
        input.setOrderId("fkaga111");
        TitlePointsOfferResponse response = anyolifeRestClient.offerCarbonReduceTitlePoints(input);
        System.out.println(JSON.toJSONString(response));
        queryCreditsBeforeTitle();
    }


    @Test
    public void queryCommentsCreditsPoints() throws Exception {
        QueryCreditsBeforeRequest input = new QueryCreditsBeforeRequest("01", "15737920402171439617");
        input.setFeeType(25);
        input.setGainCredits(new BigDecimal(1));
        QueryCreditsBeforeResponse response = anyolifeRestClient.queryGainCredits(input);
        System.out.println(JSON.toJSONString(response));

        input = new QueryCreditsBeforeRequest("01", "15737920402171439617");
        input.setFeeType(26);
        input.setGainCredits(new BigDecimal(1));
        response = anyolifeRestClient.queryGainCredits(input);
        System.out.println(JSON.toJSONString(response));


        input.setGainCredits(new BigDecimal(2));
        response = anyolifeRestClient.queryGainCredits(input);
        System.out.println(JSON.toJSONString(response));
    }


    @Test
    public void gainCommentsCreditsPoints() throws Exception {
        //取车评价
        GainCreditsRequest input = new GainCreditsRequest("01", "13917351715175801139");
        input.setFeeType(25);
        input.setOrderNo("test_20220601_001");// + UUID.randomUUID());
        input.setGainCredits(new BigDecimal(1));
        JSONObject object = new JSONObject();
        object.put("orderSeq", "C2022060108151600000001");
        object.put("remark", "取车评价"); //"账户初始化，初始发放100积分"
        //input.setFeeDetail("{\"remark\":\"账户初始化，初始发放100积分\"}");
        input.setFeeDetail(object.toJSONString());
        GainCreditsResponse response = anyolifeRestClient.gainCredits(input);
        System.out.println(JSON.toJSONString(response));


        //还车评价
        input = new GainCreditsRequest("01", "13917351715175801139");
        input.setFeeType(26);
        input.setOrderNo("test_20220601_002");// + UUID.randomUUID());
        input.setGainCredits(new BigDecimal(2));
        object = new JSONObject();
        object.put("orderSeq", "C2022060108151600000001");
        object.put("remark", "还车评价"); //"账户初始化，初始发放100积分"
        response = anyolifeRestClient.gainCredits(input);
        System.out.println(JSON.toJSONString(response));
    }
}
