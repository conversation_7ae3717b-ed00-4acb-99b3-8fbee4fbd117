package com.extracme.evcard.membership.core.service;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.core.dto.CheckOrderDepositResp;
import com.extracme.evcard.membership.core.dto.CityDto;
import com.extracme.evcard.membership.core.dto.MemberAuthorizedLogDTO;
import com.extracme.evcard.membership.core.dto.input.*;
import com.extracme.evcard.membership.core.input.CheckDepositInput;
import com.extracme.evcard.membership.core.input.CheckDepositOrderInput;
import com.extracme.evcard.rpc.dto.Page;
import com.extracme.evcard.rpc.dto.PageBeanDto;
import org.apache.commons.lang.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import com.extracme.evcard.membership.App;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2018/7/16
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class MemberDepositServiceTest {

    @Autowired
    private MemberDepositServiceImpl memberDepositService;
    @Test
    public void queryDepositInfo() throws Exception {
        QueryDepositInfoInputDTO infoInputDTO = new QueryDepositInfoInputDTO();
        infoInputDTO.setAuthId("17600008033190207369");
        System.out.println("!!!!!!!!!!!!!!:"+ JSON.toJSONString(memberDepositService.queryDepositInfo(infoInputDTO)));
    }

    @Test
    public void queryDepositDetailList() throws Exception {
        System.out.println("!!!!!!!!!!!!!!:"+ JSON.toJSONString(memberDepositService.queryDepositDetailList("17600008000102109325")));
    }

    @Test
    public void checkDepositForOrderVehicle() throws Exception{
        CheckDepositInput checkDepositInput = new CheckDepositInput();
        checkDepositInput.setVehicleModelType(3);
        checkDepositInput.setAuthId("18616260963130847");
        checkDepositInput.setShopSeq(1516);
        checkDepositInput.setVehicleModelInfo("特斯拉Model S");
        CheckDepositResult checkDepositResult = memberDepositService.checkDepositForOrderVehicle(checkDepositInput);
        System.out.println(JSON.toJSONString(checkDepositResult));
    }

    @Test
    public void saveZhimaAuthorizedRecord() throws Exception {
        MemberAuthorizedRecordInput infoInputDTO = new MemberAuthorizedRecordInput();
        infoInputDTO.setAuthId("15026408738170901432");
        infoInputDTO.setAuthorizedType(1);
        infoInputDTO.setAuthorizedResult(1);
        infoInputDTO.setAuthorizedDateTime(new Date());
        infoInputDTO.setAuthorizedFraction(900);
        memberDepositService.saveZhimaAuthorizedRecord(infoInputDTO);
    }

    @Test
    public void canUseZhimaCitys(){
        List<CityDto> cityDtos = memberDepositService.canUseZhimaCitys();
        System.out.println(JSON.toJSONString(cityDtos));
    }

    @Test
    public void saveZhimaAuthorizedRecord2() throws Exception {
        MemberAuthorizedRecordInput infoInputDTO = new MemberAuthorizedRecordInput();
        infoInputDTO.setAuthId("15026408738170901432");
        infoInputDTO.setAuthorizedType(1);
        infoInputDTO.setAuthorizedResult(1);
        infoInputDTO.setAuthorizedDateTime(new Date());
        memberDepositService.saveZhimaAuthorizedRecord(infoInputDTO);
    }

    @Test
    public void saveZhimaAuthorizedRecord3() throws Exception {
        MemberAuthorizedRecordInput infoInputDTO = new MemberAuthorizedRecordInput();
        infoInputDTO.setAuthId("15026408738170901432");
        infoInputDTO.setAuthorizedType(2);
        infoInputDTO.setAuthorizedResult(1);
        infoInputDTO.setAuthorizedDateTime(new Date());
        memberDepositService.saveZhimaAuthorizedRecord(infoInputDTO);
    }

    @Test
    public void queryZhimaAuthorizedRecordPage() throws Exception {
        MemberAuthorizedRecordQueryInput infoInputDTO = new MemberAuthorizedRecordQueryInput();
        infoInputDTO.setAuthId("15026408738170901432");
//        infoInputDTO.setAuthorizedType(1);
//        infoInputDTO.setAuthorizedResult(1);
        Page page = new Page(1,2, true);
        infoInputDTO.setPage(page);
        PageBeanDto<MemberAuthorizedLogDTO> result = memberDepositService.queryZhimaAuthorizedRecordPage(infoInputDTO);

        System.out.println("!!!!!!!!!!!!!!:"+ JSON.toJSONString(result));
    }

    @Test
    public void queryZhimaAuthorizedRecords() throws Exception {
        List<Integer> types = Arrays.asList(0,1);
        List<MemberAuthorizedLogDTO> result = memberDepositService.queryUserZhimaAuthorizedRecords("15026408738170901432",
                types, null);
        System.out.println("!!!!!!!!!!!!!!:"+ JSON.toJSONString(result));
    }
    @Test
    public void checkDepositOrder() throws Exception {
      /*  CheckDepositInput checkDepositDto = new CheckDepositInput();
        checkDepositDto.setAuthId("17600008010165746548");
        checkDepositDto.setShopSeq(1516);
        checkDepositDto.setVehicleModelType(1);
        checkDepositDto.setVehicleModelInfo("荣威Ei5分时租赁版");*/
        CheckDepositInput checkDepositDto = JSON.parseObject("{\n" +
                "\t\"authId\": \"15729307771105729636\",\n" +
                "\t\"vehicleModelType\": 11,\n" +
                "\t\"shopSeq\": 18267,\n" +
                "\t\"vehicleModelInfo\": \"荣威Ei5分时租赁版\"\n" +
                "}",CheckDepositInput.class);

        CheckOrderDepositResult result = memberDepositService.checkDepositOrder(checkDepositDto);
        System.out.println("!!!!!!!!!!!!!!:"+ JSON.toJSONString(result));
    }


    @Test
    public void checkDepositOrder2() throws Exception {
      /*  CheckDepositInput checkDepositDto = new CheckDepositInput();
        checkDepositDto.setAuthId("17600008010165746548");
        checkDepositDto.setShopSeq(1516);
        checkDepositDto.setVehicleModelType(1);
        checkDepositDto.setVehicleModelInfo("荣威Ei5分时租赁版");*/
        CheckDepositOrderInput checkDepositDto = JSON.parseObject("{\n" +
                "\t\"authId\": \"18911110001140516712\",\n" +
                "\t\"vehicleModelType\": 1,\n" +
                "\t\"orgCode\": \"000T\",\n" +
                "\t\"vehicleModelInfo\": \"荣威E50\"\n" +
                "}",CheckDepositOrderInput.class);

        CheckOrderDepositResp checkOrderDepositResp = memberDepositService.checkDepositOrderCommon(checkDepositDto);
        System.out.println("!!!!!!!!!!!!!!:"+ JSON.toJSONString(checkOrderDepositResp));
    }
}