package com.extracme.evcard.membership.core.service;

import com.extracme.evcard.membership.core.dto.MemberInviteInfoDto;
import com.extracme.evcard.membership.core.dto.RegisterDto;
import com.extracme.evcard.membership.invitation.service.IMemberShipInvitationServ;
import com.extracme.evcard.rpc.messagepush.service.IMessagepushServ;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import com.extracme.evcard.membership.App;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.rmi.ssl.SslRMIClientSocketFactory;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class MemberShipInvitationServTest {

    @Autowired
    private IMemberShipInvitationServ memberShipInvitationServ;


    @Test
    public void testGetMemberShareInfo() throws Exception {
        MemberInviteInfoDto member = memberShipInvitationServ.getMemberInviteInfo(
                "123456789023873409",
                Short.valueOf("0"));
        System.out.println(member);
    }
}


