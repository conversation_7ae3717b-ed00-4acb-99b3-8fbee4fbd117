package com.extracme.evcard.membership.core.manager;

import com.extracme.evcard.membership.App;
import com.extracme.evcard.membership.core.dto.OperatorDto;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.MessageSource;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
class MemberDocumentManagerTest {

    @Resource
    private ConfigurableEnvironment environment;
    @Resource
    private MemberDocumentManager memberDocumentManager;

    @Test
    void hasOrder() {
        /*String mid ="2108261600001";
        String authid ="18888888888164228149";*/

        String mid ="1910081400101";
        String authid ="11122235100144948377";
        System.out.println(memberDocumentManager.hasOrder(mid, authid));
    }


    @Test
    void disableIdentityDocument2() {
        OperatorDto operatorDto = new OperatorDto();
        operatorDto.setOperatorId(111L);
        operatorDto.setOperatorName("xl");
        memberDocumentManager.disableIdentityDocument2("1805091400002",operatorDto);
    }

    @Test
    void pt(){
        System.out.println(environment);
    }


}