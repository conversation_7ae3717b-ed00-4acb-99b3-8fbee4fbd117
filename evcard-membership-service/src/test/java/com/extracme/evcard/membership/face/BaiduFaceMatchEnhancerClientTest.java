package com.extracme.evcard.membership.face;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.third.baidu.*;
import com.extracme.evcard.membership.third.baidu.facelive.FaceLiveVerifyRequest;
import com.extracme.evcard.membership.third.baidu.facelive.FaceLiveVerifyResp;
import com.extracme.evcard.membership.third.baidu.facelive.SessionCodeRequest;
import com.extracme.evcard.membership.third.baidu.facelive.SessionCodeResult;
import com.extracme.framework.core.util.FileUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import com.extracme.evcard.membership.App;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Base64Utils;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

//@RunWith(SpringJUnit4ClassRunner.class)
//@ContextConfiguration({"classpath:applicationContext.xml"})
public class BaiduFaceMatchEnhancerClientTest {

    @Autowired
    BaiduFaceMatchEnhancerClient baiduFaceMatchEnhancerClient;

    @Autowired
    BaiduFaceMatchClient baiduFaceMatchClient;

    @Autowired
    BaiduFaceLiveNessClient baiduFaceLiveNessClient;

    @Test
    public void faceMatch() throws Exception {
        byte[] bytes = FileUtil.readFileByBytes("C:\\Users\\<USER>\\Desktop\\pic.jpg");
        String image1 = Base64Util.encode(bytes);

        byte[] bytes1 = FileUtil.readFileByBytes("C:\\Users\\<USER>\\Desktop\\pic.jpg");
        String image2 = Base64Util.encode(bytes1);

        FaceMatchEnhancerRequest input = new FaceMatchEnhancerRequest();
        input.setScene_type("H5");
        input.setRegister_image(image1);
        input.setRegister_image_type("BASE64");
        input.setImage(image2);
        input.setImage_type("BASE64");

        List<FaceMatchRequest> faceMatchRequests = new ArrayList<>();
        FaceMatchRequest imageDTO = new FaceMatchRequest();
        imageDTO.setImage(input.getImage());
        imageDTO.setImageType(input.getImage_type());

        FaceMatchRequest registerImageDTO = new FaceMatchRequest();
        registerImageDTO.setImage(input.getRegister_image());
        registerImageDTO.setImageType(input.getRegister_image_type());

        faceMatchRequests.add(imageDTO);
        faceMatchRequests.add(registerImageDTO);

        BaseResult<FaceMatchResult> matchResult = baiduFaceMatchClient.faceMatch(faceMatchRequests);
        System.out.println(JSON.toJSONString(matchResult));
    }

    @Test
    public void encode() throws IOException {
        byte[] bytes = FileUtil.readFileByBytes("C:\\Users\\<USER>\\Desktop\\pic.jpg");

        String encode = Base64Util.encode(bytes);
        System.out.println(encode);
    }

    @Test
    public void decode() throws IOException {
        byte[] bytes = FileUtil.readFileByBytes("C:\\Users\\<USER>\\Desktop\\base64.txt");

        byte[] decode = Base64Utils.decode(bytes);
        FileOutputStream fileOutputStream = new FileOutputStream("C:\\Users\\<USER>\\Desktop\\base64.jpg");
        fileOutputStream.write(decode);
    }


    @Test
    public void testGetSession() {
        SessionCodeRequest sessionCodeRequest = new SessionCodeRequest();
        sessionCodeRequest.setType(1);
        sessionCodeRequest.setMaxCodeLength(1);
        sessionCodeRequest.setMinCodeLength(1);
        BaseResult<SessionCodeResult> sessionCode = baiduFaceLiveNessClient.getSessionCode(sessionCodeRequest);

        System.out.println(JSON.toJSONString(sessionCode));
    }




    @Test
    public void testFaceLiveNess() throws IOException {
        byte[] bytes = FileUtil.readFileByBytes("C:\\Users\\<USER>\\Desktop\\output.mp4");

        String encode = Base64Util.encode(bytes);

        FaceLiveVerifyRequest faceLiveVerifyRequest = new FaceLiveVerifyRequest();
        faceLiveVerifyRequest.setSessionId("S612df7d124eea190020755");
        faceLiveVerifyRequest.setTypeIdentify("action");
        faceLiveVerifyRequest.setVideoBase64(encode);
//        System.out.println("=======================");
//        System.out.println(encode);
//        System.out.println("=======================");
//
//        BufferedWriter bufferedWriter = new BufferedWriter(new FileWriter(new File("C:\\Users\\<USER>\\Desktop\\1.txt")));
//        bufferedWriter.write(encode);
//        bufferedWriter.close();
        BaseResult<FaceLiveVerifyResp> verify = baiduFaceLiveNessClient.verify(faceLiveVerifyRequest);

        System.out.println(JSON.toJSONString(verify));
    }


    private BaseResult<SessionCodeResult> getDefaultSessionCode() {
        BaseResult<SessionCodeResult> sessionCode = new BaseResult<>();
        SessionCodeResult sessionCodeResult = new SessionCodeResult();
        sessionCodeResult.setSessionId("");
        sessionCode.setResult(sessionCodeResult);
        return sessionCode;
    }




}