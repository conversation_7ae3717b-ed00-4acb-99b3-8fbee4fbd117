package com.extracme.evcard.membership.core.service;


import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.core.dto.FeedbackBaseDto;
import com.extracme.evcard.membership.core.dto.input.DealFeedbackInput;
import com.extracme.evcard.membership.core.input.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import com.extracme.evcard.membership.App;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @Date 2018/9/11 14:40
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class MemberFeedbackServiceTest {

    @Autowired
    private IMemberFeedbackService memberFeedbackService;


    @Test
    public void reportVehicleMalfunction() {
        String vehicleReport = "{\"authId\":\"17200006070093810099\",\"createOperName\":\"王骁\",\"feedbackDesc\":\"测试\",\"imageUrls\":\"evcard/image/vehicleMalfunction/20181010160921498.jpg\",\"latitude\":31287840,\"longitude\":121171630,\"mobilePhone\":\"17200006070\",\"name\":\"王骁\",\"orderSeq\":\"C2018101016080000024\",\"vehicleNo\":\"沪FZ2192\",\"vin\":\"LSJE12839ES056509\"}";

        ReportVehicleMalfunctionInput input = new ReportVehicleMalfunctionInput();
        input = JSON.parseObject(vehicleReport,ReportVehicleMalfunctionInput.class);
//        input.setAuthId("******************");
//        input.setName("谌年");
//        input.setMobilePhone("158863427032");
//        input.setFeedbackDesc("车辆故障描述!!!!测试3");
//        input.setImageUrls("evcard/image/20180301134404106.jpg");
//        input.setCreateOperName("谌年");
//        input.setVehicleNo("沪B88888");
//        input.setVin("");
        memberFeedbackService.reportVehicleMalfunction(input);
    }

    @Test
    public void reportElectricPileMalfunction() {
        ReportElectricPileMalfunctionInput input = new ReportElectricPileMalfunctionInput();
        input.setAuthId("******************");
        input.setName("谌年");
        input.setMobilePhone("158863427032");
        input.setFeedbackDesc("充电桩故障描述!!!!测试3");
        input.setImageUrls("evcard/image/20180301134404106.jpg");
        input.setCreateOperName("谌年");
        input.setShopSeq(1516);
        input.setParkNum("3-1");
        memberFeedbackService.reportElectricPileMalfunction(input);
    }

    @Test
    public void reportRecommendBuildShop() {
        ReportRecommendBuildShopInput input = new ReportRecommendBuildShopInput();
        input.setAuthId("******************");
        input.setName("谌年");
        input.setMobilePhone("158863427032");
        input.setFeedbackDesc("推荐建网点");
        input.setCreateOperName("谌年");
        input.setShopAddressDesc("汽车城大厦");
        input.setShopAddressName("汽车城大厦北门");
        memberFeedbackService.reportRecommendBuildShop(input);
    }


    @Test
    public void reportOtherProblem() {
        ReportOtherProblemInput input = new ReportOtherProblemInput();
        input.setAuthId("******************");
        input.setName("谌年");
        input.setMobilePhone("158863427032");
        input.setFeedbackDesc("其他问题上报");
        input.setCreateOperName("谌年");
        memberFeedbackService.reportOtherProblem(input);
    }

    @Test
    public void queryFeedback(){
        FeedbackBaseInput feedbackBaseInput = new FeedbackBaseInput();
        feedbackBaseInput.setAuthId("******************");
        //feedbackBaseInput.setReportType(1);
//        feedbackBaseInput.setOrderSeq("C2018101010000000025");
//        feedbackBaseInput.setVehicleNo("沪AD12345");
        //feedbackBaseInput.setReportStatus(0);
        //feedbackBaseInput.setMobilePhone("123");
        //feedbackBaseInput.setStartTime(new Date());
        //feedbackBaseInput.setEndTime(new Date());
        List<FeedbackBaseDto> feedbackBaseDtos =  memberFeedbackService.queryFeedBackInfo(feedbackBaseInput);
        System.out.println(JSON.toJSONString(feedbackBaseDtos));
    }

    @Test
    public void queryFeedBackInfoPageTest(){
        QueryFeedbackInput queryFeedbackInput = new QueryFeedbackInput();
        queryFeedbackInput.setName("谌年");
        queryFeedbackInput.setAuthId("123");
        queryFeedbackInput.setMobilePhone("123456");
        queryFeedbackInput.setReportStatus(1);
        queryFeedbackInput.setVehicleNo("沪A88888");
        List<FeedbackBaseDto> feedbackBaseDtos = memberFeedbackService.queryFeedBackInfoPage(queryFeedbackInput);
        System.out.println(JSON.toJSONString(feedbackBaseDtos));
    }

    @Test
    public void dealFeedback(){
        DealFeedbackInput dealFeedbackInput = new DealFeedbackInput();
//        dealFeedbackInput.setDesc();
        dealFeedbackInput.setFeedbackType(2);
        dealFeedbackInput.setId(97);
//        dealFeedbackInput.setTaskId();
        dealFeedbackInput.setUpdateUser("wudi");
        dealFeedbackInput.setType(2);
        memberFeedbackService.dealFeedback(dealFeedbackInput);
    }

    @Test
    public void queryUnReadFeedbackReplyCount(){
        System.out.println(JSON.toJSONString(memberFeedbackService.queryUnReadFeedbackReplyCount("000N","apptest1")));

    }

}
