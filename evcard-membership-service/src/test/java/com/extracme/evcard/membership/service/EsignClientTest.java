/*
package com.extracme.evcard.membership.service;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.contract.esign.request.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import com.extracme.evcard.membership.App;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

*/
/**
 * <AUTHOR>
 * @Discription
 * @date 2021/2/19
 *//*

@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class EsignClientTest {

    @Autowired
    EsignRestClient esignRestClient;

    @Test
    public void testCreateAccount() throws Exception {
        CreateAccountRequest request = new CreateAccountRequest();
        request.setThirdPartyUserId("0");
        request.setIdType("CRED_PSN_UNKNOWN");
        request.setIdNumber("123");
        request.setName("测试");
        BaseResult<CreateAccountResponse> response = esignRestClient.CreateAccount(request);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testQueryAccount() throws Exception {
        BaseResult<QueryAccountResponse> response = esignRestClient.queryAccount("0");
        System.out.println(JSON.toJSONString(response));
    }


    @Test
    public void testUpdateAccount() throws Exception {
        BaseResult<QueryAccountResponse> response = esignRestClient.queryAccount("0");
        System.out.println(JSON.toJSONString(response));
        String accountId = response.getData().getAccountId();
        BaseResult<QueryAccountResponse> response1 = esignRestClient.updateAccountName(accountId, "测试一");
        System.out.println(JSON.toJSONString(response1));
    }

    @Test
    public void testUploadTemplate() throws Exception {
        String url = "http://evcard.oss-cn-shanghai.aliyuncs.com/dev/member_provision/SZ0042.pdf";
        BaseResult<UploadTemplateResponse> response = esignRestClient.uploadTemplate(url, "SZ0042.pdf", true);
        System.out.println(JSON.toJSONString(response));
    }


    @Test
    public void testAutoSign() throws Exception {
//        //创建账号
//        CreateAccountRequest request = new CreateAccountRequest();
//        request.setThirdPartyUserId("1");
//        request.setIdType("CRED_PSN_UNKNOWN");
//        request.setIdNumber("443");
//        request.setName("测试二");
//        BaseResult<CreateAccountResponse> response = esignRestClient.CreateAccount(request);
//        //{"code":"********","data":{"accountId":"b2761efcb8ab4237930f6fe99fea6573"},"message":"账号已存在","status":0}
//        System.out.println(JSON.toJSONString(response));
//        CreateAccountResponse resp = response.getData();
//        String accountId = resp.getAccountId();
//
//
//        //申请无期限静默签署
//        esignRestClient.setSilentSignAuth(accountId, null);

        String accountId = "f92c3efa65924295ad98c6458ef47383";
        //一部签署
        String fileId = "e7e3dfc79c934c7f972aab23bb327217";

//        GetSignFileRequest request = new GetSignFileRequest();
//        request.setFlowId("10d13ad08b254c16b5f51f58e6078111");
//        BaseResult<GetSignFileResponse> result111 = request.execute();

        BaseResult<FilesResponse> respon8se0 = esignRestClient.getfileInfo("f30aaedce59b479ebdd5357ef028327f");
        System.out.println(JSON.toJSONString(respon8se0));

        BaseResult<CreateFlowOneStepResponse> response1 = esignRestClient.personAutoOneStepSign(accountId, fileId, "隐私条款",
                450, 710);
        System.out.println(JSON.toJSONString(response1));

        CreateFlowOneStepResponse resp1 = response1.getData();
        if(resp1 != null) {
            String flowId = resp1.getFlowId();
            BaseResult<GetSignFileResponse> response2 = esignRestClient.getFileResponse(flowId);
            System.out.println(JSON.toJSONString(response2));
        }
//
//        String flowId = "f456ffc54a5046d8a600e6ca8720001a";
//        BaseResult<GetSignFileResponse> response2 = esignRestClient.getFileResponse(flowId);
//        System.out.println(JSON.toJSONString(response2));
    }



}
*/
