package com.extracme.evcard.membership.service;


import com.extracme.evcard.membership.App;
import com.extracme.evcard.membership.core.dto.blacklist.*;
import com.extracme.evcard.membership.core.service.IChannelBlacklistService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class ChannelBlacklistServiceTest {

    @Autowired
    private IChannelBlacklistService channelBlacklistService;

    @Test
    public void insert() {
        UpdateChannelBlacklistDto dto = new UpdateChannelBlacklistDto();
        dto.setBlacklistType(1);
        dto.setUserName("zwc");
        dto.setEnableStatus(1);
        dto.setCertificateType(1);
        dto.setCertificateNum("330682199212282834");
        dto.setRemark("啊啊啊啊啊");
        dto.setOperateUser("超级管理员");
        channelBlacklistService.insertOrUpdate(dto);
    }

    @Test
    public void updateById() {
        UpdateChannelBlacklistDto dto = new UpdateChannelBlacklistDto();
        dto.setId(4);
        dto.setRemark("啊啊啊啊啊13ab");
        dto.setMobilePhone("13811321411");
        dto.setEnableStatus(1);
        dto.setOperateUser("夏磊");
        channelBlacklistService.updateById(dto);
    }

    @Test
    public void getById() {
        ChannelBlacklistDto channelBlacklistDto = channelBlacklistService.getById(4);
        System.out.println(channelBlacklistDto);
    }

    @Test
    public void judgeBlacklist() {
        /**
        System.out.println(channelBlacklistService.judgeBlacklist("", "332603198006260011"));
        System.out.println(channelBlacklistService.judgeBlacklist("李东", "2112822000005286039"));
        System.out.println(channelBlacklistService.judgeBlacklist("", "2112822000005286039"));
        System.out.println(channelBlacklistService.judgeBlacklist("李东", ""));
        System.out.println(channelBlacklistService.judgeBlacklist("", ""));
        System.out.println(channelBlacklistService.judgeBlacklist(null, null));
        System.out.println(channelBlacklistService.judgeBlacklist(null, "2112822000005286039"));
        System.out.println(channelBlacklistService.judgeBlacklist("123456789101", "李东"));
        System.out.println(channelBlacklistService.judgeBlacklist("18122223333", "140101199001012222"));
         **/

        System.out.println(channelBlacklistService.judgeBlacklist("13012341234", "440823199809283641", "1", 1, "1"));
    }

    @Test
    public void listChannelBlacklist() {
        ListChannelBlacklistDto listChannelBlacklistDto = new ListChannelBlacklistDto();
        listChannelBlacklistDto.setPageNum(2);
        listChannelBlacklistDto.setPageSize(2);
        listChannelBlacklistDto.setUserName("");
        listChannelBlacklistDto.setCertificateNum("");
        listChannelBlacklistDto.setMobilePhone("");
        listChannelBlacklistDto.setEnableStatus(null);
        //listChannelBlacklistDto.setMobilePhone("12345678911");
        ListChannelBlacklistResponse listChannelBlacklistResponse = channelBlacklistService.listChannelBlacklist(listChannelBlacklistDto);
        System.out.println(listChannelBlacklistResponse);
    }

    @Test
    public void listChannelBlacklistLog() {
        ListChannelBlacklistLogDto listChannelBlacklistLogDto = new ListChannelBlacklistLogDto();
        listChannelBlacklistLogDto.setChannelBlacklistId(4);
//        listChannelBlacklistLogDto.setPageNum(2);
//        listChannelBlacklistLogDto.setPageSize(2);

        ListChannelBlacklistLogResponse response = channelBlacklistService.listChannelBlacklistLog(listChannelBlacklistLogDto);
        System.out.println(response);
    }

}
