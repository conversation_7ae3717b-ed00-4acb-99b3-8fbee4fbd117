package com.extracme.evcard.membership.core.service;

import com.alibaba.fastjson.JSON;
import com.baosight.iplat4j.util.util.DateUtil;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.dto.*;
import com.extracme.evcard.membership.core.dto.input.MemberContractQueryInput;
import com.extracme.evcard.membership.core.dto.input.ProvisionUserFeedbackInput;
import com.extracme.evcard.membership.core.dto.input.ProvisionUserTagInput;
import com.extracme.evcard.rpc.dto.Page;
import com.extracme.evcard.rpc.dto.PageBeanDto;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import com.extracme.evcard.membership.App;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/9/24
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class MemberProvisionServiceTest {

    @Autowired
    private IMemberProvisionService memberProvisionService;

    @Test
    public void getProvisionGroups() throws Exception {
        ProvisionGroupViewDto result = memberProvisionService.getProvisionGroups(3);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void getProvisionNodesOfGroup() throws Exception {
        List<ProvisionNodeDto> result = memberProvisionService.getProvisionNodesOfGroup(1L);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void addNodeTag() throws Exception {
        ProvisionUserTagInput input = new ProvisionUserTagInput();
        input.setNodeId("rates");
        input.setType(1);
        input.setTag(1);
        Long id = memberProvisionService.addNodeTag(null, input);

        input.setTagId(id);
        input.setTag(-1);
        memberProvisionService.addNodeTag(null, input);
    }

    @Test
    public void submitNodeFeedback() throws Exception {
        for(int i = 0; i < 8000; i ++) {
            ProvisionUserFeedbackInput input = new ProvisionUserFeedbackInput();
            input.setNodeId("rates");
            input.setType(3);
            input.setContent("i" + "jigagjagaga就会交给你感觉我测试实施测试");
            memberProvisionService.submitNodeFeedback(null, input);
        }
    }

    @Test
    public void queryUserTags() throws Exception {
        ProvisionUserTagDto result = memberProvisionService.queryUserTags(1L, "rates");
        System.out.println(JSON.toJSONString(result));
    }


    @Test
    public void queryUserFeedbacks() throws Exception {
        Page page = new Page();
        page.setPageSize(10);
        page.setPageNo(1);
        ProvisionFeedbackQueryDto queryDto = new ProvisionFeedbackQueryDto();
        queryDto.setNodeId("rates");
        queryDto.setProvisionId(1L);
        PageBeanDto<ProvisionUserFeedbackDto> result = memberProvisionService.queryUserFeedbacks(queryDto, page);
        queryDto.setStartDate(ComUtil.getDateFromStr("2020-09-01", ComUtil.DATE_TYPE5));
        queryDto.setEndDate(ComUtil.getDateFromStr("2020-09-29", ComUtil.DATE_TYPE5));
        result = memberProvisionService.queryUserFeedbacks(queryDto, page);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void testQueryUserContract() throws Exception {
//        MemberContractQueryInput input = new MemberContractQueryInput();
//        input.setAuthId("17200005023194430415");
//        input.setProvisionType(1);
//        input.setDate(ComUtil.getDateFromStr("20180601", "yyyymmMMdd"));
//        UserContractViewDto result = memberProvisionService.queryMemberContract(input);
//        System.out.println(JSON.toJSONString(result));


//        input.setDate(ComUtil.getDateFromStr("20180401", "yyyymmMMdd"));
//        result = memberProvisionService.queryMemberContract(input);
//        System.out.println(JSON.toJSONString(result));


        UserContractInfo contract = memberProvisionService.getUserContractArchiveUrl("330382198601160061", "YS0064");
        System.out.println(JSON.toJSONString(contract));
    }


    @Test
    public void testCloseContract() throws Exception {
        MemberContractQueryInput input = new MemberContractQueryInput();
        input.setAuthId("330382198601160061");
        input.setDate(DateUtil.getDate(2021, 1, 10));
        input.setProvisionType(1);
        input.setVersionId("0021");
        UserContractViewDto contract = memberProvisionService.getMemberCloseContract(input);
        System.out.println(JSON.toJSONString(contract));
    }

}
