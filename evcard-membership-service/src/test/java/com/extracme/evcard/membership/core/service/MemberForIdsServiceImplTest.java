package com.extracme.evcard.membership.core.service;

import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.input.QueryMemberInputDTO;
import com.extracme.evcard.membership.core.input.QueryMemberListInputDTO;
import com.extracme.evcard.membership.core.input.UpdateMemberInputDTO;
import com.extracme.evcard.rpc.dto.Page;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import com.extracme.evcard.membership.App;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2019/8/26
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class MemberForIdsServiceImplTest {

    @Autowired
    private IMemberForIdsService memberForIdsService;

    @Test
    public void queryMemberInfo() throws Exception {
        QueryMemberInputDTO queryMemberInputDTO = new QueryMemberInputDTO();
        //queryMemberInputDTO.setAuthId("******************");
        //queryMemberInputDTO.setDriverCode("******************");
        queryMemberInputDTO.setMemberType(1);
        //queryMemberInputDTO.setMobilePhone("13477083078");
        //queryMemberInputDTO.setName("test");
        queryMemberInputDTO.setOrgId("000T");
        queryMemberInputDTO.setPersonnelState(Arrays.asList(1,2));
        MembershipBasicInfo membershipBasicInfo = memberForIdsService.queryMemberInfo(queryMemberInputDTO);
        System.out.println(membershipBasicInfo);
    }

    @Test
    public void queryMemberList() throws Exception {
        QueryMemberListInputDTO queryMemberListInputDTO = new QueryMemberListInputDTO();
        //queryMemberListInputDTO.setMobilePhone("134");
        //queryMemberLTistInputDTO.setIsLikeQueryMobilePhone(2);
        //queryMemberListInputDTO.setAuthIdList(Arrays.asList("******************"));
//        queryMemberListInputDTO.setName("张");
//        queryMemberListInputDTO.setIsLikeQueryName(3);
//        queryMemberListInputDTO.setOrgId("000T");
        queryMemberListInputDTO.setMemberType(1);
//        queryMemberListInputDTO.setDriverCode("12123213123");
       // queryMemberListInputDTO.setPage(new Page(1,10));
        List<MembershipBasicInfo> membershipBasicInfoList = memberForIdsService.queryMemberList(queryMemberListInputDTO);
        System.out.println(membershipBasicInfoList);

    }

    @Test
    public void updateMemberInfo() throws Exception {

        UpdateMemberInputDTO updateMemberInputDTO = new UpdateMemberInputDTO("18011112222143344800",1);
        updateMemberInputDTO.setName("王晓sssss");
        updateMemberInputDTO.setUpdateTime("20190826142200000");
        boolean b = memberForIdsService.updateMemberInfo(updateMemberInputDTO);
        System.out.println(b);
    }

    @Test
    public void queryInnerMemberNames() throws Exception {
        List<String> list = memberForIdsService.queryInnerMemberNames("金");
        System.out.println(list);
    }

    @Test
    public void queryInnerMemberMobiles() throws Exception {
        List<String> list = memberForIdsService.queryInnerMemberMobiles("133");
        System.out.println(list);
    }

}