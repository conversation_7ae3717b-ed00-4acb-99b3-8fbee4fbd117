package com.extracme.evcard.membership.service;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.App;
import com.extracme.evcard.membership.core.dto.AppKeyDto;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.dto.MembershipDetailInfo;
import com.extracme.evcard.membership.core.dto.md.MdRentalContract;
import com.extracme.evcard.membership.core.dto.md.QueryOrderPayInfoRes;
import com.extracme.evcard.membership.core.service.IMembershipWrapService;
import com.extracme.evcard.membership.core.service.IPlatformService;
import com.extracme.evcard.membership.core.service.md.MdOrderService;
import com.extracme.evcard.rpc.order.dto.OrderInfoDto;
import com.extracme.evcard.rpc.order.service.IntergationOrderService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/8/17
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class MdOrderServiceTest {

    @Autowired
    private IntergationOrderService intergationOrderService;

    @Autowired
    private MdOrderService mdOrderService;

    @Autowired
    private IMembershipWrapService membershipWrapService;

    @Autowired
    private IPlatformService platformService;

    @Test
    public void queryCreditsAccount11() throws Exception {
        AppKeyDto appKeyDto = platformService.getAppKey("zhifubao_02");
        System.out.println(appKeyDto);
    }


    @Test
    public void queryCreditsAccount() throws Exception {
        MdRentalContract resp = mdOrderService.getMdContract("*****************");
        System.out.println(JSON.toJSONString(resp));
    }

    @Test
    public void queryOrderPayInfo() throws Exception {
        QueryOrderPayInfoRes resp = mdOrderService.queryOrderPayInfo("*****************");
        System.out.println(JSON.toJSONString(resp));
    }

    @Test
    public void getMemberLateOpenOrder() throws Exception {
        OrderInfoDto memberLateOpenOrder = intergationOrderService.getMemberLateOpenOrder("18000000044130730271", "**************");
        System.out.println(memberLateOpenOrder);
    }

    @Test
    public void getMemberByMid() throws Exception {
        MembershipBasicInfo memberByMid = membershipWrapService.getMemberByMid("*************");
        System.out.println(memberByMid);
    }

    @Test
    public void getMemberWithAdditionByMid() throws Exception {
        MembershipDetailInfo info = membershipWrapService.getMemberWithAdditionByMid("*************");
        System.out.println(info);
    }
}
