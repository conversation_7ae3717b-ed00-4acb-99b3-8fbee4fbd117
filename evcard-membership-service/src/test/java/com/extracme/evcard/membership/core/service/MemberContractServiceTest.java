package com.extracme.evcard.membership.core.service;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.extracme.evcard.membership.contract.service.IMemberContractSignProvider;
import com.extracme.evcard.membership.contract.service.MemberShipContractServ;
import com.extracme.evcard.membership.core.dto.ContractVersionDto;
import com.extracme.evcard.membership.core.dto.SignContractDto;
import com.extracme.evcard.membership.core.dto.UserContractKeyDto;
import com.extracme.evcard.membership.core.model.UserContract;
import com.extracme.evcard.membership.job.MemberContractArchiveJob;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import com.extracme.evcard.membership.App;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.*;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class MemberContractServiceTest {

    @Autowired
    private MemberShipContractServ memberShipContractServ;

    @Resource(name = "fddContractService")
    private IMemberContractSignProvider memberContractSignProvider;


    @Test
    public void queryAgencyDiscount() throws Exception {
        Map<String, UserContractKeyDto> data = new HashMap<>();
        Map<String,String> map = new HashMap<>();
        map.put("9fb07ab12c17459d97e7ad98060410d6", "test");
        map.put("a2560ba79a8f4dcebfb7cfed8b9f652d", "test");
        map.put("ee0daaecb74d4278a4309295f7df46d8", "test");
        map.put("20c1cae6d783480bb92a0f50a9b6316f", "test");
        map.put("d8475f6cf01b4e0ca08d91169a18e4d9", "test");
        map.put("8c1f4ad37091434dab492c8636f63c71", "test");
        map.put("3e96254f469d488196d8abbebe6814d7", "test");
        map.put("142a3f89160545aa93f18a34e6d2dd55", "test");
        map.put("927d38710e1e4ced8416e88ffa486d0a", "test");
        map.put("c6c58f71e70e4aa8b735009d89b0e62a", "test");
        map.put("edb0d24e6d83446082582e1fd48f92c6", "test");

        for(String key : map.keySet()) {
            UserContractKeyDto user = new UserContractKeyDto();
            user.setAuthId("1");
            user.setContractId(key);
            user.setAuthId("111");
            user.setSupplier(0);
            data.put(key, user);
        }
        memberContractSignProvider.batchDownloadContract(data);
    }

    @Test
    public void autoSign() throws Exception {
        SignContractDto dto = new SignContractDto();
        List<ContractVersionDto> list = new ArrayList<ContractVersionDto>();
        //ContractVersionDto contract1 = new ContractVersionDto("SZ0064", "0053", "会员守则");
        ContractVersionDto contract2 = new ContractVersionDto("YS0064", "0064", "隐私政策");
        //list.add(contract1);
        list.add(contract2);
        dto.setAuthId("18234239999134058301");
        dto.setContractVersionDtoList(list);
        memberShipContractServ.autoSignContract(dto);
    }

    @Resource
    MemberContractArchiveJob memberContractArchiveJob;
    @Test
    public void archive() throws Exception {
        memberContractArchiveJob.doWork(30000L, 30250L);
    }

}


