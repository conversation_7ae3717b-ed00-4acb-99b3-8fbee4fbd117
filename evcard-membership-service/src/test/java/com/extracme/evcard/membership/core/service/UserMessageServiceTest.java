package com.extracme.evcard.membership.core.service;


import com.extracme.evcard.membership.credit.dto.UserMessageDto;
import com.extracme.evcard.membership.credit.service.UserMessageServ;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import com.extracme.evcard.membership.App;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class UserMessageServiceTest {


    @Autowired
    UserMessageServ userMessageServ;

    @Test
    public void saveUserMessageTest() throws Exception{
        UserMessageDto userMessage = new UserMessageDto();
        userMessage.setAuthId("");
        userMessage.setMembershipType(new BigDecimal(0));
        userMessage.setMessageContent("测试");
        userMessage.setType(new BigDecimal(2));
        userMessage.setMessageTitle("审核通过之后的制卡通");
        userMessage.setCreatedUser("EngineServer");
        Long id = userMessageServ.saveUserMessage(userMessage);
        System.out.println(id);
    }
}
