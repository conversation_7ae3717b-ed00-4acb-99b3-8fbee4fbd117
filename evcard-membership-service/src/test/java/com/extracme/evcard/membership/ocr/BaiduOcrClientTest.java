package com.extracme.evcard.membership.ocr;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.App;
import com.extracme.evcard.membership.core.enums.CommonSideEnums;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2022/8/18
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class BaiduOcrClientTest {

    @Autowired
    private BaiduOcrClient baiduOcrClient;

    @Test
    public void driverLicenseOCRFront() {
        CommonDrivingLicenseOcrReq req = new CommonDrivingLicenseOcrReq();
        req.setMid("123");
        req.setUrl("https://evcard-sensitive.oss-cn-shanghai.aliyuncs.com/test/sensitiveBucket/newDriverCardImg/1661764898596.jpg?Expires=**********&OSSAccessKeyId=TMP.3KdcYrpeuzstDcgMpSseT72V8dHzUwX8qDVEbQwq8wfx48GSroZBus9YszDA2WrxpMFbmUEpzfaKuGG56fZRihVXh2SCfp&Signature=py5lu82fPGUPvJwm7WJyfd7vdPk%3D");
        req.setCommonSide(CommonSideEnums.FRONT);
        CommonDrivingLicenseOcrResp resp = baiduOcrClient.drivingLicenseOcr(req);
        System.out.println(JSON.toJSONString(resp));
    }

    @Test
    public void driverLicenseOCRBack() {
        CommonDrivingLicenseOcrReq req = new CommonDrivingLicenseOcrReq();
        req.setMid("123");
        req.setUrl("https://evcard-sensitive.oss-cn-shanghai.aliyuncs.com/test/sensitiveBucket/newDriverCardImg/1661765021952.jpg?Expires=**********&OSSAccessKeyId=TMP.3KdcYrpeuzstDcgMpSseT72V8dHzUwX8qDVEbQwq8wfx48GSroZBus9YszDA2WrxpMFbmUEpzfaKuGG56fZRihVXh2SCfp&Signature=83Y4Z69kbSF6K%2FLx3ZmAJGEAT2M%3D");
        req.setCommonSide(CommonSideEnums.BACK);
        CommonDrivingLicenseOcrResp resp = baiduOcrClient.drivingLicenseOcr(req);
        System.out.println(JSON.toJSONString(resp));
    }

    @Test
    public void idCardOCRFront() {
        CommonIdCardOcrReq req = new CommonIdCardOcrReq();
        req.setMid("123");
        req.setUrl("https://evcard-sensitive.oss-cn-shanghai.aliyuncs.com/test/sensitiveBucket/newIdCardImg/16000000104185346012/1662375245574.jpg?Expires=**********&OSSAccessKeyId=TMP.3KdcYrpeuzstDcgMpSseT72V8dHzUwX8qDVEbQwq8wfx48GSroZBus9YszDA2WrxpMFbmUEpzfaKuGG56fZRihVXh2SCfp&Signature=bCoJbW51UYrCIi9XgDfvUt8DNgo%3D");
        req.setCommonSide(CommonSideEnums.FRONT);
        CommonIdCardOcrResp resp = baiduOcrClient.idCardOcr(req);
        System.out.println(JSON.toJSONString(resp));
    }

    @Test
    public void idCardOCRBack() {
        CommonIdCardOcrReq req = new CommonIdCardOcrReq();
        req.setMid("123");
        req.setUrl("https://evcard-sensitive.oss-cn-shanghai.aliyuncs.com/test/sensitiveBucket/newIdCardImg/16000000104185346012/1662375284085.jpg?Expires=**********&OSSAccessKeyId=TMP.3KdcYrpeuzstDcgMpSseT72V8dHzUwX8qDVEbQwq8wfx48GSroZBus9YszDA2WrxpMFbmUEpzfaKuGG56fZRihVXh2SCfp&Signature=sZIsiXdm4LcSzjTleU2kDVOFj74%3D");
        req.setCommonSide(CommonSideEnums.BACK);
        CommonIdCardOcrResp resp = baiduOcrClient.idCardOcr(req);
        System.out.println(JSON.toJSONString(resp));
    }

    // 由于使用url调用供应商，所以url不再支持本地路径，所以下面的单元测试注释了
//    @Test
//    public void driverLicenseOCRFront() {
//        CommonDrivingLicenseOcrReq req = new CommonDrivingLicenseOcrReq();
//        req.setMid("123");
//        req.setUrl("d:/驾驶证正页.jpeg");
//        req.setCommonSide(CommonSideEnums.FRONT);
//        CommonDrivingLicenseOcrResp resp = baiduOcrClient.drivingLicenseOcr(req);
//        System.out.println(JSON.toJSONString(resp));
//    }
//
//    @Test
//    public void driverLicenseOCRFrontForver() {
//        CommonDrivingLicenseOcrReq req = new CommonDrivingLicenseOcrReq();
//        req.setMid("123");
//        req.setUrl("d:/驾驶证正页_长期.jpg");
//        req.setCommonSide(CommonSideEnums.FRONT);
//        CommonDrivingLicenseOcrResp resp = baiduOcrClient.drivingLicenseOcr(req);
//        System.out.println(JSON.toJSONString(resp));
//    }
//
//    @Test
//    public void driverLicenseOCRBack() {
//        CommonDrivingLicenseOcrReq req = new CommonDrivingLicenseOcrReq();
//        req.setMid("123");
//        req.setUrl("d:/驾驶证副页.jpg");
//        req.setCommonSide(CommonSideEnums.BACK);
//        CommonDrivingLicenseOcrResp resp = baiduOcrClient.drivingLicenseOcr(req);
//        System.out.println(JSON.toJSONString(resp));
//    }
//
//    @Test
//    public void driverLicenseOCRFrontCopy() {
//        CommonDrivingLicenseOcrReq req = new CommonDrivingLicenseOcrReq();
//        req.setMid("123");
//        req.setUrl("d:/驾驶证正页_复印件.jpg");
//        req.setCommonSide(CommonSideEnums.FRONT);
//        CommonDrivingLicenseOcrResp resp = baiduOcrClient.drivingLicenseOcr(req);
//        System.out.println(JSON.toJSONString(resp));
//    }
//
//    @Test
//    public void driverLicenseOCRFrontForverCopy() {
//        CommonDrivingLicenseOcrReq req = new CommonDrivingLicenseOcrReq();
//        req.setMid("123");
//        req.setUrl("d:/驾驶证正页_长期_复印件.jpg");
//        req.setCommonSide(CommonSideEnums.FRONT);
//        CommonDrivingLicenseOcrResp resp = baiduOcrClient.drivingLicenseOcr(req);
//        System.out.println(JSON.toJSONString(resp));
//    }
//
//    @Test
//    public void driverLicenseOCRBackCopy() {
//        CommonDrivingLicenseOcrReq req = new CommonDrivingLicenseOcrReq();
//        req.setMid("123");
//        req.setUrl("d:/驾驶证副页2_复印件.jpg");
//        req.setCommonSide(CommonSideEnums.BACK);
//        CommonDrivingLicenseOcrResp resp = baiduOcrClient.drivingLicenseOcr(req);
//        System.out.println(JSON.toJSONString(resp));
//    }
//
//    @Test
//    public void idCardOCRFront() {
//        CommonIdCardOcrReq req = new CommonIdCardOcrReq();
//        req.setMid("123");
//        req.setUrl("d:/身份证正面.jpg");
//        req.setCommonSide(CommonSideEnums.FRONT);
//        CommonIdCardOcrResp resp = baiduOcrClient.idCardOcr(req);
//        System.out.println(JSON.toJSONString(resp));
//    }
//
//    @Test
//    public void idCardOCRBack() {
//        CommonIdCardOcrReq req = new CommonIdCardOcrReq();
//        req.setMid("123");
//        req.setUrl("d:/身份证反面.jpg");
//        req.setCommonSide(CommonSideEnums.BACK);
//        CommonIdCardOcrResp resp = baiduOcrClient.idCardOcr(req);
//        System.out.println(JSON.toJSONString(resp));
//    }
//
//    @Test
//    public void idCardOCRBackForever() {
//        CommonIdCardOcrReq req = new CommonIdCardOcrReq();
//        req.setMid("123");
//        req.setUrl("d:/身份证反面_长期.jpg");
//        req.setCommonSide(CommonSideEnums.BACK);
//        CommonIdCardOcrResp resp = baiduOcrClient.idCardOcr(req);
//        System.out.println(JSON.toJSONString(resp));
//    }
//
//    @Test
//    public void idCardOCRFrontCopy() {
//        CommonIdCardOcrReq req = new CommonIdCardOcrReq();
//        req.setMid("123");
//        req.setUrl("d:/身份证正面_复印件.jpg");
//        req.setCommonSide(CommonSideEnums.FRONT);
//        CommonIdCardOcrResp resp = baiduOcrClient.idCardOcr(req);
//        System.out.println(JSON.toJSONString(resp));
//    }
//
//    @Test
//    public void idCardOCRBackCopy() {
//        CommonIdCardOcrReq req = new CommonIdCardOcrReq();
//        req.setMid("123");
//        req.setUrl("d:/身份证反面_复印件.jpg");
//        req.setCommonSide(CommonSideEnums.BACK);
//        CommonIdCardOcrResp resp = baiduOcrClient.idCardOcr(req);
//        System.out.println(JSON.toJSONString(resp));
//    }
//
//    @Test
//    public void idCardOCRBackForeverCopy() {
//        CommonIdCardOcrReq req = new CommonIdCardOcrReq();
//        req.setMid("123");
//        req.setUrl("d:/身份证反面_长期_复印件.jpg");
//        req.setCommonSide(CommonSideEnums.BACK);
//        CommonIdCardOcrResp resp = baiduOcrClient.idCardOcr(req);
//        System.out.println(JSON.toJSONString(resp));
//    }
}
