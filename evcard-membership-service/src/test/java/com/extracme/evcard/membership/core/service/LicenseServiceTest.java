package com.extracme.evcard.membership.core.service;


import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.App;
import com.extracme.evcard.membership.common.SeqGenerator;
import com.extracme.evcard.membership.core.dto.UpdateUserDto;
import com.extracme.evcard.membership.core.input.SaveDriverElementsAuthenticateLogInput;
import com.extracme.evcard.membership.core.service.license.DianWeiAuthPlusService;
import com.extracme.evcard.membership.core.service.license.DianWeiAuthService;
import com.extracme.evcard.membership.core.service.license.ShoukalaAuthService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class LicenseServiceTest {

    @Autowired
    private DianWeiAuthService dianWeiAuthService;

    @Autowired
    private DianWeiAuthPlusService dianWeiAuthPlusService;

    @Autowired
    private ShoukalaAuthService shoukalaAuthService;

    @Autowired
    private IMemberReviewService memberReviewService;

    @Autowired
    private SeqGenerator seqGenerator;

    @Test
    public void aaa() {
        for(int i = 0; i < 2000; i ++) {
            System.out.println(seqGenerator.genDianWeiReqSeq());
        }
    }

    @Test
    public void dianweiAuthenticatePlusTest() throws Exception {
        memberReviewService.drivingLicenseAuthenticate("16000000078220345370", UpdateUserDto.SYSTEM);


        SaveDriverElementsAuthenticateLogInput result = dianWeiAuthPlusService.authenticate("510902199811126954","黄鑫",  "320401697161", null);
        System.out.println(JSON.toJSONString(result));

        SaveDriverElementsAuthenticateLogInput result1 = dianWeiAuthPlusService.authenticate("53232619750402141X","刘销一",  "320401697161", null);
        System.out.println(JSON.toJSONString(result1));

        //DriverLicenseValidResultDto result = dianWeiAuthService.checkDriverLicenseValid("李洪为", "320723198304033615", "320401697161");
        //System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void shoukalaAuthenticateTest() throws Exception {
//        SaveDriverElementsAuthenticateLogInput result1 = shoukalaAuthService.authenticate("42011519870212007X","雷宜都",  "420103961357", null);
//        System.out.println(JSON.toJSONString(result1));

        SaveDriverElementsAuthenticateLogInput result = shoukalaAuthService.authenticate("李洪为", "320723198304033615", "320401697161", null);
        System.out.println(JSON.toJSONString(result));

        SaveDriverElementsAuthenticateLogInput result3 = shoukalaAuthService.authenticate("510902199811126954","黄鑫",  "320401697161", null);
        System.out.println(JSON.toJSONString(result));

        //DriverLicenseValidResultDto result = dianWeiAuthService.checkDriverLicenseValid("李洪为", "320723198304033615", "320401697161");
        //System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void dianweiAuthenticateTest() throws Exception {
        SaveDriverElementsAuthenticateLogInput result = dianWeiAuthService.authenticate("李洪为", "320723198304033615", "320401697161", null);
        System.out.println(JSON.toJSONString(result));

        //DriverLicenseValidResultDto result = dianWeiAuthService.checkDriverLicenseValid("李洪为", "320723198304033615", "320401697161");
        //System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void drivingLicenseAuthenticateTest() throws Exception {
        memberReviewService.drivingLicenseAuthenticate("16000000036131015762", UpdateUserDto.buildUserOperator("unitTest", ""));
//        SaveDriverElementsAuthenticateLogInput result = dianWeiAuthService.authenticate("李洪为",
//                "320723198304033615", "320401697161", 10);
    }
}
