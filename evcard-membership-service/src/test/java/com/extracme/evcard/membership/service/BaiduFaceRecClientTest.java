package com.extracme.evcard.membership.service;

import com.extracme.evcard.membership.core.dto.FacePersonVerifyResult;
import com.extracme.evcard.membership.core.dto.input.FacePersonVerifyInput;
import com.extracme.evcard.membership.core.service.IMemberFaceVerifyService;
import com.extracme.evcard.membership.face.BaiduFaceVerifyClient;
import com.extracme.evcard.membership.third.baidu.BaseResult;
import com.extracme.evcard.membership.third.baidu.PersonVerifyRequest;
import com.extracme.evcard.membership.third.baidu.PersonVerifyResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import com.extracme.evcard.membership.App;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @Discription
 * @date 2021/3/17
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class BaiduFaceRecClientTest {

    @Autowired
    BaiduFaceVerifyClient baiduFaceVerifyClient;

    @Autowired
    IMemberFaceVerifyService memberFaceVerifyService;

    @Test
    public void personVerifyTest() throws Exception{
        PersonVerifyRequest request = new PersonVerifyRequest();
        request.setImage("sfasq35sadvsvqwr5q...");
        request.setImage_type("BASE64");
        request.setId_card_number("110424424114");
        request.setName("张三");
        //            Map<String, Object> map = new HashMap<>();
//            map.put("image", "sfasq35sadvsvqwr5q...");
//            map.put("liveness_control", "HIGH");
//            map.put("name", "张三");
//            map.put("id_card_number", "110...");
//            map.put("image_type", "BASE64");
//            map.put("quality_control", "LOW");
        BaseResult<PersonVerifyResult> result = baiduFaceVerifyClient.personVerify(request);
        System.out.println(result);
    }


    @Test
    public void personVerifyTest1() throws Exception{
        FacePersonVerifyInput request = new FacePersonVerifyInput();
        request.setImage("sfasq35sadvsvqwr5q...");
        request.setImageType(0);
        request.setIdCardNo("110424424114");
        request.setName("张三");
        //            Map<String, Object> map = new HashMap<>();
//            map.put("image", "sfasq35sadvsvqwr5q...");
//            map.put("liveness_control", "HIGH");
//            map.put("name", "张三");
//            map.put("id_card_number", "110...");
//            map.put("image_type", "BASE64");
//            map.put("quality_control", "LOW");
        FacePersonVerifyResult result = memberFaceVerifyService.personVerify(request);
        System.out.println(result);
    }
}
