package com.extracme.evcard.membership.ocr;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.App;
import com.extracme.evcard.membership.core.enums.CommonSideEnums;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2022/8/19
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class OcrAdapterTest {

    @Autowired
    private OcrAdapter ocrAdapter;

    // 由于使用url调用供应商，所以url不再支持本地路径，所以下面的单元测试注释了
//    @Test
//    public void driverLicenseOCRFront() {
//        CommonDrivingLicenseOcrReq req = new CommonDrivingLicenseOcrReq();
//        req.setMid("123");
//        req.setUrl("d:/驾驶证正页.jpeg");
//        req.setCommonSide(CommonSideEnums.FRONT);
//        CommonDrivingLicenseOcrResp resp = ocrAdapter.drivingLicenseOcr(req);
//        System.out.println(JSON.toJSONString(resp));
//    }
//
//    @Test
//    public void driverLicenseOCRBack() {
//        CommonDrivingLicenseOcrReq req = new CommonDrivingLicenseOcrReq();
//        req.setMid("123");
//        req.setUrl("d:/驾驶证副页.jpg");
//        req.setCommonSide(CommonSideEnums.BACK);
//        CommonDrivingLicenseOcrResp resp = ocrAdapter.drivingLicenseOcr(req);
//        System.out.println(JSON.toJSONString(resp));
//    }
//
//    @Test
//    public void driverLicenseOCRFrontCopy() {
//        CommonDrivingLicenseOcrReq req = new CommonDrivingLicenseOcrReq();
//        req.setMid("123");
//        req.setUrl("d:/驾驶证正页_复印件.jpg");
//        req.setCommonSide(CommonSideEnums.FRONT);
//        CommonDrivingLicenseOcrResp resp = ocrAdapter.drivingLicenseOcr(req);
//        System.out.println(JSON.toJSONString(resp));
//    }
//
//    @Test
//    public void driverLicenseOCRBackCopy() {
//        CommonDrivingLicenseOcrReq req = new CommonDrivingLicenseOcrReq();
//        req.setMid("123");
//        req.setUrl("d:/驾驶证副页2_复印件.jpg");
//        req.setCommonSide(CommonSideEnums.BACK);
//        CommonDrivingLicenseOcrResp resp = ocrAdapter.drivingLicenseOcr(req);
//        System.out.println(JSON.toJSONString(resp));
//    }
//
//    @Test
//    public void idCardOCRFront() {
//        CommonIdCardOcrReq req = new CommonIdCardOcrReq();
//        req.setMid("123");
//        req.setUrl("d:/身份证正面.jpg");
//        req.setCommonSide(CommonSideEnums.FRONT);
//        CommonIdCardOcrResp resp = ocrAdapter.idCardOcr(req);
//        System.out.println(JSON.toJSONString(resp));
//    }
//
//    @Test
//    public void idCardOCRBack() {
//        CommonIdCardOcrReq req = new CommonIdCardOcrReq();
//        req.setMid("123");
//        req.setUrl("d:/身份证反面.jpg");
//        req.setCommonSide(CommonSideEnums.BACK);
//        CommonIdCardOcrResp resp = ocrAdapter.idCardOcr(req);
//        System.out.println(JSON.toJSONString(resp));
//    }
//
//    @Test
//    public void idCardOCRFrontCopy() {
//        CommonIdCardOcrReq req = new CommonIdCardOcrReq();
//        req.setMid("123");
//        req.setUrl("d:/身份证正面_复印件.jpg");
//        req.setCommonSide(CommonSideEnums.FRONT);
//        CommonIdCardOcrResp resp = ocrAdapter.idCardOcr(req);
//        System.out.println(JSON.toJSONString(resp));
//    }
//
//    @Test
//    public void idCardOCRBackCopy() {
//        CommonIdCardOcrReq req = new CommonIdCardOcrReq();
//        req.setMid("123");
//        req.setUrl("d:/身份证反面_复印件.jpg");
//        req.setCommonSide(CommonSideEnums.BACK);
//        CommonIdCardOcrResp resp = ocrAdapter.idCardOcr(req);
//        System.out.println(JSON.toJSONString(resp));
//    }

}
