package com.extracme.evcard.membership.core.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.extracme.evcard.membership.App;
import com.extracme.evcard.membership.core.bean.BaseBean;
import com.extracme.evcard.membership.core.bean.LoginBeanResult;
import com.extracme.evcard.membership.core.dto.*;
import com.extracme.evcard.membership.core.dto.input.CRGTMemberInputDto;
import com.extracme.evcard.membership.core.exception.UserMessageException;
import com.extracme.evcard.membership.core.input.*;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs;
import com.extracme.evcard.redis.JedisUtil;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.messagepush.service.IMessagepushServ;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2018/1/31
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class MemberShipServiceTest {



    @Autowired
    private IMemberShipService memberShipService;
    
    @Autowired
    private IMessagepushServ messagepushServ;

    @Autowired
    private ThirdPartMemberServiceImpl thirdPartMemberService;

    @Autowired
    private IMembershipWrapService membershipWrapService;

    @Autowired
    private IMemberOperateServ memberOperateServ;



    @Test
    public void queryUserTagListByAuthIds() {
        String[] t = {"15266668888150012230"};
        List<String> list = Arrays.asList(t);
        List<MmpUserTagDto> mmpUserTagDtos = memberShipService.queryUserTagListByAuthIds(list);
    }

    @Test
    public void pushSMS() {
    	System.out.println("开始发送短信");
    	messagepushServ.asyncSendBehaviorSMS("15502118547", "测试2.3.0短信发送", "武益博");
    	System.out.println("结束发送短信");
    }

    @Test
    public void register() throws Exception {
        RegisterDto registerDto = new RegisterDto("18817350000","123456","m123456", "省", "市","区", "sdsad", 3);
        registerDto.setInvitationCode("bcodgu");
        registerDto.setAppKey("evcardapp");
        System.out.println(memberShipService.register(registerDto, false));
    }

    @Test
    public void keywordScan() throws Exception {
        System.out.println(memberShipService.keywordScan("习近平"));
    }

    @Test
    public void autoSignContract() throws Exception {
        long l = System.currentTimeMillis();
        SignContractDto signContractDto = new SignContractDto();
        signContractDto.setAuthId("******************");
        signContractDto.setContractVersionDto(new ContractVersionDto("SZ0033","0033","会员条款"));
        signContractDto.setContractVersionDto(new ContractVersionDto("YS0018","0018","隐私政策"));
        memberShipService.autoSignContract(signContractDto);
        System.out.println(System.currentTimeMillis() - l);
    }
   @Test
    public void autoSignContract1() throws Exception {
       long l = System.currentTimeMillis();
       SignContractDto signContractDto = new SignContractDto();
       /*String typeOneVersion = JedisUtil.hget("typeVersionMap", "1");
       String typeOneOssUrl = JedisUtil.hget("typeOssUrlMap", "1");
       String typeTwoVersion = JedisUtil.hget("typeVersionMap", "2");
       String typeTwoOssUrl = JedisUtil.hget("typeOssUrlMap", "2");
       SignContractDto signContractDto = new SignContractDto();
       signContractDto.setAuthId(member.getAuthId());*/
       signContractDto.setAuthId("15128912113154049987");
       //String userUrl="https://evcard.oss-cn-shanghai.aliyuncs.com/test/member_provision/SZ0126.pdf";
       signContractDto.setContractVersionDto(
               new ContractVersionDto("SZ" + "0128", "0128", "会员条款","" ));
       //String ysUrl="https://evcard.oss-cn-shanghai.aliyuncs.com/test/member_provision/YS0079.pdf";
       signContractDto.setContractVersionDto(
               new ContractVersionDto("YS" + "0082", "0082", "隐私政策",""));
       memberShipService.autoSignContract(signContractDto);
       System.out.println(System.currentTimeMillis() - l);

    }

    @Test
    public void handleUserInfoTest() throws Exception{
        Map<String,Object> userProperties = new HashMap<>();
        userProperties.put("mobilePhone","13477083078");
        userProperties.put("recentlyVisitTime",new Date());
        memberShipService.handleUserInfo(userProperties,"0987123455");
        System.out.println(JSON.toJSONString(userProperties));
    }

    @Test
    public void userInfoTest() throws Exception{
        UserInfoDto userInfoDto = memberShipService.getUserInfoByToken("123321");
        System.out.println(JSON.toJSONString(userInfoDto));
    }

    @Test
    public void uploadServiceVerTest() throws Exception{
        UploadServiceVerInput uploadServiceVerInput = new UploadServiceVerInput();
        uploadServiceVerInput.setAuthId("123123213213");
        uploadServiceVerInput.setMemberType(0);
        uploadServiceVerInput.setToken("123");
        uploadServiceVerInput.setUpdateUser("test");
        memberShipService.uploadServiceVer(uploadServiceVerInput);
    }

    @Test
    public void ugetSMSVerifyCodeTest() throws Exception{
        SMSVerifyBeanInput smsVerifyBeanInput = new SMSVerifyBeanInput();
       smsVerifyBeanInput.setMobilePhone("13391422240");
       smsVerifyBeanInput.setType(3);
       smsVerifyBeanInput.setVerifyCodeType(0);
        BaseBean baseBean = memberShipService.getSMSVerifyCode(smsVerifyBeanInput);
        System.out.println(JSON.toJSONString(baseBean));
    }


    @Test
    public void getAgencyMinsByidTest() throws Exception{
        AgencyMinsDto userInfoDto = memberShipService.getAgencyMinsByid("0001");
        System.out.println(JSON.toJSONString(userInfoDto));
    }

    @Test
    public void queryInsideFlagTest() throws Exception{
        int userInfoDto = memberShipService.queryInsideFlag("0001");
        System.out.println(JSON.toJSONString(userInfoDto));
    }
    @Test
    public void getOrgCardStatusByIdTest() throws Exception{
        int userInfoDto = memberShipService.getOrgCardStatusById("00000B","3341450A");
        System.out.println(JSON.toJSONString(userInfoDto));
    }
    
    @Test
    public void getUserBasicInfo() {
    	MembershipBasicInfo base = memberShipService.getUserBasicInfo("******************", (short)0);
    	System.out.println(JSON.toJSON(base));
    	System.out.println("123456789");
    }

    @Test
    public void getUserInfoByTokenTest() throws Exception{
        UserInfoDto userInfoDto = memberShipService.getUserInfoByToken("15700083759133014583");
        System.out.println(JSON.toJSON(userInfoDto));
    }

    @Test
    public void checkUserCardTest() throws Exception{
        memberShipService.checkUserCard("17621785755163311478",0);
    }
    @Test
    public void getImageStrFromUrl(){
        try {
            thirdPartMemberService.getImageStrFromUrl("https://carpooling-**********.cos.ap-chengdu.myqcloud.com/1871ab10-0e4c-4852-bd02-1c94f4f2ba97.jpg",1,"123");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testCRGT(){
        String input = "{\"appKey\":\"gtjx\",\"authId\":\"15287654321141735917\",\"facePic\":\"https://carpooling-**********.cos.ap-chengdu.myqcloud.com/1871ab10-0e4c-4852-bd02-1c94f4f2ba97.jpg\",\"faceVideo\":\"\",\"licenseEndTime\":\"2018-12-19\",\"licenseId\":\"\",\"licenseLevel\":\"C2\",\"licenseNo\":\"******************\",\"licensePicFirst\":\"https://carpooling-**********.cos.ap-chengdu.myqcloud.com/e3581072-100c-415c-be69-b6c6343d7cd7.jpg\",\"licensePicSecond\":\"https://carpooling-**********.cos.ap-chengdu.myqcloud.com/6b8fb95f-a5c8-44bb-9efd-9bc1fedc9c9f.jpg\",\"licenseStartTime\":\"2012-12-19\",\"name\":\"鍒樻柦鍢?\",\"phoneNumber\":\"15287654321\"}\n";
        CRGTMemberInputDto crgtMemberInputDto = JSON.parseObject(input,CRGTMemberInputDto.class);
        thirdPartMemberService.insertCRGTmMember(crgtMemberInputDto);
    }
@Resource
MembershipInfoMapper membershipInfoMapper;
    @Test
    public void carLifeLogin(){
        try {
            String in = "{\"appKey\":\"ALI_PAY_CAR_LIFE\",\"cityId\":\"510100\",\"idCardNo\":\"513022199003106720\",\"mobilePhone\":\"***********\",\"openId\":\"2088802717780950\",\"userName\":\"信息\"}";
            CarLifeLoginInput input = JSON.parseObject(in,CarLifeLoginInput.class);
            thirdPartMemberService.carLifeLogin(input);
        } catch (BusinessException e) {
            e.printStackTrace();
        }
       /* MembershipInfoWithBLOBs member = new MembershipInfoWithBLOBs();
        int insertSelective = membershipInfoMapper.insertSelective(member);*/
    }

    @Test
    public void qingLuLogin() {
        try {
            //String in = "{\"appKey\":\"second_ql_halou\",\"cityId\":\"00\",\"idCardNo\":\"12342134\",\"mobilePhone\":\"***********\"}";
            String in = "{\"appKey\":\"second_ql_xiecheng\",\"cityId\":\"310100\",\"idCardNo\":\"310113198402235547\",\"mobilePhone\":\"***********\"}";
            ThirdLoginInput input = JSON.parseObject(in, ThirdLoginInput.class);
            thirdPartMemberService.qingLuLogin(input);
        } catch (BusinessException e) {
            e.printStackTrace();
        }
       /* MembershipInfoWithBLOBs member = new MembershipInfoWithBLOBs();
        int insertSelective = membershipInfoMapper.insertSelective(member);*/
    }

    @Test
    public void carLifeLogin1() {
        try {
            //String in = "{\"appKey\":\"second_ql_halou\",\"cityId\":\"00\",\"idCardNo\":\"12342134\",\"mobilePhone\":\"***********\"}";
            String in = "{\"appKey\":\"second_ALI_PAY_CAR_LIFE\",\"cityId\":\"310100\",\"idCardNo\":\"310113198402235547\",\"mobilePhone\":\"***********\"}";
            ThirdLoginInput input = JSON.parseObject(in, ThirdLoginInput.class);
            thirdPartMemberService.qingLuLogin(input);
        } catch (BusinessException e) {
            e.printStackTrace();
        }
       /* MembershipInfoWithBLOBs member = new MembershipInfoWithBLOBs();
        int insertSelective = membershipInfoMapper.insertSelective(member);*/
    }





    @Test
    public void carLifeLoginCRGT(){
    /*    String input = "{\"appKey\":\"ALI_PAY_CAR_LIFE\",\"cityId\":\"1\",\"idCardNo\":\"142328199208120519\",\"mobilePhone\":\"***********\",\"userName\":\"姬亮\"}";
        CarLifeLoginInput inputDto = JSON.parseObject(input,CarLifeLoginInput.class);
        try {
            ThirdLoginDto thirdLoginDto = thirdPartMemberService.carLifeLogin(inputDto);
            System.out.println(JSON.toJSONString(thirdLoginDto));
        } catch (BusinessException e) {
            e.printStackTrace();
            System.out.println(e);
        }*/
    }

    @Test
    public void getAccountStatus() throws Exception{
        AccountStatusDto account = memberShipService.getAccountStatusByMobile("***********", 0);
        System.out.print(account.getAccountStatus());
    }

    @Test
    public void registerOk() throws Exception{
        UnregisterDto unregisterDto = new UnregisterDto();
        unregisterDto.setAuthId("24000000025142346932");
        unregisterDto.setMembershipType((short)0);
        memberShipService.unregister(unregisterDto);
    }

    @Test
    public void registerDepo() throws Exception{
        UnregisterDto unregisterDto = new UnregisterDto();
        unregisterDto.setAuthId("25000000239143306283");
        unregisterDto.setMembershipType((short)0);
        memberShipService.unregister(unregisterDto);
    }

    @Test
    public void registerDepo2() throws Exception{
        UnregisterDto unregisterDto = new UnregisterDto();
        unregisterDto.setAuthId("13641831753163355200");
        unregisterDto.setMembershipType((short)0);
        memberShipService.unregister(unregisterDto);
    }

    @Test
    public void registerEx1() throws Exception{
        UnregisterDto unregisterDto = new UnregisterDto();
        unregisterDto.setAuthId("410223199211283550");
        unregisterDto.setMembershipType((short)0);
        memberShipService.unregister(unregisterDto);
    }

    @Test
    public void registerEx3() throws Exception{
        UnregisterDto unregisterDto = new UnregisterDto();
        unregisterDto.setAuthId("17600009997152107414");
        unregisterDto.setMembershipType((short)0);
        memberShipService.unregister(unregisterDto);
    }

    @Test
    public void registerEx22222() throws Exception{
        UnregisterDto unregisterDto = new UnregisterDto();
        unregisterDto.setMobile("***********");
        unregisterDto.setMembershipType((short)0);
        memberShipService.unregisterRecover(unregisterDto);
    }

    @Test
    public void registerEx223222() throws Exception{
        AccountRecoverDto accountRecoverDto = new AccountRecoverDto();
        accountRecoverDto.setAuthId("***********092145648");
        accountRecoverDto.setMembershipType((short)0);
        accountRecoverDto.setReason("xxxxxxx");
        memberShipService.unregisterRecoverByAuthId(accountRecoverDto);
    }

    @Test
    public void registerEx3illegals() throws Exception{
        UnregisterDto unregisterDto = new UnregisterDto();
        unregisterDto.setAuthId("13422221111145609939");
        unregisterDto.setMembershipType((short)0);
        memberShipService.unregister(unregisterDto);
    }

    @Test
    public void registerEx4illegals() throws Exception{
        UnregisterDto unregisterDto = new UnregisterDto();
        unregisterDto.setAuthId("123456889077897867");
        unregisterDto.setMembershipType((short)0);
        memberShipService.unregister(unregisterDto);
    }

    @Test
    public void getAuthOrigin() throws Exception{
        UnregisterDto unregisterDto = new UnregisterDto();
        unregisterDto.setAuthId("123456889077897867");
        unregisterDto.setMembershipType((short)0);
        memberShipService.getMemberAuthOrigin("17200005220092839676", 0, 1L);
    }




    @Test
    public void saveUserOperationLog() throws Exception {

        UserOperationLogInput userOperationLogInput = new UserOperationLogInput();
        userOperationLogInput.setAuthId("******************");
        userOperationLogInput.setRefKey1("iosapp");
        userOperationLogInput.setOperationTime(new Date());
        userOperationLogInput.setOperator("sdsadsadas");
        memberShipService.saveUserOperationLog(userOperationLogInput);
    }

    @Test
    public void modifyMobilePhone() throws Exception {
        UpdateUserDto dto = new UpdateUserDto();
        memberShipService.modifyMobilePhone("13053149856124116457","18656444171","123456","13053149856",dto);
    }


    @Test
    public void queryExpressInfo() throws Exception {
        List<ExpressInfoDTO> expressInfoDTOList = memberShipService.queryExpressInfo("310115198602071914");
    }

    @Test
    public void queryMemberClauseRecord() throws Exception {
        List<MemberClauseRecord> memberClauseRecords = memberShipService.queryMemberClauseRecord(new QueryMemberClauseRecordInput("******************",1,10));
        System.out.println(memberClauseRecords);

    }

    @Test
    public void queryMemberOperateLog() throws Exception {

        memberShipService.queryMemberOperateLog(new QueryMemberOperateLogInput("430903199512301560",1,10));

    }

    @Test
    public void querySimpleMembershipInfoList() throws Exception {

        QuerySimpleMemberInput querySimpleMemberInput = new QuerySimpleMemberInput();
       /* querySimpleMemberInput.setAuthId("******************");*/
        querySimpleMemberInput.setMobilePhone("16639708523");
        querySimpleMemberInput.setPlatFormId(0);
        List<SimpleMembershipInfoDTO> simpleMembershipInfoDTOS = memberShipService.querySimpleMembershipInfoList(querySimpleMemberInput);
        System.out.println(simpleMembershipInfoDTOS);
    }

    @Test
    public void queryDriverLicenseDetail() throws Exception {
        DriverLicenseQueryResultDTO resultDTO = memberShipService.queryDriverLicenseDetail("430903199512301560", null);
        System.out.println(JSON.toJSONString(resultDTO));
    }

    @Test
    public void driverLicenseElementsAuthenticate() throws Exception {
        memberShipService.driverLicenseElementsAuthenticate("430903199512301560");
    }

    @Test
    public void driverLicenseElementsQuery() throws Exception {
        List<DriverLicenseElementsAuthenticateLogDTO> result = memberShipService.getLastAuthenticateLogsByUser("17200009508103616846", 0, 10);
    }

    @Test
    public void submitDrivingLicenseInfo() throws Exception {
        SubmitDrivingLicenseInput parse = JSON.parseObject("{\"driverCode\":\"bjbsjnsjskk\",\"driverLicenseInputType\":2,\"drivingLicenseImgUrl\":\"sensitiveBucket/driving_license/17200009999153306638/1567045385351.jpg\",\"drivingLicenseType\":\"C1\",\"expirationDate\":\"2023-08-29\",\"fileNo\":\"123456789000\",\"fileNoImgUrl\":\"sensitiveBucket/driving_license/17200009999153306638/1567045453646.jpg\",\"firstGetLicenseDate\":\"2019-08-29\",\"name\":\"阿里\",\"national\":\"外国\",\"submitType\":0}", SubmitDrivingLicenseInput.class);
        memberShipService.submitDrivingLicenseInfo("17200009999153306638", "ddd", parse);
    }

    @Test
    public void queryOrgCardList() throws Exception {
        List<QueryOrgCardInfoDto> list = memberShipService.queryOrgCardList("000N", -1);
        System.out.println(JSON.toJSONString(list));
    }

//    @Autowired
//    private YiguanMemberTrackJob yiguanMemberTrackJob;
//
//
//    @Test
//    public void testYiguanJob(){
//        yiguanMemberTrackJob.execute(null);
//    }

    @Test
    public void checkLoginChange() throws Exception {
        LoginInput loginInput = new LoginInput();
        String s = "{\"appType\":0,\"channelId\":\"\",\"checkType\":2,\"compression\":\"\",\"encryption\":\"\",\"imei\":\"C2A085EF-CB63-49D9-AB40-0EA1F5A2F077\"," +
                "\"loginName\":\"15000524437\",\"loginOrigin\":0,\"password\":\"m123456\",\"supportNoKey\":false,\"verifyCode\":\"\"," +
                "\"version\":\"3.6.0\",\"checkType\":2}";
        loginInput = JSONObject.parseObject(s, LoginInput.class);
        LoginBeanResult loginBeanResult = new LoginBeanResult();
        try {
            loginBeanResult = memberOperateServ.checkLoginChange(loginInput);
        } catch (UserMessageException e) {
            System.out.println(e.getMessage());
        }
        System.out.println(JSON.toJSONString(loginBeanResult));
    }
/*
    @Autowired
    private YiguanMemberTrackJob yiguanMemberTrackJob;


    @Test
    public void testYiguanJob(){
        yiguanMemberTrackJob.execute(null);
    }*/

    @Test
    public void testQueryUserOperatorLog(){
        QueryMemberOperateLogInput queryMemberOperateLogInput = new QueryMemberOperateLogInput();
        queryMemberOperateLogInput.setAuthId("******************");
        queryMemberOperateLogInput.setForeignKey2("0");
        List<UserOperatorLogDTO> userOperatorLogDTOS = memberShipService.queryUserOperatorLog(queryMemberOperateLogInput);
        System.out.println(userOperatorLogDTOS);
    }
    @Test
    public void testqueryMemberRuleVersion(){
        MemberRuleVersionDTO memberRuleVersionDTO = memberShipService.queryMemberRuleVersion();
        System.out.println(memberRuleVersionDTO);
    }

    @Test
    public void testQueryLicAuthLog(){
        List<LicenseAuthLogDTO> licAuthLogs = membershipWrapService.getUserLastLicenseAuthLogs("430903199512301560", 0, 20);
        System.out.println(licAuthLogs);
    }

    @Test
    public void isExistMemberClauseLog() {
        Boolean existMemberClauseLog = memberShipService.isExistMemberClauseLog("******************");
        System.out.println(existMemberClauseLog);
    }

    @Test
    public void queryHealthCode() {
        HealthCodeDto existMemberClauseLog = memberShipService.queryHealthCode("15700083760092531064",2);
        System.out.println(existMemberClauseLog);
    }
    @Test
    public void getUserInfoByToken() {
        UserInfoDto existMemberClauseLog = null;
        try {
            existMemberClauseLog = memberShipService.getUserInfoByToken("17200009501095225101","");
        } catch (UserMessageException e) {
            e.printStackTrace();
        }
        System.out.println(existMemberClauseLog);
    }

    @Test
    public void ggg() {
        memberShipService.getMemberByUid("18851236987151402224");
        memberShipService.updateMemberUidByPkId(3111336L, "fafafafa");
    }

    @Test
    public void getMemberOrgInfo() {
        memberShipService.getMemberOrgInfo("18851236987151402224", 0);
    }

    @Test
    public void addMembershipInfoForMmp() {
        String jsonString = "{\n" +
                "\t\"address\": \"\",\n" +
                "\t\"areaOfOrigin\": \"东城区\",\n" +
                "\t\"authId\": \"15669059325171309029\",\n" +
                "\t\"cityOfOrigin\": \"北京\",\n" +
                "\t\"createOperId\": \"3654\",\n" +
                "\t\"createOperName\": \"超级管理员\",\n" +
                "\t\"drivingLicense\": \"12345633333\",\n" +
                "\t\"drivingLicenseImgUrl\": \"sensitiveBucket/driving_license/15669059325171309029/1662369189161.jpg\",\n" +
                "\t\"drivingLicenseType\": \"C1\",\n" +
                "\t\"faceRecognitionImgUrl\": \"sensitiveBucket/holdIcCardPic/15669059325171309029/1662369189643.jpg\",\n" +
                "\t\"fileNo\": \"15669059325\",\n" +
                "\t\"fileNoImgUrl\": \"sensitiveBucket/driving_license/15669059325171309029/1662369189304.jpg\",\n" +
                "\t\"holdIdcardPicUrl\": \"sensitiveBucket/driving_license/15669059325171309029/1662369189548.jpg\",\n" +
                "\t\"idCardExpirationTime\": \"2023-09-14\",\n" +
                "\t\"idCardExpirationType\": 1,\n" +
                "\t\"idCardcBackPicUrl\": \"sensitiveBucket/driving_license/15669059325171309029/1662369189480.jpg\",\n" +
                "\t\"idType\": \"1\",\n" +
                "\t\"idcardPicUrl\": \"sensitiveBucket/driving_license/15669059325171309029/1662369189389.jpg\",\n" +
                "\t\"infoOrigin\": \"\",\n" +
                "\t\"isFlag\": 1,\n" +
                "\t\"licenseExpirationTime\": \"2023-09-14\",\n" +
                "\t\"mail\": \"\",\n" +
                "\t\"mobilePhone\": \"15669059325\",\n" +
                "\t\"name\": \"xl\",\n" +
                "\t\"obtainDriverTimer\": \"2021-09-07\",\n" +
                "\t\"passportNo\": \"15669059325\",\n" +
                "\t\"provinceOfOrigin\": \"北京\",\n" +
                "\t\"userType\": \"\"\n" +
                "}";
        MembershipForMmpDTO mdVehicleOrder = JSON.parseObject(jsonString, MembershipForMmpDTO.class);
        String s = memberShipService.addMembershipInfoForMmp(mdVehicleOrder);
    }

}