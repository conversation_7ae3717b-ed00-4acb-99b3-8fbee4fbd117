package com.extracme.evcard.membership.core.service;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.extracme.evcard.membership.core.bean.LoginBeanResult;
import com.extracme.evcard.membership.core.dto.DriverLicenseValidResultDto;
import com.extracme.evcard.membership.core.dto.RegisterDto;
import com.extracme.evcard.membership.core.dto.UpdateUserDto;
import com.extracme.evcard.membership.core.exception.UserMessageException;
import com.extracme.evcard.membership.core.input.LoginInput;
import com.extracme.evcard.membership.core.input.SaveDriverElementsAuthenticateLogInput;
import com.extracme.evcard.membership.core.service.license.JuHeAuthService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import com.extracme.evcard.membership.App;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.UUID;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class MembershipOperateTest {


    @Autowired
    private IMemberOperateServ memberOperateServ;
    @Autowired
    private JuHeAuthService juHeAuthService;


    @Test
    public void check() throws Exception {
        LoginInput loginInput = new LoginInput();
        loginInput.setAppType(1);
        loginInput.setChannelId("4445559n898h989");
        loginInput.setCheckType(0);
        loginInput.setImei("54013-98BA-D9E34471743D");
        loginInput.setCompression("");
        loginInput.setEncryption("");
        loginInput.setLoginOrigin(0);
        loginInput.setLoginName("13917351715");
        loginInput.setPassword("2c13b94a259480b3df9df4ff02a67d13");
        loginInput.setSupportNoKey(false);
        loginInput.setVerifyCode("123456");
        loginInput.setVersion("2.20.0");
        LoginBeanResult loginBeanResult = new LoginBeanResult();
        try {
            loginBeanResult = memberOperateServ.checkLogin(loginInput);
        } catch (UserMessageException e){
            System.out.println(e.getMessage());
        }
        System.out.println(JSON.toJSONString(loginBeanResult));
    }


    @Test
    public void checkLoginChange() throws Exception {
        LoginInput loginInput = new LoginInput();
        String s = "{\"appType\":0,\"channelId\":\"\",\"checkType\":2,\"compression\":\"\",\"encryption\":\"\",\"imei\":\"C2A085EF-CB63-49D9-AB40-0EA1F5A2F077\"," +
                "\"loginName\":\"15000524437\",\"loginOrigin\":0,\"password\":\"m123456\",\"supportNoKey\":false,\"verifyCode\":\"\"," +
                "\"version\":\"3.6.0\",\"checkType\":2}";
        LoginBeanResult loginBeanResult = new LoginBeanResult();
        try {
            loginBeanResult = memberOperateServ.checkLoginChange(loginInput);
        } catch (UserMessageException e){
            System.out.println(e.getMessage());
        }
        System.out.println(JSON.toJSONString(loginBeanResult));
    }

    @Test
    public void checkVerifyCodeLoginChange() throws Exception {
        LoginInput loginInput = new LoginInput();
        String s = "{\"appType\":1,\"bindImei\":1,\"channelId\":\"\",\"checkType\":2,\"compression\":\"\",\"encryption\":\"\",\"imei\":\"C2A085EF-CB63-49D9-AB40-0EA1F5A2F077\",\"loginName\":\"15000524437\",\"loginOrigin\":0,\"password\":\"\"," +
                "\"supportNoKey\":false,\"verifyCode\":\"123456\",\"version\":\"3.6.0\"}";
        LoginBeanResult loginBeanResult = new LoginBeanResult();
        loginInput = JSONObject.parseObject(s,LoginInput.class);
        try {
            loginBeanResult = memberOperateServ.checkVerifyCodeLoginChange(loginInput);
        } catch (UserMessageException e){
            System.out.println(e.getMessage());
        }
        System.out.println(JSON.toJSONString(loginBeanResult));
    }

    @Test
    public void changeUserImage() throws Exception {
        UpdateUserDto updateUser = new UpdateUserDto();
        updateUser.setUserId(430903199512301560L);
        updateUser.setUserName("chennian");
        memberOperateServ.changeUserImage("430903199512301560", "000T", "GGGGG", updateUser);
    }
    @Test
    public void checkDriverLicenseValid() throws Exception {
        DriverLicenseValidResultDto dto = juHeAuthService.checkDriverLicenseValid("王骁", "320923198911155455", "320901200043");
        System.out.println(JSON.toJSONString(dto));
    }
    @Test
    public void queryDriverDeduction() throws Exception {
        SaveDriverElementsAuthenticateLogInput dto = juHeAuthService.queryDriverDeduction("320923198911155455","王骁",  "320901200043");
        System.out.println(JSON.toJSONString(dto));
    }
}
