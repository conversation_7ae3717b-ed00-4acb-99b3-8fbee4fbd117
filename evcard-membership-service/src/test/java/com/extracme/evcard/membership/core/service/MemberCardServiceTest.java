package com.extracme.evcard.membership.core.service;

import com.extracme.evcard.membership.core.dao.CardPauseLogMapper;
import com.extracme.evcard.membership.core.dto.*;
import com.extracme.evcard.membership.core.input.CardInfoHistoryInputDTO;
import com.extracme.evcard.membership.core.input.QueryCardInfoConditionInput;
import com.extracme.evcard.membership.core.input.QueryCardInfoHistoryListIConditionInput;
import com.extracme.evcard.membership.core.model.CardInfoHistroy;
import com.extracme.evcard.membership.core.model.CardPauseLog;
import com.extracme.evcard.rpc.dto.PageBeanDto;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.extracme.evcard.membership.App;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.List;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class MemberCardServiceTest {


    @Autowired
    private IMemberCardService memberCardService;

    @Autowired
    private IBaseInfoService baseInfoService;

    private void pause(String cardNo, String authId, Integer pauseType, String recoverTime, String remark) throws Exception{
        CardOperateDto cardOperateDto = new CardOperateDto();
        cardOperateDto.setCardNo(cardNo); //必填
        cardOperateDto.setAuthId(authId); //必填
        cardOperateDto.setRemark(remark); //必填
        cardOperateDto.setPauseTimeType(pauseType); //0-7天、1-30天、2-永久.
        cardOperateDto.setRecoverTime(recoverTime); //pauseTimeType未设置时必须提供

        UpdateUserDto updateUserDto = new UpdateUserDto();
        updateUserDto.setUpdateTime(new Date());
        updateUserDto.setUserId(Long.valueOf(1));
        updateUserDto.setUserName("fafafa");
        memberCardService.cardPause(cardOperateDto, updateUserDto);
    }

    private void recover(String cardNo, String authId, String remark) throws Exception{
        CardOperateDto cardOperateDto = new CardOperateDto();
        cardOperateDto.setCardNo(cardNo);
        cardOperateDto.setAuthId(authId);
        cardOperateDto.setRemark(remark);

        UpdateUserDto updateUserDto = new UpdateUserDto();
        updateUserDto.setUpdateTime(new Date());
        updateUserDto.setUserId(Long.valueOf(1));
        updateUserDto.setUserName("fafafa");
        memberCardService.cardRecover(cardOperateDto, updateUserDto);
    }

    @Test
    public void testCardPause0() throws Exception {
        pause("42113593", "17625703913141102016",
                0 , null, "暂停7天");
    }

    @Test
    public void testCardPause00() throws Exception {
        pause("42113593", "17625703913141102016", null,"", "暂停7天");
    }

    @Test
    public void testCardPause01() throws Exception {
        pause("42113593", "17625703913141102016", null,"aaa", "暂停7天");
    }

    @Test
    public void testCardPause1() throws Exception {
        pause("42113593", "17625703913141102016",
                1 , null, "暂停30天");
    }

    @Test
    public void testCardPause2() throws Exception {
        pause("42113593", "17625703913141102016",
                2 , null, "永久暂停");
    }

    @Test
    public void testCardPauseOK() throws Exception {
        pause("42113593", "17625703913141102016",
                null , "2019-10-11", "暂停到指定时间");
    }

    @Test
    public void testCardRecoverOk() throws Exception {
        recover("42113593", "17625703913141102016","恢复会员卡");
    }

    @Test
    public void testCardRecoverErr() throws Exception{
        recover("42113593", "17625703913141102016","恢复已注销卡");
    }

    @Test
    public void queryCardInfo() throws Exception {
        CardInfoDTO cardInfoDTO = memberCardService.queryCardInfo("001F2AAB");
    }

    @Test
    public void queryCardInfoList() throws Exception {
      /*  List<CardInfoHistoryDTO> cardInfoDTOList = memberCardService.queryCardInfoHistoryList("310105198005092415");
        System.out.println(cardInfoDTOList);*/
        List<CardInfoHistoryDTO> cardInfoHistoryDTOList = memberCardService.queryCardInfoHistoryList("330382198601160063");
        System.out.println(cardInfoHistoryDTOList);
    }

    @Test
    public void queryAppKeyManagerByAppKey() {

        AppKeyManagerDTO appKeyManagerDTO = baseInfoService.queryAppKeyManagerByAppKey("evcardapp");
        System.out.println(appKeyManagerDTO);
    }

    @Test
    public void updateCardInfoHistory() {
        CardInfoHistoryInputDTO cardInfoHistoryDTO = new CardInfoHistoryInputDTO();
        cardInfoHistoryDTO.setAuthId("422323232");
        cardInfoHistoryDTO.setCardNo("AAAABBBB");
        cardInfoHistoryDTO.setRemark("test");
        boolean b = memberCardService.updateCardInfoHistory(cardInfoHistoryDTO);
        System.out.println(b);
    }

    @Test
    public void insertCardInfoHistory() {
        CardInfoHistoryInputDTO cardInfoHistoryDTO = new CardInfoHistoryInputDTO();
        cardInfoHistoryDTO.setAuthId("422323232");
        cardInfoHistoryDTO.setCardNo("AAAABBBB");
        cardInfoHistoryDTO.setRemark("test");
        boolean b = memberCardService.insertCardInfoHistory(cardInfoHistoryDTO);
        System.out.println(b);
    }

    @Test
    public void queryCardInfoByCondition() {
        QueryCardInfoConditionInput queryCardInfoConditionInput = new QueryCardInfoConditionInput();
        queryCardInfoConditionInput.setCardNo("AAAABBBB");
        queryCardInfoConditionInput.setInternalNo("11111");
        List<CardInfoDTO> cardInfoDTOList = memberCardService.queryCardInfoByCondition(queryCardInfoConditionInput);
        System.out.println(cardInfoDTOList);
    }

    @Test
    public void updateCardInfo() {
        CardInfoDTO cardInfoDTO = memberCardService.queryCardInfo("AAAABBBB");
        cardInfoDTO.setInternalNo("11111");
        boolean b = memberCardService.updateCardInfo(cardInfoDTO);
        System.out.println(b);
    }

    @Test
    public void insertCardInfo() {
        CardInfoDTO cardInfoDTO = new CardInfoDTO();
        cardInfoDTO.setCardNo("AAAABBBB");
        cardInfoDTO.setInternalNo("2131231231231");
        cardInfoDTO.setActivateStatus(1);
        cardInfoDTO.setCardType(1);
        cardInfoDTO.setAuthId("422323232");
        boolean b = memberCardService.insertCardInfo(cardInfoDTO);
        System.out.println(b);
    }

    @Autowired
    private CardPauseLogMapper cardPauseLogMapper;

    @Test
    public void updateCardPauseLog() {
        CardPauseLog cardPauseLog = cardPauseLogMapper.selectByPrimaryKey(170L);
        CardPauseLogDTO cardPauseLogDTO = new CardPauseLogDTO();
        BeanUtils.copyProperties(cardPauseLog,cardPauseLogDTO);
        cardPauseLogDTO.setName("AAAA");
        memberCardService.updateCardPauseLog(cardPauseLogDTO);
    }

    @Test
    public void queryCardInfoHistoryListByCondition() {
        QueryCardInfoHistoryListIConditionInput conditionInput = new QueryCardInfoHistoryListIConditionInput();
        //conditionInput.setAuthId("******************");
        conditionInput.setMemberType(1);
        conditionInput.addOrderCondition(QueryCardInfoHistoryListIConditionInput.OrderColumnNameEnum.CREATE_TIME,false);
        conditionInput.addOrderCondition(QueryCardInfoHistoryListIConditionInput.OrderColumnNameEnum.CARD_STATUS,true);
        PageBeanDto<CardInfoHistoryDTO> cardInfoHistoryDTOS = memberCardService.queryCardInfoHistoryListByCondition(conditionInput);
        System.out.println(cardInfoHistoryDTOS);

        List<CardInfoHistoryDTO> cardInfoHistoryDTOS1 = memberCardService.queryCardInfoHistoryList("******************");
        System.out.println(cardInfoHistoryDTOS1);

    }
}