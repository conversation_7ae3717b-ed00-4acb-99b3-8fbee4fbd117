package com.extracme.evcard.membership.service;

import com.extracme.evcard.membership.App;
import com.extracme.evcard.membership.core.dto.CreateWxQrCodeResp;
import com.extracme.evcard.membership.core.dto.OperatorDto;
import com.extracme.evcard.membership.core.dto.input.CreateWxQrCodeDto;
import com.extracme.evcard.membership.core.dto.input.CreateWxQrCodeInput;
import com.extracme.evcard.membership.core.dto.input.WxQrCodeDto;
import com.extracme.evcard.membership.core.service.IShortlinkManagementService;
import com.extracme.evcard.rpc.exception.BusinessException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class WechatTest {

    @Autowired
    private IShortlinkManagementService mmpShortlinkManagementService;


    @Test
    public void test() {
        OperatorDto operatorDto = new OperatorDto();
        operatorDto.setOperatorName("xialei");
        operatorDto.setOperatorId(1111L);
        mmpShortlinkManagementService.createWxQrCode("cdktest", "type=1&id=1234&source=1", "pages/storeOrder/index", 1, "", operatorDto);
    }

    @Test
    public void batchTest() throws BusinessException {
        long startTime = System.nanoTime();

        CreateWxQrCodeInput createWxQrCodeInput = new CreateWxQrCodeInput();

        OperatorDto operatorDto = new OperatorDto();
        operatorDto.setOperatorName("xialei");
        operatorDto.setOperatorId(1111L);

        CreateWxQrCodeDto dto1 = new CreateWxQrCodeDto();
        dto1.setOriginalUrl("type=1&id=1234&source=1");
        dto1.setShortLinkName("cdktest1");
        dto1.setWeChatPage("pages/storeOrder/index");

        CreateWxQrCodeDto dto2 = new CreateWxQrCodeDto();
        dto2.setOriginalUrl("type=1&id=5678&source=1");
        dto2.setShortLinkName("cdktest2");
        dto2.setWeChatPage("pages/order/vipActive/scanToRedeem");

        HashMap<String, List<CreateWxQrCodeDto>> map = new HashMap<>();
        map.put("xialei1", Collections.singletonList(dto1));
        map.put("xialei2", Collections.singletonList(dto2));

        createWxQrCodeInput.setOperatorDto(operatorDto);
        createWxQrCodeInput.setZipName("xialei_20241115");
        createWxQrCodeInput.setCreateWxQrCodeDtoMap(map);
        List<CreateWxQrCodeDto> createWxQrCodeDtos = new ArrayList<>();
        createWxQrCodeDtos.add(dto1);
        createWxQrCodeDtos.add(dto2);
        Map<String, WxQrCodeDto> stringWxQrCodeDtoMap = mmpShortlinkManagementService.batchCreateWxQrCode(createWxQrCodeDtos, "", operatorDto);
        System.out.println(stringWxQrCodeDtoMap);

        long endTime = System.nanoTime();
        long duration = endTime - startTime;
        System.out.println("文件读取时间: " + duration + " 纳秒");
        System.out.println("文件读取时间: " + (duration / 1_000_000.0) + " 毫秒");
    }
}
