package com.extracme.evcard.membership.core.service;

import com.extracme.evcard.membership.core.dto.UpdateUserDto;
import com.extracme.evcard.membership.core.input.SubmitDrivingLicenseInput;
import com.extracme.evcard.membership.core.input.UpdateFileNoInput;
import com.extracme.evcard.membership.job.MemberLicenseAuthJob;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import com.extracme.evcard.membership.App;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/4/29
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class MemberReviewServiceTest {
    @Autowired
    private IMemberShipService memberShipService;
    @Autowired
    private IMemberReviewService memberReviewService;
    @Autowired
    private IMembershipWrapService membershipWrapService;
    @Autowired
    private MemberLicenseAuthJob memberLicenseAuthJob;

    @Test
    public void testDriverLicenseElementsAuthenticate() throws Exception {
        memberReviewService.drivingLicenseAuthenticate("15021372988221227605");
    }

    @Test
    public void mannalLicenseElementsAuthenticateSuccess() throws Exception {
        memberReviewService.manualLicenseElementsAuthenticateSuccess("******************", UpdateUserDto.SYSTEM);
    }

    @Test
    public void getUserLastLicenseAuthLogs() throws Exception {
        membershipWrapService.getUserLastLicenseAuthLogs("******************", 0, 20);
    }




    @Test
    public void testSubmitDrivingLicenseInfo() throws Exception {
        //提交不正确的三要素信息
        SubmitDrivingLicenseInput input = new SubmitDrivingLicenseInput();
        input.setDriverCode("******************");
        input.setName("谌年年");
        input.setNational("中国");
        input.setDrivingLicenseImgUrl("sensitiveBucket/driving_license/17200009999153306638/1567045385351.jpg");
        input.setDrivingLicenseType("C1");
        input.setExpirationDate("2023-08-29");
        input.setFirstGetLicenseDate("2019-08-29");
        input.setFileNo("310069284580");
        input.setFileNoImgUrl("sensitiveBucket/driving_license/17200009999153306638/1567045453646.jpg");
        input.setDriverLicenseInputType(1);
        input.setSubmitType(0);
        memberReviewService.submitDrivingLicenseInfo("******************", "ddd", input);
    }

    @Test
    public void testSubmitDrivingLicenseInfo2() throws Exception {
        //手动修改为正确信息
        SubmitDrivingLicenseInput input = new SubmitDrivingLicenseInput();
        input.setDriverCode("******************");
        input.setName("谌年");
        input.setNational("中国");
        input.setDrivingLicenseImgUrl("sensitiveBucket/driving_license/17200009999153306638/1567045385351.jpg");
        input.setDrivingLicenseType("C1");
        input.setExpirationDate("2023-08-29");
        input.setFirstGetLicenseDate("2019-08-29");
        input.setFileNo("310069284580");
        input.setFileNoImgUrl("sensitiveBucket/driving_license/17200009999153306638/1567045453646.jpg");
        input.setDriverLicenseInputType(2);
        input.setSubmitType(0);
        memberReviewService.submitDrivingLicenseInfo("******************", "ddd", input);
    }

    @Test
    public void testSubmitDrivingLicenseInfo3() throws Exception {
        //更新驾照有效期
        SubmitDrivingLicenseInput input = new SubmitDrivingLicenseInput();
        input.setDriverCode("******************");
        input.setName("谌年");
        input.setNational("中国");
        input.setDrivingLicenseImgUrl("sensitiveBucket/driving_license/17200009999153306638/1567045385351.jpg");
        input.setDrivingLicenseType("C1");
        input.setExpirationDate("2023-12-29");
        input.setFirstGetLicenseDate("2019-08-29");
        input.setFileNo("310069284580");
        input.setFileNoImgUrl("sensitiveBucket/driving_license/17200009999153306638/1567045453646.jpg");
        input.setDriverLicenseInputType(2);
        input.setSubmitType(1);
        memberReviewService.submitDrivingLicenseInfo("******************", "ddd", input);
    }

    @Test
    public void testSubmitFileNo() throws Exception {
        //更新驾照有效期
        UpdateFileNoInput input = new UpdateFileNoInput();
        input.setAuthId("******************");
        input.setFileNo("310069284580");
        input.setFileNoImgUrl("sensitiveBucket/driving_license/17200009999153306638/1567045453646.jpg");
        input.setDriverLicenseInputType(1);
        memberShipService.updateUserFileNo(input);
    }

    @Test
    public void testRefreashDrivingLicenseStatus() throws Exception {
        memberReviewService.refreshDrivingLicenseStatus("******************", "ddd");
    }

    @Test
    public void testJob1() throws Exception {
        memberLicenseAuthJob.execute(null);
    }
}
