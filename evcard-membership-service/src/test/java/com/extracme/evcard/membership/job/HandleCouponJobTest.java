package com.extracme.evcard.membership.job;

import com.extracme.evcard.membership.App;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
class HandleCouponJobTest {
    @Resource
    private  HandleCouponJob handleCouponJob;

    @Test
    void execute() {
        handleCouponJob.execute(null);
    }
}