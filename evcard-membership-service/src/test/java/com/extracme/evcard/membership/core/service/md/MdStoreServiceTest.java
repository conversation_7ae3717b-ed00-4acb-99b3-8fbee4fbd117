package com.extracme.evcard.membership.core.service.md;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.App;
import com.extracme.evcard.membership.core.dto.md.SearchCityConfigurationResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2022/8/30
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class MdStoreServiceTest {

    @Autowired
    private MdStoreService mdStoreService;

    @Test
    public void searchCityConfiguration() {
        SearchCityConfigurationResponse resp = mdStoreService.searchCityConfiguration(310100);
        System.out.println(JSON.toJSONString(resp));
    }
}
