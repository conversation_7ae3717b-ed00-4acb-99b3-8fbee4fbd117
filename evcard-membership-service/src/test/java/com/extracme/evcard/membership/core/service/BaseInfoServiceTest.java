package com.extracme.evcard.membership.core.service;

import com.extracme.evcard.membership.core.dto.AgencyInfoDto;
import com.extracme.evcard.membership.core.dto.OrgInfoDto;
import com.extracme.evcard.membership.core.dto.input.AgencyInfoInput;
import com.extracme.evcard.membership.core.input.QueryAgencyInfoInput;
import com.extracme.evcard.membership.core.input.QueryAgencyOperationLogInput;
import com.extracme.evcard.rpc.dto.Page;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import com.extracme.evcard.membership.App;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <p>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @date 2019/3/12
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class BaseInfoServiceTest {

    @Autowired
    private BaseInfoServiceImpl baseInfoService;

    @Test
    public void queryOrgInfoByOrgId() throws Exception {
        OrgInfoDto orgInfoDto = baseInfoService.queryOrgInfoByOrgId("000T");
    }

    @Test
    public void queryAgencyInfoByAgencyId() throws Exception {
        AgencyInfoDto agencyInfoDto = baseInfoService.queryAgencyInfoByAgencyId("0001");
    }

    @Test
    public void createAgencyInfo() throws Exception {
        AgencyInfoInput agencyInfoInput = new AgencyInfoInput();
        agencyInfoInput.setAgencyName("我的机构测试2222");
        agencyInfoInput.setOrgId("000T");
        baseInfoService.createAgencyInfo(agencyInfoInput);
    }

    @Test
    public void createAgencyInfoAndBegin() throws Exception {
        AgencyInfoInput agencyInfoInput = new AgencyInfoInput();
        agencyInfoInput.setAgencyName("我的机构测试22222");
        agencyInfoInput.setOrgId("000T");
        baseInfoService.createAgencyInfoAndBegin(agencyInfoInput);
    }

    @Test
    public void modifyAgencyInfo() throws Exception {
        AgencyInfoInput agencyInfoInput = new AgencyInfoInput();
        agencyInfoInput.setAgencyId("003E");
        agencyInfoInput.setAgencyName("我的机构测试2223232");
        agencyInfoInput.setOrgId("008N01");
        baseInfoService.modifyAgencyInfo(agencyInfoInput);
    }

    @Test
    public void modifyAgencyCooperationStatus() throws Exception {
        baseInfoService.modifyAgencyCooperationStatus("003E",true,"test","20190322154100000");

    }

    @Test
    public void queryAgencyInfoPage() throws Exception {
        QueryAgencyInfoInput queryAgencyInfoInput = new QueryAgencyInfoInput();
        queryAgencyInfoInput.setOrgId("000T");
        Page page = new Page(1,3,true);
        queryAgencyInfoInput.setPage(page);
        baseInfoService.queryAgencyInfoPage(queryAgencyInfoInput);
    }

    @Test
    public void queryAgencyOperationLog() throws Exception {
        QueryAgencyOperationLogInput queryAgencyInfoInput = new QueryAgencyOperationLogInput();
        queryAgencyInfoInput.setAgencyId("003E");
        Page page = new Page(1,3,true);
        queryAgencyInfoInput.setPage(page);
        baseInfoService.queryAgencyOperationLog(queryAgencyInfoInput);
    }


}