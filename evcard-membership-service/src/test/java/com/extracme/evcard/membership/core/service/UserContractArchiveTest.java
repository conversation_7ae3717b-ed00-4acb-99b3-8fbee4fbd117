package com.extracme.evcard.membership.core.service;


import com.extracme.evcard.membership.App;
import com.extracme.evcard.membership.job.UserContractArchiveRecoverJob;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class UserContractArchiveTest {

    @Autowired
    private UserContractArchiveRecoverJob userContractArchiveRecoverJob;

    @Test
    public void testArchive(){
        userContractArchiveRecoverJob.batchArchiveSeveralFiles("1,2");

        userContractArchiveRecoverJob.batchArchiveFilesByTime(null);
    }

}
