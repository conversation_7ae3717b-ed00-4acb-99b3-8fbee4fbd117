package com.extracme.evcard.membership.ocr;

import com.extracme.evcard.membership.App;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2022/8/19
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class OcrAdapterImplTest {

    @Autowired
    private OcrAdapterImpl ocrAdapterImpl;

    @Test
    public void driverLicenseOCRFront() {
        String key = "driving.license.ocr.route";
        String route = "baidu=80,textin=20";
        int baiduCount = 0;
        int textinCount = 0;
        for (int i = 0; i < 10000; i++) {
            String result = ocrAdapterImpl.getChannel(key, route);
            System.out.println(i + " " + result);
            if ("baidu".equals(result)) {
                baiduCount++;
            } else if ("textin".equals(result)) {
                textinCount++;
            }
        }
        System.out.println("baidu=" + baiduCount);
        System.out.println("textin=" + textinCount);
    }

}
