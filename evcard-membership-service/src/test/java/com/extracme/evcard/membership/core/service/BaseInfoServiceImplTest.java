package com.extracme.evcard.membership.core.service;

import com.extracme.evcard.membership.core.dto.AgencyInfoBlackDTO;
import com.extracme.evcard.membership.core.dto.AgencyInfoDto;
import com.extracme.evcard.membership.core.input.QueryAgencyInfoBlackConditionInput;
import com.extracme.evcard.membership.core.input.QueryAgencyListConditionInput;
import com.extracme.evcard.membership.health.service.MemberShipInvitationServ;
import com.extracme.evcard.rpc.util.DateType;
import com.extracme.evcard.rpc.util.DateUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import com.extracme.evcard.membership.App;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class BaseInfoServiceImplTest {

    @Autowired
    private IBaseInfoService baseInfoService;
    @Autowired
    private MemberShipInvitationServ memberShipInvitationServ;

    @Test
    public void queryAgencyListByCondition() {
        QueryAgencyListConditionInput queryAgencyListConditionInput = new QueryAgencyListConditionInput();
        queryAgencyListConditionInput.setOrgId("000T");
        queryAgencyListConditionInput.setStatus(1);
        List<String> agencyIds = new ArrayList<>();
        agencyIds.add("0024");
        queryAgencyListConditionInput.setAgencyIds(agencyIds);
        queryAgencyListConditionInput.setIsInQueryAgencyIds(false);
        List<AgencyInfoDto> agencyInfoDtoList = baseInfoService.queryAgencyListByCondition(queryAgencyListConditionInput);
        System.out.println(agencyInfoDtoList);
    }

    @Test
    public void queryAgencyInfoBlackById() {
        AgencyInfoBlackDTO agencyInfoBlackDTO = baseInfoService.queryAgencyInfoBlackById("00AABB");
        System.out.println(agencyInfoBlackDTO);
    }

    @Test
    public void deleteAgencyInfoBlackById() {
        boolean b = baseInfoService.deleteAgencyInfoBlackById("00AABB");
        System.out.println(b);
    }

    @Test
    public void insertAgencyInfoBlack() {
        AgencyInfoBlackDTO agencyInfoBlackDTO = new AgencyInfoBlackDTO();
        agencyInfoBlackDTO.setAgencyId("00AABB");
        agencyInfoBlackDTO.setAgencyName("test");
        agencyInfoBlackDTO.setOrgId("000T");
        //agencyInfoBlackDTO.setAddTime(DateUtils.format(new Date(), DateType.DATE_TYPE12));
        boolean b = baseInfoService.insertAgencyInfoBlack(agencyInfoBlackDTO);
        System.out.println(b);
    }

    @Test
    public void queryAgencyInfoBlackByCondition() {

        QueryAgencyInfoBlackConditionInput infoBlackConditionInput = new QueryAgencyInfoBlackConditionInput();
        infoBlackConditionInput.setOrgId("000");
        infoBlackConditionInput.setIsLikeQueryOrgId(3);
        List<AgencyInfoBlackDTO> agencyInfoBlackDTOList = baseInfoService.queryAgencyInfoBlackByCondition(infoBlackConditionInput);
        System.out.println(agencyInfoBlackDTOList);
    }

    @Test
    public void healthCode() {

        memberShipInvitationServ.getMemberHealth("吴迪","15700083760092531064","");
    }

}
