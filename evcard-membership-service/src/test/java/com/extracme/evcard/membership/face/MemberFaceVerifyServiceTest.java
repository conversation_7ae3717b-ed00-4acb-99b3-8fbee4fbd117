package com.extracme.evcard.membership.face;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.core.dto.FacePersonVerifyResult;
import com.extracme.evcard.membership.core.dto.input.FaceMatchInput;
import com.extracme.evcard.membership.third.baidu.FileUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import com.extracme.evcard.membership.App;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;

import static org.junit.Assert.*;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class MemberFaceVerifyServiceTest {

    @Autowired
    MemberFaceVerifyService memberFaceVerifyService;

    @Test
    public void testMathEnhance() throws IOException {
        String image1 = FileUtil.readFileAsString("C:\\Users\\<USER>\\Desktop\\image13.txt");
        String image2 = FileUtil.readFileAsString("C:\\Users\\<USER>\\Desktop\\image14.txt");

        FaceMatchInput faceMatchInput = new FaceMatchInput();
        faceMatchInput.setAuthId("111");
        faceMatchInput.setSceneType(1);
        faceMatchInput.setImage(image2);
        faceMatchInput.setImageType(0);
        faceMatchInput.setApp(1);
        faceMatchInput.setRegisterImage(image1);
        faceMatchInput.setRegisterImageType(0);

        FacePersonVerifyResult facePersonVerifyResult = memberFaceVerifyService.enhancerFaceMatch(faceMatchInput);
        System.out.println(JSON.toJSONString(facePersonVerifyResult));
    }
    



}