package com.extracme.evcard.membership.inner.service;

import com.extracme.evcard.membership.App;
import com.extracme.evcard.membership.core.model.UserDeviceInfo;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.jupiter.api.Assertions.*;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
class UserDeviceInfoServiceTest {

    @Autowired
    private UserDeviceInfoService userDeviceInfoService;
    @Test
    void bindUserDeviceInfo() {
        UserDeviceInfo userDeviceInfo = new UserDeviceInfo();
        userDeviceInfo.setMid("123456");
        userDeviceInfo.setDeviceCode("devicecode");
        userDeviceInfo.setOsType(1);
        userDeviceInfo.setDeviceCodeType(1);
        userDeviceInfo.setType(1);
        userDeviceInfoService.bindUserDeviceInfo(userDeviceInfo);
    }
}