package com.extracme.evcard.membership.ocr;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.App;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2022/8/18
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class TextinOcrClientTest {

    @Autowired
    private TextinOcrClient textinOcrClient;

    // 由于生腾渠道支持image方式，所以这边逻辑是把url或image方式都统一转成image，这里的url还是支持本地路径或线上url的
    @Test
    public void idCardOCRFront() {
        CommonIdCardOcrReq req = new CommonIdCardOcrReq();
        req.setMid("123");
        req.setUrl("d:/身份证正面.jpg");
        CommonIdCardOcrResp resp = textinOcrClient.idCardOcr(req);
        System.out.println(JSON.toJSONString(resp));
    }

    @Test
    public void idCardOCRBack() {
        CommonIdCardOcrReq req = new CommonIdCardOcrReq();
        req.setMid("123");
        req.setUrl("d:/身份证反面.jpg");
        CommonIdCardOcrResp resp = textinOcrClient.idCardOcr(req);
        System.out.println(JSON.toJSONString(resp));
    }

    @Test
    public void idCardOCRFrontCopy() {
        CommonIdCardOcrReq req = new CommonIdCardOcrReq();
        req.setMid("123");
        req.setUrl("d:/身份证正面_复印件.jpg");
        CommonIdCardOcrResp resp = textinOcrClient.idCardOcr(req);
        System.out.println(JSON.toJSONString(resp));
    }

    @Test
    public void idCardOCRBackCopy() {
        CommonIdCardOcrReq req = new CommonIdCardOcrReq();
        req.setMid("123");
        req.setUrl("d:/身份证反面_复印件.jpg");
        CommonIdCardOcrResp resp = textinOcrClient.idCardOcr(req);
        System.out.println(JSON.toJSONString(resp));
    }

    @Test
    public void idCardOCRBackForever() {
        CommonIdCardOcrReq req = new CommonIdCardOcrReq();
        req.setMid("123");
        req.setUrl("d:/身份证反面_长期.jpg");
        CommonIdCardOcrResp resp = textinOcrClient.idCardOcr(req);
        System.out.println(JSON.toJSONString(resp));
    }

    @Test
    public void idCardOCRBackForeverCopy() {
        CommonIdCardOcrReq req = new CommonIdCardOcrReq();
        req.setMid("123");
        req.setUrl("d:/身份证反面_长期_复印件.jpg");
        CommonIdCardOcrResp resp = textinOcrClient.idCardOcr(req);
        System.out.println(JSON.toJSONString(resp));
    }
}
