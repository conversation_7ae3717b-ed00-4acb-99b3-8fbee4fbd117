package com.extracme.evcard.membership.job;

import com.extracme.evcard.membership.App;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;
@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
class YiguanMemberStatusTrackJobTest {

    @Resource
    private  YiguanMemberStatusTrackJob job;
    @Resource
    YiguanMemberTrackJob job2;

    @Test
    void execute() {
        job.start(530);
    }

    @Test
    void execute2() {
        job2.execute(null);
    }
}