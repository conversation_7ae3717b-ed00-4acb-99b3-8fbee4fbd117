package com.extracme.evcard.membership.core.service.auth;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.App;
import com.extracme.evcard.membership.common.ImgUtils;
import com.extracme.evcard.membership.common.UploadImgUtil;
import com.extracme.evcard.membership.core.dto.*;
import com.extracme.evcard.membership.core.dto.input.*;
import com.extracme.evcard.membership.core.exception.AuthenticationException;
import com.extracme.evcard.membership.core.input.OcrAndSubmitIdentityInput;
import com.extracme.evcard.membership.core.input.OcrAndSubmitLicenseInput;
import com.extracme.evcard.membership.core.service.IMemberCertificationService;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.dto.PageBeanDto;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
class MemberCertificationServiceTest {

    @Autowired
    private IMemberCertificationService memberCertificationService;

    @Test
    void updateDrivingLicenceReviewStatus() {
        UpdateLicenceReviewDto updateAuthIdItemsDTO = new  UpdateLicenceReviewDto();
        updateAuthIdItemsDTO.setMid("2201211500001");
        updateAuthIdItemsDTO.setAuditStatus(1);
        updateAuthIdItemsDTO.setOperatorId("3654");
        updateAuthIdItemsDTO.setReviewItems("111111111111");
        updateAuthIdItemsDTO.setOperatorUserName("extracme@hq");
        updateAuthIdItemsDTO.setOperateSourceType(1);

        memberCertificationService.updateDrivingLicenceReviewStatus(updateAuthIdItemsDTO);
    }

    @Test
    void passAuditIdentity() {
        AuditIdCardInput auditIdCardInput = new AuditIdCardInput();
        auditIdCardInput.setMid("2208301500002");
        auditIdCardInput.setOperateSourceType(0);
        auditIdCardInput.setOperatorId("3654");
        auditIdCardInput.setOperatorUserName("xl-test-pass2");
        auditIdCardInput.setReviewItems("1111111");

        System.out.println(memberCertificationService.passAuditIdentity(auditIdCardInput));
    }

    @Test
    void notPassAuditIdentity() {

        AuditIdCardInput auditIdCardInput = new AuditIdCardInput();
        auditIdCardInput.setMid("2208160900002");
        auditIdCardInput.setOperateSourceType(0);
        auditIdCardInput.setOperatorId("12342134");
        auditIdCardInput.setOperatorUserName("xl-test-notpass");
        auditIdCardInput.setReviewItems("2111111");
        auditIdCardInput.setReviewRemark("身份证号码不对");
        System.out.println(memberCertificationService.notPassAuditIdentity(auditIdCardInput));
    }

    @Test
    void reAuditIdentity() {
        AuditIdCardInput auditIdCardInput = new AuditIdCardInput();
        auditIdCardInput.setMid("2208311300001");
        auditIdCardInput.setOperateSourceType(0);
        auditIdCardInput.setOperatorId("12342134");
        auditIdCardInput.setOperatorUserName("xl-test-reaudit");
        //auditIdCardInput.setReviewItems("0000000");
        System.out.println(memberCertificationService.reAuditIdentity(auditIdCardInput));
    }

    @Test
    void queryAuditLogs() {
        PageBeanDto<MemberAuthLogDTO> pageBeanDto = memberCertificationService.queryAuditLogs(0, 10, 1, "2208301500002");
        System.out.println(pageBeanDto);
        System.out.println(pageBeanDto.getList().size());
        List<MemberAuthLogDTO> list = pageBeanDto.getList();
        list.stream().forEach(l -> System.out.println(l));

    }
    @Test
    void reAuditMember() {


        MemberReAuditInput input2 = new MemberReAuditInput();
        input2.setType(1);
        input2.setMid("2209071400003");
        input2.setOperatorId("1111");
        input2.setOperatorName("ceshi");
        input2.setOperateSource("xlsource");
        DefaultServiceRespDTO defaultServiceRespDTO2 = memberCertificationService.reAuditMember(input2);
        System.out.println(defaultServiceRespDTO2.getCode());

        MemberReAuditInput input = new MemberReAuditInput();
        input.setType(2);
        input.setMid("2312041400001");
        input.setOperatorId("1111");
        input.setOperatorName("ceshi");
        input.setOperateSource("xlsource");
        DefaultServiceRespDTO defaultServiceRespDTO = memberCertificationService.reAuditMember(input);
        System.out.println(defaultServiceRespDTO.getCode());
    }


    @Test
    public void judgeIfCanTrade() {
        JudgeIfCanTradeResp resp = memberCertificationService.judgeIfCanTrade("15839636001132839981", (short) 0,"");
        System.out.println(JSON.toJSONString(resp));
    }
    @Test
    public void thirdSubmitFaceImgToReview() {
        try {
            String k1 ="{\"appKey\":\"wechet_2019\",\"checkFaceAuthStatus\":true,\"faceRecImg\":\"\",\"mid\":\"2111221000001\",\"refKey2\":\"silent_idnumber_verification\"}";
            String k ="{\"appKey\":\"wechet_2019\",\"checkFaceAuthStatus\":true,\"faceRecImg\":\"\",\"mid\":\"2310101000001\",\"refKey2\":\"silent_idnumber_verification\"}";

            SubmitFaceImgSepInput input = JSON.parseObject(k,SubmitFaceImgSepInput.class);
            BaseResponse baseResponse = memberCertificationService.thirdSubmitFaceImgToReview(input);
            System.out.println(JSON.toJSONString(baseResponse));
        } catch (AuthenticationException e) {
            e.printStackTrace();
        }
    }


    @Test
    public void ofcSubmitFacePic() {
      /*  try {
            String k = "{\n" +
                    "    \n" +
                    "    \"mid\":\"2302211400001\",\n" +
                    "    \"imageType\":1,\n" +
                    "    \"appKey\":\"ofc\",\n" +
                    "    \"appType\":\"ios\",\n" +
                    "    \"forceSuccess\":false,\n" +
                    "    \"imageSrc\":\"https://evcard-sensitive.oss-cn-shanghai.aliyuncs.com/test/sensitiveBucket/faceRecognitionPic/15669059310144159799/1682411210855.jpg?Expires=**********&OSSAccessKeyId=LTAI5tJumdVNXHPLszk49yAk&Signature=hEZ6vBQgx1whdazSFYOmZPxbfeU%3D\"\n" +
                    "}";*/

           /* String k = "{\n" +
                    "    \n" +
                    "    \"mid\":\"2302211400001\",\n" +
                    "    \"imageType\":1,\n" +
                    "    \"appKey\":\"ofc\",\n" +
                    "    \"appType\":\"ios\",\n" +
                    "    \"forceSuccess\":false,\n" +
                    "    \"imageSrc\":\"https://evcard-sensitive.oss-cn-shanghai.aliyuncs.com/test/sensitiveBucket/faceRecognitionPic/15669059310144159799/1682411210855.jpg?Expires=**********&OSSAccessKeyId=LTAI5tJumdVNXHPLszk49yAk&Signature=zfVfb9wSCFAZPwyBuWXhMhpE3oM%3D\"\n" +
                    "}";

            OfcSubmitFacePicInput input = JSON.parseObject(k,OfcSubmitFacePicInput.class);
            BaseResponse baseResponse = memberCertificationService.ofcSubmitFacePic(input);
            System.out.println(JSON.toJSONString(baseResponse));
        } catch (AuthenticationException e) {
            e.printStackTrace();
        }*/
    }


    @Test
    public void ocrAndSubmitLicense() {
        try {
            OcrAndSubmitLicenseInput input = new OcrAndSubmitLicenseInput();
            input.setMid("2307111500006");
            input.setLicenseNo("1232132");
            input.setAppKey("test");
            input.setOperationModel("1");
            input.setSecondAppKey("second_test");
            input.setDriverLicenseImgUrl("http://evcard-sensitive.oss-cn-shanghai.aliyuncs.com/test/sensitiveBucket/driving_license/15669059315153342701/1726123802028.jpg?Expires=**********&OSSAccessKeyId=LTAI5tJumdVNXHPLszk49yAk&Signature=RhYwleUg%2BroS9vJit6qVdn59p9o%3D");
            input.setReverseDriverLicenseImgUrl("");
            System.out.println(JSON.toJSONString(input));
            String k ="{\"appKey\":\"wechet_2019\",\"checkFaceAuthStatus\":true,\"faceRecImg\":\"\",\"mid\":\"2310101000001\",\"refKey2\":\"silent_idnumber_verification\"}";

            //OcrAndSubmitLicenseInput input = JSON.parseObject(k,OcrAndSubmitLicenseInput.class);
            OcrAndSubmitLicenseRespDto baseResponse = memberCertificationService.ocrAndSubmitLicense(input);
            System.out.println(JSON.toJSONString(baseResponse));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Test
    public void ocrAndSubmitIdentity() {
        try {
            OcrAndSubmitIdentityInput input = new OcrAndSubmitIdentityInput();
            input.setMid("2307111500006");
            input.setIdentityNo("12323412455311");
            input.setAppKey("test");
            input.setOperationModel("1");
            input.setSecondAppKey("second_test");
            input.setIdentityType(1);

            input.setIdentityCardImgUrl("http://evcard-sensitive.oss-cn-shanghai.aliyuncs.com/test/sensitiveBucket/newIdCardImg/1665623192015.jpg?Expires=**********&OSSAccessKeyId=LTAI5tJumdVNXHPLszk49yAk&Signature=uHZBas8HPA9nk%2Fo%2BEHjYvM9qvkY%3D");
            input.setReverseIdentityCardImgUrl("http://evcard-sensitive.oss-cn-shanghai.aliyuncs.com/test/sensitiveBucket/newIdCardImg/1665623202032.jpg?Expires=**********&OSSAccessKeyId=LTAI5tJumdVNXHPLszk49yAk&Signature=OnUAS0R1sePlbN8CBNffnw83LKQ%3D");
            System.out.println(JSON.toJSONString(input));
            String k ="{\"appKey\":\"wechet_2019\",\"checkFaceAuthStatus\":true,\"faceRecImg\":\"\",\"mid\":\"2310101000001\",\"refKey2\":\"silent_idnumber_verification\"}";

            //OcrAndSubmitIdentityInput input = JSON.parseObject(k,OcrAndSubmitIdentityInput.class);
            OcrAndSubmitIdentityRespDto baseResponse = memberCertificationService.ocrAndSubmitIdentity(input);
            System.out.println(JSON.toJSONString(baseResponse));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }






}