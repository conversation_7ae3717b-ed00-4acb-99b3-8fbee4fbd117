package com.extracme.evcard.membership.job;

import com.extracme.evcard.membership.App;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
class NewUserContractArchiveJobTest {
    @Resource
    private  NewUserContractArchiveRecoverJob newUserContractArchiveRecoverJob;

    @Test
    void exbatchArchiveSeveralFilesecute() {
        //newUserContractArchiveRecoverJob.batchArchiveSeveralFiles("MC240704140000501,MC240703160000101,MC240628090000401");
    }
}