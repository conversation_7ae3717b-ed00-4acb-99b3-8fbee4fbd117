package com.extracme.evcard.membership.credit.model;

import java.math.BigDecimal;
import java.util.Date;

public class MmpUserTag {
    private Long id;

    private String authId;

    private BigDecimal realAmount;

    private BigDecimal effectiveContd;

    private Double creditAmount;

    private String remark;

    private Date createTime;

    private Long createOperId;

    private String createOperName;

    private Date updateTime;

    private Long updateOperId;

    private String updateOperName;

    private String spare1;

    private String spare2;

    private String spare3;

    private String spare4;

    private String spare5;

    private String spare6;
    /**
     * 支付宝userid
     */
    private String spare7;

    /**
     * 微信openId
     */
    private String spare8;

    private String spare9;

    private String spare10;

    /**
     * 支付宝openid
     */
    private String aliOpenId;

    private String shopLimit;

    private Integer  profession;

    private Integer  educational;

    private Integer ownCar;

    private String studentCardUrl;

    // 支付宝订单中心授权0：未授权 1：已授权
    private Integer alipayOrderAuthority;

    public Integer getAlipayOrderAuthority() {
        return alipayOrderAuthority;
    }

    public void setAlipayOrderAuthority(Integer alipayOrderAuthority) {
        this.alipayOrderAuthority = alipayOrderAuthority;
    }

    public String getAliOpenId() {
        return aliOpenId;
    }

    public void setAliOpenId(String aliOpenId) {
        this.aliOpenId = aliOpenId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public BigDecimal getRealAmount() {
        return realAmount;
    }

    public void setRealAmount(BigDecimal realAmount) {
        this.realAmount = realAmount;
    }

    public BigDecimal getEffectiveContd() {
        return effectiveContd;
    }

    public void setEffectiveContd(BigDecimal effectiveContd) {
        this.effectiveContd = effectiveContd;
    }

    public Double getCreditAmount() {
        return creditAmount;
    }

    public void setCreditAmount(Double creditAmount) {
        this.creditAmount = creditAmount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateOperId() {
        return createOperId;
    }

    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    public String getCreateOperName() {
        return createOperName;
    }

    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdateOperId() {
        return updateOperId;
    }

    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    public String getUpdateOperName() {
        return updateOperName;
    }

    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }

    public String getSpare1() {
        return spare1;
    }

    public void setSpare1(String spare1) {
        this.spare1 = spare1;
    }

    public String getSpare2() {
        return spare2;
    }

    public void setSpare2(String spare2) {
        this.spare2 = spare2;
    }

    public String getSpare3() {
        return spare3;
    }

    public void setSpare3(String spare3) {
        this.spare3 = spare3;
    }

    public String getSpare4() {
        return spare4;
    }

    public void setSpare4(String spare4) {
        this.spare4 = spare4;
    }

    public String getSpare5() {
        return spare5;
    }

    public void setSpare5(String spare5) {
        this.spare5 = spare5;
    }

    public String getSpare6() {
        return spare6;
    }

    public void setSpare6(String spare6) {
        this.spare6 = spare6;
    }

    public String getSpare7() {
        return spare7;
    }

    public void setSpare7(String spare7) {
        this.spare7 = spare7;
    }

    public String getSpare8() {
        return spare8;
    }

    public void setSpare8(String spare8) {
        this.spare8 = spare8;
    }

    public String getSpare9() {
        return spare9;
    }

    public void setSpare9(String spare9) {
        this.spare9 = spare9;
    }

    public String getSpare10() {
        return spare10;
    }

    public void setSpare10(String spare10) {
        this.spare10 = spare10;
    }

    public String getShopLimit() {
        return shopLimit;
    }

    public void setShopLimit(String shopLimit) {
        this.shopLimit = shopLimit;
    }

    public Integer getProfession() {
        return profession;
    }

    public void setProfession(Integer profession) {
        this.profession = profession;
    }

    public Integer getEducational() {
        return educational;
    }

    public void setEducational(Integer educational) {
        this.educational = educational;
    }

    public Integer getOwnCar() {
        return ownCar;
    }

    public void setOwnCar(Integer ownCar) {
        this.ownCar = ownCar;
    }

    public String getStudentCardUrl() {
        return studentCardUrl;
    }

    public void setStudentCardUrl(String studentCardUrl) {
        this.studentCardUrl = studentCardUrl;
    }
}