package com.extracme.evcard.membership.core.service.auth;

import com.extracme.evcard.membership.core.input.EvcardTokenDto;
import com.extracme.evcard.membership.core.input.GetThirdAccessTokenInput;
import com.extracme.evcard.membership.core.input.ThirdLoginDto;
import com.extracme.evcard.membership.core.input.ThirdLoginInput;
import com.extracme.evcard.rpc.exception.BusinessException;

public interface IThirdLogin {

    ThirdLoginDto thirdLogin (ThirdLoginInput input) throws BusinessException;


    EvcardTokenDto getThirdAccessToken(GetThirdAccessTokenInput input) throws BusinessException;
}
