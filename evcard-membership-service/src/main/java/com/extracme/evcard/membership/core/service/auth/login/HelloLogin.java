package com.extracme.evcard.membership.core.service.auth.login;

import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 擎路 登录注册
 */
@Slf4j
@Service
public class HelloLogin extends QingLuLogin {
    @Override
    public String getSecondAppKey() {
        return BussinessConstants.HELLO_SECOND_APP_KEY;
    }

    @Override
    public String getDescription() {
        return "哈啰登录";
    }
}
