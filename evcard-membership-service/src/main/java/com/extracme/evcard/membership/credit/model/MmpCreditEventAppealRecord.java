package com.extracme.evcard.membership.credit.model;

import java.util.Date;

public class MmpCreditEventAppealRecord {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_appeal_record.id
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_appeal_record.auth_id
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    private String authId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_appeal_record.event_id
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    private Long eventId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_appeal_record.appeal_desc
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    private String appealDesc;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_appeal_record.appeal_file_path
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    private String appealFilePath;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_appeal_record.appeal_image_path
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    private String appealImagePath;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_appeal_record.appeal_time
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    private Date appealTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_appeal_record.handle_user_id
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    private String handleUserId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_appeal_record.handle_user
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    private String handleUser;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_appeal_record.handle_time
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    private Date handleTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_appeal_record.handle_remark
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    private String handleRemark;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_appeal_record.handle_result
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    private Integer handleResult;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_appeal_record.misc_desc
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    private String miscDesc;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_appeal_record.status
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_appeal_record.create_time
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_appeal_record.create_oper_id
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    private String createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_appeal_record.create_oper_name
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    private String createOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_appeal_record.update_time
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_appeal_record.update_oper_id
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    private Long updateOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_appeal_record.update_oper_name
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    private String updateOperName;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_appeal_record.id
     *
     * @return the value of mmp_credit_event_appeal_record.id
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_appeal_record.id
     *
     * @param id the value for mmp_credit_event_appeal_record.id
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_appeal_record.auth_id
     *
     * @return the value of mmp_credit_event_appeal_record.auth_id
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public String getAuthId() {
        return authId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_appeal_record.auth_id
     *
     * @param authId the value for mmp_credit_event_appeal_record.auth_id
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public void setAuthId(String authId) {
        this.authId = authId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_appeal_record.event_id
     *
     * @return the value of mmp_credit_event_appeal_record.event_id
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public Long getEventId() {
        return eventId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_appeal_record.event_id
     *
     * @param eventId the value for mmp_credit_event_appeal_record.event_id
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public void setEventId(Long eventId) {
        this.eventId = eventId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_appeal_record.appeal_desc
     *
     * @return the value of mmp_credit_event_appeal_record.appeal_desc
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public String getAppealDesc() {
        return appealDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_appeal_record.appeal_desc
     *
     * @param appealDesc the value for mmp_credit_event_appeal_record.appeal_desc
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public void setAppealDesc(String appealDesc) {
        this.appealDesc = appealDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_appeal_record.appeal_file_path
     *
     * @return the value of mmp_credit_event_appeal_record.appeal_file_path
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public String getAppealFilePath() {
        return appealFilePath;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_appeal_record.appeal_file_path
     *
     * @param appealFilePath the value for mmp_credit_event_appeal_record.appeal_file_path
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public void setAppealFilePath(String appealFilePath) {
        this.appealFilePath = appealFilePath;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_appeal_record.appeal_image_path
     *
     * @return the value of mmp_credit_event_appeal_record.appeal_image_path
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public String getAppealImagePath() {
        return appealImagePath;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_appeal_record.appeal_image_path
     *
     * @param appealImagePath the value for mmp_credit_event_appeal_record.appeal_image_path
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public void setAppealImagePath(String appealImagePath) {
        this.appealImagePath = appealImagePath;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_appeal_record.appeal_time
     *
     * @return the value of mmp_credit_event_appeal_record.appeal_time
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public Date getAppealTime() {
        return appealTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_appeal_record.appeal_time
     *
     * @param appealTime the value for mmp_credit_event_appeal_record.appeal_time
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public void setAppealTime(Date appealTime) {
        this.appealTime = appealTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_appeal_record.handle_user_id
     *
     * @return the value of mmp_credit_event_appeal_record.handle_user_id
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public String getHandleUserId() {
        return handleUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_appeal_record.handle_user_id
     *
     * @param handleUserId the value for mmp_credit_event_appeal_record.handle_user_id
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public void setHandleUserId(String handleUserId) {
        this.handleUserId = handleUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_appeal_record.handle_user
     *
     * @return the value of mmp_credit_event_appeal_record.handle_user
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public String getHandleUser() {
        return handleUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_appeal_record.handle_user
     *
     * @param handleUser the value for mmp_credit_event_appeal_record.handle_user
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public void setHandleUser(String handleUser) {
        this.handleUser = handleUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_appeal_record.handle_time
     *
     * @return the value of mmp_credit_event_appeal_record.handle_time
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public Date getHandleTime() {
        return handleTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_appeal_record.handle_time
     *
     * @param handleTime the value for mmp_credit_event_appeal_record.handle_time
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public void setHandleTime(Date handleTime) {
        this.handleTime = handleTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_appeal_record.handle_remark
     *
     * @return the value of mmp_credit_event_appeal_record.handle_remark
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public String getHandleRemark() {
        return handleRemark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_appeal_record.handle_remark
     *
     * @param handleRemark the value for mmp_credit_event_appeal_record.handle_remark
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public void setHandleRemark(String handleRemark) {
        this.handleRemark = handleRemark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_appeal_record.handle_result
     *
     * @return the value of mmp_credit_event_appeal_record.handle_result
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public Integer getHandleResult() {
        return handleResult;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_appeal_record.handle_result
     *
     * @param handleResult the value for mmp_credit_event_appeal_record.handle_result
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public void setHandleResult(Integer handleResult) {
        this.handleResult = handleResult;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_appeal_record.misc_desc
     *
     * @return the value of mmp_credit_event_appeal_record.misc_desc
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public String getMiscDesc() {
        return miscDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_appeal_record.misc_desc
     *
     * @param miscDesc the value for mmp_credit_event_appeal_record.misc_desc
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_appeal_record.status
     *
     * @return the value of mmp_credit_event_appeal_record.status
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_appeal_record.status
     *
     * @param status the value for mmp_credit_event_appeal_record.status
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_appeal_record.create_time
     *
     * @return the value of mmp_credit_event_appeal_record.create_time
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_appeal_record.create_time
     *
     * @param createTime the value for mmp_credit_event_appeal_record.create_time
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_appeal_record.create_oper_id
     *
     * @return the value of mmp_credit_event_appeal_record.create_oper_id
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public String getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_appeal_record.create_oper_id
     *
     * @param createOperId the value for mmp_credit_event_appeal_record.create_oper_id
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public void setCreateOperId(String createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_appeal_record.create_oper_name
     *
     * @return the value of mmp_credit_event_appeal_record.create_oper_name
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_appeal_record.create_oper_name
     *
     * @param createOperName the value for mmp_credit_event_appeal_record.create_oper_name
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_appeal_record.update_time
     *
     * @return the value of mmp_credit_event_appeal_record.update_time
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_appeal_record.update_time
     *
     * @param updateTime the value for mmp_credit_event_appeal_record.update_time
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_appeal_record.update_oper_id
     *
     * @return the value of mmp_credit_event_appeal_record.update_oper_id
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public Long getUpdateOperId() {
        return updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_appeal_record.update_oper_id
     *
     * @param updateOperId the value for mmp_credit_event_appeal_record.update_oper_id
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_appeal_record.update_oper_name
     *
     * @return the value of mmp_credit_event_appeal_record.update_oper_name
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public String getUpdateOperName() {
        return updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_appeal_record.update_oper_name
     *
     * @param updateOperName the value for mmp_credit_event_appeal_record.update_oper_name
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }
}