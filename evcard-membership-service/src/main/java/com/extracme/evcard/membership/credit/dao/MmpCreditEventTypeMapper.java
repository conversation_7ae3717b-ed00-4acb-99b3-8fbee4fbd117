package com.extracme.evcard.membership.credit.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.extracme.evcard.membership.credit.dto.CreditEventTypeDto;
import com.extracme.evcard.membership.credit.dto.CreditEventTypePageDto;
import com.extracme.evcard.membership.credit.dto.CreditEventTypeParamsDto;
import com.extracme.evcard.membership.credit.model.MmpCreditEventType;
import com.extracme.evcard.rpc.dto.Page;

public interface MmpCreditEventTypeMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_credit_event_type
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_credit_event_type
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    int insert(MmpCreditEventType record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_credit_event_type
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    int insertSelective(MmpCreditEventType record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_credit_event_type
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    MmpCreditEventType selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_credit_event_type
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    int updateByPrimaryKeySelective(MmpCreditEventType record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_credit_event_type
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    int updateByPrimaryKey(MmpCreditEventType record);

    /**
     * 获取所有事件类型
     * @return
     */
    List<CreditEventTypeDto> getCreditEventTypes();

    /**
     * 查询事件类型总条数
     * @param paramsDto
     * @return
     */
    Integer getCreditEventTypeCount(CreditEventTypeParamsDto paramsDto);

    /**
     * 获取事件类型列表分页
     * @param paramsDto
     * @param page
     * @return
     */
    List<CreditEventTypePageDto> getCreditEventTypePages(@Param("paramsDto") CreditEventTypeParamsDto paramsDto,
                                                         @Param("page")Page page);

}