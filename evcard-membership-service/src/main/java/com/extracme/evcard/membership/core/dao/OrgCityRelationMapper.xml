<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.core.dao.OrgCityRelationMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.OrgCityRelation" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="city_id" property="cityId" jdbcType="BIGINT" />
    <result column="city_name" property="cityName" jdbcType="VARCHAR" />
    <result column="org_id" property="orgId" jdbcType="VARCHAR" />
    <result column="manage_vehicle_no" property="manageVehicleNo" jdbcType="VARCHAR" />
    <result column="org_name" property="orgName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, city_id, city_name, org_id, manage_vehicle_no, org_name
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from org_city_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
    <select id="selectByCityName" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from ${siacSchema}.org_city_relation
      where city_name = #{name}
    </select>
  <select id="selectByOrgId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${siacSchema}.org_city_relation
    where org_id in
    <foreach item="item" collection="list" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from org_city_relation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.membership.core.model.OrgCityRelation" >
    insert into org_city_relation (id, city_id, city_name, 
      org_id, manage_vehicle_no, org_name
      )
    values (#{id,jdbcType=BIGINT}, #{cityId,jdbcType=BIGINT}, #{cityName,jdbcType=VARCHAR}, 
      #{orgId,jdbcType=VARCHAR}, #{manageVehicleNo,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.core.model.OrgCityRelation" >
    insert into org_city_relation
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="cityId != null" >
        city_id,
      </if>
      <if test="cityName != null" >
        city_name,
      </if>
      <if test="orgId != null" >
        org_id,
      </if>
      <if test="manageVehicleNo != null" >
        manage_vehicle_no,
      </if>
      <if test="orgName != null" >
        org_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="cityId != null" >
        #{cityId,jdbcType=BIGINT},
      </if>
      <if test="cityName != null" >
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null" >
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="manageVehicleNo != null" >
        #{manageVehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null" >
        #{orgName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.core.model.OrgCityRelation" >
    update org_city_relation
    <set >
      <if test="cityId != null" >
        city_id = #{cityId,jdbcType=BIGINT},
      </if>
      <if test="cityName != null" >
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null" >
        org_id = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="manageVehicleNo != null" >
        manage_vehicle_no = #{manageVehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null" >
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.core.model.OrgCityRelation" >
    update org_city_relation
    set city_id = #{cityId,jdbcType=BIGINT},
      city_name = #{cityName,jdbcType=VARCHAR},
      org_id = #{orgId,jdbcType=VARCHAR},
      manage_vehicle_no = #{manageVehicleNo,jdbcType=VARCHAR},
      org_name = #{orgName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>