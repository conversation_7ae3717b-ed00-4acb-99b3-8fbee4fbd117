<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.extracme.evcard.membership.core.dao.CardActionMapper">
  
  <insert id="insert">
    insert into ${siacSchema}.card_action (CARD_NO, VIN, 
      DOOR_STATUS, CREATED_TIME, CREATED_USER, 
      UPDATED_TIME, UPDATED_USER, RETURN_CODE
      )
    values (#{cardNo,jdbcType=VARCHAR}, #{vin,jdbcType=VARCHAR}, 
      #{doorStatus,jdbcType=DOUBLE}, #{createdTime,jdbcType=VARCHAR}, #{createdUser,jdbcType=VARCHAR}, 
      #{updatedTime,jdbcType=VARCHAR}, #{updatedUser,jdbcType=VARCHAR}, #{returnCode,jdbcType=INTEGER}
      )
  </insert>

</mapper>