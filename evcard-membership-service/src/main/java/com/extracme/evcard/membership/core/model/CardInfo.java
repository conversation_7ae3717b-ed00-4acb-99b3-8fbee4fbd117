package com.extracme.evcard.membership.core.model;

/**
 * 模型类，，对应表card_info.
 */
public class CardInfo {
    private String cardNo;
    private String internalNo;
    private String validityTime;
    private String rwKeytAa;
    private String rwKeytBb;
    private Double cardType;
    private Double activateStatus;
    private String activateTime;
    /** 卡号类型（1:虚拟卡号，2:物理卡号） */
    private Integer cardNoType;
    private String createdTime;
    private String createdUser;
    private String updatedTime;
    private String updatedUser;
    /** 证件号 */
    private String authId;
    /** 状态：1有效 0无效 */
    private Integer status;

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getInternalNo() {
        return internalNo;
    }

    public void setInternalNo(String internalNo) {
        this.internalNo = internalNo;
    }

    public String getValidityTime() {
        return validityTime;
    }

    public void setValidityTime(String validityTime) {
        this.validityTime = validityTime;
    }

    public String getRwKeytAa() {
        return rwKeytAa;
    }

    public void setRwKeytAa(String rwKeytAa) {
        this.rwKeytAa = rwKeytAa;
    }

    public String getRwKeytBb() {
        return rwKeytBb;
    }

    public void setRwKeytBb(String rwKeytBb) {
        this.rwKeytBb = rwKeytBb;
    }

    public Double getCardType() {
        return cardType;
    }

    public void setCardType(Double cardType) {
        this.cardType = cardType;
    }

    public Double getActivateStatus() {
        return activateStatus;
    }

    public void setActivateStatus(Double activateStatus) {
        this.activateStatus = activateStatus;
    }

    public String getActivateTime() {
        return activateTime;
    }

    public void setActivateTime(String activateTime) {
        this.activateTime = activateTime;
    }

    public Integer getCardNoType() {
        return cardNoType;
    }

    public void setCardNoType(Integer cardNoType) {
        this.cardNoType = cardNoType;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public String getCreatedUser() {
        return createdUser;
    }

    public void setCreatedUser(String createdUser) {
        this.createdUser = createdUser;
    }

    public String getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(String updatedTime) {
        this.updatedTime = updatedTime;
    }

    public String getUpdatedUser() {
        return updatedUser;
    }

    public void setUpdatedUser(String updatedUser) {
        this.updatedUser = updatedUser;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}