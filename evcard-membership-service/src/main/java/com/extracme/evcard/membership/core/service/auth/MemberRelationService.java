package com.extracme.evcard.membership.core.service.auth;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.core.dao.MemberRelationMapper;
import com.extracme.evcard.membership.core.enums.MemberRelationTypeEnums;
import com.extracme.evcard.membership.core.enums.MemberTypeEnum;
import com.extracme.evcard.membership.core.model.MemberRelation;
import com.extracme.evcard.membership.core.model.MemberRelationExample;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.model.MembershipInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class MemberRelationService {

    @Resource
    private MembershipInfoMapper membershipInfoMapper;
    @Resource
    private MemberRelationMapper memberRelationMapper;

    /**
     * 判断是否 有绑定关系
     *
     * @param pkid1
     * @param pkId2
     * @return true：没有绑定关系，可以添加 false：已经有绑定关系，无需添加
     */
    public boolean needAdd(Long pkid1, Long pkId2, int type) {
        // 判断是否 绑定关系
        try {
            MemberRelationExample memberRelationExample = new MemberRelationExample();
            memberRelationExample.createCriteria()
                    .andPkId1EqualTo(pkid1)
                    .andPkId2EqualTo(pkId2)
                    .andIsDeletedEqualTo(0)
                    .andTypeEqualTo(type)
            ;
            List<MemberRelation> memberRelations = memberRelationMapper.selectByExample(memberRelationExample);
            if (CollectionUtils.isEmpty(memberRelations)) {
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error("判断是否 绑定关系异常，pkid1={},pkid2={}", pkid1, pkId2, e);
            return false;
        }
    }

    /**
     * 绑定指定手机号，渠道会员和外部会员关联关系
     *
     * @param mobile 手机号
     * @param type   1:新增渠道会员 2：新增外部会员
     * @param pkId2  渠道会员id
     */
    public void bindRelation(String mobile, int type, Long pkId2) {

        try {
            Date now = new Date();
            List<MembershipInfo> channelMembershipInfos = membershipInfoMapper.selectByMobilePhoneAndType(mobile, MemberTypeEnum.QINGLU_USER.getValue());
            List<MembershipInfo> outMembershipInfos = membershipInfoMapper.selectByMobilePhoneAndType(mobile, MemberTypeEnum.OUT_USER.getValue());

            List<MemberRelation> batchAddList = new ArrayList<>();
            // 外部会员唯一
            if (CollectionUtils.isEmpty(outMembershipInfos) || CollectionUtils.isEmpty(channelMembershipInfos)) {
                return;
            }
            MembershipInfo outMembershipInfo = outMembershipInfos.get(0);
            Long pkId1 = outMembershipInfo.getPkId();

            if (type == 1) {
                // 新增渠道会员，最多保存一条记录
                if (pkId2 == null) {
                    MembershipInfo channelmembershipInfo = channelMembershipInfos.get(0);
                    pkId2 = channelmembershipInfo.getPkId();
                }
                MemberRelation addMemberRelation = getAddMemberRelation(now, pkId1, pkId2);
                if (addMemberRelation != null) {
                    batchAddList.add(addMemberRelation);
                }
            } else if (type == 2) {
                // 新增外部会员，最多保存多条记录
                for (MembershipInfo channelMembershipInfo : channelMembershipInfos) {
                    try {
                        MemberRelation addMemberRelation = getAddMemberRelation(now, pkId1, channelMembershipInfo.getPkId());
                        if (addMemberRelation != null) {
                            batchAddList.add(addMemberRelation);
                        }
                    } catch (Exception e) {
                        log.error("新增外部会员，绑定关联关系时，异常，channelMembershipInfo={}", JSON.toJSONString(channelMembershipInfo),e);
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(batchAddList)) {
                memberRelationMapper.batchInsert(batchAddList);
            }
        } catch (Exception e) {
            log.error("绑定渠道会员和外部会员关联关系时，异常，mobile={},type={}", mobile,type,e);
        }
    }

    /**
     *  封装 新增对象
     * @param now
     * @param pkId1 外部会员id
     * @param pkId2 渠道会员id
     * @return
     */
    private MemberRelation getAddMemberRelation(Date now, Long pkId1, Long pkId2) {
        if (needAdd(pkId1, pkId2, MemberRelationTypeEnums.CHANNEL_AND_INNER_MEMBER_RELATION.getType())) {
            MemberRelation memberRelationRecord = new MemberRelation();
            memberRelationRecord.setPkId1(pkId1);
            memberRelationRecord.setPkId2(pkId2);
            memberRelationRecord.setType(MemberRelationTypeEnums.CHANNEL_AND_INNER_MEMBER_RELATION.getType());
            memberRelationRecord.setCreateTime(now);
            memberRelationRecord.setUpdateTime(now);
            memberRelationRecord.setCreateOperId(pkId1);
            memberRelationRecord.setUpdateOperId(pkId1);
            memberRelationRecord.setCreateOperName("系统");
            memberRelationRecord.setUpdateOperName("系统");
            return memberRelationRecord;
        }
        return null;
    }

    /**
     * 删除关联关系
     *
     * @param pkId1 外部会员id
     * @return
     */
    public int deleteMemberRelations(Long pkId1){
        return memberRelationMapper.deleteRecordsByPkId1(pkId1, MemberRelationTypeEnums.CHANNEL_AND_INNER_MEMBER_RELATION.getType());
    }

}
