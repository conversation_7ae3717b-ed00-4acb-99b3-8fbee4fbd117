<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- CardInfoHistroyMapper，，对应表card_info_histroy -->
<mapper namespace="com.extracme.evcard.membership.core.dao.CardInfoHistroyMapper">
    <!-- 返回结果集Map -->
    <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.CardInfoHistroy">
        <id column="AUTH_ID" jdbcType="VARCHAR" property="authId" />
        <result column="CARD_NO" jdbcType="VARCHAR" property="cardNo" />
        <result column="AUTH_ID" jdbcType="VARCHAR" property="authId" />
        <result column="MEMBERSHIP_TYPE" jdbcType="DOUBLE" property="membershipType" />
        <result column="CARD_STATUS" jdbcType="DOUBLE" property="cardStatus" />
        <result column="REMARK" jdbcType="VARCHAR" property="remark" />
        <result column="CREATED_TIME" jdbcType="VARCHAR" property="createdTime" />
        <result column="CREATED_USER" jdbcType="VARCHAR" property="createdUser" />
        <result column="UPDATED_TIME" jdbcType="VARCHAR" property="updatedTime" />
        <result column="UPDATED_USER" jdbcType="VARCHAR" property="updatedUser" />
    </resultMap>
    
    <!--数据列-->
    <sql id="Base_Column_List" >
            CARD_NO,
            AUTH_ID,
            MEMBERSHIP_TYPE,
            CARD_STATUS,
            REMARK,
            CREATED_TIME,
            CREATED_USER,
            UPDATED_TIME,
            UPDATED_USER
    </sql>
    
    <!-- 保存数据 -->
    <insert id="addCardInfoHistroy" parameterType="com.extracme.evcard.membership.core.model.CardInfoHistroy">
        insert into ${siacSchema}.card_info_histroy 
        <trim  prefix="(" suffix=")" suffixOverrides=",">
            <if test="cardNo!=null">CARD_NO,</if>
            <if test="authId!=null">AUTH_ID,</if>
            <if test="membershipType!=null">MEMBERSHIP_TYPE,</if>
            <if test="cardStatus!=null">CARD_STATUS,</if>
            <if test="remark!=null">REMARK,</if>
            CREATED_TIME,
            <if test="createdUser!=null">CREATED_USER,</if>
            UPDATED_TIME,
            <if test="updatedUser!=null">UPDATED_USER</if>
        </trim>
        <trim prefix="values(" suffix=")" suffixOverrides=",">
            <if test="cardNo!=null">#{cardNo,jdbcType=VARCHAR},</if>
            <if test="authId!=null">#{authId,jdbcType=VARCHAR},</if>
            <if test="membershipType!=null">#{membershipType,jdbcType=DOUBLE},</if>
            <if test="cardStatus!=null">#{cardStatus,jdbcType=DOUBLE},</if>
            <if test="remark!=null">#{remark,jdbcType=VARCHAR},</if>
            date_format(sysdate(), '%Y%m%d%H%i%s000'),
            <if test="createdUser!=null">#{createdUser,jdbcType=VARCHAR},</if>
            date_format(sysdate(), '%Y%m%d%H%i%s000'),
            <if test="updatedUser!=null">#{updatedUser,jdbcType=VARCHAR}</if>
        </trim>
    </insert>
    
    <!-- 更新数据 -->
	<update id="updateCardInfoHistroy"
		parameterType="com.extracme.evcard.membership.core.model.CardInfoHistroy">
		update ${siacSchema}.card_info_histroy
		<set>
			<if test="authId != null">
				AUTH_ID = #{authId,jdbcType=VARCHAR},
			</if>
			<if test="membershipType != null">
				MEMBERSHIP_TYPE = #{membershipType,jdbcType=DOUBLE},
			</if>
			<if test="cardStatus != null">
				CARD_STATUS = #{cardStatus,jdbcType=DOUBLE},
			</if>
			<if test="remark != null">
				REMARK = #{remark,jdbcType=VARCHAR},
			</if>
			<if test="createdTime != null">
				CREATED_TIME = #{createdTime,jdbcType=VARCHAR},
			</if>
			<if test="createdUser != null">
				CREATED_USER = #{createdUser,jdbcType=VARCHAR},
			</if>
			<if test="updatedUser != null">
				UPDATED_USER = #{updatedUser,jdbcType=VARCHAR},
			</if>
			UPDATED_TIME = date_format(sysdate(), '%Y%m%d%H%i%s000')
		</set>
		where AUTH_ID = #{authId,jdbcType=VARCHAR} AND CARD_NO
		=#{cardNo,jdbcType=VARCHAR}
	</update>
</mapper>