<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.credit.dao.MmpCreditEventRecordMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.credit.model.MmpCreditEventRecord" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 22 16:26:13 CST 2017.
    -->
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="auth_id" property="authId" jdbcType="VARCHAR" />
    <result column="order_seq" property="orderSeq" jdbcType="VARCHAR" />
    <result column="event_type_id" property="eventTypeId" jdbcType="BIGINT" />
    <result column="event_desc" property="eventDesc" jdbcType="VARCHAR" />
    <result column="event_source" property="eventSource" jdbcType="VARCHAR" />
    <result column="event_name" property="eventName" jdbcType="VARCHAR" />
    <result column="event_image_path" property="eventImagePath" jdbcType="VARCHAR" />
    <result column="event_file_path" property="eventFilePath" jdbcType="VARCHAR" />
    <result column="event_nature" property="eventNature" jdbcType="VARCHAR" />
    <result column="amount" property="amount" jdbcType="INTEGER" />
    <result column="black_list" property="blackList" jdbcType="BIT" />
    <result column="misc_desc" property="miscDesc" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 22 16:26:13 CST 2017.
    -->
    id, auth_id, order_seq, event_type_id, event_desc, event_source, event_name, event_image_path, event_file_path,
    event_nature, amount, black_list, misc_desc, status, create_time, create_oper_id, 
    create_oper_name, update_time, update_oper_id, update_oper_name
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 22 16:26:13 CST 2017.
    -->
    select 
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_credit_event_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 22 16:26:13 CST 2017.
    -->
    delete from ${issSchema}.mmp_credit_event_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.membership.credit.model.MmpCreditEventRecord"
          useGeneratedKeys="true" keyProperty="id">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 22 16:26:13 CST 2017.
    -->
    insert into ${issSchema}.mmp_credit_event_record (id, auth_id, order_seq,
      event_type_id, event_desc, event_source, 
      event_name, event_image_path, event_file_path, event_nature,
      amount, black_list, misc_desc, 
      status, create_time, create_oper_id, 
      create_oper_name, update_time, update_oper_id, 
      update_oper_name)
    values (#{id,jdbcType=BIGINT}, #{authId,jdbcType=VARCHAR}, #{orderSeq,jdbcType=VARCHAR}, 
      #{eventTypeId,jdbcType=BIGINT}, #{eventDesc,jdbcType=VARCHAR}, #{eventSource,jdbcType=VARCHAR}, 
      #{eventName,jdbcType=VARCHAR}, #{eventImagePath,jdbcType=VARCHAR}, #{eventFilePath,jdbcType=VARCHAR},
      #{eventNature,jdbcType=VARCHAR}, #{amount,jdbcType=INTEGER}, #{blackList,jdbcType=BIT}, #{miscDesc,jdbcType=VARCHAR},
      #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, 
      #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, 
      #{updateOperName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.credit.model.MmpCreditEventRecord"
          useGeneratedKeys="true" keyProperty="id">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 22 16:26:13 CST 2017.
    -->
    insert into ${issSchema}.mmp_credit_event_record
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="authId != null" >
        auth_id,
      </if>
      <if test="orderSeq != null" >
        order_seq,
      </if>
      <if test="eventTypeId != null" >
        event_type_id,
      </if>
      <if test="eventDesc != null" >
        event_desc,
      </if>
      <if test="eventSource != null" >
        event_source,
      </if>
      <if test="eventName != null" >
        event_name,
      </if>
      <if test="eventImagePath != null" >
        event_image_path,
      </if>
      <if test="eventFilePath != null" >
        event_file_path,
      </if>
      <if test="eventNature != null" >
        event_nature,
      </if>
      <if test="amount != null" >
        amount,
      </if>
      <if test="blackList != null" >
        black_list,
      </if>
      <if test="miscDesc != null" >
        misc_desc,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateOperId != null" >
        update_oper_id,
      </if>
      <if test="updateOperName != null" >
        update_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="authId != null" >
        #{authId,jdbcType=VARCHAR},
      </if>
      <if test="orderSeq != null" >
        #{orderSeq,jdbcType=VARCHAR},
      </if>
      <if test="eventTypeId != null" >
        #{eventTypeId,jdbcType=BIGINT},
      </if>
      <if test="eventDesc != null" >
        #{eventDesc,jdbcType=VARCHAR},
      </if>
      <if test="eventSource != null" >
        #{eventSource,jdbcType=VARCHAR},
      </if>
      <if test="eventName != null" >
        #{eventName,jdbcType=VARCHAR},
      </if>
      <if test="eventImagePath != null" >
        #{eventImagePath,jdbcType=VARCHAR},
      </if>
      <if test="eventFilePath != null" >
        #{eventFilePath,jdbcType=VARCHAR},
      </if>
      <if test="eventNature != null" >
        #{eventNature,jdbcType=VARCHAR},
      </if>
      <if test="amount != null" >
        #{amount,jdbcType=INTEGER},
      </if>
      <if test="blackList != null" >
        #{blackList,jdbcType=BIT},
      </if>
      <if test="miscDesc != null" >
        #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        #{updateOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.credit.model.MmpCreditEventRecord" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 22 16:26:13 CST 2017.
    -->
    update ${issSchema}.mmp_credit_event_record
    <set >
      <if test="authId != null" >
        auth_id = #{authId,jdbcType=VARCHAR},
      </if>
      <if test="orderSeq != null" >
        order_seq = #{orderSeq,jdbcType=VARCHAR},
      </if>
      <if test="eventTypeId != null" >
        event_type_id = #{eventTypeId,jdbcType=BIGINT},
      </if>
      <if test="eventDesc != null" >
        event_desc = #{eventDesc,jdbcType=VARCHAR},
      </if>
      <if test="eventSource != null" >
        event_source = #{eventSource,jdbcType=VARCHAR},
      </if>
      <if test="eventName != null" >
        event_name = #{eventName,jdbcType=VARCHAR},
      </if>
      <if test="eventImagePath != null" >
        event_image_path = #{eventImagePath,jdbcType=VARCHAR},
      </if>
      <if test="eventFilePath != null" >
        event_file_path = #{eventFilePath,jdbcType=VARCHAR},
      </if>
      <if test="eventNature != null" >
        event_nature = #{eventNature,jdbcType=VARCHAR},
      </if>
      <if test="amount != null" >
        amount = #{amount,jdbcType=INTEGER},
      </if>
      <if test="blackList != null" >
        black_list = #{blackList,jdbcType=BIT},
      </if>
      <if test="miscDesc != null" >
        misc_desc = #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.credit.model.MmpCreditEventRecord" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 22 16:26:13 CST 2017.
    -->
    update ${issSchema}.mmp_credit_event_record
    set auth_id = #{authId,jdbcType=VARCHAR},
      order_seq = #{orderSeq,jdbcType=VARCHAR},
      event_type_id = #{eventTypeId,jdbcType=BIGINT},
      event_desc = #{eventDesc,jdbcType=VARCHAR},
      event_source = #{eventSource,jdbcType=VARCHAR},
      event_name = #{eventName,jdbcType=VARCHAR},
      event_image_path = #{eventImagePath,jdbcType=VARCHAR},
      event_file_path  = #{eventFilePath,jdbcType=VARCHAR},
      event_nature = #{eventNature,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=INTEGER},
      black_list = #{blackList,jdbcType=BIT},
      misc_desc = #{miscDesc,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <sql id="Base_AuthCreditEventRecordPageDto_Column_List">
    er.id as eventId , er.auth_id as authId, er.event_type_id as eventTypeId, er.event_source as eventSource,  er.event_name as eventName,
    er.black_list as blackList, er.status as eventStatus, er.create_time as createTime, er.create_oper_id as createOperId,
    er.create_oper_name as createOperName, IFNULL(ar.status,0) as appealStatus
  </sql>

  <select id="getCreditEventCountByAuthId" resultType="java.lang.Integer">
    select COUNT(1)
    from ${issSchema}.mmp_credit_event_record
    where auth_id = #{authId,jdbcType=VARCHAR} AND event_nature=0
  </select>

  <select id="getCreditEventPagesByAuthId" resultType="com.extracme.evcard.membership.credit.dto.AuthCreditEventRecordPageDto" >
    select
    <include refid="Base_AuthCreditEventRecordPageDto_Column_List" />
    from ${issSchema}.mmp_credit_event_record er
    LEFT JOIN ${issSchema}.mmp_credit_event_appeal_record ar on er.id=ar.event_id
    where er.auth_id = #{authId,jdbcType=VARCHAR} AND er.event_nature=0
    ORDER BY ar.status desc , er.create_time DESC
    limit  #{page.offSet} , #{page.limitSet}
  </select>

  <sql id="Base_CreditEventRecordDetailDto_Column_List">
    er.id as eventId, er.auth_id as authId , mi.`NAME` as authName , mi.MOBILE_PHONE as mobilePhone ,
    i.org_id as orgId, i.ORG_NAME as orgName, er.event_type_id as eventTypeId, er.event_desc as eventDesc, er.event_source as eventSource,
    er.event_name as eventName, er.event_image_path as eventImagePath, er.event_file_path as eventFilePath,
    er.event_nature as eventNature, er.amount as amount, er.black_list as blackList, er.status as eventStatus,
    er.create_time as eventCreateTime, er.create_oper_id as eventCreateOperId, er.create_oper_name as eventCreateOperName
  </sql>

  <select id="getCreditEventDetailByAuthIdAndEventId" resultType="com.extracme.evcard.membership.credit.dto.CreditEventRecordDetailDto">
    SELECT
    <include refid="Base_CreditEventRecordDetailDto_Column_List"/>
    FROM ${issSchema}.mmp_credit_event_record er
    LEFT JOIN ${siacSchema}.membership_info mi on er.auth_id=mi.AUTH_ID AND mi.MEMBERSHIP_TYPE=0
    LEFT JOIN ${siacSchema}.city b ON mi.city_of_origin=b.CITY
    LEFT JOIN ${isvSchema}.org_info i on i.org_id=b.org_id
    WHERE er.auth_id= #{authId,jdbcType=VARCHAR} AND er.id = #{eventId,jdbcType=BIGINT}
  </select>

  <sql id="Base_CreditEventRecordPageDto_Column_List">
    er.id as eventId, er.auth_id as authId, mi.`NAME` as authName , mi.MOBILE_PHONE as mobilePhone ,
    i.org_id as orgId, i.ORG_NAME as orgName, er.event_name as eventName, er.event_nature as eventNature,  er.status as eventStatus,
    er.create_time as createTime, er.create_oper_id as createOperId, er.create_oper_name as createOperName
  </sql>

  <select id="getCreditEventRecordCount" resultType="java.lang.Integer">
     select COUNT(1)
     FROM ${issSchema}.mmp_credit_event_record er
      LEFT JOIN ${siacSchema}.membership_info mi on er.auth_id=mi.AUTH_ID AND mi.MEMBERSHIP_TYPE=0
     LEFT JOIN ${siacSchema}.city b ON mi.city_of_origin=b.CITY
     LEFT JOIN ${isvSchema}.org_info i on i.org_id=b.org_id
    <where>
        <if test=" eventId !=null ">
          AND er.id= #{eventId,jdbcType=BIGINT}
        </if>
        <if test=" mobilePhone !='' and mobilePhone != null">
          AND mi.MOBILE_PHONE= #{mobilePhone,jdbcType=VARCHAR}
        </if>
        <if test=" authName !='' and authName != null">
          AND mi.`NAME`= #{authName,jdbcType=VARCHAR}
        </if>
        <if test=" orgId !='' and orgId != null">
          <if test=" orgId != '00' ">
            AND ifnull(i.org_id,'') = #{orgId,jdbcType=VARCHAR}
          </if>
        </if>
        <if test=" eventName !='' and eventName != null ">
          AND er.event_name= #{eventName,jdbcType=VARCHAR}
        </if>
        <if test=" eventNature !='' and eventNature != null ">
          AND er.event_nature= #{eventNature,jdbcType=VARCHAR}
        </if>
        <if test=" eventStatus != null ">
          AND er.status= #{eventStatus,jdbcType=INTEGER}
        </if>
        <if test=" eventStartTime !='' and  eventStartTime != null ">
          <![CDATA[ AND er.create_time >= #{eventStartTime} ]]>
        </if>
        <if test=" eventEndTime !='' and  eventEndTime != null ">
          <![CDATA[ AND er.create_time < #{eventEndTime}  ]]>
        </if>
    </where>
  </select>

  <select id="getCreditEventRecordPages" resultType="com.extracme.evcard.membership.credit.dto.CreditEventRecordPageDto">
    select
    <include refid="Base_CreditEventRecordPageDto_Column_List"/>
    FROM ${issSchema}.mmp_credit_event_record er
    LEFT JOIN ${siacSchema}.membership_info mi on er.auth_id=mi.AUTH_ID AND mi.MEMBERSHIP_TYPE=0
    LEFT JOIN ${siacSchema}.city b ON mi.city_of_origin=b.CITY
    LEFT JOIN ${isvSchema}.org_info i on i.org_id=b.org_id
    <where>
      <if test=" paramsDto.eventId !=null ">
        AND er.id= #{paramsDto.eventId,jdbcType=BIGINT}
      </if>
      <if test=" paramsDto.mobilePhone !='' and paramsDto.mobilePhone != null">
        AND mi.MOBILE_PHONE= #{paramsDto.mobilePhone,jdbcType=VARCHAR}
      </if>
      <if test=" paramsDto.authName !='' and paramsDto.authName != null">
        AND mi.`NAME`= #{paramsDto.authName,jdbcType=VARCHAR}
      </if>
      <if test=" paramsDto.orgId !='' and paramsDto.orgId != null">
        <if test=" paramsDto.orgId != '00' ">
          AND ifnull(i.org_id,'') =  #{paramsDto.orgId,jdbcType=VARCHAR}
        </if>
      </if>
      <if test=" paramsDto.eventName !='' and paramsDto.eventName != null ">
        AND er.event_name= #{paramsDto.eventName,jdbcType=VARCHAR}
      </if>
      <if test=" paramsDto.eventNature !='' and paramsDto.eventNature != null ">
        AND er.event_nature= #{paramsDto.eventNature,jdbcType=VARCHAR}
      </if>
      <if test=" paramsDto.eventStatus != null ">
        AND er.status= #{paramsDto.eventStatus,jdbcType=INTEGER}
      </if>
      <if test=" paramsDto.eventStartTime !='' and  paramsDto.eventStartTime != null ">
        <![CDATA[ AND er.create_time >= #{paramsDto.eventStartTime} ]]>
      </if>
      <if test=" paramsDto.eventEndTime !='' and  paramsDto.eventEndTime != null ">
        <![CDATA[ AND er.create_time < #{paramsDto.eventEndTime}  ]]>
      </if>
    </where>
    ORDER BY er.create_time DESC
    limit  #{page.offSet} , #{page.limitSet}
  </select>

  <update id="updateCreditEventRecordStatusByEventIdAndAuthId">
    UPDATE
      ${issSchema}.mmp_credit_event_record
      SET status=0 , update_time = now(), update_oper_id = #{updateOperId}, update_oper_name = #{updateOperName}
      WHERE auth_id = #{authId} AND  id = #{eventId}
  </update>

  <select id="getCreditEventByAuthIdAndEventId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_credit_event_record
    where  status=1 AND auth_id = #{authId} AND  id = #{eventId}
  </select>

  <select id="getThisYearInTimePayTotalAmount" resultType="java.lang.Integer">
    select sum(amount)
    from ${issSchema}.mmp_credit_event_record
    where auth_id = #{authId} AND event_type_id= #{eventTypeId}
    and DATE_FORMAT(create_time,'%Y') = DATE_FORMAT(now(),'%Y')
  </select>

</mapper>