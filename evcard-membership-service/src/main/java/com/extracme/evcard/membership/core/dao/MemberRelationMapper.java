package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.model.MemberRelation;
import com.extracme.evcard.membership.core.model.MemberRelationExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MemberRelationMapper {
    int countByExample(MemberRelationExample example);

    int deleteByPrimaryKey(Long id);

    int insert(MemberRelation record);

    int insertSelective(MemberRelation record);

    List<MemberRelation> selectByExample(MemberRelationExample example);

    MemberRelation selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MemberRelation record, @Param("example") MemberRelationExample example);

    int updateByExample(@Param("record") MemberRelation record, @Param("example") MemberRelationExample example);

    int updateByPrimaryKeySelective(MemberRelation record);

    int updateByPrimaryKey(MemberRelation record);

    int deleteRecordsByPkId1(@Param("pkId1")Long pkid1,@Param("type")int type);

    int batchInsert(List<MemberRelation> list);
}