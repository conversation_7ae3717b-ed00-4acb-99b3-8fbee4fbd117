package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.model.HealthCode;
import org.apache.ibatis.annotations.Param;

public interface HealthCodeMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(HealthCode record);

    int insertSelective(HealthCode record);

    HealthCode selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(HealthCode record);

    int updateByPrimaryKey(HealthCode record);

    HealthCode selectByAuthId(String authId);

    void updateByAuthId(@Param("authId") String authId,@Param("color")Integer color,@Param("status") Short status);
}