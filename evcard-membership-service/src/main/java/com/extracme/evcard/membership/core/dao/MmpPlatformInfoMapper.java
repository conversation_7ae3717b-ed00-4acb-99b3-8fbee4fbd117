package com.extracme.evcard.membership.core.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.extracme.evcard.membership.core.dto.PlatformInfoDTO;
import com.extracme.evcard.membership.core.model.MmpPlatformInfo;

public interface MmpPlatformInfoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_platform_info
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_platform_info
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    int insert(MmpPlatformInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_platform_info
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    int insertSelective(MmpPlatformInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_platform_info
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    MmpPlatformInfo selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_platform_info
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    int updateByPrimaryKeySelective(MmpPlatformInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_platform_info
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    int updateByPrimaryKey(MmpPlatformInfo record);

	List<PlatformInfoDTO> queryAllPlatform();

	List<String> getAppKeysByPlatformId(@Param("platformId") Long platformId, @Param("status") Integer status);

}