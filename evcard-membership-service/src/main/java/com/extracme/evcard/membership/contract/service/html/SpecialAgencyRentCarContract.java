package com.extracme.evcard.membership.contract.service.html;

import com.extracme.evcard.membership.contract.service.AddRentCarContractRequest;
import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.rpc.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class SpecialAgencyRentCarContract extends RentCarContractForm {

    @Override
    public String description() {
        return "特殊企业的租车合同";
    }

    @Override
    public String renderContractHtml(RenderHtmlContext context) throws BusinessException {
        String result = super.renderContractHtml(context);
        AddRentCarContractRequest addRequest = context.getAddRequest();

        String storeManagerMobile = addRequest.getStoreManagerMobile();
        String storeMobile = addRequest.getStoreMobile();

        result = result.replace(BussinessConstants.SOTRE_MANAGER_MOBILE, storeManagerMobile)
                .replace(BussinessConstants.SOTRE_MOBILE, storeMobile);
        return result;
    }

}
