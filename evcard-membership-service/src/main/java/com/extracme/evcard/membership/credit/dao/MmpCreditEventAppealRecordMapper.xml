<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.credit.dao.MmpCreditEventAppealRecordMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.credit.model.MmpCreditEventAppealRecord" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Nov 23 13:31:43 CST 2017.
    -->
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="auth_id" property="authId" jdbcType="VARCHAR" />
    <result column="event_id" property="eventId" jdbcType="BIGINT" />
    <result column="appeal_desc" property="appealDesc" jdbcType="VARCHAR" />
    <result column="appeal_file_path" property="appealFilePath" jdbcType="VARCHAR" />
    <result column="appeal_image_path" property="appealImagePath" jdbcType="VARCHAR" />
    <result column="appeal_time" property="appealTime" jdbcType="TIMESTAMP" />
    <result column="handle_user_id" property="handleUserId" jdbcType="VARCHAR" />
    <result column="handle_user" property="handleUser" jdbcType="VARCHAR" />
    <result column="handle_time" property="handleTime" jdbcType="TIMESTAMP" />
    <result column="handle_remark" property="handleRemark" jdbcType="VARCHAR" />
    <result column="handle_result" property="handleResult" jdbcType="INTEGER" />
    <result column="misc_desc" property="miscDesc" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="VARCHAR" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Nov 23 13:31:43 CST 2017.
    -->
    id, auth_id, event_id, appeal_desc, appeal_file_path, appeal_image_path, appeal_time, 
    handle_user_id, handle_user, handle_time, handle_remark, handle_result, misc_desc, 
    status, create_time, create_oper_id, create_oper_name, update_time, update_oper_id, 
    update_oper_name
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Nov 23 13:31:43 CST 2017.
    -->
    select 
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_credit_event_appeal_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Nov 23 13:31:43 CST 2017.
    -->
    delete from ${issSchema}.mmp_credit_event_appeal_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.membership.credit.model.MmpCreditEventAppealRecord"
                      useGeneratedKeys="true" keyProperty="id">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Nov 23 13:31:43 CST 2017.
    -->
    insert into ${issSchema}.mmp_credit_event_appeal_record (id, auth_id, event_id,
      appeal_desc, appeal_file_path, appeal_image_path, 
      appeal_time, handle_user_id, handle_user, 
      handle_time, handle_remark, handle_result, 
      misc_desc, status, create_time, 
      create_oper_id, create_oper_name, update_time, 
      update_oper_id, update_oper_name)
    values (#{id,jdbcType=BIGINT}, #{authId,jdbcType=VARCHAR}, #{eventId,jdbcType=BIGINT}, 
      #{appealDesc,jdbcType=VARCHAR}, #{appealFilePath,jdbcType=VARCHAR}, #{appealImagePath,jdbcType=VARCHAR}, 
      #{appealTime,jdbcType=TIMESTAMP}, #{handleUserId,jdbcType=VARCHAR}, #{handleUser,jdbcType=VARCHAR}, 
      #{handleTime,jdbcType=TIMESTAMP}, #{handleRemark,jdbcType=VARCHAR}, #{handleResult,jdbcType=INTEGER}, 
      #{miscDesc,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{createOperId,jdbcType=VARCHAR}, #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
      #{updateOperId,jdbcType=BIGINT}, #{updateOperName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.credit.model.MmpCreditEventAppealRecord" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Nov 23 13:31:43 CST 2017.
    -->
    insert into ${issSchema}.mmp_credit_event_appeal_record
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="authId != null" >
        auth_id,
      </if>
      <if test="eventId != null" >
        event_id,
      </if>
      <if test="appealDesc != null" >
        appeal_desc,
      </if>
      <if test="appealFilePath != null" >
        appeal_file_path,
      </if>
      <if test="appealImagePath != null" >
        appeal_image_path,
      </if>
      <if test="appealTime != null" >
        appeal_time,
      </if>
      <if test="handleUserId != null" >
        handle_user_id,
      </if>
      <if test="handleUser != null" >
        handle_user,
      </if>
      <if test="handleTime != null" >
        handle_time,
      </if>
      <if test="handleRemark != null" >
        handle_remark,
      </if>
      <if test="handleResult != null" >
        handle_result,
      </if>
      <if test="miscDesc != null" >
        misc_desc,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateOperId != null" >
        update_oper_id,
      </if>
      <if test="updateOperName != null" >
        update_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="authId != null" >
        #{authId,jdbcType=VARCHAR},
      </if>
      <if test="eventId != null" >
        #{eventId,jdbcType=BIGINT},
      </if>
      <if test="appealDesc != null" >
        #{appealDesc,jdbcType=VARCHAR},
      </if>
      <if test="appealFilePath != null" >
        #{appealFilePath,jdbcType=VARCHAR},
      </if>
      <if test="appealImagePath != null" >
        #{appealImagePath,jdbcType=VARCHAR},
      </if>
      <if test="appealTime != null" >
        #{appealTime,jdbcType=TIMESTAMP},
      </if>
      <if test="handleUserId != null" >
        #{handleUserId,jdbcType=VARCHAR},
      </if>
      <if test="handleUser != null" >
        #{handleUser,jdbcType=VARCHAR},
      </if>
      <if test="handleTime != null" >
        #{handleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="handleRemark != null" >
        #{handleRemark,jdbcType=VARCHAR},
      </if>
      <if test="handleResult != null" >
        #{handleResult,jdbcType=INTEGER},
      </if>
      <if test="miscDesc != null" >
        #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=VARCHAR},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        #{updateOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.credit.model.MmpCreditEventAppealRecord" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Nov 23 13:31:43 CST 2017.
    -->
    update ${issSchema}.mmp_credit_event_appeal_record
    <set >
      <if test="authId != null" >
        auth_id = #{authId,jdbcType=VARCHAR},
      </if>
      <if test="eventId != null" >
        event_id = #{eventId,jdbcType=BIGINT},
      </if>
      <if test="appealDesc != null" >
        appeal_desc = #{appealDesc,jdbcType=VARCHAR},
      </if>
      <if test="appealFilePath != null" >
        appeal_file_path = #{appealFilePath,jdbcType=VARCHAR},
      </if>
      <if test="appealImagePath != null" >
        appeal_image_path = #{appealImagePath,jdbcType=VARCHAR},
      </if>
      <if test="appealTime != null" >
        appeal_time = #{appealTime,jdbcType=TIMESTAMP},
      </if>
      <if test="handleUserId != null" >
        handle_user_id = #{handleUserId,jdbcType=VARCHAR},
      </if>
      <if test="handleUser != null" >
        handle_user = #{handleUser,jdbcType=VARCHAR},
      </if>
      <if test="handleTime != null" >
        handle_time = #{handleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="handleRemark != null" >
        handle_remark = #{handleRemark,jdbcType=VARCHAR},
      </if>
      <if test="handleResult != null" >
        handle_result = #{handleResult,jdbcType=INTEGER},
      </if>
      <if test="miscDesc != null" >
        misc_desc = #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=VARCHAR},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.credit.model.MmpCreditEventAppealRecord" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Nov 23 13:31:43 CST 2017.
    -->
    update ${issSchema}.mmp_credit_event_appeal_record
    set auth_id = #{authId,jdbcType=VARCHAR},
      event_id = #{eventId,jdbcType=BIGINT},
      appeal_desc = #{appealDesc,jdbcType=VARCHAR},
      appeal_file_path = #{appealFilePath,jdbcType=VARCHAR},
      appeal_image_path = #{appealImagePath,jdbcType=VARCHAR},
      appeal_time = #{appealTime,jdbcType=TIMESTAMP},
      handle_user_id = #{handleUserId,jdbcType=VARCHAR},
      handle_user = #{handleUser,jdbcType=VARCHAR},
      handle_time = #{handleTime,jdbcType=TIMESTAMP},
      handle_remark = #{handleRemark,jdbcType=VARCHAR},
      handle_result = #{handleResult,jdbcType=INTEGER},
      misc_desc = #{miscDesc,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=VARCHAR},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <sql id="Base_CreditEventAppealRecordDetailDto_Column_List">
    id as appealId, auth_id as authId, event_id as eventId, appeal_desc as appealDesc, appeal_file_path as appealFilePath,
    appeal_image_path as appealImagePath, appeal_time as appealTime, handle_user_id as handleUserId,
    handle_user as handleUser, handle_time as handleTime, handle_remark as handleRemark, handle_result as handleResult,
    status as appealStatus
  </sql>

  <sql id="Base_CreditEventAppealRecordPageDto_Column_List">
    ar.id as appealId, ar.auth_id as authId, ar.event_id as eventId, ar.appeal_time as appealTime, ar.status as appealStatus,
    ar.handle_result as handleResult, mi.`NAME` as authName,mi.MOBILE_PHONE as mobilePhone,er.event_name as eventName
  </sql>

  <select id="getCreditEventAppealDetailRecordByAuthIdAndEventId" resultType="com.extracme.evcard.membership.credit.dto.CreditEventAppealRecordDetailDto">
    select
    <include refid="Base_CreditEventAppealRecordDetailDto_Column_List" />
    from ${issSchema}.mmp_credit_event_appeal_record
    where auth_id = #{authId,jdbcType=VARCHAR} AND event_id = #{eventId,jdbcType=BIGINT}
  </select>

  <select id="getCreditEventAppealRecordCount" resultType="java.lang.Integer">
    SELECT COUNT(1)
    FROM ${issSchema}.mmp_credit_event_appeal_record ar
    LEFT JOIN ${issSchema}.mmp_credit_event_record er on ar.event_id=er.id
    LEFT JOIN ${siacSchema}.membership_info mi  on ar.auth_id=mi.AUTH_ID  AND mi.MEMBERSHIP_TYPE=0
    <where>
        <if test=" mobilePhone !='' and mobilePhone != null">
          AND mi.MOBILE_PHONE= #{mobilePhone,jdbcType=VARCHAR}
        </if>
        <if test=" eventName !='' and eventName != null ">
          AND er.event_name like CONCAT('%', #{eventName}, '%')
        </if>
        <if test=" handleResult != null and handleResult == 1 ">
          AND ar.handle_result != 0
        </if>
        <if test=" handleResult != null and handleResult == 0 ">
          AND ar.handle_result= 0
        </if>
        <if test=" appealId != null ">
          AND ar.id = #{appealId,jdbcType=BIGINT}
        </if>
        <if test=" appealStartTime !='' and appealStartTime != null ">
          <![CDATA[ AND ar.appeal_time >= #{appealStartTime} ]]>
        </if>
        <if test=" appealEndTime !='' and appealEndTime != null ">
          <![CDATA[ AND ar.appeal_time < #{appealEndTime} ]]>
        </if>
    </where>
  </select>

  <select id="getCreditEventAppealRecordPages" resultType="com.extracme.evcard.membership.credit.dto.CreditEventAppealRecordPageDto">
    SELECT
    <include refid="Base_CreditEventAppealRecordPageDto_Column_List"/>
    FROM ${issSchema}.mmp_credit_event_appeal_record ar
    LEFT JOIN ${issSchema}.mmp_credit_event_record er on ar.event_id=er.id
    LEFT JOIN ${siacSchema}.membership_info mi  on ar.auth_id=mi.AUTH_ID  AND mi.MEMBERSHIP_TYPE=0
    <where>
      <if test=" paramsDto.mobilePhone !='' and paramsDto.mobilePhone != null">
        AND mi.MOBILE_PHONE= #{paramsDto.mobilePhone,jdbcType=VARCHAR}
      </if>
      <if test=" paramsDto.eventName !='' and paramsDto.eventName != null ">
        AND er.event_name like CONCAT('%', #{paramsDto.eventName}, '%')
      </if>
      <if test=" paramsDto.handleResult != null and paramsDto.handleResult == 1 ">
        AND ar.handle_result != 0
      </if>
      <if test=" paramsDto.handleResult != null and paramsDto.handleResult == 0 ">
        AND ar.handle_result= 0
      </if>
      <if test=" paramsDto.appealId != null ">
        AND ar.id = #{paramsDto.appealId,jdbcType=BIGINT}
      </if>
      <if test=" paramsDto.appealStartTime !='' and paramsDto.appealStartTime != null ">
        <![CDATA[ AND ar.appeal_time >= #{paramsDto.appealStartTime} ]]>
      </if>
      <if test=" paramsDto.appealEndTime !='' and paramsDto.appealEndTime != null ">
        <![CDATA[ AND ar.appeal_time < #{paramsDto.appealEndTime} ]]>
      </if>
    </where>
    ORDER BY ar.appeal_time DESC
    limit  #{page.offSet} , #{page.limitSet}
  </select>

  <select id="getCreditEventAppealRecordById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_credit_event_appeal_record
    where status=1 AND handle_result = 0
    AND id = #{appealId,jdbcType=BIGINT}
    AND auth_id = #{authId,jdbcType=VARCHAR}
  </select>

  <update id="updateAppealEventHandleStatus">
    UPDATE ${issSchema}.mmp_credit_event_appeal_record
    SET handle_result=#{handleResult} , handle_remark= #{handleRemark} , handle_time= now() ,
        handle_user_id=#{handleUserId}, handle_user=#{handleUser},
        update_oper_id = #{updateOperId}, update_oper_name = #{updateOperName}
    where status=1 AND handle_result = 0
    AND id = #{appealId,jdbcType=BIGINT}
    AND auth_id = #{authId,jdbcType=VARCHAR}
  </update>

  <select id="getCreditEventAppealRecordByAuthIdAndEventId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_credit_event_appeal_record
    where  status=1 AND handle_result = 0
    AND auth_id = #{authId,jdbcType=VARCHAR}
    AND event_id = #{eventId,jdbcType=BIGINT}
  </select>

  <insert id="saveNewCreditEventAppealRecord" parameterType="com.extracme.evcard.membership.credit.model.MmpCreditEventAppealRecord"
          useGeneratedKeys="true" keyProperty="id">
    insert into ${issSchema}.mmp_credit_event_appeal_record (id, auth_id, event_id,
    appeal_desc, appeal_file_path, appeal_image_path,
    appeal_time, status, create_time,
    create_oper_id, create_oper_name)
    values (#{id,jdbcType=BIGINT}, #{authId,jdbcType=VARCHAR}, #{eventId,jdbcType=BIGINT},
    #{appealDesc,jdbcType=VARCHAR}, #{appealFilePath,jdbcType=VARCHAR}, #{appealImagePath,jdbcType=VARCHAR},
    now(), 1, now(), #{createOperId,jdbcType=VARCHAR}, #{createOperName,jdbcType=VARCHAR})
  </insert>
</mapper>