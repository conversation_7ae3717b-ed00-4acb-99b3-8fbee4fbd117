package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.dto.agency.DiscountRuleDTO;
import com.extracme.evcard.membership.core.model.MmpDiscountRule;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MmpDiscountRuleMapper {

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_discount_rule
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    MmpDiscountRule selectByPrimaryKey(Long id);
    /**
     * 查询机构
     * @param agencyId
     * @param discountType
     * @return
     */
    List<DiscountRuleDTO> findByAgencyId(@Param("agencyId") String agencyId, @Param("discountType") Integer discountType);

}
