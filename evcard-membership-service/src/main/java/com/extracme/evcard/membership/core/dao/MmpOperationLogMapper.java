package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.model.AgencyInfo;
import com.extracme.evcard.membership.core.model.MmpOperationLog;
import com.extracme.evcard.rpc.dto.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MmpOperationLogMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MmpOperationLog record);

    int insertSelective(MmpOperationLog record);

    MmpOperationLog selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MmpOperationLog record);

    int updateByPrimaryKey(MmpOperationLog record);

    /**
     * 统计机构日志总数
     * @param agencyId
     * @return
     */
    int countAgencyOperationLogNum( @Param("agencyId") String agencyId);

    /**
     * 根据条件查询机构日志列表分页
     * @param agencyId
     * @param page
     * @return
     */
    List<MmpOperationLog> queryAgencyOperationLogPage( @Param("agencyId") String agencyId,@Param("page") Page page);

}