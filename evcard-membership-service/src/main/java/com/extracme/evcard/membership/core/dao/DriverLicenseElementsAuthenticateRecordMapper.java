package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.model.DriverLicenseElementsAuthenticateRecord;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface DriverLicenseElementsAuthenticateRecordMapper {

    /**
     * 新增
     * @param record
     * @return
     */
    int insert(DriverLicenseElementsAuthenticateRecord record);

    /**
     * 新增
     * @param record
     * @return
     */
    int insertSelective(DriverLicenseElementsAuthenticateRecord record);

    /**
     * 根据id查询
     * @param id
     * @return
     */
    DriverLicenseElementsAuthenticateRecord selectByPrimaryKey(Long id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(DriverLicenseElementsAuthenticateRecord record);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKey(DriverLicenseElementsAuthenticateRecord record);

    /**
     * 查询用户待审核记录
     * @param userId
     * @return
     */
    DriverLicenseElementsAuthenticateRecord selectUserWaitReviewInfo(Long userId);

    /**
     * 查询用户最新的驾照三要素认证结果
     * @param userId
     * @return
     */
    DriverLicenseElementsAuthenticateRecord getLastestRecordByUser(Long userId);

    /**
     * 查询用户认证通过的最新的驾照三要素认证结果
     * @param userId
     * @return
     */
    DriverLicenseElementsAuthenticateRecord getLastAuthenticateRecordByUser(Long userId);

    /**
     * 获取用户最近的认证结果
     * @param userId
     * @param limit
     * @return
     */
    List<DriverLicenseElementsAuthenticateRecord> getLastLicAuthRecordsByUser(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 获取用户最近的认证结果
     * @param startTime
     * @param endTime
     * @return
     */
    Set<Long> getAuthIngUsers(@Param("startTime") Date startTime, @Param("endTime") Date endTime);
}