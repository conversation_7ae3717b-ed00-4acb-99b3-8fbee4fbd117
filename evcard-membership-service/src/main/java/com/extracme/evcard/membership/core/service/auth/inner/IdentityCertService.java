package com.extracme.evcard.membership.core.service.auth.inner;


import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.OnExceptionContext;
import com.aliyun.openservices.ons.api.SendCallback;
import com.aliyun.openservices.ons.api.SendResult;
import com.aliyun.openservices.ons.api.bean.ProducerBean;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.dao.ApplyProgressMapper;
import com.extracme.evcard.membership.core.dao.MemberIdentityDocumentMapper;
import com.extracme.evcard.membership.core.dao.UserOperatorLogMapper;
import com.extracme.evcard.membership.core.dto.*;
import com.extracme.evcard.membership.core.dto.input.MemberReAuditInput;
import com.extracme.evcard.membership.core.enums.*;
import com.extracme.evcard.membership.core.exception.AuthenticationException;
import com.extracme.evcard.membership.core.input.SubmitIdCardInput;
import com.extracme.evcard.membership.core.input.UserOperationLogInput;
import com.extracme.evcard.membership.core.manager.MemberDocumentManager;
import com.extracme.evcard.membership.core.model.ApplyProgress;
import com.extracme.evcard.membership.core.model.MemberIdentityDocument;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.membership.core.service.MemberOptLogService;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.model.MembershipBaseInfo;
import com.extracme.evcard.membership.credit.model.MembershipInfo;
import com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs;
import com.extracme.evcard.membership.invitation.service.IMemberShipInvitationServ;
import com.extracme.evcard.mq.bean.EventEnum;
import com.extracme.evcard.mq.bean.event.MemberAudit;
import com.extracme.evcard.mq.bean.event.MemberToAudit;
import com.extracme.evcard.mq.bean.event.NewMemberAudit;
import com.extracme.evcard.protobuf.ProtobufUtil;
import com.extracme.evcard.redis.JedisUtil;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.entity.HidLog;
import com.extracme.evcard.rpc.entity.LogPoint;
import com.extracme.evcard.rpc.enums.StatusCode;
import com.extracme.evcard.rpc.exception.BusinessRuntimeException;
import com.extracme.evcard.rpc.messagepush.service.ISensorsdataService;
import com.extracme.evcard.rpc.util.BeanCopyUtils;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Component
public class IdentityCertService {

    @Resource
    private CertSubmitChecker certSubmitChecker;

    @Resource
    private MemberDocumentManager memberDocumentManager;

    @Resource
    private MembershipInfoMapper membershipInfoMapper;

    @Resource
    private ApplyProgressMapper applyProgressMapper;

    @Autowired
    IMemberShipService memberShipService;

    @Resource
    MemberOptLogService memberOptLogService;

    @Resource(name = "producer")
    protected ProducerBean producer;
    @Value("${ons.raw.topic}")
    protected String evcardRawDataTopic;
    @Autowired
    private IMemberShipInvitationServ memberShipInvitationServ;

    @Resource
    private ISensorsdataService sensorsdataService;

    @Value("${env_sub}")
    private String envSub;
    @Resource
    private MemberIdentityDocumentMapper memberIdentityDocumentMapper;

    @Resource
    private LicenseCertService licenseCertService;
    @Resource
    private UserOperatorLogMapper userOperatorLogMapper;

    @Transactional(rollbackFor = Exception.class)
    public DefaultServiceRespDTO reAuditMember(MemberReAuditInput input,boolean clearLicense){
        String mid = input.getMid();
        String operatorId = input.getOperatorId();
        String operatorName = input.getOperatorName();
        String operateSource = input.getOperateSource();
        Date now = new Date();
        // 查询membershipInfo
        MembershipBasicInfo membershipInfo = membershipInfoMapper.getUserBasicInfoByMid(mid);
        if (membershipInfo == null) {
            return new DefaultServiceRespDTO(-1, "未查询到用户");
        }

        Long identityId = membershipInfo.getIdentityId();
        MemberIdentityDocument memberIdentityDocument = memberIdentityDocumentMapper.selectByPrimaryKey(identityId);
        if (memberIdentityDocument == null) {
            return new DefaultServiceRespDTO(-1, "未查询到用户");
        }

        // 未认证 不能重新认证
        if (IdentityAuthStatusEnum.unAuthed(memberIdentityDocument.getAuthenticationStatus())) {
            return new DefaultServiceRespDTO(-1, "用户身份认证状态不允许重新认证");
        }

        //membershipInfoMapper 更新
        MembershipInfoWithBLOBs updateMember = new MembershipInfoWithBLOBs();
        //主键
        updateMember.setPkId(membershipInfo.getPkId());
        //更新时间
        updateMember.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
        //更新操作人
        updateMember.setUpdatedUser(operatorName);
        //身份认证状态 0待认证(未认证) 1待认证(未刷脸上传) 2认证通过 3认证不通过
        updateMember.setAuthenticationStatus(0);
        updateMember.setReviewUser(operatorName);
        // 这里给 ReviewItems 设置为 0000000
        updateMember.setReviewItems(BussinessConstants.IDCARD_REVIEW_ITEMS_INIT);
        updateMember.setPassportNo(StringUtils.EMPTY);
        updateMember.setIdCardNumber(StringUtils.EMPTY);
        updateMember.setName(StringUtils.EMPTY);
        updateMember.setIdType(1);
        updateMember.setIdcardPicUrl(StringUtils.EMPTY);
        updateMember.setFaceRecognitionImgUrl(StringUtils.EMPTY);
        membershipInfoMapper.clearIdentityByPrimaryKeySelective(updateMember);


        MemberIdentityDocument updateMemberIdentityDocument = new MemberIdentityDocument();
        updateMemberIdentityDocument.setId(memberIdentityDocument.getId());
        //认证状态
        updateMemberIdentityDocument.setAuthenticationStatus(IdentityAuthStatusEnum.UN_SUBMIT.getValue());
        //更新记录人的 id
        updateMemberIdentityDocument.setUpdateOperId(Long.valueOf(operatorId));
        updateMemberIdentityDocument.setUpdateOperName(operatorName);
        //自动审核
        updateMemberIdentityDocument.setReviewMode(1);
        //审核人
        updateMemberIdentityDocument.setReviewUser(operatorName);
        //审核时间
        updateMemberIdentityDocument.setReviewTime(now);
        //审核项审核结果
        updateMemberIdentityDocument.setReviewItems(BussinessConstants.IDCARD_REVIEW_ITEMS_INIT);
        //重新审核 或者 已认证 不通过原因相关字段 置空
        updateMemberIdentityDocument.setReviewIds("");
        updateMemberIdentityDocument.setReviewRemark("");
        updateMemberIdentityDocument.setReviewItemNames("");
        //更新时间
        updateMemberIdentityDocument.setUpdateTime(now);

        // 用户信息清除
        updateMemberIdentityDocument.setIdentityNo(StringUtils.EMPTY);
        updateMemberIdentityDocument.setIdentityType(1);
        updateMemberIdentityDocument.setName(StringUtils.EMPTY);
        updateMemberIdentityDocument.setExpireType(1);
        updateMemberIdentityDocument.setExpirationDate(StringUtils.EMPTY);
        updateMemberIdentityDocument.setIdentityCardImgUrl(StringUtils.EMPTY);
        updateMemberIdentityDocument.setReverseIdentityCardImgUrl(StringUtils.EMPTY);
        updateMemberIdentityDocument.setCertInputType(2);
        updateMemberIdentityDocument.setFaceRecognitionImgUrl(StringUtils.EMPTY);
        memberIdentityDocumentMapper.updateByPrimaryKeySelective(updateMemberIdentityDocument);

        // 记录操作日志
        ComUtil.insertOperatorLog(MemOperateTypeEnum.IDCARD_MEMBER_REAUDIT.getOperate(), membershipInfo.getAuthId(),
                membershipInfo.getMobilePhone(), operatorName, userOperatorLogMapper);

        // 操作日志
        UserOperationLogInput operationLog = new UserOperationLogInput();
        operationLog.setUserId(membershipInfo.getPkId());
        operationLog.setAuthId(membershipInfo.getAuthId());
        operationLog.setMembershipType((int)membershipInfo.getMembershipType());
        operationLog.setOperationType(MemOperateTypeEnum.IDCARD_MEMBER_REAUDIT.getCode());
        operationLog.setOperationContent("身份证件重新认证");
        operationLog.setRefKey1(operateSource);
        operationLog.setOperatorId(Long.valueOf(operatorId));
        operationLog.setOperator(operatorName);
        operationLog.setOperationTime(now);
        memberShipService.saveUserOperationLog(operationLog);

        // 清除驾照信息
        if (clearLicense) {
            // 驾照信息只有特定状态，才需要清除
            licenseCertService.reAuditMember(input,false);
        }
        return DefaultServiceRespDTO.SUCCESS;
    }


    @Transactional(rollbackFor = Exception.class)
    public SubmitIdCardRespDto submitUserIdCert(SubmitIdCardInput input) throws AuthenticationException {
        if(StringUtils.isBlank(input.getIdCardNumber())
                || StringUtils.isBlank(input.getIdcardPicUrl())
                || null == input.getCertInputType()
                || null == input.getIdType()) {
            throw BusinessRuntimeException.PARAM_EXEPTION;
        }
        //图片路径处理
        input.setIdcardPicUrl(ComUtil.splitPicUrl(input.getIdcardPicUrl()));
        input.setIdcardPicBackUrl(ComUtil.splitPicUrl(input.getIdcardPicBackUrl()));
        input.setHoldIdcardPicUrl(ComUtil.splitPicUrl(input.getHoldIdcardPicUrl()));
        input.setFacePicUrl(ComUtil.splitPicUrl(input.getFacePicUrl()));
        input.setIdCardNumber(input.getIdCardNumber().toUpperCase());

        if(IdTypeEnum.isMainlandId(input.getIdType())) {
            return submitMainlandUserIdCert(input);
        }
        return submitUnNativeUserIdCert(input);
    }

    /**
     * 本籍用户提交身份证件
     * @param input
     * @throws AuthenticationException
     */
    @Transactional(rollbackFor = Exception.class)
    public SubmitIdCardRespDto submitMainlandUserIdCert(SubmitIdCardInput input) throws AuthenticationException {
        /**
         * 1. 参数校验
         */
        String idCardNumber = input.getIdCardNumber();
        if(StringUtils.isBlank(input.getName())
                || StringUtils.isBlank(idCardNumber)
                || StringUtils.isBlank(input.getIdcardPicUrl()) || StringUtils.isBlank(input.getIdcardPicBackUrl())
                || !IdTypeEnum.isMainlandId(input.getIdType())
                || null == input.getExpireType()
                || (input.getExpireType().equals(1) && StringUtils.isBlank(input.getExpirationDate()))) {
            throw BusinessRuntimeException.PARAM_EXEPTION;
        }
        // 是否需要占用证件
        boolean isNeedOccupied = StringUtils.equals(BussinessConstants.APPKEY_OFC, input.getAppKey()) && StringUtils.isNotEmpty(input.getOccupiedMid());
        //// 20221208 由于用户流程走不下去，业务要求去掉该验证
      /*  //1.1 频繁提交
        BaseResponse checkTimesResp = certSubmitChecker.checkSubmitTimes("identity", input.getMid());
        if (checkTimesResp != null) {
            log.warn("提交身份证信息，不可频繁提交, mid={}, resp={}", input.getMid(), JSON.toJSONString(checkTimesResp));
            return new SubmitIdCardRespDto(AuthStateCodeEnum.SUBMIT_TIMES_LIMIT, checkTimesResp.getMessage());
        }*/
        //1.2 本籍身份证号不满足格式
        if (!ComUtil.checkIDCard(idCardNumber)) {
            HidLog.membership(LogPoint.MAS_SUBMIT_USER_INFO_NATIVE, StatusCode.SUBMIT_DRIVER_MODE_ERROR.getMsg(), input.getMid(), false);
            return new SubmitIdCardRespDto(AuthStateCodeEnum.INVALID_ID_NO);
        }
        /**
         * 1.3 当前用户状态
         * 用户不存在或已注销
         * 审核通过用户
         * 提交本籍用户驾照    TODO 确认当前提交驾照 与 驾照审核通过用户的国籍不相同时，如何处理？
         */
        MembershipBasicInfo member = membershipInfoMapper.getUserBasicInfoByMid(input.getMid());
        if(member == null || member.getAccountStatus().equals(2)) {
            throw new AuthenticationException(StatusCode.MEMBER_NOT_EXIT);
        }


        MemberIdentityDocument memberIdentity = memberDocumentManager.getMemberIdentity(input.getMid());
        String beforeStatus = "未认证";
        if(memberIdentity != null) {
            beforeStatus = IdentityAuthStatusEnum.getDesByCode(memberIdentity.getAuthenticationStatus());
        }

        //1.5 证件有效期检查
        if(input.getExpireType().equals(1)) {
            AuthStateCodeEnum checkResp = certSubmitChecker.checkExpireDate(input.getExpirationDate());
            if(checkResp != null) {
                memberAuditTrack(false,member.getAuthId(),1,checkResp.getMsg(), beforeStatus,input.getOperationModel());
                return new SubmitIdCardRespDto(checkResp);
            }
        }

        /**
         * app5.7.1
         * 判断 证件号是否 已被其它用户驾照号 认证过（即其它用户用 该证件号 驾照认证过）
         *
         */
        // 虚拟用户存在 证件号被多次使用
        if (member.getMembershipType() != MemberTypeEnum.QINGLU_USER.getValue().shortValue()) {
            List<MembershipBaseInfo> membershipBaseInfoList = membershipInfoMapper.checkDriverCodeOccupied(member.getAuthId(), idCardNumber, 0);
            if (CollectionUtils.isNotEmpty(membershipBaseInfoList) && !isNeedOccupied) {
                log.error("提交身份证信息，证件号已被其它用户驾照认证用过,认证失败 mid={}", input.getMid());
                SubmitIdCardRespDto submitIdCardRespDto = new SubmitIdCardRespDto(AuthStateCodeEnum.IDCARD_HAS_EXIST_ERROR);
                if (StringUtils.equals(BussinessConstants.APPKEY_OFC, input.getAppKey())) {
                    submitIdCardRespDto.setOccupiedMobilePhone(membershipBaseInfoList.get(0).getMobilePhone());
                    submitIdCardRespDto.setOccupiedMid(membershipBaseInfoList.get(0).getMid());
                }
                return submitIdCardRespDto;
            }
        }

        /**
         * 未认证/认证不通过--> 直接提交身份证件
         * 已认证--> 临期更新身份证件 or 过期更新身份证件-->修改
         */
        beforeStatus = "未认证";
        boolean firstSubmit = true;
        if(memberIdentity != null) {
            beforeStatus = IdentityAuthStatusEnum.getDesByCode(memberIdentity.getAuthenticationStatus());
            if(IdentityAuthStatusEnum.TO_MANUAL_REVIEW.getValue().equals(memberIdentity.getAuthenticationStatus())) {
                log.error("提交身份证信息，当前身份证状态为未刷脸/待认证，请勿重复提交, mid={}", input.getMid());
                memberAuditTrack(false,member.getAuthId(),1,StatusCode.SUBMIT_FAILURE.getMsg(),beforeStatus,input.getOperationModel());
                throw new AuthenticationException(StatusCode.SUBMIT_FAILURE);
            }
            else if(IdentityAuthStatusEnum.AUTHENTICATED.getValue().equals(memberIdentity.getAuthenticationStatus())) {
                log.info("提交身份证信息，当前身份证状态为已认证，更新证件有效期, mid={}", input.getMid());
                return updateCertExpiredDate(input, memberIdentity, member);
            }
            else if(IdentityAuthStatusEnum.AUTHENTICATE_FAILED.getValue().equals(memberIdentity.getAuthenticationStatus())) {
                log.info("提交身份证信息，当前身份证状态为认证不通过，重新提交证件, mid={}", input.getMid());
                firstSubmit = false;
                //重新提交场景，不再校验具体不通过项
//                //重新提交证件时，需要比对审核不通过原因  {0，1，3， 4} 证件编号、姓名 、过期日期、身份证正页(护照/通行证)、身份证副页、手持证件照、人脸照片
//                String reviewItems = memberIdentity.getReviewItems();
//                if(!"2".equals(StringUtils.substring(reviewItems, 0, 1)) && StringUtils.isNotBlank(input.getIdCardNumber())
//                        && !input.getIdCardNumber().equalsIgnoreCase(memberIdentity.getIdentityNo())) {
//                    log.error("提交身份证信息，重新提交证件，证件编号不可与初次提交不同, mid={}, reviewItem={}", input.getMid(), reviewItems);
//                    return new SubmitIdCardRespDto(AuthStateCodeEnum.ID_NO_NOT_MATCH);
//                }
//                if(!"2".equals(StringUtils.substring(reviewItems, 1, 2)) && StringUtils.isNotBlank(input.getName())
//                        && !input.getName().equalsIgnoreCase(memberIdentity.getName())) {
//                    log.error("提交身份证信息，重新提交证件，证件编号不可与初次提交不同, mid={}, reviewItem={}", input.getMid(), reviewItems);
//                    return new SubmitIdCardRespDto(AuthStateCodeEnum.NAME_NOT_MATCH);
//                }
            }
            else if(IdentityAuthStatusEnum.TO_FACE_REC.getValue().equals(memberIdentity.getAuthenticationStatus())) {
                log.info("提交身份证信息，当前身份证状态为待刷脸，重新提交证件, mid={}", input.getMid());
                firstSubmit = false;
            }
        }
        //当前驾照认证状态为通过 --> 本籍需要与已认证的驾照号/姓名一致，TODO军官证是否特殊处理。
        boolean licenseAuthPassed = LicenseAuthStatusEnum.AUTHENTICATED.getValue().equals(member.getLicenseReviewStatus());
        if(licenseAuthPassed && !StringUtils.equals(envSub, BussinessConstants.ENV_SUB_STRESS)) {
            if(IdTypeEnum.ID_CARD.getValue().equals(input.getIdType())) {
                if(!StringUtils.equals(member.getDriverCode(), idCardNumber)) {
                    memberAuditTrack(false,member.getAuthId(),1,AuthStateCodeEnum.ID_NO_NOT_MATCH.getMsg(),beforeStatus,input.getOperationModel());
                    log.error("提交身份证件： 驾照审核通过用户，提交的身份证件与驾照号不一致, driverCode={}, identityNo={}, mid={}.",
                            member.getDriverCode(), idCardNumber, input.getMid());
                    return new SubmitIdCardRespDto(AuthStateCodeEnum.ID_NO_NOT_MATCH);
                }
                if(!StringUtils.equals(member.getName(), input.getName())) {
                    memberAuditTrack(false,member.getAuthId(),1,AuthStateCodeEnum.NAME_NOT_MATCH.getMsg(),beforeStatus,input.getOperationModel());
                    log.error("提交身份证件： 驾照审核通过用户，提交的身份证件姓名与驾照姓名不一致, name={}, id.name={}, mid={}.",
                            member.getName(), input.getName(), input.getMid());
                    return new SubmitIdCardRespDto(AuthStateCodeEnum.NAME_NOT_MATCH);
                }
            }
        }

        // 虚拟用户存在 证件号被多次使用
        if (member.getMembershipType() != MemberTypeEnum.QINGLU_USER.getValue().shortValue()) {
            //1.4 身份证账号已被注销冻结、身份证账号已存在
            MemberStatusDto memberStatusDto = memberDocumentManager.selectUserWithSameIdentityNo(idCardNumber, Arrays.asList(1,5), input.getMid());
            if(null != memberStatusDto && !isNeedOccupied) {
                if(!memberStatusDto.getAccountStatus().equals(0)) {
                    memberAuditTrack(false,member.getAuthId(),1,AuthStateCodeEnum.ACCOUNT_FROZEN.getMsg(),beforeStatus,input.getOperationModel());
                    return new SubmitIdCardRespDto(AuthStateCodeEnum.ACCOUNT_FROZEN);
                }
                memberAuditTrack(false,member.getAuthId(),1,AuthStateCodeEnum.SAME_ID_NO.getMsg(), beforeStatus,input.getOperationModel());
                SubmitIdCardRespDto submitIdCardRespDto = new SubmitIdCardRespDto(AuthStateCodeEnum.SAME_ID_NO);
                if (StringUtils.equals(BussinessConstants.APPKEY_OFC, input.getAppKey())) {
                    submitIdCardRespDto.setOccupiedMobilePhone(memberStatusDto.getMobilePhone());
                    submitIdCardRespDto.setOccupiedMid(memberStatusDto.getMid());
                }
                return submitIdCardRespDto;
            }
        }else{
            // 虚拟用户提交证件时，需要校验手机号、证件号、二级渠道号，是否已存在
            try {
                List<MembershipBasicInfo> memberList = membershipInfoMapper.getMemberList(member.getMobilePhone(), member.getSecondAppKey(),idCardNumber, 2);
                if (CollectionUtils.isNotEmpty(memberList)) {
                    if (memberList.size() == 1) {
                        MembershipBasicInfo membershipBasicInfo = memberList.get(0);
                        String existedMid = membershipBasicInfo.getMid();
                        String mid = input.getMid();
                        if (mid.equals(existedMid)){
                            log.info("虚拟用户提交的信息就是本人");
                        }else if (!isNeedOccupied){
                            log.error("虚拟用户提交身份证信息，手机号、证件号、二级渠道已被其它用户驾照认证用过,一个用户，认证失败 mid={},membershipBasicInfo={}", input.getMid(),JSON.toJSONString(membershipBasicInfo));
                            SubmitIdCardRespDto submitIdCardRespDto = new SubmitIdCardRespDto(AuthStateCodeEnum.SAME_ID_NO);
                            if (StringUtils.equals(BussinessConstants.APPKEY_OFC, input.getAppKey())) {
                                if (StringUtils.equals(member.getMobilePhone(), memberList.get(0).getMobilePhone())) {
                                    return new SubmitIdCardRespDto(AuthStateCodeEnum.SAME_MOBILE_AND_ID);
                                }
                                else {
                                    submitIdCardRespDto.setOccupiedMobilePhone(memberList.get(0).getMobilePhone());
                                    submitIdCardRespDto.setOccupiedMid(memberList.get(0).getMid());
                                }
                            }
                            return submitIdCardRespDto;
                        }
                    }else{
                        log.error("虚拟用户提交身份证信息，手机号、证件号、二级渠道已被其它用户驾照认证用过,认证失败,查出多个用户， mid={}，memberList={}", input.getMid(),JSON.toJSONString(memberList));
                        return new SubmitIdCardRespDto(AuthStateCodeEnum.SAME_ID_NO);
                    }
                }
            } catch (Exception e) {
                log.error("虚拟用户提交身份证信息时，校验唯一性，异常,input={}",JSON.toJSONString(input),e);
            }
        }
        /**
         * 2. 提交证件信息
         */
        submitForReview(input, memberIdentity, member, firstSubmit);
        return new SubmitIdCardRespDto(AuthStateCodeEnum.SUCCESS);
    }

    @Transactional(rollbackFor = Exception.class)
    public SubmitIdCardRespDto updateCertExpiredDate(SubmitIdCardInput input, MemberIdentityDocument memberIdentity,
                                                         MembershipBasicInfo member) {
        if(StringUtils.isBlank(input.getIdcardPicUrl()) || StringUtils.isBlank(input.getIdcardPicBackUrl())
                || !IdTypeEnum.isMainlandId(input.getIdType())
                || null == input.getExpireType()
                || (input.getExpireType().equals(1) && StringUtils.isBlank(input.getExpirationDate()))) {
            throw BusinessRuntimeException.PARAM_EXEPTION;
        }
        /**
         * 1.判断有效期是否在原有效期时间之后
         */
        if (input.getExpireType().equals(1) &&
                StringUtils.isNotBlank(memberIdentity.getExpirationDate())) {
            DateTimeFormatter dfm = DateTimeFormatter.ofPattern(ComUtil.DATE_TYPE5);
            LocalDate firstExpirationTime = LocalDate.parse(memberIdentity.getExpirationDate(), dfm);
            LocalDate expirationDate = LocalDate.parse(input.getExpirationDate(), dfm);
            if (!expirationDate.isAfter(firstExpirationTime)) {
                HidLog.membership(LogPoint.UPDATE_ID_CARD_EXPIRED_TIME, StatusCode.LICENSE_EXPIRATION_TIME_ERROR.getMsg(), input.getMid(), false);
                memberAuditTrack(false,member.getAuthId(),1,AuthStateCodeEnum.EARLY_EXPIRE_DATE.getMsg(),"已认证",input.getOperationModel());
                return new SubmitIdCardRespDto(AuthStateCodeEnum.EARLY_EXPIRE_DATE);
            }
        }
        if(StringUtils.isNotBlank(input.getIdCardNumber())) {
            if(!StringUtils.equals(memberIdentity.getIdentityNo(), input.getIdCardNumber())) {
                log.error("更新身份证件：身份证编号与原认证不一致，mid={}, identityNo={}, oriIdNo={}.", input.getMid(), memberIdentity.getIdentityNo(),
                        input.getIdCardNumber());
                memberAuditTrack(false,member.getAuthId(),1,AuthStateCodeEnum.ID_NO_NOT_MATCH.getMsg(),"已认证",input.getOperationModel());
                return new SubmitIdCardRespDto(AuthStateCodeEnum.ID_NO_NOT_MATCH);
            }
        }
        if(StringUtils.isNotBlank(input.getName())) {
            if(!StringUtils.equals(memberIdentity.getName(), input.getName())) {
                log.error("更新身份证件：姓名与原认证不一致，mid={}, name={}, ori.name={}.", input.getMid(), memberIdentity.getName(), input.getName());
                memberAuditTrack(false,member.getAuthId(),1,AuthStateCodeEnum.NAME_NOT_MATCH.getMsg(),"已认证",input.getOperationModel());
                return new SubmitIdCardRespDto(AuthStateCodeEnum.NAME_NOT_MATCH);
            }
        }

        /**
         * 2. 数据提交及状态变更
         */
        String content = "更新身份证有效期成功，已认证";
        Integer authenticationStatus = null;
        OperatorDto operator = OperatorDto.buildUserOperator(StringUtils.abbreviate("USER@" + input.getAppKey(), 20));
        PostCertSubmitEnum nextOpType = PostCertSubmitEnum.AUTO_AUTH_SUCEESS;

        if (StringUtils.equals(BussinessConstants.APPKEY_OFC, input.getAppKey())) {
            log.info("履约更新身份证件：更新证件有效期，mid={}, expireDate: {}->{}.", input.getMid(), memberIdentity.getExpirationDate(),
                    input.getExpirationDate());
        } else {
            //2.1 未经过修改, 则保持认证通过状态，仅更新
            if (input.getCertInputType().equals(1)) {
                log.info("更新身份证件：更新证件有效期，mid={}, expireDate: {}->{}.", input.getMid(), memberIdentity.getExpirationDate(),
                        input.getExpirationDate());
            } else { //2.2 经过修改则变更状态： 由认证通过-->待认证
                content = "更新身份证有效期完成，提交人工审核";
                authenticationStatus = IdentityAuthStatusEnum.TO_MANUAL_REVIEW.getValue();
            }
        }

        HidLog.membership(LogPoint.UPDATE_ID_CARD_EXPIRED_TIME, content, input.getMid(), true);
        memberDocumentManager.updateIdCardExpireDate(memberIdentity.getId(), input, authenticationStatus, operator);

        /**
         * 3. 保存操作日志
         */
        memberOptLogService.saveCertOptLog(content, member, MemOperateTypeEnum.IDCARD_SUBMIT_DATA,
                input.getAppKey(), null, true, operator);

        memberAuditTrack(true,member.getAuthId(),1,"成功","已认证",input.getOperationModel());
        HidLog.membership(LogPoint.UPDATE_ID_CARD_EXPIRED_TIME, content, member.getMid(), true);
        return new SubmitIdCardRespDto(AuthStateCodeEnum.SUCCESS);
    }

    @Transactional(rollbackFor = Exception.class)
    public SubmitIdCardRespDto submitForReview(SubmitIdCardInput input, MemberIdentityDocument memberIdentity,
                                                     MembershipBasicInfo member, boolean firstSubmit) throws AuthenticationException {
        // 占用即为第一次提交
        if (StringUtils.equals(BussinessConstants.APPKEY_OFC, input.getAppKey()) && StringUtils.isNotEmpty(input.getOccupiedMid())) {
            firstSubmit = true;
        }
        String content = "提交身份证";
        OperatorDto operator = OperatorDto.buildUserOperator(StringUtils.abbreviate("USER@" + input.getAppKey(), 20));
        //判断是否需要刷脸
        boolean needReRecFace = true;
        Long oriIdentityId = null;
        // 非首次提交包括：认证不通过、待刷脸（重新提交证件）
        if(!firstSubmit) {
            //TODO 确认此逻辑： 非首次提交，则已人脸认证过，若此次未变更姓名驾照号则可不需要重新刷脸。
            if(!IdentityAuthStatusEnum.TO_FACE_REC.eq(memberIdentity.getAuthenticationStatus())) {
                if(StringUtils.equals(memberIdentity.getIdentityNo(), input.getIdCardNumber())
                        && StringUtils.equals(memberIdentity.getName(), input.getName())) {
                    needReRecFace = false;
                }
            }
            content = "重新提交身份证";
            log.info("提交身份证件： 重新提交身份证，当前认证状态={}, 证件编号/姓名变更->重新刷脸={}, mid={}.",
                    memberIdentity.getAuthenticationStatus(), needReRecFace, input.getMid());
            oriIdentityId = memberIdentity.getId();
        } else {
            //首次提交，综合驾照审核状态及原审核状态： 驾照审核通过且原先刷过脸的老用户，此次无需刷脸
            boolean licenseAuthPassed = LicenseAuthStatusEnum.AUTHENTICATED.eq(member.getLicenseReviewStatus());
            boolean alreadyFace = licenseAuthPassed && member.getAuthenticationStatus().equals(2);
            needReRecFace = (!alreadyFace);
            log.info("提交身份证件： 首次提交身份证信息，licenseReviewStatus={}，authenticationStatus={}, mid={}",
                    member.getLicenseReviewStatus(), member.getAuthenticationStatus());
        }

        if (StringUtils.equals(BussinessConstants.APPKEY_OFC, input.getAppKey())) {
            // 履约提交证件后必须走刷脸
            log.info("履约提交证件后，必须刷脸，input={},needReRecFace={}",JSON.toJSONString(input),needReRecFace);
            needReRecFace = true;
        }else if(StringUtils.equals(BussinessConstants.APPKEY_QL, input.getAppKey())){
            // 履约提交证件后必须走刷脸
            log.info("擎路的自助取还认证,同步提交证件后，不刷脸，input={},needReRecFace={}",JSON.toJSONString(input),needReRecFace);
            needReRecFace = false;
        }

        //判断之前是否 根据驾照审核认证状态决定是否需要熟练
        log.info("提交身份证件： content={}, firstSubmit={}，needReRecFace={}, mid={}", content, firstSubmit, needReRecFace, input.getMid());
        PostCertSubmitEnum nextOperationType = PostCertSubmitEnum.TO_FACE_REC;
        if(needReRecFace) {
            //状态为未刷脸，APP端提示用户进入刷脸流程
            content += "完成，待刷脸";
            nextOperationType = PostCertSubmitEnum.TO_FACE_REC;
        }else {
            //已刷脸则根据数据录入方式，提交自动审核 或 提交人工审核
            if(input.getCertInputType().equals(1)) {
                content += "，已刷脸自动认证通过";
                nextOperationType = PostCertSubmitEnum.AUTO_AUTH_SUCEESS;
                memberAuditTrack(true,member.getAuthId(),1,"成功",memberIdentity != null ?IdentityAuthStatusEnum.getDesByCode(memberIdentity.getAuthenticationStatus()):"未认证",input.getOperationModel());
            }else {
                content += "，已刷脸提交人工审核";
                nextOperationType = PostCertSubmitEnum.TO_MANUAL_REVIEW;
            }
        }
        memberDocumentManager.saveIdentityDocument(input, member, oriIdentityId, nextOperationType, operator);
        //制卡及后续通知
        afterProcess(input.getMid(), member, PostCertSubmitEnum.AUTO_AUTH_SUCEESS, operator);
        // 履约证件号码被原⼿机号账号占⽤,清除被占用人信息，并设置为未认证
        if (StringUtils.equals(BussinessConstants.APPKEY_OFC, input.getAppKey())) {
            if (StringUtils.isNotEmpty(input.getOccupiedMid())) {
                clearIDCardAndLicenseByOccupied(member, input.getOccupiedMid(), input.getIdCardNumber(), operator);
            }
        }
        /*
         * 3. 保存操作日志
         */
        memberOptLogService.saveCertOptLog(content, member, MemOperateTypeEnum.IDCARD_SUBMIT_DATA,
                input.getAppKey(), null, true, operator);
        /**
         * 消息推送
         */
        PostCertSubmitEnum nextOpType = nextOperationType;
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                //90天有效期
                if (StringUtils.isNotBlank(input.getOperationModel())) {
                    JedisUtil.set("SubmitIdCard_Operation_Type_"+member.getAuthId(),input.getOperationModel(),60*60*24*90);
                }
                notifyIdentityAuthResult(input.getMid(), input.getAppKey(), member, nextOpType);
            }
        });
        return new SubmitIdCardRespDto(AuthStateCodeEnum.SUCCESS);
    }

    /**
     *会员审核完成的埋点
     *
     * @param success
     * @param authId
     * @param auditType 0 身份证人工 1 身份证自动 2 驾照人工
     * @param reason
     */
    public void memberAuditTrack(boolean success, String authId,int auditType,String reason,String beforeStatus,String operationModel){
        try {
            Map<String,Object> map = new HashMap<>();

            map.put("type",auditType == 1 ?"自动审核":"人工审核 ");
            map.put("nav_type",auditType == 2 ?" 驾照认证" :"身份认证");
            map.put("is_success",success);
            map.put("reason",reason);
            map.put("duration",0);
            map.put("desc",beforeStatus);

            String key = "";
            if (auditType == 2) {
                key = "SubmitLicence_Operation_Type_"+ authId;
            }else{
                key = "SubmitIdCard_Operation_Type_"+ authId;
            }

            if (StringUtils.isEmpty(operationModel)) {
                operationModel = JedisUtil.get(key);
            }

            if (StringUtils.isNotEmpty(operationModel)) {
                if (operationModel.equals("1")) {
                    map.put("operation_model","门店");
                }else if(operationModel.equals("0")){
                    map.put("operation_model","大库");
                }
                //续命
                JedisUtil.set(key,operationModel,60*60*24*90);
            }
            sensorsdataService.track(authId, true, "verify_completed", map);
        } catch (Exception e) {
            log.error("会员审核[{}]埋点异常[{}],入参success[{}],authId[{}],auditType[{}],reason[{}],beforeStatus[{}]","verify_completed",e,success,authId,auditType,reason,beforeStatus);
        }
    }

    /**
     * 非本籍用户提交身份证件
     * 外籍&港澳台
     * @param input
     * @throws AuthenticationException
     */
    public SubmitIdCardRespDto submitUnNativeUserIdCert(SubmitIdCardInput input) throws AuthenticationException {
        log.info("提交非大陆身份证件：input={}", JSON.toJSONString(input));
        HidLog.membership(LogPoint.SUBMIT_USER_CARD_FACE_INFO, "提交用户认证信息开始", input.getMid(), true);
        /**
         * 0. 参数校验
         */
        if(StringUtils.isBlank(input.getMid()) || StringUtils.isBlank(input.getAppKey())
                || StringUtils.isBlank(input.getIdcardPicUrl()) || StringUtils.isBlank(input.getHoldIdcardPicUrl())
        || StringUtils.isBlank(input.getFacePicUrl())) {
            throw BusinessRuntimeException.PARAM_EXEPTION;
        }
        //外籍证件默认走人工审核
        input.setCertInputType(2);
        //外籍驾照号符合是否是本籍
        if (ComUtil.checkIDCard(input.getIdCardNumber())) {
            log.info("提交非大陆身份证件：提交的证件号为证件id，input={}", JSON.toJSONString(input));
            HidLog.membership(LogPoint.SUBMIT_USER_ID_CARD_PIC, StatusCode.SUBMIT_DRIVER_MODE_ERROR.getMsg(), input.getMid(), false);
            throw new AuthenticationException(StatusCode.SUBMIT_DRIVER_MODE_ERROR);
        }
        /**
         * 1.查询用户信息
         */
        MembershipBasicInfo member = memberShipService.getUserBasicInfo(input.getMid());
        if(member == null || member.getAccountStatus().equals(2)) {
            HidLog.membership(LogPoint.SUBMIT_USER_CARD_FACE_INFO, StatusCode.USER_INFO_NO_EXIST.getMsg(), input.getMid(), false);
            throw new AuthenticationException(StatusCode.MEMBER_NOT_EXIT);
        }

        // 虚拟用户存在 证件号被多次使用
        if (member.getMembershipType() != MemberTypeEnum.QINGLU_USER.getValue().shortValue()) {
            //1.4 身份证账号已被注销冻结、身份证账号已存在
            if (StringUtils.isNotBlank(input.getIdCardNumber())) {
                MemberStatusDto memberStatusDto = memberDocumentManager.selectUserWithSameIdentityNo(input.getIdCardNumber(),
                        IdTypeEnum.UN_NATIVE_TYPES, input.getMid());
                if(null != memberStatusDto) {
                    if(!memberStatusDto.getAccountStatus().equals(0)) {
                        memberAuditTrack(false,member.getAuthId(),1,AuthStateCodeEnum.ACCOUNT_FROZEN.getMsg(),"未认证",input.getOperationModel());
                        return new SubmitIdCardRespDto(AuthStateCodeEnum.ACCOUNT_FROZEN);
                    }
                    memberAuditTrack(false,member.getAuthId(),1,AuthStateCodeEnum.SAME_ID_NO.getMsg(),"未认证",input.getOperationModel());
                    return new SubmitIdCardRespDto(AuthStateCodeEnum.SAME_ID_NO);
                }
            }
        }else{
            // 虚拟用户提交证件时，需要校验手机号、证件号、二级渠道号，是否已存在
            try {
                List<MembershipBasicInfo> memberList = membershipInfoMapper.getMemberList(member.getMobilePhone(), member.getSecondAppKey(),input.getIdCardNumber(), 2);
                if (CollectionUtils.isNotEmpty(memberList)) {
                    if (memberList.size() == 1) {
                        MembershipBasicInfo membershipBasicInfo = memberList.get(0);
                        String existedMid = membershipBasicInfo.getMid();
                        String mid = input.getMid();
                        if (mid.equals(existedMid)){
                            log.info("外籍虚拟用户提交的信息就是本人");
                        }else{
                            log.error("外籍虚拟用户提交身份证信息，手机号、证件号、二级渠道已被其它用户驾照认证用过,一个用户，认证失败 mid={},membershipBasicInfo={}", input.getMid(),JSON.toJSONString(membershipBasicInfo));
                            return new SubmitIdCardRespDto(AuthStateCodeEnum.SAME_ID_NO);
                        }
                    }else{
                        log.error("外籍虚拟用户提交身份证信息，手机号、证件号、二级渠道已被其它用户驾照认证用过,认证失败,查出多个用户， mid={}，memberList={}", input.getMid(),JSON.toJSONString(memberList));
                        return new SubmitIdCardRespDto(AuthStateCodeEnum.SAME_ID_NO);
                    }
                }
            } catch (Exception e) {
                log.error("外籍虚拟用户提交身份证信息时，校验唯一性，异常,input={}",JSON.toJSONString(input),e);
            }
        }


        /**
         * 当前证件状态
         */
        MemberIdentityDocument memberIdentity = memberDocumentManager.getMemberIdentity(input.getMid());
        boolean firstSubmit = true;
        //确认何种身份证件状态下可以更新外籍身份证件信息(未认证、认真干部通过)
        if(memberIdentity != null) {
            String beforeStatus = IdentityAuthStatusEnum.getDesByCode(memberIdentity.getAuthenticationStatus());
            if(IdentityAuthStatusEnum.TO_MANUAL_REVIEW.getValue().equals(memberIdentity.getAuthenticationStatus())) {
                log.error("提交身份证信息，当前身份证状态为待认证，请勿重复提交, mid={}", input.getMid());
                memberAuditTrack(false,member.getAuthId(),1,StatusCode.SUBMIT_FAILURE.getMsg(),beforeStatus,input.getOperationModel());
                throw new AuthenticationException(StatusCode.SUBMIT_FAILURE);
            }
            else if(IdentityAuthStatusEnum.AUTHENTICATED.getValue().equals(memberIdentity.getAuthenticationStatus())) {
                log.error("提交身份证信息，当前身份证状态为已认证，请勿重复提交, mid={}", input.getMid());
                memberAuditTrack(false,member.getAuthId(),1,StatusCode.SUBMIT_FAILURE.getMsg(),beforeStatus,input.getOperationModel());
                throw new AuthenticationException(StatusCode.SUBMIT_FAILURE);
            }
            else if(IdentityAuthStatusEnum.AUTHENTICATE_FAILED.getValue().equals(memberIdentity.getAuthenticationStatus())) {
                log.info("提交身份证信息，当前身份证状态为认证不通过，重新提交证件, mid={}", input.getMid());
                firstSubmit = false;
//                //重新提交证件时，需要比对审核不通过原因  {0，1，3， 4} 证件编号、姓名 、过期日期、身份证正页(护照/通行证)、身份证副页、手持证件照、人脸照片
//                String reviewItems = memberIdentity.getReviewItems();
//                if(!"2".equals(StringUtils.substring(reviewItems, 0, 1)) && StringUtils.isNotBlank(input.getIdCardNumber())
//                        && !input.getIdCardNumber().equalsIgnoreCase(memberIdentity.getIdentityNo())) {
//                    log.error("提交身份证信息，重新提交证件，证件编号不可与初次提交不同, mid={}, reviewItem={}", input.getMid(), reviewItems);
//                    return new SubmitIdCardRespDto(AuthStateCodeEnum.ID_NO_NOT_MATCH);
//                }
//                if(!"2".equals(StringUtils.substring(reviewItems, 1, 2)) && StringUtils.isNotBlank(input.getName())
//                        && !input.getName().equalsIgnoreCase(memberIdentity.getName())) {
//                    log.error("提交身份证信息，重新提交证件，证件编号不可与初次提交不同, mid={}, reviewItem={}", input.getMid(), reviewItems);
//                    return new SubmitIdCardRespDto(AuthStateCodeEnum.NAME_NOT_MATCH);
//                }
            } else if(IdentityAuthStatusEnum.TO_FACE_REC.getValue().equals(memberIdentity.getAuthenticationStatus())) {
                log.info("提交身份证信息，当前身份证状态为未刷脸，重新提交证件, mid={}", input.getMid());
                firstSubmit = false;
            }
        }
        /**
         * 2.处理图片url
         */
        String idCardPicUrl = ComUtil.splitPicUrl(input.getIdcardPicUrl());
        String holdIdCardPic = ComUtil.splitPicUrl(input.getHoldIdcardPicUrl());
        String facePicUrl = ComUtil.splitPicUrl(input.getFacePicUrl());
        /**
         * 3.校验手持和人脸相似度
         *
         *   20240605 产品gyy 外籍用户人脸校验默认通过，去掉校验
         */
        /*try {
            memberShipService.getFaceContrastResult(ComUtil.subString(holdIdCardPic), ComUtil.subString(facePicUrl), member.getAuthId());
        }catch (Exception ex) {
            log.info("提交非大陆身份证件：手持证件照片与人脸照片不匹配，mid=" + input.getMid(), ex);
            memberAuditTrack(false,member.getAuthId(),1,AuthStateCodeEnum.FACE_ID_NOT_MATCH.getMsg(),"未认证",input.getOperationModel());
            return new SubmitIdCardRespDto(AuthStateCodeEnum.FACE_ID_NOT_MATCH);
        }*/

        /**
         * 4. 提交资料
         */
        OperatorDto operator = OperatorDto.buildUserOperator("USER");
        Long oriIdentityId = null;
        if(memberIdentity != null) {
            oriIdentityId = memberIdentity.getId();
        }
        //判断之前是否 根据驾照审核认证状态决定是否需要熟练
        String content = "提交身份证资料(非本籍大陆用户)，待人工审核";
        log.info("提交非大陆身份证件： content={}, firstSubmit={}, mid={}", content, firstSubmit, input.getMid());
        PostCertSubmitEnum nextOperationType = PostCertSubmitEnum.TO_MANUAL_REVIEW;
        input.setCertInputType(2);
        input.setExpireType(2);
        input.setFacePicUrl(facePicUrl);
        input.setHoldIdcardPicUrl(holdIdCardPic);
        input.setIdcardPicUrl(idCardPicUrl);
        input.setIdcardPicBackUrl(StringUtils.EMPTY);
        /** 提交外籍身份证件资料，若当前驾照已提交则不清除姓名(非未提交) */
        input.setName(StringUtils.EMPTY);
        if(!LicenseAuthStatusEnum.UN_SUBMIT.eq(member.getLicenseReviewStatus())) {
            input.setName(null);
        }

        // 履约 外籍身份认证 自动认证通过
        if (StringUtils.equals(BussinessConstants.APPKEY_OFC, input.getAppKey())) {
            nextOperationType =  PostCertSubmitEnum.AUTO_AUTH_SUCEESS;
            input.setCertInputType(1);
            content = "履约提交身份证资料(非本籍大陆用户)，自动认证通过";
        }
        memberDocumentManager.saveIdentityDocument(input, member, oriIdentityId, nextOperationType, operator);

        // 5 20230321 外籍提交证件照后，立即制卡
        markCardAfterSubmitUnNativeIdCard(input.getMid(),member,"提交身份证资料(非本籍大陆用户)，待人工审核-制卡",operator);

        /**
         * 6.保存操作日志
         * 人脸认证日志 & 资料提交
         */
        String refKey2 = "ali-face-verify";
        memberOptLogService.saveCertOptLog(content, member, MemOperateTypeEnum.FACE_REC, input.getAppKey(), refKey2, false, operator);
        memberOptLogService.saveCertOptLog(content, member, MemOperateTypeEnum.IDCARD_SUBMIT_DATA, input.getAppKey(), null, false, operator);

        /**
         * 推送消息
         */
        PostCertSubmitEnum nextOpType = nextOperationType;
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                //90天有效期
                JedisUtil.set("SubmitIdCard_Operation_Type_"+member.getAuthId(),input.getOperationModel(),60*60*24*90);
                notifyIdentityAuthResult(input.getMid(), input.getAppKey(), member, nextOpType);
            }
        });

        HidLog.membership(LogPoint.SUBMIT_USER_CARD_FACE_INFO, content, member.getAuthId(), true);
        memberAuditTrack(true,member.getAuthId(),1,"成功","未认证",input.getOperationModel());
        return new SubmitIdCardRespDto(AuthStateCodeEnum.SUCCESS);
    }


    public void afterProcess(String mid, MembershipBasicInfo member,
                             PostCertSubmitEnum nextOpType, OperatorDto operator) throws AuthenticationException {
        /**
         * 审核通过
         */
        String content = nextOpType.getDesc();
        String authId = member.getAuthId();
        //1. 更新账号申请进度
        ApplyProgress progress = new ApplyProgress();
        progress.setAuthId(authId);
        progress.setProgressContent(content);
        progress.setCreatedUser(operator.getOperatorName());
        progress.setProgressTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE1));
        applyProgressMapper.addApplyProgress(progress);

        /**
         * 身份证件提交人工审核、自动/人工审核通过，均做制卡
         */
        if(PostCertSubmitEnum.AUTO_AUTH_SUCEESS.equals(nextOpType)
                || PostCertSubmitEnum.TO_MANUAL_REVIEW.equals(nextOpType)) {
            //2. 增加制卡检查和制卡逻辑(检查是否无卡)
            memberShipService.setVirtualCard(authId,member.getMembershipType());
        }

        //3. 埋点
        String operation = operator.getRemark();
        //添加日志埋点
        HidLog.membership(LogPoint.UPDATE_USER_REVIEW_STATUS, operation, authId);
        String operatorContent = operation;
        log.info("身份证件提审/自动审核通过，完成制卡，mid={}, cause={}", mid, operatorContent);
    }

    /**
     *
     * @param mid
     * @param member
     * @param content
     * @param operator
     * @throws AuthenticationException
     */
    public void markCardAfterSubmitUnNativeIdCard(String mid, MembershipBasicInfo member, String content, OperatorDto operator) throws AuthenticationException {
        try {
            log.info("外籍提交证件通过后，开始制卡，mid={}, content={}", mid, content);
            String authId = member.getAuthId();
            //1. 更新账号申请进度
            ApplyProgress progress = new ApplyProgress();
            progress.setAuthId(authId);
            progress.setProgressContent(content);
            progress.setCreatedUser(operator.getOperatorName());
            progress.setProgressTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE1));
            applyProgressMapper.addApplyProgress(progress);

            //2. 增加制卡检查和制卡逻辑(检查是否无卡)
            memberShipService.setVirtualCard(authId,member.getMembershipType());

            //3. 埋点
            String operation = operator.getRemark();
            //添加日志埋点
            HidLog.membership(LogPoint.UPDATE_USER_REVIEW_STATUS, operation, authId);
            log.info("外籍提交证件通过后，完成制卡，mid={}, cause={}", mid, content);
        } catch (Exception e) {
            log.info("外籍提交证件通过后，制卡异常，mid={}, cause={}", mid, content,e);
        }
    }


    public void notifyIdentityAuthResult(String mid, String appKey, MembershipBasicInfo member, PostCertSubmitEnum nextOpType) {
        //ThreadPoolUtils.EXECUTOR.execute(()-> {
            switch (nextOpType) {
                case TO_FACE_REC: break;
                case TO_MANUAL_REVIEW:
                    notifyIdentityToManual(mid, appKey, member);
                    break;
                case AUTO_AUTH_SUCEESS:
                    notifyIdentityAuthSuccess(mid, appKey, member);
                    break;
            }
        //});
    }


    @Transactional(rollbackFor = Exception.class)
    public BaseResponse submitFaceImgToReview(String mid, boolean manualFlag, String faceRecImg,
                                              String appKey, MembershipBasicInfo member) throws AuthenticationException {
        return submitFaceImgToReview(mid, manualFlag, faceRecImg, null,
                appKey, null, member);
    }

    @Transactional(rollbackFor = Exception.class)
    public BaseResponse submitFaceImgToReview(String mid, boolean manualFlag, String faceRecImg,
                                              String idCardNo, String appKey, String refKey2,
                                              MembershipBasicInfo member) throws AuthenticationException {
        if(StringUtils.isNotBlank(faceRecImg)) {
            faceRecImg = ComUtil.splitPicUrl(faceRecImg);
        }
        /**
         * 认证认证通过后，提交人脸照片，提交身份认证审核
         */
        MemberIdentityDocument identityDocument = memberDocumentManager.getMemberIdentity(mid);
        if(identityDocument == null) {
            log.error("更新人脸照片时，尚无身份认证信息, mid={}", mid);
            throw new AuthenticationException(StatusCode.AUTHENTICATION_ERROR_ONE);
        }
        Integer identityAuthStatus = identityDocument.getAuthenticationStatus();
        if(StringUtils.isNotBlank(appKey) && !BussinessConstants.FACE_NO_CHECK_CHANNEL.contains(appKey)) {
            if (!IdentityAuthStatusEnum.TO_FACE_REC.eq(identityAuthStatus)) {
                log.error("更新人脸照片时，当前身份认证状态非未刷脸, mid={}, appKey={}", mid, appKey);
                throw new AuthenticationException(StatusCode.SUBMIT_FAILURE);
            }
        } else {
            log.warn("更新人脸照片时，特定认证渠道跳过当前人脸状态校验, mid={}, appKey={}, i ", mid, appKey);
        }
        OperatorDto operator = OperatorDto.buildUserOperator(StringUtils.abbreviate("USER@" + appKey, 20));
        String content = "人脸认证通过";
        PostCertSubmitEnum nextOperationType;
        MemOperateTypeEnum opType;
        if(!manualFlag && identityDocument.getCertInputType().equals(1)) {
            content += "，自动认证通过";
            nextOperationType = PostCertSubmitEnum.AUTO_AUTH_SUCEESS;
            opType = MemOperateTypeEnum.IDCARD_AUTO_AUDIT_PASS;
            //埋点
            memberAuditTrack(true,member.getAuthId(),1,"成功","未刷脸","");
        } else {
            content += "，提交人工审核";
            nextOperationType = PostCertSubmitEnum.TO_MANUAL_REVIEW;
            opType = MemOperateTypeEnum.SUBMIT_REVIEW_AFTER_FACE_REC;
        }
        memberDocumentManager.updateReviewStatusAfterFaceRec(mid, identityDocument.getId(), faceRecImg,
                idCardNo, member, nextOperationType, operator);
        /*
         * 3. 保存操作日志
         */
        memberOptLogService.saveCertOptLog(content, member, opType, appKey, refKey2, true, operator);

        //制卡及后续通知
        afterProcess(mid, member, nextOperationType, operator);
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                notifyIdentityAuthResult(mid, appKey, member, nextOperationType);
            }
        });
        return new SubmitDriverLicenseRespDto(AuthStateCodeEnum.SUCCESS);
    }

    /**
     * 履约 人脸识别通过后处理
     * @param mid
     * @param faceRecImg
     * @param appKey
     * @param member
     * @return
     * @throws AuthenticationException
     */
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse ofcSubmitFaceImgToReview(String mid, String faceRecImg, String appKey, MembershipBasicInfo member,int forceSuccess) throws AuthenticationException {
        if(StringUtils.isNotBlank(faceRecImg)) {
            faceRecImg = ComUtil.splitPicUrl(faceRecImg);
        }
        /**
         * 认证认证通过后，提交人脸照片，提交身份认证审核
         */
        MemberIdentityDocument identityDocument = memberDocumentManager.getMemberIdentity(mid);
        if(identityDocument == null) {
            log.error("履约更新人脸照片时，尚无身份认证信息, mid={}", mid);
            throw new AuthenticationException(StatusCode.AUTHENTICATION_ERROR_ONE);
        }
        /*Integer identityAuthStatus = identityDocument.getAuthenticationStatus();
        if (!IdentityAuthStatusEnum.TO_FACE_REC.eq(identityAuthStatus)) {
            log.error("履约更新人脸照片时，当前身份认证状态非未刷脸, mid={}, appKey={}", mid, appKey);
            throw new AuthenticationException(StatusCode.SUBMIT_FAILURE);
        }*/

        OperatorDto operator = OperatorDto.buildUserOperator(StringUtils.abbreviate("USER@" + appKey, 20));
        String content = "履约人脸认证通过，自动认证通过";
        if (forceSuccess == 1){
            content = "履约人脸自动认证失败后，强制认证通过";
        }
        PostCertSubmitEnum nextOperationType= PostCertSubmitEnum.AUTO_AUTH_SUCEESS;
        MemOperateTypeEnum opType= MemOperateTypeEnum.IDCARD_AUTO_AUDIT_PASS;
        //埋点
        memberAuditTrack(true,member.getAuthId(),1,"成功","未刷脸","");

        memberDocumentManager.updateReviewStatusAfterFaceRec(mid, identityDocument.getId(), faceRecImg,
                identityDocument.getIdentityNo(), member, nextOperationType, operator);
        /*
         * 3. 保存操作日志
         */
        memberOptLogService.saveCertOptLog(content, member, opType, appKey, member.getAuthId(), true, operator);

        //制卡及后续通知
        afterProcess(mid, member, nextOperationType, operator);
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                notifyIdentityAuthResult(mid, appKey, member, nextOperationType);
            }
        });
        return new SubmitDriverLicenseRespDto(AuthStateCodeEnum.SUCCESS);
    }


    public boolean getFaceRecFlag(MembershipBasicInfo member) {
        boolean alreayFaceAuthed = member.getAuthenticationStatus().equals(2);
        MemberIdentityDocument identityDocument = memberDocumentManager.getMemberIdentity(member.getMid());
        if(identityDocument != null) {
            alreayFaceAuthed = !IdentityAuthStatusEnum.unFaceAuthed(identityDocument.getAuthenticationStatus());
        }
        return alreayFaceAuthed;
    }

    private void notifyIdentityAuthSuccess(String mid, String appKey, MembershipBasicInfo member) {
        //1. 推送邀请好友消息
        try {
            BaseResponse baseResponse = memberShipInvitationServ.giveFriendInvitationGiftBag(member.getAuthId(),
                    member.getMobilePhone(), "00", "evcard-membership-rpc");
            if (baseResponse.getCode() != 0) {
                log.warn("会员推送邀请好友事件失败, mid={}, authId={}", member.getMid(), member.getAuthId());
            }
        } catch (Exception e) {
            log.error("会员推送邀请好友事件异常,authId=" + member.getAuthId() + ",mid=" + member.getMid(), e);
        }
        //2. 照片
        String isFirstReview = (member.getIdentityFirstAuthTime() == null) ? "1" : "0";
        MemberAudit memberAudit = new MemberAudit();
        memberAudit.setAuthId(member.getAuthId());
        memberAudit.setMemberType(0);
        memberAudit.setMobilePhone(member.getMobilePhone());
        memberAudit.setCreatedTime(new Date());
        memberAudit.setDataOrigin(appKey);
        memberAudit.setFirstReviewFlag(isFirstReview);
        memberAudit.setCreatedTime(new Date());
        //memberAudit.setRegTime(member.getRegTime());
        //确认是否需要标记用老用户
        memberAudit.setNewUser(0);
        memberAudit.setOptUser("membership-rpc");
        memberAudit.setReviewMode(2);
        memberAudit.setReviewStatus("1");
        memberAudit.setAuthenticationStatus(2);
        memberAudit.setReviewTime(new Date());
        byte[] messageBody = ProtobufUtil.serializeProtobuf(memberAudit);
        pushMq(messageBody, EventEnum.MEMBER_AUDIT, mid);

        NewMemberAudit newMemberAudit = new NewMemberAudit();
        BeanCopyUtils.copyProperties(memberAudit, newMemberAudit);
        newMemberAudit.setMid(member.getMid());
        newMemberAudit.setAuthenticationStatus(IdentityAuthStatusEnum.AUTHENTICATED.getValue());
        newMemberAudit.setReviewMode(2);
        newMemberAudit.setAuditType(0);
        newMemberAudit.setOperatorId("1");
        newMemberAudit.setOperatorUserName(mid);
        if ("ofc".equals(appKey)) {
            newMemberAudit.setOperateSourceType(1);
        }
        messageBody = ProtobufUtil.serializeProtobuf(newMemberAudit);
        pushMq(messageBody, EventEnum.MEMBER_AUDIT_IDCARD, mid);

        //TODO 3.2 埋点
        //trackMemberReviewResult(authId, member, updateReviewStatusDto);
    }

    private void notifyIdentityToManual(String mid, String appKey, MembershipBasicInfo member) {
        //1. 推送邀请好友消息
        try {
            BaseResponse baseResponse = memberShipInvitationServ.giveFriendInvitationGiftBag(member.getAuthId(),
                    member.getMobilePhone(), "00", "evcard-membership-rpc");
            if (baseResponse.getCode() != 0) {
                log.warn("会员推送邀请好友事件失败, mid={}, authId={}", member.getMid(), member.getAuthId());
            }
        } catch (Exception e) {
            log.error("会员推送邀请好友事件异常,authId=" + member.getAuthId() + ",mid=" + member.getMid(), e);
        }
        /**
         * 2. 推送身份证件提交审核事件
         */
        MemberToAudit memberToAudit = new MemberToAudit();
        memberToAudit.setMid(mid);
        memberToAudit.setAuthId(member.getAuthId());
        memberToAudit.setCertType(1);
        memberToAudit.setSubmitTime(new Date());
        memberToAudit.setSubmitAppKey(appKey);
        byte[] messageBody = ProtobufUtil.serializeProtobuf(memberToAudit);
        pushMq(messageBody, EventEnum.MEMBER_TO_AUDIT_IDCARD, mid);
        //TODO 3.2 埋点
        //trackMemberReviewResult(authId, member, updateReviewStatusDto);
    }

    private void pushMq(byte[] messageBody, EventEnum eventEnum, String key) {
        /**
         * 推送身份证件提交审核事件
         */
        try {
            Message msg = new Message(evcardRawDataTopic, eventEnum.getTag(), messageBody);
            msg.setKey(key);
            producer.sendAsync(msg, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    log.info(eventEnum.getTag() + ":" + key + "发送mq成功");
                }
                @Override
                public void onException(OnExceptionContext onExceptionContext) {
                    log.error(eventEnum + "发送mq失败，失败原因<{}>", onExceptionContext);
                }
            });
        }catch (Exception e) {
            log.error("推送审核消息失败，tag=" + eventEnum.getTag() + ", key=" + key, e);
        }
    }
    
    private void clearIDCardAndLicenseByOccupied(MembershipBasicInfo member, String occupiedMid, String idCardNumber, OperatorDto operator) {
        log.info("身份占用-清除被占用人认证信息开始,mid:{},idCardNumber:{}.", occupiedMid, idCardNumber);
        log.info("身份占用step1-清除身份证信息开始");
        Date now = new Date();
        MembershipBasicInfo occupiedMembershipInfo = membershipInfoMapper.getUserBasicInfoByMid(occupiedMid);
        Long identityId = occupiedMembershipInfo.getIdentityId();
        MemberIdentityDocument occupiedMemberIdentityDocument = memberIdentityDocumentMapper.selectByPrimaryKey(identityId);
        if (occupiedMemberIdentityDocument != null) {
            log.info("身份占用step1-存在需要清除的身份证信息, memberIdentityDocumentId:{}", occupiedMemberIdentityDocument.getId());
            MembershipBasicInfo occupiedMembershipBasicInfo = membershipInfoMapper.getUserBasicInfoByMid(occupiedMid);

            //membershipInfoMapper 更新
            MembershipInfoWithBLOBs updateMember = new MembershipInfoWithBLOBs();
            //主键
            updateMember.setPkId(occupiedMembershipBasicInfo.getPkId());
            //更新时间
            updateMember.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
            //更新操作人
            updateMember.setUpdatedUser(operator.getOperatorName());
            //身份认证状态 0待认证(未认证) 1待认证(未刷脸上传) 2认证通过 3认证不通过
            updateMember.setAuthenticationStatus(0);
            updateMember.setReviewUser(operator.getOperatorName());
            // 这里给 ReviewItems 设置为 0000000
            updateMember.setReviewItems(BussinessConstants.IDCARD_REVIEW_ITEMS_INIT);
            updateMember.setPassportNo(StringUtils.EMPTY);
            updateMember.setIdCardNumber(StringUtils.EMPTY);
            updateMember.setName(StringUtils.EMPTY);
            updateMember.setIdType(1);
            updateMember.setIdcardPicUrl(StringUtils.EMPTY);
            updateMember.setFaceRecognitionImgUrl(StringUtils.EMPTY);
            membershipInfoMapper.clearIdentityByPrimaryKeySelective(updateMember);


            MemberIdentityDocument updateMemberIdentityDocument = new MemberIdentityDocument();
            updateMemberIdentityDocument.setId(occupiedMemberIdentityDocument.getId());
            //认证状态
            updateMemberIdentityDocument.setAuthenticationStatus(IdentityAuthStatusEnum.UN_SUBMIT.getValue());
            //更新记录人的 id
            updateMemberIdentityDocument.setUpdateOperId(operator.getOperatorId());
            updateMemberIdentityDocument.setUpdateOperName(operator.getOperatorName());
            //自动审核
            updateMemberIdentityDocument.setReviewMode(1);
            //审核人
            updateMemberIdentityDocument.setReviewUser(operator.getOperatorName());
            //审核时间
            updateMemberIdentityDocument.setReviewTime(now);
            //审核项审核结果
            updateMemberIdentityDocument.setReviewItems(BussinessConstants.IDCARD_REVIEW_ITEMS_INIT);
            //重新审核 或者 已认证 不通过原因相关字段 置空
            updateMemberIdentityDocument.setReviewIds("");
            updateMemberIdentityDocument.setReviewRemark("");
            updateMemberIdentityDocument.setReviewItemNames("");
            //更新时间
            updateMemberIdentityDocument.setUpdateTime(now);

            // 用户信息清除
            updateMemberIdentityDocument.setIdentityNo(StringUtils.EMPTY);
            updateMemberIdentityDocument.setIdentityType(1);
            updateMemberIdentityDocument.setName(StringUtils.EMPTY);
            updateMemberIdentityDocument.setExpireType(1);
            updateMemberIdentityDocument.setExpirationDate(StringUtils.EMPTY);
            updateMemberIdentityDocument.setIdentityCardImgUrl(StringUtils.EMPTY);
            updateMemberIdentityDocument.setReverseIdentityCardImgUrl(StringUtils.EMPTY);
            updateMemberIdentityDocument.setCertInputType(2);
            updateMemberIdentityDocument.setFaceRecognitionImgUrl(StringUtils.EMPTY);
            memberIdentityDocumentMapper.updateByPrimaryKeySelective(updateMemberIdentityDocument);

            // 记录操作日志
            ComUtil.insertOperatorLog(MemOperateTypeEnum.IDCARD_MEMBER_REAUDIT.getOperate(), member.getAuthId(),
                    member.getMobilePhone(), operator.getOperatorName(), userOperatorLogMapper);

            // 操作日志
            UserOperationLogInput operationLog = new UserOperationLogInput();
            operationLog.setUserId(occupiedMembershipInfo.getPkId());
            operationLog.setAuthId(occupiedMembershipInfo.getAuthId());
            operationLog.setMembershipType((int) occupiedMembershipInfo.getMembershipType());
            operationLog.setOperationType(MemOperateTypeEnum.IDCARD_MEMBER_REAUDIT.getCode());
            operationLog.setOperationContent("身份证占用清除身份信息");
            operationLog.setRefKey1("履约");
            operationLog.setOperatorId(operator.getOperatorId());
            operationLog.setOperator(operator.getOperatorName());
            operationLog.setOperationTime(now);
            memberShipService.saveUserOperationLog(operationLog);
        }
        log.info("身份占用step1-清除身份证信息结束");
        log.info("身份占用step2-清除驾驶证信息开始");
        List<MembershipBaseInfo> membershipBaseInfoList = membershipInfoMapper.checkDriverCodeOccupied(member.getAuthId(), idCardNumber, 0);
        if (CollectionUtils.isNotEmpty(membershipBaseInfoList)) {
            log.info("驾驶证占用step2-存在需要清除的驾驶证信息, mid:{}", membershipBaseInfoList.get(0).getMid());
            //membershipInfoMapper 更新
            MembershipInfoWithBLOBs occupiedUpdateMember = new MembershipInfoWithBLOBs();
            //审核状态
            occupiedUpdateMember.setReviewStatus((short) -1);
            //主键
            occupiedUpdateMember.setPkId(membershipBaseInfoList.get(0).getPkId());
            //更新时间
            occupiedUpdateMember.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
            //更新操作人
            occupiedUpdateMember.setUpdatedUser(operator.getOperatorName());
            //驾照认证状态 1:未认证、2:待认证、3:已认证、4:认证失败、 5:即将过期（剩余90天）、6:已过期
            occupiedUpdateMember.setLicenseReviewStatus(LicenseAuthStatusEnum.UN_SUBMIT.getValue());
            occupiedUpdateMember.setReviewUser(operator.getOperatorName());
            // 这里给 ReviewItems 设置为 0000000
            occupiedUpdateMember.setReviewItems(BussinessConstants.IDCARD_REVIEW_ITEMS_INIT);
            // 清除驾照数据
            occupiedUpdateMember.setDriverCode(StringUtils.EMPTY);
            occupiedUpdateMember.setObtainDriverTimer(StringUtils.EMPTY);
            occupiedUpdateMember.setLicenseExpirationTime(StringUtils.EMPTY);
            occupiedUpdateMember.setFileNoImgUrl(StringUtils.EMPTY);
            occupiedUpdateMember.setDrivingLicenseImgUrl(StringUtils.EMPTY);
            occupiedUpdateMember.setFileNo(StringUtils.EMPTY);
            membershipInfoMapper.clearLicenseByPrimaryKeySelective(occupiedUpdateMember);

            // 记录操作日志
            ComUtil.insertOperatorLog(MemOperateTypeEnum.IDCARD_MEMBER_REAUDIT.getOperate(), member.getAuthId(),
                    member.getMobilePhone(), operator.getOperatorName(), userOperatorLogMapper);

            // 操作日志
            UserOperationLogInput operationLog = new UserOperationLogInput();
            operationLog.setUserId(occupiedMembershipInfo.getPkId());
            operationLog.setAuthId(occupiedMembershipInfo.getAuthId());
            operationLog.setMembershipType((int) occupiedMembershipInfo.getMembershipType());
            operationLog.setOperationType(MemOperateTypeEnum.IDCARD_MEMBER_REAUDIT.getCode());
            operationLog.setOperationContent("身份证占用清除驾照信息");
            operationLog.setRefKey1("履约");
            operationLog.setOperatorId(operator.getOperatorId());
            operationLog.setOperator(operator.getOperatorName());
            operationLog.setOperationTime(now);
            memberShipService.saveUserOperationLog(operationLog);
        }
        log.info("身份占用step2-清除驾驶证信息结束");
        log.info("清除被占用人认证信息结束,mid:{},idCardNumber:{}.", occupiedMid, idCardNumber);
    }
}
