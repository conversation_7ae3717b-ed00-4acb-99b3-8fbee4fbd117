package com.extracme.evcard.membership.core.dao;

import java.util.List;
import java.util.Map;

import com.extracme.evcard.membership.core.dto.CardInfoHistoryDTO;
import com.extracme.evcard.membership.core.input.QueryCardInfoConditionInput;
import com.extracme.evcard.membership.core.input.QueryCardInfoHistoryListIConditionInput;
import com.extracme.evcard.membership.core.dto.QueryOrgCardInfoDto;
import org.apache.ibatis.annotations.Param;

import com.extracme.evcard.membership.core.model.CardInfo;

/**
 * Mapper类，，对应表card_info
 */
public interface CardInfoMapper  {
    /**
     * 插入卡信息
     * @param cardInfo
     */
    void addCardInfo(CardInfo cardInfo);

    /**
     * 根据卡号查询卡信息
     * @param cardNo
     * @return
     */
    List<CardInfo> getCardInfo(@Param("cardNo") String cardNo);

    /**
     * 根据用户类型查询card_seq
     * @param condition
     * @return
     */
    List<Integer> queryInternalNo(@Param("condition") Map condition);

    /**
     * 更新card_seq
     * @param condition
     */
    void updateCardSeq(@Param("condition") Map condition);


    /**
     * 根据authId和cardNo更新status
     * @param cardInfo
     * @return
     */
    Integer updateCardStatusByCardNo(CardInfo cardInfo);

    /**
     * 根据企业id和cardNo判断改企业卡是否可用，0不可用
     * @param orgId
     * @param cardNo
     * @return
     */
    int getOrgCardStatusById(@Param("orgId")String orgId,@Param("cardNo") String cardNo);

    /**
     * 查询用户卡详情列表
     * @param authId
     * @return
     */
    List<CardInfo> queryCardInfoByAuthId(String authId);

    /**
     * 查询卡详情历史列表
     * @param conditionInput
     * @return
     */
    List<CardInfoHistoryDTO> queryCardInfoHistoryList(QueryCardInfoHistoryListIConditionInput conditionInput);

    /**
     * 绑定已存在但被注销使用的卡
     * @param updateCardEntity
     * @return
     */
    Integer updateCardNo(CardInfo updateCardEntity);

    int updateByPrimaryKeySelective(CardInfo cardInfo);

    int insertSelective(CardInfo record);

    List<CardInfo> queryCardInfoByCondition(QueryCardInfoConditionInput queryCardInfoConditionInput);

    /**
     * 根据企业id和cardNo判断改企业卡是否可用，0不可用
     * @param orgId
     * @return
     */
    List<QueryOrgCardInfoDto> queryOrgCardList(@Param("orgId")String orgId);


    List<Map<String,String>> batchQueryInternalNo(@Param("list") List<String> cardNoList);

    Integer countCardInfoHistoryList(QueryCardInfoHistoryListIConditionInput conditionInput);


}