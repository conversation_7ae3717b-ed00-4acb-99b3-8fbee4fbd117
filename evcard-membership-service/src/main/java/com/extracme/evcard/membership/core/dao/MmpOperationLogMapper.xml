<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.extracme.evcard.membership.core.dao.MmpOperationLogMapper">
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.MmpOperationLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="agency_id" jdbcType="VARCHAR" property="agencyId" />
    <result column="operation_time" jdbcType="TIMESTAMP" property="operationTime" />
    <result column="operation_user_id" jdbcType="BIGINT" property="operationUserId" />
    <result column="operation_user_name" jdbcType="VARCHAR" property="operationUserName" />
    <result column="operation_remark" jdbcType="VARCHAR" property="operationRemark" />
    <result column="discount_new_id" jdbcType="BIGINT" property="discountNewId" />
    <result column="discount_old_id" jdbcType="BIGINT" property="discountOldId" />
    <result column="misc_desc" jdbcType="VARCHAR" property="miscDesc" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_oper_id" jdbcType="BIGINT" property="createOperId" />
    <result column="create_oper_name" jdbcType="VARCHAR" property="createOperName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_oper_id" jdbcType="BIGINT" property="updateOperId" />
    <result column="update_oper_name" jdbcType="VARCHAR" property="updateOperName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, agency_id, operation_time, operation_user_id, operation_user_name, operation_remark, 
    discount_new_id, discount_old_id, misc_desc, status, create_time, create_oper_id, 
    create_oper_name, update_time, update_oper_id, update_oper_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_operation_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ${issSchema}.mmp_operation_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.membership.core.model.MmpOperationLog">
    insert into ${issSchema}.mmp_operation_log (id, agency_id, operation_time,
      operation_user_id, operation_user_name, operation_remark, 
      discount_new_id, discount_old_id, misc_desc, 
      status, create_time, create_oper_id, 
      create_oper_name, update_time, update_oper_id, 
      update_oper_name)
    values (#{id,jdbcType=BIGINT}, #{agencyId,jdbcType=VARCHAR}, #{operationTime,jdbcType=TIMESTAMP}, 
      #{operationUserId,jdbcType=BIGINT}, #{operationUserName,jdbcType=VARCHAR}, #{operationRemark,jdbcType=VARCHAR}, 
      #{discountNewId,jdbcType=BIGINT}, #{discountOldId,jdbcType=BIGINT}, #{miscDesc,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, 
      #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, 
      #{updateOperName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.core.model.MmpOperationLog">
    insert into ${issSchema}.mmp_operation_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="agencyId != null">
        agency_id,
      </if>
      <if test="operationTime != null">
        operation_time,
      </if>
      <if test="operationUserId != null">
        operation_user_id,
      </if>
      <if test="operationUserName != null">
        operation_user_name,
      </if>
      <if test="operationRemark != null">
        operation_remark,
      </if>
      <if test="discountNewId != null">
        discount_new_id,
      </if>
      <if test="discountOldId != null">
        discount_old_id,
      </if>
      <if test="miscDesc != null">
        misc_desc,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createOperId != null">
        create_oper_id,
      </if>
      <if test="createOperName != null">
        create_oper_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateOperId != null">
        update_oper_id,
      </if>
      <if test="updateOperName != null">
        update_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="agencyId != null">
        #{agencyId,jdbcType=VARCHAR},
      </if>
      <if test="operationTime != null">
        #{operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operationUserId != null">
        #{operationUserId,jdbcType=BIGINT},
      </if>
      <if test="operationUserName != null">
        #{operationUserName,jdbcType=VARCHAR},
      </if>
      <if test="operationRemark != null">
        #{operationRemark,jdbcType=VARCHAR},
      </if>
      <if test="discountNewId != null">
        #{discountNewId,jdbcType=BIGINT},
      </if>
      <if test="discountOldId != null">
        #{discountOldId,jdbcType=BIGINT},
      </if>
      <if test="miscDesc != null">
        #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        #{updateOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.core.model.MmpOperationLog">
    update ${issSchema}.mmp_operation_log
    <set>
      <if test="agencyId != null">
        agency_id = #{agencyId,jdbcType=VARCHAR},
      </if>
      <if test="operationTime != null">
        operation_time = #{operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operationUserId != null">
        operation_user_id = #{operationUserId,jdbcType=BIGINT},
      </if>
      <if test="operationUserName != null">
        operation_user_name = #{operationUserName,jdbcType=VARCHAR},
      </if>
      <if test="operationRemark != null">
        operation_remark = #{operationRemark,jdbcType=VARCHAR},
      </if>
      <if test="discountNewId != null">
        discount_new_id = #{discountNewId,jdbcType=BIGINT},
      </if>
      <if test="discountOldId != null">
        discount_old_id = #{discountOldId,jdbcType=BIGINT},
      </if>
      <if test="miscDesc != null">
        misc_desc = #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.core.model.MmpOperationLog">
    update ${issSchema}.mmp_operation_log
    set agency_id = #{agencyId,jdbcType=VARCHAR},
      operation_time = #{operationTime,jdbcType=TIMESTAMP},
      operation_user_id = #{operationUserId,jdbcType=BIGINT},
      operation_user_name = #{operationUserName,jdbcType=VARCHAR},
      operation_remark = #{operationRemark,jdbcType=VARCHAR},
      discount_new_id = #{discountNewId,jdbcType=BIGINT},
      discount_old_id = #{discountOldId,jdbcType=BIGINT},
      misc_desc = #{miscDesc,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="countAgencyOperationLogNum"  resultType="int">
    select
    count(*)
    from ${issSchema}.mmp_operation_log
    where agency_id = #{agencyId}
  </select>

  <select id="queryAgencyOperationLogPage" resultMap="BaseResultMap">
    select
      <include refid="Base_Column_List"/>
    from ${issSchema}.mmp_operation_log
    where agency_id = #{agencyId}
    limit #{page.offSet},#{page.limitSet}
  </select>
</mapper>