package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.dto.AgencyInfoDto;
import com.extracme.evcard.membership.core.dto.AgencyMinsDto;
import com.extracme.evcard.membership.core.input.QueryAgencyListConditionInput;
import com.extracme.evcard.membership.core.model.AgencyInfo;
import com.extracme.evcard.membership.core.model.EnterpriseAgencyInfo;
import com.extracme.evcard.rpc.dto.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AgencyInfoMapper {
    int deleteByPrimaryKey(String agencyId);

    int insert(AgencyInfo record);

    int insertSelective(AgencyInfo record);

    AgencyInfo selectByPrimaryKey(String agencyId);

    int updateByPrimaryKeySelective(AgencyInfo record);

    int updateByPrimaryKey(AgencyInfo record);

    EnterpriseAgencyInfo getAgencyInfoByName(@Param("name") String name);

    /**
     * 查询是否为小循环
     * @param agencyId
     * @return
     */
    int getFlagByAgencyId(@Param("agencyId") String agencyId);

    /**
     * 根据agencyid查询企业相关信息
     * @param agencyId
     * @return
     */
    AgencyInfo queryAgencyInfoByAgencyId(@Param("agencyId") String agencyId);

    AgencyMinsDto getAgencyMinsById(@Param("agencyId")String orgId);

    /**
     * 根据orgId是否支持芝麻免押
     * @param orgId
     * @return 芝麻信用免押标志 0:不支持 1:支持
     */
    Integer getZhiMaCreditFlagByOrgId(@Param("orgId") String orgId);

    /**
     * 查询已开通芝麻信用的机构id
     * @return
     */
    List<String> canUseZhimaCitys();


     List<AgencyInfo> queryAllAgencyInfo();

    /**
     * 统计机构总数
     * @param orgId
     * @param agencyId
     * @param cooperateStatus
     * @return
     */
     int countAgencyInfoNum(@Param("orgId") String orgId,@Param("agencyName") String agencyName,
                            @Param("cooperateStatus")Integer cooperateStatus);

    /**
     * 根据条件查询机构列表分页
     * @param orgId
     * @param agencyName
     * @param cooperateStatus
     * @param page
     * @return
     */
      List<AgencyInfo> queryAgencyInfoByCondition(@Param("orgId") String orgId,@Param("agencyName") String agencyName,
                           @Param("cooperateStatus")Integer cooperateStatus,@Param("page") Page page);

    /**
     * 获取当前最大的agencyId
     * @return
     */
    String getMaxAgencyId();

    /**
     * 根据机构名查询
     * @param agencyName
     * @return
     */
    int countByAgencyName(@Param("agencyName") String agencyName,@Param("agencyId") String agencyId);


    /**
     * 根据条件查询机构列表
     * @param queryAgencyListConditionInput
     * @return
     */
    List<AgencyInfo> queryAgencyListByCondition(QueryAgencyListConditionInput queryAgencyListConditionInput);

    /**
     * 根据authId查询合作状态下的机构
     * @param agencyId
     * @return
     */
    AgencyInfo selectByPrimaryKeyAndStatus(@Param("agencyId")String agencyId, @Param("status")Integer status);

    // 通过登陆者信息查询关联企业
    String getAgencyId(@Param("name") String name);

}