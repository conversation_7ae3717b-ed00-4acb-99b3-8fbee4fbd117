package com.extracme.evcard.membership.ocr;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.extracme.evcard.membership.core.enums.CommonRiskTypeEnums;
import com.extracme.evcard.membership.core.enums.CommonSideEnums;
import com.extracme.evcard.membership.core.enums.OcrChannelEnums;
import com.extracme.evcard.membership.core.enums.OcrTypeEnums;
import com.extracme.evcard.membership.core.manager.MembershipOcrCallLogManager;
import com.extracme.evcard.membership.third.baidu.BaiDuAddressResp;
import com.extracme.evcard.membership.third.baidu.Base64Util;
import com.extracme.evcard.membership.third.baidu.HttpUtil;
import com.extracme.evcard.rpc.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Discription
 * @date 2021/3/17
 */
@Slf4j
@Component
public class BaiduOcrClient implements OcrService {

    @Value("${baidu.ocr.ak}")
    private String ak;

    @Value("${baidu.ocr.sk}")
    private String sk;

    @Value("${baidu.face.api}")
    private String baseUrl;

    @Resource
    private MembershipOcrCallLogManager membershipOcrCallLogManager;

    @Override
    public String getChannel() {
        return "baidu";
    }

    @Override
    public CommonIdCardOcrResp idCardOcr(CommonIdCardOcrReq req) {
        String url = baseUrl + "rest/2.0/ocr/v1/idcard";
        String reqParams = "";
        String respParams = "";
        try {
            // 拼接参数
            String param = "id_card_side=" + (req.getCommonSide() == null ? CommonSideEnums.FRONT.getSide() : req.getCommonSide().getSide());
            param += "&detect_risk=true";
            log.info("用户[{}]调用百度的身份证识别接口[{}]，参数：{}", req.getMid(), url, param);
            reqParams = param; // 此字段要落库，不要image这个很长的内容

            // 拼接图片参数
            param = getString(param, req.getUrl(), req.getImage());

            // 注意这里仅为了简化编码每一次请求都去获取access_token，线上环境access_token有过期时间， 客户端可自行缓存，过期后重新获取。
            String accessToken = getAuthToken();
            if (StringUtils.isBlank(accessToken)) {
                log.error("获取baidubce accessToken失败");
                return null;
            }

            String result = HttpUtil.post(url, accessToken, "application/x-www-form-urlencoded", param);
            log.info("用户[{}]调用百度的身份证识别接口[{}]，结果：{}", req.getMid(), url, result);
            respParams = result;

            // 封装应答
            if (StringUtils.isNotBlank(result)) {
                CommonIdCardOcrResp resp = new CommonIdCardOcrResp();
                JSONObject jsonObject = JSONObject.parseObject(result);
                JSONObject words_result = jsonObject.getJSONObject("words_result");
                String words_result_num = jsonObject.getString("words_result_num");
                String risk_type = jsonObject.getString("risk_type");
                if (words_result != null && StringUtils.isNotBlank(words_result_num) && !"0".equals(words_result_num)) {
                    // 正面信息
                    resp.setAddress(getString(words_result, "住址"));
                    resp.setIdCardNo(getString(words_result, "公民身份号码"));
                    resp.setBirthday(getString(words_result, "出生"));
                    resp.setName(getString(words_result, "姓名"));
                    resp.setGender(getString(words_result, "性别"));
                    resp.setNation(getString(words_result, "民族"));
                    // 反面信息
                    resp.setIssuedBy(getString(words_result, "签发机关"));
                    resp.setIssueDate(getString(words_result, "签发日期"));
                    resp.setExpirationDate(getString(words_result, "失效日期"));
                }
                resp.setRiskType(CommonRiskTypeEnums.getByType(risk_type));
                return resp;
            }
        } catch (Exception e) {
            log.error("用户[{}]调用百度的身份证识别接口异常，req={}", req.getMid(), JSON.toJSONString(req), e);
            respParams = JSON.toJSONString(e);
        } finally {
            membershipOcrCallLogManager.addMembershipOcrCallLog(req.getMid(), OcrChannelEnums.BAIDU, OcrTypeEnums.ID_CARD, url, reqParams, respParams);
        }
        return null;
    }

    @NotNull
    private String getString(String param, String url2, byte[] image) throws UnsupportedEncodingException, BusinessException {
        if (StringUtils.isNotBlank(url2)) {
            param += "&url=" + URLEncoder.encode(url2, "UTF-8");
        } else if (!ObjectUtils.isEmpty(image)) {
            String imgParam = URLEncoder.encode(Base64Util.encode(image), "UTF-8");
            param += "&image=" + imgParam;
        } else {
            throw new BusinessException("图片数据不能为空！");
        }
        return param;
    }

    @Override
    public CommonDrivingLicenseOcrResp drivingLicenseOcr(CommonDrivingLicenseOcrReq req) {
        String url = baseUrl + "rest/2.0/ocr/v1/driving_license";
        String reqParams = "";
        String respParams = "";
        try {
            // 拼接参数
            String param = "driving_license_side=" + (req.getCommonSide() == null ? CommonSideEnums.FRONT.getSide() : req.getCommonSide().getSide());
            param += "&unified_valid_period=true&risk_warn=true";
            log.info("用户[{}]调用百度的驾驶证识别接口[{}]，参数：{}", req.getMid(), url, param); // 日志早点打印，不打印image
            reqParams = param; // 此字段要落库，不要image这个很长的内容

            // 拼接图片参数
            param = getString(param, req.getUrl(), req.getImage());

            // 注意这里仅为了简化编码每一次请求都去获取access_token，线上环境access_token有过期时间， 客户端可自行缓存，过期后重新获取。
            String accessToken = getAuthToken();
            if (StringUtils.isBlank(accessToken)) {
                log.error("获取baidubce accessToken失败");
                return null;
            }

            String result = HttpUtil.post(url, accessToken, "application/x-www-form-urlencoded", param);
            log.info("用户[{}]调用百度的驾驶证识别接口[{}]，结果：{}", req.getMid(), url, result);
            respParams = result;

            JSONObject jsonObject = JSONObject.parseObject(result);
            JSONObject words = jsonObject.getJSONObject("words_result");
            String wordsNum = jsonObject.getString("words_result_num");
            String risk_type = jsonObject.getString("risk_type");
            if (words != null && StringUtils.isNotBlank(wordsNum) && !"0".equals(wordsNum)) {
                CommonDrivingLicenseOcrResp resp = new CommonDrivingLicenseOcrResp();
                resp.setLicenseNo(getString(words, "证号"));
                resp.setName(getString(words, "姓名"));
                resp.setGender(getString(words, "性别"));
                resp.setAddress(getString(words, "住址"));
                resp.setBirthday(getString(words, "出生日期"));
                resp.setDriveType(getString(words, "准驾车型"));
                resp.setNationality(getString(words, "国籍"));
                resp.setIssueDate(getString(words, "初次领证日期"));
                String startDate = StringUtils.isNotBlank(getString(words, "有效起始日期")) ? getString(words, "有效起始日期") : getString(words, "有效期限");
                resp.setStartDate(startDate);
                String endDate = StringUtils.isNotBlank(getString(words, "失效日期")) ? getString(words, "失效日期") : getString(words, "至");
                resp.setEndDate(endDate);
                resp.setIssuedBy(getString(words, "发证单位"));
                resp.setFileNo(getString(words, "档案编号"));
                resp.setRecord(getString(words, "记录"));
                resp.setRiskType(CommonRiskTypeEnums.getByType(risk_type));
                return resp;
            }
        } catch (Exception e) {
            log.error("用户[{}]调用百度的驾驶证识别接口异常，req={}", req.getMid(), JSON.toJSONString(req), e);
            respParams = JSON.toJSONString(e);
        } finally {
            membershipOcrCallLogManager.addMembershipOcrCallLog(req.getMid(), OcrChannelEnums.BAIDU, OcrTypeEnums.DRIVING_LICENSE, url, reqParams, respParams);
        }
        return null;
    }

    /**
     * TODO 添加缓存
     * 暂时每次都重新获取
     */
    public String getAuthToken() {
        // 获取token地址
        String authHost = baseUrl + "oauth/2.0/token?";
        String getAccessTokenUrl = authHost
                // 1. grant_type为固定参数
                + "grant_type=client_credentials"
                // 2. 官网获取的 API Key
                + "&client_id=" + ak
                // 3. 官网获取的 Secret Key
                + "&client_secret=" + sk;
        try {
            URL realUrl = new URL(getAccessTokenUrl);
            // 打开和URL之间的连接
            HttpURLConnection connection = (HttpURLConnection) realUrl.openConnection();
            connection.setRequestMethod("GET");
            connection.connect();
            // 获取所有响应头字段
            Map<String, List<String>> map = connection.getHeaderFields();
            // 遍历所有的响应头字段
            for (String key : map.keySet()) {
                System.err.println(key + "--->" + map.get(key));
            }
            // 定义 BufferedReader输入流来读取URL的响应
            BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String result = "";
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
            /**
             * 返回结果示例
             */
            System.err.println("result:" + result);
            JSONObject jsonObject = JSONObject.parseObject(result);
            String access_token = jsonObject.getString("access_token");
            return access_token;
        } catch (Exception e) {
            System.err.printf("获取token失败！");
            e.printStackTrace(System.err);
        }
        return null;
    }

    private String getString(JSONObject jsonObject, String key) {
        JSONObject object = jsonObject.getJSONObject(key);
        if (object != null) {
            return object.getString("words");
        }
        return null;
    }

    public BaiDuAddressResp getBaiDuAddress(String address) {
        String url = baseUrl + "rpc/2.0/nlp/v1/address";
        Map<String, Object> param = new HashMap<>();
        param.put("text", address);
        String accessToken = getAuthToken();
        try {
            if (StringUtils.isBlank(accessToken)) {
                log.error("获取baidubce accessToken失败");
                return null;
            }
            log.info("百度地址识别参数：【{}】", param);
            String result = HttpUtil.post(url, accessToken, "application/json", JSON.toJSONString(param));
            log.info("百度地址识别结果：【{}】", result);

            if (!ObjectUtils.isEmpty(result)) {
                return JSON.parseObject(result, BaiDuAddressResp.class);
            }
        } catch (Exception e) {
            log.error("百度地址识别异常：", e);
        }
        return null;
    }

}
