package com.extracme.evcard.membership.core.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.extracme.evcard.membership.core.dto.CardPauseLogDTO;
import com.extracme.evcard.membership.core.dto.CardPauseQueryDto;
import com.extracme.evcard.membership.core.model.CardPauseLog;

public interface CardPauseLogMapper {
	int deleteByPrimaryKey(Long cardRecoverSeq);

	int insert(CardPauseLog record);

	int insertSelective(CardPauseLog record);

	CardPauseLog selectByPrimaryKey(Long cardRecoverSeq);

	int updateByPrimaryKeySelective(CardPauseLog record);

	int updateByPrimaryKey(CardPauseLog record);

	CardPauseLog selectPauseLogByCardNo(String cardNo);

	int updatePauseStatusByCardNo(CardPauseLog record);

	int updateAllPauseStatusByCardNo(CardPauseLog record);

	CardPauseQueryDto queryPauseLogByCardNo(String cardNo);

	/**
	 * 根据authId查询会员卡暂停日志(分页)
	 * 
	 * @param authId
	 * @param rowStart
	 * @param pageSize
	 * @return
	 */
	List<CardPauseLogDTO> queryCardPauseLogByAuthId(@Param("authId") String authId, @Param("rowStart") int rowStart,
			@Param("pageSize") int pageSize);
}