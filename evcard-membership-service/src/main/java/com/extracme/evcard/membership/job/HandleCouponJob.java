package com.extracme.evcard.membership.job;

import com.alibaba.fastjson.JSON;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.extracme.evcard.elasticjob.ElasticSimpleJob;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.dao.UserCouponListMapper;
import com.extracme.evcard.membership.core.manager.MemberDocumentManager;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.model.MembershipBaseInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;


/**
 * 会员 处理新人优惠券（只刷一次）
 *
 * <AUTHOR>
 * @date 2022/9/21
 */
@Slf4j
@Component
@ElasticSimpleJob(jobName = "evcard-membership-handleCoupon", cron = "0 00 16 20 7 ? 2099", description = "新人注册优惠券处理任务", overwrite = true)
public class HandleCouponJob implements SimpleJob {

    @Autowired
    private MemberDocumentManager memberDocumentManager;
    @Autowired
    private MembershipInfoMapper membershipInfoMapper;
    @Autowired
    private UserCouponListMapper userCouponListMapper;

    @Value("${licenseFirstAuthTimeLimit:2022-09-16 22:00:00}")
    private String licenseFirstAuthTimeLimit;



    @Override
    public void execute(ShardingContext shardingContext) {
        //List<String> mobileList = Arrays.asList("17805588724","15861921581","13776637600","16521274498","13382346838","19155128988","18680541773","17366020736","15895819847","18372082549","15907779190","15751884711","17891191824","16620178716","18356351371","13022571347","18520654497","18133306150","15202770380","15518899077","15862243182","17314977547","18753753778","18277525930","18018190575","15093543883","18061853146","13067870988","17852026770","18017248845","13085506969","18262711931","15262028681","15906611464","17874519421","13524337737","13634165835","18680748066","18523044026","13750862822","17760997478","19982089317","15161216988","15595777025","18502955575","15934609963","15938712286","15999921980","19962770188","15508118613","18205141409","13683211241","15982463860","17313614090","15651928802","18455908377","18631051346","18636919192","18168815858","13961971344","15935616100","13585984189","13855942127","18261977158","15623113150","18655010701","13880145686","15665118187","13735571298","19962005596","18951638481","18921708707","18996367876","13270854880","13862188379","13770057056","13101354199","15039673064","13365309638","13525171121","17603248698","17314482446","15920866757","15901697407","15021314540","18267772173","17354169780","16521274502","19816282825","15821890020","18712402349","13591997009","13529448108","18260213496","15328015999","15585240713","13816979376","17714384017","18901586615","18362710243","13935355390","18851154968","13764331364","18725398995","18115888361","15295704317","15330547045","18917804278","15256640241","18666634440","15261472122","18862366532","17552746519","18955969271","18913494433","18398506610","17389850606","17762858937","13776794400","13610292831","13768292382","15862991887","18860896193","18756688223","13388357961","17826079733","15973150746","18900625855","15077994542","15399272672","19897886188","18752026879","15859878250","17712385583","19939359534","17302590190","17715278496","18523619621","18017585432","17625601943","15830100877","13162753728","18602021568","13638676115","13784056114","15959783352","15191798865","13682076083","19538263023","13328002327","18664589117","17878915628","15380129915","15335045258","13655290571","18684204228","13862535545","13916931106","19357578515","15627772767","15380392127","15010386384","13951854718","13250803670","19890750044","13182935170","18276960764","13917496220","18081167836","18860957076","17826019131","18751416521","15099596655","19962536992","13951104821","13631848230","18136366725","13348114262","15807091186","16521502163","18860960167","18225860108","13225117723","17521006944","15821780719","17636305640","13533888890","13761255664","15012468438","18612615217","13179399560","15895817397","15805290535","18331039293","17368761102","13675203702","13543439768","18451718625","18262883955","18082098945","13815480168","17368699131","17715222833","13913427215","18752017076","17552735106","15156793227","15259985909","17705554118","15288200632","18975818261","15255298828","18221121145","13813930481","18851413840","15556043823","18862213603","18107974256","18220550822","17898600085","13918704556","13291316007","15365463559","18654103089","18653160255","18293675521","13400557017","15157232138","18211940669","17325652687","15610576020","18806102651","19812642521","15308381607","15751257515","15883846237","18616010230","18752069937","18792191757","17714088111","13255273777","13776939035","15080730064","13706166254","18607926017","15363016783","18752010529","13359092989","13795236822","15152017052","15343405005","15988333605","18661494349","19379172975","13692909208","13600641636","15756613140","15067339240","15171500065","18708408996","18268410047","13713916153","13636563635","15000913673","15816228089","18851025807","18134588596","13952431389","18376699620","13721043716","13751145564","18010586729","18261005902","18362167282","13505106664","18952710289","13335246021","13434897560","13699557019","18651857752","18010261289","15250161955","18861459775","15880707076","13584662725","17333052621","13122892002","18020863597","13916152427","15855571727","17558727024","18020455008","18532694205","15766246935","13814006314","13072507753","18563514245","15651877008","15862875976","13915499524","15290111172","19939513131","18785109831","15155364329","18112880229","13093697031","13381810527","17081376512","13825147757","13701473123","13604572832","18375574608","19330904814","18884396775","18245117203","13951027783","18587978404","18226780832","19807490908","18211716718","15901838798","13305646987","18318978006","19966528916","13617999963","15189105151","16651150121","15280570257","18019090613","18913829358","18552191419","15261862474","18252598683","16521502165","18018463870","13681960785","13122286397","18649010266","18871163325","15198005178","15557456780","13675101099","15261165827","18699353627","18671985994","13085383397","13666993692","18616845673","18710257950","13073474521","18860852143","17601213828","18317634084","18001410811","17802161050","18756524149","18351926507","13616937134","18955391334","13327802237","13610019960","13585632620","13057496685","15851845888","15240665818","18355017722","13921977065","18952987502","15952011750","13478901271","18552644546","18262623237","18794814234","13687227513","18136273099","18317171410","15690888304","15996123301","17805595421","19992711932","15062221592","17718138326","17754847511","15357159345","15838117290","18112928288","19855903823","17396808097","18356531902","15051187277","15252479871","18918365936","13960542436","18784219763","18918376373","18370881928","13307659739","18259786675","13965517026","17608211794","18856238963","18451975934","13913274476","15250955088","15618587557","18915118677","18852047114","17621121239","13279239803","13801458360","13965514876","13357700522","19937060596","13851591896","15252047355","15626206688","18005820262","18687832582","15221004191","18394077521","18715096755","13286214064","18871502893","13875722218","13814874225","13965004832","18156618851","15240494970","18919628053","15165480959","15154849010","16521274506","18912807771","13805219377","19983651162","18351924968","13512126271","18187521627","18695981651","19526591167","17155161718","17730033803","15916236213","15956916138","13733082772","18198962270","13968941199","13564832057","13507591412","18795887899","18621618361","18000127707","13681815640","17351295546","15052939539","18555337163","18206155405","13623715717","15101050583","18275417784","14745157065","15216837390","18602119906","13273812820","13100682623","15952440752","13951869975","19836532345","17310263079","18595460491","18394535127","13962951848","13160035206","18191317608","15922910692","13401993648","15989659821","13851958586","13813824217","18816285030","18744932007","18684946186","15011654005","19560863753","13950915762","19802170581","13585713928","18555181064","13640314733","18751882466","15221559442","13701401211","13101849330","17625316420","15650759578","15626869787","13052948576","13857289146","17826595828","13611305002","15105198534","18896730037","15821559995","15010210424","13851893276","13708394239","15190921744","17717438884","18895702775","17772314021","15055117697","15050537899","18368348205","18851075352","15177188804","15055761717","15996913256","13952055776","18912101931","15205611016","19156691101","18851773930","15195916562","13054317878","13182965356","18701920446","17802640695","18061739563","18061549280","15907160983","18955223201","15906705330","15975705521","18725594667","13039660020","13225862911","18993162977","15678787070","13338312818","15626194530","13796013615","18277535470","13474707152","13776616231","15054483736","15195800357","15951873220","13218015172","18474379001","18351826624","15105158119","13058838795","18333933311","13676680615","17665137767","13814006642","17321016766","13681928980","15957133529","17513376382","18592066973","13002567737","18255192409","17361988386","13911680120","16521502168","18756643613","15371330814","13918355916","13907320010","18898873742","13770789806","16521500650","15758039024","15522028699","17314825785","18061223175","18951624882","15755923435","13270337182","17521601109","19947662605","18804620262","13591813765","17385805035","17606570527","15366480871","18068863928","18258095640","15864073555","17602544873","17621970687","17826084096","15897023779","17785337818","19878533547","15660461468","18170011481","17680696951","17601549947","19965818885","17361872365","18317121280","17620789447","18365280361","18061689552","13304178143","15996974243","17796645093","18936016473","13912918503","18001610808","15802186508","18066077205","19538326596","13236323825","13889543673","17350338629","13965368185","13809000509","13866426309","17361586763","13382788325","15295778740","13913860028","17721528005","15978849948","13720252023","18605568803","17638923059","17605091425","19862873102","18321212665","19825762699","13545887858","13121016308","15854851480","17769606167","18018578251","18356611588","13187304446","13430509010","13541372197","18075985710","18017362832","17751711441","18855998002","15059017560","13611566114","15720813076","15055996456","18560051966","15278073759","13939740479","18516250789","13738563872","15869891743","17717412190","18005592702","13851530950","15521383026","15803716939","18382657303","18082955161","17368149624","18060134920","17603397077","13576991448","17366109084","15022094991","15982316806","19529354323","16605144108","15618788721","17838625090","13299522808","13262235749","13981771656","13803499487","18356630306","17751898291","18058086829","18888195299","15662564688","13758490330","18065851007","18012942952","18251827758","18156020404","18360359529","13797086611","18123766533","18109432817","18235933203","17337874355","15151803040","17770230593","18115139091","13228820850","13913833299","15181265991","18077625003","13376090627","18021414916","15651783521","18124428188","13265338027","18344598981","13186983913","17781714464","18521523515","17610517925","18626420805","13083596852","18805176682","13376026331","17683090142","17834498279","13538129418","15657292559","13913132231","13962636028","18952968986","15951657789","15222749486","18209497129","18235105191","18859888901","13802927732","17621790258","15835552884","15651554310","13770760505","13636022467","18799216813","15800562599","18672522228","13220904939","18591885167","15365015808","17606120806","13478056709","17625948602","18315336638","15862976217","17767732854","18258181585","15920207879","18769621109","15850628537","15950921054","19851414999","13225089915","17712860891","17605878026","13558871372","15821740393","18094234101","13512505522","15215609069","17712910136","17775277036","17681263540","18268379772","15069451692","18056237847","15188350103","13109376210","17714418121","15309014134","17386035263","13266100212","18584850652","19551000430","15167100923","18144264578","18862012343","17628292412","17612568876","13210119538","15762479889","17788695369","18362984616","18751525355","19921131612","18650930720","18752030084","13781922702","15659112101","17539510667","13427518031","13020138666","17759856395","17609206187","18950935483","13952152919","17280112475","18782242145","14790567807","15366018678","18053271103","18994002886","18260630601","13773008942","17766073812","15216850223","17327998063","17756972340","13507628251","13906084622","18608769239","13728795985","18308359789","18260263843","18151282272","17788696697","15208347622","17770887770","18252889067","17838170190","13151683990","17637299099","18662919867","13961058598","18936872111","18616983961","17673731999","15038871495","13817280364","13296686802","18761251051","18795881190","18921920760","18085508638","15670021017","19533294347","13861941664","13186482702","13851943551","15088667864","18547588077","18093079863","16623130090","13340014448","18950629615","18627137193","13646370954","19852822881","17602532149","13890206733","18806084546","17602828617","13951589926","18515990176","15380363900","15872082918","17695965850","13181380123","17122148548","18717815264","17623427084","18851997515","15154515565","19895591093","18757323315","18193218098","18455097850","17674318883","13760177758","13046607326","15951981250","15000209287","15736836980","13588430847","15618096149","15638838586","15629008930","19529952583","13990126764","18887041560","17122148559","18795177807","19990991799","15064452297","18972137333","15179939532","13723825933","15092199020","13017578928","16755753902","15201714860","15105167588","13729303291","18870001588","13398980417","13370025653","13501948124","18652041621","19707201361","18126829051","15189143935","13608004332","13541099200","18582587605","18981853184","13427097141","18583920580","15189103737","14704566333","18510994823","13145521009","17855360641","18179257929","18701991956","18683088945","15810746591","13276607885","13585433170","19834541429","19983059559","18244981958","18731323328","15021791966","15951960911","17788361129","13062585286","15357548180","15922595052","15240287375","18551871568","15962110232","19850813279","18917521855","15949090051","18168030439","13952038117","18840487862","13170302878","13818615954","18991777110","13817197897","17712051685","18962583058","18212789399","13342704759","18765105946","18852865220","13636312350","16675346568","13285218432","15365123707","15990330430","15288372849","18552575036","13705669492","15717114673","18262883310","18788861912","18014833635","13174439771","18119939378","18913965178","15617322201","19826294070","15871730092","13016968930","19159086664","15013626257","13528815601","19890065230","15536152557","15670932136","18816928835","15651375859","15223389874","13776574320","18521202282","18881263019","13814711863","15850733022","18752069187","15152340356","18655567850","15996362856","15548793257","17825395984","15884156347","17855959472","18672840910","15692696629","18018277392","13952011800","17601484450","13851490128","18168118530","18638801042","15357393798","15333799167","13057530891","17077058454","13585877430","18862961105","13112140993","18070293500","16521502029","18888199778","18130513860","18994955023","13818812789","15123189321","17314989835","13304654480","13505956629","18296520755","18344881164","18501694280","16601111064","18520190824","13901582637","18618296663","18608029820","15681123317","13813087848","19805186119","15851919535","13512110601","18352656562","15000836359","13682694627","17687938931","18506577857","17771555521","13135572336","15874088724","13152222752","13753932324","15170092950","15571372084","17609295343","15723628481","15014465584","13331085753","18855988092","13914495200","15931660228","17861123626","13297480705","19957062526","17696712989","15523896651","15122870436","18120140551","18315050621","15996605711","19965056246","15155159372","13156689512","13287733038","18655523524","15255293351","15808063955","15951825202","19902062284","18852869192","18112589946","18905196305","15246805056","18180526810","15251950596","18602193377","17502315849","13154877004","18163515408","17751239274","15072277941","19966585766","13956277370","13605997792","17315363334","15313555762","17361904209","13912953425","18551664083","15145385550","18352850937","15109682026","17339829556","18362080092","18061629457","18358920157","18586124812","15343371520","13660447566","18856638881","15800438927","18942502138","15720805513","15727192356","13156505800","15823526811","13969287352","15689878503","18760276826","18161542642","13908692742","15808924470","18351966935","15868728170","18718708110","19972896080","13688977994","15371005281","15026655481","17705168655","18884233759","17755864063","18752530476","17834621848","13347902615","18068830600","17720210511","17816113245","17601447223","18505629090","13372122588","17520496419","15011033056","15052930837","18821251505","18652076540","13851550955","13761173858","16650825898","17795757021","19852512919","13770509199","17307891582","13887017030","18505663273","17378110487","15962208932","18595657265","18351997890","13122208705","17770843096","13405587402","13140636868","13588833444","18020264242","18801296719","17314491520","13956095231","13360578171","18098580611","15024378558","15099800516","18770223792","18652294678","15871417202","13856655000","15065002476","18875540498","13852042322","17867415281","15128041273","17798246626","15079557519","15126999961","13780822547","13957329515","17397998015","18641235633","18556841993","13849151149","18205005424","13845345985","18855955154","13197940627","17688307700","13355596607","17636339894","15627218094","15252272216","15852225279","13286840206","13117022665","18407815235","17857573537","17321270177","18886044572","13026862168","19812245103","18061257079","13507592070","18363081259","13625289231","15358108087","18744571119","13764766616","17600977962","13915941866","15996355759","18852870037","18630014748","15555599117","17751044931","17750681347","18014489781","18112657708","13750739219","13622943815","17306475845","16650316915","18506281121","13149624886","15862531818","18321783010","18166129365","13735987315","13852055994","17319180410","13057168633","18589600310","19106126372","15957124482","18862155535","13890853401","15968001681","13114467907","17375583858","17718726646","19537570375","18511637675","17855905921","13851989703","15968893240","18552235598","15515770606","18212167787","15850596215","18307799469","13772766883","15315707849","13023225508","13143527817","13851717812","19374022152","17521787052","17696848550","17682319965","13672142776","18549852686","13581855859","15856736445","13006163483","18205090340","17077058455","18052028747","15961702774","15007870640","18392956220","13073445301","15689833805","13681901917","13113005977","17626988044","15272792289","17826278439","13556185706","15272793916","18480604228","13911197995","16755753895","13632228562","18855226409","19926094540","15947306181","18156083201","18262609128","16638946697","15168866292","18956287012","18896669231","15051441830","18994825555","17798556547","18717899770","13303831726","15847760378","13220918216","17712486242","15996298648","13345851314","18168608330","13247569870","16651074197","18752314456","19124331401","18217621454","19966792794","18005576938","15156823123","15807973103","15261222661","16521190087","15111165891","13974866770","13638658151","15215149882","18850511687","18182121836","18351890202","15914537909","15314160943","13725373835","15132677446","17856361662","18737552199","13681252537","18321573457","13145152555","13852410048","15088997955","18379740215","18962050936","15190585030","13022543283","15824062146","15349863596","13925646496","18862970319","13025903741","13226161531","18913819240","13730695444","19952472775","15956394431","15937131221","18905112027","18202520068","17768303936","13856633920","19154948122","17312121492","17512509027","19847360120","13823146932","19898889513","13655191967","18437187686","13912988600","18729728161","15999942024","13862893817","17612397831","18245312333","18851842230","15272245144","18676161641","18362597097","15195863676","18715255210","17858198636","15102124653","13632929763","15851868095","18221138093","13056206865","13611807516","18157220453","13301593098","18370647378","13767518866","13362031418","18351969317","13631300340","18194192532","13122522707","15695993305","18368728181","15990120975","17551041044","17704629271","13912936920","18362916227","17356686236","18201801405","13508390037","13612478205","16609096870","15366578518","15648744761","19166492617","18549827352","18351004221","13682285214","13222231211","13150137668","15862920614","18712352672","13755678542","16755752050","19962017589","18952028031","18652733891","19951263006","13567371229","18226920062","18356028910","13883152529","13017699139","18777136974","15006518690","15329684206","18795991893","18678543875","15380446623","19822636693","17509175587","18696721265","13682206369","19524978295","13278717295","13376052824","17363381670","15221079261","15527831027","19851475415","13353663970","13636679653","18856267967","18256624261","17855363353","15380753161","18955958371","15160718035","18005556607","15037044850","15356733281","18692262692","13918686539","17623636558","17755924455","18686818805","17355201759","17601266180","13912428100","18837560981","19190249269","13993156090","15026955625","18547543614","15683395433","18158833300","15588305425","13703987692","18972575708","17661111992","13218881009","18779797700","13290989391","13758259765","18051531088","18382772671","15090666402","15217969386","18912970552","18953210324","13955977298","17372797009","18951071172","18811726462","13814528760","13067299971","15693110903","15005166946","18066186083","18172886665","17316009686","13685163128","15090164755","17740690593","18705625196","17374087426","18036251688","17625116199","13361214969","18806352982","13913946314","18842680016","18951358712","16655033581","18952973985","13751875672","17721510730","19157959168","15152151163","15050893393","18061783857","17361987966","15528327139","15295197278","18118297311","17262639618","13170033233","18796881771","19905170510","17312211026","13818009274","18183103668","18937966902","13814110881","19844558418","13404169083","17357121937","18611116292","13189007098","17705145985","13100964277","13764635665","15024450597","19908041816","18065856739","13451917893","13085050529","13635598991","15190935192","13770688801","13298114610","13795388907","13120688329","13675168687","18914594489","13859176911","15929907067","19828449589","13917755518","18255055474","17625579705","13584091787","17696724068","15026538075","18303051097","18913039906","18252001735","19138172879","18552471882","18888043209","15915916770","13058272677","18355565837","17630357692","15900400696","13004360925","17754092821","18053358442","18516696106","18915915918","13814145123","13162099133","17712688935","18976041314","15660956372","18617103412","15928739395","18505672303","17607967531","15895825386","17802590332","17644471111","19825009525","18800140200","13755348303","15026703141","17633870374","19924296582","13870910706","17756676515","15955729291","15841320185","18867505470","18371077127","17766101255","18255980174","15995261787","19987114035","15605173167","18793459807","13584047058","15251702210","18656621879","18500518621","13564864754","13067017806","18621537267","13546285866","13204067025","13114350511","13145207956","13915027262","13976741609","18217626864","15765575147","13992112234","17714381226","15967379030","15072420525","13379055778","17502172900","13027756059","13700515673","17666496214","18312194788","17729902146","18851127108","15962662602","18516518520","18362972391","18262420050","13776523455","18019945791","13016891503","13401075779","13775318700","13913321635","18142026396","13621597146","18930173379","13852996979","13100612390","18967335191","17851877702","15212613143","13756558124","15871736773","13859194926","13142821030","18297966074","18817281598","18559210135","18000185005","17372760008","13023408756","15119911649","13952940935","15724808891","15751407342","17696946422","15198334967","15855174534","13883158997","17358594595","17698096679","15674840112","18371331558","15905150660","18857616258","15651813577","15000113763","18065869039","13222073965","15152813520","13207386088","18305289687","13815377421","17673427384","17625657405","19851525230","18082927101","17315445796","13770885152","17625951306","13141985457","15210599055","18936043736","13343102573","15352864280","15251953698","13913872721","13913843178","18576699584","17368519862","18156812832","15173395802","13671762676","13622298100","18236691361","18930302810","18514555887","18801130429","15897958859","18806086022","17625569229","17621324390","17551010875","13641310201","15996330208","18877885788","17863935552","18662204996","15634855900","13182507208","15059916382","17633396831","15150971950","15655213868","17772278137","13771629973","18351813141","18261741071","13951984570","13546316789","18510238168","13264663210","18810032503","17560608526","15150566356","13105628284","13411910461","18955119158","16621535116","15150655529","18346302355","13355173090","18964648779","18336933391","15651831837","16619783520","15077878830","15923906990","18676542101","15295798620","18652006307","13915596459","18346012149","15392320195","15061818798","13816205739","15303829529","18851760398","17625910971","13222080085","13072573656","13916833074","15850507383","19825936268","18130168994","18321145193","15872396061","15968020769","18656969960","15178226056","13150438888","13732366918","18362944172","18120180973","15021393496","19857173132","15215594923","15527251103","13989151717","15501333849","18388252835","18502180781","15580588536","13865940151","18576750978","17601287630","15151096656","18053317277","15395983208","18860874179","13370093971","18351270262","15366073195","18244047776","15110489332","13661905111","17754472520","13714819307","13955952169","18122419561","15821205762","13020193689","17647309836","13958011852","15735343347","17626535228","18018022237","13024545855","15834240413","17854170406","13961084321","13186581115","18616036666","15173156242","17387314710","18252321717","13653668554","15862632815","18618436538","15312088555","18613373846","13162177238","15507433357","18580405670","15572134297","13705167613","13515015287","18512463228","15038199580","17505209178","18656317143","13764429642","13952533347","17639222688","18061055077","13564273229","19123709551","17368118908","13280526666","13701403901","18607561854","13852911878","18306102812","15960226903","18145587171","18795418967","15921553396","13249015985","18257534403","17751280613","18357515037","17855674532","17644255374","17312248108","18955667660","13841756888","19517803339","18549808873","18777882607","15651725282","18225501213","18976641209","13859618589","17601542405","18819144686","13078677888","17375953036","15365626753","16712506844","18551974250","17852733697","15800285857","15906171977","13901611819","18865387378","13391022867","13186543629","13983258200","15261807520","13770514294","15103882085","19855977573","13913273390","13886155653","13706214536","18204526397","15295787278","19949179186","13512160059","17689701065","15121142765","15038137687","18852853001","15851884713","18643743678","17537617953","18052056186","18800296082","17761735667","15850605479","18505239002","13128516731","18601346885","15818819236","17752540845","13302172303","13382066558","15180287582","15137192014","18826415574","18018020735","15575584751","13913986394","13399599641","18851427837","18802097847","18846921283","18206100105","17506508745","13022543318","15996888163","13588431487","13601453085","18751803030","13433573311","13515259828","19991205749","13564671054","13813878978","13369688840","18721064792","18738616252","13776656363","18168063835","15252772603","18167031325","13859262218","13141936392","18897555993","13908833129","18651714359","15801935913","16755752062","15651705819","18625163532","18551659571","13023187853","13802972137","13043397337","17199917407","15305107717","13452166642","18936020930","13258111247","15951011197","17721569891","13336043799","13218900636","18983190897","15312888519","16619964627","17309089058","15797878521","13913870513","15603371518","15171713461","15506985352","18114453301","13860535126","13179693108","13072917502","18752175771","16712565024","17708233033","18622282795","15392323002","18601407588","18094498301","15720600592","13275112437","15321332053","18252020142","17745552083","18994374660","13645163546","17737931806","13598645118","15252707628","18261931026","15879848380","18752132732","18061736597","18481822123","13609051166","18994266117","18656521271","13640416461","13515592813","18859860539","13142829220","16621756628","13062866583","13615606963","13250299808","17884173093","17602134492","18841584565","18806570907","19955901168","19194256012","18914453038","18556767318","17366086793","18505989983","13459888369","18255926368","18757999407","13828444191","17602549793","18023704126","18767796582","18710878162","17721516621","18625156598","18136658628","13686529938","17543025399","15150597551","13926791059","18936620091","18656659952","13357729049","13914600352","18336333574","19558930601","13962725011","15651830121","17612503732","18762029325","13339667762","13852865087","17327994755","18024773771","13914613313","15956615386","13235783861","15055376009","18344896162","16587237555","15669059315","15738264834","18604571237");
        List<String> mobileList = Arrays.asList("18398506610","18661494349","18259786675","17155161718","13813824217","18555181064","13054317878","17314825785","17680696951","17796645093","17721528005","13262235749","18065851007","15151803040","13265338027","18151282272","19990991799","19159086664","15152340356","18506577857","19965056246","18352850937","15868728170","18505629090","13026862168","13735987315","13851717812","15961702774","13303831726","19951263006","13376052824","18705625196","17357121937","13162099133","18516518520","15751407342","13222080085","18255926368");
        log.info("HandleCouponJob新人注册优惠券处理任务begin！shardingContext={}", JSON.toJSONString(shardingContext));
        for (String mobilePhone : mobileList) {
            try {
                MembershipBaseInfo membershipBaseInfo = membershipInfoMapper.selectBaseByPhone2(mobilePhone, 0);
                if (membershipBaseInfo == null) {
                    log.info("HandleCouponJob手机号为[{}]，未查询到用户信息", mobilePhone);
                    continue;
                }

                String regTime = membershipBaseInfo.getRegTime();
                Date regDate = ComUtil.getDateFromStr(regTime, ComUtil.DATE_TYPE4);
                Date limitDate = ComUtil.getDateFromStr(licenseFirstAuthTimeLimit, ComUtil.DATE_TYPE1);
                if (limitDate.before(regDate)) {
                    log.info("HandleCouponJob手机号为[{}]，注册时间[{}]，是新用户，不需要处理", mobilePhone, regTime);
                    continue;
                }

                String authId = membershipBaseInfo.getAuthId();
                String mid = membershipBaseInfo.getMid();
                if (StringUtils.isNotBlank(authId) && StringUtils.isNotBlank(mid)) {
                    boolean hasOrderFlag = memberDocumentManager.hasOrder(mid, authId);
                    if (hasOrderFlag) {
                        log.info("HandleCouponJob手机号为[{}]，之前下过订单，需要处理", mobilePhone);
                        String nowDateString = ComUtil.getFormatDate(new Date(), ComUtil.DATE_TYPE3);
                        int count = userCouponListMapper.batchInvalidatedCouponByUserCouponSeq(authId, "System", nowDateString, "75045690", "1014", "20220916220000000");
                        log.info("HandleCouponJob手机号为[{}]，处理新人优惠券张数[{}]成功", mobilePhone, count);
                    } else {
                        log.info("HandleCouponJob手机号为[{}]，之前没下过订单，不需要处理", mobilePhone);
                    }
                }
            } catch (Exception e) {
                log.error("HandleCouponJob手机号为[{}]，处理新人优惠券异常[{}]", mobilePhone, e);
            }

        }
        log.info("HandleCouponJob新人注册优惠券处理任务end！shardingContext={}", JSON.toJSONString(shardingContext));

    }

}
