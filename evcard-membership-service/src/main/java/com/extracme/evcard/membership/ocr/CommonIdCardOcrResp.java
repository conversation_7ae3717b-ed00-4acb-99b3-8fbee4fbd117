package com.extracme.evcard.membership.ocr;

import com.extracme.evcard.membership.core.enums.CommonRiskTypeEnums;
import lombok.Data;

import java.io.Serializable;

/**
 * 统一的身份证识别出参
 *
 * <AUTHOR>
 * @date 2022/8/19
 */
@Data
public class CommonIdCardOcrResp implements Serializable {
    /* 正面 begin */
    /**
     * 姓名
     */
    private String name;

    /**
     * 性别，男 或 女
     */
    private String gender;

    /**
     * 民族
     */
    private String nation;

    /**
     * 出生日期，格式：yyyyMMdd
     */
    private String birthday;

    /**
     * 住址
     */
    private String address;

    /**
     * 公民身份号码
     */
    private String idCardNo;
    /* 正面 end */

    /* 反面 end */
    /**
     * 签发机关
     */
    private String issuedBy;

    /**
     * 签发日期，格式：yyyyMMdd
     */
    private String issueDate;

    /**
     * 失效日期，格式：yyyyMMdd，或“长期”
     */
    private String expirationDate;
    /* 反面 end */

    /**
     * 身份证风险类型:
     * normal-正常身份证；
     * copy-复印件；
     * temporary-临时身份证；
     * screen-翻拍；
     * unknown-其他未知情况
     */
    private CommonRiskTypeEnums riskType;

    /**
     * 供应商
     *
     */
    private String channel;
}
