<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.credit.dao.MmpCreditEventTypeReportMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.credit.model.MmpCreditEventTypeReport" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 28 14:08:44 CST 2017.
    -->
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="year_num" property="yearNum" jdbcType="VARCHAR" />
    <result column="org_id" property="orgId" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="event_type_id" property="eventTypeId" jdbcType="BIGINT" />
    <result column="event_name" property="eventName" jdbcType="VARCHAR" />
    <result column="month" property="month" jdbcType="INTEGER" />
    <result column="total" property="total" jdbcType="INTEGER" />
    <result column="misc_desc" property="miscDesc" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 28 14:08:44 CST 2017.
    -->
    id, year_num, org_id, type, event_type_id, event_name, month, total, misc_desc, status, create_time,
    create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 28 14:08:44 CST 2017.
    -->
    select 
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_credit_event_type_report
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 28 14:08:44 CST 2017.
    -->
    delete from ${issSchema}.mmp_credit_event_type_report
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.membership.credit.model.MmpCreditEventTypeReport"
      useGeneratedKeys="true" keyProperty="id">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 28 14:08:44 CST 2017.
    -->
    insert into ${issSchema}.mmp_credit_event_type_report (id, year_num, org_id, 
      type, event_type_id,event_name, month,
      total, misc_desc, status, 
      create_time, create_oper_id, create_oper_name, 
      update_time, update_oper_id, update_oper_name
      )
    values (#{id,jdbcType=BIGINT}, #{yearNum,jdbcType=VARCHAR}, #{orgId,jdbcType=VARCHAR}, 
      #{type,jdbcType=INTEGER}, #{eventTypeId,jdbcType=BIGINT}, #{eventName,jdbcType=VARCHAR}, #{month,jdbcType=INTEGER},
      #{total,jdbcType=INTEGER}, #{miscDesc,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, #{createOperName,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, #{updateOperName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.credit.model.MmpCreditEventTypeReport"
          useGeneratedKeys="true" keyProperty="id">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 28 14:08:44 CST 2017.
    -->
    insert into ${issSchema}.mmp_credit_event_type_report
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="yearNum != null" >
        year_num,
      </if>
      <if test="orgId != null" >
        org_id,
      </if>
      <if test="type != null" >
        type,
      </if>
      <if test="eventTypeId != null" >
        event_type_id,
      </if>
      <if test="eventName != null" >
        event_name,
      </if>
      <if test="month != null" >
        month,
      </if>
      <if test="total != null" >
        total,
      </if>
      <if test="miscDesc != null" >
        misc_desc,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateOperId != null" >
        update_oper_id,
      </if>
      <if test="updateOperName != null" >
        update_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="yearNum != null" >
        #{yearNum,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null" >
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        #{type,jdbcType=INTEGER},
      </if>
      <if test="eventTypeId != null" >
        #{eventTypeId,jdbcType=INTEGER},
      </if>
      <if test="eventName != null" >
        #{eventName,jdbcType=VARCHAR},
      </if>
      <if test="month != null" >
        #{month,jdbcType=INTEGER},
      </if>
      <if test="total != null" >
        #{total,jdbcType=INTEGER},
      </if>
      <if test="miscDesc != null" >
        #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        #{updateOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.credit.model.MmpCreditEventTypeReport" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 28 14:08:44 CST 2017.
    -->
    update ${issSchema}.mmp_credit_event_type_report
    <set >
      <if test="yearNum != null" >
        year_num = #{yearNum,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null" >
        org_id = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="eventTypeId != null" >
        event_type_id = #{eventTypeId,jdbcType=INTEGER},
      </if>
      <if test="eventName != null" >
        event_name = #{eventName,jdbcType=VARCHAR},
      </if>
      <if test="month != null" >
        month = #{month,jdbcType=INTEGER},
      </if>
      <if test="total != null" >
        total = #{total,jdbcType=INTEGER},
      </if>
      <if test="miscDesc != null" >
        misc_desc = #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.credit.model.MmpCreditEventTypeReport" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 28 14:08:44 CST 2017.
    -->
    update ${issSchema}.mmp_credit_event_type_report
    set year_num = #{yearNum,jdbcType=VARCHAR},
      org_id = #{orgId,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      event_type_id = #{eventTypeId,jdbcType=VARCHAR},
      event_name = #{eventName,jdbcType=VARCHAR},
      month = #{month,jdbcType=INTEGER},
      total = #{total,jdbcType=INTEGER},
      misc_desc = #{miscDesc,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <sql id="Base_CreditEventTypeReport_Column_List" >
    tr.id as id, tr.year_num as yearNum, tr.org_id as orgId, tr.type as type ,
    tr.event_type_id as eventTypeId, tr.event_name as eventName, tr.month as month,
    IFNULL(tr.total,0) as total
  </sql>

  <select id="getCreditEventTypeReportCount" resultType="java.lang.Integer">
    SELECT
      count(1)
    FROM
    ${issSchema}.mmp_credit_event_type a  where
    EXISTS(SELECT * FROM ${issSchema}.mmp_credit_event_type_report tr
    WHERE
    tr.status=1 AND tr.event_type_id=a.id
    <if test=" yearNum != null ">
      AND tr.year_num = #{yearNum,jdbcType=VARCHAR}
    </if>
    <if test=" orgId !='' and orgId != null">
        AND tr.org_id = #{orgId,jdbcType=VARCHAR}
    </if>
    <if test=" type != null ">
      AND tr.type = #{type,jdbcType=INTEGER}
    </if>   )
  </select>

  <select id="getCreditEventTypeReportPages" resultType="com.extracme.evcard.membership.credit.dto.CreditEventTypeReportPageDto">
    SELECT
    <include refid="Base_CreditEventTypeReport_Column_List"/>
    from  ${issSchema}.mmp_credit_event_type_report tr
    WHERE tr.status=1 AND tr.event_type_id in
    <foreach collection="list" item="item" open="(" close=")" index="index" separator=",">
      #{item}
    </foreach>
    <if test=" paramsDto.yearNum != null ">
      AND tr.year_num = #{paramsDto.yearNum,jdbcType=VARCHAR}
    </if>
    <if test=" paramsDto.orgId  !='' and paramsDto.orgId  != null">
        AND tr.org_id  = #{paramsDto.orgId ,jdbcType=VARCHAR}
    </if>
    <if test=" paramsDto.type != null ">
      AND tr.type = #{paramsDto.type,jdbcType=INTEGER}
    </if>

  </select>


  <select id="getCreditEventTypeIdReportPages" resultType="java.lang.Integer">
    SELECT
    a.id
    FROM
    ${issSchema}.mmp_credit_event_type a  where
    EXISTS(SELECT * FROM ${issSchema}.mmp_credit_event_type_report tr
    WHERE tr.status=1 AND tr.event_type_id=a.id
    <if test=" paramsDto.yearNum != null ">
      AND tr.year_num = #{paramsDto.yearNum,jdbcType=VARCHAR}
    </if>
    <if test=" paramsDto.orgId  !='' and paramsDto.orgId  != null">
        AND tr.org_id  = #{paramsDto.orgId ,jdbcType=VARCHAR}
    </if>
    <if test=" paramsDto.type != null ">
      AND tr.type = #{paramsDto.type,jdbcType=INTEGER}
    </if>  )
    limit  #{page.offSet} , #{page.limitSet}
  </select>

  <select id="getCreditEventTypeIdReportExport" resultType="java.lang.Integer">
    SELECT
    a.id
    FROM
    ${issSchema}.mmp_credit_event_type a  where
    EXISTS(SELECT * FROM ${issSchema}.mmp_credit_event_type_report tr
    WHERE tr.status=1 AND tr.event_type_id=a.id
    <if test=" paramsDto.yearNum != null ">
      AND tr.year_num = #{paramsDto.yearNum,jdbcType=VARCHAR}
    </if>
    <if test=" paramsDto.orgId  !='' and paramsDto.orgId  != null">
        AND tr.org_id  = #{paramsDto.orgId ,jdbcType=VARCHAR}
    </if>
    <if test=" paramsDto.type != null ">
      AND tr.type = #{paramsDto.type,jdbcType=INTEGER}
    </if>  )
  </select>

  <select id="selectIsExistTypeReport" resultType="java.lang.Integer">
    SELECT count(1)
     FROM ${issSchema}.mmp_credit_event_type_report
     WHERE  year_num = #{paramsDto.yearNum,jdbcType=VARCHAR}
     AND org_id = #{paramsDto.orgId ,jdbcType=VARCHAR}
     AND type = #{paramsDto.type,jdbcType=INTEGER}
     AND event_type_id = #{paramsDto.eventTypeId,jdbcType=INTEGER}
     AND month = #{paramsDto.month,jdbcType=INTEGER}
  </select>

  <update id="updateExistEventTypeReport">
    UPDATE  ${issSchema}.mmp_credit_event_type_report
    SET total = #{paramsDto.total,jdbcType=INTEGER}
     WHERE  year_num = #{paramsDto.yearNum,jdbcType=VARCHAR}
     AND org_id  = #{paramsDto.orgId ,jdbcType=VARCHAR}
     AND type  = #{paramsDto.type,jdbcType=INTEGER}
     AND event_type_id = #{paramsDto.eventTypeId,jdbcType=INTEGER}
     AND month  = #{paramsDto.month,jdbcType=INTEGER}
  </update>

</mapper>