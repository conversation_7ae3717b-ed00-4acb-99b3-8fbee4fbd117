package com.extracme.evcard.membership.vipcredits.anyolife.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 积分账户履历查询参数
 * <AUTHOR>
 * @Discription
 * @date 2020/8/12
 */
@Data
@NoArgsConstructor
public class QueryCreditsBeforeRequest extends BaseCreditsRequest {
    /**
     * 事件类型: 3.签到14.成就任务...
     */
    private Integer feeType;

    /**
     * 碳减排订单分享编号
     */
    private String shareOrderNo;

    /**
     * 数额：
     * 取环车评价积分时，为档次，取值为1、2
     */
    private BigDecimal gainCredits;

    public QueryCreditsBeforeRequest(String channel, String uid){
        this.setUid(uid);
        this.setChannel(channel);
    }

}
