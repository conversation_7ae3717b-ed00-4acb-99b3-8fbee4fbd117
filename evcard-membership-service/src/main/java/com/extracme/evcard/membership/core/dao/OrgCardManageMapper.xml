<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- OrgCardManageMapper，，对应表org_card_manage -->
<mapper namespace="com.extracme.evcard.membership.core.dao.OrgCardManageMapper">
    <!-- 返回结果集Map -->
    <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.OrgCardManage">
        <id column="CARD_NO" jdbcType="VARCHAR" property="cardNo" />
        <result column="ORG_ID" jdbcType="VARCHAR" property="orgId" />
        <result column="CARD_NO" jdbcType="VARCHAR" property="cardNo" />
        <result column="AUTH_ID" jdbcType="VARCHAR" property="authId" />
        <result column="STATUS" jdbcType="VARCHAR" property="status" />
        <result column="CREATED_TIME" jdbcType="VARCHAR" property="createdTime" />
        <result column="CREATED_USER" jdbcType="VARCHAR" property="createdUser" />
        <result column="UPDATED_TIME" jdbcType="VARCHAR" property="updatedTime" />
        <result column="UPDATED_USER" jdbcType="VARCHAR" property="updatedUser" />
    </resultMap>
    
    <!--数据列-->
    <sql id="Base_Column_List" >
            ORG_ID,
            CARD_NO,
            AUTH_ID,
            STATUS,
            CREATED_TIME,
            CREATED_USER,
            UPDATED_TIME,
            UPDATED_USER
    </sql>

    <update id="updateOrgCardManaStatus" parameterType="com.extracme.evcard.membership.core.model.OrgCardManage">
        UPDATE ${siacSchema}.ORG_CARD_MANAGE 
        SET 
            STATUS  = #{status,jdbcType=VARCHAR},
            UPDATED_TIME = #{updatedTime,jdbcType=VARCHAR},
            UPDATED_USER = #{updatedUser,jdbcType=VARCHAR}
        WHERE   
            CARD_NO = #{cardNo,jdbcType=VARCHAR} and
            ORG_ID = #{orgId,jdbcType=VARCHAR}
    </update>
</mapper>