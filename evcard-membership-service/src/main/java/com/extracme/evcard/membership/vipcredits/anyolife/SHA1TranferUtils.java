/**  
 * @Description:       
 * @author:v-ch<PERSON><PERSON><PERSON>
 * @date:   2019年4月4日 下午5:28:39   
 * @version V1.0 
 */
package com.extracme.evcard.membership.vipcredits.anyolife;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 签名所使用加密算法
 * anyolife
 * 安悦积分商城提供
 */
public class SHA1TranferUtils {

	public static String encode(String str) {
		if (null == str || 0 == str.length()) {
			return "";

		}
		char[] hexDigits = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f' };
		try {
			MessageDigest mdTemp = MessageDigest.getInstance("SHA1");
			mdTemp.update(str.getBytes("UTF-8"));

			byte[] md = mdTemp.digest();
			int j = md.length;
			char[] buf = new char[j * 2];
			int k = 0;
			for (int i = 0; i < j; i++) {
				byte byte0 = md[i];
				buf[k++] = hexDigits[byte0 >>> 4 & 0xf];
				buf[k++] = hexDigits[byte0 & 0xf];
			}
			return String.valueOf(buf);
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		return "";
	}

	/*public static void main(String[] args) {
		String s = encode(
				"{\"mobile\":\"piaid1\",\"name\":\"测试title\",\"uidType\":\"\",\"avatar\":\"测试message\",\"gender\":\"\",\"nationality\":\"\",\"birthday\":\"11111\",\"idCard\":\"\",\"contractCompany\":\"\",\"cityCode\":\"\",\"clientIp\":\"\",\"channelCode\":\"\",\"operateBy\":\"\",\"clientId\":\"\",\"merchantId\":\"\"}20190415112900iYqltQKd");
		System.out.println(s);
	}*/
}
