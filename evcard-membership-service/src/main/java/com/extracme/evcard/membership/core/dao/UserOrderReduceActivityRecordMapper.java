package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.model.UserOrderReduceActivityRecord;

import java.util.List;

public interface UserOrderReduceActivityRecordMapper {


    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_order_reduce_activity_record
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    int insertSelective(UserOrderReduceActivityRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_order_reduce_activity_record
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    UserOrderReduceActivityRecord selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_order_reduce_activity_record
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    int updateByPrimaryKeySelective(UserOrderReduceActivityRecord record);

    /**
     * 根据条件查询
     * @param record
     * @return
     */
    List<UserOrderReduceActivityRecord> selectByCondition(UserOrderReduceActivityRecord record);

}