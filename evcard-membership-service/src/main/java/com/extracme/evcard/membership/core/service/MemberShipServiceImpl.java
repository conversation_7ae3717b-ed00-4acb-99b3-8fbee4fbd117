package com.extracme.evcard.membership.core.service;

import com.alibaba.dubbo.rpc.RpcContext;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.bean.ProducerBean;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.afs.model.v20180112.AuthenticateSigRequest;
import com.aliyuncs.afs.model.v20180112.AuthenticateSigResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.green.model.v20170112.TextScanRequest;
import com.aliyuncs.http.FormatType;
import com.aliyuncs.http.HttpResponse;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.http.ProtocolType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.aliyuncs.sts.model.v20150401.AssumeRoleRequest;
import com.aliyuncs.sts.model.v20150401.AssumeRoleResponse;
import com.baosight.iplat4j.security.util.Crypto;
import com.evcard.ids.provider.api.response.TaskDetailResponse;
import com.evcard.ids.provider.api.service.IdsMembershipInfoServiceProvider;
import com.evcard.ids.provider.api.service.IdsSendVehicleServiceProvider;
import com.evcard.mtc.provider.dto.QueryRepairHistroyByUserDTO;
import com.evcard.mtc.provider.dto.UserRepairHistroyDTO;
import com.evcard.mtc.provider.service.IMtcRepairTaskService;
import com.extracme.evcard.activity.dubboService.IDubboShareService;
import com.extracme.evcard.ccs.provider.dto.RefundBaseInfoDTO;
import com.extracme.evcard.ccs.provider.service.IRefundBaseInfoService;
import com.extracme.evcard.membership.common.*;
import com.extracme.evcard.membership.config.*;
import com.extracme.evcard.membership.contract.service.IMemberShipContractServ;
import com.extracme.evcard.membership.core.bean.BaseBean;
import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.dao.*;
import com.extracme.evcard.membership.core.dto.*;
import com.extracme.evcard.membership.core.dto.input.FaceMatchInput;
import com.extracme.evcard.membership.core.dto.input.FacePersonVerifyInput;
import com.extracme.evcard.membership.core.dto.input.QueryDepositInfoInputDTO;
import com.extracme.evcard.membership.core.dto.input.SubmitDepositSecurityInput;
import com.extracme.evcard.membership.core.dto.md.GetMemberDepositInfosData;
import com.extracme.evcard.membership.core.dto.md.SearchCityConfigurationResponse;
import com.extracme.evcard.membership.core.enums.*;
import com.extracme.evcard.membership.core.exception.*;
import com.extracme.evcard.membership.core.input.*;
import com.extracme.evcard.membership.core.manager.MemberDocumentManager;
import com.extracme.evcard.membership.core.model.*;
import com.extracme.evcard.membership.core.service.auth.IRegister;
import com.extracme.evcard.membership.core.service.auth.inner.IdentityCertService;
import com.extracme.evcard.membership.core.service.auth.register.RegisterAdapter;
import com.extracme.evcard.membership.core.service.license.LicenseAuthenticateProxy;
import com.extracme.evcard.membership.core.service.license.LicenseStatusChecker;
import com.extracme.evcard.membership.core.service.md.MdDepositService;
import com.extracme.evcard.membership.core.service.md.MdStoreService;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.dao.MmpUserTagMapper;
import com.extracme.evcard.membership.credit.dto.MemberPointsOfferDto;
import com.extracme.evcard.membership.credit.dto.UpdateServiceVerDto;
import com.extracme.evcard.membership.credit.model.MembershipBaseInfo;
import com.extracme.evcard.membership.credit.model.MembershipInfo;
import com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs;
import com.extracme.evcard.membership.credit.model.MmpUserTag;
import com.extracme.evcard.membership.credit.service.IMemberPointsService;
import com.extracme.evcard.membership.face.MemberFaceVerifyService;
import com.extracme.evcard.membership.health.dto.GetMemberHealthResponse;
import com.extracme.evcard.membership.health.service.MemberShipInvitationServ;
import com.extracme.evcard.membership.ocr.CommonDrivingLicenseOcrReq;
import com.extracme.evcard.membership.ocr.CommonDrivingLicenseOcrResp;
import com.extracme.evcard.membership.ocr.OcrAdapter;
import com.extracme.evcard.mq.bean.EventEnum;
import com.extracme.evcard.mq.bean.MemPointsPushEnum;
import com.extracme.evcard.mq.bean.event.MemberRegister;
import com.extracme.evcard.protobuf.ProtobufUtil;
import com.extracme.evcard.redis.JedisLock;
import com.extracme.evcard.redis.JedisUtil;
import com.extracme.evcard.rpc.base.dto.OrgInfo;
import com.extracme.evcard.rpc.base.service.IOrgService;
import com.extracme.evcard.rpc.coupon.service.ICouponServ;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.dto.Page;
import com.extracme.evcard.rpc.entity.HidLog;
import com.extracme.evcard.rpc.entity.LogPoint;
import com.extracme.evcard.rpc.enums.StatusCode;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.exception.BusinessRuntimeException;
import com.extracme.evcard.rpc.messagepush.dto.MailSenderInfo;
import com.extracme.evcard.rpc.messagepush.service.IMessagepushServ;
import com.extracme.evcard.rpc.messagepush.service.ISensorsdataService;
import com.extracme.evcard.rpc.order.dto.GetUserCurrentOrderInput;
import com.extracme.evcard.rpc.order.dto.OrderInfoDto;
import com.extracme.evcard.rpc.order.dto.SaveOrderFaceContrastRecordDto;
import com.extracme.evcard.rpc.order.dto.UserOrderInfoDto;
import com.extracme.evcard.rpc.order.input.QueryUserCurrentOrderInput;
import com.extracme.evcard.rpc.order.service.IOrderService;
import com.extracme.evcard.rpc.order.service.IPickupOrderService;
import com.extracme.evcard.rpc.order.service.IntergationOrderService;
import com.extracme.evcard.rpc.riskorder.dto.RpcMemberIllegalDTO;
import com.extracme.evcard.rpc.riskorder.dto.RpcRiskOrderDTO;
import com.extracme.evcard.rpc.riskorder.service.IRiskOrderService;
import com.extracme.evcard.rpc.util.BeanCopyUtils;
import com.extracme.evcard.rpc.util.DateType;
import com.extracme.evcard.saic.service.ISaicMemberService;
import com.extracme.evcard.sso.dto.SsoUserInfoDto;
import com.extracme.evcard.sso.service.SsoUserService;
import com.extracme.evcard.sts.rpc.service.AppConfigRpcService;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.message.BasicHeader;
import org.apache.http.protocol.HTTP;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.ObjectUtils;
import redis.clients.jedis.Jedis;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URL;
import java.security.SignatureException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.extracme.evcard.membership.common.ComUtil.*;
import static com.extracme.evcard.membership.core.canstant.BussinessConstants.APP_KEY_EVCARD_MMP;

@Service("memberShipService")
public class MemberShipServiceImpl implements IMemberShipService {

    @Value("${cancel.sms.templateid:261}")
    private int CANCEL_SMS_TEMPLATEID;

    @Autowired
    IMessagepushServ messagepush;

    @Autowired
    private MembershipInfoMapper membershipInfoMapper;

    @Resource(name = "producer")
    private ProducerBean producer;

    @Autowired
    private UserContractMapper userContractMapper;

    @Autowired
    private MembershipClauseLogMapper membershipClauseLogMapper;

    @Autowired
    private MmpUserOperationLogMapper mmpUserOperationLogMapper;

    @Autowired
    private OcrAdapter ocrAdapter;

    private static Logger logger = LoggerFactory.getLogger(MemberShipServiceImpl.class);

    public static final String VERIFY_CODE_NUM = "verifycode_nume";

    @Autowired
    private CardInfoMapper cardInfoMapper;

    @Autowired
    private ApplyProgressMapper applyProgressMapper;

    @Autowired
    private UserOperatorLogMapper userOperatorLogMapper;

    @Autowired
    CardInfoHistroyMapper cardInfoHistroyMapper;
    @Autowired
    CardPauseLogMapper cardPauseLogMapper;
    @Autowired
    CityMapper cityMapper;
    @Autowired
    private AgencyInfoMapper agencyInfoMapper;
    @Autowired
    MembershipAdditionalInfoMapper membershipAdditionalInfoMapper;
    @Autowired
    IMemberOperateServ memberOperateServ;
    @Autowired
    MemberReviewService memberReviewService;
    @Autowired
    TEsUserMapper tEsUserMapper;
    @Autowired
    AppKeyManagerMapper appKeyManagerMapper;

    @Autowired
	ISensorsdataService sensorsdataService;

    @Autowired
    IMemberDepositService memberDepositService;

    @Autowired
    IDubboShareService dubboShareService;
    @Autowired
    IOrderService orderService;
    @Autowired
    IntergationOrderService intergationOrderService;
    @Autowired
    IdsMembershipInfoServiceProvider idsMembershipInfoServiceProvider;
    @Autowired
    IdsSendVehicleServiceProvider idsSendVehicleServiceProvider;
    @Autowired
    IRiskOrderService riskOrderService;
    @Autowired
    IMtcRepairTaskService mtcRepairTaskService;
    @Autowired
    private IBaseInfoService baseInfoService;
    @Autowired
    IRefundBaseInfoService refundBaseInfoService;
    @Autowired
    private ExpressInfoMapper expressInfoMapper;
    @Autowired
    private MmpUserTagMapper mmpUserTagMapper;
    @Autowired
    private IPickupOrderService pickupOrderService;
    @Autowired
    private DriverLicenseElementsAuthenticateRecordMapper licenseElementsAuthenticateRecordMapper;
    @Autowired
    private DriverLicenseElementsAuthenticateLogMapper licenseElementsAuthenticateLogMapper;
    @Autowired
    private LicenseAuthenticateProxy licenseAuthenticateService;

    @Autowired
    private DataSourceTransactionManager txManager;

    @Autowired
    private MmpProvisionInfoMapper mmpProvisionInfoMapper;

    @Autowired
    private IPlatformService platformService;
    @Autowired
    private IOrgService orgService;
    @Autowired
    private IMemberCardService memberCardService;
    @Autowired
    private HealthCodeMapper healthCodeMapper;
    @Autowired
    private MemberShipInvitationServ memberShipInvitationServ;
    @Autowired
    private IMemberPointsService memberPointsService;
    @Resource
    private ISaicMemberService saicMemberService;

    @Autowired
    private SsoUserService ssoUserService;

    @Autowired
    private IMemberShipContractServ memberShipContractServ;

    @Autowired
    private DepositSecurityMapper depositSecurityMapper;

    @Autowired
    private UserFaceContrastResultMapper userFaceContrastResultMapper;

    @Resource
    private MemberIdentityDocumentMapper memberIdentityDocumentMapper;

    @Resource
    private MemberDocumentManager memberDocumentManager;

    @Value("${ons.raw.topic}")
    private String evcardRawDataTopic;

    @Value("${aliAfs.enable}")
    private String aliAfsEnable;

    @Autowired
    private OssConfig ossConfig;

    @Autowired
    private AliyunConfig aliyunConfig;

    @Autowired
    private CommConfig commConfig;

    @Resource
    private MidGenerator midGenerator;

    @Autowired
    ICouponServ couponServ;

    @Autowired
    IdentityCertService identityCertService;

    @Autowired
    MemberOptLogService memberOptLogService;

    @Resource
    IMemberShipService memberShipService;

    @Resource
    private MdStoreService mdStoreService;

    @Resource
    private UserTokenMapper userTokenMapper;

    @Resource
    private SecondAppKeyManagerMapper secondAppKeyManagerMapper;

    @Autowired
    private RegisterAdapter registerAdapter;

    public static IAcsClient acsClient;

    private String unregisterSecondAppKey = "second_yxhd_unregistered";

    @Autowired
    private MdDepositService mdDepositService;

    //static {
    @PostConstruct
    public void initComponents() {
        IClientProfile profile = DefaultProfile.getProfile(aliyunConfig.getRegionId(),
                aliyunConfig.getAccessKeyId(), aliyunConfig.getAccessKeySecret());
        acsClient = new DefaultAcsClient(profile);
        try {
            DefaultProfile.addEndpoint(aliyunConfig.getRegionId(), aliyunConfig.getRegionId(),
                    aliyunConfig.getAfsAppCode(), aliyunConfig.getAfsNameServer());
        } catch (Exception e) {
            logger.error("IAcsClient init error:", e);
        }
    }

    @Override
    public String register(RegisterDto registerDto) throws MemberException {
        return this.register(registerDto, true);
    }

    @Override
    public String registerV1(RegisterDto registerDto) throws MemberException {
        return this.registerV1(registerDto, false);
    }

    @Override
    public String registerV1(RegisterDto registerDto, boolean checkVerifyCode) throws MemberException {
        logger.info("注册服务接口入参={}" + JSON.toJSONString(registerDto));
        //参数校验
        if (StringUtils.isBlank(registerDto.getMobilePhone())
                || StringUtils.isEmpty(registerDto.getCity())) {
            throw new MemberException(StatusCode.PARAM_EMPTY);
        }
        if (checkVerifyCode) {
            if (checkVerifyCode && StringUtils.isBlank(registerDto.getSmsVerifyCode())) {
                HidLog.membership(LogPoint.MEMBER_REGISTER, StatusCode.PARAM_EMPTY.getMsg(), registerDto.getMobilePhone(), false);
                throw new MemberException(StatusCode.PARAM_EMPTY);
            }
        }

        Jedis jedis = null;
        JedisLock jedisLock = null;
        String userId = null;
        // 之前传的是一级渠道 现在有可能传二级渠道
        String appKey = null;
        String secondAppKey = StringUtils.isBlank(registerDto.getSecondAppKey()) ? null : registerDto.getSecondAppKey();
        boolean successFlag = false;
        TransactionStatus transactionStatus = null;
        try {
            //手机号校验
            String mobilePhone = registerDto.getMobilePhone().trim();
            //防止手机号并发注册
            jedis = JedisUtil.getJedis();
            jedisLock = new JedisLock(mobilePhone);
            if (!jedisLock.acquire(jedis)) {
                throw new MemberException(StatusCode.SYSTEM_ERROR);
            }
            if (!pattern.matcher(mobilePhone).matches()) {
                throw new MemberException(StatusCode.MOBILE_FORMATE_ERROR);
            }
            if (membershipInfoMapper.countByMobilePhone(mobilePhone,registerDto.getOrgId()) > 0) {
                /**
                 * 验证账号是否被注销
                 */
                AccountStatusDto accountStatusDto = this.getAccountStatusByMobileV2(mobilePhone, 0,registerDto.getOrgId());
                if (accountStatusDto != null) {
                    if (accountStatusDto.getAccountStatus() == 1) {
                        throw new MemberException(StatusCode.MOBILE_FREEZING);
                    }
                    if (accountStatusDto.getAccountStatus() != 2) {
                        throw new MemberException(StatusCode.MOBILE_USED);
                    }
                }
            }
            //密码校验
            String password = registerDto.getMobilePhone().substring(1,6).trim();
            if (StringUtils.isNotBlank(registerDto.getPassword())){
                password = registerDto.getPassword();
            }
            //加密密码
            password = Crypto.encryptDES(password, ENCRYPT_KEY);
            if (checkVerifyCode) {
                boolean verifyCodeResult = memberOperateServ.checkVerifyCode(mobilePhone, registerDto.getSmsVerifyCode(), true);
                if (!verifyCodeResult) {
                    verifyCodeResult = memberOperateServ.checkVerifyCode(mobilePhone, registerDto.getSmsVerifyCode(), true);
                    if (!verifyCodeResult) {
                        HidLog.membership(LogPoint.MEMBER_REGISTER, StatusCode.VALIDATE_CODE_ERROR.getMsg(), mobilePhone, false);
                        throw new MemberException(StatusCode.VALIDATE_CODE_ERROR);
                    }
                }
            }
            //第三方appKey
            appKey = registerDto.getAppKey();
            //去除空格
            if (StringUtils.isNotEmpty(appKey)) {
                appKey = appKey.trim();
            }

            // 渠道校验
            if (StringUtils.isNotEmpty(appKey)){
                if (appKey.startsWith(BussinessConstants.SECOND_CHANNEL_PRE)) {
                    secondAppKey = appKey;
                    if(secondAppKeyManagerMapper.checkAppKeyExist(secondAppKey) == 0){
                        logger.error("二级渠道未查询到,registerDto={}",JSON.toJSONString(registerDto));
                        throw new MemberException(StatusCode.APPKEY_INVALID);
                    }
                }else{
                    if( membershipInfoMapper.isExistAppKey(appKey) == 0){
                        logger.error("一级渠道未查询到,registerDto={}",JSON.toJSONString(registerDto));
                        throw new MemberException(StatusCode.APPKEY_INVALID);
                    }
                }
            }


            // 特殊渠道 不做appkey特殊处理
            if (!appKey.startsWith(BussinessConstants.SECOND_CHANNEL_PRE)) {
                /**
                 * app分包渠道和appKey渠道 两个概念合并.<br>
                 */
                if(HidLog.PARAM_LOCAL.get() != null && StringUtils.isNotBlank(HidLog.PARAM_LOCAL.get().get(HidLog.CHANNEL))) {
                    appKey = HidLog.PARAM_LOCAL.get().get(HidLog.CHANNEL);
                }
                if (StringUtils.isBlank(appKey)) {
                    HidLog.membership(LogPoint.MEMBER_REGISTER, LogPoint.MEMBER_REGISTER.getLogType(), mobilePhone, false);
                    throw new MemberException(StatusCode.APPKEY_INVALID);
                }
            }
            //city = area 特殊处理(存在县级所属运营公司,所以归属地划分需要特殊处理)
            String city = registerDto.getCity().trim();
            appKey = appKey.trim();
            //生成会员ID
            LocalDateTime localDateTime = LocalDateTime.now();
            userId = mobilePhone + localDateTime.format(DateTimeFormatter.ofPattern(DATE_TYPE6));
            //插入记录
            MembershipInfoWithBLOBs member = new MembershipInfoWithBLOBs();
            member.setAuthId(userId);
            member.setPassword(password);
            member.setMobilePhone(mobilePhone);
            member.setCityOfOrigin(city);
            member.setProvinceOfOrigin(registerDto.getProvince());
            member.setMembershipType((short) 0);
            //审核状态
            member.setReviewStatus((short) -1);
            member.setDataOrigin((short) registerDto.getRegisterOrigin());
            member.setCreatedTime(localDateTime.format(DateTimeFormatter.ofPattern(DATE_TYPE3)));
            member.setCreatedUser(registerDto.getOperateUser());
            member.setUpdatedTime(localDateTime.format(DateTimeFormatter.ofPattern(DATE_TYPE3)));
            member.setUpdatedUser(registerDto.getOperateUser());
            member.setRegTime(localDateTime.format(DateTimeFormatter.ofPattern(DATE_TYPE4)));
            //第三方appKey
            member.setAppKey(appKey);
            // 手机厂商
            member.setManufacturer(registerDto.getManufacturer());
            // 虚构用户（非注册用户发券）
            member.setIsFictional(registerDto.getIsFictional());
            // 设置会员渠道信息
            try {
                if (StringUtils.isNotBlank(secondAppKey)) {
                    SecondAppKeyManager secondAppKeyManager = secondAppKeyManagerMapper.selectBySecondAppKey(secondAppKey);
                    if (secondAppKeyManager != null) {
                        member.setAppKey(secondAppKeyManager.getFirstAppKey());
                        member.setSecondAppKey(secondAppKey);
                        member.setPlatformId(secondAppKeyManager.getPlatformId());
                    }else{
                        logger.error("二级渠道未查询到,registerDto={}",JSON.toJSONString(registerDto));
                        throw new MemberException(StatusCode.APPKEY_INVALID);
                    }
                }else{
                    // 通过一级渠道查询二级渠道信息
                    List<SecondAppKeyManager> appKeyManagers = secondAppKeyManagerMapper.selectListByFirstAppKey(appKey);
                    if (CollectionUtils.isNotEmpty(appKeyManagers)) {
                        SecondAppKeyManager secondAppKeyManager = appKeyManagers.get(0);
                        member.setSecondAppKey(secondAppKeyManager.getSecondAppKey());
                        member.setPlatformId(secondAppKeyManager.getPlatformId());
                        secondAppKey = secondAppKeyManager.getSecondAppKey();
                    }else{
                        logger.error("二级渠道未查询到,registerDto={}",JSON.toJSONString(registerDto));
                        throw new MemberException(StatusCode.APPKEY_INVALID);
                    }
                }
            } catch (Exception e) {
                logger.error("注册时，通过一级渠道查询二级渠道报错，registerDto={}",JSON.toJSONString(registerDto),e);
            }

            if(StringUtils.isNotBlank(registerDto.getUid())) {
            	member.setUid(registerDto.getUid());
            }
            //邀请码
            if (StringUtils.isNotBlank(registerDto.getInvitationCode())) {
                //通过邀请码查询相应的shareId
                String shareId = membershipClauseLogMapper.queryShareIdByInvitationCode(registerDto.getInvitationCode().toLowerCase());
                if (StringUtils.isNotBlank(shareId)) {
                    logger.info("邀请码" + registerDto.getInvitationCode() + "对应分享ID" + shareId);
                    member.setShareUid(shareId.trim().replace(' ', '+'));
                } else {
                    logger.info("邀请码" + registerDto.getInvitationCode() + "无效邀请码");
                    throw new MemberException(StatusCode.INVITED_CODE_ERROR);
                }
            }
            //好友分享shareId(H5)
            if (StringUtils.isNotBlank(registerDto.getShareUid())) {
                logger.info("分享Id=" + registerDto.getShareUid());
                member.setShareUid(registerDto.getShareUid().replace(' ', '+'));
                logger.info("处理完的分享Id=" + member.getShareUid());
            }

            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
            transactionStatus = txManager.getTransaction(def);
            //更新会员条款版本号
            String typeTwoVersion = registerDto.getPrivacyPolicyVersion();
            String typeOneVersion = registerDto.getMembershipPolicyVersion();

            if (registerDto.getAgreePolicy()){
                if (StringUtils.isBlank(typeOneVersion)) {
                    typeOneVersion = JedisUtil.hget("typeVersionMap", "1");
                }
                if (StringUtils.isBlank(typeTwoVersion)) {
                    typeTwoVersion = JedisUtil.hget("typeVersionMap", "2");
                }
                if (StringUtils.isNotEmpty(unregisterSecondAppKey) && unregisterSecondAppKey.equals(registerDto.getSecondAppKey())) {
                    member.setServiceVer(null);
                }
                else {
                    member.setServiceVer(typeOneVersion + "," + typeTwoVersion);
                }
            }
            member.setMid(midGenerator.getMid());
            int insertSelective = membershipInfoMapper.insertSelective(member);
            if (insertSelective > 0) {
                //插入条款历史
                if (StringUtils.isNotBlank(typeOneVersion)){
                    insertMemberClaseLog(userId, registerDto.getOperateUser(), "1_" + typeOneVersion);
                }
                if (StringUtils.isNotBlank(typeTwoVersion)){
                    insertMemberClaseLog(userId, registerDto.getOperateUser(), "2_" + typeTwoVersion);
                }
                logger.info("mobile=" + mobilePhone + "注册成功");
                //绑定设备号
                String imeiNo = registerDto.getImeiNo();
                memberOperateServ.changeImei(imeiNo, mobilePhone, userId, 2);
                logger.debug("changeImei mobile={}， imei={}完成", mobilePhone, imeiNo);
                txManager.commit(transactionStatus);
                logger.debug("commit 注册mobile={}， authId={}完成", mobilePhone, userId);
                //第三方渠道回调
                notifyRegisterSuccess(mobilePhone, appKey,member.getSecondAppKey());
                //消息推送
                //会员注册事件推送时，增加shareUid。
                registerDto.setShareUid(member.getShareUid());
                sendMessage(userId, registerDto);
                //数据埋点
                //writeRegisterData(userId, registerDto);
                HidLog.membership(LogPoint.MEMBER_REGISTER, LogPoint.MEMBER_REGISTER.getLogType(), userId, true);
                successFlag = true;
                return userId;
            } else {
                throw new MemberException(StatusCode.SYSTEM_ERROR);
            }
        } catch (Exception e) {
            if(null != transactionStatus) {
                txManager.rollback(transactionStatus);
            }
            if(e instanceof MemberException){
                try {
                    registerTrack(appKey, secondAppKey,-1, e.getMessage(), registerDto.getProvince(), registerDto.getCity(),
                            registerDto.getInvitationCode(), userId, registerDto.getAnonymousId(), registerDto.getTrackPlatForm(),registerDto.getOperationModel(),registerDto.getRegisterWay());
                } catch (Exception ex) {
                    logger.error("注册埋点失败", ex);
                }
                throw e;
            }else{
                logger.error("注册失败,原因：创建会员记录失败，" + e.getMessage(), e);
                throw new MemberException(StatusCode.SYSTEM_ERROR);
            }
        } finally {
            if (jedisLock != null && jedis != null) {
                jedisLock.releaseLua(jedis);
                jedis.close();
                logger.debug("注册服务-释放redis锁和链接成功");
            }
            if (successFlag) {
                try {
                    registerTrack(appKey, secondAppKey,0, StringUtils.EMPTY,
                            registerDto.getProvince(), registerDto.getCity(), registerDto.getInvitationCode(), userId, registerDto.getAnonymousId(), registerDto.getTrackPlatForm(),registerDto.getOperationModel(),registerDto.getRegisterWay());
                } catch (Exception ex) {
                    logger.error("注册埋点失败", ex);
                }
            }
        }
    }

    public void registerTrack(String appKey,String secondAppKey, int successOrFailure, String failureReason, String province, String cityName, String inviteCode, String authId, String anonymousId, String trackPlatForm,String operationModel,int registerWay) {
        String platformType = StringUtils.EMPTY;
        String orderPlatform = StringUtils.EMPTY;
        String secondChannel = StringUtils.EMPTY;
        if (StringUtils.isNotBlank(appKey)){
            AppKeyDto appKeyDto = platformService.getAppKey(appKey);
            if (appKeyDto != null) {
                if (StringUtils.isNotBlank(appKeyDto.getPlatformName())) {
                    platformType = appKeyDto.getPlatformName();
                }
                orderPlatform = appKeyDto.getPlatName();
            }
        }

        if (StringUtils.isNotBlank(secondAppKey)){
            SecondAppKeyManager secondAppKeyManager = secondAppKeyManagerMapper.selectBySecondAppKey(secondAppKey);
            if (secondAppKeyManager != null) {
                secondChannel = secondAppKeyManager.getSecondAppKeyName();
            }
        }
        if(StringUtils.isBlank(trackPlatForm)) {
            trackPlatForm = "Java";
        }
        if (org.apache.commons.lang.StringUtils.isNotBlank(authId) || org.apache.commons.lang.StringUtils.isNotBlank(anonymousId)) {
            Map<String, Object> trackMap = new HashMap<>(1);
            boolean successStatus = true;
            if (successOrFailure != 0) {
                successStatus = false;
            }
            trackMap.put("channel_name", orderPlatform);
            trackMap.put("channel_key", appKey);
            trackMap.put("secondary_channel", secondChannel);
            trackMap.put("is_success", successStatus);
            if (!successStatus) {
                trackMap.put("reason", failureReason);
            }
            trackMap.put("province", province);
            trackMap.put("city_name", cityName);
            trackMap.put("invite_code", inviteCode);

            try {
                if ("1".equals(operationModel)) {
                    trackMap.put("operation_model", "门店");
                }else{
                    trackMap.put("operation_model", "大库");
                }

                if (registerWay == 2) {
                    trackMap.put("type", "微信");
                }else if(registerWay == 3){
                    trackMap.put("type", "支付宝");
                }else if(registerWay == 4){
                    trackMap.put("type", "Apple ID");
                }else{
                    trackMap.put("type", "手机号");
                }
            } catch (Exception e) {
                logger.error("注册埋点异常[{}]",e);
            }

            String distinctId = anonymousId;
            if (successOrFailure == 0) {
                if (org.apache.commons.lang.StringUtils.isNotBlank(authId) && org.apache.commons.lang.StringUtils.isNotBlank(anonymousId)) {
                    if (anonymousId.length() >= 32 || anonymousId.equals(authId)) {
                        sensorsdataService.trackSignUp(anonymousId, authId);
                    } else {
                        sensorsdataService.trackSignUp(authId, authId);
                    }
                }
                distinctId = authId;
            }
            boolean isLoginId = false;
            if (successOrFailure == 0) {
                isLoginId = true;
            }
            sensorsdataService.track(distinctId, isLoginId, "registered", trackMap, trackPlatForm);
        }
        //用户表
        if (successOrFailure == 0) {
            Map<String, Object> properties = new HashMap<String, Object>();
            properties.put("register_city", cityName);
            properties.put("register_platform", orderPlatform);
            properties.put("register_source", appKey);
            properties.put("register_time_modified", ComUtil.getFormatDate(new Date(), ComUtil.DATE_TYPE1));
            sensorsdataService.profileSet(authId, true, properties);
        }
    }

    /**
     * 城市是否支持门店模式
     *
     * @param cityName
     * @return
     */
    public boolean getStoreMode(String cityName) {
        try {
            long cityId = 310100;
            if (StringUtils.isNotBlank(cityName)) {
                City city = cityMapper.getCityByName(cityName);
                if (city != null && city.getCityid() != null) {
                    cityId = city.getCityid();
                }
            }
            logger.info("查询城市配置，请求：cityId={}", cityId);
            SearchCityConfigurationResponse resp = mdStoreService.searchCityConfiguration(cityId);
            logger.info("查询城市配置，应答：{}", JSON.toJSONString(resp));
            if (resp != null && resp.getData() != null && CollectionUtils.isNotEmpty(resp.getData().getCfg())) {
                return true;
            }
        } catch (Exception e) {
            logger.info("判断城市{}是否支持门店异常{}", cityName,e);
        }
        return false;
    }

    @Override
    public String register(RegisterDto registerDto, boolean checkVerifyCode) throws MemberException {
        try {
            return this.registerV2(registerDto,checkVerifyCode);
        } catch (RegisterException e){
            throw new MemberException(-1,e.getMessage());
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String registerV2(RegisterDto registerDto, boolean checkVerifyCode) throws RegisterException {
        logger.info("注册服务接口入参={}" + JSON.toJSONString(registerDto));
        //参数校验
        if (StringUtils.isBlank(registerDto.getMobilePhone())
                || StringUtils.isBlank(registerDto.getPassword())
                || StringUtils.isEmpty(registerDto.getProvince())
                || StringUtils.isEmpty(registerDto.getCity())
                || StringUtils.isEmpty(registerDto.getArea())) {
            HidLog.membership(LogPoint.MEMBER_REGISTER, StatusCode.PARAM_EMPTY.getMsg(), registerDto.getMobilePhone(), false);
            throw new RegisterException(StatusCode.PARAM_EMPTY);
        }
        if (checkVerifyCode && StringUtils.isBlank(registerDto.getSmsVerifyCode())) {
            HidLog.membership(LogPoint.MEMBER_REGISTER, StatusCode.PARAM_EMPTY.getMsg(), registerDto.getMobilePhone(), false);
            throw new RegisterException(StatusCode.PARAM_EMPTY);
        }

        Jedis jedis = null;
        JedisLock jedisLock = null;
        String userId = null;
        String appKey = null;
        String secondAppKey = null;
        boolean successFlag = false;
        TransactionStatus transactionStatus = null;
        try {
            //手机号校验
            String mobilePhone = registerDto.getMobilePhone().trim();
            //防止手机号并发注册
            jedis = JedisUtil.getJedis();
            jedisLock = new JedisLock(mobilePhone);
            if (!jedisLock.acquire(jedis)) {
                HidLog.membership(LogPoint.MEMBER_REGISTER, StatusCode.SYSTEM_ERROR.getMsg(), mobilePhone, false);
                throw new RegisterException(StatusCode.SYSTEM_ERROR);
            }
            if (!pattern.matcher(mobilePhone).matches()) {
                HidLog.membership(LogPoint.MEMBER_REGISTER, StatusCode.MOBILE_FORMATE_ERROR.getMsg(), mobilePhone, false);
                throw new RegisterException(StatusCode.MOBILE_FORMATE_ERROR);
            }
            if (membershipInfoMapper.countByMobilePhone(mobilePhone,registerDto.getOrgId()) > 0) {
                /**
                 * 验证账号是否被注销
                 */
                AccountStatusDto accountStatusDto = this.getAccountStatusByMobileV2(mobilePhone, 0,registerDto.getOrgId());
                if (accountStatusDto != null) {
                    if (accountStatusDto.getAccountStatus() == 1) {
                        HidLog.membership(LogPoint.MEMBER_REGISTER, StatusCode.MOBILE_FREEZING.getMsg(), mobilePhone, false);
                        throw new RegisterException(StatusCode.MOBILE_FREEZING);
                    }
                    if (accountStatusDto.getAccountStatus() != 2) {
                        HidLog.membership(LogPoint.MEMBER_REGISTER, StatusCode.MOBILE_USED.getMsg(), mobilePhone, false);
                        throw new RegisterException(StatusCode.MOBILE_USED);
                    }
                }
            }
            //密码校验
            String password = registerDto.getPassword().trim();
            String checkResult = checkPassword(password);
            if (checkResult != null) {
                HidLog.membership(LogPoint.MEMBER_REGISTER, StatusCode.PASSWORD_FORMAT_ERROR.getMsg(), mobilePhone, false);
                throw new RegisterException(StatusCode.PASSWORD_FORMAT_ERROR);
            } else {
                //加密密码
                password = Crypto.encryptDES(password, ENCRYPT_KEY);
            }
            if (checkVerifyCode) {
                boolean verifyCodeResult = memberOperateServ.checkVerifyCode(mobilePhone, registerDto.getSmsVerifyCode(), true);
                if (!verifyCodeResult) {
                    HidLog.membership(LogPoint.MEMBER_REGISTER, StatusCode.VALIDATE_CODE_ERROR.getMsg(), mobilePhone, false);
                    throw new RegisterException(StatusCode.VALIDATE_CODE_ERROR);
                }
            }
            //第三方appKey
            appKey = registerDto.getAppKey();
            //去除空格
            if (StringUtils.isNotEmpty(appKey)) {
                appKey = appKey.trim();
            }
            if (StringUtils.isNotEmpty(appKey) && membershipInfoMapper.isExistAppKey(appKey) == 0) {
                HidLog.membership(LogPoint.MEMBER_REGISTER, StatusCode.APPKEY_INVALID.getMsg(), mobilePhone, false);
                throw new RegisterException(StatusCode.APPKEY_INVALID);
            }
            /**
             * app分包渠道和appKey渠道 两个概念合并.<br>
             */
            if(HidLog.PARAM_LOCAL.get() != null && StringUtils.isNotBlank(HidLog.PARAM_LOCAL.get().get(HidLog.CHANNEL))) {
                appKey = HidLog.PARAM_LOCAL.get().get(HidLog.CHANNEL);
            }
            if (StringUtils.isBlank(appKey)) {
                HidLog.membership(LogPoint.MEMBER_REGISTER, LogPoint.MEMBER_REGISTER.getLogType(), mobilePhone, false);
                HidLog.membership(LogPoint.MEMBER_REGISTER, StatusCode.APPKEY_INVALID.getMsg(), mobilePhone, false);
                throw new RegisterException(StatusCode.APPKEY_INVALID);
            }
            //city = area 特殊处理(存在县级所属运营公司,所以归属地划分需要特殊处理)
            String area = registerDto.getArea().trim();
            String city = registerDto.getCity().trim();
            appKey = appKey.trim();
            String cityName = membershipInfoMapper.queryCityEqualsArea(area);
            if (StringUtils.isNotEmpty(cityName)) {
                city = cityName;
            }
            LocalDateTime localDateTime = LocalDateTime.now();
            if(StringUtils.isBlank(registerDto.getUid())) {
            	//生成会员ID
            	userId = mobilePhone + localDateTime.format(DateTimeFormatter.ofPattern(DATE_TYPE6));
            } else {
            	userId = registerDto.getUid();
            }
            //插入记录
            MembershipInfoWithBLOBs member = new MembershipInfoWithBLOBs();
            member.setAuthId(userId);
            if(StringUtils.isNotBlank(registerDto.getUid())) {
            	member.setUid(registerDto.getUid());
            }
            member.setPassword(password);
            member.setMobilePhone(mobilePhone);
            member.setAreaOfOrigin(area);
            member.setCityOfOrigin(city);
            member.setProvinceOfOrigin(registerDto.getProvince());
            member.setMembershipType((short) 0);
            //审核状态
            member.setReviewStatus((short) -1);
            member.setDataOrigin((short) registerDto.getRegisterOrigin());
            member.setCreatedTime(localDateTime.format(DateTimeFormatter.ofPattern(DATE_TYPE3)));
            member.setCreatedUser(registerDto.getOperateUser());
            member.setUpdatedTime(localDateTime.format(DateTimeFormatter.ofPattern(DATE_TYPE3)));
            member.setUpdatedUser(registerDto.getOperateUser());
            member.setRegTime(localDateTime.format(DateTimeFormatter.ofPattern(DATE_TYPE4)));
            //第三方appKey
            member.setAppKey(appKey);
            //邀请码
            if (StringUtils.isNotBlank(registerDto.getInvitationCode())) {
                //通过邀请码查询相应的shareId
                String shareId = membershipClauseLogMapper.queryShareIdByInvitationCode(registerDto.getInvitationCode().toLowerCase());
                if (StringUtils.isNotBlank(shareId)) {
                    logger.info("邀请码" + registerDto.getInvitationCode() + "对应分享ID" + shareId);
                    member.setShareUid(shareId.trim().replace(' ', '+'));
                } else {
                    logger.info("邀请码" + registerDto.getInvitationCode() + "无效邀请码");
                    HidLog.membership(LogPoint.MEMBER_REGISTER, StatusCode.INVITED_CODE_ERROR.getMsg(), mobilePhone, false);
                    throw new RegisterException(StatusCode.INVITED_CODE_ERROR);
                }
            }
            //好友分享shareId(H5)
            if (StringUtils.isNotBlank(registerDto.getShareUid())) {
                logger.info("分享Id=" + registerDto.getShareUid());
                member.setShareUid(registerDto.getShareUid().replace(' ', '+'));
                logger.info("处理完的分享Id=" + member.getShareUid());
            }
            if (appKey.startsWith(BussinessConstants.SECOND_CHANNEL_PRE)) {
                secondAppKey = appKey;
            }
            // 查询二级渠道信息
            if (StringUtils.isBlank(secondAppKey)) {
                // 通过一级渠道查询二级渠道信息
                List<SecondAppKeyManager> appKeyManagers = secondAppKeyManagerMapper.selectListByFirstAppKey(appKey);
                if (CollectionUtils.isNotEmpty(appKeyManagers)) {
                    SecondAppKeyManager secondAppKeyManager = appKeyManagers.get(0);
                    secondAppKey = secondAppKeyManager.getSecondAppKey();
                }
            }

            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
            transactionStatus = txManager.getTransaction(def);
            //更新会员条款版本号
            String typeOneVersion = JedisUtil.hget("typeVersionMap", "1");
            String typeTwoVersion = JedisUtil.hget("typeVersionMap", "2");
            member.setServiceVer(typeOneVersion + "," + typeTwoVersion);
            member.setMid(midGenerator.getMid());
            int insertSelective = membershipInfoMapper.insertSelective(member);
            if (insertSelective > 0) {
                //插入条款历史
                insertMemberClaseLog(userId, registerDto.getOperateUser(), "1_" + typeOneVersion);
                insertMemberClaseLog(userId, registerDto.getOperateUser(), "2_" + typeTwoVersion);
                logger.info("mobile=" + mobilePhone + "注册成功");
                //绑定设备号
                String imeiNo = registerDto.getImeiNo();
                memberOperateServ.bindImei(imeiNo, mobilePhone, userId);
                logger.info("mobile=" + mobilePhone + " imeiNo=" + imeiNo + "绑定设备成功");
                txManager.commit(transactionStatus);
                //第三方渠道回调
                notifyRegisterSuccess(mobilePhone, appKey,null);
                //消息推送
                //会员注册事件推送时，增加shareUid。
                registerDto.setShareUid(member.getShareUid());
                sendMessage(userId, registerDto);
                //数据埋点
                //writeRegisterData(userId, registerDto);
                HidLog.membership(LogPoint.MEMBER_REGISTER, LogPoint.MEMBER_REGISTER.getLogType(), userId, true);
                HidLog.membership(LogPoint.MEMBER_REGISTER, "注册成功", userId, true);
                successFlag = true;
                return userId;
            } else {
                HidLog.membership(LogPoint.MEMBER_REGISTER, StatusCode.SYSTEM_ERROR.getMsg(), mobilePhone, false);
                throw new RegisterException(StatusCode.SYSTEM_ERROR);
            }
        }catch (Exception e) {
            if(null != transactionStatus) {
                txManager.rollback(transactionStatus);
            }
            if(e instanceof RegisterException){
                try {
                    registerTrack(appKey, secondAppKey,-1, e.getMessage(), registerDto.getProvince(), registerDto.getCity(),
                            registerDto.getInvitationCode(), userId, registerDto.getAnonymousId(), registerDto.getTrackPlatForm(),registerDto.getOperationModel(),registerDto.getRegisterWay());
                } catch (Exception ex) {
                    logger.error("注册埋点失败", ex);
                }
                throw e;
            }else{
                logger.error("注册失败,原因：创建会员记录失败，" + e.getMessage(), e);
                throw new RegisterException(StatusCode.SYSTEM_ERROR);
            }
        }
       finally {
            if (jedisLock != null && jedis != null) {
                jedisLock.releaseLua(jedis);
                jedis.close();
                logger.debug("注册服务-释放redis锁和链接成功");
            }
            if (successFlag) {
                try {
                    registerTrack(appKey, secondAppKey,0, StringUtils.EMPTY,
                            registerDto.getProvince(), registerDto.getCity(), registerDto.getInvitationCode(), userId, registerDto.getAnonymousId(), registerDto.getTrackPlatForm(),registerDto.getOperationModel(),registerDto.getRegisterWay());
                } catch (Exception ex) {
                    logger.error("注册埋点失败", ex);
                }
            }
        }
    }

    @Override
    @Transactional
    public void changeBindMobilePhone(String mobilePhone, String newMobilePhone, String driverCode, String verifyCode, String imei) throws ChangeMobileException {
        logger.info("changeBindMobilePhone: mobilePhone" + mobilePhone + "newMobilePhone: "
                + newMobilePhone + "driverCode:" + driverCode + "verifyCode:" + verifyCode + "imei:" + imei);
        ChangeMobileDto changeMobileDto = new ChangeMobileDto();
        changeMobileDto.setDriverCode(driverCode);
        changeMobileDto.setImei(imei);
        changeMobileDto.setMobilePhone(mobilePhone);
        changeMobileDto.setNewMobilePhone(newMobilePhone);
        changeMobileDto.setVerifyCode(verifyCode);
        try {
            this.changeBindMobilePhoneV2(changeMobileDto);
        }catch (ChangeMobileException e){
            throw new ChangeMobileException(-1,e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeBindMobilePhoneV2(ChangeMobileDto changeMobileDto) throws ChangeMobileException {
        String verifyCode = changeMobileDto.getVerifyCode();
        String newMobilePhone = changeMobileDto.getNewMobilePhone();
        String mobilePhone = changeMobileDto.getMobilePhone();
        String driverCode = changeMobileDto.getDriverCode();
        String imei = changeMobileDto.getImei();
        logger.info("changeBindMobilePhone: mobilePhone" + mobilePhone + "newMobilePhone: "
                + newMobilePhone + "driverCode:" + driverCode + "verifyCode:" + verifyCode + "imei:" + imei);
        /**
         * 判断设备是否在黑名单
         */
        if (this.checkImeiBlackList(imei)) {
            HidLog.membership(LogPoint.CHANGE_BIND_MOBILE_PHONE, "设备号在黑名单,mobilePhone" + mobilePhone + "newMobilePhone: "
                    + newMobilePhone + "driverCode:" + driverCode + "verifyCode:" + verifyCode + "imei:" + imei, mobilePhone, false);
            throw new ChangeMobileException(StatusCode.UPDATE_FAILED);
        }
        /**
         * 1、通过手机号和证件号查询用户是否存在，并验证
         */
        MembershipBaseInfo memberBaseInfo = membershipInfoMapper.selectBaseByDriverCode(driverCode, 0,changeMobileDto.getOrgId());
        if (memberBaseInfo == null || !StringUtils.equals(memberBaseInfo.getMobilePhone(), mobilePhone)) {
            throw new ChangeMobileException(StatusCode.AUTH_ID_ERROR);
        }
        /**
         * 2、验证手机号是否已经被占用.<br>
         */
        AccountStatusDto account = membershipInfoMapper.getAccountStatusByPhone(newMobilePhone, 0,changeMobileDto.getOrgId());
        if(account != null) {
            if(AccountStatusEnum.NORMAL.getCode().equals(account.getAccountStatus())) {
                throw new ChangeMobileException(StatusCode.MOBILE_USED);
            }
            else if(AccountStatusEnum.FREEZED.getCode().equals(account.getAccountStatus())) {
                throw new ChangeMobileException(StatusCode.MOBILE_FREEZING);
            }
        }
        /**
         * 3、校验验证码.
         */
        if (!memberOperateServ.checkVerifyCode(newMobilePhone, verifyCode, true)) {
            throw new ChangeMobileException(StatusCode.INVITED_CODE_ERROR);
        }
        /**
         * 4、验证旧手机号绑定imei的次数是否超过限制，并绑定imei<br>
         */
        if (!memberOperateServ.bindImei(imei, newMobilePhone, mobilePhone, true)) {
            throw new ChangeMobileException(StatusCode.BIND_LIMIT);
        }

        /**
         * 5、更新手机号
         */
        if (!changeMobilePhone(mobilePhone, newMobilePhone, memberBaseInfo.getAuthId(), 0,changeMobileDto.getOrgId())) {
            throw new ChangeMobileException(StatusCode.UPDATE_FAILED);
        }
    }

    private boolean changeMobilePhone(String mobilePhone, String newMobilePhone, String authId, int membershipType,String orgId) {
        /**
         * 1、修改会员表.<br>
         */
        String updateTime = DateFormatUtils.format(System.currentTimeMillis(), DateType.DATE_TYPE3);
        int result = membershipInfoMapper.updateMobilePhone(authId, membershipType, newMobilePhone, updateTime, authId,orgId);
        if (result != 1) {
            return false;
        }
        /**
         * 2、添加操作日志.<br>
         */
        String message = "更换手机号绑定" + ", " + mobilePhone + "->" + newMobilePhone;
        UserOperatorLog userOperatorLog = new UserOperatorLog();
        userOperatorLog.setForeignKey(authId);
        userOperatorLog.setOperatorContent(message);
        userOperatorLog.setCreatedUser(newMobilePhone);
        userOperatorLog.setCreatedTime(updateTime);
        userOperatorLogMapper.saveSelective(userOperatorLog);
        //记录新表操作日志
        UserOperationLogInput userOptRecord = new UserOperationLogInput();
        userOptRecord.setAuthId(authId);
        userOptRecord.setRefKey1(newMobilePhone);
        userOptRecord.setOperationType(MemOperateTypeEnum.CHANGE_MOBILE.getCode());
        userOptRecord.setOperationContent(message);
        userOptRecord.setOperator("APP");
        saveUserOperationLog(userOptRecord);
        return true;
    }


    /**
     * 注册渠道回调
     *
     * @param mobilePhone
     * @param appKey
     */
    private void notifyRegisterSuccess(String mobilePhone, String appKey,String secondAppKey) {
        if (StringUtils.isNotEmpty(appKey) && APPKEY_REGISTER_CALLBACK.contains(appKey)) {
            Map<String, String> appKeyMap = membershipInfoMapper.selectOneByAppKey(appKey);
            if (MapUtils.isNotEmpty(appKeyMap)) {
                try {
                    String postUrl = appKeyMap.get("postUrl");
                    String appSecret = appKeyMap.get("appSecret");
                    //头信息
                    Map<String, Object> headerMap = new HashMap<>(3);
                    String timestamp = "" + System.currentTimeMillis();
                    headerMap.put("AppKey", appKey);
                    headerMap.put("TimeStamp", timestamp);
                    String str = appKey + appSecret + timestamp + mobilePhone;
                    String sign = DigestUtils.md5Hex(str.getBytes());
                    headerMap.put("sn", sign);
                    //参数
                    Map<String, Object> paramMap = new HashMap<>(2);
                    paramMap.put("registerResult", 0);
                    paramMap.put("mobilePhone", mobilePhone);
                    HttpClientUtils.httpPostRequest(postUrl, headerMap, paramMap);
                } catch (Exception e) {
                    //e.printStackTrace();
                    logger.error("渠道注册回调，mobilePhone=" + mobilePhone + " appKey=" + appKey, e);
                }
            }
        }

        // 注册成功后的 通知
        IRegister registerHandler = registerAdapter.getRegisterService(secondAppKey);
        if (registerHandler != null) {
            NotifyRegisterDto notifyRegisterDto = new NotifyRegisterDto();
            notifyRegisterDto.setSecondAppKey(secondAppKey);
            notifyRegisterDto.setMobilePhone(mobilePhone);
            registerHandler.notifyRegisterSuccess(notifyRegisterDto);
        }

    }


    /**
     * 推送会员注册事件
     *
     * @param authId
     * @param registerDto
     */
    public void sendMessage(String authId, RegisterDto registerDto) {
        try {
            MemberRegister memberRegister = new MemberRegister();
            memberRegister.setAuthId(authId);
            memberRegister.setMemberType(0);
            memberRegister.setMobilePhone(registerDto.getMobilePhone().trim());
            memberRegister.setDataOrigin(String.valueOf(registerDto.getRegisterOrigin()));
            memberRegister.setProvince(registerDto.getProvince());
            memberRegister.setCity(registerDto.getCity());
            memberRegister.setArea(registerDto.getArea());
            memberRegister.setCreatedTime(new Date());
            memberRegister.setAppKey(registerDto.getAppKey());
            memberRegister.setShareUid(registerDto.getShareUid());
            byte[] messageBody = ProtobufUtil.serializeProtobuf(memberRegister);
            String messageKey = "membership#" + UUID.randomUUID().toString().replace("-", "");
            Message msg = new Message(evcardRawDataTopic, EventEnum.MEMBER_REGISTER.getTag(), messageBody);
            msg.setKey(messageKey);
            producer.send(msg);
        } catch (Exception e) {
            logger.error("推送会员注册事件失败，authId=" + authId, e);
        }
    }

    /**
     * 插入会员条款记录
     *
     * @param authId
     * @param operator
     * @param version
     */
    private int insertMemberClaseLog(String authId, String operator, String version) {
        MembershipClauseLog membershipClauseLog = new MembershipClauseLog();
        membershipClauseLog.setAuthid(authId);
        membershipClauseLog.setCreateOperName(operator);
        membershipClauseLog.setUpdateOperName(operator);
        membershipClauseLog.setCreateTime(new Date());
        membershipClauseLog.setUpdateTime(new Date());
        membershipClauseLog.setVersion(version);
        return membershipClauseLogMapper.insertSelective(membershipClauseLog);
    }


    /**
     * 注册埋点
     *
     * @param authId
     * @param registerDto
     */
    private void writeRegisterData(String authId, RegisterDto registerDto) {
        try {
            Map<String, Object> properties = new HashMap<String, Object>();
            properties.put("register_city", registerDto.getCity());
            String orderPlatform = StringUtils.EMPTY;
            if (StringUtils.isNotBlank(registerDto.getAppKey())){
                AppKeyDto appKeyDto = platformService.getAppKey(registerDto.getAppKey());
                if (appKeyDto != null) {
                    orderPlatform = appKeyDto.getPlatName();
                }
            }
            properties.put("register_platform", orderPlatform);
            properties.put("register_source", registerDto.getAppKey());
            properties.put("register_time_modified", ComUtil.getFormatDate(new Date(), ComUtil.DATE_TYPE1));
            if(StringUtils.isNotBlank(registerDto.getAnonymousId())) {
            	sensorsdataService.trackSignUp(authId, registerDto.getAnonymousId());
            }
            sensorsdataService.profileSet(authId, true, properties);
        } catch (Exception e) {
            logger.error("神策数据埋点-记录注册事件时捕获到异常", e);
        }
    }


    @Override
    public BigDecimal consumAmount(String authId) {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public boolean keywordScan(String content) {
        boolean flag = true;
        try {
            logger.warn("敏感词检测：keywordScan，content={}", StringUtils.abbreviate(content, 100));
            IClientProfile profile = DefaultProfile.getProfile(aliyunConfig.getRegionId(),
                    aliyunConfig.getAccessKeyId(), aliyunConfig.getAccessKeySecret());
            DefaultProfile.addEndpoint(aliyunConfig.getRegionId(), aliyunConfig.getRegionId(), "Green", aliyunConfig.getGreenEndpoints());
            IAcsClient client = new DefaultAcsClient(profile);

            TextScanRequest textScanRequest = new TextScanRequest();
            textScanRequest.setAcceptFormat(FormatType.JSON);
            textScanRequest.setContentType(FormatType.JSON);
            textScanRequest.setMethod(com.aliyuncs.http.MethodType.POST);
            textScanRequest.setEncoding("UTF-8");
            textScanRequest.setRegionId(aliyunConfig.getRegionId());

            List<Map<String, Object>> tasks = new ArrayList<>();

            Map<String, Object> task1 = new LinkedHashMap<>();
            task1.put("dataId", UUID.randomUUID().toString());
            task1.put("content", content);

            tasks.add(task1);

            JSONObject data = new JSONObject();
            data.put("scenes", Collections.singletonList("keyword"));
            data.put("tasks", tasks);

            //关键字检测
            textScanRequest.setContent(data.toJSONString().getBytes("UTF-8"), "UTF-8", FormatType.JSON);
            textScanRequest.setConnectTimeout(3000);
            textScanRequest.setReadTimeout(6000);
            HttpResponse httpResponse = client.doAction(textScanRequest);
            if (httpResponse.isSuccess()) {
                JSONObject scrResponse = JSON.parseObject(new String(httpResponse.getContent(), "UTF-8"));
                if (200 == scrResponse.getInteger("code")) {
                    JSONArray taskResults = scrResponse.getJSONArray("data");
                    for (Object taskResult : taskResults) {
                        if (200 == ((JSONObject) taskResult).getInteger("code")) {
                            JSONArray sceneResults = ((JSONObject) taskResult).getJSONArray("results");
                            for (Object sceneResult : sceneResults) {
                                String suggestion = ((JSONObject) sceneResult).getString("suggestion");
                                if ("block".equals(suggestion)) {
                                    logger.warn("敏感词检测不通过={}", JSON.toJSONString(scrResponse, true));
                                    flag = false;
                                }
                            }
                        } else {
                            logger.warn("task process fail:" + ((JSONObject) taskResult).getInteger("code"));
                        }
                    }
                } else {
                    logger.warn("detect not success. code:" + scrResponse.getInteger("code"));
                }
            } else {
                logger.warn("response not success. status:" + httpResponse.getStatus());
            }
            //反垃圾检测
            data.put("scenes", Collections.singletonList("antispam"));
            textScanRequest.setContent(data.toJSONString().getBytes("UTF-8"), "UTF-8", FormatType.JSON);
            textScanRequest.setConnectTimeout(3000);
            textScanRequest.setReadTimeout(6000);
            httpResponse = client.doAction(textScanRequest);
            if (httpResponse.isSuccess()) {
                JSONObject scrResponse = JSON.parseObject(new String(httpResponse.getContent(), "UTF-8"));
                if (200 == scrResponse.getInteger("code")) {
                    JSONArray taskResults = scrResponse.getJSONArray("data");
                    for (Object taskResult : taskResults) {
                        if (200 == ((JSONObject) taskResult).getInteger("code")) {
                            JSONArray sceneResults = ((JSONObject) taskResult).getJSONArray("results");
                            for (Object sceneResult : sceneResults) {
                                String suggestion = ((JSONObject) sceneResult).getString("suggestion");
                                if ("block".equals(suggestion)) {
                                    logger.warn("反垃圾检测不通过={}", JSON.toJSONString(scrResponse, true));
                                    flag = false;
                                }
                            }
                        } else {
                            logger.warn("task process fail:" + ((JSONObject) taskResult).getInteger("code"));
                        }
                    }
                } else {
                    logger.warn("detect not success. code:" + scrResponse.getInteger("code"));
                }
            } else {
                logger.warn("response not success. status:" + httpResponse.getStatus());
            }
        } catch (Exception e) {
            flag = false;
            logger.error(e.getMessage(), e);

        }
        return flag;
    }


    @Override
    public void autoSignContract(SignContractDto signContractDto) throws MemberException {
        memberShipContractServ.autoSignContract(signContractDto);

//        //TODO 签约暂时发布原始接口
//        //用户ID
//        String authId = signContractDto.getAuthId();
//        //查询会员
//        MembershipInfoWithBLOBs member = membershipInfoMapper.selectByAuthId(authId, 0);
//        //个人CA申请的customId
//        String customerId = member.getCustomerId();
//        if (StringUtils.isBlank(customerId)) {
//            //先调用法大大个人CA申请
//            if (StringUtils.isBlank(member.getName()) || StringUtils.isBlank(member.getMobilePhone())) {
//                throw new MemberException(-1, "请先完成身份认证");
//            }
//            try {
//                String identType = ComUtil.checkIDCard(member.getDriverCode()) ? "0" : "1";
//                String response = client.invokeSyncPersonAuto(member.getName(), "", member.getDriverCode(), identType, member.getMobilePhone());
//                logger.info("法大大个人CA申请接口, authId={}, 返回={}", authId, response);
//                JSONObject jsonObject = JSONObject.parseObject(response);
//                Assert.equals(jsonObject.getString("code"), "1000", "个人CA申请失败");
//                customerId = jsonObject.getString("customer_id");
//                //保存customId
//                membershipInfoMapper.saveCustomerId(signContractDto.getAuthId(), customerId);
//            } catch (Exception e) {
//                logger.error("自动签署合同异常， authId=" + authId, e);
//                throw new MemberException(-1, "个人CA申请失败");
//            }
//        }
//        List<ContractVersionDto> contractVersionDtoList = signContractDto.getContractVersionDtoList();
//        if (CollectionUtils.isNotEmpty(contractVersionDtoList)) {
//            for (ContractVersionDto p : contractVersionDtoList) {
//                //模板编号
//                String templateId = p.getTemplateId();
//                //合同标题
//                String docTitle = p.getDocTitle();
//                if (StringUtils.isBlank(authId) || StringUtils.isBlank(templateId) || StringUtils.isBlank(docTitle)) {
//                    throw new MemberException(-1, "请检查参数完整性");
//                }
//                //检查当前版本是否已经签署过
//                if (membershipInfoMapper.countContract(authId, customerId, templateId) == 0) {
//                    try {
//                        //通过模板先生成合同
//                        JSONObject templateParam = new JSONObject();
//                        templateParam.put("evcard", "");
//                        String contractId = UUID.randomUUID().toString().replace("-", "");
//                        String response = client.invokeGenerateContract(templateId, contractId, p.getDocTitle(), null, null, templateParam.toJSONString(), null);
//                        logger.info("法大大生成合同接口, authId={}, 返回={}", authId, response);
//                        JSONObject jsonObject = JSONObject.parseObject(response);
//                        Assert.equals(jsonObject.getString("code"), "1000", "生成合同失败");
//                        //自动签署合同
//                        JSONObject searchLocation = new JSONObject();
//                        searchLocation.put("pagenum", 0);
//                        searchLocation.put("x", 420);
//                        searchLocation.put("y", 180);
//                        JSONArray signaturePositions = new JSONArray();
//                        signaturePositions.add(searchLocation);
//                        String transactionId = UUID.randomUUID().toString().replace("-", "");
//                        String response1;
//                        try {
//                            response1 = client.invokeExtSignAutoXY(transactionId, customerId, "1", contractId, p.getDocTitle(), signaturePositions.toJSONString(), null);
//                        }catch (Exception e){
//                            //超时异常重试一次
//                            response1 = client.invokeExtSignAutoXY(transactionId, customerId, "1", contractId, p.getDocTitle(), signaturePositions.toJSONString(), null);
//                        }
//                        logger.info("法大大生自动签署合同接口, authId={}, 返回={}", authId, response1);
//                        JSONObject jsonObject1 = JSONObject.parseObject(response1);
//                        Assert.equals(jsonObject1.getString("code"), "1000", "自动签署合同失败");
//                        //合同归档
//                        try {
//                            String response2 = client.invokeContractFilling(contractId);
//                            logger.info("法大大生归档合同接口, authId={}, 返回={}", authId, response2);
//                        }catch (Exception e){
//                            logger.warn("法大大生归档合同接口调用失败, authId=" + authId + "，contractId="+contractId, e);
//                            //超时异常重试一次
//                            try {
//                                String response2 = client.invokeContractFilling(contractId);
//                                logger.info("法大大生归档合同接口, authId={}, 返回={}", response2);
//                            }catch (Exception e1){
//                                logger.warn("法大大生归档合同接口, 调用失败,authId=" + authId + "contractId="+contractId, e1);
//                            }
//                        }
//                        //保存合同签署记录记录
//                        UserContract userContract = new UserContract();
//                        userContract.setAuthId(authId);
//                        userContract.setContractId(contractId);
//                        userContract.setCustomerId(customerId);
//                        userContract.setTransactionId(transactionId);
//                        userContract.setTemplateId(templateId);
//                        userContract.setVersionId(p.getVersionId());
//                        userContract.setDownloadUrl(jsonObject1.getString("download_url"));
//                        userContract.setViewpdfUrl(jsonObject1.getString("viewpdf_url"));
//                        userContractMapper.insertSelective(userContract);
//                        logger.info("authid=" + authId + " 签署法大大合同成功");
//                    } catch (Exception e) {
//                        logger.error("自动签署合同失败，authId=" + authId, e);
//                        throw new MemberException(-1, "签署合同失败");
//                    }
//                }
//            }
//        }
    }


    @Resource
    private CardActionMapper cardActionMapper;

    @Override
    @Deprecated
    public void saveCardAction(CardActionDto cardActionDto) {
        CardAction cardAction = new CardAction();
        BeanCopyUtils.copyProperties(cardActionDto, cardAction);
        cardAction.setReturnCode(cardActionDto.getReturnCode());
        cardAction.setCreatedUser(cardActionDto.getCreatedUser());
        cardAction.setUpdatedUser(cardActionDto.getCreatedUser());
        String time = DateFormatUtils.format(System.currentTimeMillis(), DateType.DATE_TYPE3);
        cardAction.setCreatedTime(time);
        cardAction.setUpdatedTime(time);
        cardActionMapper.insert(cardAction);
    }

    @Value("${secretLogTopic}")
    private String secretLogTopic;

    @Override
    public MembershipSecretInfo getMembershipSecretInfo(String authId, int messageType, String optUser, String systemFlag) {
        return this.getMembershipSecretInfo(authId, 0, messageType, optUser, systemFlag);
    }

    /**
     *  20230509 兼容履约上传的图片不展示问题
     *  例如 http://evcard-sensitive.oss-cn-shanghai-internal.aliyuncs.com/prod/sensitiveBucket/newDriverCardImg/xxx.jpg
     */
    public String adapterOfcPic(String imageUrl){
        try {
            String path = "http://evcard-sensitive.oss-cn-shanghai-internal.aliyuncs.com/" + CommConfigUtil.getENV();
            String path2 = path + "/sensitiveBucket";
            if (imageUrl.startsWith(path2)) {
                logger.info("兼容履约上传的图片数据命中,imageUrl[{}]",imageUrl);
                imageUrl = imageUrl.replace(path, "");
            }
        } catch (Exception e) {
            logger.error("兼容履约上传的图片数据异常,imageUrl[{}]",imageUrl,e);
        }
        return imageUrl;
    }

    @Override
    public MembershipSecretInfo getMembershipSecretInfo(String authId, int membershipType, int messageType, String optUser, String systemFlag) {
        MembershipInfoWithBLOBs member = membershipInfoMapper.selectByAuthId(authId, membershipType);
        if (member == null) {
            if (membershipType == 0) {
                logger.info("getMembershipSecretInfo兼容擎路用户,authId={}",authId);
                return getMembershipSecretInfo(authId,2,messageType,optUser,systemFlag);
            }
            return null;
        }
        MembershipSecretInfo secretInfo = new MembershipSecretInfo();
        secretInfo.setAuthId(authId);
        secretInfo.setMessageType(messageType);
        //1：手机号; 2:驾照号;3：驾照图片;4：人脸图片;5: 邮寄地址; 6：姓名; 7：身份证号
        if (messageType == 1) {
            secretInfo.setMessage(member.getMobilePhone());
        } else if (messageType == 2) {
            secretInfo.setMessage(member.getDriverCode());
        } else if (messageType == 3) {
            //人脸照片地址
            if(StringUtils.isNotBlank(member.getFaceRecognitionImgUrl())) {
                secretInfo.setMessage(ComUtil.subString(adapterOfcPic(member.getFaceRecognitionImgUrl())));
            }
        } else if (messageType == 4) { //DRIVING_LICENSE_IMG_URL
            if(StringUtils.isNotBlank(member.getDrivingLicenseImgUrl())) {
        		secretInfo.setMessage(ComUtil.subString(adapterOfcPic(member.getDrivingLicenseImgUrl())));
        	}
        } else if (messageType == 5) {
            String province = StringUtils.isBlank(member.getProvince()) ? StringUtils.EMPTY : member.getProvince();
            String city = StringUtils.isBlank(member.getCity()) ? StringUtils.EMPTY : member.getCity();
            String area = StringUtils.isBlank(member.getArea()) ? StringUtils.EMPTY : member.getArea();
            String address = StringUtils.isBlank(member.getAddress()) ? StringUtils.EMPTY : member.getAddress();
            secretInfo.setMessage(province + city + area + address);
        } else if (messageType == 6) {
            secretInfo.setMessage(member.getName());
        } else if (messageType == 7) {
//        	if(StringUtils.isNotBlank(member.getIdCardNumber()) && member.getIdCardNumber().length() == 18) {
//        		secretInfo.setMessage(member.getIdCardNumber());
//        	} else if(StringUtils.isNotBlank(member.getPassportNo()) && member.getPassportNo().length() == 18) {
//        		secretInfo.setMessage(member.getPassportNo());
//        	} else if(StringUtils.isNotBlank(member.getDriverCode()) && member.getDriverCode().length() == 18){
//        		secretInfo.setMessage(member.getDriverCode());
//        	}
            secretInfo.setMessage(ComUtil.getUserIDCardNo(member));
        } else if (messageType == 8) {
            //驾照副页
            if(StringUtils.isNotBlank(member.getFileNoImgUrl())) {
                secretInfo.setMessage(ComUtil.subString(adapterOfcPic(member.getFileNoImgUrl())));
            }
        } else if (messageType == 9) {
            //身份证照片
            if(StringUtils.isNotBlank(member.getIdcardPicUrl())) {
                secretInfo.setMessage(ComUtil.subString(adapterOfcPic(member.getIdcardPicUrl())));
            }
        }else if (messageType == 10) {
          /*  //手持身份证照片
            MemberIdentityDocument memberIdentityDocument = memberIdentityDocumentMapper.selectByPrimaryKey(member.getIdentityId());
            if (memberIdentityDocument != null) {
                String holdIdcardPicUrl = memberIdentityDocument.getHoldIdcardPicUrl();
                if (StringUtils.isNotBlank(holdIdcardPicUrl)) {
                    secretInfo.setMessage(ComUtil.subString(holdIdcardPicUrl));
                }
            }*/
            //手持身份证照片
            if(StringUtils.isNotBlank(member.getHoldIdcardPicUrl())) {
                secretInfo.setMessage(ComUtil.subString(adapterOfcPic(member.getHoldIdcardPicUrl())));
            }
        }else if (messageType == 11) {
            secretInfo.setMessage(member.getPassportNo());
        } else if(messageType == 12) {
        	secretInfo.setMessage(ComUtil.getFileFullPath(member.getUserImgUrl()));
        }
        else if(messageType == 15) {
            MmpUserTag userTag = mmpUserTagMapper.selectMmpUserByAuthId(authId);
            if(userTag != null && StringUtils.isNotBlank(userTag.getStudentCardUrl())) {
                secretInfo.setMessage(ComUtil.getFileFullPath(userTag.getStudentCardUrl()));
            }
        }else if(messageType == 16) {
            //身份证正面url
            MemberIdentityDocument memberIdentityDocument = memberIdentityDocumentMapper.selectByPrimaryKey(member.getIdentityId());
            if (memberIdentityDocument != null) {
                String identityCardImgUrl = memberIdentityDocument.getIdentityCardImgUrl();
                if (StringUtils.isNotBlank(identityCardImgUrl)) {
                    secretInfo.setMessage(ComUtil.subString(adapterOfcPic(identityCardImgUrl)));
                }
            }
        }else if(messageType == 17) {
            //身份证 反面url
            MemberIdentityDocument memberIdentityDocument = memberIdentityDocumentMapper.selectByPrimaryKey(member.getIdentityId());
            if (memberIdentityDocument != null) {
                String reverseIdentityCardImgUrl = memberIdentityDocument.getReverseIdentityCardImgUrl();
                if (StringUtils.isNotBlank(reverseIdentityCardImgUrl)) {
                    secretInfo.setMessage(ComUtil.subString(adapterOfcPic(reverseIdentityCardImgUrl)));
                }
            }
        }
       /* try {
            MembershipSecretLog secretLog = new MembershipSecretLog();
            secretLog.setAuthId(authId);
            secretLog.setMessageType(messageType);
            secretLog.setOptUser(optUser);
            secretLog.setSystemFlag(systemFlag);
            secretLog.setTime(System.currentTimeMillis());
            messagepush.pushKafka(secretLogTopic, ProcessEnum.MEMBERSHIOP_SECRET_LOG.getTag(), ProtobufUtil.jprotobufSerialize(secretLog));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        catch (Throwable e) {
            logger.error(e.getMessage(), e);
        }*/

        return secretInfo;
    }


    @Override
    public MembershipAdditionalDto getAdditionalInfo(String authId) {
        MembershipAdditionalInfo additional = membershipAdditionalInfoMapper.selectByAuthId(authId, 0);
        if(null == additional) {
            return null;
        }
        MembershipAdditionalDto additionalDto = new MembershipAdditionalDto();
        BeanCopyUtils.copyProperties(additional, additionalDto);
        if(StringUtils.isNotBlank(additional.getDrivingLicenseImgUrl())) {
            additionalDto.setDrivingLicenseImgUrl(ComUtil.subString(additional.getDrivingLicenseImgUrl()));
        }
        if(StringUtils.isNotBlank(additional.getFileNoImgUrl())) {
            additionalDto.setFileNoImgUrl(ComUtil.subString(additional.getFileNoImgUrl()));
        }
        return additionalDto;
    }

    @Override
    @Transactional
    public String addInnerMembershipInfo(InnerNewMemberInfoDto innerMemberInfoDto, String createdUser) {
        String mobilePhone = innerMemberInfoDto.getMobilePhone();
        MembershipInfo innerMember = membershipInfoMapper.selectInnerMemberByMobile(mobilePhone,innerMemberInfoDto.getOrgId(),innerMemberInfoDto.getDriverCode());
        if (null != innerMember) {
            return innerMember.getAuthId();
        }
        //1、新增内部会员
        MembershipInfoWithBLOBs record = new MembershipInfoWithBLOBs();
        StringBuilder authId = new StringBuilder(innerMemberInfoDto.getMobilePhone());
        authId.append(ComUtil.getSystemDate(ComUtil.DATE_TYPE6));
        record.setAuthId(authId.toString());
        // 会员类型(0:外部会员 1：内部员工）
        record.setMembershipType((short) 1);
        record.setName(innerMemberInfoDto.getName());
        record.setMobilePhone(innerMemberInfoDto.getMobilePhone());
        record.setOrgId(innerMemberInfoDto.getOrgId());
        record.setMail(innerMemberInfoDto.getEmail());
        if (innerMemberInfoDto.getAreaid() != null) {
            record.setArea(innerMemberInfoDto.getAreaid());
        }
        record.setRegionid(innerMemberInfoDto.getRegionid());
        record.setDriverCode(innerMemberInfoDto.getDriverCode());
        String plainPassword = innerMemberInfoDto.getDriverCode().substring(innerMemberInfoDto.getDriverCode().length() - 6);// TODO
        String password = Crypto.encryptDES(plainPassword, ENCRYPT_KEY);
        record.setPassword(password);
        // 性别 0：男 1:女
        record.setGender("0");
        record.setRegTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE4));
        // 审核状态(0:待审核 1：审核通过 2: 审核不通过 3：用户无效 4:重新审核）
        record.setReviewStatus((short) 1);
        // 状态(0：有效 1：无效)
        record.setStatus((short) 0);
        // 是否免押金 0：不免押金 1：免押金
        record.setExemptDeposit((short) 0);
        record.setCreatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
        record.setCreatedUser(createdUser);
        record.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
        record.setUpdatedUser(createdUser);
        // 人员状态 0：正常 1：休息 2：离职
        record.setPersonnelState((short) 0);
        // 1 内部工作人员 2 洗车工作人员
        record.setTypeFlag((short) 1);
        record.setUnRegisterTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE4));
        record.setMid(midGenerator.getMid());
        membershipInfoMapper.insertSelective(record);
        //2、制虚拟卡
        CardInfo cardInfo = new CardInfo();
        String virtual_cardNo = ComUtil.getVirtualCardNo(cardInfoMapper);
        cardInfo.setCardNo(virtual_cardNo);
        cardInfo.setCardType(0.0);
        // 卡号类型（1:虚拟卡号，2:物理卡号）
        Integer cardNoType = 1;
        cardInfo.setCardNoType(cardNoType);
        cardInfo.setAuthId(authId.toString());
        String internalNo = ComUtil.generateInternalNo(cardInfoMapper, 1);
        cardInfo.setInternalNo(internalNo);
        cardInfo.setValidityTime("20200101");
        // 初始秘钥
        cardInfo.setRwKeytAa("ffffffffffff");
        cardInfo.setRwKeytBb("ffffffffffff");
        cardInfo.setStatus(0);
        cardInfo.setActivateStatus(1.0);
        cardInfo.setCreatedUser("Virtual_system");
        cardInfo.setCreatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
        cardInfo.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
        cardInfoMapper.addCardInfo(cardInfo);
        //3、更新虚拟卡给会员
        MembershipInfo membershipInfo = new MembershipInfo();
        membershipInfo.setCardNo(virtual_cardNo);
        membershipInfo.setAuthId(authId.toString());
        membershipInfoMapper.updateInnerMemberCardNoByAuthId(membershipInfo);
        //4、申请制卡进程
        ApplyProgress progress = new ApplyProgress();
        progress.setAuthId(authId.toString());
        progress.setProgressContent("审核通过");
        progress.setCreatedUser(createdUser);
        progress.setProgressTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE8));
        //新用户而且审核通过
        applyProgressMapper.addApplyProgress(progress);
        //5、保存日志
        String operatorContent = "添加会员卡【" + internalNo + "】";
        ComUtil.insertOperatorLog(operatorContent, authId.toString(), "0", createdUser, userOperatorLogMapper);
        CardInfoHistroy cardInfoHistroy = new CardInfoHistroy();
        cardInfoHistroy.setCardStatus(1.0);
        cardInfoHistroy.setUpdatedUser("Virtual_system");
        cardInfoHistroy.setAuthId(authId.toString());
        cardInfoHistroy.setCardNo(virtual_cardNo);
        Integer num1 = cardInfoHistroyMapper.updateCardInfoHistroy(cardInfoHistroy);
        if (num1 < 1) {
            cardInfoHistroy.setCreatedUser("Virtual_system");
            cardInfoHistroy.setMembershipType(1d);
            cardInfoHistroyMapper.addCardInfoHistroy(cardInfoHistroy);
        }
        operatorContent = "用户审核通过";
        ComUtil.insertOperatorLog(operatorContent, authId.toString(), "0", createdUser, userOperatorLogMapper);
        return authId.toString();
    }

    @Override
    public void handleUserInfo(Map<String, Object> properties, String authId) {
        MembershipBaseInfo membershipBaseInfo = membershipInfoMapper.selectBaseByAuthId(authId, 0);
        if (membershipBaseInfo == null) {
            return;
        }
        String cityOfOrigin = membershipBaseInfo.getCityOfOrigin();
        if (StringUtils.isNotEmpty(cityOfOrigin)) {
            List<City> cityList = cityMapper.selectCity(cityOfOrigin);
            if (CollectionUtils.isNotEmpty(cityList)) {
                String orgId = cityList.get(0).getOrgId();
                properties.put("orgId", orgId);
            }
        }
        String appKey = membershipBaseInfo.getAppKey();
        properties.put("appKey", appKey == null ? "" : appKey);
        int dataOrigin = membershipBaseInfo.getDataOrigin().intValue();
        String registerOrigin;
        switch (dataOrigin) {
            case 0:
                registerOrigin = "网点注册";
                break;
            case 1:
                registerOrigin = "网站注册";
                break;
            case 2:
                registerOrigin = "管理平台注册";
                break;
            case 3:
                registerOrigin = "手机APP";
                break;
            case 4:
                registerOrigin = "第三方";
                break;
            case 5:
                registerOrigin = "e享天开";
                break;
            case 6:
                registerOrigin = "e享天开/evcard共同会员";
                break;
            case 7:
                registerOrigin = "CRM";
                break;
            default:
                registerOrigin = "手机APP";
        }
        properties.put("registerOrigin", registerOrigin);
        int reviewStatus = membershipBaseInfo.getReviewStatus().intValue();
        String reviewDesc;
        switch (reviewStatus) {
            case 0:
                reviewDesc = "待审核";
                break;
            case 1:
                reviewDesc = "审核通过";
                break;
            case 2:
                reviewDesc = "审核不通过";
                break;
            default:
                reviewDesc = "审核不通过";
        }
        properties.put("reviewStatus", reviewDesc);
    }

    @Override
    @Deprecated
    public UserInfoDto getUserInfoByToken(String authId) throws UserMessageException {
        return this.getUserInfoByToken(authId,null);
    }

    @Override
    public UserInfoDto getUserInfoByToken(String authId, String appKey) throws UserMessageException {
        if (StringUtils.isBlank(authId)) {
            throw UserMessageException.PARAM_ERROR;
        }
        UserInfoDto userInfoDto = new UserInfoDto();
        MembershipInfoWithBLOBs membershipInfo = membershipInfoMapper.selectByAuthId(authId, 0);
        if (membershipInfo == null) {
            throw UserMessageException.MEMBER_NOT_EXIST;
        }
        setUserInfoDto(membershipInfo, userInfoDto, appKey);
        //认证状态
        int authenticationStatus = membershipInfo.getAuthenticationStatus();
        userInfoDto.setAuthenticationStatus(authenticationStatus);
        //是否是老用户（审核通过 / 待审核）
        int reviewStatus = membershipInfo.getReviewStatus().intValue();
        userInfoDto.setReviewStatus(reviewStatus);
        DateTimeFormatter ymd = DateTimeFormatter.ofPattern(ComUtil.DATE_TYPE4);
        LocalDateTime createDateTime = LocalDateTime.parse(membershipInfo.getCreatedTime().substring(0, 14), ymd);
        //如果老用户没有去认证需要显示认证截止日期
        if ((reviewStatus == 1 || reviewStatus == 0) && BussinessConstants.OLD_USER_DATETIME.isAfter(createDateTime) && authenticationStatus != 2) {
            userInfoDto.setIsOldUser(1);
            LocalDateTime checkDateTime = BussinessConstants.OLD_USER_DATETIME.plus(6, ChronoUnit.MONTHS);
            String formatDate = checkDateTime.format(DateTimeFormatter.ofPattern(ComUtil.DATE_TYPE22));
            userInfoDto.setOldUserAuthenticationEndDate(formatDate);
        }
        //审核状态描述（话术）
        String desc = getReviewDescByStatus(reviewStatus, authenticationStatus, userInfoDto.getIsForeign(), userInfoDto.getIsOldUser(), userInfoDto.getIsExpire());
        userInfoDto.setReviewStatusDesc(desc);
        //添加驾照认证状态&驾照三要素认证状态&驾照状态描述信息
        userInfoDto.setLicenseAuthStatus(membershipInfo.getLicenseAuthStatus());
        userInfoDto.setLicenseElementsAuthStatus(membershipInfo.getLicenseElementsAuthStatus());
        userInfoDto.setElementsReviewItems(membershipInfo.getElementsReviewItems());
        //兼容当前已为三要素不通过的历史数据
        if(StringUtils.isBlank(userInfoDto.getElementsReviewItems())
                && StringUtils.equals(String.valueOf(membershipInfo.getLicenseElementsAuthStatus()), "2")) {
            userInfoDto.setElementsReviewItems("000");
        }
        //用户信息是否可以编辑/开关
        if (JedisUtil.exists(BussinessConstants.IS_EDIT_REDIS_KEY)) {
            userInfoDto.setIsEdit(1);
        }
        //增加会员卡id状态
        List<CardInfo> cardInfos = cardInfoMapper.getCardInfo(userInfoDto.getCardNo());
        if (CollectionUtils.isNotEmpty(cardInfos)) {
            CardInfo cardInfo = cardInfos.get(0);
            userInfoDto.setActivityStatus(cardInfo.getActivateStatus().intValue());
            userInfoDto.setCardStatus(cardInfo.getStatus());
        }
        String cityOfOriginOrgId = StringUtils.EMPTY;
        if (StringUtils.isNotBlank(membershipInfo.getCityOfOrigin())){
            List<City> cityList = cityMapper.selectCity(membershipInfo.getCityOfOrigin());
            if (CollectionUtils.isNotEmpty(cityList)){
                try{
                    userInfoDto.setCitylat(cityList.get(0).getLat().doubleValue());
                    userInfoDto.setCityLon(cityList.get(0).getLon().doubleValue());
                    cityOfOriginOrgId = cityList.get(0).getOrgId();
                }catch (Exception e){
                    //防止空指针
                }
            }
        }
        MmpUserTag mmpUserTag = mmpUserTagMapper.selectMmpUserByAuthId(authId);
        if (mmpUserTag != null){
            userInfoDto.setProfession(mmpUserTag.getProfession());
            userInfoDto.setEducational(mmpUserTag.getEducational());
            userInfoDto.setOwnCar(mmpUserTag.getOwnCar());
            userInfoDto.setDrivingLicenseAddress(mmpUserTag.getSpare9());
        }
        userInfoDto.setOrgId(cityOfOriginOrgId);
        return userInfoDto;
    }

    @Override
    public int queryInsideFlag(String agencyId) {
        return agencyInfoMapper.getFlagByAgencyId(agencyId);
    }


    @Override
    public AgencyInfoDto queryAgencyInfoByAgencyId(String agencyId) {
        AgencyInfoDto agencyInfoDto = null;
        if(StringUtils.isNotBlank(agencyId)){
            AgencyInfo agencyInfo = agencyInfoMapper.queryAgencyInfoByAgencyId(agencyId);
            if(agencyInfo != null ){
                agencyInfoDto = new AgencyInfoDto();
                BeanUtils.copyProperties(agencyInfo,agencyInfoDto);
            }
        }
        return agencyInfoDto;
    }

    @Override
    public int getOrgCardStatusById(String orgId, String cardNo) {
        return cardInfoMapper.getOrgCardStatusById(orgId, cardNo);
    }

    @Override
    public ReviewAndCarStatusDto reviewAndCardStatus(String authId, Integer membershipType) {
        if (StringUtils.isBlank(authId) || membershipType == null) {
            return null;
        }
        ReviewAndCarStatusDto reviewAndCarStatusDto = membershipInfoMapper.reviewAndCardStatus(authId, membershipType);
        if (reviewAndCarStatusDto == null && membershipType ==0) {
            logger.info("reviewAndCardStatus 兼容擎路用户,authId={}",authId);
            return reviewAndCardStatus(authId,2);
        }
        return reviewAndCarStatusDto;
    }

    @Override
    public List<String> getAllAuthId(String authId) {
        if (StringUtils.isBlank(authId)) {
            return null;
        }
        List<String> authIds = membershipInfoMapper.getAllAuthIdByUser(authId);
        return authIds;
    }

    @Override
    public AgencyMinsDto getAgencyMinsByid(String orgId) {
        if (StringUtils.isBlank(orgId)) {
            return null;
        }
        AgencyMinsDto dto = agencyInfoMapper.getAgencyMinsById(orgId);
        return dto;
    }


    @Value("${csms_address}")
    private String csmsAddress;

    @Override
    public MemberRuleVersionDTO queryMemberRuleVersion() {
        MemberRuleVersionDTO memberRuleVersionDTO = new MemberRuleVersionDTO();
        //会员条款
        String version1 = JedisUtil.hget("typeVersionMap", "1");
        if (StringUtils.isBlank(version1)) {
            version1 = mmpProvisionInfoMapper.queryLatestMmpProvisionVersion(1);
            JedisUtil.hset("typeVersionMap", "1",version1);
        }
        memberRuleVersionDTO.setMembershipPolicyVersion(version1);
        memberRuleVersionDTO.setMembershipPolicyAddress(csmsAddress + BussinessConstants.MEMEBER_SZ);

        //隐私政策
        String version2 = JedisUtil.hget("typeVersionMap", "2");
        if (StringUtils.isBlank(version2)) {
            version2 = mmpProvisionInfoMapper.queryLatestMmpProvisionVersion(2);
            JedisUtil.hset("typeVersionMap", "2",version2);
        }
        memberRuleVersionDTO.setPrivacyPolicyVersion(version2);
        memberRuleVersionDTO.setPrivacyPolicyVersionAddress(csmsAddress + BussinessConstants.MEMEBER_YS);
        return memberRuleVersionDTO;
    }

    @Override
    public void uploadServiceVer(UploadServiceVerInput uploadServiceVerInput) {
        if (StringUtils.isBlank(uploadServiceVerInput.getAuthId())
                || uploadServiceVerInput.getMemberType() == null) {
            throw new MemberException(-1, "检查参数正确及完整性");
        }
        MemberRuleVersionDTO memberRuleVersionDTO = this.queryMemberRuleVersion();
        //会员条款最新版本号
        String typeOneVersion = uploadServiceVerInput.getMembershipPolicyVersion();
        if(StringUtils.isBlank(typeOneVersion)){
            typeOneVersion = memberRuleVersionDTO.getMembershipPolicyVersion();
        }
        if (StringUtils.isBlank(typeOneVersion)) {
            throw new MemberException(1001, "版本号不存在");
        }
        //隐私政策最新版本号
        String typeTwoVersion = uploadServiceVerInput.getPrivacyPolicyVersion();
        if(StringUtils.isBlank(typeTwoVersion)){
            typeTwoVersion = memberRuleVersionDTO.getPrivacyPolicyVersion();
        }
        if (StringUtils.isBlank(typeTwoVersion)) {
            throw new MemberException(1001, "版本号不存在");
        }
        //查询会员条款版本号
        MembershipBaseInfo serverVer = membershipInfoMapper.selectBaseByAuthId(uploadServiceVerInput.getAuthId(), uploadServiceVerInput.getMemberType());
        if (serverVer == null) {
            throw new MemberException(1000, "会员不存在");
        }
        //版本号不同
        if (!(typeOneVersion + "," + typeTwoVersion).equals(serverVer)) {
            String userTypeOneVersion = StringUtils.EMPTY;
            String userTypeTwoVersion = StringUtils.EMPTY;
            if (StringUtils.isNotEmpty(serverVer.getServerVer()) && serverVer.getServerVer().contains(",")) {
                //逗号分隔的版本号
                userTypeOneVersion = serverVer.getServerVer().split(",")[0];
                userTypeTwoVersion = serverVer.getServerVer().split(",")[1];
            }
            //插入更新日志
            if (StringUtils.isEmpty(userTypeOneVersion) || !userTypeOneVersion.equals(typeOneVersion)) {
                int flag = insertMemberClaseLog(uploadServiceVerInput.getAuthId(), uploadServiceVerInput.getUpdateUser(), "1_" + typeOneVersion);
                if (flag < 1) {
                    logger.error(uploadServiceVerInput.getAuthId() + "会员条款记录插入失败");
                    throw new MemberException(-1, "系统忙,请稍后再试");
                }
            }
            if (StringUtils.isEmpty(userTypeTwoVersion) || !userTypeTwoVersion.equals(typeTwoVersion)) {
                int flag = insertMemberClaseLog(uploadServiceVerInput.getAuthId(), uploadServiceVerInput.getUpdateUser(), "2_" + typeTwoVersion);
                if (flag < 1) {
                    logger.error(uploadServiceVerInput.getAuthId() + "隐私政策记录插入失败");
                    throw new MemberException(-1, "系统忙,请稍后再试");
                }
            }
            //更新会员记录中的版本号
            UpdateServiceVerDto updateServiceVerDto = new UpdateServiceVerDto();

            updateServiceVerDto.setUpdateUser(uploadServiceVerInput.getUpdateUser());
            updateServiceVerDto.setServiceVer(typeOneVersion + "," + typeTwoVersion);
            updateServiceVerDto.setUpdateTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
            updateServiceVerDto.setAuthId(uploadServiceVerInput.getAuthId());
            updateServiceVerDto.setMemberType(uploadServiceVerInput.getMemberType());
            int updateFlag = membershipInfoMapper.updateServiceVer(updateServiceVerDto);
            if (updateFlag < 1) {
                logger.error(uploadServiceVerInput.getAuthId() + "会员表条款版本更新失败");
                throw new MemberException(-1, "系统忙,请稍后再试");
            }
//            JedisUtil.hset(uploadServiceVerInput.getToken(), "serviceVersion", typeOneVersion + "," + typeTwoVersion);
        }
    }

    @Override
    public BaseBean getSMSVerifyCode(SMSVerifyBeanInput smsVerifyBeanInput) throws UserMessageException {
        try {
            return this.getSMSVerifyCodeV2(smsVerifyBeanInput);
        }catch (VerifyCodeException e){
            List<Integer> list = Arrays.asList(-208203, -208204, -208207,30024,30025);
            int code = e.getCode();
            if(code == 30026){
                code = 2;
            }else if(5 == smsVerifyBeanInput.getType() && list.contains(code)){
                //针对登录时的短信，特殊处理 处理频繁发送。
                code = 5;
            }else{
                code = -1;
            }
            throw new UserMessageException(code,e.getMessage());
        }
    }

    @Override
    public BaseBean getSMSVerifyCodeV2(SMSVerifyBeanInput smsVerifyBeanInput) throws VerifyCodeException { logger.info("获取验证码 input={}, host={}", JSON.toJSONString(smsVerifyBeanInput),
                RpcContext.getContext().getRemoteHost());
        if (StringUtils.isBlank(smsVerifyBeanInput.getMobilePhone()) || smsVerifyBeanInput.getType() < 0) {
            throw new VerifyCodeException(StatusCode.PARAM_EMPTY);
        }
        if (JedisUtil.exists(BussinessConstants.SMS_EXIT + smsVerifyBeanInput.getMobilePhone())) {
            throw new VerifyCodeException(StatusCode.ONE_MIN_CODE_MAX);
        }
        /**
         * 1.发送短信数量
         */
        String sendCount = JedisUtil.get(BussinessConstants.SEND_SMS_NUM + smsVerifyBeanInput.getMobilePhone());
        //String lrSendCount = JedisUtil.get(BussinessConstants.SEND_LRSMS_NUM + smsVerifyBeanInput.getMobilePhone());
        if (StringUtils.isNotBlank(sendCount)) {
            if (Integer.parseInt(sendCount) >= 20) {
                throw new VerifyCodeException(StatusCode.VERIFY_CODE_MAX);
            }
        }
        Pattern pattern = Pattern.compile("([0-9]){11}");
        boolean checkResult = pattern.matcher(smsVerifyBeanInput.getMobilePhone().trim()).matches();
        String mobilePhone = smsVerifyBeanInput.getMobilePhone().trim();
        /**
         * 2.判断手机号是否正确 账号是否被注销
         */
        if (!checkResult) {
            throw new VerifyCodeException(StatusCode.MOBILE_FORMATE_ERROR);
        }

        if (Arrays.asList(1,2,4,5).contains(smsVerifyBeanInput.getType()) && saicMemberService.beginSaic()) {
            try {
                saicMemberService.sendAuthcode(smsVerifyBeanInput.getMobilePhone(), smsVerifyBeanInput.getType(), smsVerifyBeanInput.getVerifyCodeType(), smsVerifyBeanInput.getImei());
            } catch (BusinessException e) {
                logger.error("调用服务发送短信验证码失败 saic：" + JSON.toJSONString(smsVerifyBeanInput) + " | " + e.getMessage(), e);

                //针对登录时的短信，特殊处理。
                List<Integer> list = Arrays.asList(-208203, -208204, -208207);
                int code = e.getCode();
                if(5 == smsVerifyBeanInput.getType() && list.contains(code)){
                    throw new VerifyCodeException(code, e.getMessage());
                }

                throw new VerifyCodeException(-1, e.getMessage());
            } catch (Exception e) {
                logger.error("调用服务发送短信验证码失败 saic：" + JSON.toJSONString(smsVerifyBeanInput) + " | " + e.getMessage(), e);
                throw new VerifyCodeException(StatusCode.UPDATE_FAILED);
            }
        } else {
            int member = membershipInfoMapper.countByMobilePhone(smsVerifyBeanInput.getMobilePhone(), smsVerifyBeanInput.getOrgId());
            AccountStatusDto accountStatusDto = this.getAccountStatusByMobileV2(smsVerifyBeanInput.getMobilePhone(), 0, smsVerifyBeanInput.getOrgId());
            logger.debug("根据手机号查询会员注销信息:" + JSON.toJSONString(accountStatusDto));
            if (member < 1) {
                if (smsVerifyBeanInput.getType() == 1) {
                    throw new VerifyCodeException(StatusCode.USER_NOT_EXITED);
                }
                if ((smsVerifyBeanInput.getType() == 5 || smsVerifyBeanInput.getType() == 6) && smsVerifyBeanInput.getUserExist()) {
                    throw new VerifyCodeException(StatusCode.USER_NOT_EXITED);
                }
            } else {
                if (accountStatusDto.getAccountStatus() != 1 && accountStatusDto.getAccountStatus() != 2) {
                    if (/*smsVerifyBeanInput.getType() == 2 || */smsVerifyBeanInput.getType() == 4) {
                        throw new VerifyCodeException(StatusCode.MOBILE_USED);
                    }
                }
            }
            /**
             * 3.发送验证码
             */
            String verifyCode = smsVerifyBeanInput.getVerifyCode();
            if (StringUtils.isBlank(verifyCode)) {
                verifyCode = ComUtil.getVerifyCode(true, 6);
                if ("dev".equals(CommConfigUtil.getENV()) || "test".equals(CommConfigUtil.getENV())) {
                    verifyCode = "123456";
                }
            }
            JedisUtil.set(smsVerifyBeanInput.getMobilePhone().trim(), verifyCode, 300);
            int templateId;
            //3.1根据type对应不同的短信模板
            if (smsVerifyBeanInput.getType() == 0) {
                // 通用类型
                templateId = 20;
            } else if (smsVerifyBeanInput.getType() == 1) {
                // 找回密码
                templateId = 19;
            } else if (smsVerifyBeanInput.getType() == 2) {
                // 注册
                templateId = 23;
            } else if (smsVerifyBeanInput.getType() == 3) {
                //绑定设备
                templateId = 24;
            } else if (smsVerifyBeanInput.getType() == 4) {
                //修改手机号
                templateId = 21;
            } else if (smsVerifyBeanInput.getType() == 5) {
                //验证码登录
                templateId = 22;
            } else if (smsVerifyBeanInput.getType() == 6) {
                //退款校验
                templateId = 51;
            } else if (smsVerifyBeanInput.getType() == 7) {
                //注销账户
                //templateId = 62;
                templateId = CANCEL_SMS_TEMPLATEID;
            } else if (smsVerifyBeanInput.getType() == 9) {
                //预付款退款
                templateId = 72;
            } else {
                templateId = 20;
            }
            //3.2设置一分钟销毁
            JedisUtil.set("sms_" + smsVerifyBeanInput.getMobilePhone(), "" + System.currentTimeMillis(), 60);
            JedisUtil.hset(BussinessConstants.VERIFYCODE_NUM, smsVerifyBeanInput.getMobilePhone(), "0");
            if (smsVerifyBeanInput.getVerifyCodeType() == 1) {
                messagepush.syncSendvoiceVerify(mobilePhone.trim(), verifyCode, smsVerifyBeanInput.getMobilePhone());
            } else {
                //短信验证码
                Map<String, String> param = new HashMap<>(1);
                param.put("verifyCode", verifyCode);
                String appKey = "mem-rpc";
                if (StringUtils.isNotBlank(smsVerifyBeanInput.getAppKey()) || StringUtils.isNotBlank(RpcContext.getContext().getAttachment(HidLog.CHANNEL))) {
                    appKey = smsVerifyBeanInput.getAppKey();
                    if (StringUtils.isBlank(appKey)) {
                        appKey = RpcContext.getContext().getAttachment(HidLog.CHANNEL);
                    }
                }
                String optUser = smsVerifyBeanInput.getOptUser();
                if (StringUtils.isBlank(optUser)) {
                    optUser = smsVerifyBeanInput.getMobilePhone();
                }
                messagepush.syncSendSMSTemplate(appKey, mobilePhone.trim(), templateId, param, optUser);
            }
        }
        /**
         * 发送验证码次数加一
         */
        if (StringUtils.isNotBlank(sendCount)) {
            JedisUtil.incr(BussinessConstants.SEND_SMS_NUM + smsVerifyBeanInput.getMobilePhone());
        } else {
            JedisUtil.setCover(BussinessConstants.SEND_SMS_NUM + smsVerifyBeanInput.getMobilePhone(), "1", 60 * 60 * 24);
        }
        /**
         * 4.注册短信埋点
         */
        try {
            if (smsVerifyBeanInput.getType() == 2) {
                Map<String, Object> properties = new HashMap<>();
                properties.put("result", true);
                String distinctId = smsVerifyBeanInput.getMobilePhone();
                //sensorsdataService.track(distinctId, false, "GetVerificationCode", properties);
            }
        } catch (Exception e) {
            logger.error("神策数据埋点-记录事件时捕获到异常", e);
        }
        BaseBean baseBean = new BaseBean();
        baseBean.setStatus(0);
        baseBean.setMessage("验证码已发送");
        return baseBean;
    }

    @Override
	public BaseBean getSMSVerifyCodeV3(SMSVerifyBeanInput smsVerifyBeanInput, AliAfs aliAfs) throws VerifyCodeException {
        //调用aliAfs进行人机验证，验证通过后调用发送验证码.
        getSMSAliAFS(aliAfs);
		return this.getSMSVerifyCodeV2(smsVerifyBeanInput);
	}

    @Override
    public void getSMSAliAFS(AliAfs aliAfs) throws VerifyCodeException{
        if("0".equals(aliAfsEnable)) {
            return;
        }
        //调用aliAfs进行人机验证，验证通过后调用发送验证码.
        AuthenticateSigRequest authenticateSigRequest = new AuthenticateSigRequest();
        authenticateSigRequest.setSessionId(aliAfs.getSessionId());
        authenticateSigRequest.setSig(aliAfs.getSig());
        authenticateSigRequest.setToken(aliAfs.getToken());
        authenticateSigRequest.setScene(aliAfs.getScene());
        authenticateSigRequest.setAppKey(aliAfs.getAppKey());
        authenticateSigRequest.setRemoteIp(aliAfs.getRemoteIp());
        try {
            AuthenticateSigResponse response = acsClient.getAcsResponse(authenticateSigRequest);
            logger.debug("response-------->" + JSON.toJSONString(response));
            if (response.getCode() != 100) {
                throw new VerifyCodeException(-1, "人机验证失败");
            }
        } catch (Exception e) {
            logger.error("获取验证码时人机验证异常：", e);
            throw new VerifyCodeException(-1, "人机验证失败");
        }

    }

    @Deprecated
    private void setUserInfoDto(MembershipInfoWithBLOBs membershipInfo, UserInfoDto userInfoDto, String appKey) {
        BeanUtils.copyProperties(membershipInfo, userInfoDto);
        userInfoDto.setUserName(membershipInfo.getName());
        userInfoDto.setEmail(membershipInfo.getMail());
        //e币
        BigDecimal rentMins = membershipInfo.getRentMins();
        userInfoDto.setRentMins(rentMins == null ? 0 : rentMins.floatValue());
        //储备金
        BigDecimal reserveAmount = membershipInfo.getReserveAmount();
        userInfoDto.setReserveAmount(reserveAmount == null ? "0" : reserveAmount.toString());
        //初次领证时间
        userInfoDto.setFirstGetLicenseDate(membershipInfo.getObtainDriverTimer());
        //到期时间
        userInfoDto.setExpirationDate(membershipInfo.getLicenseExpirationTime());
        //审核结果
        userInfoDto.setReviewResult(membershipInfo.getReviewItems());
        //用户头像
        if (StringUtils.isNotEmpty(membershipInfo.getUserImgUrl())) {
            userInfoDto.setImageUrl(ComUtil.subString(membershipInfo.getUserImgUrl()));
        }
        //驾照图片
        if (StringUtils.isNotEmpty(membershipInfo.getDrivingLicenseImgUrl())) {
            userInfoDto.setDrivingLicenseImgUrl(ComUtil.subString(membershipInfo.getDrivingLicenseImgUrl()));
        }
        //人脸识别照片
        if (StringUtils.isNotEmpty(membershipInfo.getFaceRecognitionImgUrl())) {
            userInfoDto.setFaceRecognitionImgUrl(ComUtil.subString(membershipInfo.getFaceRecognitionImgUrl()));
        }
        //手持身份证照片
        if (StringUtils.isNotEmpty(membershipInfo.getHoldIdcardPicUrl())) {
            userInfoDto.setHoldIdcardPicUrl(ComUtil.subString(membershipInfo.getHoldIdcardPicUrl()));
        }
        //身份证照片
        if (StringUtils.isNotEmpty(membershipInfo.getIdcardPicUrl())) {
            userInfoDto.setIdcardPicUrl(ComUtil.subString(membershipInfo.getIdcardPicUrl()));
        }
        //驾照附页
        if (StringUtils.isNotBlank(membershipInfo.getFileNoImgUrl())){
            userInfoDto.setFileNoImgUrl(ComUtil.subString(membershipInfo.getFileNoImgUrl()));
        }
        //外籍判断
        String national = membershipInfo.getNational();
        if (StringUtils.isEmpty(national) || national.contains(BussinessConstants.CHINA_NATIONAL)) {
            userInfoDto.setIsForeign(0);
        } else {
            userInfoDto.setIsForeign(1);
        }
        //驾照是否过期
        int memberReviewStatus = membershipInfo.getReviewStatus().intValue();
        LocalDate now = LocalDate.now();
        DateTimeFormatter ymd = DateTimeFormatter.ofPattern(ComUtil.DATE_TYPE5);
        //无论是否认证成功都返回驾照状态
        if (StringUtils.isNotBlank(membershipInfo.getLicenseExpirationTime())){
            LocalDate expirationDate = LocalDate.parse(membershipInfo.getLicenseExpirationTime(), ymd);
            if (now.isAfter(expirationDate)){
                userInfoDto.setIsExpire(1);
            }
        }
        if (memberReviewStatus == 1 && StringUtils.isNotBlank(membershipInfo.getLicenseExpirationTime()) && StringUtils.isBlank(appKey)) {
            LocalDate expirationDate = LocalDate.parse(membershipInfo.getLicenseExpirationTime(), ymd);
            if (now.isAfter(expirationDate)) {
                userInfoDto.setIsExpire(1);
                //审核状态更新为审核不通过,认证状态更新为未认证
                MembershipInfoWithBLOBs entity = new MembershipInfoWithBLOBs();
//                entity.setAuthenticationStatus(0);
                //entity.setReviewStatus((short) 2);
                entity.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
                entity.setUpdatedUser(membershipInfo.getAuthId());
                entity.setPkId(membershipInfo.getPkId());
                //驾照过期时，修改驾照认证状态为不通过
                entity.setReviewItems("112112111");
                entity.setLicenseAuthStatus(2);
                entity.setLicenseStatusMsg("驾照已过期");
                int updateStatus = membershipInfoMapper.updateByPrimaryKeySelective(entity);
                if (updateStatus > 0) {
                    //添加会员 审核认证状态修改日志
                    String content = "驾照过期，修改会员状态为审核不通过未认证";
                    ComUtil.insertOperatorLog(content, membershipInfo.getAuthId(),
                            membershipInfo.getMobilePhone(), membershipInfo.getMobilePhone(), userOperatorLogMapper);
                    UserOperationLogInput userOptRecord = new UserOperationLogInput();
                    userOptRecord.setUserId(membershipInfo.getPkId());
                    userOptRecord.setRefKey1(membershipInfo.getMobilePhone());
                    userOptRecord.setOperationType(MemOperateTypeEnum.UPDATE_MEM_STATUS.getCode());
                    userOptRecord.setOperationContent(content);
                    userOptRecord.setOperatorId(-1L);
                    userOptRecord.setOperator(membershipInfo.getMobilePhone());
                    userOptRecord.setMiscDesc("驾照已过期");
                    saveUserOperationLog(userOptRecord);
                    logger.warn(content + "，authId=" + membershipInfo.getAuthId());
                }else{
                    logger.warn("会员authId=" + membershipInfo.getAuthId() + "驾照过期，更新审核失败");
                }
            } else {
                LocalDate nowAfter = now.plusDays(90);
                if (nowAfter.isAfter(expirationDate)) {
                    userInfoDto.setIsExpire(2);
                    Integer reviewStatus = membershipAdditionalInfoMapper.selectReviewStatus(membershipInfo.getAuthId(), 0);
                    if (reviewStatus != null) {
                        if (reviewStatus == 0) {
                            userInfoDto.setIsExpire(3);
                        }
                        if (reviewStatus == 2) {
                            userInfoDto.setIsExpire(4);
                        }
                    }
                }
            }
        }
    }


    /**
     * 获取审核状态话术
     *
     * @param reviewStatus
     * @param authenticationStatus
     * @param isForeign
     * @return
     */
    @Deprecated
    private static String getReviewDescByStatus(int reviewStatus, int authenticationStatus, int isForeign, int oldUser, int isExpire) {
        String desc = StringUtils.EMPTY;
        if (reviewStatus == -1) {
            //未做任何认证
            if (authenticationStatus == 1) {
                desc = (isForeign == 0 ? ReviewStatusEnum.THREE.getErrMsg() : ReviewStatusEnum.TWO.getErrMsg());
            } else {
                //未认证
                desc = ReviewStatusEnum.ONE.getErrMsg();
            }
        } else if (reviewStatus == 0) {
            //审核中
            desc = ReviewStatusEnum.FIVE.getErrMsg();
        } else if (reviewStatus == 1) {
            //审核通过
            if (authenticationStatus == 0) {
                //未认证
                desc = (oldUser == 1 ? ReviewStatusEnum.FOUR.getErrMsg() : ReviewStatusEnum.ONE.getErrMsg());
            } else if (authenticationStatus == 1) {
                desc = (isForeign == 0 ? ReviewStatusEnum.THREE.getErrMsg() : ReviewStatusEnum.TWO.getErrMsg());
            } else {
                //已认证
                desc = ReviewStatusEnum.SEVEN.getErrMsg();
            }
        } else if (reviewStatus == 2) {
            //审核不通过
            if (oldUser == 1) {
                desc = ReviewStatusEnum.ONE.getErrMsg();
            } else {
                desc = ReviewStatusEnum.SIX.getErrMsg();
            }
        }
        //驾照有效期判断
        if (isExpire == 1) {
            desc = ReviewStatusEnum.EIGHT.getErrMsg();
        } else if (isExpire == 2 && reviewStatus == 1) {
            desc = ReviewStatusEnum.TEN.getErrMsg();
        } else if (isExpire == 3) {
            desc = ReviewStatusEnum.ELEVEN.getErrMsg();
        } else if (isExpire == 4) {
            desc = ReviewStatusEnum.TWELVE.getErrMsg();
        }
        return desc;
    }

    @Override
    public MembershipBasicInfo getUserBasicInfo(String mid) {
        MembershipBasicInfo base = membershipInfoMapper.getUserBasicInfoByMid(mid);
        MemberIdentityDocument memberIdentityDocument = memberIdentityDocumentMapper.selectOneByMid(mid);
        if (memberIdentityDocument != null) {
            base.setAuthenticationStatusNew(memberIdentityDocument.getAuthenticationStatus());
            base.setExpireType(memberIdentityDocument.getExpireType());
            base.setExpirationDate(memberIdentityDocument.getExpirationDate());

            try {
                String identityNo = memberIdentityDocument.getIdentityNo();
                if (StringUtils.isNotBlank(identityNo)) {
                    base.setPassportNo(identityNo);
                }

                Integer identityType = memberIdentityDocument.getIdentityType();
                if (identityType != null && identityType != 0) {
                    base.setIdType(identityType);
                }
            } catch (Exception e) {
                logger.error("获取身份证信息异常，mid={}",mid,e);
            }
        }
        return getUserBasicInfoWrap(base, true);
    }

    @Override
    public MembershipBasicInfo getUserBasicInfo(String authId, Short membershipType) {
        MembershipBasicInfo base = membershipInfoMapper.getUserBasicInfo(authId, membershipType);
        if(base == null) {
            if (membershipType == 0) {
                logger.info("getUserBasicInfo兼容擎路用户,authId={}",authId);
                return getUserBasicInfo(authId,(short)2);
            }
            logger.warn("用户查询： getUserBasicInfo，用户不存在，authId={}", authId);
            return null;
        }
        boolean alreadyFaceAuthed = base.getAuthenticationStatus().equals(2);
        MemberIdentityDocument memberIdentityDocument = memberIdentityDocumentMapper.selectOneByMid(base.getMid());
        if (memberIdentityDocument != null) {
            base.setAuthenticationStatusNew(memberIdentityDocument.getAuthenticationStatus());
            base.setExpireType(memberIdentityDocument.getExpireType());
            base.setExpirationDate(memberIdentityDocument.getExpirationDate());
            alreadyFaceAuthed = !IdentityAuthStatusEnum.unFaceAuthed(memberIdentityDocument.getAuthenticationStatus());
        }
        base.setAlreadyFaceAuthed(alreadyFaceAuthed);

        return getUserBasicInfoWrap(base, false);
    }

    @Value("${oss.web_url}")
    public String faceUrl;
    public MembershipBasicInfo getUserBasicInfoWrap(MembershipBasicInfo base, boolean withLicenseFullPath) {
    	if(base != null){
    	    if(StringUtils.isNotBlank(base.getFaceRecognitionImgUrl())){
                base.setFaceRecognitionImgUrl(ComUtil.subString(base.getFaceRecognitionImgUrl()));
            }
            if(withLicenseFullPath && StringUtils.isNotBlank(base.getDrivingLicenseImgUrl())) {
                base.setDrivingLicenseImgUrl(ComUtil.subString(base.getDrivingLicenseImgUrl()));
            }
    	    if (StringUtils.isNotBlank(base.getNational())){
    	        if (base.getNational().contains("中国")){
    	            base.setForeignNationality(1);
                }else {
    	            base.setForeignNationality(0);
                }
            }
            if(StringUtils.isNotBlank(base.getHoldIdcardPicUrl())){
                base.setHoldIdcardPicUrl(ComUtil.subString(base.getHoldIdcardPicUrl()));
            }
            AgencyInfoDto agencyInfoDto = baseInfoService.queryAgencyInfoByAgencyId(base.getAgencyId());
            if(agencyInfoDto != null){
                base.setAgencyName(agencyInfoDto.getAgencyName());
            }
            if(StringUtils.isNotBlank(base.getCityOfOrigin())){
                String orgName = cityMapper.queryOrgNameByCity(base.getCityOfOrigin().trim());
                base.setOrgName(orgName);
            }
            AppKeyManager appKeyInfo = appKeyManagerMapper.selectByPrimaryKey(base.getAppKey());
            if(appKeyInfo != null){
                base.setAppKeyOrigin(appKeyInfo.getPlatName());
            }
            base.setReviewStatusDesc(this.hanleReviewStatus(base.getReviewStatus(),base.getAuthenticationStatus()));
        }
        return base;
    }

    @Override
    public MembershipBasicInfo getUserBasicInfoByPkId(Long pkId) {
        MembershipBasicInfo base = membershipInfoMapper.getUserBasicInfoByPkId(pkId);
        try {
            MemberIdentityDocument memberIdentityDocument = memberIdentityDocumentMapper.selectOneByMid(base.getMid());
            if (memberIdentityDocument != null) {
                base.setAuthenticationStatusNew(memberIdentityDocument.getAuthenticationStatus());
                base.setExpireType(memberIdentityDocument.getExpireType());
                base.setExpirationDate(memberIdentityDocument.getExpirationDate());
            }
        } catch (Exception e) {

        }
        return base;
    }

    /**
     * 处理审核状态
     * @param reviewStatus -1 资料不全 0:待审核 1：审核通过 2: 审核不通过 3：用户无效 4:重新审核
     * @param authenticationStatus 0 未认证 1 未刷脸/未上传 2 已认证
     * @return
     */
    private String hanleReviewStatus(int reviewStatus,int authenticationStatus){
        String reviewStatusDesc = StringUtils.EMPTY;
        if(reviewStatus == -1){
            reviewStatusDesc = "请去APP提交驾照信息";
        }else if(reviewStatus == 0){
            reviewStatusDesc = "请耐心等待工作人员审核";
        } else if(reviewStatus == 1){
            if(authenticationStatus == 0){
                reviewStatusDesc = "请去APP提交驾照信息";
            } else if(authenticationStatus == 1){
                reviewStatusDesc = "请去APP做人脸认证";
            } else{
                reviewStatusDesc = "已认证通过";
            }
        } else if(reviewStatus == 2){
            reviewStatusDesc = "审核不通过,请去APP重新提交驾照信息";
        } else if(reviewStatus == 4){
            reviewStatusDesc = "重新审核状态,请联系客服";
        }
        return reviewStatusDesc;
    }

    @Override
    public MembershipBasicInfo getMembershipByPhone(String phone, int membershipType) {
    	MembershipBasicInfo base = membershipInfoMapper.getMembershipByPhone(phone, membershipType);
    	if(base != null){
    	    if(StringUtils.isNotBlank(base.getFaceRecognitionImgUrl())){
                base.setFaceRecognitionImgUrl(ComUtil.subString(base.getFaceRecognitionImgUrl()));
            }
            if(StringUtils.isNotBlank(base.getHoldIdcardPicUrl())){
                base.setHoldIdcardPicUrl(ComUtil.subString(base.getHoldIdcardPicUrl()));
            }
            AgencyInfoDto agencyInfoDto = baseInfoService.queryAgencyInfoByAgencyId(base.getAgencyId());
            if(agencyInfoDto != null){
                base.setAgencyName(agencyInfoDto.getAgencyName());
            }
            if(StringUtils.isNotBlank(base.getCityOfOrigin())){
                String orgName = cityMapper.queryOrgNameByCity(base.getCityOfOrigin().trim());
                base.setOrgName(orgName);
            }
            AppKeyManager appKeyInfo = appKeyManagerMapper.selectByPrimaryKey(base.getAppKey());
            if(appKeyInfo != null){
                base.setAppKeyOrigin(appKeyInfo.getPlatName());
            }
            base.setReviewStatusDesc(this.hanleReviewStatus(base.getReviewStatus(),base.getAuthenticationStatus()));
        }else{
            //TODO 虚拟会员
        }

        try {
            MemberIdentityDocument memberIdentityDocument = memberIdentityDocumentMapper.selectOneByMid(base.getMid());
            if (memberIdentityDocument != null) {
                base.setAuthenticationStatusNew(memberIdentityDocument.getAuthenticationStatus());
                base.setExpireType(memberIdentityDocument.getExpireType());
                base.setExpirationDate(memberIdentityDocument.getExpirationDate());
            }
        } catch (Exception e) {

        }
        return base;
    }

    @Override
    public MembershipBlobDTO getMembershipByAuthId(String authId, int membershipType) {
        MembershipInfoWithBLOBs membership = membershipInfoMapper.selectByAuthId(authId, membershipType);
        if(membership != null) {
            MembershipBlobDTO membershipBlobDTO = new MembershipBlobDTO();
            BeanCopyUtils.copyProperties(membership, membershipBlobDTO);
            membershipBlobDTO.setReviewStatus(membership.getReviewStatus().intValue());
            if(membership.getStatus() != null) {
                membershipBlobDTO.setStatus(membership.getStatus().intValue());
            }
            return membershipBlobDTO;
        }else{
            if (membershipType == 0) {
                logger.info("getMembershipByAuthId 兼容擎路用户,authId={}",authId);
                return getMembershipByAuthId(authId,2);
            }
        }
        return null;
    }


    @Override
    public MembershipBlobDTO getMemberByUid(String uid) {
        MembershipInfoWithBLOBs membership = membershipInfoMapper.selectByUid(uid);
        if(membership != null) {
            MembershipBlobDTO membershipBlobDTO = new MembershipBlobDTO();
            BeanCopyUtils.copyProperties(membership, membershipBlobDTO);
            membershipBlobDTO.setReviewStatus(membership.getReviewStatus().intValue());
            if(membership.getStatus() != null) {
                membershipBlobDTO.setStatus(membership.getStatus().intValue());
            }
            return membershipBlobDTO;
        }
        return null;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateMemberUidByPkId(Long pkId, String uid) {
        if(StringUtils.isBlank(uid)) {
            uid = null;
        }
        //TODO 确认
        MembershipInfoWithBLOBs membershipInfo = membershipInfoMapper.selectByPrimaryKey(pkId);
        if(membershipInfo == null) {
            throw new BusinessRuntimeException(StatusCode.SYSTEM_ERROR);
        }
//        if(StringUtils.isNotBlank(membershipInfo.getUid())) {
//            throw new BusinessRuntimeException(StatusCode.SYSTEM_ERROR);
//        }

        /**
         * 1、修改会员表.<br>
         */
        int result = membershipInfoMapper.updateMemberUidByPkId(pkId, uid);

        /**
         * 2、添加操作日志.<br>
         */
        //保存操作日志
        UserOperationLogInput userOperationLogInput = new UserOperationLogInput();
        userOperationLogInput.setOperationType(MemOperateTypeEnum.UPDATE_SAIC_UID.getCode());
        userOperationLogInput.setOperationContent("更新享道统一用户ID");
        userOperationLogInput.setOperationTime(new Date());
        userOperationLogInput.setOperator("membership");
        userOperationLogInput.setRefKey1(uid);
        userOperationLogInput.setUserId(pkId);
        saveUserOperationLog(userOperationLogInput);
        //HidLog.membership(LogPoint.UPDATE_DRIVER_FILE_NO, content, authId, true);
        return result;
    }

    @Override
    public EnterpriseAgencyInfoDto queryAgencyInfoByName(String name) {
        EnterpriseAgencyInfoDto dto = new EnterpriseAgencyInfoDto();
        EnterpriseAgencyInfo agencyInfoDto = agencyInfoMapper.getAgencyInfoByName(name);
        if (agencyInfoDto != null) {
            BeanUtils.copyProperties(agencyInfoDto, dto);
        }
        return dto;
    }

    @Override
    public Boolean checkCardValid(String cardNo) {
        List<CardInfo> cardInfoList = cardInfoMapper.getCardInfo(cardNo);
        if(CollectionUtils.isNotEmpty(cardInfoList)){
            if(cardInfoList.get(0).getStatus() == 1){
                return Boolean.FALSE;
            }else{
                return Boolean.TRUE;
            }
        }else{
            return Boolean.FALSE;
        }
    }
    @Override
	@Transactional(rollbackFor = Exception.class)
	public boolean changeMail(String oldMail, String newMail, String authId, UpdateUserDto updateUserDto) {
		/**
		 * 1、修改会员表.<br>
		 */
		int result = 0;
		try {
			result = membershipInfoMapper.updateMail(newMail, authId, updateUserDto.getUserName(),
					ComUtil.getFormatDate(updateUserDto.getUpdateTime(), ComUtil.DATE_TYPE3));
		} catch (Exception e) {
			logger.error("修改会员邮箱失败, authId=" + authId, e);
		}
		if (result != 1) {
			return false;
		}
		/**
		 * 2、添加操作日志.<br>
		 */
		try {
		    String content = "修改邮箱地址，旧邮箱" + oldMail + "修改为新邮箱" + newMail;
			UserOperatorLog userOperatorLog = new UserOperatorLog();
			userOperatorLog.setOperatorContent(content);
			userOperatorLog.setForeignKey(authId);
			userOperatorLog.setForeignKey2(updateUserDto.getUserId().toString());
			userOperatorLog.setCreatedUser(updateUserDto.getUserName());
			userOperatorLog.setCreatedTime(ComUtil.getFormatDate(updateUserDto.getUpdateTime(), ComUtil.DATE_TYPE3));
			userOperatorLogMapper.saveSelective(userOperatorLog);

            UserOperationLogInput userOptRecord = new UserOperationLogInput();
            userOptRecord.setRefKey1(authId);
            userOptRecord.setOperationType(MemOperateTypeEnum.UPDATE_EMAL.getCode());
            userOptRecord.setOperatorId(updateUserDto.getUserId());
            userOptRecord.setOperationContent(content);
            userOptRecord.setOperator(updateUserDto.getUserName());
            saveUserOperationLog(userOptRecord);
		} catch (Exception e) {
			logger.error("记录修改会员邮箱操作日志失败，authId=" + authId, e);
			return false;
		}
        //增加资料补全积分奖励推送
        if(StringUtils.isBlank(oldMail) && StringUtils.isNotBlank(newMail)) {
            memberInfoCompleteReward(authId, MemPointsPushEnum.COMPETE_MEMBER_EMAIL);
        }
		return true;
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean changeAddress(String authId, String address, String province, String city, String area,
			UpdateUserDto updateUserDto) {
		/**
		 * 1、修改会员表.<br>
		 */
        MembershipInfoWithBLOBs member = membershipInfoMapper.selectByAuthId(authId, 0);
        if(member == null) {
            return false;
        }
		int result = 0;
		try {
			result = membershipInfoMapper.updateAddress(authId, address, province, city, area,
					updateUserDto.getUserName(),
					ComUtil.getFormatDate(updateUserDto.getUpdateTime(), ComUtil.DATE_TYPE3));
			//20200826 修改邮寄地址不变更会员审核状态
			//membershipInfoMapper.updateReviewStatus(authId);
		} catch (Exception e) {
			logger.error("修改会员地址失败，authId=" + authId, e);
		}
		if (result != 1) {
			return false;
		}
		/**
		 * 2、添加操作日志.<br>
		 */
		try {
			UserOperatorLog userOperatorLog = new UserOperatorLog();
			userOperatorLog.setOperatorContent("修改用户地址");
			userOperatorLog.setForeignKey(authId);
			userOperatorLog.setForeignKey2(updateUserDto.getUserId().toString());
			userOperatorLog.setCreatedUser(updateUserDto.getUserName());
			userOperatorLog.setCreatedTime(ComUtil.getFormatDate(updateUserDto.getUpdateTime(), ComUtil.DATE_TYPE3));
			userOperatorLogMapper.saveSelective(userOperatorLog);

            UserOperationLogInput userOptRecord = new UserOperationLogInput();
            userOptRecord.setRefKey1(authId);
            userOptRecord.setOperationType(MemOperateTypeEnum.UPDATE_ADDR.getCode());
            userOptRecord.setOperatorId(updateUserDto.getUserId());
            userOptRecord.setOperationContent(MemOperateTypeEnum.UPDATE_ADDR.getOperate());
            userOptRecord.setOperator(updateUserDto.getUserName());
            saveUserOperationLog(userOptRecord);
		} catch (Exception e) {
			logger.error("记录会员地址后操作日志失败，authId=" + authId, e);
			return false;
		}

		//增加资料补全积分奖励推送
        if(StringUtils.isBlank(member.getAddress()) && StringUtils.isNotBlank(address)) {
            memberInfoCompleteReward(authId, MemPointsPushEnum.COMPETE_MEMBER_ADDRESS);
        }
		return true;
	}

	private void memberInfoCompleteReward(String authId, MemPointsPushEnum pushType){
        ThreadPoolUtils.EXECUTOR.execute(()-> {
            try {
                MemberPointsOfferDto offerDto = new MemberPointsOfferDto();
                offerDto.setAuthId(authId);
                offerDto.setEventType(pushType.getCode());
                offerDto.setCreateTime(new Date());
                offerDto.setDetails(null);
                logger.info("MemberPoints：推送【{}】积分奖励, authId={}", pushType.getTitle(), authId);
                HidLog.membership(LogPoint.MEMBER_POINTS_PUSH, pushType.getTitle(), authId);
                memberPointsService.asyncOfferPoints(offerDto);
            }catch (Exception ex) {
                HidLog.membership(LogPoint.MEMBER_POINTS_PUSH, pushType.getTitle(), authId, false);
                logger.error("MemberPoints：推送【" + pushType.getTitle() +"】积分奖励失败, authId=" + authId, ex);
            }
        });
    }

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean resetPassword(String authId, String password, UpdateUserDto updateUserDto) {
		/**
		 * 1、修改会员表.<br>
		 */
		int result = 0;
		try {
			result = membershipInfoMapper.updatePassword(authId, password, updateUserDto.getUserName(),
					ComUtil.getFormatDate(updateUserDto.getUpdateTime(), ComUtil.DATE_TYPE3));
		} catch (Exception e) {
			logger.error("重置密码失败", e);
		}
		if (result != 1) {
			return false;
		}
		/**
		 * 2、添加操作日志.<br>
		 */
		try {
			UserOperatorLog userOperatorLog = new UserOperatorLog();
			userOperatorLog.setOperatorContent("重置密码");
			userOperatorLog.setForeignKey(authId);
			userOperatorLog.setForeignKey2(updateUserDto.getUserId().toString());
			userOperatorLog.setCreatedUser(updateUserDto.getUserName());
			userOperatorLog.setCreatedTime(ComUtil.getFormatDate(updateUserDto.getUpdateTime(), ComUtil.DATE_TYPE3));
			userOperatorLogMapper.saveSelective(userOperatorLog);
			//记录新表操作日志
			MembershipBaseInfo membershipInfo = membershipInfoMapper.selectBaseByAuthId(authId, 0);
			if(membershipInfo != null) {
				UserOperationLogInput userOptRecord = new UserOperationLogInput();
	            userOptRecord.setUserId(membershipInfo.getPkId());
	            userOptRecord.setRefKey1(membershipInfo.getMobilePhone());
	            userOptRecord.setOperationType(MemOperateTypeEnum.RESET_PWD.getCode());
	            userOptRecord.setOperatorId(updateUserDto.getUserId());
	            userOptRecord.setOperationContent(MemOperateTypeEnum.RESET_PWD.getOperate());
	            userOptRecord.setOperator(updateUserDto.getUserName());
	            saveUserOperationLog(userOptRecord);
	            //易观埋点.
	            Map<String, Object> properties = new HashMap<String, Object>();
	            properties.put("result", true);
				//sensorsdataService.track(membershipInfo.getAuthId(), true, "ResetPassword", properties);
			}
		} catch (Exception e) {
			logger.error("重置密码后添加操作日志失败", e);
			return false;
		}
		return true;
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean cardPause(CardPauseDto cardPauseDto, UpdateUserDto updateUserDto) {
		String recoverDate = "";
		CardPauseLog cardPauseLog;
		CardInfo cardInfo;
		CardInfoHistroy cardInfoHistroy;
		try {
			cardPauseLog = new CardPauseLog();
			cardInfo = new CardInfo();
			cardInfo.setAuthId(cardPauseDto.getAuthId());
			cardInfo.setCardNo(cardPauseDto.getCardNo());
			cardInfo.setStatus(Integer.parseInt(cardPauseDto.getStatus()));
			cardInfo.setUpdatedTime(ComUtil.getFormatDate(updateUserDto.getUpdateTime(), ComUtil.DATE_TYPE3));
			cardInfo.setUpdatedUser(updateUserDto.getUserName());
			cardInfoHistroy = new CardInfoHistroy();
			cardInfoHistroy.setAuthId(cardPauseDto.getAuthId());
			cardInfoHistroy.setCardNo(cardInfoHistroy.getCardNo());
			cardInfoHistroy.setRemark(cardPauseDto.getRemark());
			cardInfoHistroy.setUpdatedTime(ComUtil.getFormatDate(updateUserDto.getUpdateTime(), ComUtil.DATE_TYPE3));
			cardInfoHistroy.setUpdatedUser(updateUserDto.getUserName());
		} catch (Exception e) {
			logger.error("参数不符合要求！", e);
			return false;
		}
		UserOperatorLog userOperatorLog = new UserOperatorLog();
		if ("1".equals(cardPauseDto.getStatus())) {
			CardPauseLog pauseLog = cardPauseLogMapper.selectPauseLogByCardNo(cardPauseDto.getCardNo());
			if (pauseLog != null && pauseLog.getPauseStatus() != 1) {
				logger.error("该会员已经被暂停!");
				return false;
			}
			cardInfoHistroy.setCardStatus(2D);
			try {
				cardPauseLog.setCardNo(cardPauseDto.getCardNo());
				cardPauseLog.setAuthId(cardPauseDto.getAuthId());
				cardPauseLog.setName(cardPauseDto.getName());
				cardPauseLog.setMobilePhone(cardPauseDto.getMobilePhone());
				cardPauseLog.setPauseReason(cardPauseDto.getRemark());
				cardPauseLog.setPauseStatus(0);
				cardPauseLog.setCreatedUser(updateUserDto.getUserName());
				cardPauseLog.setCreatedTime(ComUtil.getFormatDate(updateUserDto.getUpdateTime(), ComUtil.DATE_TYPE3));
			} catch (Exception e) {
				logger.error("参数不符合要求！", e);
				return false;
			}
			if (StringUtils.isNotEmpty(cardPauseDto.getPauseTimeType())) {
				// 获取当前时间并设置为0点
				Calendar calendar = Calendar.getInstance();
				if ("0".equals((cardPauseDto.getPauseTimeType()))) {
					// +7天
					calendar.add(Calendar.DAY_OF_MONTH, 7);
					cardPauseLog.setRecoverTime(calendar.getTimeInMillis());
				} else if ("1".equals((cardPauseDto.getPauseTimeType()))) {
					// 加30天
					calendar.add(Calendar.DAY_OF_MONTH, 30);
					cardPauseLog.setRecoverTime(calendar.getTimeInMillis());
				} else if ("2".equals((cardPauseDto.getPauseTimeType()))) {
					// 永久暂停
					cardPauseLog.setRecoverTime(0L);
					cardPauseLog.setPauseStatus(2);
				}
				recoverDate = DateFormatUtils.ISO_DATE_FORMAT.format(calendar);
			} else {
				recoverDate = cardPauseDto.getRecoverTime();
				try {
					cardPauseLog.setRecoverTime(
							DateFormatUtils.ISO_DATE_FORMAT.parse(cardPauseDto.getRecoverTime()).getTime());
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			userOperatorLog.setOperatorContent("暂停会员至" + recoverDate + ", " + cardPauseDto.getRemark());
		} else if ("0".equals(cardPauseDto.getStatus())) {
			cardInfoHistroy.setCardStatus(1D);
			userOperatorLog.setOperatorContent("恢复会员" + ", " + cardPauseDto.getRemark());
		}
		boolean result = false;
		try {
			if(cardInfoMapper.updateCardStatusByCardNo(cardInfo) > 0){
				result = true;
			}
			if(cardInfoHistroyMapper.updateCardInfoHistroy(cardInfoHistroy) > 0){
				result = true;
			}
		} catch (Exception e) {
			logger.error("暂停会员卡失败!", e);
			return false;
		}
		if (result) {
			// 新增/修改一条暂停记录
			try {
				if ("1".equals(cardPauseDto.getStatus())) {
					cardPauseLogMapper.insertSelective(cardPauseLog);
				} else if ("0".equals(cardPauseDto.getStatus())) {
					CardPauseLog record = new CardPauseLog();
					record.setCardNo(cardPauseDto.getCardNo());
					record.setUpdatedTime(ComUtil.getFormatDate(updateUserDto.getUpdateTime(), ComUtil.DATE_TYPE3));
					record.setUpdatedUser(updateUserDto.getUserName());
					cardPauseLogMapper.updatePauseStatusByCardNo(cardPauseLog);
				}
			} catch (Exception e) {
				logger.error("新增/修改暂停会员卡记录失败！", e);
				return false;
			}
			// 新增一条操作日志
			try {
				userOperatorLog.setForeignKey(cardPauseDto.getAuthId());
				userOperatorLog.setForeignKey2(updateUserDto.getUserId().toString());
				userOperatorLog.setCreatedUser(updateUserDto.getUserName());
				userOperatorLog.setCreatedTime(ComUtil.getFormatDate(updateUserDto.getUpdateTime(), ComUtil.DATE_TYPE3));
				userOperatorLogMapper.saveSelective(userOperatorLog);
			} catch (Exception e) {
				logger.error("暂停会员卡后添加操作日志失败！", e);
				return false;
			}
		}
		return true;
	}

    @Override
    public BaseResponse checkUserCard(String authId,Integer membershipType) {
        BaseResponse baseResponse = new BaseResponse();
        if (StringUtils.isBlank(authId) || null== membershipType){
            baseResponse.setCode(-1);
            baseResponse.setMessage("参数错误");
            return baseResponse;
        }
        MembershipBasicInfo membershipBasicInfo = this.getUserBasicInfo(authId,membershipType.shortValue());
        if (membershipBasicInfo == null){
            if (membershipType == 0) {
                logger.info("checkUserCard 兼容擎路用户,authId={}",authId);
                return checkUserCard(authId,2);
            }
            baseResponse.setCode(-1);
            baseResponse.setMessage("用户不存在");
            return baseResponse;
        }
        if (StringUtils.isBlank(membershipBasicInfo.getCardNo())){
            baseResponse.setCode(5);
            baseResponse.setMessage("您的会员卡正在制作中，请耐心等待。");
            return baseResponse;
        }
        if (membershipBasicInfo.getStatus().equals(1)){
            baseResponse.setCode(3);
            baseResponse.setMessage("用户已经被暂停使用");
            return baseResponse;
        }
        if (!membershipBasicInfo.getReviewStatus().equals(1)){
            baseResponse.setCode(1);
            baseResponse.setMessage("用户资料需要等待管理员审核");
            return baseResponse;
        }
        //增加会员卡id状态
        List<CardInfo> cardInfos = cardInfoMapper.getCardInfo(membershipBasicInfo.getCardNo());
        if (CollectionUtils.isNotEmpty(cardInfos)) {
            CardInfo cardInfo = cardInfos.get(0);
            if (cardInfo.getStatus() != 0){
                baseResponse.setCode(4);
                baseResponse.setMessage("用户卡已失效");
                return baseResponse;
            }
            if (cardInfo.getActivateStatus()!= 1){
                baseResponse.setCode(2);
                baseResponse.setMessage("用户卡未激活");
                return baseResponse;
            }
        }else{
            baseResponse.setCode(5);
            baseResponse.setMessage("您的会员卡正在制作中，请耐心等待。");
            return baseResponse;
        }
        baseResponse.setCode(0);
        baseResponse.setMessage("用户正常");
        return baseResponse;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unregister(UnregisterDto unregisterDto) throws UnregisterException {

        /**
         * 目前仅外部会员允许注销账号
         * 账号注销冻结期180天，期间账号不允许登录，手机号、驾照号不允许重新注册。
         * 超过冻结期，账号失效，可重新注册完全视为新用户。
         */
        logger.info("注销服务接口|入参={}", JSON.toJSONString(unregisterDto));
        // 参数检查
        if (StringUtils.isBlank(unregisterDto.getAuthId())
                || null == unregisterDto.getMembershipType()) {
            throw new MemberException(-1, "请检查参数完整性");
        }
        if(unregisterDto.getConfirmFlag() == null) {
            unregisterDto.setConfirmFlag(true);
        }

        Jedis jedis = null;
        JedisLock jedisLock = null;
        try {
            //防止账号并发注销，多次注销记录(注销条件检查-维修任务查询慢sql)
            jedis = JedisUtil.getJedis();
            jedisLock = new JedisLock("unregister_" + unregisterDto.getAuthId());
            if (!jedisLock.acquire(jedis)) {
                throw new RegisterException(StatusCode.SYSTEM_ERROR);
            }
            //1. 注销条件检查
            // 1.1 查询会员信息
            MembershipAccountInfo accountInfo = membershipInfoMapper.getMemberAccountInfo(
                    unregisterDto.getAuthId(), unregisterDto.getMembershipType().intValue());
            // 会员不存在
            if(null == accountInfo || StringUtils.isBlank(accountInfo.getMobilePhone())) {
                logger.error("账号注销失败 原因：会员mobile={}不存在", accountInfo.getMobilePhone());
                throw UnregisterException.MEMBER_NOT_EXIST;
            }

            String authId = unregisterDto.getAuthId();
            int membershipType = unregisterDto.getMembershipType().intValue();
            // 1.2 会员已注销，不能再次注销
            if(StringUtils.isNotBlank(accountInfo.getUnregisterTime())) {
                logUnregisterFailed(unregisterDto, accountInfo, UnregisterException.MEMBER_UNREGISTERED, null);
                throw UnregisterException.MEMBER_UNREGISTERED;
            }

            // 1.3 会员注销条件检查，不符合条件抛出异常
            String checkMsg = checkUnregister(unregisterDto, accountInfo);

            /**
             * 2. 注销操作
             */
            String operator = StringUtils.isNotBlank(unregisterDto.getOperateUserName()) ?
                    unregisterDto.getOperateUserName() : "app";
            unregisterDto.setOperateUserName(operator);
            String unregisterTime = DateFormatUtils.format(System.currentTimeMillis(), DateType.DATE_TYPE4);

            //2.1 给用户添加注销标签并解绑消息推送渠道id
            String updateTime = DateFormatUtils.format(System.currentTimeMillis(), DateType.DATE_TYPE3);
            int result = membershipInfoMapper.accountUnregister(authId, membershipType, unregisterTime,
                    AccountStatusEnum.FREEZED.getCode(), updateTime, authId);
            if (result != 1) {
                logUnregisterFailed(unregisterDto, accountInfo, UnregisterException.UNREGISTER_FAILED, null);
                throw UnregisterException.UNREGISTER_FAILED;
            }

            // 2.3 订单分享相关绑定信息解绑
            dubboShareService.unBindUserRecord(authId, operator);

            //2.2 记录注销操作日志
            checkMsg = StringUtils.isNotBlank(checkMsg) ? StringUtils.join("(", checkMsg,  ")") : null;
            logUnregisterRecord(unregisterDto, accountInfo, null, checkMsg);

            //2.3 注销完成，账号登出
            logout(authId, membershipType);

        }finally {
            if (jedisLock != null && jedis != null) {
                jedisLock.releaseLua(jedis);
                jedis.close();
                logger.debug("注销服务-释放redis锁和链接成功");
            }
        }
    }


    @Override
    public void unregisterRecover(UnregisterDto unregisterDto) throws UnregisterException{
        if(StringUtils.isBlank(unregisterDto.getAuthId()) || unregisterDto.getMembershipType() == null) {
            throw UnregisterException.PARAM_ERROR;
        }
        //1. 注销条件检查
        int membershipType = unregisterDto.getMembershipType().intValue();
        String mobile = unregisterDto.getMobile();
        // 1.1 查询会员信息
        AccountStatusDto accountStatus = this.getAccountStatusByMobile(mobile, membershipType);
        // 会员不存在
        if(null == accountStatus) {
            logger.error("会员注销恢复|恢复失败,原因：会员mobile={}不存在", mobile);
            throw UnregisterException.MEMBER_NOT_EXIST;
        }
        String authId = accountStatus.getAuthId();
        // 会员未注销，无需恢复
        if(accountStatus.getAccountStatus() == 0) {
            logger.error("会员注销恢复|恢复失败,原因：会员mobile={} authId={}未注销，无需恢复", mobile, authId);
            throw UnregisterException.MEMBER_NOT_UNREGISTERED;
        }
        // 会员注销已过冻结期，无法再恢复，可重新注册
        if(accountStatus.getAccountStatus() == 2) {
            logger.error("会员注销恢复|恢复失败 原因：会员mobile={} authId={}已过冻结期，无法恢复", mobile, authId);
            throw UnregisterException.MEMBER_OVER_FROZEN;
        }
        //恢复账号
        AccountRecoverInfo recoverInfo = new AccountRecoverInfo();
        BeanCopyUtils.copyProperties(unregisterDto, recoverInfo);
        recoverInfo.setOrigin(unregisterDto.getUnregisterOrigin());
        recoverInfo.setAuthId(authId);
        recoverInfo.setPkId(accountStatus.getPkId());
        recoverInfo.setReason(unregisterDto.getUnregisteReason());
        accountRecover(recoverInfo);
    }

    @Override
    public void unregisterRecoverByAuthId(AccountRecoverDto accountRecoverDto) throws UnregisterException {
        if(StringUtils.isBlank(accountRecoverDto.getAuthId()) || accountRecoverDto.getMembershipType() == null) {
            throw UnregisterException.PARAM_ERROR;
        }
        //非用户自主恢复时，需要
        if("2".equals(accountRecoverDto.getOrigin()) || "3".equals(accountRecoverDto.getOrigin())) {
            if(StringUtils.isBlank(accountRecoverDto.getReason()) || StringUtils.isBlank(accountRecoverDto.getOperateUserName())) {
                throw UnregisterException.PARAM_ERROR;
            }
        }
        String authId = accountRecoverDto.getAuthId();
        MembershipBasicInfo accountInfo = membershipInfoMapper.getUserBasicInfo(authId, accountRecoverDto.getMembershipType());
        //会员不存在
        if(null == accountInfo || StringUtils.isBlank(accountInfo.getMobilePhone())) {
            logger.error("会员注销恢复|恢复失败,原因：会员不存在或已注销完成, authId={}, mobile={}", authId, accountInfo.getMobilePhone());
            throw UnregisterException.MEMBER_NOT_ACTIVE;
        }
        //会员未注销，无需恢复
        if(accountInfo.getAccountStatus() == 0) {
            logger.error("会员注销恢复|账号状态正常无需恢复：authId={}, mobile={}", authId, accountInfo.getMobilePhone());
            return;
        }
        //判断手机号是否存在正常账号
        String mobilePhone = accountInfo.getMobilePhone();
        AccountStatusDto accountStatus = this.getAccountStatusByMobile(mobilePhone, accountRecoverDto.getMembershipType().intValue());
        if(accountStatus.getAccountStatus() == 0 && !authId.equals(accountStatus.getAuthId())) {
            logger.error("会员注销恢复|恢复失败,原因：手机号被其他会员占用，不可恢复，authId={}, mobile={}", authId, mobilePhone);
            throw UnregisterException.MEMBER_MOBILE_USED;
        }
        //恢复账号
        AccountRecoverInfo recoverInfo = new AccountRecoverInfo();
        BeanCopyUtils.copyProperties(accountRecoverDto, recoverInfo);
        recoverInfo.setMobile(mobilePhone);
        recoverInfo.setPkId(accountInfo.getPkId());
        accountRecover(recoverInfo);
    }
    public void accountRecover(AccountRecoverInfo recoverInfo) throws UnregisterException {
        /**
         * 注销恢复
         */
        // 用户信息恢复
        String authId = recoverInfo.getAuthId();
        String updateTime = DateFormatUtils.format(System.currentTimeMillis(), DateType.DATE_TYPE3);
        String operator = StringUtils.isNotBlank(recoverInfo.getOperateUserName()) ?
                recoverInfo.getOperateUserName() : "app";
        int result = membershipInfoMapper.accountUnregister(authId, recoverInfo.getMembershipType(), "",
                AccountStatusEnum.NORMAL.getCode(), updateTime, authId);
        if (result != 1) {
            logger.error("会员注销恢复|恢复失败 原因：会员mobile={} authId={}未能恢复", recoverInfo.getMobile(), authId);
            throw UnregisterException.UNREGISTER_RECOVER_FAILED;
        }
        // 操作日志记录
        ComUtil.insertOperatorLog("账号恢复成功", authId, recoverInfo.getMobile(), operator, userOperatorLogMapper);
        //记录操作日志
        UserOperationLogInput userOptRecord = new UserOperationLogInput();
        userOptRecord.setUserId(recoverInfo.getPkId());
        userOptRecord.setOperationType(MemOperateTypeEnum.RECOVER.getCode());
        userOptRecord.setOperatorId(recoverInfo.getOperateUserId());
        userOptRecord.setOperationContent("账号恢复成功");
        userOptRecord.setOperator(recoverInfo.getOperateUserName());
        userOptRecord.setMiscDesc(recoverInfo.getOrigin());
        saveUserOperationLog(userOptRecord);
    }

    private static final Integer STATUS_NORMAL = 0;
    private static final int LAST_ORDER_MAX_DAYS = 30 * 2;
    private static final int MEM_CHANGE_LIMIT = 14;
    private static List<Long> key4PwdAndPhone = new ArrayList<Long>(){
        private static final long serialVersionUID = -5019716101673780864L;

        {
        add(MemOperateTypeEnum.CHANGE_MOBILE.getCode());
        add(MemOperateTypeEnum.CHANGE_PWD.getCode());
        add(MemOperateTypeEnum.FIND_PWD.getCode());
        add(MemOperateTypeEnum.RESET_PWD.getCode());
    }};
    private String checkUnregister(UnregisterDto unregisterDto, MembershipAccountInfo accountInfo)
            throws UnregisterException {
        StringBuffer remindMsg = new StringBuffer();
        // 会员信息
        String authId = unregisterDto.getAuthId();
        int membershipType = unregisterDto.getMembershipType().intValue();
        String mobile = accountInfo.getMobilePhone();

        // 1. 账号安全
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.DATE, 0 - MEM_CHANGE_LIMIT);

        /**
         * 通管局通报整改： 移除前两条账号注销检查条件
         */
//        // 1.1 最近一次密码/手机号修改成功时间>14个自然日
//        MmpUserOperationLog pwdPhoneLog = mmpUserOperationLogMapper.selectOneMemOptLogByTypes(
//                accountInfo.getPkId(), calendar.getTime(), key4PwdAndPhone);
//        if(null != pwdPhoneLog) {
//            logUnregisterFailed(unregisterDto, accountInfo, UnregisterException.MEM_PWD_MOBILE_UNSTABLE_EXP, null);
//            throw UnregisterException.MEM_PWD_MOBILE_UNSTABLE_EXP;
//        }
//
//        // 1.2 最近一次更换设备成功时间>14个自然日
//        MmpUserOperationLog bindImeiLog = mmpUserOperationLogMapper.selectOneMemOptLogByType(
//                accountInfo.getPkId(), calendar.getTime(), MemOperateTypeEnum.BIND_IMEI.getCode());
//        if(null != bindImeiLog) {
//            logUnregisterFailed(unregisterDto, accountInfo, UnregisterException.MEM_IMEI_CHANGED_EXP, null);
//            throw UnregisterException.MEM_IMEI_CHANGED_EXP;
//        }

        // 2. 会员基础状态
        // 2.1 黑名单
        if(!STATUS_NORMAL.equals(accountInfo.getStatus())) {
            logUnregisterFailed(unregisterDto, accountInfo, UnregisterException.MEM_BLACK_LIST_EXP, null);
            throw UnregisterException.MEM_BLACK_LIST_EXP;
        }
        // appv5.7.1 去除资料不全的判定，线上出现满足此条件，但是用户有押金未退。
        // 资料不全的会员，刚创建，不许做其他验证即可注销
//        if(Integer.valueOf(-1).equals(accountInfo.getReviewStatus())) {
//            logger.info("会员审核状态为资料不全，可注销。authId={}, membershipType={}", authId, membershipType);
//            remindMsg.append("资料不全；");
//            return remindMsg.toString();
//        }

        // 2.2 会员卡未激活或已暂停
        //TODO 企业账户卡状态判断方式与个人会员不同。
        if(null != accountInfo.getCardNo() &&
                (!STATUS_NORMAL.equals(accountInfo.getCardStatus())
                || !Integer.valueOf(1).equals(accountInfo.getCardActiveStatus()))) {
            logUnregisterFailed(unregisterDto, accountInfo, UnregisterException.MEM_CARD_NOT_ACTIVE_EXP, null);
            throw UnregisterException.MEM_CARD_NOT_ACTIVE_EXP;
        }
        // 2.3 E币账户数量不为0
        if(accountInfo.getRentMins() != null
                && BigDecimal.ZERO.compareTo(accountInfo.getRentMins()) < 0) {
            //若需要二次确认则提示
            if(unregisterDto.getConfirmFlag()) {
                logUnregisterFailed(unregisterDto, accountInfo, UnregisterException.MEM_ECOUNT_NOT_EMPTY_EXP, null, true);
                throw UnregisterException.MEM_ECOUNT_NOT_EMPTY_EXP;
            } else {
                remindMsg.append("E币账户余额").append(accountInfo.getRentMins()).append(";");
            }
        }

        // 查询押金、押金预授权、芝麻信用代扣信息
        QueryDepositInfoInputDTO queryDeposit = new QueryDepositInfoInputDTO();
        queryDeposit.setAuthId(authId);
        DepositAuthorizonDto authDepoDto = memberDepositService.queryAuthorizedDepositInfo(queryDeposit);

        // 2.4 基础押金->现金/预授权大于0
        BigDecimal deposit = accountInfo.getDeposit();
        BigDecimal authDeposit = (null != authDepoDto) ? authDepoDto.getBaseDeposit() : BigDecimal.ZERO;
        if(BigDecimal.ZERO.compareTo(deposit) < 0
                || BigDecimal.ZERO.compareTo(authDeposit) < 0) {
            logUnregisterFailed(unregisterDto, accountInfo, UnregisterException.MEM_BASIC_DEPOSIT_EXIST_EXP, null);
            throw UnregisterException.MEM_BASIC_DEPOSIT_EXIST_EXP;
        }
        // 2.5 车辆押金->现金/预授权不为0 --> 追加预授权判断
        deposit = accountInfo.getDepositVehicle();
        authDeposit = (null != authDepoDto) ? authDepoDto.getVehicleDeposit() : BigDecimal.ZERO;
        if(BigDecimal.ZERO.compareTo(deposit) < 0
                || BigDecimal.ZERO.compareTo(authDeposit) < 0) {
            logUnregisterFailed(unregisterDto, accountInfo, UnregisterException.MEM_VEH_DEPOSIT_EXIST_EXP, null);
            throw UnregisterException.MEM_VEH_DEPOSIT_EXIST_EXP;
        }

        //2.6 芝麻信息代扣授权未取消
        int zhimaCreditAuth = (null != authDepoDto) ? authDepoDto.getWithHoldSignStatus() : 0;
        //芝麻信用代扣已授权
        if(zhimaCreditAuth != 0) {
            logUnregisterFailed(unregisterDto, accountInfo, UnregisterException.MEM_SESAME_CREDIT_EXP, null);
            throw UnregisterException.MEM_SESAME_CREDIT_EXP;
        }

        // 新押金担保方式判断
        try {
            GetMemberDepositInfosData memberDepositInfos = mdDepositService.getMemberDepositInfos(accountInfo.getMid());
            if (memberDepositInfos != null) {
                if (memberDepositInfos.getDepositBalanceStatus() == 2){
                    logUnregisterFailed(unregisterDto, accountInfo, UnregisterException.DEPOSIT_BALANCE_NOT_EMPTY, null);
                    throw UnregisterException.MEM_BASIC_DEPOSIT_EXIST_EXP;
                }

                if (memberDepositInfos.getDepositOrderStatus() == 2){
                    logUnregisterFailed(unregisterDto, accountInfo, UnregisterException.ORDER_DEPOSIT_NOT_EMPTY, null);
                    throw UnregisterException.MEM_BASIC_DEPOSIT_EXIST_EXP;
                }

                if (memberDepositInfos.getDepositZhiMaStatus() == 2){
                    logUnregisterFailed(unregisterDto, accountInfo, UnregisterException.ZHIMA_DEPOSIT_NOT_EMPTY, null);
                    throw UnregisterException.MEM_BASIC_DEPOSIT_EXIST_EXP;
                }
            }
        } catch (UnregisterException e) {
            throw e;
        }catch (Exception e){
            logger.error("用户注销 查询押金信息异常，authId={}", authId,e);
        }

        // 3. 订单状况
        // 3.1 未完成的订单
        int orderCnt = orderService.countOngoingPersonOrderNum(authId.toString());
        if(orderCnt > 0) {
            logger.error("用户注销 存在未完成的订单，不做注销，authId={}");
            logUnregisterFailed(unregisterDto, accountInfo, UnregisterException.MEM_VEH_DEPOSIT_EXIST_EXP, null);
            throw UnregisterException.MEM_OUT_STANDING_ORDER_EXP;
        }

        TaskDetailResponse idsSendVehicle = idsSendVehicleServiceProvider.queryTask(authId);
        if (idsSendVehicle != null && StringUtils.isNotBlank(idsSendVehicle.getSendVehicleTaskSeq())) {
            logger.error("用户注销 存在未完成的送车上门订单，不做注销，authId={}", authId);
            logUnregisterFailed(unregisterDto, accountInfo, UnregisterException.MEM_VEH_DEPOSIT_EXIST_EXP,
                    idsSendVehicle.getSendVehicleTaskSeq());
            throw UnregisterException.MEM_OUT_STANDING_ORDER_EXP;
        }

        // 3.2 30天内存在有效个人订单(分时/短租)，不包括企业订单，不包含已取消订单
        calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.DATE, 0 - LAST_ORDER_MAX_DAYS);
        String returnDateStart = ComUtil.getFormatDate(calendar, ComUtil.DATE_TYPE4);
        //检查用户是否存在：还车时间在一个月之内的有效订单
        OrderInfoDto lastestOrder = intergationOrderService.getMemberLateOpenOrder(authId, returnDateStart);
        Date orderTime = null;
        if(null != lastestOrder) {
            //此处用户当前无未完成订单，即全部订单均为已支付-->还车时间字段应不为空。
            if(StringUtils.isBlank(lastestOrder.getReturndatetime())) {
                orderTime = ComUtil.getDateFromStr(lastestOrder.getCreatedTime(), DateType.DATE_TYPE3);
            } else {
                orderTime = ComUtil.getDateFromStr(lastestOrder.getReturndatetime(), DateType.DATE_TYPE4);
            }
            Date checkFreeTime = appConfigRpcService.queryCurrentDateNatureDay(orderTime);
            Date now = new Date();
            if(now.compareTo(checkFreeTime) <= 0) {
                logger.error("用户注销 末次订单时间不符合注销条件，不做注销，authId={}, orderSeq={}, orderTime={}, checkFreeTime={}",
                        authId, lastestOrder.getOrderSeq(), orderTime, checkFreeTime);
                String msg = UnregisterException.MEM_LASTEST_ORDER_EXP.getMessage() + "，将在" +
                        ComUtil.getFormatDate(checkFreeTime, DateType.DATE_TYPE7) + "后开放注销";
                UnregisterException orderEx = new UnregisterException(UnregisterException.MEM_LASTEST_ORDER_EXP.getCode(), msg);
                logUnregisterFailed(unregisterDto, accountInfo, orderEx, lastestOrder.getOrderSeq());
                throw orderEx;
            }
        }

        if(membershipType == 0) {
            // 4. 车管-违章维修情况
            // 4.1 未处理违章
            List<RpcMemberIllegalDTO> illegals = riskOrderService.queryUntreatedIllegals(authId);
            if(CollectionUtils.isNotEmpty(illegals)) {
                logger.error("用户注销 存在未处理的违章，不做注销，authId={}", authId);
                String ids = getLogSeqs(illegals.stream().map(p->p.getIllegalSeq()).collect(Collectors.toList()));
                logUnregisterFailed(unregisterDto, accountInfo, UnregisterException.MEM_REF_RISKORDER_EXP,
                        "未处理的违章=" + ids);
                throw UnregisterException.MEM_REF_RISKORDER_EXP;
            }
            // 4.2 未处理违章/维修/逾期租车费风控订单
            //风控订单-违章-未处理
            List<RpcRiskOrderDTO> riskOrders = riskOrderService.queryUntreatedRiskOrder(authId);
            if(CollectionUtils.isNotEmpty(riskOrders)) {
                logger.error("用户注销 存在未处理的违章违约金订单，不做注销，authId={}", authId);
                String ids = getLogSeqs(riskOrders.stream().map(p->p.getOrderSeq()).collect(Collectors.toList()));
                logUnregisterFailed(unregisterDto, accountInfo, UnregisterException.MEM_REF_RISKORDER_EXP,
                        "风控订单=" + ids);
                throw UnregisterException.MEM_REF_RISKORDER_EXP;
            }
            // 5. 调度-未处理关联事故任务
            List<String> idsTasks = idsMembershipInfoServiceProvider.getCustomerDoingTasks(authId);
             if(CollectionUtils.isNotEmpty(idsTasks)) {
                logger.error("用户注销 存在未处理调度关联事故任务，不做注销，authId={}", authId);
                logUnregisterFailed(unregisterDto, accountInfo, UnregisterException.MEM_REF_ACCIDNET_TASK_EXP, getLogSeqs(idsTasks));
                throw UnregisterException.MEM_REF_ACCIDNET_TASK_EXP;
            }

            // 7. 维修-未处理的关联维修任务
            QueryRepairHistroyByUserDTO queryRepairDto = new QueryRepairHistroyByUserDTO();
            queryRepairDto.setMobilePhone(mobile);
            queryRepairDto.setUserName(accountInfo.getName());
            List<UserRepairHistroyDTO> repairTasks = mtcRepairTaskService.queryRepairTaskByAuthId(authId);
            if(CollectionUtils.isNotEmpty(repairTasks)) {
                logger.error("用户注销存在未处理关联维修任务，不做注销，authId={}, repareTasks={}", authId, JSON.toJSONString(repairTasks));
                String ids = getLogSeqs(repairTasks.stream().map(p->p.getTaskNo()).collect(Collectors.toList()));
                logUnregisterFailed(unregisterDto, accountInfo, UnregisterException.MEM_REF_REPAIR_TASK_EXP, ids);
                throw UnregisterException.MEM_REF_REPAIR_TASK_EXP;
            }

            //8. 客服-未处理的关联付款申请单
            List<RefundBaseInfoDTO> refundList = refundBaseInfoService.queryRefundBaseInfoInUse(authId);
            if(CollectionUtils.isNotEmpty(refundList)) {
                logger.error("用户注销 存在未处理的付款申请单，不做注销，authId={}, refundTasks={}", authId, JSON.toJSONString(refundList));
                String ids = getLogSeqs(refundList.stream().map(p->p.getRefundSeq()).collect(Collectors.toList()));
                logUnregisterFailed(unregisterDto, accountInfo, UnregisterException.MEM_REF_PAY_TASK_EXP, "付款申请单编号=" + ids);
                throw UnregisterException.MEM_REF_PAY_TASK_EXP;
            }
        }
        return remindMsg.toString();
    }

    private int MAX_LOG_SEQ_SIZE = 5;
    private String getLogSeqs(List<String> list) {
        String content = StringUtils.EMPTY;
        if(list.size() > MAX_LOG_SEQ_SIZE) {
            list = list.subList(0, MAX_LOG_SEQ_SIZE);
            content = "...";
        }
        return StringUtils.join(list, ",") + content;
    }

    /**
     * 账号登出
     *
     * @param authId
     * @param membershipType
     */
    private void logout(String authId, Integer membershipType) throws UnregisterException{
        if (StringUtils.isBlank(authId) || null == membershipType) {
            return;
        }
        try {
            String logoutUrl = commConfig.getAppApiAddress() + commConfig.getLogoutUrl();
            //参数
            Map<String, Object> paramMap = new HashMap<>(2);
            paramMap.put("authId", authId);
            paramMap.put("membershipType", membershipType);
            String result = HttpClientUtils.httpPostRequest(logoutUrl, JSON.toJSONString(paramMap));

            AppResponseDto responseDto = null;
            responseDto = JSON.parseObject(result, AppResponseDto.class);
            if (responseDto == null || responseDto.getStatus() != 0) {
                logger.error("调用APP接口，会员登出失败, authId={}, result={}。", authId, responseDto.getStatus());
                throw UnregisterException.UNREGISTER_FAILED;
            }
            logger.info("调用APP接口，完成会员登出，authId={}, result={}。", authId, result);
        } catch (Exception e) {
            logger.error("调用APP接口，会员登出失败，authId=" + authId, e);
            throw UnregisterException.UNREGISTER_FAILED;
        }
    }


    private void logUnregisterFailed(UnregisterDto unregisterDto,
                                     MembershipAccountInfo account, UnregisterException ex, String refKey) {
        logUnregisterFailed(unregisterDto, account, ex, refKey, false);
    }
    /**
     * 记录注销操作异常
     */
    private void logUnregisterFailed(UnregisterDto unregisterDto,
                                     MembershipAccountInfo account, UnregisterException ex, String refKey, Boolean warning){
        //注销失败日志写入，为单独事务
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus status = txManager.getTransaction(def);
        try {
            Long code = MemOperateTypeEnum.UNREGISTER_FAILED.getCode();
            StringBuffer content = new StringBuffer();
            if(warning) {
                content.append("账号注销提醒：").append(ex.getCode()).append("-").append(ex.getMessage());
            }else {
                content.append("账号注销失败,原因：").append(ex.getCode()).append("-").append(ex.getMessage());
            }
            if(StringUtils.isNotBlank(refKey)) {
                content.append(", ").append(refKey);
            }
            logUnregisterRecord(unregisterDto, account, code, content.toString());
            txManager.commit(status);
        }catch (Exception e) {
            txManager.rollback(status);
        }
    }

    /**
     * 记录注销日志
     * @param unregisterDto
     * @param account
     * @param code
     * @param content
     */
    public void logUnregisterRecord(UnregisterDto unregisterDto, MembershipAccountInfo account, Long code, String content){
        String operator = StringUtils.defaultIfBlank(unregisterDto.getOperateUserName(), "app");
        content = StringUtils.defaultString(content);
        if(code == null) {
            code = MemOperateTypeEnum.UNREGISTER.getCode();
            content = "账号注销成功" + content;
        }
        ComUtil.insertOperatorLog(content, unregisterDto.getAuthId(),
                account.getMobilePhone(), operator, userOperatorLogMapper);
        //记录操作日志
        UserOperationLogInput userOptRecord = new UserOperationLogInput();
        userOptRecord.setUserId(account.getPkId());
        userOptRecord.setRefKey1(account.getMobilePhone());
        userOptRecord.setOperationType(code);
        userOptRecord.setOperationContent(content);
        userOptRecord.setOperatorId(unregisterDto.getOperateUserId());
        userOptRecord.setOperator(operator);
        userOptRecord.setMiscDesc(unregisterDto.getUnregisteReason());
        saveUserOperationLog(userOptRecord);
    }

    @Override
    public AccountStatusDto getAccountStatusByMobile(String mobilePhone, Integer membershipType) {
        return this.getAccountStatusByMobileV2(mobilePhone,membershipType,null);
    }

    @Override
    public AccountStatusDto getAccountStatusByMobileV2(String mobilePhone, Integer membershipType, String orgId) {
        if (StringUtils.isBlank(mobilePhone) || null == membershipType){
            return null;
        }
        //查询会员最新账号信息
        AccountStatusDto accountStatusDto = membershipInfoMapper.getAccountStatusByPhone(mobilePhone, membershipType,orgId);
        return accountStatusDto;
    }

    @Override
    public AccountStatusDto getAccountStatusByDriverCode(String driverCode, Integer membershipType) {
        if (StringUtils.isBlank(driverCode) || null == membershipType){
            return null;
        }
        //查询会员最新账号信息
        AccountStatusDto accountStatusDto = membershipInfoMapper.getAccountStatusByDriverCode(driverCode, membershipType);
        return accountStatusDto;
    }

    @Override
    public void saveUserOperationLog(UserOperationLogInput userOperationLogInput) {
        memberOptLogService.saveUserOperationLog(userOperationLogInput);
    }

    @Override
	@Transactional(rollbackFor = Exception.class)
	public void saveUserOperationLogOldTable(String operatorContent, String foreignKey, String foreignKey2, String createUser) {
    	UserOperatorLog userOperatorLog = new UserOperatorLog();
        userOperatorLog.setOperatorContent(operatorContent);
        userOperatorLog.setForeignKey(foreignKey);
        userOperatorLog.setForeignKey2(foreignKey2);
        userOperatorLog.setCreatedUser(createUser);
        userOperatorLog.setCreatedTime(ComUtil.getSystemDate("yyyyMMddHHmmssSSS"));
        userOperatorLogMapper.saveSelective(userOperatorLog);
    }

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void findPassword(String mobilePhone, String verifyCode, String newPassword, UpdateUserDto updateUserDto)
			throws UserMessageException {
        FindPasswordDto findPasswordDto = new FindPasswordDto();
        findPasswordDto.setMobilePhone(mobilePhone);
        findPasswordDto.setNewPassword(newPassword);
        findPasswordDto.setVerifyCode(verifyCode);
        findPasswordDto.setUpdateUserDto(updateUserDto);
        try {
            this.findPasswordV2(findPasswordDto);
        }catch (FindPasswordException e){
            throw new UserMessageException(-1,e.getMessage());
        }
	}

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void findPasswordV2(FindPasswordDto findPasswordDto) throws FindPasswordException {
        String mobilePhone = findPasswordDto.getMobilePhone();
        String newPassword = findPasswordDto.getNewPassword();
        String verifyCode = findPasswordDto.getVerifyCode();
        UpdateUserDto updateUserDto = findPasswordDto.getUpdateUserDto();
        // 手机号不可以为空
        if (StringUtils.isBlank(mobilePhone)) {
            throw new FindPasswordException(StatusCode.PARAM_EMPTY);
        }
        // 密码不能为空
        if (StringUtils.isBlank(newPassword)) {
            throw new FindPasswordException(StatusCode.PARAM_EMPTY);
        }
        // 验证密码格式是否正确
        String msg = ComUtil.checkPassword(newPassword);
        if (msg != null) {
            throw new FindPasswordException(StatusCode.PASSWORD_FORMAT_ERROR);
        }
        Pattern pattern = Pattern.compile("([0-9]){11}");
        boolean checkResult = pattern.matcher(mobilePhone).matches();
        boolean checkStatus = false;
        if (StringUtils.isNotBlank(verifyCode)) {
            if (memberOperateServ.checkVerifyCode(mobilePhone, verifyCode, true)) {
            	checkStatus = true;
            }
        } else {
            String checkVerifyStatus = JedisUtil.get(BussinessConstants.CHECK_VERIFY_STATUS + mobilePhone);
            if (StringUtils.isNotBlank(checkVerifyStatus) && "1".equals(checkVerifyStatus)) {
                checkStatus = true;
            }
        }
        String optUserName = StringUtils.EMPTY;
        String updateTime = ComUtil.getFormatDate(new Date(), ComUtil.DATE_TYPE3);
        Long userId = 0L;
        if (updateUserDto != null) {
            optUserName = updateUserDto.getUserName();
            userId = updateUserDto.getUserId();
        }
        // 验证码正确修改用户密码
        if (checkStatus) {
            // 个人用户
            if (checkResult) {
                String password = Crypto.encryptDES(newPassword, ComUtil.ENCRYPT_KEY);
                int i = membershipInfoMapper.updatePasswordByMobilePhone(mobilePhone, password, optUserName, updateTime, findPasswordDto.getOrgId());
                if(i==0){
                    throw new FindPasswordException(StatusCode.UPDATE_FAILED);
                }
            } else { // 企业用户
                tEsUserMapper.updateCodedPasswordByName(mobilePhone, ComUtil.MD5(newPassword));
            }
        } else {
            throw new FindPasswordException(StatusCode.VALIDATE_CODE_ERROR);
        }
        // 记录操作日志
        MembershipBasicInfo member = membershipInfoMapper.getMembershipByPhone(mobilePhone, 0);
        if (member != null) {
            // 记录操作日志
            ComUtil.insertOperatorLog(MemOperateTypeEnum.FIND_PWD.getOperate(), member.getAuthId(), mobilePhone,
                    optUserName, userOperatorLogMapper);

            UserOperationLogInput userOptRecord = new UserOperationLogInput();
            userOptRecord.setUserId(member.getPkId());
            userOptRecord.setRefKey1(mobilePhone);
            userOptRecord.setOperationType(MemOperateTypeEnum.FIND_PWD.getCode());
            userOptRecord.setOperatorId(userId);
            userOptRecord.setOperationContent(MemOperateTypeEnum.FIND_PWD.getOperate());
            userOptRecord.setOperator(optUserName);
            saveUserOperationLog(userOptRecord);
            //更新用户imei
            if(StringUtils.isNotBlank(findPasswordDto.getImeiNo())){
                memberOperateServ.changeImei(findPasswordDto.getImeiNo(),member.getMobilePhone(),member.getAuthId(),2);
            }
            // 忘记密码 埋点
            try {
                Map<String, Object> properties = new HashMap<>();
                properties.put("result", true);
                //sensorsdataService.track(member.getAuthId(), false, "ResetPassword", properties);
            } catch (Exception e) {
                logger.error("神策数据埋点-记录事件时捕获到异常", e);
            }
        }
    }

    @Override
	@Transactional(rollbackFor = Exception.class)
	public void modifyPassword(ModifyPasswordDTO modifyPasswordDTO) throws UserMessageException {
        try {
            this.modifyPasswordV2(modifyPasswordDTO);
        }catch (ModifyPasswordException e){
            throw new UserMessageException(-1,e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyPasswordV2(ModifyPasswordDTO modifyPasswordDTO) throws ModifyPasswordException {
        String authId = modifyPasswordDTO.getAuthId();
        // 修改前密码不可以为空
        String oldPassword = modifyPasswordDTO.getOldPassword();
        if (StringUtils.isBlank(oldPassword)) {
            throw new ModifyPasswordException(StatusCode.PARAM_EMPTY);
        }
        // 修改后密码不可以为空
        String newPassword = modifyPasswordDTO.getNewPassword();
        if (StringUtils.isBlank(newPassword)) {
            throw new ModifyPasswordException(StatusCode.PARAM_EMPTY);
        }
        // 密码正则表达式校验
        String msg = checkPassword(newPassword);
        if (msg != null) {
            throw new ModifyPasswordException(StatusCode.PASSWORD_FORMAT_ERROR);
        }
        // 密码不作修改
        if (newPassword.equals(oldPassword)) {
            throw new ModifyPasswordException(StatusCode.OLD_PASSWORD_EQUEAL_NEW_PASSWORD);
        }
        // 查询用户信息
        MembershipInfo member = membershipInfoMapper.selectMembershipInfoByAuthId(authId, modifyPasswordDTO.getOrgId());
        if (member != null) {
            Integer orgUser = modifyPasswordDTO.getOrgUser();
            // 0:个人用户 1：企业用户
            if (orgUser != null && orgUser == 0) {
                // 数据库的密码解密
                String oldPwd = member.getPassword();
                oldPwd = Crypto.decryptDES(oldPwd, ComUtil.ENCRYPT_KEY);
                if (!oldPassword.equals(oldPwd)) {
                    throw new ModifyPasswordException(StatusCode.OLD_PASSWORD_ERROR);
                } else {
                    // 修改密码
                    newPassword = Crypto.encryptDES(newPassword, ComUtil.ENCRYPT_KEY);
                    membershipInfoMapper.updatePassword(authId, newPassword, modifyPasswordDTO.getOptUserName(),
                            ComUtil.getFormatDate(new Date(), ComUtil.DATE_TYPE3));
                }
            } else {
                // 企业用户
                tEsUserMapper.updateCodedPasswordByName(modifyPasswordDTO.getOrgUserName(), ComUtil.MD5(newPassword));
            }
        } else {
            throw new ModifyPasswordException(StatusCode.SYSTEM_ERROR);
        }
        String optUserName = modifyPasswordDTO.getOptUserName();
        if(StringUtils.isBlank(optUserName)) {
            optUserName = authId;
        }
        // 记录操作日志
        ComUtil.insertOperatorLog(MemOperateTypeEnum.CHANGE_PWD.getOperate(), member.getAuthId(),
                member.getMobilePhone(), optUserName, userOperatorLogMapper);

        UserOperationLogInput userOptRecord = new UserOperationLogInput();
        userOptRecord.setUserId(member.getPkId());
        userOptRecord.setRefKey1(member.getMobilePhone());
        userOptRecord.setOperationType(MemOperateTypeEnum.CHANGE_PWD.getCode());
        userOptRecord.setOperatorId(modifyPasswordDTO.getOptUserId());
        userOptRecord.setOperationContent(MemOperateTypeEnum.CHANGE_PWD.getOperate());
        userOptRecord.setOperator(optUserName);
        saveUserOperationLog(userOptRecord);
    }

    @Override
	@Transactional(rollbackFor = Exception.class)
	public void resetPassword(String authId, String password, String optUserName, Date updateTime)
			throws UserMessageException {
		// 查询用户
		MembershipBaseInfo member = membershipInfoMapper.selectBaseByAuthId(authId, 0);
		if (member == null) {
			throw UserMessageException.MEMBER_NOT_EXIST;
		}
		String drivingLicense = member.getDriverCode();
		// 如果密码为空则取驾照号后六位加密作为密码
		if (StringUtils.isBlank(password) && StringUtils.isNotBlank(drivingLicense)) {
			password = Crypto.encryptDES(drivingLicense.substring(drivingLicense.length() - 6, drivingLicense.length()),
					ComUtil.ENCRYPT_KEY);
		}
		if(StringUtils.isBlank(optUserName)) {
            optUserName = authId;
        }
		//重置密码
		membershipInfoMapper.updatePassword(authId, password, optUserName,
				ComUtil.getFormatDate(updateTime, ComUtil.DATE_TYPE3));
		//记录操作日志
		ComUtil.insertOperatorLog(MemOperateTypeEnum.RESET_PWD.getOperate(), member.getAuthId(),
				member.getMobilePhone(), optUserName, userOperatorLogMapper);

        UserOperationLogInput userOptRecord = new UserOperationLogInput();
        userOptRecord.setUserId(member.getPkId());
        userOptRecord.setRefKey1(member.getMobilePhone());
        userOptRecord.setOperationType(MemOperateTypeEnum.RESET_PWD.getCode());
        userOptRecord.setOperatorId(-1L);
        userOptRecord.setOperationContent(MemOperateTypeEnum.RESET_PWD.getOperate());
        userOptRecord.setOperator(optUserName);
        saveUserOperationLog(userOptRecord);
	}

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyMobilePhone(String authId, String newMobile, String verifyCode,
                                  String oldMobile, UpdateUserDto updateUserDto) throws UserMessageException {
        ModifyMobilePhoneDto modifyMobilePhoneDto = new ModifyMobilePhoneDto();
        modifyMobilePhoneDto.setAuthId(authId);
        modifyMobilePhoneDto.setNewMobile(newMobile);
        modifyMobilePhoneDto.setOldMobile(oldMobile);
        modifyMobilePhoneDto.setVerifyCode(verifyCode);
        modifyMobilePhoneDto.setUpdateUserDto(updateUserDto);
        try {
            this.modifyMobilePhoneV2(modifyMobilePhoneDto);
        } catch (ChangeMobileException e) {
            throw new UserMessageException(-1,e.getMessage());
        }
    }

    @Override
    public ModifyPhoneResultDto modifyMobilePhoneUnLogin(String newMobilePhone, String verifyCode, String oldMobile, UpdateUserDto updateUserDto,
                                                         String driverCode, String passWord, String channel) throws  ChangeMobileException {
        //未登录情况下修改
        MembershipBasicInfo membershipByPhone =   membershipInfoMapper.getMembershipByPhone(oldMobile,0);
        if (membershipByPhone == null){
            throw new ChangeMobileException(StatusCode.USER_NOT_EXITED);
        }
        ModifyPhoneResultDto modifyPhoneResultDto = new ModifyPhoneResultDto();
        if (membershipByPhone.getReviewStatus().intValue() == 1 && membershipByPhone.getAuthenticationStatus() == 2) {

            String driverNum = membershipByPhone.getDriverCode();
            if (StringUtils.isNotBlank(driverNum) && driverNum.length()>6){
                modifyPhoneResultDto.setCheckMd5(driverNum.substring(driverNum.length()-6,driverNum.length()));
                if (StringUtils.isBlank(driverCode) || !driverNum.substring(driverNum.length()-6,driverNum.length()).equals(driverCode)){
                    modifyPhoneResultDto.setCheckMd5(driverNum.substring(driverNum.length()-1,driverNum.length()));
                    modifyPhoneResultDto.setCode("30039");
                    return modifyPhoneResultDto;
                }
            }
        }else{
            throw new ChangeMobileException(StatusCode.AUTHENTICATION_ERROR_ONE);
        }
        if (StringUtils.isBlank(newMobilePhone)){
            throw new ChangeMobileException(StatusCode.MOBILE_FORMATE_ERROR);
        }
        if (saicMemberService.beginSaic()) {
            try {
                saicMemberService.modifyUserMobile(oldMobile, passWord, newMobilePhone, verifyCode, channel);
                //记录操作日志
                String updateUser = membershipByPhone.getAuthId();
                Long userId = -1L;
                if (updateUserDto != null) {
                    updateUser = updateUserDto.getUserName();
                    userId = updateUserDto.getUserId();
                }
                String message = MemOperateTypeEnum.UN_LOGIN_SUBMIT_CHANGE_MOBILE.getOperate() + ", " + oldMobile + "->" + newMobilePhone;
                ComUtil.insertOperatorLog(message, membershipByPhone.getAuthId(), newMobilePhone, updateUser, userOperatorLogMapper);

                UserOperationLogInput userOptRecord = new UserOperationLogInput();
                userOptRecord.setUserId(membershipByPhone.getPkId());
                userOptRecord.setRefKey1(newMobilePhone);
                userOptRecord.setOperationType(MemOperateTypeEnum.CHANGE_MOBILE.getCode());
                userOptRecord.setOperationType(MemOperateTypeEnum.CHANGE_MOBILE.getCode());
                userOptRecord.setOperatorId(userId);
                userOptRecord.setOperationContent(message);
                userOptRecord.setOperator(updateUser);
                saveUserOperationLog(userOptRecord);
            } catch (BusinessException e) {
                logger.error("未登录修改手机号失败 saic,入参：oldMobile=" + oldMobile + ", passWord=" + passWord +", newMobilePhone="+ newMobilePhone + ",verifyCode=" + verifyCode + ",channel=" + channel, e);
                throw new ChangeMobileException(-1, e.getMessage());
            }
        } else {
            //校验手机号格式
            boolean checkResult = pattern.matcher(newMobilePhone.trim()).matches();
            if (!checkResult) {
                throw new ChangeMobileException(StatusCode.MOBILE_FORMATE_ERROR);
            }
            // 检验验证码是否正确
            if (!memberOperateServ.checkVerifyCode(newMobilePhone, verifyCode, true)) {
                throw new ChangeMobileException(StatusCode.VALIDATE_CODE_ERROR);
            }
            // 检查手机号是否已经被使用
            AccountStatusDto accountStatus = membershipInfoMapper.checkMobilRegistered(newMobilePhone, membershipByPhone.getAuthId(), 0);
            if (accountStatus != null) {
                if (AccountStatusEnum.NORMAL.getCode().equals(accountStatus.getAccountStatus())) {
                    throw new ChangeMobileException(StatusCode.MOBILE_USED);
                }
                if (AccountStatusEnum.FREEZED.getCode().equals(accountStatus.getAccountStatus())) {
                    throw new ChangeMobileException(StatusCode.MOBILE_USED);
                }
            }

            // 更新手机号
            String authId = membershipByPhone.getAuthId();
            String updateTime = ComUtil.getFormatDate(new Date(), ComUtil.DATE_TYPE3);
            String updateUser = authId; //默认操作人为会员authId
            Long userId = -1L;
            if (updateUserDto != null) {
                updateUser = updateUserDto.getUserName();
                userId = updateUserDto.getUserId();
            }
            int updateResult = membershipInfoMapper.updateMobilePhone(authId, 0, newMobilePhone, updateTime, updateUser, membershipByPhone.getOrgId());
            if (updateResult > 0) {
                // 更新绑定设备关系
                this.changeMobileBindImei(oldMobile, newMobilePhone);
                // 删除验证码
                JedisUtil.del(newMobilePhone);
                //记录操作日志
                String message = MemOperateTypeEnum.CHANGE_MOBILE.getOperate() + ", " + oldMobile + "->" + newMobilePhone;
                ComUtil.insertOperatorLog(message, authId, newMobilePhone, updateUser, userOperatorLogMapper);

                UserOperationLogInput userOptRecord = new UserOperationLogInput();
                userOptRecord.setUserId(membershipByPhone.getPkId());
                userOptRecord.setRefKey1(newMobilePhone);
                userOptRecord.setOperationType(MemOperateTypeEnum.CHANGE_MOBILE.getCode());
                userOptRecord.setOperationType(MemOperateTypeEnum.CHANGE_MOBILE.getCode());
                userOptRecord.setOperatorId(userId);
                userOptRecord.setOperationContent(message);
                userOptRecord.setOperator(updateUser);
                saveUserOperationLog(userOptRecord);
            } else {
                throw new ChangeMobileException(StatusCode.UPDATE_FAILED);
            }
        }
        modifyPhoneResultDto.setCode("0");
        return modifyPhoneResultDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyMobilePhoneV2(ModifyMobilePhoneDto modifyMobilePhoneDto) throws ChangeMobileException {
        String authId = modifyMobilePhoneDto.getAuthId();
        String newMobile = modifyMobilePhoneDto.getNewMobile();
        String oldMobile = modifyMobilePhoneDto.getOldMobile();
        String orgId = modifyMobilePhoneDto.getOrgId();
        UpdateUserDto updateUserDto = modifyMobilePhoneDto.getUpdateUserDto();
        String verifyCode = modifyMobilePhoneDto.getVerifyCode();
        MembershipBaseInfo membershipInfo = membershipInfoMapper.selectBaseByAuthId(authId, 0);
        if (membershipInfo.getReviewStatus().intValue() == 1 && membershipInfo.getAuthenticationStatus() == 2) {
            String checkAuthIdSatus = JedisUtil.get(BussinessConstants.CHECK_AUTHID_STATUS + authId);
            if (StringUtils.isBlank(checkAuthIdSatus) || !"1".equals(checkAuthIdSatus)) {
                throw new ChangeMobileException(StatusCode.AUTHENTICATION_ERROR_ONE);
            }
        }
        //校验手机号格式
        boolean checkResult = pattern.matcher(newMobile.trim()).matches();
        if (!checkResult) {
            throw new ChangeMobileException(StatusCode.MOBILE_FORMATE_ERROR);
        }
        // 增加限制验证码重试次数
        long checkNum = JedisUtil.hincrby(VERIFY_CODE_NUM, newMobile, 1);
        if (checkNum >= 10) {
            throw new ChangeMobileException(StatusCode.VALIDATE_CODE_ERROR);
        }
        // 检验验证码是否正确
        if (!memberOperateServ.checkVerifyCode(newMobile, verifyCode, true)) {
            throw new ChangeMobileException(StatusCode.VALIDATE_CODE_ERROR);
        }
        // 检查手机号是否已经被使用
        AccountStatusDto accountStatus = membershipInfoMapper.checkMobilRegistered(newMobile, authId,0);
        if(accountStatus != null) {
            if(AccountStatusEnum.NORMAL.getCode().equals(accountStatus.getAccountStatus())) {
                throw new ChangeMobileException(StatusCode.MOBILE_USED);
            }
            if(AccountStatusEnum.FREEZED.getCode().equals(accountStatus.getAccountStatus())) {
                throw new ChangeMobileException(StatusCode.MOBILE_USED);
            }
        }
        // 更新手机号
        updateDbMobilePhone(authId, membershipInfo.getPkId(), newMobile, oldMobile, updateUserDto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDbMobilePhone(String authId, Long userPkId, String newMobile, String oldMobile, UpdateUserDto updateUserDto) throws ChangeMobileException {
        // 更新手机号
        String updateTime = ComUtil.getFormatDate(new Date(), ComUtil.DATE_TYPE3);
        String updateUser = authId; //默认操作人为会员authId
        Long userId = -1L;
        if(updateUserDto != null) {
            updateUser = updateUserDto.getUserName();
            userId = updateUserDto.getUserId();
        }
        int updateResult = membershipInfoMapper.updateMobilePhone(authId, 0, newMobile, updateTime, updateUser, StringUtils.EMPTY);
        if (updateResult > 0) {
            // 更新绑定设备关系
            this.changeMobileBindImei(oldMobile, newMobile);
            // 删除验证码
            JedisUtil.del(newMobile);
            //记录操作日志
            String message = MemOperateTypeEnum.CHANGE_MOBILE.getOperate() + ", " + oldMobile + "->" + newMobile;
            ComUtil.insertOperatorLog(message, authId, newMobile, updateUser, userOperatorLogMapper);

            UserOperationLogInput userOptRecord = new UserOperationLogInput();
            userOptRecord.setUserId(userPkId);
            userOptRecord.setRefKey1(newMobile);
            userOptRecord.setOperationType(MemOperateTypeEnum.CHANGE_MOBILE.getCode());
            userOptRecord.setOperationType(MemOperateTypeEnum.CHANGE_MOBILE.getCode());
            userOptRecord.setOperatorId(userId);
            userOptRecord.setOperationContent(message);
            userOptRecord.setOperator(updateUser);
            saveUserOperationLog(userOptRecord);
        } else {
            throw new ChangeMobileException(StatusCode.UPDATE_FAILED);
        }
    }
    @Override
    public MembershipRegionInfo getUserRegionInfo(String authId, Short membershipType) {
        MembershipRegionInfo base = membershipInfoMapper.getUserRegionInfo(authId, membershipType);
        if (base == null && membershipType == 0) {
            logger.info("getUserRegionInfo 兼容擎路用户,authId={}",authId);
            return getUserRegionInfo(authId,(short)2);
        }
        return base;
    }

    public void changeMobileBindImei(String oldMobilePhone, String newMobilePhone) {
        if(StringUtils.isNotBlank(oldMobilePhone) && StringUtils.isNotBlank(newMobilePhone)){
            String imei = JedisUtil.get("imei_" + oldMobilePhone);
            if(StringUtils.isNotEmpty(imei)){
                JedisUtil.set("imei_" + newMobilePhone, imei);
                JedisUtil.set("unique_imei_"+imei, newMobilePhone);
                JedisUtil.del("imei_" + oldMobilePhone);
            }
            String times = JedisUtil.get("imeiChangeNum_" + newMobilePhone);
            if(StringUtils.isNotEmpty(times)){
                JedisUtil.set("imeiChangeNum_" + newMobilePhone,times);
                JedisUtil.del("imeiChangeNum_" + oldMobilePhone);
            }
        }
    }

    @Override
    public AppKeyDto getMemberAuthOrigin(String authId, Integer membershipType, Long operateType) {
        MembershipBaseInfo memberInfo = membershipInfoMapper.selectBaseByAuthId(authId, Integer.valueOf(membershipType));
        if(memberInfo == null) {
            if (membershipType == 0) {
                logger.info("getMemberAuthOrigin 兼容擎路用户,authId={}",authId);
                return getMemberAuthOrigin(authId,2,operateType);
            }
            logger.warn("会员信息不存在 , authId={}, membershipType={}, opType={}", authId, membershipType, operateType);
            return null;
        }

        MmpUserOperationLog opLog = mmpUserOperationLogMapper.selectlastestMemOptLogByType(memberInfo.getPkId(), operateType);
        if(opLog == null || StringUtils.isBlank(opLog.getRefKey1())) {
            logger.warn("会员无认证来源信息, authId={}, membershipType={}, opType={}", authId, membershipType, operateType);
            return null;
        }

        String appKey = opLog.getRefKey1();
        AppKeyDto appKeyDto = new AppKeyDto();
        appKeyDto.setAppKey(appKey);
        AppKeyManager appKeyInfo = appKeyManagerMapper.selectByPrimaryKey(appKey);
        if(appKeyInfo != null) {
            BeanCopyUtils.copyProperties(appKeyInfo, appKeyDto);
        }
        return appKeyDto;
    }

    @Override
    public List<ExpressInfoDTO> queryExpressInfo(String authId) {
        List<ExpressInfoDTO> expressInfoDTOList = null;
        List<ExpressInfo> expressInfoList = expressInfoMapper.selectByAuthId(authId);
        if(CollectionUtils.isNotEmpty(expressInfoList)){
            expressInfoDTOList = new ArrayList<>(expressInfoList.size());
            for(ExpressInfo expressInfo : expressInfoList){
                ExpressInfoDTO expressInfoDTO = new ExpressInfoDTO();
                BeanUtils.copyProperties(expressInfo,expressInfoDTO);
                expressInfoDTOList.add(expressInfoDTO);
            }
        }
        return expressInfoDTOList;
    }

    @Override
    public List<MemberClauseRecord> queryMemberClauseRecord(QueryMemberClauseRecordInput input) {
        return userContractMapper.selectMemberClauseRecord(input.getAuthId(),(input.getPageNum() - 1) * input.getPageSize(), input.getPageSize());
    }

    @Override
    public List<MemberOperateLogDTO> queryMemberOperateLog(QueryMemberOperateLogInput input) {
        List<MemberOperateLogDTO> memberOperateLogDTOList = null;
        MembershipBasicInfo userBasicInfo = this.getUserBasicInfo(input.getAuthId(), (short) 0);
        if(userBasicInfo != null){
            List<MmpUserOperationLog> mmpUserOperationLogList = mmpUserOperationLogMapper.selectMmpUserOperationLog(userBasicInfo.getPkId(), (input.getPageNum() - 1) * input.getPageSize(), input.getPageSize());
            if(CollectionUtils.isNotEmpty(mmpUserOperationLogList)){
                memberOperateLogDTOList = new ArrayList<>(mmpUserOperationLogList.size());
                for(MmpUserOperationLog mmpUserOperationLog : mmpUserOperationLogList){
                    MemberOperateLogDTO memberOperateLogDTO = new MemberOperateLogDTO();
                    BeanUtils.copyProperties(mmpUserOperationLog,memberOperateLogDTO);
                    memberOperateLogDTOList.add(memberOperateLogDTO);
                }
            }
        }
        return memberOperateLogDTOList;
    }

    @Override
    public List<SimpleMembershipInfoDTO> querySimpleMembershipInfoList(QuerySimpleMemberInput querySimpleMemberInput) {
        Page page = querySimpleMemberInput.getPage();
        if(page == null){
            querySimpleMemberInput.setPage(new Page(1,10));
        }
        List<SimpleMembershipInfoDTO> simpleMembershipInfoDTOList = membershipInfoMapper.selectSimpleMembershipInfo(querySimpleMemberInput);
        simpleMembershipInfoDTOList.forEach(p->{
            String driverCode = p.getDriverCode();
            if(StringUtils.isNotBlank(driverCode) && driverCode.length() > 6){
                p.setDriverCode(driverCode.substring(0,4)+ "****" + driverCode.substring(driverCode.length()- 4));
            }
        });
        return simpleMembershipInfoDTOList;
    }

	@Override
	public List<UserOperatorLogDTO> queryUserOperatorLog(QueryMemberOperateLogInput input) {
        Page page = null;
        if(input.getPageNum() != null && input.getPageSize() != null){
            page = new Page(input.getPageNum(),input.getPageSize());
        }
        List<UserOperatorLogDTO> list = userOperatorLogMapper.queryUserOperateLog(input.getAuthId(), input.getForeignKey2(),page);
        // 如果外键2有值 查询机构名
        if(CollectionUtils.isNotEmpty(list)){
            for (UserOperatorLogDTO userOperatorLogDTO : list) {
            	if (StringUtils.isNotBlank(userOperatorLogDTO.getForeignKey2())){
					List<Long> userIds = new ArrayList<>();
					userIds.add(Long.parseLong(userOperatorLogDTO.getForeignKey2()));
					List<SsoUserInfoDto> ssoUserList = ssoUserService.getUserById(userIds);
					if (CollectionUtils.isNotEmpty(ssoUserList)) {
						OrgInfo orgInfo = orgService.selectByOrgid(ssoUserList.get(0).getOrgCode());
						if (null != orgInfo) {
							userOperatorLogDTO.setOrgName(orgInfo.getOrgName());
						}
					}
				}
            }
        }
		return list;
	}

    @Override
    public StsTokenDto getStsToken(String bucketName) {
        StsTokenDto dto = new StsTokenDto();
        // RoleSessionName 是临时Token的会话名称，自己指定用于标识你的用户，主要用于审计，或者用于区分Token颁发给谁
        // 但是注意RoleSessionName的长度和规则，不要有空格，只能有'-' '.' '@' 字母和数字等字符
        // 具体规则请参考API文档中的格式要求
        String policy = "{\n" + "    \"Version\": \"1\", \n" + "    \"Statement\": [\n" + "        {\n"
                + "            \"Action\": [\n" + "                \"oss:*\"\n" + "            ], \n"
                + "            \"Resource\": [\n" + "                \"acs:oss:*:*:*\" \n" + "            ], \n"
                + "            \"Effect\": \"Allow\"\n" + "        }\n" + "    ]\n" + "}";
        // 向OSS获取临时凭证
        try {
            AssumeRoleResponse assumeRoleResponse = assumeRole(ossConfig.getALI_ACCESS_ID(),
                    ossConfig.getALI_ACCESS_KEY(),
                    ossConfig.getROLE_ARN(),
                    ossConfig.getROLE_SESSION_NAME(), policy, ProtocolType.HTTPS);
            dto.setAccessKeyId(assumeRoleResponse.getCredentials().getAccessKeyId());
            dto.setAccessKeySecret(assumeRoleResponse.getCredentials().getAccessKeySecret());
            dto.setStsToken(assumeRoleResponse.getCredentials().getSecurityToken());
            dto.setBucketName(bucketName);
            dto.setEndpoint(ossConfig.getOSS_ENDPOINT());
            dto.setEnv(ossConfig.getENV());
        } catch (ClientException e) {
            logger.error("获取STS临时凭证失败", e);
            throw  new MemberException(-1, "获取sts临时token失败");
        }
        return dto;
    }

    @Override
    public String getStsUrlTokenGet(String bucketName, String objectName) {
        URL url = ComUtil.getStsUrlTokenGet(bucketName, objectName);
        return url.toString();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateUserFileNo(UpdateFileNoInput input) throws AuthenticationException {
        DrivingLicenseReviewResult result = memberReviewService.updateUserFileNo(input);
        if(result.getCode() != 0) {
            logger.error("补全档案编号失败，input={}, result={}", JSON.toJSONString(input), JSON.toJSONString(result));
            throw new AuthenticationException(result.getCode(), result.getMessage());
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitUserIDCardPic(SubmitUserIdCardPicInput picInput) throws AuthenticationException {
        HidLog.membership(LogPoint.SUBMIT_USER_CARD_FACE_INFO, "提交用户认证信息开始", picInput.getAuthId(), true);
        /**
         * 1.查询用户信息
         */
        MembershipBasicInfo membershipInfo = this.getUserBasicInfo(picInput.getAuthId(),(short)0);
        if (membershipInfo == null){
            HidLog.membership(LogPoint.SUBMIT_USER_CARD_FACE_INFO, StatusCode.USER_INFO_NO_EXIST.getMsg(), picInput.getAuthId(), false);
            throw new AuthenticationException(StatusCode.USER_INFO_NO_EXIST);
        }

        if (StringUtils.isNotBlank(picInput.getPassportNo())){
            List<MembershipBaseInfo> membershipBaseInfos = membershipInfoMapper.checkPassPortOccupied(picInput.getAuthId(),picInput.getPassportNo(),0);
            if (CollectionUtils.isNotEmpty(membershipBaseInfos)){
                HidLog.membership(LogPoint.SUBMIT_USER_CARD_FACE_INFO, StatusCode.PASS_PORT_OCCUPIED.getMsg(), picInput.getAuthId(), false);
                throw new AuthenticationException(StatusCode.PASS_PORT_OCCUPIED);
            }
        }
        int reviewStatus = membershipInfo.getReviewStatus().intValue();
        /**
         * 2.处理图片url
         */
        String idCardPicUrl = ComUtil.splitPicUrl(picInput.getIdcardPicUrl());
        String holdIdCardPic = ComUtil.splitPicUrl(picInput.getHoldIdcardPicUrl());
        String facePicUrl = ComUtil.splitPicUrl(picInput.getFacePicUrl());
        /**
         * 3.校验手持和人脸相似度
         */
        this.getFaceContrastResult(ComUtil.subString(holdIdCardPic),ComUtil.subString(facePicUrl),picInput.getAuthId());

        /**
         * 4.更新用户信息
         * 认证状态修改为已认证
         */
        MembershipInfo entity = new MembershipInfo();
        if(reviewStatus != 1){
            entity.setReviewStatus((short)0);
        }else {
            memberReviewService.sendReviewSuccessMesage(picInput.getAuthId(), membershipInfo.getMobilePhone(),
                    reviewStatus, 1, membershipInfo.getMobilePhone());
        }
        /**
         * 5.若用户为外籍，选择的证件类型为 3：香港澳门通行证 4 台湾通行证，则更新national为港澳台
         */
        if (!membershipInfo.getNational().contains(BussinessConstants.CHINA_NATIONAL) && Arrays.asList(3,4).contains(picInput.getIdType())) {
            entity.setNational(BussinessConstants.NATIONAL_HK_MACAO_TAIWAN);
        }
        entity.setIdcardPicUrl(idCardPicUrl);
        entity.setHoldIdcardPicUrl(holdIdCardPic);
        entity.setAuthenticationStatus(2);
        entity.setReviewMode(1);
        entity.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
        entity.setUpdatedUser(picInput.getAuthId());
        entity.setAppReviewTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE4));
        entity.setAuthId(picInput.getAuthId());
        entity.setIdType(picInput.getIdType());
        entity.setPassportNo(picInput.getPassportNo());
        entity.setFaceRecognitionImgUrl(facePicUrl);
        int i = membershipInfoMapper.updateUserIdCardPic(entity);
        if(i <= 0){
            HidLog.membership(LogPoint.SUBMIT_USER_CARD_FACE_INFO, StatusCode.SUBMIT_FAILURE.getMsg(), picInput.getAuthId(), false);
            throw new AuthenticationException(StatusCode.SUBMIT_FAILURE);
        }
        /**
         * 6.保存操作日志
         */
        UserOperationLogInput userOperationLogInput = new UserOperationLogInput();
        userOperationLogInput.setOperationType(1L);
        userOperationLogInput.setOperationContent("提交身份证图片/护照图片");
        userOperationLogInput.setOperationTime(new Date());
        userOperationLogInput.setOperator(picInput.getAuthId());
        userOperationLogInput.setRefKey1(picInput.getAppKey());
        userOperationLogInput.setAuthId(picInput.getAuthId());
        this.saveUserOperationLog(userOperationLogInput);
        ComUtil.insertOperatorLog("提交人工审核", picInput.getAuthId(), "", picInput.getAuthId(), userOperatorLogMapper);
        HidLog.membership(LogPoint.SUBMIT_USER_CARD_FACE_INFO, "提交成功，等待人工审核", picInput.getAuthId(), true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> faceAuthentication(String name, String idCardNumber, byte[] fileByte, String liveDelta,String authId) throws AuthenticationException {
        return this.faceAuthentication(name, idCardNumber, fileByte, liveDelta,authId, authId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> faceAuthentication(String name, String idCardNumber, byte[] fileByte, String liveDelta,String authId, String appKey) throws AuthenticationException {
        return this.faceAuthentication(name, idCardNumber, fileByte, liveDelta, authId, appKey, "");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, String> faceAuthentication(String name, String idCardNumber, byte[] fileByte, String liveDelta, String authId, String appKey, String imageUrl) throws AuthenticationException {
        //0百度  1商汤，默认使用百度api
        String faceAuthenticationApi = JedisUtil.get("faceAuthenticationApi");
        if (StringUtils.isNotBlank(faceAuthenticationApi) && "1".equals(faceAuthenticationApi)) {
            return sensetimeFaceAuthentication(name,idCardNumber,fileByte,liveDelta,authId,appKey);
        } else {
            return baiDuFaceAuthentication(name, idCardNumber, imageUrl, authId, appKey);
        }
    }

    @Override
    public Map<String, String> sensetimeFaceAuthentication(String name, String idCardNumber, byte[] fileByte, String liveDelta, String authId, String appKey) throws AuthenticationException {
        HidLog.membership(LogPoint.USER_AUTH_SIAC, "用户认证(商汤api)开始", authId, true);
        File imageFile = new File("/data/" + System.currentTimeMillis() + ".png");
        try {
            FileUtils.writeByteArrayToFile(imageFile, fileByte);
        } catch (IOException e) {
            logger.error("人脸数组转文件失败", e);
            HidLog.membership(LogPoint.USER_AUTH_SIAC, "人脸数组转文件失败", authId, false);
            throw new AuthenticationException(StatusCode.UPDATE_FAILED);
        }
        Map<String, String> resultMap = new HashMap<>(2);
        String faceAuthenticationResult = StringUtils.EMPTY;
        String faceAuthenticationSimilarity = StringUtils.EMPTY;
        //请求头
        Map<String,Object> headers = new HashMap<>(1);
        String accessToken = null;
        try {
            accessToken = GenerateString.genHeaderParam(GenerateString.id,GenerateString.secret);
        } catch (SignatureException e) {
            e.printStackTrace();
        }
        if (StringUtils.isBlank(accessToken)) {
            HidLog.membership(LogPoint.USER_AUTH_SIAC, StatusCode.GET_ACCESS_TOKEN_ERROR.getMsg(), authId, false);
            throw new AuthenticationException(StatusCode.GET_ACCESS_TOKEN_ERROR);
        }

        headers.put("Authorization", accessToken);
        //参数
        Map<String,Object> params = new HashMap<>(4);
        params.put("name",name);
        params.put("idnumber",idCardNumber);
        params.put("auto_rotate",true);
        try {
            String response = HttpClientUtils.httpPostRequest(BussinessConstants.Sensetime_AUTHENTICATION,headers, params,imageFile,"image_file");
            logger.info("请求商汤人脸识别返回:" + response);
            HidLog.membership(LogPoint.USER_AUTH_SIAC, "请求商汤人脸识别api返回:" + response, authId, true);
            JSONObject jsonObject = JSON.parseObject(response);
            Integer code = jsonObject.getInteger("code");
            Float verificationScore = 0F;
            if (jsonObject.getFloat("verification_score") != null){
                verificationScore = new BigDecimal(jsonObject.getFloat("verification_score")).multiply(new BigDecimal(100)).floatValue();
            }
            //人脸相似度大于60
            if(code != null && code.equals(1000) && verificationScore >= 60F){
                faceAuthenticationResult = "0";
                faceAuthenticationSimilarity = verificationScore.toString();
            }else if(code != null && code.equals(3005)){
                //身份证号和姓名是正常的，但公安那边的数据源没有他的照片
                faceAuthenticationResult = "2";
            } else {
                //上汽身份核查服务失败(name idCard不符)
                faceAuthenticationResult = "1";
            }

            //保存操作日志
            UserOperationLogInput userOperationLogInput = new UserOperationLogInput();
            userOperationLogInput.setOperationType(20L);
            userOperationLogInput.setOperationContent(response);
            userOperationLogInput.setOperationTime(new Date());
            userOperationLogInput.setOperator(authId);
            userOperationLogInput.setRefKey1(appKey);
            userOperationLogInput.setAuthId(authId);
            userOperationLogInput.setRefKey2(authId);
            this.saveUserOperationLog(userOperationLogInput);
            logger.warn("会员"+idCardNumber+"人脸识别结果={}",response);
        } catch (Exception e) {
            logger.error("人脸识别失败 "+e.getMessage(),e);
            HidLog.membership(LogPoint.USER_AUTH_SIAC, "人脸识别失败 "+e.getMessage(), authId, false);
            throw new AuthenticationException(StatusCode.FACE_FAILURE);
        }
        resultMap.put("faceAuthenticationResult", faceAuthenticationResult);
        resultMap.put("faceAuthenticationSimilarity", faceAuthenticationSimilarity);
        FileUtils.deleteQuietly(imageFile);
        HidLog.membership(LogPoint.USER_AUTH_SIAC, "用户认证(商汤api)结束", authId, true);
        return resultMap;
    }

    @Autowired
    MemberFaceVerifyService memberFaceVerifyService;
    @Override
    public Map<String, String> baiDuFaceAuthentication(String name, String idCardNumber, String imageUrl, String authId, String appKey) throws AuthenticationException {
        String sensitiveImgUrl = ComUtil.getFileFullPath(imageUrl);
        return baiDuFaceAuthentication2(name,idCardNumber,sensitiveImgUrl,1,authId,appKey);
    }

    @Override
    public Map<String, String> baiDuFaceAuthentication2(String name, String idCardNumber, String imageSrc, int imageType, String authId, String appKey) throws AuthenticationException {
        HidLog.membership(LogPoint.USER_AUTH_BAI_DU, "用户认证(百度api)开始", authId, true);
        Map<String, String> resultMap = new HashMap<>(2);
        String faceAuthenticationResult = StringUtils.EMPTY;
        String faceAuthenticationSimilarity = StringUtils.EMPTY;
        try {
            FacePersonVerifyInput request = new FacePersonVerifyInput();
            request.setImage(imageSrc);
            request.setImageType(imageType != 1 ? 0 : 1);
            request.setAuthId(authId);
            request.setIdCardNo(idCardNumber);
            request.setName(name);
            logger.info("请求百度人脸识别请求参数:" + JSON.toJSONString(request));
            FacePersonVerifyResult facePersonVerifyResult = memberFaceVerifyService.personVerify(request);
            logger.info("请求百度人脸识别返回:" + JSON.toJSONString(facePersonVerifyResult));
            HidLog.membership(LogPoint.USER_AUTH_BAI_DU, "请求百度人脸识别返回:" + JSON.toJSONString(facePersonVerifyResult), authId, true);
            int code = facePersonVerifyResult.getCode();
            double verificationScore = facePersonVerifyResult.getScore()==null?0:facePersonVerifyResult.getScore();
            //人脸相似度大于60
            if(code == 0 && verificationScore >= 80){
                faceAuthenticationResult = "0";
                faceAuthenticationSimilarity = String.valueOf(verificationScore);
            } else {
                //身份核查服务失败(name idCard不符)
                faceAuthenticationResult = "1";
            }
            //保存操作日志
            UserOperationLogInput userOperationLogInput = new UserOperationLogInput();
            userOperationLogInput.setOperationType(20L);
            userOperationLogInput.setOperationContent(JSON.toJSONString(facePersonVerifyResult));
            userOperationLogInput.setOperationTime(new Date());
            userOperationLogInput.setOperator(authId);
            userOperationLogInput.setRefKey1(appKey);
            userOperationLogInput.setAuthId(authId);
            userOperationLogInput.setRefKey2(authId);
            this.saveUserOperationLog(userOperationLogInput);
            logger.warn("会员"+idCardNumber+"人脸识别结果={}",JSON.toJSONString(facePersonVerifyResult));
        } catch (Exception e) {
            logger.error("人脸识别失败 "+e.getMessage(),e);
            HidLog.membership(LogPoint.USER_AUTH_BAI_DU, "人脸识别失败 "+e.getMessage(), authId, false);
            throw new AuthenticationException(StatusCode.FACE_FAILURE);
        }
        resultMap.put("faceAuthenticationResult", faceAuthenticationResult);
        resultMap.put("faceAuthenticationSimilarity", faceAuthenticationSimilarity);
        HidLog.membership(LogPoint.USER_AUTH_BAI_DU, "用户认证(商汤api)结束", authId, true);
        return resultMap;
    }

    @Override
    public Map<String, String> baiDuFaceAuthenticationSec(String name, String idCardNumber, String image, Integer appType, String authId, String appKey) throws AuthenticationException {
        return  baiDuFaceAuthenticationSec2(name,idCardNumber,image,FacePersonVerifyInput.BASE64,appType,authId,appKey);
    }

    @Override
    public Map<String, String> baiDuFaceAuthenticationSec2(String name, String idCardNumber, String imageSrc, int imageType, Integer appType, String authId, String appKey) throws AuthenticationException {
        FacePersonVerifyInput request = new FacePersonVerifyInput();
        request.setImage(imageSrc);
        request.setImageType(imageType != 1 ? 0 : 1);
        request.setAuthId(authId);
        request.setIdCardNo(idCardNumber);
        request.setName(name);
        request.setApp(appType);
        request.setLivenessControl("NORMAL");
        request.setSpoofingControl("NORMAL");

        String faceAuthenticationResult;
        String faceAuthenticationSimilarity = StringUtils.EMPTY;
        try {
            FacePersonVerifyResult facePersonVerifyResult = memberFaceVerifyService.personVerifySec(request);
            logger.warn("会员{}人脸识别结果={}", idCardNumber, JSON.toJSONString(facePersonVerifyResult));

            int code = facePersonVerifyResult.getCode();
            double verificationScore = facePersonVerifyResult.getScore() == null ? 0 : facePersonVerifyResult.getScore();

            //人脸相似度大于80
            if (code == 0 && verificationScore >= 80) {
                faceAuthenticationResult = "0";
                faceAuthenticationSimilarity = String.valueOf(verificationScore);
            } else {
                //身份核查服务失败(name idCard不符)
                faceAuthenticationResult = "1";
            }
            //保存操作日志
            UserOperationLogInput userOperationLogInput = new UserOperationLogInput();
            userOperationLogInput.setOperationType(20L);
            userOperationLogInput.setOperationContent(JSON.toJSONString(facePersonVerifyResult));
            userOperationLogInput.setOperationTime(new Date());
            userOperationLogInput.setOperator(authId);
            userOperationLogInput.setRefKey1(appKey);
            userOperationLogInput.setAuthId(authId);
            userOperationLogInput.setRefKey2(authId);
            this.saveUserOperationLog(userOperationLogInput);
        } catch (Exception e) {
            logger.error("人脸识别失败 " + e.getMessage(), e);
            HidLog.membership(LogPoint.USER_AUTH_BAI_DU, "人脸识别失败 " + e.getMessage(), authId, false);
            throw new AuthenticationException(StatusCode.FACE_FAILURE);
        }

        Map<String, String> resultMap = new HashMap<>(2);
        resultMap.put("faceAuthenticationResult", faceAuthenticationResult);
        resultMap.put("faceAuthenticationSimilarity", faceAuthenticationSimilarity);
        HidLog.membership(LogPoint.USER_AUTH_BAI_DU, "用户认证(百度api增强版) 结束", authId, true);
        return resultMap;
    }

    @Override
    public FacePersonVerifyResult baiduEnhancerFaceMatch(FaceMatchInput input) {
        return memberFaceVerifyService.enhancerFaceMatch(input);
    }


    @Override
    public FacePersonVerifyResult baiduFaceMatch(FaceMatchInput input) {
        return memberFaceVerifyService.faceMatch(input);
    }

    @Override
    public FaceLiveSessionCode getFaceLiveSessionCode() {
        return memberFaceVerifyService.getFaceLiveSessionCode();
    }

    @Override
    public FaceLiveVerifyResult faceLiveVerify(FaceLiveVerifyInput input) {
        return memberFaceVerifyService.faceLiveVerify(input);
    }

    public static final List<String> NOT_FACE_IMG_ORDER = Arrays.asList("zhifubao_02");

    @Override
    public void submitFaceRecognitionPic(SubmitFacePicInput input) throws AuthenticationException {
        HidLog.membership(LogPoint.SUBMIT_USER_FACE_INFO_NATIVE, "提交用户认证信息开始", input.getAuthId(), true);
        MembershipBasicInfo membershipInfo = this.getUserBasicInfo(input.getAuthId(),(short)0);

        if (membershipInfo == null){
            HidLog.membership(LogPoint.SUBMIT_USER_FACE_INFO_NATIVE, StatusCode.USER_INFO_NO_EXIST.getMsg(), input.getAuthId(), false);
            throw new AuthenticationException(StatusCode.USER_INFO_NO_EXIST);
        }

        /**
         * 变更点：是否刷脸标记，当前直接使用新审核状态的是否刷脸标记。
         * 会导致老用户补全身份证件时需要重新刷脸
         * 改为优先使用新身份证认证中的刷脸标记，若无更新则使用
         */
        boolean faceAuthed = identityCertService.getFaceRecFlag(membershipInfo);
        if (faceAuthed && StringUtils.isNotBlank(input.getAppkey()) && !NOT_FACE_IMG_ORDER.contains(input.getAppkey())) {
            HidLog.membership(LogPoint.SUBMIT_USER_FACE_INFO_NATIVE, StatusCode.SUBMIT_FAILURE.getMsg(), input.getAuthId(), false);
            return;
        }

        //老用户标志
        int reviewStatus = membershipInfo.getReviewStatus().intValue();
        String faceAuthenticationResult = null;
        String faceAuthenticationSimilarity = StringUtils.EMPTY;

        try {
            //TODO 证件id亦可以取新身份证件表的身份证编号， 此处会员表的name
            String idcardNum  = StringUtils.isNotBlank(membershipInfo.getIdCardNumber())?membershipInfo.getIdCardNumber():membershipInfo.getDriverCode();
            Map<String, String> faceResultMap = this.faceAuthentication(membershipInfo.getName(),idcardNum,input.getFile(),input.getLiveDelta(),input.getAuthId(), input.getAppkey(), input.getFaceRecognitionPic());
            faceAuthenticationResult = faceResultMap.get("faceAuthenticationResult");
            faceAuthenticationSimilarity = faceResultMap.get("faceAuthenticationSimilarity");
        }catch (Exception e){
            HidLog.membership(LogPoint.SUBMIT_USER_FACE_INFO_NATIVE, "人脸识别失败", input.getAuthId(), false);
            throw new AuthenticationException(-1,"人脸识别失败");
        }


        //存入人脸相似度
        if (StringUtils.isNotBlank(faceAuthenticationSimilarity)) {
            MmpUserTag mmpUserTag = new MmpUserTag();
            mmpUserTag.setSpare6(faceAuthenticationSimilarity);
            mmpUserTag.setUpdateTime(new Date());
            mmpUserTag.setUpdateOperName(input.getAuthId());
            mmpUserTag.setAuthId(input.getAuthId());
            mmpUserTagMapper.updateFaceSimilarity(mmpUserTag);
        }
        if ("0".equals(faceAuthenticationResult) || "2".equals(faceAuthenticationResult)) {
            boolean mannualFlag = false;
            //更新会员审核状态
            if("2".equals(faceAuthenticationResult)){
                //特殊处理此类数据，当做手动输入待审核
                membershipInfo.setDriverLicenseInputType(2);
                mannualFlag = true;
            }
            /**
             * TODO 完善变更刷脸通过后的认证逻辑
             */
            String faceUrl = ComUtil.splitPicUrl(input.getFaceRecognitionPic());
            identityCertService.submitFaceImgToReview(membershipInfo.getMid(), mannualFlag, faceUrl, input.getAppkey(), membershipInfo);
//            reviewStatus = updateAutoReviewStatus(input.getAuthId(),input.getFaceRecognitionPic(),membershipInfo.getDriverLicenseInputType(),
//                    membershipInfo.getMobilePhone(),reviewStatus,input.getAppkey(),null, input.getRefKey2());
//            //新老用户发放优惠券
//            memberReviewService.sendReviewSuccessMesage(input.getAuthId(), membershipInfo.getMobilePhone(), reviewStatus, 1, membershipInfo.getMobilePhone());

            logger.info("会员authId=" + input.getAuthId() + "人脸识别成功");
            HidLog.membership(LogPoint.SUBMIT_USER_FACE_INFO_NATIVE, "人脸识别成功", input.getAuthId(), true);
        } else {
            //保存操作日志
            UserOperationLogInput userOperationLogInput = new UserOperationLogInput();
            userOperationLogInput.setOperationType(17L);
            userOperationLogInput.setOperationContent("人脸认证失败");
            userOperationLogInput.setOperationTime(new Date());
            userOperationLogInput.setOperator(input.getAuthId());
            userOperationLogInput.setRefKey1(input.getAppkey());
            userOperationLogInput.setAuthId(input.getAuthId());
            userOperationLogInput.setRefKey2(input.getRefKey2());
            this.saveUserOperationLog(userOperationLogInput);
            HidLog.membership(LogPoint.MEMBER_AUTH_FAIL, "自动认证不通过", input.getAuthId());
            HidLog.membership(LogPoint.SUBMIT_USER_FACE_INFO_NATIVE, "人脸识别失败,请确保是本人操作", input.getAuthId(), false);
            throw new AuthenticationException(2,"人脸识别失败,请确保是本人操作");
        }
        HidLog.membership(LogPoint.SUBMIT_USER_FACE_INFO_NATIVE, "提交用户认证信息结束", input.getAuthId(), true);
    }

    @Override
    public void submitOnlyFaceRecognitionPic(SubmitFaceInfoInput input) throws AuthenticationException {
        /**
         * 1.查询用户信息
         */
        MembershipBasicInfo membershipInfo = this.getUserBasicInfo(input.getAuthId(),(short)0);
        if (membershipInfo == null){
            throw new AuthenticationException(StatusCode.USER_INFO_NO_EXIST);
        }
        /**
         * 2.判断用户认证状态
         */
        boolean faceAuthed = identityCertService.getFaceRecFlag(membershipInfo);
        if (faceAuthed && StringUtils.isNotBlank(input.getAppKey()) && !NOT_FACE_IMG_ORDER.contains(input.getAppKey())){
            logger.info("submitOnlyFaceRecognitionPic: 人脸已认证不可重复操作，faceAuthed={}", faceAuthed);
            throw new AuthenticationException(StatusCode.SUBMIT_FAILURE);
        }
        int reviewStatus = membershipInfo.getReviewStatus().intValue();
        String faceAuthenticationResult = input.getFaceAuthenticationResult();
        String faceAuthenticationSimilarity = input.getFaceAuthenticationSimilarity();
        /**
         * 3.存入人脸相似度
         */
        if (StringUtils.isNotBlank(faceAuthenticationSimilarity)) {
            MmpUserTag mmpUserTag = new MmpUserTag();
            mmpUserTag.setSpare6(faceAuthenticationSimilarity);
            mmpUserTag.setUpdateOperName(input.getAuthId());
            mmpUserTag.setUpdateTime(new Date());
            mmpUserTag.setAuthId(input.getAuthId());
            mmpUserTagMapper.updateFaceSimilarity(mmpUserTag);
        }
        /**
         * 4.若成功则更新用户状态，发放新老用户优惠券
         */
        logger.warn("人脸： faceAuthenticationResult={}", faceAuthenticationResult);
        if ("0".equals(faceAuthenticationResult) || "2".equals(faceAuthenticationResult)) {
            boolean mannualFlag = false;
            if("2".equals(faceAuthenticationResult)){
                //特殊处理此类数据，当做手动输入待审核
                membershipInfo.setDriverLicenseInputType(2);
                mannualFlag = true;
                logger.warn("人脸： faceAuthenticationResult={} 走人工, ", faceAuthenticationResult);
            }
            /**
             * TODO 完善变更刷脸通过后的认证逻辑
             */
            String faceUrl = ComUtil.splitPicUrl(input.getFaceRecognitionPic());
            identityCertService.submitFaceImgToReview(membershipInfo.getMid(), mannualFlag, faceUrl, input.getAppKey(), membershipInfo);
//            reviewStatus = updateAutoReviewStatus(input.getAuthId(),input.getFaceRecognitionPic(),membershipInfo.getDriverLicenseInputType(),
//                    membershipInfo.getMobilePhone(), reviewStatus,input.getAppKey(),null, input.getRefKey2());
//            memberReviewService.sendReviewSuccessMesage(input.getAuthId(), membershipInfo.getMobilePhone(), reviewStatus, 1, membershipInfo.getMobilePhone());
            logger.info("会员authId=" + input.getAuthId() + "人脸识别成功");
        } else {
            /**
             * 5.若不成功，则写入日志
             */
            UserOperationLogInput userOperationLogInput = new UserOperationLogInput();
            userOperationLogInput.setOperationType(17L);
            userOperationLogInput.setOperationContent("人脸认证失败");
            userOperationLogInput.setOperationTime(new Date());
            userOperationLogInput.setOperator(input.getAuthId());
            userOperationLogInput.setRefKey1(input.getAppKey());
            userOperationLogInput.setAuthId(input.getAuthId());
            userOperationLogInput.setRefKey2(input.getRefKey2());
            this.saveUserOperationLog(userOperationLogInput);
            HidLog.membership(LogPoint.MEMBER_AUTH_FAIL, "自动认证不通过", input.getAuthId());

            /**
             * 增加人脸认证不通过新日志，冗余存储
             */
            userOperationLogInput.setOperationType(MemOperateTypeEnum.IDCARD_AUTO_AUDIT_NOT_PASS.getCode());
            this.saveUserOperationLog(userOperationLogInput);
            throw new AuthenticationException(2,"人脸识别失败,请确保是本人操作");
        }
    }

    /**
     * 存人脸相似度
     *
     * 成功：更新db，人脸信息
     * 失败：写入日志
     *
     *
     * @param input
     * @throws AuthenticationException
     */
    @Override
    public void ofcSubmitOnlyFaceRecognitionPic(SubmitFaceInfoInput input) throws AuthenticationException {
        logger.info("履约提交人脸图片开始，input={}", JSON.toJSONString(input));
        /**
         * 1.查询用户信息
         */
        MembershipBasicInfo membershipInfo = this.getUserBasicInfo(input.getAuthId(), (short) input.getMembershipType());
        if (membershipInfo == null) {
            throw new AuthenticationException(StatusCode.USER_INFO_NO_EXIST);
        }

        String faceAuthenticationResult = input.getFaceAuthenticationResult();
        String faceAuthenticationSimilarity = input.getFaceAuthenticationSimilarity();
        /**
         * 3.存入人脸相似度
         */
        if (StringUtils.isNotBlank(faceAuthenticationSimilarity)) {
            MmpUserTag mmpUserTag = new MmpUserTag();
            mmpUserTag.setSpare6(faceAuthenticationSimilarity);
            mmpUserTag.setUpdateOperName(input.getAuthId());
            mmpUserTag.setUpdateTime(new Date());
            mmpUserTag.setAuthId(input.getAuthId());
            mmpUserTagMapper.updateFaceSimilarity(mmpUserTag);
        }

        // 履约可以强制 刷脸成功
        if (input.getOrigin() == 1 && input.getForceSuccess() == 1) {
            faceAuthenticationResult = "0";
        }
        /**
         * 4.若成功则更新用户状态，发放新老用户优惠券
         */
        if ("0".equals(faceAuthenticationResult)) {
            String faceUrl = ComUtil.splitPicUrl(input.getFaceRecognitionPic());
            identityCertService.ofcSubmitFaceImgToReview(membershipInfo.getMid(), faceUrl, input.getAppKey(), membershipInfo,input.getForceSuccess());
            logger.info("履约会员authId=" + input.getAuthId() + "人脸识别成功");
        } else {
            /**
             * 5.若不成功，则写入日志
             */
            UserOperationLogInput userOperationLogInput = new UserOperationLogInput();
            userOperationLogInput.setOperationType(17L);
            userOperationLogInput.setOperationContent("履约-人脸认证失败");
            userOperationLogInput.setOperationTime(new Date());
            userOperationLogInput.setOperator(input.getAuthId());
            userOperationLogInput.setRefKey1(input.getAppKey());
            userOperationLogInput.setAuthId(input.getAuthId());
            userOperationLogInput.setRefKey2(input.getRefKey2());
            this.saveUserOperationLog(userOperationLogInput);
            HidLog.membership(LogPoint.MEMBER_AUTH_FAIL, "自动认证不通过", input.getAuthId());

            /**
             * 增加人脸认证不通过新日志，冗余存储
             */
            userOperationLogInput.setOperationType(MemOperateTypeEnum.IDCARD_AUTO_AUDIT_NOT_PASS.getCode());
            this.saveUserOperationLog(userOperationLogInput);
            throw new AuthenticationException(2, "人脸识别失败,请确保是本人操作");
        }
    }

    @Override
    public void updateFaceRecognitionPic(UpdateFacePicInput input) throws AuthenticationException {
        //查询会员信息
        MembershipBasicInfo membershipInfo = this.getUserBasicInfo(input.getAuthId(),(short)0);
        if (membershipInfo == null){
            throw new AuthenticationException(StatusCode.USER_INFO_NO_EXIST);
        }
        //老用户标志
        String faceAuthenticationResult = null;
        String faceAuthenticationSimilarity = StringUtils.EMPTY;
        try {
            String idCardNum = StringUtils.isNotBlank(membershipInfo.getDriverCode()) ? membershipInfo.getDriverCode() : membershipInfo.getIdCardNumber();
            Map<String, String> faceResultMap = faceAuthentication(membershipInfo.getName(),idCardNum,input.getFile(),input.getLiveDelta(),input.getAuthId(), input.getAppkey(), input.getFaceRecognitionPic());
            faceAuthenticationResult = faceResultMap.get("faceAuthenticationResult");
            faceAuthenticationSimilarity = faceResultMap.get("faceAuthenticationSimilarity");
        } catch (Exception e) {
            logger.warn(e.getMessage(), e);
            throw new AuthenticationException(StatusCode.FACE_FAILURE);
        }
        if ("0".equals(faceAuthenticationResult) || "2".equals(faceAuthenticationResult)) {
            //TODO 2022.9 更新人脸图片-->此处是否需要变更
            MembershipInfo entity = new MembershipInfo();
            entity.setFaceRecognitionImgUrl(ComUtil.splitPicUrl(input.getFaceRecognitionPic()));
            entity.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
            entity.setUpdatedUser(input.getAuthId());
            entity.setAuthId(input.getAuthId());
            entity.setAppKey(input.getAppkey());
            int i = membershipInfoMapper.updateFaceRecognitionImgUrl(entity);
            if (i<= 0) {
                throw new AuthenticationException(StatusCode.FACE_FAILURE);
            }
            logger.info("会员authId=" + input.getAuthId() + "人脸识别成功");
        } else {
            throw new AuthenticationException(StatusCode.VERIFICATION_SCORE);
        }

        if(input.getOpenDoor() !=null && !input.getOpenDoor()){
            return;
        }
        //查询当前订单
        QueryUserCurrentOrderInput orderInput = new QueryUserCurrentOrderInput();
        orderInput.setAuthId(input.getAuthId());
        List<OrderInfoDto> orderInfoList = orderService.queryUserEvcardCurrentOrder(orderInput);
        if (CollectionUtils.isNotEmpty(orderInfoList)) {
            OrderInfoDto orderInfo = orderInfoList.get(0);

            SaveOrderFaceContrastRecordDto faceContrastResult = new SaveOrderFaceContrastRecordDto();
            faceContrastResult.setIsSuccess(1);
            faceContrastResult.setOrderSeq(orderInfo.getOrderSeq());
            faceContrastResult.setOpenDoorFaceImage(ComUtil.splitPicUrl(input.getFaceRecognitionPic()));
            faceContrastResult.setCreateTime(new Date());
            faceContrastResult.setCreateOperName(input.getAuthId());
            faceContrastResult.setMiscDesc("百度api");
            faceContrastResult.setFaceOrigin(input.getFaceOrigin());
            if (StringUtils.isNotBlank(faceAuthenticationSimilarity)) {
                faceContrastResult.setConfidence(Double.valueOf(faceAuthenticationSimilarity));
            }
            orderService.saveOrderFaceContrastRecord(faceContrastResult);
        }

    }

    @Override
    public void orderVehicleUpdateFace(UpdateFacePicInput input) throws AuthenticationException {
        //查询会员信息
        MembershipBasicInfo membershipInfo = this.getUserBasicInfo(input.getAuthId(),(short)0);
        if (membershipInfo == null){
            throw new AuthenticationException(StatusCode.USER_INFO_NO_EXIST);
        }

        String national = membershipInfo.getNational();
        if (StringUtils.isNotEmpty(national) && national.contains(BussinessConstants.CHINA_NATIONAL)){
            //本籍对人脸进行核身
            input.setOpenDoor(false);
            updateFaceRecognitionPic(input);
        }else {
            //外籍与手持做对比
            String holdIdImg = membershipInfo.getHoldIdcardPicUrl();
            if(StringUtils.isBlank(holdIdImg)){
                throw new AuthenticationException(StatusCode.FACE_IMAGE_EMPTY);
            }
            String facePicUrl = ComUtil.splitPicUrl(input.getFaceRecognitionPic());
            getFaceContrastResult(ComUtil.subString(holdIdImg),ComUtil.subString(facePicUrl),input.getAuthId());
        }
    }


    @Override
    public DriverLicenseQueryResultDTO queryDriverLicenseDetail(String authId, UpdateUserDto operator) {
        if(operator == null) {
            operator = UpdateUserDto.buildUserOperator(authId);
        }
        /**
         * 1.查询用户信息
         */
        MembershipInfoWithBLOBs membershipInfo = membershipInfoMapper.selectByAuthId(authId, 0);
        if (membershipInfo == null) {
            logger.error("通过供应商查询驾照详细信息：用户信息不存在， authId={}", authId);
            return null;
        }
        /**
         * 2. 调用供应商接口，查询驾照号详情
         */
        String driverCode = membershipInfo.getDriverCode();
        String name = membershipInfo.getName();
        if(StringUtils.isBlank(driverCode) || StringUtils.isBlank(name)){
            logger.error("通过供应商查询驾照详细信息：驾照号或姓名不完整， authId={}", authId);
            return null;
        }
        DriverLicenseAuthResultDTO authResultDTO = getLastDriverLicenseAuthResult(authId);
        if(authResultDTO == null) {
            logger.error("通过供应商查询驾照详细信息：未进行三要素认证，暂不查询驾照详情， authId={}", authId);
            return null;
        }
        DriverLicenseQueryResultDTO result = licenseAuthenticateService.queryDriverLicenseInfo(authId,
                driverCode.trim(), name.trim(), 1);
        if(result != null) {
            result.setRecordId(authResultDTO.getId());
            result.setCreateTime(new Date());
            result.setCreateOperName(operator.getUserName());
            result.setCreateOperId(operator.getUserId());
            saveDriverLicenseElementsAuthenticateLog(result);
        }
        return result;
    }

    @Override
    public void driverLicenseElementsAuthenticate(String authId, UpdateUserDto operator) {
        memberReviewService.drivingLicenseAuthenticate(authId, operator);
    }

    @Override
    public void driverLicenseElementsAuthenticate(String authId) {
        /**
         * 版本 4.11 下线驾照三要素认证功能
         */
        logger.warn("驾照三要素功能已下线， authId={}", authId);
        UpdateUserDto operator = new UpdateUserDto();
        operator.setUserId(-1L);
        operator.setUserName(authId);
        operator.setUpdateTime(new Date());
        driverLicenseElementsAuthenticate(authId, operator);
    }

    @Override
    public void saveDriverLicenseElementsAuthenticateRecord(SaveDriverElementsAuthenticateInput input) throws AuthenticationException {
        memberReviewService.saveDriverLicenseElementsAuthenticateRecord(input);
    }

    @Override
    public void saveDriverLicenseElementsAuthenticateLog(SaveDriverElementsAuthenticateLogInput input) {
        memberReviewService.saveDriverLicenseElementsAuthenticateLog(input);
    }

    @Override
    public void submitDrivingLicenseInfo(String authId, String appKey, SubmitDrivingLicenseInput input) throws AuthenticationException {
        memberReviewService.submitDrivingLicenseInfo(authId, appKey, input);
    }

    /**
     * APP获取OSS临时访问凭证
     * STS（Security Token Service）是阿里云提供的临时访问凭证服务
     * @param accessKeyId
     * @param accessKeySecret
     * @param roleArn
     * @param roleSessionName
     * @param policy
     * @param protocolType
     * @return
     * @throws ClientException
     */
    public static AssumeRoleResponse assumeRole(String accessKeyId, String accessKeySecret,
                                                String roleArn, String roleSessionName, String policy,
                                                ProtocolType protocolType) throws ClientException {
        try {
            // 创建一个 Aliyun Acs Client, 用于发起 OpenAPI 请求
            IClientProfile profile = DefaultProfile.getProfile(BussinessConstants.REGION_CN_HANGZHOU, accessKeyId, accessKeySecret);
            DefaultAcsClient client = new DefaultAcsClient(profile);

            // 创建一个 AssumeRoleRequest 并设置请求参数
            final AssumeRoleRequest request = new AssumeRoleRequest();
            request.setVersion(BussinessConstants.STS_API_VERSION);
            request.setMethod(MethodType.POST);
            request.setProtocol(protocolType);

            request.setRoleArn(roleArn);
            request.setRoleSessionName(roleSessionName);
            request.setPolicy(policy);

            // 发起请求，并得到response
            final AssumeRoleResponse response = client.getAcsResponse(request);
            return response;
        } catch (ClientException e) {
            throw e;
        }
    }

    @Override
	public String getSMSVerifyCodeWithoutSendSMS(
			SMSVerifyBeanInput smsVerifyBeanInput) throws VerifyCodeException {
		 if (StringUtils.isBlank(smsVerifyBeanInput.getMobilePhone()) || smsVerifyBeanInput.getType() < 0) {
	            throw new VerifyCodeException(StatusCode.PARAM_EMPTY);
	        }

	        Pattern pattern = Pattern.compile("([0-9]){11}");
	        boolean checkResult = pattern.matcher(smsVerifyBeanInput.getMobilePhone().trim()).matches();
	        String mobilePhone = smsVerifyBeanInput.getMobilePhone().trim();
	        /**
	         * 2.判断手机号是否正确 账号是否被注销
	         */
	        if (!checkResult) {
	            throw new VerifyCodeException(StatusCode.MOBILE_FORMATE_ERROR);
	        }
	        int member = membershipInfoMapper.countByMobilePhone(smsVerifyBeanInput.getMobilePhone(),smsVerifyBeanInput.getOrgId());
	        AccountStatusDto accountStatusDto = this.getAccountStatusByMobileV2(smsVerifyBeanInput.getMobilePhone(), 0,smsVerifyBeanInput.getOrgId());
	        logger.debug("根据手机号查询会员注销信息:" + JSON.toJSONString(accountStatusDto));
	        if (member < 1) {
	            if (smsVerifyBeanInput.getType() == 1) {
	                throw new VerifyCodeException(StatusCode.ERROR_USER);
	            }
	            if (smsVerifyBeanInput.getType() == 5 || smsVerifyBeanInput.getType() == 6) {
	                throw new VerifyCodeException(StatusCode.USER_NOT_EXITED);
	            }
	        } else {
	            if (accountStatusDto.getAccountStatus() != 1 && accountStatusDto.getAccountStatus() != 2) {
	                if (smsVerifyBeanInput.getType() == 2 || smsVerifyBeanInput.getType() == 4) {
	                    throw new VerifyCodeException(StatusCode.MOBILE_USED);
	                }
	            }
	        }
	        /**
	         * 3.发送验证码
	         */
	        String verifyCode = ComUtil.getVerifyCode(true, 6);
	        if ("dev".equals(CommConfigUtil.getENV()) || "test".equals(CommConfigUtil.getENV())) {
	            verifyCode = "123456";
	        }
	        JedisUtil.set(mobilePhone, verifyCode, 300);
	        return verifyCode;
	}


    public String getAccessToken() {
        String accessToken = JedisUtil.get(BussinessConstants.ACCESS_TOKEN_KEY);
        if(StringUtils.isBlank(accessToken)){
            Map<String,Object> params = new HashMap<>(1);
            String sign = BussinessConstants.SAIC_APP_FLAG + ":" + BussinessConstants.SAIC_APP_SECRET;
            params.put("Authorize","Basic "+ ImgUtils.encode(sign.getBytes()));
            try {
                String responseResult = HttpClientUtils.httpGetRequest(BussinessConstants.GET_SAIC_ASSESSTOKEN_URL, params);
                if(StringUtils.isNotEmpty(responseResult)){
                    JSONObject jsonObject = JSON.parseObject(responseResult);
                    if(jsonObject != null && jsonObject.getString("accessToken") != null){
                        accessToken = jsonObject.getString("accessToken");
                        JedisUtil.set(BussinessConstants.ACCESS_TOKEN_KEY,accessToken);
                    }else{
                        logger.warn("调用上汽获取签名接口失败,返回="+responseResult);
                    }
                }else{
                    logger.warn("调用上汽获取签名接口失败");
                }
            } catch (Exception e) {
                logger.warn("调用上汽获取签名接口失败");
            }
        }
        return accessToken;
    }

    public int updateAutoReviewStatus(String authId, String facePicUrl,int driverLicenseInputType,String mobilePhone,
                                      int reviewStatus,String appkey,String idCardNO, String refKey2) throws AuthenticationException {
        HidLog.membership(LogPoint.UPDATE_USER_REVIEW_STATUS, "更新用户审核状态开始", authId, true);
        MembershipInfoWithBLOBs membershipInfo = membershipInfoMapper.selectByAuthId(authId, 0);
        if (membershipInfo == null) {
            HidLog.membership(LogPoint.UPDATE_USER_REVIEW_STATUS, StatusCode.USER_INFO_NO_EXIST.getMsg(), authId, false);
            throw new AuthenticationException(StatusCode.USER_INFO_NO_EXIST);
        }
        boolean autoReviewNoPass = false;
        boolean isMainland = ComUtil.checkIsUsersInMainlandResidents(membershipInfo.getNational());
        boolean autoCheckLicense = isMainland && checkCityIsAutoLicenseAuth(membershipInfo.getCityOfOrigin());
        boolean licenseAuthPassed = NumberUtils.INTEGER_ONE.equals(membershipInfo.getLicenseElementsAuthStatus())
                && NumberUtils.INTEGER_ONE.equals(membershipInfo.getLicenseElementsAuthStatus());
        //自动审核通过
        MembershipInfo entity = new MembershipInfo();
        Short objReviewStatus = null;
        //审核方式 1:手动审核 2:自动审核
        Integer reviewMode = null;
        Integer licenseAuthStatus = null;
        //区分驾照提取方式  新用户手动输入驾照变成待审核
        if (reviewStatus != 1 && driverLicenseInputType == 2) {
            objReviewStatus = 0;
            //add 20200513 将用户审核状态修改为待审核时，同时修改驾照认证状态为待审核。
            licenseAuthStatus = 0;
            reviewMode = 1;
        } else {
            //无需校验三要素或者三要素验证通过
            if(autoCheckLicense && !licenseAuthPassed) {
                HidLog.membership(LogPoint.UPDATE_USER_REVIEW_STATUS, "大陆用户三要素及驾照认证状态未通过，刷脸后不自动审核通过", authId, false);
                if("2".equals(String.valueOf(membershipInfo.getLicenseElementsAuthStatus()))) {
                        //|| "2".equals(String.valueOf(membershipInfo.getLicenseAuthStatus()))) {
                    //三要素认证不通过或驾照认证不通过，则自动审核不通过
                    autoReviewNoPass = true;
                }else {
                    //城市开启自动验证&&三要素待认证或驾照状态待认证，则提交人工审核
                    objReviewStatus = 0;
                    licenseAuthStatus = 0;
                    reviewMode = 1;
                }
            }else{
                objReviewStatus = 1;
                reviewMode = 2;
            }
        }
        if (StringUtils.isNoneBlank(idCardNO)){
            entity.setIdCardNumber(idCardNO);
        }
        entity.setReviewStatus(objReviewStatus);
        entity.setReviewMode(reviewMode);
        if(StringUtils.isNotBlank(appkey) && NOT_FACE_IMG_ORDER.contains(appkey)){
            entity.setAppKey(appkey);
        }
        entity.setAuthenticationStatus(2);
        entity.setFaceRecognitionImgUrl(facePicUrl);
        entity.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
        entity.setReviewTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE4));
        entity.setAppReviewTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE4));
        entity.setUpdatedUser(authId);
        entity.setReviewItems("111111111");
        entity.setAuthId(authId);
        entity.setLicenseAuthStatus(licenseAuthStatus);

        //修改个人信息地址
//        if(reviewStatus == 1){
//            MembershipInfoWithBLOBs member = membershipInfoMapper.selectByAuthId(authId,0);
//            if(!ObjectUtils.isEmpty(member) && ObjectUtils.isEmpty(member.getAddress())){
//                MmpUserTag mmpUserTag = mmpUserTagMapper.selectMmpUserByAuthId(authId);
//                if(!ObjectUtils.isEmpty(mmpUserTag) && !ObjectUtils.isEmpty(mmpUserTag.getSpare9())){
//                    BaiDuAddressResp baiDuAddressResp = baiduOcrClient.getBaiDuAddress(mmpUserTag.getSpare9());
//                    if(!ObjectUtils.isEmpty(baiDuAddressResp)){
//                        DateFormat dateFormat = new SimpleDateFormat(DateType.DATE_TYPE3);
//                        membershipInfoMapper.updateAddress(authId,baiDuAddressResp.getDetail(),baiDuAddressResp.getProvince()
//                                ,baiDuAddressResp.getCity(),baiDuAddressResp.getCounty(),"系统",dateFormat.format(new Date()));
//                    }
//                }
//            }
//        }

        int i = membershipInfoMapper.updateFaceRecognitionImgUrl(entity);
        if (i <= 0) {
            HidLog.membership(LogPoint.UPDATE_USER_REVIEW_STATUS, "更新数据库用户审核状态失败", authId, false);
            throw new AuthenticationException(StatusCode.FACE_FAILURE);
        } else {
            //保存操作日志
            UserOperationLogInput userOperationLogInput = new UserOperationLogInput();
            userOperationLogInput.setOperationType(1L);
            userOperationLogInput.setOperationContent("人脸认证通过");
            userOperationLogInput.setOperationTime(new Date());
            userOperationLogInput.setOperator(authId);
            userOperationLogInput.setRefKey1(appkey);
            userOperationLogInput.setAuthId(authId);
            userOperationLogInput.setRefKey2(refKey2);
            this.saveUserOperationLog(userOperationLogInput);
            insertOperatorLog("人脸认证通过", authId, "", authId);

            if(objReviewStatus != null) {
                String operatorContent = "提交人工审核";
                if (objReviewStatus == 1) {
                    operatorContent = "自动审核通过";
                    HidLog.membership(LogPoint.MEMBER_AUTH_SUCCESS, "自动认证通过", authId);
                    userOperationLogInput.setOperationType(2L);
                    userOperationLogInput.setOperationContent("自动审核通过");
                    this.saveUserOperationLog(userOperationLogInput);
                    //modify 20200605 恢复仅审核通过，刷脸之后才做制卡检查逻辑(检查是否无卡)
                    setVirtualCard(authId);
                }
                insertOperatorLog(operatorContent, authId, "", authId);
                logger.warn("会员authId=" + authId + operatorContent + "成功");
            }
            if(autoReviewNoPass) {
                LicenseStatusInfo licenseStatusInfo = LicenseStatusChecker.check(membershipInfo.getDrivingLicenseType(),
                        membershipInfo.getLicenseExpirationTime(), membershipInfo.getLicenseStatusMsg());
                //将会员自动审核为不通过， cause为驾照状态不可用
                DrivingLicenseReviewInfo licenseReviewInfo = new DrivingLicenseReviewInfo();
                licenseReviewInfo.setLicenseStatus(licenseStatusInfo.getLicenseStatus());
                licenseReviewInfo.setLicenseStatusMsg(licenseStatusInfo.getLicenseStatusMsg());
                licenseReviewInfo.setAuthenticateStatus(membershipInfo.getLicenseElementsAuthStatus());
                licenseReviewInfo.setElementsReviewItems(membershipInfo.getElementsReviewItems());
                //0驾照认证不通过 1三要素认证不通过 2二者均不通过
                Integer reviewFailedCause = null;
                if(!licenseStatusInfo.isValid()) {
                    reviewFailedCause = 0;
                }
                if(licenseReviewInfo.getAuthenticateStatus() != 1) {
                    reviewFailedCause = (reviewFailedCause == null) ? 1 : 2;
                }
                if(reviewFailedCause != null) {
                    memberReviewService.autoMemberReviewFailed(membershipInfo, licenseReviewInfo, licenseStatusInfo, MemberReivewOriginEnum.SYS_AUTO_REVIEW_FAILED_FACEPASS, reviewFailedCause);
                    HidLog.membership(LogPoint.UPDATE_USER_REVIEW_STATUS, "刷脸通过，三要素或驾照状态认证不通过->系统自动审核不通过", authId, true);
                }
            }

        }
        HidLog.membership(LogPoint.UPDATE_USER_REVIEW_STATUS, "更新用户审核状态结束", authId, true);
        if(objReviewStatus == null) {
            objReviewStatus = (short)reviewStatus;
        }
        //埋点
        try {
            if (objReviewStatus == 1) {
                String[] reviewArray = new String[]{"资料不全", "待审核", "审核通过", "审核不通过", "用户无效", "重新审核"};
                Map<String, Object> map = new HashMap<>();
                map.put("type", "自动审核");
                map.put("is_success", true);
                map.put("reason", "");
                map.put("duration", 0);
                map.put("desc", reviewArray[reviewStatus +1]);
                sensorsdataService.track(authId, true, "verify_completed", map);
            }
        } catch (Exception e) {
            logger.error(authId +"自动审核埋点", e);
        }
        return objReviewStatus;
    }

    public void insertOperatorLog(String operatorContent, String foreignKey, String foreignKey2, String createUser) {
        UserOperatorLog userOperatorLog = new UserOperatorLog();
        userOperatorLog.setOperatorContent(operatorContent);
        userOperatorLog.setForeignKey(foreignKey);
        userOperatorLog.setForeignKey2(foreignKey2);
        userOperatorLog.setCreatedUser(createUser);
        userOperatorLog.setCreatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
        userOperatorLogMapper.saveSelective(userOperatorLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setVirtualCard(String authId) throws AuthenticationException {
        setVirtualCard(authId,0);
    }

    @Override
    public void setVirtualCard(String authId, int memberType) throws AuthenticationException {
        logger.warn(authId + "制虚拟卡");
        String createUser = "membership";
        // 添加卡信息
        CardInfo cardInfo = new CardInfo();
        String virtualCardNo = ComUtil.getVirtualCardNo(cardInfoMapper);
        logger.warn(authId + ",生成虚拟卡号：" + virtualCardNo);
        cardInfo.setCardNo(virtualCardNo);
        cardInfo.setCardType(0.0);
        // 卡号类型（1:虚拟卡号，2:物理卡号）
        Integer cardNoType = 1;
        cardInfo.setCardNoType(cardNoType);
        cardInfo.setAuthId(authId);
        String internalNo = ComUtil.generateInternalNo(cardInfoMapper, 1);
        logger.warn("获取会员卡的内部编号：" + internalNo);
        cardInfo.setInternalNo(internalNo);
        cardInfo.setValidityTime("20200101");
        // 初始秘钥
        cardInfo.setRwKeytAa("ffffffffffff");
        cardInfo.setRwKeytBb("ffffffffffff");
        cardInfo.setStatus(0);
        cardInfo.setActivateStatus(1.0);
        cardInfo.setCreatedUser(createUser);
        cardInfo.setCreatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
        cardInfo.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));

        // 判断用户知第一次发卡还是补发卡
        MembershipInfoWithBLOBs membershipInfo =null;
        try {
            membershipInfo = membershipInfoMapper.selectByAuthId2(authId);
        } catch (Exception e) {
            membershipInfo = membershipInfoMapper.selectByAuthId(authId,memberType);
        }
        if (membershipInfo == null){
            throw new AuthenticationException(StatusCode.USER_INFO_NO_EXIST);
        }
        logger.warn(authId + "会员卡cardNo:" + membershipInfo.getCardNo());
        //自动审核，只给没有会员卡的用户制虚拟卡
        if (membershipInfo.getCardNo() == null || membershipInfo.getCardNo().trim().equals("")) {
            logger.warn(authId + "第一次发卡");

            ApplyProgress progress = new ApplyProgress();
            progress.setAuthId(authId);
            progress.setProgressContent("审核通过，生成虚拟卡号");
            progress.setCreatedUser(createUser);
            progress.setProgressTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE1));
            //新用户而且审核通过
            applyProgressMapper.addApplyProgress(progress);

            List<CardInfo> cardUsedInfolist = cardInfoMapper.getCardInfo(virtualCardNo);
            if (CollectionUtils.isEmpty(cardUsedInfolist)) {
                cardInfoMapper.addCardInfo(cardInfo);
            } else {

                CardInfo newCardInfo = cardUsedInfolist.get(0);
                logger.warn(authId + "绑定已存在但被注销使用的卡：" + newCardInfo);
                CardInfo updateCardEntity = new CardInfo();
                updateCardEntity.setStatus(0);
                updateCardEntity.setActivateStatus(Double.valueOf(1));
                updateCardEntity.setAuthId(authId);
                updateCardEntity.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
                updateCardEntity.setUpdatedUser(createUser);
                updateCardEntity.setCardNo(virtualCardNo);
                cardInfoMapper.updateCardNo(updateCardEntity);
                internalNo = newCardInfo.getInternalNo();
            }
            //更新会员虚拟卡号
            MembershipInfo memberEntity = new MembershipInfo();
            memberEntity.setCardNo(virtualCardNo);
            memberEntity.setAuthId(authId);
            memberEntity.setMembershipType((short)memberType);
            membershipInfoMapper.updateMemberCardNoByAuthIdAndType(memberEntity);

            // 添加操作日志
            String operatorContent = "添加会员卡【" + internalNo + "】";
            String foreignKey = authId;
            insertOperatorLog(operatorContent, foreignKey, "-1", createUser);

            String nowTime4 = ComUtil.getSystemDate(ComUtil.DATE_TYPE4);
            CardInfoHistroy cardInfoHistroyEntity = new CardInfoHistroy();
            cardInfoHistroyEntity.setCardStatus(Double.valueOf(1));
            cardInfoHistroyEntity.setUpdatedTime(nowTime4);
            cardInfoHistroyEntity.setUpdatedUser(createUser);
            cardInfoHistroyEntity.setAuthId(authId);
            cardInfoHistroyEntity.setCardNo(virtualCardNo);
            Integer num1 = cardInfoHistroyMapper.updateCardInfoHistroy(cardInfoHistroyEntity);
            if (num1 < 1) {
                cardInfoHistroyEntity.setCreatedTime(nowTime4);
                cardInfoHistroyEntity.setCreatedUser(createUser);
                cardInfoHistroyEntity.setMembershipType(Double.valueOf(memberType));
                cardInfoHistroyMapper.addCardInfoHistroy(cardInfoHistroyEntity);
            }
            String token = JedisUtil.get(authId + "_" + "0");
            if (token != null) {
                // 更新此token内的cardNo
                JedisUtil.hset(token, "cardNo", virtualCardNo);
            }
        }
    }

    @Override
    public HealthCodeDto queryHealthCode(String authId, Integer type) {
            if (StringUtils.isBlank(authId)){
                return null;
            }
            HealthCode healthCode = healthCodeMapper.selectByAuthId(authId);
            if(type != null && type.equals(1)){
                if (healthCode == null) {
                    return null;
                }
            }else {
                HealthCodeDto healthCodeDto = new HealthCodeDto();
                //查询健康码颜色
                MembershipBasicInfo basicInfo = this.getUserBasicInfo(authId,(short) 0);
                if (basicInfo ==null){
                    return null;
                }

                GetMemberHealthResponse baseResponse = memberShipInvitationServ.getMemberHealth(basicInfo.getName(),basicInfo.getDriverCode(),basicInfo.getAuthId());
                if (baseResponse == null){
                    healthCodeDto.setColor(1);
                    return healthCodeDto;
                }
                int color = 1;
                if (baseResponse != null && baseResponse.getHealthType() != null){
                    if (baseResponse.getCode() == 0){
                        switch (baseResponse.getHealthType()) {
                            case GREEN:
                                color =1;
                                break;
                            case YELLOW:
                                color = 2;
                                break;
                            case RED:
                                color = 3;
                                break;
                        }
                    }
                }
                if (color != 1){
                    //健康码颜色不为绿色
                    GetUserCurrentOrderInput getUserCurrentOrderInput = new GetUserCurrentOrderInput(authId, 0, "");
                    List<UserOrderInfoDto> userOrderInfoDto = orderService.getUserCurrentOrderList(getUserCurrentOrderInput);
                    if(CollectionUtils.isNotEmpty(userOrderInfoDto) && userOrderInfoDto.get(0).getOrderStatus() == 3){
                        //存在进行中的订单需要发短信
                        try{
                            messagepush.asyncSendSMSTemplate(basicInfo.getMobilePhone(),104,authId);
                            this.senHealthCodeMail( basicInfo,userOrderInfoDto.get(0));
                        }catch (Exception e){
                            logger.info("推送健康码消息失败");
                        }
                    }

                }
                if (healthCode != null) {
                    healthCode.setColor(color);
                    healthCode.setAuthId(authId);
                    short status = healthCode.getStatus();
                    if (type.equals(2)){
                        status = 0;
                    }
                    healthCodeMapper.updateByAuthId(authId,color,status);
                }else {
                    healthCode = new HealthCode();
                    healthCode.setColor(color);
                    healthCode.setAuthId(authId);
                    healthCode.setCreatedTime(ComUtil.getFormatDate(new Date(),DateType.DATE_TYPE3));
                    healthCode.setCreatedUser(authId);
                    healthCode.setUpdatedTime(ComUtil.getFormatDate(new Date(),DateType.DATE_TYPE3));
                    healthCode.setUpdatedUser(authId);
                    if (type.equals(3)){
                        healthCode.setStatus((short)3);
                    }
                    healthCodeMapper.insertSelective(healthCode);
                }

            }
        HealthCodeDto healthCodeDto = new HealthCodeDto();

            if (healthCode !=null){
                BeanCopyUtils.copyProperties(healthCode,healthCodeDto);
            }
            return healthCodeDto;
    }

    @Override
    public void submitDepositSecurity(SubmitDepositSecurityInput depositSecurityInput) {

        DepositSecurity depositSecurity = new DepositSecurity();
        BeanCopyUtils.copyProperties(depositSecurityInput,depositSecurity);
        depositSecurityMapper.insertSelective(depositSecurity);

    }

    @Override
    public void checkInvitationCode(String invitationCode) {
        if (StringUtils.isBlank(invitationCode)){
            throw new RegisterException(StatusCode.INVITED_CODE_ERROR);
        }
        //通过邀请码查询相应的shareId
        String shareId = membershipClauseLogMapper.queryShareIdByInvitationCode(invitationCode.toLowerCase());
        if (StringUtils.isBlank(shareId)){
            throw new RegisterException(StatusCode.INVITED_CODE_ERROR);
        }
    }

    @Override
    public List<MembershipBasicInfo> queryEffectiveMemberForCq(String orgId, String createdTime, String updatedTime) {
        return membershipInfoMapper.queryEffectiveMemberForCq(orgId,createdTime,updatedTime);
    }

    /**
     * CSV文件列分隔符
     */
    private static final String CSV_COLUMN_SEPARATOR = ",\t";

    /**
     * CSV文件行分隔符
     */
    private static final String CSV_ROW_SEPARATOR = "\r\n";
    private void senHealthCodeMail(MembershipBasicInfo basicInfo,UserOrderInfoDto userOrderInfoDto) {

        // 构造导出数据结构
        // 设置表头
        List<String> titles = Arrays.asList("用户姓名", "手机号", "健康码查询日期", "健康码查询时间", "会员所属公司", "车辆运营机构", "车型名称", "车牌号",
                "订单编号", "用户取车时间", "计划还车时间", "取车网点", "计划还车网点");
        // 设置每列字段
        List<String> keys = Arrays.asList("name", "mobilePhone", "date", "time", "userOrg", "vehicleOrg",
                "vehicleModel", "vehicleNo", "orderSeq", "pickUpTime", "returnTime", "pickUpShop",
                "returnShop");

        // 构造导出数据
        List<Map<String, Object>> dataList = new ArrayList<>();
        Map<String, Object> map;
        map = new HashMap<>();
        map.put("name", basicInfo.getName());
        map.put("mobilePhone",basicInfo.getMobilePhone());
        map.put("date", ComUtil.getFormatDate(new Date(),DateType.DATE_TYPE8));
        map.put("time", ComUtil.getFormatDate(new Date(),DateType.DATE_TYPE19));
        map.put("userOrg", basicInfo.getOrgName());
        map.put("vehicleOrg", userOrderInfoDto.getVehicleInfoDto().getOperationOrgId());
        map.put("vehicleModel", userOrderInfoDto.getVehicleInfoDto().getVehicleModelInfo());
        map.put("vehicleNo", userOrderInfoDto.getVehicleInfoDto().getVehicleNo());
        map.put("orderSeq", userOrderInfoDto.getOrderSeq());
        map.put("pickUpTime", userOrderInfoDto.getPickupVehicleInfoDto().getPickupDateTime());
        map.put("returnTime", userOrderInfoDto.getReturnVehicleInfoDto().getPlanReturnDateTime());
        map.put("pickUpShop", userOrderInfoDto.getPickupVehicleInfoDto().getPickupStoreName());
        map.put("returnShop", userOrderInfoDto.getReturnVehicleInfoDto().getReturnStoreName());
        dataList.add(map);

        // 设置导出文件前缀
        String fName = basicInfo.getAuthId();
        // 文件导出
        try {
            // 保证线程安全
            StringBuffer buf = new StringBuffer();
            // 组装表头
            for (String title : titles) {
                buf.append(title).append(CSV_COLUMN_SEPARATOR);
            }
            buf.append(CSV_ROW_SEPARATOR);

            // 组装数据
            if (CollectionUtils.isNotEmpty(dataList)) {
                for (Map<String, Object> data : dataList) {
                    for (String key : keys) {
                        buf.append(data.get(key)).append(CSV_COLUMN_SEPARATOR);
                    }
                    buf.append(CSV_ROW_SEPARATOR);
                }
            }
            String targetPath = "/healthCode/"+basicInfo.getAuthId() + System.currentTimeMillis() +".csv" ;
            //上传oss
            UploadImgUtil.uploadByteArray(buf.toString().getBytes("GBK"), targetPath, UploadImgUtil.OSS_BUCKET);
            String imageUrl = OssConfigUtil.getFileBaseUrl() + targetPath;
            MailSenderInfo mailSenderInfo = new MailSenderInfo();
            mailSenderInfo.setUserName("<EMAIL>");
            mailSenderInfo.setPassword("Extracme123");
            mailSenderInfo.setFromAddress("<EMAIL>");
            mailSenderInfo.setSubject("健康码预警邮件");
            mailSenderInfo.setContent("您好：附件是当前时刻" + ComUtil.getFormatDate(new Date(),DateType.DATE_TYPE1) +"系统查出健康码为红色/黄色且以取车订单的用户，请知晓。（系统邮件，勿回）");
            List<String> mailList = new ArrayList<>();
            mailList.add("<EMAIL>");
            mailList.add("<EMAIL>");
            List<String> attachFilePaths = new ArrayList<>();
            attachFilePaths.add(imageUrl);
            List<String> attachFileNames = new ArrayList<>();
            attachFileNames.add(basicInfo.getName());
            mailSenderInfo.setToAddressList(mailList);
            mailSenderInfo.setAttachFilePaths(attachFilePaths);
            mailSenderInfo.setAttachFileNames(attachFileNames);
            messagepush.sendHtmlMail(mailSenderInfo);
        } catch (Exception e) {
            logger.error("导出失败", e.getMessage());
        }
    }

    public void getFaceContrastResult(String holdIdImg,String faceImg,String authId) throws AuthenticationException {
        HidLog.membership(LogPoint.GET_USER_SIMILARITY, "人脸对比开始", authId, true);
        //调用阿里人脸对比API
        HttpPost httpPost = new HttpPost(BussinessConstants.FACE_VERIFY);
        httpPost.setHeader("Authorization", "APPCODE " + BussinessConstants.APP_CODE);
        Map<String, Object> params = new HashMap<>();
        params.put("type", 0);
        params.put("image_url_1", holdIdImg);
        params.put("image_url_2", faceImg);
        StringEntity se = new StringEntity(JSON.toJSONString(params), "UTF-8");
        se.setContentType("application/json");
        se.setContentEncoding(new BasicHeader(HTTP.CONTENT_TYPE, "application/json"));
        httpPost.setEntity(se);
        String response = HttpClientUtils.getResult(httpPost);
        if (org.apache.commons.lang.StringUtils.isBlank(response)) {
            HidLog.membership(LogPoint.GET_USER_SIMILARITY, "人脸验证API调用失败", authId, false);
            throw new AuthenticationException(-1,"人脸验证API调用失败,请联系客服");
        }
        JSONObject jsonObject = JSON.parseObject(response);
        int isSuccess = 0;
        double confidence = 0d;
        int errno = jsonObject.getIntValue("errno");
        if (errno == 0) {
            confidence = jsonObject.getDoubleValue("confidence");
            isSuccess = confidence < BussinessConstants.CONFIDENCE ? 0 : 1;
        }

        if (isSuccess == 0) {
            logger.error("用户" + authId + "调用人脸对比接口返回={}", response);
            HidLog.membership(LogPoint.GET_USER_SIMILARITY, "人脸对比失败,result=" + response, authId, false);
            throw new AuthenticationException(-1,"请确保人脸照片为您本人");
        }
        HidLog.membership(LogPoint.GET_USER_SIMILARITY, "人脸对比结束", authId, true);
    }

    @Override
    public DriverLicenseAuthResultDTO getLastDriverLicenseAuthResult(String authId) {
        MembershipBaseInfo member = membershipInfoMapper.selectBaseByAuthId(authId, 0);
        if(member != null) {
            DriverLicenseAuthResultDTO resultDTO = new DriverLicenseAuthResultDTO();
            DriverLicenseElementsAuthenticateRecord record =
                    licenseElementsAuthenticateRecordMapper.getLastestRecordByUser(member.getPkId());
            if(record != null) {
                BeanCopyUtils.copyProperties(record, resultDTO);
                return resultDTO;
            }
        }
        return null;
    }

    @Override
    public DriverLicenseAuthResultDTO getLastAuthenticateRecordByUser(String authId,Integer membershipType) {
        MembershipBaseInfo member = membershipInfoMapper.selectBaseByAuthId(authId, membershipType);
        if(member != null) {
            DriverLicenseAuthResultDTO resultDTO = new DriverLicenseAuthResultDTO();
            DriverLicenseElementsAuthenticateRecord record =
                    licenseElementsAuthenticateRecordMapper.getLastAuthenticateRecordByUser(member.getPkId());
            if(record != null) {
                BeanCopyUtils.copyProperties(record, resultDTO);
                return resultDTO;
            }
        }else{
            if ( membershipType == 0) {
                logger.info("getLastAuthenticateRecordByUser 兼容擎路用户,authId={}",authId);
                return getLastAuthenticateRecordByUser(authId,2);
            }
        }
        return null;
    }

    @Override
    public DriverLicenseElementsAuthenticateLogDTO getLastAuthenticateLogByRecordId(Long recordId,Integer logType) {
            DriverLicenseElementsAuthenticateLogDTO resultDTO = new DriverLicenseElementsAuthenticateLogDTO();
            DriverLicenseElementsAuthenticateLog record =
                    licenseElementsAuthenticateLogMapper.selectByRecordId(recordId,logType);
            if(record != null) {
                BeanCopyUtils.copyProperties(record, resultDTO);
                return resultDTO;
            }
        return null;
    }

    @Override
    public List<DriverLicenseElementsAuthenticateLogDTO> getLastAuthenticateLogsByUser(String authId, Integer logType, Integer num) {
        MembershipBaseInfo member = membershipInfoMapper.selectBaseByAuthId(authId, 0);
        if(member == null) {
            return null;
        }
        List<DriverLicenseElementsAuthenticateLogDTO> result = new ArrayList<>(10);
        Integer limit = (num == null || num > 20) ? 20 : num;
        List<DriverLicenseElementsAuthenticateRecord> records =
                licenseElementsAuthenticateRecordMapper.getLastLicAuthRecordsByUser(member.getPkId(), limit);
        if(CollectionUtils.isNotEmpty(records)) {
            List<Long> recordIds = records.stream().map(p->p.getId()).collect(Collectors.toList());
            //限制日志最大日志条数
            List<DriverLicenseElementsAuthenticateLog> logRecords =
                    licenseElementsAuthenticateLogMapper.selectByRecordIds(recordIds, logType, limit * 3);
            for(DriverLicenseElementsAuthenticateLog log : logRecords) {
                DriverLicenseElementsAuthenticateLogDTO logDTO = new DriverLicenseElementsAuthenticateLogDTO();
                BeanCopyUtils.copyProperties(log, logDTO);
                result.add(logDTO);
            }
        }
        return result;
    }

    @Override
    public UploadMemberImageDto uploadMemberImage(Integer type, byte[] byteArray, String authId) throws BusinessException {
        UploadMemberImageDto dto = new UploadMemberImageDto();
        if (type != null) {
            String targetPath;
            String systemDate;
            switch (type) {
                case 1:
                    targetPath = "sensitiveBucket/driving_license/" + authId + "/";
                    break;
                case 2:
                    targetPath = "sensitiveBucket/holdIcCardPic/" + authId + "/";
                    break;
                case 3:
                    targetPath = "sensitiveBucket/idCardPic/" + authId + "/";
                    break;
                case 4:
                    targetPath = "sensitiveBucket/faceRecognitionPic/" + authId + "/";
                    break;
                case 5:
                    systemDate = DateFormatUtils.format(new Date(),DateType.DATE_TYPE8);
                    targetPath = "sensitiveBucket/openDoorFaceImage/" + authId + "/" + systemDate + "/";
                    break;
                case 6:
                    systemDate = DateFormatUtils.format(new Date(),DateType.DATE_TYPE8);
                    targetPath = "sensitiveBucket/modifyTelephoneFaceImage/" + authId + "/" + systemDate + "/";
                    break;
                case 7:
                    targetPath = "sensitiveBucket/newIdCardImg/";
                    break;
                default:
                    targetPath = StringUtils.EMPTY;
            }
            targetPath = targetPath +  System.currentTimeMillis() + ".jpg";
            UploadImgUtil.uploadByteArray(byteArray, targetPath, UploadImgUtil.OSS_SENSITIVE_BUCKET);
            String displayUrl = ComUtil.getStsUrlTokenGet(UploadImgUtil.OSS_SENSITIVE_BUCKET,UploadImgUtil.ENV + "/"+ targetPath).toString();
            dto.setDisplayUrl(displayUrl);
            dto.setTargetPath(targetPath);

//            dto.setDisplayUrl(StringUtils.EMPTY);
//            dto.setTargetPath(StringUtils.EMPTY);
        }
        return dto;
    }

    @Override
    public MmpUserTagDto queryUserTagByAuthId(String authId) {
        if(StringUtils.isBlank(authId)){
            return null;
        }
        MmpUserTag mmpUserTag =  mmpUserTagMapper.selectMmpUserByAuthId(authId);
        if(mmpUserTag != null) {
            MmpUserTagDto mmpUserTagDto = new MmpUserTagDto();
            BeanCopyUtils.copyProperties(mmpUserTag,mmpUserTagDto);
            return mmpUserTagDto;
        }
        return null;
    }

    @Override
    public List<MmpUserTagDto> queryUserTagListByAuthIds(List<String> authIds) {
        if(CollectionUtils.isEmpty(authIds)){
            return null;
        }

        List<MmpUserTag> mmpUserTags =  mmpUserTagMapper.selectMmpUserListByAuthIds(authIds);
        if(CollectionUtils.isNotEmpty(mmpUserTags)) {
            List<MmpUserTagDto> result = new ArrayList<>();
            mmpUserTags.forEach(m->{
                MmpUserTagDto mmpUserTagDto = new MmpUserTagDto();
                BeanCopyUtils.copyProperties(m,mmpUserTagDto);
                result.add(mmpUserTagDto);
            });
            return result;
        }
        return null;
    }


    @Override
    public int updateUserTagByAuthId(MmpUserTagDto userTagDto) {
        if(StringUtils.isBlank(userTagDto.getAuthId())){
            return 0;
        }
        MmpUserTag mmpUserTag = new MmpUserTag();
        BeanCopyUtils.copyProperties(userTagDto,mmpUserTag);
        /**
         * 若职位/学历/私家车拥有情况补全，也推送积分
         */
        MmpUserTag oldTag = mmpUserTagMapper.selectMmpUserByAuthId(userTagDto.getAuthId());
        if(oldTag != null) {
            if(checkIsEmptyTagValue(oldTag.getEducational()) && !checkIsEmptyTagValue(mmpUserTag.getEducational())) {
                memberInfoCompleteReward(userTagDto.getAuthId(), MemPointsPushEnum.COMPETE_MEMBER_EDUCATION);
            }
            if(checkIsEmptyTagValue(oldTag.getProfession()) && !checkIsEmptyTagValue(mmpUserTag.getProfession())) {
                memberInfoCompleteReward(userTagDto.getAuthId(), MemPointsPushEnum.COMPETE_MEMBER_JOB);
            }
            if(checkIsEmptyTagValue(oldTag.getOwnCar()) && !checkIsEmptyTagValue(mmpUserTag.getOwnCar())) {
                memberInfoCompleteReward(userTagDto.getAuthId(), MemPointsPushEnum.COMPETE_MEMBER_CAROWN);
            }
        }
        if (userTagDto.getAlipayOrderAuthority() != null && userTagDto.getAlipayOrderAuthority() > 0) {
            mmpUserTag.setAlipayOrderAuthority(userTagDto.getAlipayOrderAuthority());
        } else {
            mmpUserTag.setAlipayOrderAuthority(null);
        }
        return mmpUserTagMapper.updateByAuthId(mmpUserTag);
    }

    private boolean checkIsEmptyTagValue(Integer value){
        return (value == null || NumberUtils.INTEGER_ZERO.equals(value));
    }

    @Override
    public List<QueryOrgCardInfoDto> queryOrgCardList(String orgId, Integer cardStatus) {
        List<QueryOrgCardInfoDto> resultList = new ArrayList<>();
        List<QueryOrgCardInfoDto> list = cardInfoMapper.queryOrgCardList(orgId);
        if (CollectionUtils.isNotEmpty(list)) {
            for (QueryOrgCardInfoDto row:list) {
                if (cardStatus == 0) {
                    if (row.getCardStatus() == 0) {
                        resultList.add(row);
                    }
                } else if (cardStatus == 1) {
                    if (row.getCardStatus() == 1) {
                        resultList.add(row);
                    }
                } else {
                    resultList.add(row);
                }
            }
        }
        return resultList;
    }

    @Override
    public Boolean checkImeiBlackList(String imei) {
        boolean flag = false;
        String imeiBlackStr = JedisUtil.get(BussinessConstants.IMEI_BLACK_LIST_KEY);
        if (StringUtils.isNotBlank(imeiBlackStr)) {
            String[] value = StringUtils.split(imeiBlackStr, ",");
            List<String> blackList = Arrays.asList(value);
            if (blackList.contains(imei)) {
                flag = true;
            }
        }
        return flag;
    }

    /**
     * ocr提取驾照信息
     * @param fileByte
     * @return
     * @throws AuthenticationException
     */
    @Override
    public String ocrDriverLicense(byte[] fileByte) throws AuthenticationException {
        File imageFile = new File("/data/" + System.currentTimeMillis() + ".png");
        try {
            FileUtils.writeByteArrayToFile(imageFile, fileByte);
        } catch (IOException e) {
            logger.error("图片数组转文件失败", e);
            throw new AuthenticationException(StatusCode.UPDATE_FAILED);
        }
        String driverLicenseInfo;
        //请求头
        Map<String,Object> headers = new HashMap<>(1);
        String accessToken = getAccessToken();
        if (StringUtils.isBlank(accessToken)) {
            throw new AuthenticationException(StatusCode.GET_ACCESS_TOKEN_ERROR);
        }
        headers.put("Authorization","Bearer "+ accessToken);
        //参数
        Map<String,Object> params = new HashMap<>(0);
        try {
            driverLicenseInfo = HttpClientUtils.httpPostRequest(BussinessConstants.SAIC_DRIVER_LICENSE_OCR,headers, params,imageFile,"image_file");
            if(StringUtils.isBlank(driverLicenseInfo)){
                //如果请返回处理的结果为空,默认为是token失效，重新获取
                JedisUtil.del(BussinessConstants.ACCESS_TOKEN_KEY);
                accessToken = getAccessToken();
                //再次进行ocr识别
                headers.put("Authorization","Bearer "+ accessToken);
                driverLicenseInfo = HttpClientUtils.httpPostRequest(BussinessConstants.SAIC_DRIVER_LICENSE_OCR,headers, params,imageFile,"image_file");
            }
            if (StringUtils.isBlank(driverLicenseInfo)) {
                throw new AuthenticationException(StatusCode.DRIVER_LICENSE_OCR_FAILURE);
            }
        } catch (Exception e) {
            logger.error("驾照识别失败 "+e.getMessage(),e);
            throw new AuthenticationException(StatusCode.DRIVER_LICENSE_OCR_FAILURE);
        } finally {
            FileUtils.deleteQuietly(imageFile);
        }
        return driverLicenseInfo;
    }

    @Override
    @Deprecated
    public void updateMemberInfoFromFlyingPig(UpdateMemberInfoFromFlyingPigInputDto inputDto) throws AuthenticationException {
        /**
         * 1.校验参数
         */
        if (inputDto.getPkId() == null || StringUtils.isBlank(inputDto.getName()) || StringUtils.isBlank(inputDto.getDriverCode())
                || StringUtils.isBlank(inputDto.getObtainDriverTimer()) || StringUtils.isBlank(inputDto.getLicenseExpirationTime()) || StringUtils.isBlank(inputDto.getDrivingLicenseType())
                || StringUtils.isBlank(inputDto.getDrivingLicenseImgUrl()) || StringUtils.isBlank(inputDto.getFileNoImgUrl()) || StringUtils.isBlank(inputDto.getFileNo())
                || StringUtils.isBlank(inputDto.getFaceRecognitionImgUrl()) || StringUtils.isBlank(inputDto.getIdCardNumber()) || StringUtils.isBlank(inputDto.getIdCardPicUrl())) {
            logger.error("飞猪更新用户信息失败，参数缺失：", inputDto.toString());
            throw new AuthenticationException(StatusCode.PARAM_EMPTY);
        }
        Integer driverLicenseInputType = inputDto.getDriverLicenseInputType();
        if (driverLicenseInputType == null || driverLicenseInputType < 0 || driverLicenseInputType > 2) {
            logger.error("飞猪更新用户信息失败，参数缺失：", inputDto.toString());
            throw new AuthenticationException(StatusCode.PARAM_EMPTY);
        }
        Integer reviewStatus = inputDto.getReviewStatus();
        if (reviewStatus == null || reviewStatus < 0 || reviewStatus > 4) {
            logger.error("飞猪更新用户信息失败，参数缺失：", inputDto.toString());
            throw new AuthenticationException(StatusCode.PARAM_EMPTY);
        }
        Integer authenticationStatus = inputDto.getAuthenticationStatus();
        if (authenticationStatus == null || authenticationStatus < 0 || authenticationStatus > 2) {
            logger.error("飞猪更新用户信息失败，参数缺失：", inputDto.toString());
            throw new AuthenticationException(StatusCode.PARAM_EMPTY);
        }
        /**
         * 2.查询用户信息
         */
        MembershipInfoWithBLOBs membershipInfo = membershipInfoMapper.selectByPrimaryKey(inputDto.getPkId());
        if (membershipInfo == null) {
            logger.error("飞猪更新用户信息失败，用户不存在：", inputDto.toString());
            throw new AuthenticationException(StatusCode.USER_INFO_NO_EXIST);
        }
        /**
         * 3.判断用户状态
         */
        if (membershipInfo.getReviewStatus() == 1 && membershipInfo.getAuthenticationStatus() == 2) {
            logger.error("飞猪更新用户信息失败，用户状态已通过，不需要重新认证：", inputDto.toString());
            throw new AuthenticationException(StatusCode.SUBMIT_FAILURE);
        }
        /**
         * 4.更新用户信息
         */
        MembershipInfoWithBLOBs updateRow = new MembershipInfoWithBLOBs();
        BeanUtils.copyProperties(inputDto, updateRow);
        updateRow.setReviewStatus(reviewStatus.shortValue());
        updateRow.setIdcardPicUrl(inputDto.getIdCardPicUrl());
        updateRow.setNational(BussinessConstants.CHINA_NATIONAL);
        if (reviewStatus == 1) {
            updateRow.setReviewMode(2);
        } else {
            updateRow.setReviewMode(1);
        }
        int updateRs = membershipInfoMapper.updateByPrimaryKeySelective(updateRow);
        if (updateRs <= 0) {
            logger.error("飞猪更新用户信息失败，数据库更新失败：", inputDto.toString());
            throw new AuthenticationException(StatusCode.UPDATE_FAILED);
        }
        /**
         * 5.记录操作日志
         */
        String authId = membershipInfo.getAuthId();
        if (inputDto.getAuthenticationStatus() == 2) {
            //保存操作日志
            UserOperationLogInput userOperationLogInput = new UserOperationLogInput();
            userOperationLogInput.setOperationType(1L);
            userOperationLogInput.setOperationContent("人脸认证通过");
            userOperationLogInput.setOperationTime(new Date());
            userOperationLogInput.setOperator(authId);
            userOperationLogInput.setRefKey1("Fliggy");
            userOperationLogInput.setAuthId(authId);
            this.saveUserOperationLog(userOperationLogInput);
            //审核通过需要制卡
            String content = "提交人工审核";
            if (reviewStatus == 1) {
                content = "自动审核通过";
                setVirtualCard(authId,membershipInfo.getMembershipType());
                HidLog.membership(LogPoint.MEMBER_AUTH_SUCCESS, "自动认证通过", authId);
                userOperationLogInput.setOperationType(2L);
                userOperationLogInput.setOperationContent(content);
                this.saveUserOperationLog(userOperationLogInput);
            }
            insertOperatorLog(content, authId, "", authId);
        } else {
            /**
             * 5.若不成功，则写入日志
             */
            UserOperationLogInput userOperationLogInput = new UserOperationLogInput();
            userOperationLogInput.setOperationType(17L);
            userOperationLogInput.setOperationContent("人脸认证失败");
            userOperationLogInput.setOperationTime(new Date());
            userOperationLogInput.setOperator(authId);
            userOperationLogInput.setRefKey1("Fliggy");
            userOperationLogInput.setAuthId(authId);
            this.saveUserOperationLog(userOperationLogInput);
            HidLog.membership(LogPoint.MEMBER_AUTH_FAIL, "自动认证不通过", membershipInfo.getAuthId());
        }
    }

    @Override
    public boolean insertUserOperateLog(UserOperatorLogDTO userOperatorLogDTO) {
        if(userOperatorLogDTO != null){
            UserOperatorLog userOperatorLog = new UserOperatorLog();
            BeanUtils.copyProperties(userOperatorLogDTO,userOperatorLog);
            int num = userOperatorLogMapper.saveSelective(userOperatorLog);
            return num > 0;
        }
        return false;
    }

	@Override
	public DriverLicenseValidResultDto checkDriverLicenseValid(String name, String cardNo, String archviesNo) {
		return licenseAuthenticateService.checkDriverLicenseValid(name, cardNo, archviesNo);
	}

    @Override
    public Boolean isExistMemberClauseLog(String authId) {
        MembershipClauseLogExample example = new MembershipClauseLogExample();
        example.createCriteria().andAuthidEqualTo(authId);
        int i = membershipClauseLogMapper.countByExample(example);
        return i > 0;
    }

    @Autowired
    private AppConfigRpcService appConfigRpcService;
    public boolean checkCityIsAutoLicenseAuth(String cityName){
        try{
            Integer status = appConfigRpcService.queryDriverLienseConfigByName(cityName);
            boolean enableAutoCheck = NumberUtils.INTEGER_ONE.equals(status);
            return enableAutoCheck;
        }catch (Exception ex) {
            logger.error("获取城市是否开启三要素自动验证配置失败, cityName=" + cityName, ex);
        }
        return false;
    }

    /**
     * 用户表埋点
     * @param p
     */
    public void traceMember(MembershipBasicInfo p) {
        try {
            // 渠道会员 之前的用户埋点报错，暂时屏蔽
            if (p.getMembershipType() != 0) {
                return;
            }

            Map<String, Object> userProperties = new HashMap<>();
            userProperties.put("user_id", p.getAuthId());

            userProperties.put("register_city", p.getCityOfOrigin());
            String orderPlatform = StringUtils.EMPTY;
            if (StringUtils.isNotBlank(p.getAppKey())) {
                AppKeyDto appKeyDto = platformService.getAppKey(p.getAppKey());
                if (appKeyDto != null) {
                    orderPlatform = appKeyDto.getPlatName();
                }
            }
            userProperties.put("register_platform", orderPlatform);
            userProperties.put("register_source", p.getAppKey());

            //注册时间
            String regTime = StringUtils.EMPTY;
            if (StringUtils.isNotBlank(p.getRegTime())) {
                regTime = ComUtil.getFormatDate(p.getRegTime(), ComUtil.DATE_TYPE4, ComUtil.DATE_TYPE1);
            }
            userProperties.put("register_time_modified", regTime);

            //审核时间
            String reviewTime = StringUtils.EMPTY;
            if (StringUtils.isNotBlank(p.getReviewTime())) {
                reviewTime = ComUtil.getFormatDate(p.getReviewTime(), ComUtil.DATE_TYPE4, ComUtil.DATE_TYPE1);
            }
            userProperties.put("verify_time", reviewTime);

            //所属公司
            String orgId = p.getOrgId();
            String orgDesc = StringUtils.EMPTY;
            if(StringUtils.isNotBlank(orgId)) {
                OrgInfo orgInfo = orgService.selectByOrgid(orgId);
                if (orgInfo != null) {
                    orgDesc = orgInfo.getOrgName();
                }
            }
            userProperties.put("company_name", orgDesc);

            //用户类别
            String national = p.getNational();
            String userType = StringUtils.EMPTY;
            if (StringUtils.isNotBlank(national)) {
                userType = "外籍用户";
                if (national.contains(BussinessConstants.CHINA_NATIONAL)) {
                    userType = "大陆用户";
                } else if (national.contains(BussinessConstants.NATIONAL_HK_MACAO_TAIWAN)) {
                    userType = BussinessConstants.NATIONAL_HK_MACAO_TAIWAN;
                }
            }
            userProperties.put("user_type", userType);

            //身份认证状态  1未认证(未上传) 2未刷脸 3待认证(待人工认证) 4已认证 5认证不通过
            Integer authenticationStatusNew = p.getAuthenticationStatusNew();
            String authenticationStatusDesc;
            if (authenticationStatusNew == null) {
                authenticationStatusDesc = "未认证";
            }else{
                switch (authenticationStatusNew){
                    case 1 : authenticationStatusDesc = "未认证";break;
                    case 2: authenticationStatusDesc = "未刷脸";break;
                    case 3 : authenticationStatusDesc = "待认证";break;
                    case 4 : authenticationStatusDesc = "已认证";break;
                    case 5 : authenticationStatusDesc = "认证不通过";break;
                    default: authenticationStatusDesc = "未认证";
                }
            }
            userProperties.put("user_identity_status", authenticationStatusDesc);

            //会员审核状态 审核状态(-1:资料不全 0:待审核 1：审核通过 2: 审核不通过 3：用户无效 4:重新审核）
            Integer reviewStatus = p.getReviewStatus();
            String reviewStatusDesc;
            if (reviewStatus == null) {
                reviewStatusDesc = "资料不全";
            }else{
                switch (reviewStatus){
                    case 0 : reviewStatusDesc = "资料不全";break;
                    case 1: reviewStatusDesc = "审核通过";break;
                    case 2 : reviewStatusDesc = "审核不通过";break;
                    case 3 : reviewStatusDesc = "资料不全";break;
                    case 4 : reviewStatusDesc = "资料不全";break;
                    default: reviewStatusDesc = "资料不全";
                }
            }
            userProperties.put("review_status", reviewStatusDesc);

            //驾照认证状态(1:未认证 2:待认证 3:已认证 4:认证不通过)
            Integer licenseReviewStatus = p.getLicenseReviewStatus();
            String status;
            if (licenseReviewStatus == null) {
                status = "未认证";
            }else{
                switch (licenseReviewStatus){
                    case 1: status = "未认证";break;
                    case 2 : status = "待认证";break;
                    case 3 : status = "已认证";break;
                    case 4 : status = "认证不通过";break;
                    default: status = "未认证";
                }
            }
            userProperties.put("driving_license_verify_status",status);

            //关联企业
            String agencyId = p.getAgencyId();
            String agencyDesc = StringUtils.EMPTY;
            if(StringUtils.isNotBlank(agencyId) && !"00".equalsIgnoreCase(agencyId)){
                OrgInfo orgInfo = orgService.selectByOrgid(agencyId);
                if (orgInfo != null) {
                    agencyDesc = orgInfo.getOrgName();
                }
            }
            userProperties.put("relate_company", agencyDesc);

            //会员卡状态  已激活、已暂停、已注销
            String membershipCardStatusDesc = StringUtils.EMPTY;
            CardInfoDTO cardInfoDTO = memberCardService.queryCardInfo(p.getCardNo());
            if(cardInfoDTO != null){
                if(p.getStatus() == 0){
                    if(cardInfoDTO.getActivateStatus() == 1){
                        membershipCardStatusDesc = "已激活";
                    }else {
                        membershipCardStatusDesc = "未激活";
                    }
                }else if(p.getStatus() == 1){
                    membershipCardStatusDesc = "已暂停";
                }else if(p.getStatus() == 2){
                    membershipCardStatusDesc = "已注销";
                }
            }
            userProperties.put("member_card_status", membershipCardStatusDesc);

            //会员状态(账号状态标识，0正常 1冻结中(注销冻结) 2已删除(注销完成)')  user_membershipStatus	字符串	正常、已注销
            Integer accountStatus = p.getAccountStatus();
            String accountStatusDesc;
            switch (accountStatus){
                case 0 : accountStatusDesc = "正常";break;
                case 1: accountStatusDesc = "冻结中";break;
                case 2 : accountStatusDesc = "已注销";break;
                default: accountStatusDesc = "无";
            }
            userProperties.put("member_status", accountStatusDesc);

            //押金等级
            String depositLevelDesc = StringUtils.EMPTY;
            QueryDepositInfoInputDTO inputDTO = new QueryDepositInfoInputDTO();
            inputDTO.setAuthId(p.getAuthId());
            DepositInfoDto depositInfoDto = memberDepositService.queryDepositInfo(inputDTO);
            if (depositInfoDto != null) {
                depositLevelDesc = depositInfoDto.getDepositLevelDesc();
            }
            userProperties.put("deposit_level", depositLevelDesc);

            //支付宝授权
            boolean aliPayWithholdingAuthorizationFlag = false;
            try {
                MmpUserTagDto userTagDto = this.queryUserTagByAuthId(p.getAuthId());
                if (userTagDto != null && StringUtils.isNotBlank(userTagDto.getSpare4())) {
                    aliPayWithholdingAuthorizationFlag = true;
                }
            } catch (Exception e) {
                logger.error(p.getAuthId() + "用户没有tag信息", e);
            }
            userProperties.put("alipy_authorize", aliPayWithholdingAuthorizationFlag);

            //驾照类型
            String drivingLicenseType = p.getDrivingLicenseType();
            userProperties.put("license_type", drivingLicenseType);

            //驾照有效期
            String licenseExpirationTime = p.getLicenseExpirationTime();
            userProperties.put("expired_date", licenseExpirationTime);

            //首次发证日期
            String obtainDriverTimer = p.getObtainDriverTimer();
            userProperties.put("certification_date", obtainDriverTimer);

            sensorsdataService.profileSet(p.getAuthId(),true, userProperties);
        } catch (Exception e) {
            logger.error("会员" + p.getAuthId() +"公共属性埋点失败", e);
        }
    }

    @Override
    public UserOrgInfoDto getMemberOrgInfo(String authId, Integer type) {
        UserOrgInfoDto orgInfoDto = membershipInfoMapper.getUserOrgInfo(authId, type);
        if (orgInfoDto == null && type == 0) {
            logger.info("getMemberOrgInfo兼容擎路用户,authId={}",authId);
            return getMemberOrgInfo(authId,2);
        }
        if(orgInfoDto != null && StringUtils.isBlank(orgInfoDto.getOrgId())) {
            logger.error("会员{}, city={}无对应归属企业，视为归属环球", authId, orgInfoDto.getCityOfOrigin());
            OrgInfo org = orgService.selectByOrgid("00");
            if(org != null) {
                orgInfoDto.setOrgId(org.getOrgId());
                orgInfoDto.setOrgName(org.getOrgName());
                orgInfoDto.setOrgSeq(org.getId());
            }
        }
        return orgInfoDto;
    }

    @Override
    public void updateUserStudentCardUrl(String authId, String studentCardUrl) {
        if (StringUtils.isNotBlank(authId) && StringUtils.isNotBlank(studentCardUrl)) {
            mmpUserTagMapper.updateUserStudentCardUrl(authId, studentCardUrl);
        }
    }

    @Override
    public Integer getUserAge(Long pkId) {
        int ageYear = 0;
        MembershipBasicInfo userInfo = this.getUserBasicInfoByPkId(pkId);
        if (userInfo != null) {
            if (userInfo.getReviewStatus() == 1 &&
                    userInfo.getAuthenticationStatus() == 2 &&
                    userInfo.getLicenseElementsAuthStatus() == 1 && userInfo.getNational().contains(BussinessConstants.CHINA_NATIONAL)) {
                String idCardNum = userInfo.getDriverCode();
                if (userInfo.getIdType() != null && userInfo.getIdType() == 5) {
                    idCardNum = userInfo.getIdCardNumber();
                }
                if (StringUtils.isNotBlank(idCardNum) && idCardNum.length() >= 14) {
                    String birthDay = idCardNum.substring(6, 14);
                    if (StringUtils.isNotBlank(birthDay)) {
                        Date birthDayDate = ComUtil.getDateFromStr(birthDay, "yyyyMMdd");
                        if (birthDayDate != null) {
                            Date today = new Date();
                            long age = (today.getTime() - birthDayDate.getTime()) / 1000;
                            ageYear = (int) (age / 60 / 60 / 24 / 365);
                        }
                    }
                }
            }
        }
        return ageYear;
    }

    @Override
    public DrivingLicenseOcrRespDto getDrivingLicenseOcr(DrivingLicenseOcrRequestDto drivingLicenseOcrRequestDto) {
        CommonDrivingLicenseOcrReq req = new CommonDrivingLicenseOcrReq();
        BeanUtils.copyProperties(drivingLicenseOcrRequestDto, req);
        req.setCommonSide(CommonSideEnums.getBySide(drivingLicenseOcrRequestDto.getDriving_license_side()));
        CommonDrivingLicenseOcrResp resp = ocrAdapter.drivingLicenseOcr(req);
        //logger.info("OCR识别: input={}, result={}", JSON.toJSONString(req), JSON.toJSONString(resp));
        DrivingLicenseOcrRespDto drivingLicenseOcrRespDto = null;
        if (!ObjectUtils.isEmpty(resp)) {
            drivingLicenseOcrRespDto = new DrivingLicenseOcrRespDto();
            BeanUtils.copyProperties(resp, drivingLicenseOcrRespDto);

            drivingLicenseOcrRespDto.setLicense_number(resp.getLicenseNo());
            drivingLicenseOcrRespDto.setDrivetype(resp.getDriveType());
            drivingLicenseOcrRespDto.setIssue_date(resp.getIssueDate());
            drivingLicenseOcrRespDto.setValid_from(resp.getStartDate());
            drivingLicenseOcrRespDto.setValid_date(resp.getEndDate());
            drivingLicenseOcrRespDto.setIssued_by(resp.getIssuedBy());
            drivingLicenseOcrRespDto.setFile_number(resp.getFileNo());
            drivingLicenseOcrRespDto.setRecord(resp.getRecord());

            if (StringUtils.isNoneBlank(resp.getStartDate(), resp.getEndDate())) {
                if ("长期".equals(resp.getEndDate())) {
                    drivingLicenseOcrRespDto.setValid_for("长期");
                } else {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
                    LocalDate startDate = LocalDate.parse(resp.getStartDate(), formatter);
                    LocalDate endDate = LocalDate.parse(resp.getEndDate(), formatter);
                    drivingLicenseOcrRespDto.setValid_for(String.valueOf(endDate.getYear() - startDate.getYear()) + "年");
                }
            }
        }
        return drivingLicenseOcrRespDto;
    }

    @Override
    public int insertSelective(UserFaceContrastResultDto recordDto) {
        UserFaceContrastResult model=new UserFaceContrastResult();
        BeanUtils.copyProperties(recordDto,model);
        return userFaceContrastResultMapper.insertSelective(model);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addMembershipInfoForMmp(MembershipForMmpDTO membershipForMmpDTO) {
        try {
            MembershipInfoWithBLOBs membershipInfo = new MembershipInfoWithBLOBs();
            BeanCopyUtils.copyProperties(membershipForMmpDTO, membershipInfo);
            //String authId = membershipForMmpDTO.getMobilePhone() + ComUtil.getSystemDate(ComUtil.DATE_TYPE6);
            String authId = membershipForMmpDTO.getAuthId();
            membershipInfo.setAuthId(authId);

            /** AuthenticationStatus 认证状态 0 未认证 1 未刷脸 2 已认证 */
            /** ReviewStatus 会员状态（-1:资料不全 0:待审核 1：审核通过 2: 审核不通过 3：用户无效 4:重新审核）
             * LicenseReviewStatus  驾照认证状态(1:未认证 2:待认证 3:已认证 4:认证不通过)
             * */
            //驾照状态变为待认证
            membershipInfo.setLicenseReviewStatus(2);
            //勾选了
            if (StringUtils.isBlank(membershipForMmpDTO.getFaceRecognitionImgUrl())) {
                //人脸照片为空 未刷脸
                membershipInfo.setReviewStatus((short) -1);
                membershipInfo.setAuthenticationStatus(1);
                membershipInfo.setNeedFace(2);
            }else{
                membershipInfo.setReviewStatus((short) 0);
                membershipInfo.setAuthenticationStatus(2);
                //增加是否需要刷脸 1-不需要刷脸  2-需要刷脸
                membershipInfo.setNeedFace(1);
            }

            membershipInfo.setDriverCode(membershipForMmpDTO.getDrivingLicense());
            String password = membershipForMmpDTO.getDrivingLicense().substring(membershipForMmpDTO.getDrivingLicense().length() - 6, membershipForMmpDTO.getDrivingLicense().length());
            // 加密
            password = Crypto.encryptDES(password, ENCRYPT_KEY);
            membershipInfo.setPassword(password);
            membershipInfo.setStatus((short) 0);
            membershipInfo.setMembershipType((short) 0);
//                    newMember.setReviewStatus(0);
            membershipInfo.setExemptDeposit((short) 0);
            membershipInfo.setAuthKind("1");
            membershipInfo.setReviewItems(BussinessConstants.REVIEW_ITEMS_INIT);
            membershipInfo.setCreatedUser(membershipForMmpDTO.getCreateOperName());
            membershipInfo.setUpdatedUser(membershipForMmpDTO.getCreateOperName());
            // 国籍
            if (StringUtils.equals(membershipForMmpDTO.getUserType(), "0")) {
                membershipInfo.setNational("外国");
            } else if (StringUtils.equals(membershipForMmpDTO.getUserType(), "2")) {
                membershipInfo.setNational("港澳台");
            } else {
                membershipInfo.setNational("中国");
            }
            // 会员所属公司
            String orgId = "";
            List<String> orgList = cityMapper.queryCityOrg(membershipForMmpDTO.getCityOfOrigin());
            System.out.println(orgList);
            if (!CollectionUtils.isEmpty(orgList)) {
                orgId = orgList.get(0);
            } else {
                orgId = "000T";
            }
            membershipInfo.setOrgId(orgId);
            // 会员来源
            membershipInfo.setDataOrigin((short) 2);
            // 注册渠道
            membershipInfo.setAppKey(APP_KEY_EVCARD_MMP);
            // 关联企业
            membershipInfo.setAgencyId(membershipForMmpDTO.getAgencyId());
            // 注册时间
            Date createTime = new Date();
            membershipInfo.setRegTime(ComUtil.getFormatDate(createTime, ComUtil.DATE_TYPE4));
            membershipInfo.setCreatedTime(ComUtil.getFormatDate(createTime, ComUtil.DATE_TYPE3));
            membershipInfo.setUpdatedTime(ComUtil.getFormatDate(createTime, ComUtil.DATE_TYPE3));

            //判断证件是否为长期有效
            //获取mid
            membershipInfo.setMid(midGenerator.getMid());

            membershipInfo.setManufacturer(BussinessConstants.MANUFACTURER_EVCARD_MMP);
            //操作数据库
            try {
                memberDocumentManager.addMemberForMmp(membershipForMmpDTO,membershipInfo);
            } catch (BusinessException e) {
                logger.error(ComUtil.getExceptionMsg(e));
                return "fail";
            }

            //add by elin 20180415 注册后绑定会员优惠券
            com.extracme.evcard.rpc.coupon.dto.BaseResponse baseResponse =
                    couponServ.bindCouponToAuthUser(authId, membershipForMmpDTO.getMobilePhone(), membershipForMmpDTO.getCreateOperId());
            if (null != baseResponse) {
                if (baseResponse.getCode() != 0) {
                    logger.info("注册后绑定会员优惠券返回：" + JSON.toJSONString(baseResponse) +
                            "，authId: " + authId + "，mobilePhone ：" + membershipForMmpDTO.getMobilePhone());
                }
            }
            try {
                //推送会员注册事件，推向memberShip服务中RawMemberListener，tag为MEMBER_REGISTER消费 update by Elin  date 2018/03/14
                logger.debug("会员注册事件推送开始----authId:" + authId);
                MemberRegister memberRegister = new MemberRegister();
                memberRegister.setAuthId(authId);
                memberRegister.setAppKey(APP_KEY_EVCARD_MMP);
                memberRegister.setMemberType(0);
                memberRegister.setMobilePhone(membershipInfo.getMobilePhone());
                memberRegister.setArea(membershipInfo.getArea());
                memberRegister.setProvince(membershipInfo.getProvince());
                memberRegister.setCity(membershipInfo.getCity());
                memberRegister.setCreatedTime(createTime);
                byte[] messagebody = ProtobufUtil.serializeProtobuf(memberRegister);
                Message msg = new Message(evcardRawDataTopic, EventEnum.MEMBER_REGISTER.getTag(), messagebody);
                producer.send(msg);
                logger.debug("会员注册事件推送结束----authId:" + authId);
            } catch (Exception e) {
                logger.error("推送会员注册事件失败，authId=" + authId, e);
            }
            // 新增神策注册事件埋点
            try {
                Map<String, Object> properties = new HashMap<String, Object>();
                properties.put("authenticationTime", new Date());
                sensorsdataService.profileSet(authId, true, properties);

                // 神策管理平台用户注册事件埋点
                Map<String, Object> properties1 = new HashMap<String, Object>();
                properties1.put("inviterId", "");
                properties1.put("result", true);
                properties1.put("registerType", "管理平台");
                sensorsdataService.track(authId, true, "CompleteRegister", properties1);
            } catch (Exception e) {
                logger.error("神策数据埋点-记录事件时捕获到异常", e);
            }
            String foreignKey = authId;
            String operatorContent = "新建会员";
            try {
                ComUtil.insertOperatorLog(operatorContent, foreignKey, membershipForMmpDTO.getCreateOperId(), membershipForMmpDTO.getCreateOperName(), userOperatorLogMapper);
                //记录日志
                UserOperationLogInput idCardLogInput = getLogInput(membershipForMmpDTO, MemOperateTypeEnum.OCR,membershipInfo.getPkId());
                UserOperationLogInput licLogInput = getLogInput(membershipForMmpDTO, MemOperateTypeEnum.FACE_REC,membershipInfo.getPkId());
                memberShipService.saveUserOperationLog(idCardLogInput);
                memberShipService.saveUserOperationLog(licLogInput);
            } catch (Exception e) {
                logger.error("会员管理系统-新增日志异常<{}>,authid{}", e,authId);
            }
            return "success";
        } catch (Exception e) {
            logger.error(ComUtil.getExceptionMsg(e));
            return "fail";
        }
    }

    /**
     * 操作日志
     * @param membershipForMmpDTO
     * @param memOperateTypeEnum
     * @return
     */
    public UserOperationLogInput getLogInput(MembershipForMmpDTO membershipForMmpDTO,MemOperateTypeEnum memOperateTypeEnum,Long pkId){
        UserOperationLogInput logRecord = new UserOperationLogInput();
        logRecord.setAuthId(membershipForMmpDTO.getAuthId());
        logRecord.setUserId(pkId);
        logRecord.setMembershipType(0);
        logRecord.setOperationContent("会员管理系统手动新增会员");
        logRecord.setOperationType(memOperateTypeEnum.getCode());
        logRecord.setOperator(StringUtils.abbreviate(membershipForMmpDTO.getCreateOperName(), 20));
        logRecord.setOperatorId(Long.valueOf(membershipForMmpDTO.getCreateOperId()));
        logRecord.setRefKey1(BussinessConstants.APP_KEY_EVCARD_MMP);
        return logRecord;
    }


    /**
     * 用户表 状态全量更新
     * @param p
     */
    public void traceMemberForStatus(MembershipBasicInfo p) {
        try {
            Map<String, Object> userProperties = new HashMap<>();
            userProperties.put("user_id", p.getAuthId());
            //身份认证状态  1未认证(未上传) 2未刷脸 3待认证(待人工认证) 4已认证 5认证不通过
            Integer authenticationStatusNew = p.getAuthenticationStatusNew();
            String authenticationStatusDesc;
            if (authenticationStatusNew == null) {
                authenticationStatusDesc = "未认证";
            }else{
                switch (authenticationStatusNew){
                    case 1 : authenticationStatusDesc = "未认证";break;
                    case 2: authenticationStatusDesc = "未刷脸";break;
                    case 3 : authenticationStatusDesc = "待认证";break;
                    case 4 : authenticationStatusDesc = "已认证";break;
                    case 5 : authenticationStatusDesc = "认证不通过";break;
                    default: authenticationStatusDesc = "未认证";
                }
            }
            userProperties.put("user_identity_status", authenticationStatusDesc);

            //会员审核状态 审核状态(-1:资料不全 0:待审核 1：审核通过 2: 审核不通过 3：用户无效 4:重新审核）
            Integer reviewStatus = p.getReviewStatus();
            String reviewStatusDesc;
            if (reviewStatus == null) {
                reviewStatusDesc = "资料不全";
            }else{
                switch (reviewStatus){
                    case 0 : reviewStatusDesc = "资料不全";break;
                    case 1: reviewStatusDesc = "审核通过";break;
                    case 2 : reviewStatusDesc = "审核不通过";break;
                    case 3 : reviewStatusDesc = "资料不全";break;
                    case 4 : reviewStatusDesc = "资料不全";break;
                    default: reviewStatusDesc = "资料不全";
                }
            }
            userProperties.put("review_status", reviewStatusDesc);

            //驾照认证状态(1:未认证 2:待认证 3:已认证 4:认证不通过)
            Integer licenseReviewStatus = p.getLicenseReviewStatus();
            String status;
            if (licenseReviewStatus == null) {
                status = "未认证";
            }else{
                switch (licenseReviewStatus){
                    case 1: status = "未认证";break;
                    case 2 : status = "待认证";break;
                    case 3 : status = "已认证";break;
                    case 4 : status = "认证不通过";break;
                    default: status = "未认证";
                }
            }
            userProperties.put("driving_license_verify_status",status);
            sensorsdataService.profileSet(p.getAuthId(),true, userProperties);
        } catch (Exception e) {
            logger.error("会员" + p.getAuthId() +"易观状态更新", e);
        }
    }

    @Override
    public GetMemberByTokenDto getMembershipBasicInfoByToken(String token) throws BusinessException {
        try {
            GetMemberByTokenDto result = new GetMemberByTokenDto();
            if(StringUtils.isBlank(token)){
                throw new BusinessException(-1,"参数不能为空");
            }
            UserToken userToken = userTokenMapper.selectByToken(token);
            if (userToken == null) {
                throw new BusinessException(-1,"未找到登录记录");
            }
            UserTokenDto userTokenDto = new UserTokenDto();
            BeanUtils.copyProperties(userToken,userTokenDto);

            Date expiresTime = userToken.getExpiresTime();
            if(expiresTime != null && expiresTime.before(new Date())){
                throw new BusinessException(-1,"登录已失效，请重新登录");
            }

            Long userId = userToken.getUserId();
            MembershipBasicInfo membershipBasicInfo = getUserBasicInfoByPkId(userId);
            if (membershipBasicInfo == null) {
                throw new BusinessException(-1,"未找到对应的用户");
            }
            result.setUserToken(userTokenDto);
            result.setMembershipBasicInfo(membershipBasicInfo);
            return result;
        } catch (Exception e) {
            logger.error("通过token去查询用户信息异常，token={}",token,e);
            if(e instanceof BusinessException){
                throw e;
            }else{
                throw new BusinessException(-1,"验证失败");
            }
        }
    }

    @Override
    public void updateSecondAppKey(String authId, String secondAppKey) {
        if (StringUtils.isNotBlank(secondAppKey)) {
            SecondAppKeyManager secondAppKeyManager = secondAppKeyManagerMapper.selectBySecondAppKey(secondAppKey);
            if (secondAppKeyManager != null) {
                MembershipInfo membershipInfo = new MembershipInfo();
                membershipInfo.setAuthId(authId);
                membershipInfo.setAppKey(secondAppKeyManager.getFirstAppKey());
                membershipInfo.setSecondAppKey(secondAppKey);
                membershipInfo.setPlatformId(secondAppKeyManager.getPlatformId());
                membershipInfoMapper.updateSecondAppKey(membershipInfo);
            }else{
                logger.error("二级渠道未查询到");
                throw new MemberException(StatusCode.APPKEY_INVALID);
            }
        }
        else {
            logger.error("二级渠道为空");
        }
    }
}
