package com.extracme.evcard.membership.third.baidu;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class PersonVerifySecResult extends PersonVerifyResult {

    public static final Integer SUCCESS = 0;
    public static final Integer NOT_MATCH = 1;
    public static final Integer IMAGE_NOT_EXIST = 2;
    /**
     * 认证状态，取值：
     * 0 姓名与身份证匹配，可进行人脸比对
     * 1 身份证号与姓名不匹配或该身份证号不存在
     * 2 公安网图片不存在或质量过低
     */
    @JSONField(name = "verify_status")
    private Integer verifyStatus;

    /**
     * 对SDK传入的加密图片进行解密。
     * 当场景为APP时，此参数为解密后的人脸图片信息
     */
    @JSONField(name = "dec_image")
    private String decImage;
}
