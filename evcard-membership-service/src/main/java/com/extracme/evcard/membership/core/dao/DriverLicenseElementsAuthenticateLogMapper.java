package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.model.DriverLicenseElementsAuthenticateLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DriverLicenseElementsAuthenticateLogMapper {
    int deleteByPrimaryKey(Long id);

    int insert(DriverLicenseElementsAuthenticateLog record);

    int insertSelective(DriverLicenseElementsAuthenticateLog record);

    DriverLicenseElementsAuthenticateLog selectByPrimaryKey(Long id);

    DriverLicenseElementsAuthenticateLog selectByRecordId(@Param("recordId") Long recordId,@Param("logType") Integer logType);

    int updateByPrimaryKeySelective(DriverLicenseElementsAuthenticateLog record);

    int updateByPrimaryKey(DriverLicenseElementsAuthenticateLog record);

    /**
     * 根据recordId列表查询认证明细
     * @param recordIds
     * @param logType
     * @return
     */
    List<DriverLicenseElementsAuthenticateLog> selectByRecordIds(@Param("list") List<Long> recordIds, @Param("logType") Integer logType, @Param("limit") Integer limit);
}