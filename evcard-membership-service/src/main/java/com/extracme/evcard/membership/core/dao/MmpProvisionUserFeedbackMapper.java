package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.model.MmpProvisionUserFeedback;
import com.extracme.evcard.rpc.dto.Page;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface MmpProvisionUserFeedbackMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MmpProvisionUserFeedback record);

    int insertSelective(MmpProvisionUserFeedback record);

    MmpProvisionUserFeedback selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MmpProvisionUserFeedback record);

    int updateByPrimaryKey(MmpProvisionUserFeedback record);

    List<MmpProvisionUserFeedback> queryPageByNodeId(@Param("nodeId") String nodeId, @Param("provisionType") int type,
                                                     @Param("startDate") Date startDate,
                                                     @Param("endDate") Date endDate,
                                                     @Param("page") Page page);

    Integer countPageByNodeId(@Param("nodeId") String nodeId, @Param("provisionType") int type,
                              @Param("startDate") Date startDate, @Param("endDate") Date endDate);


    List<MmpProvisionUserFeedback> queryByNodeId(@Param("nodeId") String nodeId, @Param("provisionType") int type,
                                                     @Param("startDate") Date startDate,
                                                     @Param("endDate") Date endDate,
                                                     @Param("id") Long id);
}