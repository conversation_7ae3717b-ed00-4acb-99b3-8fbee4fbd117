package com.extracme.evcard.membership.mq;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.extracme.evcard.membership.core.dto.UserTitlePointsOfferSingleDto;
import com.extracme.evcard.membership.credit.dto.MemberPointsOfferDto;
import com.extracme.evcard.membership.credit.dto.MemberPointsOfferResp;
import com.extracme.evcard.membership.credit.service.IMemberPointsService;
import com.extracme.evcard.mq.bean.EventEnum;
import com.extracme.evcard.mq.bean.event.MemberPointsPush;
import com.extracme.evcard.protobuf.ProtobufUtil;
import com.extracme.evcard.rpc.order.service.IOrderService;
import com.extracme.evcard.rpc.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 会员信用体系消费者
 * <AUTHOR>
 * @Discription
 * @date 2020/8/12
 */
@Slf4j
@Component("memberCreditsListener")
public class MemberCreditsListener implements MessageListener {

    @Resource
    private IMemberPointsService memberPointsService;

    @Autowired
    IOrderService orderService;

    /**
     * member.points.offer.enable = 0则，
     * 推送积分发放事件，保留推送记录，但不调用第三方发放接口
     * 可通过重置消费位点恢复2日内积分信息
     */
    @Value("${member.points.offer.enable}")
    private String MEMBER_POINTS_OFFER_ENABLE;

    @Override
    public Action consume(Message message, ConsumeContext context) {
        String tag = message.getTag();
        log.info("消费: " + message.getKey() + "," + message.getMsgID() + ", 标签: " + message.getTag());
        try{
            //会员积分发放消费
            if(StringUtils.equalsIgnoreCase(tag, EventEnum.MEMBER_POINTS_PUSH.toString())) {
                MemberPointsPush pointsPushInfo = new MemberPointsPush();
                ProtobufUtil.deserializeProtobuf(message.getBody(), pointsPushInfo);

                MemberPointsOfferDto offerDto = new MemberPointsOfferDto();
                BeanCopyUtils.copyProperties(pointsPushInfo, offerDto);
                if(StringUtils.isBlank(offerDto.getPointsType())) {
                    offerDto.setPointsType("01");
                }
                //是否执行发放操作的开关
                if(!StringUtils.equals("0", MEMBER_POINTS_OFFER_ENABLE)) {
                    MemberPointsOfferResp resp = memberPointsService.offerPoints(offerDto);
                    if(NumberUtils.INTEGER_ONE.equals(resp.getRetry())) {
                        log.warn("消费消息失败：tag={}, messageId={}, resultCode={}, message={}，需要重试....", message.getTag(),
                                message.getMsgID(), resp.getCode(), resp.getMessage());
                        return Action.ReconsumeLater;
                    }
                    log.debug("消费MEMBER_POINTS_PUSH消息成功，消息体={}, result={}", JSON.toJSONString(pointsPushInfo), JSON.toJSONString(resp));
                }
            }else if(StringUtils.equalsIgnoreCase(tag, EventEnum.MEMBER_TITLE_REWARD_POINTS.toString())) {
                UserTitlePointsOfferSingleDto offerDto = new UserTitlePointsOfferSingleDto();
                ProtobufUtil.deserializeProtobuf(message.getBody(), offerDto);
                //是否执行发放操作的开关
                if(!StringUtils.equals("0", MEMBER_POINTS_OFFER_ENABLE)) {
                    try {
                        MemberPointsOfferResp resp = memberPointsService.offerTitleRewardPointsSync(offerDto);
                        log.debug("消费MEMBER_TITLE_REWARD_POINTS消息成功，消息体={}, result={}",
                                JSON.toJSONString(offerDto), JSON.toJSONString(resp));
                    }catch (Exception e) {
                        log.warn("消费消息失败：tag=" + message.getTag() +", messageId=" + message.getMsgID()
                    + "需要重试....", e);
                        return Action.ReconsumeLater;
                    }
                }
            }
        }
        catch (Exception ex) {
            log.warn("消费消息异常: " +  message.getTag() + "-" + message.getMsgID(), ex);
            return Action.ReconsumeLater;
        }
        return Action.CommitMessage;
    }
}
