package com.extracme.evcard.membership.core.service;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.dao.ReportFeedbackInfoMapper;
import com.extracme.evcard.membership.core.dto.FeedbackBaseDto;
import com.extracme.evcard.membership.core.dto.FeedbackReplyMess;
import com.extracme.evcard.membership.core.dto.UpdataUndealReport;
import com.extracme.evcard.membership.core.dto.input.DealFeedbackInput;
import com.extracme.evcard.membership.core.exception.ExceptionEnum;
import com.extracme.evcard.membership.core.exception.MemberException;
import com.extracme.evcard.membership.core.input.*;
import com.extracme.evcard.membership.core.model.ReportFeedbackInfo;
import com.extracme.evcard.rpc.shop.dto.ShopInfoDto;
import com.extracme.evcard.rpc.shop.input.ReportElectricPileFeedbackInput;
import com.extracme.evcard.rpc.shop.input.ReportRecommendShopInput;
import com.extracme.evcard.rpc.shop.service.IShopFeedbackService;
import com.extracme.evcard.rpc.shop.service.IShopService;
import com.extracme.evcard.rpc.vehicle.dto.ReportVehicleFeedbackInputDto;
import com.extracme.evcard.rpc.vehicle.dto.VehicleBaseDTO;
import com.extracme.evcard.rpc.vehicle.dto.input.DamagePictureInput;
import com.extracme.evcard.rpc.vehicle.dto.input.DealVehicleFeedbackInput;
import com.extracme.evcard.rpc.vehicle.service.IVehicleFeedbackService;
import com.extracme.evcard.rpc.vehicle.service.IVehicleService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.extracme.evcard.rpc.shop.input.*;

import javax.annotation.Resource;
import java.util.*;

@Service("memberFeedbackService")
public class MemberFeedbackServiceImpl implements IMemberFeedbackService{

    Logger logger = (Logger) LoggerFactory.getLogger(MemberFeedbackServiceImpl.class);

    @Autowired
    private ReportFeedbackInfoMapper reportFeedbackInfoMapper;

    @Resource(name = "vehicleFeedbackService")
    private IVehicleFeedbackService vehicleFeedbackService;

    @Resource(name = "shopFeedbackService")
    private IShopFeedbackService shopFeedbackService;

    @Resource(name = "shopService")
    private IShopService shopService;

    @Resource
    private IVehicleService vehicleService;

    @Override
    public void reportVehicleMalfunction(ReportVehicleMalfunctionInput input) {
        logger.info("车辆故障上报，入参：" + JSON.toJSONString(input));
        //1.校验参数
        if (StringUtils.isBlank(input.getAuthId())
                || StringUtils.isBlank(input.getFeedbackDesc()) || StringUtils.isBlank(input.getImageUrls())
                || StringUtils.isBlank(input.getVehicleNo())) {
            throw new MemberException(ExceptionEnum.PARAM_ERROR.getErrCode(),ExceptionEnum.PARAM_ERROR.getErrMsg());
        }
        //判断上报车辆是否是订单车辆
        Integer isOrder = 0;
        VehicleBaseDTO vehicleBaseDTO = vehicleService.getVehBaseByVehNo(input.getVehicleNo());
        if (StringUtils.isNotBlank(input.getVehicleNo()) && StringUtils.isNotBlank(input.getVin())){
            if (vehicleBaseDTO != null && StringUtils.isNotBlank(vehicleBaseDTO.getVehicleNo()) &&
                    StringUtils.isNotBlank(input.getVin()) && input.getVin().equals(vehicleBaseDTO.getVin())){
                isOrder = 1;
            }
        }
        Integer reportStatus = 0;
        //判断车辆是否为b2c分时出库状态
        if (vehicleBaseDTO == null || vehicleBaseDTO.getRenttype() == null || !vehicleBaseDTO.getRenttype().equals(0)
                || vehicleBaseDTO.getVehicleStatus() == null || !vehicleBaseDTO.getVehicleStatus().equals(2)){
            //非短租分时出库状态
            reportStatus = 3;
        }
        //2.新增问题上报信息 总表
        ReportFeedbackInfo insertReportInfo = new ReportFeedbackInfo();
        BeanUtils.copyProperties(input, insertReportInfo);
        insertReportInfo.setReportType(1);
        insertReportInfo.setReportStatus(reportStatus);
        insertReportInfo.setInfoType(0);
        insertReportInfo.setIsRead(1);
        insertReportInfo.setUpdateOperName(input.getCreateOperName());
        insertReportInfo.setReplyTime(null);
        insertReportInfo.setIsOrderVehicle(isOrder);
        int insertRs = reportFeedbackInfoMapper.insertSelective(insertReportInfo);
        //3.新增上报车辆故障信息表
        if (insertRs > 0) {
            ReportVehicleFeedbackInputDto vehicleFeedbackInputDto = new ReportVehicleFeedbackInputDto();
            BeanUtils.copyProperties(input, vehicleFeedbackInputDto);
            vehicleFeedbackInputDto.setReportFeedbackInfoId(insertReportInfo.getId());
            vehicleFeedbackInputDto.setReportStatus(reportStatus);
            vehicleFeedbackInputDto.setIsOrderVehicle(isOrder);
            vehicleFeedbackService.reportVehicleFeedback(vehicleFeedbackInputDto);
        }
    }

    @Override
    public void reportElectricPileMalfunction(ReportElectricPileMalfunctionInput input) {
        logger.info("充电桩故障上报，入参：" + JSON.toJSONString(input));
        //1.校验参数
        if (StringUtils.isBlank(input.getAuthId())
                || StringUtils.isBlank(input.getFeedbackDesc()) || StringUtils.isBlank(input.getImageUrls())
                || input.getShopSeq() == null || input.getShopSeq() <= 0) {
            throw new MemberException(ExceptionEnum.PARAM_ERROR.getErrCode(),ExceptionEnum.PARAM_ERROR.getErrMsg());
        }
        //2.新增问题上报信息 总表
        ShopInfoDto shopInfo = shopService.getShopInfoById(input.getShopSeq());
        if (shopInfo == null) {
            throw new MemberException(ExceptionEnum.SHOP_NOT_EXIST.getErrCode(),ExceptionEnum.SHOP_NOT_EXIST.getErrMsg());
        }
        ReportFeedbackInfo insertReportInfo = new ReportFeedbackInfo();
        BeanUtils.copyProperties(input, insertReportInfo);
        insertReportInfo.setReportType(2);
        insertReportInfo.setReportStatus(0);
        insertReportInfo.setInfoType(0);
        insertReportInfo.setIsRead(1);
        insertReportInfo.setUpdateOperName(input.getCreateOperName());
        insertReportInfo.setReplyTime(null);
        insertReportInfo.setShopName(shopInfo.getShopName());
        int insertRs = reportFeedbackInfoMapper.insertSelective(insertReportInfo);
        //3.新增上报充电桩故障信息表
        if (insertRs > 0) {
            ReportElectricPileFeedbackInput electricPileFeedbackInput = new ReportElectricPileFeedbackInput();
            BeanUtils.copyProperties(input, electricPileFeedbackInput);
            electricPileFeedbackInput.setReportFeedbackInfoId(insertReportInfo.getId());
            shopFeedbackService.reportElectricPileFeedback(electricPileFeedbackInput);
        }
    }

    @Override
    public void reportRecommendBuildShop(ReportRecommendBuildShopInput input) {
        logger.info("推荐建网点上报，入参：" + JSON.toJSONString(input));
        //1.校验参数
        if (StringUtils.isBlank(input.getAuthId())
                || StringUtils.isBlank(input.getFeedbackDesc())
                || StringUtils.isBlank(input.getShopAddressName()) || StringUtils.isBlank(input.getShopAddressDesc())) {
            throw new MemberException(ExceptionEnum.PARAM_ERROR.getErrCode(),ExceptionEnum.PARAM_ERROR.getErrMsg());
        }
        //2.新增推荐建网点上报信息 总表
        ReportFeedbackInfo insertReportInfo = new ReportFeedbackInfo();
        BeanUtils.copyProperties(input, insertReportInfo);
        insertReportInfo.setReportType(0);
        insertReportInfo.setReportStatus(0);
        insertReportInfo.setInfoType(0);
        insertReportInfo.setIsRead(1);
        insertReportInfo.setUpdateOperName(input.getCreateOperName());
        int insertRs = reportFeedbackInfoMapper.insertSelective(insertReportInfo);
        //3.新增推荐建网点上报信息表
        if (insertRs > 0) {
            ReportRecommendShopInput recommendShopInput = new ReportRecommendShopInput();
            BeanUtils.copyProperties(input, recommendShopInput);
            recommendShopInput.setReportFeedbackInfoId(insertReportInfo.getId());
            shopFeedbackService.reportRecommendShop(recommendShopInput);
        }
    }

    @Override
    public void reportOtherProblem(ReportOtherProblemInput input) {
        logger.info("其他问题上报，入参：" + JSON.toJSONString(input));
        //1.校验参数
        if (StringUtils.isBlank(input.getAuthId())
                || StringUtils.isBlank(input.getFeedbackDesc())) {
            throw new MemberException(ExceptionEnum.PARAM_ERROR.getErrCode(),ExceptionEnum.PARAM_ERROR.getErrMsg());
        }
        //2.新增意见反馈信息 总表
        ReportFeedbackInfo insertReportInfo = new ReportFeedbackInfo();
        BeanUtils.copyProperties(input, insertReportInfo);
        insertReportInfo.setReportType(3);
        insertReportInfo.setReportStatus(0);
        insertReportInfo.setInfoType(0);
        insertReportInfo.setIsRead(1);
        insertReportInfo.setUpdateOperName(input.getCreateOperName());
        reportFeedbackInfoMapper.insertSelective(insertReportInfo);
    }

    @Override
    public  List<FeedbackBaseDto> queryFeedBackInfo(FeedbackBaseInput feedbackBaseInput) {
//        if(StringUtils.isBlank(feedbackBaseInput.getAuthId())){
//            return null;
//        }
        List<Integer> types = new ArrayList<>();
        if (feedbackBaseInput.getReportType() == null){
            Integer[] type = {0,1,2,3};
            types = Arrays.asList(type);
            feedbackBaseInput.setTypes(types);
        }else {
            types.add(feedbackBaseInput.getReportType());
            feedbackBaseInput.setTypes(types);
        }
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -90);
        Date queryDate = cal.getTime();
        feedbackBaseInput.setQueryDate(queryDate);
        List<FeedbackBaseDto> feedbackBaseDtos = reportFeedbackInfoMapper.getFeedbackByAuthId(feedbackBaseInput);
        if (CollectionUtils.isNotEmpty(feedbackBaseDtos)){
            for (FeedbackBaseDto feedbackBaseDto : feedbackBaseDtos){
                List<FeedbackReplyMess> feedbackReplyMesses = reportFeedbackInfoMapper.getFeedbackReplyById(feedbackBaseDto.getId());
                if (CollectionUtils.isEmpty(feedbackReplyMesses)){
                    continue;
                }
                feedbackBaseDto.setReplyMessage(feedbackReplyMesses);
            }
        }
        //查询车损信息
        if(StringUtils.isNotBlank(feedbackBaseInput.getAuthId())) {
            List<DamagePictureInput> damagePictureList = vehicleFeedbackService.queryUserDamageInfoList(feedbackBaseInput.getAuthId(), queryDate);
            if (CollectionUtils.isNotEmpty(damagePictureList)) {
                for (DamagePictureInput row : damagePictureList) {
                    FeedbackBaseDto dto = new FeedbackBaseDto();
                    dto.setAuthId(row.getAuthId());
                    dto.setVehicleNo(row.getVehicleNo());
                    dto.setFeedbackDesc(row.getDamageDesc());
                    dto.setReportType(1);
                    dto.setInfoType(0);
                    dto.setImageUrls(row.getPicture());
                    dto.setCreateTime(ComUtil.getFormatDate(row.getCreateTime(),ComUtil.DATE_TYPE8));
                    feedbackBaseDtos.add(dto);
                }
                Collections.sort(feedbackBaseDtos);
            }
        }
        return feedbackBaseDtos;
    }

    @Override
    public List<FeedbackBaseDto> queryFeedBackInfoPage(QueryFeedbackInput queryFeedbackInput) {
        Integer offset = (queryFeedbackInput.getPageNum() - 1) * queryFeedbackInput.getPageSize();
        if (offset < 0 ){
            offset = 0;
        }
        List<Integer> types = new ArrayList<>();
        if (CollectionUtils.isEmpty(queryFeedbackInput.getTypes())){
            Integer[] type = {0,1,2,3};
            types = Arrays.asList(type);
            queryFeedbackInput.setTypes(types);
        }
        queryFeedbackInput.setPageNum(offset);
        List<FeedbackBaseDto> feedbackBaseDtos = reportFeedbackInfoMapper.getFeedbackPage(queryFeedbackInput);
        if (CollectionUtils.isNotEmpty(feedbackBaseDtos)){
            setFeedbackReplyMess(feedbackBaseDtos);
        }
        return feedbackBaseDtos;
    }

    public void setFeedbackReplyMess(List<FeedbackBaseDto> feedbackBaseDtos){
            for (FeedbackBaseDto feedbackBaseDto : feedbackBaseDtos){
                List<FeedbackReplyMess> feedbackReplyMesses = reportFeedbackInfoMapper.getFeedbackReplyById(feedbackBaseDto.getId());
                if (CollectionUtils.isEmpty(feedbackReplyMesses)){
                    continue;
                }
                feedbackBaseDto.setReplyMessage(feedbackReplyMesses);
            }
    }

    @Override
    public void dealFeedback(DealFeedbackInput dealFeedbackInput) {
        logger.info("调用处理反馈信息，id:{}",JSON.toJSONString(dealFeedbackInput));
        if (!(dealFeedbackInput.getType().equals(1) || dealFeedbackInput.getType().equals(2))){
            throw new MemberException(ExceptionEnum.PARAM_ERROR.getErrCode(),ExceptionEnum.PARAM_ERROR.getErrMsg());
        }
        if (StringUtils.isBlank(dealFeedbackInput.getUpdateUser()) || dealFeedbackInput.getId() ==null){
            throw new MemberException(ExceptionEnum.PARAM_ERROR.getErrCode(),ExceptionEnum.PARAM_ERROR.getErrMsg());
        }

        Integer feedbackType = dealFeedbackInput.getFeedbackType();
        switch (feedbackType){
            case 0:
                //网点推荐
                DealRecommendShopInput dealRecommendShopInput = new DealRecommendShopInput();
                BeanUtils.copyProperties(dealFeedbackInput,dealRecommendShopInput);
                shopFeedbackService.dealReportRecommendShop(dealRecommendShopInput);
                createReplyMessage(dealFeedbackInput);
                break;
            case 1:
                //车辆故障
                DealVehicleFeedbackInput dealVehicleFeedbackInput = new DealVehicleFeedbackInput();
                BeanUtils.copyProperties(dealFeedbackInput,dealVehicleFeedbackInput);
                vehicleFeedbackService.dealFeedBack(dealVehicleFeedbackInput);
                createReplyMessage(dealFeedbackInput);
                break;
            case 2:
                //充电桩故障
                DealElecFeedbackInput elecFeedbackInput = new DealElecFeedbackInput();
                BeanUtils.copyProperties(dealFeedbackInput,elecFeedbackInput);
                shopFeedbackService.dealFeedBack(elecFeedbackInput);
                createReplyMessage(dealFeedbackInput);
                break;
            case 3:
                createReplyMessage(dealFeedbackInput);
                break;
            default:
                break;
        }
    }

    @Override
    public void updateFeedbackRead(FeedbackReadInput readInput) {
        if (StringUtils.isBlank(readInput.getAuthId())){
            throw new MemberException(ExceptionEnum.PARAM_ERROR.getErrCode(),ExceptionEnum.PARAM_ERROR.getErrMsg());
        }
        reportFeedbackInfoMapper.updateFeedbackRead(readInput);
    }

    @Override
    public int queryUnReadFeedbackReplyCount(String authId, String name) {
        return reportFeedbackInfoMapper.queryUnReadFeedbackReplyCount(authId, name);
    }

    void createReplyMessage(DealFeedbackInput dealFeedbackInput){
        UpdataUndealReport updataUndealReport = new UpdataUndealReport();
        updataUndealReport.setReportStatus(dealFeedbackInput.getType());
        updataUndealReport.setId(dealFeedbackInput.getId().longValue());
        updataUndealReport.setUpdateOperName(dealFeedbackInput.getUpdateUser());

        updataUndealReport.setUpdateOperId(dealFeedbackInput.getUpdateUserId());
        updataUndealReport.setUpdateTime(new Date());
        reportFeedbackInfoMapper.updataUndealReport(updataUndealReport);

        //判断处理是否有回复消息，无回复消息的不进行插入
        if (StringUtils.isBlank(dealFeedbackInput.getDesc())){
            return;
        }
        FeedbackBaseDto feedbackBaseDto = reportFeedbackInfoMapper.queryFeedbackInfoById(dealFeedbackInput.getId());
        ReportFeedbackInfo reportFeedbackInfo = new ReportFeedbackInfo();
        reportFeedbackInfo.setAuthId(feedbackBaseDto.getAuthId());
        reportFeedbackInfo.setName(feedbackBaseDto.getName());
        reportFeedbackInfo.setReportType(feedbackBaseDto.getReportType());
        reportFeedbackInfo.setInfoType(1);
        reportFeedbackInfo.setReportFeedbackInfoId(dealFeedbackInput.getId().longValue());
        reportFeedbackInfo.setReplyDesc(dealFeedbackInput.getDesc());
        reportFeedbackInfo.setUpdateOperName(dealFeedbackInput.getUpdateUser());
        reportFeedbackInfo.setUpdateOperId(dealFeedbackInput.getUpdateUserId());
        reportFeedbackInfo.setReplyTime(new Date());
        reportFeedbackInfo.setTaskId(dealFeedbackInput.getTaskId());
        reportFeedbackInfo.setReportStatus(dealFeedbackInput.getType());
        reportFeedbackInfoMapper.insertSelective(reportFeedbackInfo);
    }
}
