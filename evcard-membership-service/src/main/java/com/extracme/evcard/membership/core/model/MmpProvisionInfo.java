package com.extracme.evcard.membership.core.model;

import java.util.Date;

public class MmpProvisionInfo {
    private Long id;

    private Integer provisionType;

    private String version;

    private Date startTime;

    private Date endTime;

    private Integer provisionStatus;

    private Long publisherId;

    private String provisionAddress;

    private String miscDesc;

    private Integer status;

    private Date createTime;

    private Long createOperId;

    private String createOperName;

    private Date updateTime;

    private Long updateOperId;

    private String updateOperName;

    private String wordAddress;

    private String provisionPic;

    private String content;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getProvisionType() {
        return provisionType;
    }

    public void setProvisionType(Integer provisionType) {
        this.provisionType = provisionType;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getProvisionStatus() {
        return provisionStatus;
    }

    public void setProvisionStatus(Integer provisionStatus) {
        this.provisionStatus = provisionStatus;
    }

    public Long getPublisherId() {
        return publisherId;
    }

    public void setPublisherId(Long publisherId) {
        this.publisherId = publisherId;
    }

    public String getProvisionAddress() {
        return provisionAddress;
    }

    public void setProvisionAddress(String provisionAddress) {
        this.provisionAddress = provisionAddress;
    }

    public String getMiscDesc() {
        return miscDesc;
    }

    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateOperId() {
        return createOperId;
    }

    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    public String getCreateOperName() {
        return createOperName;
    }

    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdateOperId() {
        return updateOperId;
    }

    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    public String getUpdateOperName() {
        return updateOperName;
    }

    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }

    public String getWordAddress() {
        return wordAddress;
    }

    public void setWordAddress(String wordAddress) {
        this.wordAddress = wordAddress;
    }

    public String getProvisionPic() {
        return provisionPic;
    }

    public void setProvisionPic(String provisionPic) {
        this.provisionPic = provisionPic;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}