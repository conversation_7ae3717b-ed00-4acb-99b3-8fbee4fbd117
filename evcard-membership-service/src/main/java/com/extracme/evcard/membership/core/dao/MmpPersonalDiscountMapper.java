package com.extracme.evcard.membership.core.dao;


import com.extracme.evcard.membership.core.dto.agency.PersonalDiscountDTO;
import com.extracme.evcard.membership.core.model.MmpPersonalDiscount;

import java.util.List;

public interface MmpPersonalDiscountMapper{

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_personal_discount
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    MmpPersonalDiscount selectByPrimaryKey(Long id);

    /**
     *
     * @param id
     * @return
     */
    List<PersonalDiscountDTO> findPersonalList(Long id);
}