package com.extracme.evcard.membership.contract.service.html;

import com.extracme.evcard.membership.contract.service.AddRentCarContractRequest;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import lombok.Data;

import java.io.Serializable;

@Data
public class RenderHtmlContext implements Serializable {

    private static final long serialVersionUID = 1L;

    private String initHtml;

    private AddRentCarContractRequest addRequest;
    private String signImgHtml;
    private MembershipBasicInfo membershipBasicInfo;
}
