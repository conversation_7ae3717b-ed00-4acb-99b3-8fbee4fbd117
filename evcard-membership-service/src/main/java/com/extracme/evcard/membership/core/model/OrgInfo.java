package com.extracme.evcard.membership.core.model;

public class OrgInfo {
    private Long id;

    private String orgId;

    private String orgName;

    private String orgKind;

    private Double orgClass;

    private String contact;

    private String tel;

    private String mobilePhone;

    private String address;

    private String mail;

    private String licenseNo;

    private String fax;

    private String county;

    private Double city;

    private Double province;

    private String corporate;

    private String location;

    private String rtoln;

    private String licenseNoImgUrl;

    private String taxRegistrationImgUrl;

    private String orgCodeImgUrl;

    private String remark;

    private Double payWay;

    private String createdUser;

    private String createdTime;

    private String updatedUser;

    private String updatedTime;

    private Double status;

    private Double origin;

    private Double deposit;

    private Double reserveAmount;

    private Double rentMins;

    private String agencyId;

    private String cityShort;

    private Integer insideFlag;

    private String orgAlias;

    private String checkDate;

    private Integer checkAlert;

    private String balanceMail;

    private Byte orgProperty;

    private Boolean orgProtrety;

    private String orgSubName;

    private Integer zhiMaCreditFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgKind() {
        return orgKind;
    }

    public void setOrgKind(String orgKind) {
        this.orgKind = orgKind;
    }

    public Double getOrgClass() {
        return orgClass;
    }

    public void setOrgClass(Double orgClass) {
        this.orgClass = orgClass;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getMail() {
        return mail;
    }

    public void setMail(String mail) {
        this.mail = mail;
    }

    public String getLicenseNo() {
        return licenseNo;
    }

    public void setLicenseNo(String licenseNo) {
        this.licenseNo = licenseNo;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public Double getCity() {
        return city;
    }

    public void setCity(Double city) {
        this.city = city;
    }

    public Double getProvince() {
        return province;
    }

    public void setProvince(Double province) {
        this.province = province;
    }

    public String getCorporate() {
        return corporate;
    }

    public void setCorporate(String corporate) {
        this.corporate = corporate;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getRtoln() {
        return rtoln;
    }

    public void setRtoln(String rtoln) {
        this.rtoln = rtoln;
    }

    public String getLicenseNoImgUrl() {
        return licenseNoImgUrl;
    }

    public void setLicenseNoImgUrl(String licenseNoImgUrl) {
        this.licenseNoImgUrl = licenseNoImgUrl;
    }

    public String getTaxRegistrationImgUrl() {
        return taxRegistrationImgUrl;
    }

    public void setTaxRegistrationImgUrl(String taxRegistrationImgUrl) {
        this.taxRegistrationImgUrl = taxRegistrationImgUrl;
    }

    public String getOrgCodeImgUrl() {
        return orgCodeImgUrl;
    }

    public void setOrgCodeImgUrl(String orgCodeImgUrl) {
        this.orgCodeImgUrl = orgCodeImgUrl;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Double getPayWay() {
        return payWay;
    }

    public void setPayWay(Double payWay) {
        this.payWay = payWay;
    }

    public String getCreatedUser() {
        return createdUser;
    }

    public void setCreatedUser(String createdUser) {
        this.createdUser = createdUser;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public String getUpdatedUser() {
        return updatedUser;
    }

    public void setUpdatedUser(String updatedUser) {
        this.updatedUser = updatedUser;
    }

    public String getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(String updatedTime) {
        this.updatedTime = updatedTime;
    }

    public Double getStatus() {
        return status;
    }

    public void setStatus(Double status) {
        this.status = status;
    }

    public Double getOrigin() {
        return origin;
    }

    public void setOrigin(Double origin) {
        this.origin = origin;
    }

    public Double getDeposit() {
        return deposit;
    }

    public void setDeposit(Double deposit) {
        this.deposit = deposit;
    }

    public Double getReserveAmount() {
        return reserveAmount;
    }

    public void setReserveAmount(Double reserveAmount) {
        this.reserveAmount = reserveAmount;
    }

    public Double getRentMins() {
        return rentMins;
    }

    public void setRentMins(Double rentMins) {
        this.rentMins = rentMins;
    }

    public String getAgencyId() {
        return agencyId;
    }

    public void setAgencyId(String agencyId) {
        this.agencyId = agencyId;
    }

    public String getCityShort() {
        return cityShort;
    }

    public void setCityShort(String cityShort) {
        this.cityShort = cityShort;
    }

    public Integer getInsideFlag() {
        return insideFlag;
    }

    public void setInsideFlag(Integer insideFlag) {
        this.insideFlag = insideFlag;
    }

    public String getOrgAlias() {
        return orgAlias;
    }

    public void setOrgAlias(String orgAlias) {
        this.orgAlias = orgAlias;
    }

    public String getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(String checkDate) {
        this.checkDate = checkDate;
    }

    public Integer getCheckAlert() {
        return checkAlert;
    }

    public void setCheckAlert(Integer checkAlert) {
        this.checkAlert = checkAlert;
    }

    public String getBalanceMail() {
        return balanceMail;
    }

    public void setBalanceMail(String balanceMail) {
        this.balanceMail = balanceMail;
    }

    public Byte getOrgProperty() {
        return orgProperty;
    }

    public void setOrgProperty(Byte orgProperty) {
        this.orgProperty = orgProperty;
    }

    public Boolean getOrgProtrety() {
        return orgProtrety;
    }

    public void setOrgProtrety(Boolean orgProtrety) {
        this.orgProtrety = orgProtrety;
    }

    public String getOrgSubName() {
        return orgSubName;
    }

    public void setOrgSubName(String orgSubName) {
        this.orgSubName = orgSubName;
    }

    public Integer getZhiMaCreditFlag() {
        return zhiMaCreditFlag;
    }

    public void setZhiMaCreditFlag(Integer zhiMaCreditFlag) {
        this.zhiMaCreditFlag = zhiMaCreditFlag;
    }
}