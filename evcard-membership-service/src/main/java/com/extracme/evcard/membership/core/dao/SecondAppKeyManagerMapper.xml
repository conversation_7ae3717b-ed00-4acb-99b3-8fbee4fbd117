<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.core.dao.SecondAppKeyManagerMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.SecondAppKeyManager" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="second_app_key" property="secondAppKey" jdbcType="VARCHAR" />
    <result column="second_app_secret" property="secondAppSecret" jdbcType="VARCHAR" />
    <result column="second_app_key_name" property="secondAppKeyName" jdbcType="VARCHAR" />
    <result column="thirdId" property="thirdid" jdbcType="INTEGER" />
    <result column="first_app_key" property="firstAppKey" jdbcType="VARCHAR" />
    <result column="platform_id" property="platformId" jdbcType="BIGINT" />
    <result column="channel_purpose" property="channelPurpose" jdbcType="VARCHAR" />
    <result column="tax_main_company" property="taxMainCompany" jdbcType="VARCHAR" />
    <result column="org_id" property="orgId" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="is_deleted" property="isDeleted" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, second_app_key, second_app_secret, second_app_key_name, thirdId, first_app_key, 
    platform_id, channel_purpose, tax_main_company, org_id, remark, status, is_deleted, 
    create_time, create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.extracme.evcard.membership.core.model.SecondAppKeyManagerExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from siac.second_app_key_manager
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from siac.second_app_key_manager
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from siac.second_app_key_manager
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.membership.core.model.SecondAppKeyManager" >
    insert into siac.second_app_key_manager (id, second_app_key, second_app_secret,
      second_app_key_name, thirdId, first_app_key, 
      platform_id, channel_purpose, tax_main_company, 
      org_id, remark, status, 
      is_deleted, create_time, create_oper_id, 
      create_oper_name, update_time, update_oper_id, 
      update_oper_name)
    values (#{id,jdbcType=BIGINT}, #{secondAppKey,jdbcType=VARCHAR}, #{secondAppSecret,jdbcType=VARCHAR}, 
      #{secondAppKeyName,jdbcType=VARCHAR}, #{thirdid,jdbcType=INTEGER}, #{firstAppKey,jdbcType=VARCHAR}, 
      #{platformId,jdbcType=BIGINT}, #{channelPurpose,jdbcType=VARCHAR}, #{taxMainCompany,jdbcType=VARCHAR}, 
      #{orgId,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{isDeleted,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, 
      #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, 
      #{updateOperName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.core.model.SecondAppKeyManager" useGeneratedKeys="true" keyProperty="id">
    insert into siac.second_app_key_manager
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="secondAppKey != null" >
        second_app_key,
      </if>
      <if test="secondAppSecret != null" >
        second_app_secret,
      </if>
      <if test="secondAppKeyName != null" >
        second_app_key_name,
      </if>
      <if test="thirdid != null" >
        thirdId,
      </if>
      <if test="firstAppKey != null" >
        first_app_key,
      </if>
      <if test="platformId != null" >
        platform_id,
      </if>
      <if test="channelPurpose != null" >
        channel_purpose,
      </if>
      <if test="taxMainCompany != null" >
        tax_main_company,
      </if>
      <if test="orgId != null" >
        org_id,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="isDeleted != null" >
        is_deleted,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateOperId != null" >
        update_oper_id,
      </if>
      <if test="updateOperName != null" >
        update_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="secondAppKey != null" >
        #{secondAppKey,jdbcType=VARCHAR},
      </if>
      <if test="secondAppSecret != null" >
        #{secondAppSecret,jdbcType=VARCHAR},
      </if>
      <if test="secondAppKeyName != null" >
        #{secondAppKeyName,jdbcType=VARCHAR},
      </if>
      <if test="thirdid != null" >
        #{thirdid,jdbcType=INTEGER},
      </if>
      <if test="firstAppKey != null" >
        #{firstAppKey,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null" >
        #{platformId,jdbcType=BIGINT},
      </if>
      <if test="channelPurpose != null" >
        #{channelPurpose,jdbcType=VARCHAR},
      </if>
      <if test="taxMainCompany != null" >
        #{taxMainCompany,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null" >
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null" >
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        #{updateOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.extracme.evcard.membership.core.model.SecondAppKeyManagerExample" resultType="java.lang.Integer" >
    select count(*) from siac.second_app_key_manager
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update siac.second_app_key_manager
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.secondAppKey != null" >
        second_app_key = #{record.secondAppKey,jdbcType=VARCHAR},
      </if>
      <if test="record.secondAppSecret != null" >
        second_app_secret = #{record.secondAppSecret,jdbcType=VARCHAR},
      </if>
      <if test="record.secondAppKeyName != null" >
        second_app_key_name = #{record.secondAppKeyName,jdbcType=VARCHAR},
      </if>
      <if test="record.thirdid != null" >
        thirdId = #{record.thirdid,jdbcType=INTEGER},
      </if>
      <if test="record.firstAppKey != null" >
        first_app_key = #{record.firstAppKey,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null" >
        platform_id = #{record.platformId,jdbcType=BIGINT},
      </if>
      <if test="record.channelPurpose != null" >
        channel_purpose = #{record.channelPurpose,jdbcType=VARCHAR},
      </if>
      <if test="record.taxMainCompany != null" >
        tax_main_company = #{record.taxMainCompany,jdbcType=VARCHAR},
      </if>
      <if test="record.orgId != null" >
        org_id = #{record.orgId,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null" >
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createOperId != null" >
        create_oper_id = #{record.createOperId,jdbcType=BIGINT},
      </if>
      <if test="record.createOperName != null" >
        create_oper_name = #{record.createOperName,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateOperId != null" >
        update_oper_id = #{record.updateOperId,jdbcType=BIGINT},
      </if>
      <if test="record.updateOperName != null" >
        update_oper_name = #{record.updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update siac.second_app_key_manager
    set id = #{record.id,jdbcType=BIGINT},
      second_app_key = #{record.secondAppKey,jdbcType=VARCHAR},
      second_app_secret = #{record.secondAppSecret,jdbcType=VARCHAR},
      second_app_key_name = #{record.secondAppKeyName,jdbcType=VARCHAR},
      thirdId = #{record.thirdid,jdbcType=INTEGER},
      first_app_key = #{record.firstAppKey,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=BIGINT},
      channel_purpose = #{record.channelPurpose,jdbcType=VARCHAR},
      tax_main_company = #{record.taxMainCompany,jdbcType=VARCHAR},
      org_id = #{record.orgId,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{record.createOperId,jdbcType=BIGINT},
      create_oper_name = #{record.createOperName,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{record.updateOperId,jdbcType=BIGINT},
      update_oper_name = #{record.updateOperName,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.core.model.SecondAppKeyManager" >
    update siac.second_app_key_manager
    <set >
      <if test="secondAppKey != null" >
        second_app_key = #{secondAppKey,jdbcType=VARCHAR},
      </if>
      <if test="secondAppSecret != null" >
        second_app_secret = #{secondAppSecret,jdbcType=VARCHAR},
      </if>
      <if test="secondAppKeyName != null" >
        second_app_key_name = #{secondAppKeyName,jdbcType=VARCHAR},
      </if>
      <if test="thirdid != null" >
        thirdId = #{thirdid,jdbcType=INTEGER},
      </if>
      <if test="firstAppKey != null" >
        first_app_key = #{firstAppKey,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null" >
        platform_id = #{platformId,jdbcType=BIGINT},
      </if>
      <if test="channelPurpose != null" >
        channel_purpose = #{channelPurpose,jdbcType=VARCHAR},
      </if>
      <if test="taxMainCompany != null" >
        tax_main_company = #{taxMainCompany,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null" >
        org_id = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null" >
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.core.model.SecondAppKeyManager" >
    update siac.second_app_key_manager
    set second_app_key = #{secondAppKey,jdbcType=VARCHAR},
      second_app_secret = #{secondAppSecret,jdbcType=VARCHAR},
      second_app_key_name = #{secondAppKeyName,jdbcType=VARCHAR},
      thirdId = #{thirdid,jdbcType=INTEGER},
      first_app_key = #{firstAppKey,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=BIGINT},
      channel_purpose = #{channelPurpose,jdbcType=VARCHAR},
      tax_main_company = #{taxMainCompany,jdbcType=VARCHAR},
      org_id = #{orgId,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <!-- 取得渠道一览 -->
  <select id="getChannelManagement" resultType="com.extracme.evcard.membership.core.dto.SecondChannelListInfoDTO">
    SELECT
    a.id as id,
    a.second_app_key as secondAppKey,
    a.second_app_secret as secondAppSecret,
    a.second_app_key_name	as secondAppKeyName,
    b.ORG_NAME as orgName,
    date_format(a.create_time,'%Y-%m-%d')	as createTime,
    CASE WHEN c.username is null then a.create_oper_name
    ELSE CONCAT(a.create_oper_name,'(',c.username,')')
    END	as createUser,
    a.create_oper_id as createUserId,
    a.ORG_ID as orgId,
    a.status as status,
    a.channel_purpose as channelPurpose,
    a.first_app_key as firstAppKey,
    k.PLAT_NAME as firstAppKeyName,
    d.platform_name as platName,
    d.id as platformId,
    o.ORG_NAME as taxMainCompanyName,
    o.ORG_ID as taxMainCompany,
    a.remark
    FROM
    ${siacSchema}.second_app_key_manager a left join
    ${isvSchema}.org_info b on a.ORG_ID = b.ORG_ID left join
    ${mmpSchema}.mmp_user c on a.CREATE_OPER_ID = c.id
    left join ${issSchema}.mmp_platform_info d on d.id = a.platform_id
    left join ${siacSchema}.app_key_manager k on k.APP_KEY = a.first_app_key
    left join ${isvSchema}.org_info o on a.tax_main_company = o.ORG_ID
    WHERE
    1=1
    <!-- 渠道名称 -->
    <if test="secondAppKeyName!=null and secondAppKeyName!=''">
      and a.second_app_key_name like concat('%',#{secondAppKeyName,jdbcType=VARCHAR},'%')
    </if>
    <!-- 渠道Key -->
    <if test="secondAppKey!=null and secondAppKey!=''">
      and a.second_app_key like concat('%',#{secondAppKey,jdbcType=VARCHAR},'%')
    </if>
    <!-- 一级渠道Key -->
    <if test="firstAppKey!=null and firstAppKey!=''">
      and a.first_app_key like concat('%',#{firstAppKey,jdbcType=VARCHAR},'%')
    </if>
    <!-- 创建人 -->
    <if test="createdUser!=null and createdUser!=''">
      and a.create_oper_name= #{createdUser,jdbcType=VARCHAR}
    </if>
    <!-- 所属公司 -->
    <if test="orgId!=null and orgId!=''">
      and a.ORG_ID = #{orgId,jdbcType=VARCHAR}
    </if>
    <!-- 创建日期 -->
    <if test="startTime!=null and startTime!=''">
      and a.create_time &gt;=#{startTime,jdbcType=VARCHAR}
    </if>
    <!-- 创建日期 -->
    <if test="endTime!=null and endTime!=''">
      and a.create_time &lt;=#{endTime,jdbcType=VARCHAR}
    </if>
    <!-- 渠道所属平台 -->
    <if test="platformId !=null">
      and a.platform_id =#{platformId,jdbcType=BIGINT}
    </if>
    <!-- 渠道状态 -->
    <if test="status !=null">
      and a.status =#{status,jdbcType=INTEGER}
    </if>
    ORDER BY a.create_time DESC
  </select>

  <!-- 渠道信息条数 -->
  <select id="getChannelManagementNum" resultType="int">
    SELECT
    count(*)
    FROM
    ${siacSchema}.second_app_key_manager a left join
    ${isvSchema}.org_info b on a.ORG_ID = b.ORG_ID
    WHERE
    1=1
    <!-- 渠道名称 -->
    <if test="secondAppKeyName!=null and secondAppKeyName!=''">
      and a.second_app_key_name like concat('%',#{secondAppKeyName,jdbcType=VARCHAR},'%')
    </if>
    <!-- 渠道Key -->
    <if test="secondAppKey!=null and secondAppKey!=''">
      and a.second_app_key like concat('%',#{secondAppKey,jdbcType=VARCHAR},'%')
    </if>
    <!-- 一级渠道Key -->
    <if test="firstAppKey!=null and firstAppKey!=''">
      and a.first_app_key like concat('%',#{firstAppKey,jdbcType=VARCHAR},'%')
    </if>
    <!-- 创建人 -->
    <if test="createdUser!=null and createdUser!=''">
      and a.create_oper_name= #{createdUser,jdbcType=VARCHAR}
    </if>
    <!-- 所属公司 -->
    <if test="orgId!=null and orgId!=''">
      and a.ORG_ID = #{orgId,jdbcType=VARCHAR}
    </if>
    <!-- 创建日期 -->
    <if test="startTime!=null and startTime!=''">
      and a.create_time &gt;=#{startTime,jdbcType=VARCHAR}
    </if>
    <!-- 创建日期 -->
    <if test="endTime!=null and endTime!=''">
      and a.create_time &lt;=#{endTime,jdbcType=VARCHAR}
    </if>
    <!-- 渠道所属平台 -->
    <if test="platformId !=null">
      and a.platform_id =#{platformId,jdbcType=BIGINT}
    </if>
    <!-- 渠道状态 -->
    <if test="status !=null">
      and a.status =#{status,jdbcType=INTEGER}
    </if>
  </select>


  <!-- 渠道名称存在check -->
  <select id="checkPlatNameExist" resultType="int">
    SELECT
    count(*)
    FROM
    ${siacSchema}.second_app_key_manager
    WHERE
    second_app_key_name= #{secondAppKeyName,jdbcType=VARCHAR}
    <if test="secondAppKey!=null">
      AND second_app_key != #{secondAppKey,jdbcType=VARCHAR}
    </if>
  </select>

  <!-- APPKEY存在check -->
  <select id="checkAppKeyExist" resultType="int">
        SELECT
           count(*)
		FROM
			${siacSchema}.second_app_key_manager
		WHERE
			second_app_key= #{secondAppKey,jdbcType=VARCHAR}
	</select>

  <select id="selectBySecondAppKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from siac.second_app_key_manager
    where second_app_key = #{secondAppKey,jdbcType=VARCHAR}
  </select>

  <update id="disableAppKey" parameterType="string">
 	UPDATE siac.second_app_key_manager SET `STATUS` = 1 WHERE second_app_key = #{secondAppKey,jdbcType=VARCHAR} AND `STATUS` = 0
 </update>

  <update id="enableAppKey" parameterType="string">
 	UPDATE siac.app_key_manager SET `STATUS` = 0 WHERE second_app_key = #{secondAppKey,jdbcType=VARCHAR} AND `STATUS` = 1
 </update>

  <select id="selectListByFirstAppKey" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from siac.second_app_key_manager
    WHERE first_app_key = #{firstAppKey,jdbcType=VARCHAR}
    AND `STATUS` = 0
  </select>

  <select id="selectBySecondAppKeyDetailInfo"
          resultType="com.extracme.evcard.membership.core.dto.agency.SecondAppKeyDetailInfoDTO">
    SELECT
    a.id as id,
    a.second_app_key as secondAppKey,
    a.second_app_secret as secondAppSecret,
    a.second_app_key_name	as secondAppKeyName,
    a.create_oper_id as createUserId,
    a.ORG_ID as orgId,
    a.status as status,
    a.channel_purpose as channelPurpose,
    a.first_app_key as firstAppKey,
    k.PLAT_NAME as firstAppKeyName,
    d.platform_name as platName,
    d.id as platformId
    FROM
    ${siacSchema}.second_app_key_manager a
    left join ${issSchema}.mmp_platform_info d on d.id = a.platform_id
    left join ${siacSchema}.app_key_manager k on k.APP_KEY = a.first_app_key
    WHERE a.second_app_key = #{secondAppKey,jdbcType=VARCHAR}

  </select>
</mapper>