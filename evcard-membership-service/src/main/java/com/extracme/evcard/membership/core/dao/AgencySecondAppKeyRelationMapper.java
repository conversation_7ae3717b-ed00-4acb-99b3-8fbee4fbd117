package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.model.AgencySecondAppKeyRelation;
import com.extracme.evcard.membership.core.model.AgencySecondAppKeyRelationExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AgencySecondAppKeyRelationMapper {
    int countByExample(AgencySecondAppKeyRelationExample example);

    int deleteByPrimaryKey(Long id);

    int insert(AgencySecondAppKeyRelation record);

    int insertSelective(AgencySecondAppKeyRelation record);

    List<AgencySecondAppKeyRelation> selectByExample(AgencySecondAppKeyRelationExample example);

    AgencySecondAppKeyRelation selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") AgencySecondAppKeyRelation record, @Param("example") AgencySecondAppKeyRelationExample example);

    int updateByExample(@Param("record") AgencySecondAppKeyRelation record, @Param("example") AgencySecondAppKeyRelationExample example);

    int updateByPrimaryKeySelective(AgencySecondAppKeyRelation record);

    int updateByPrimaryKey(AgencySecondAppKeyRelation record);

    int deleteByAgencyId(@Param("agencyId") String agencyId, @Param("operatorId")Long operatorId, @Param("operatorName") String operatorName);

    List<AgencySecondAppKeyRelation> selectRelationByAgencyId(String agencyId);
    List<AgencySecondAppKeyRelation> selectBySecondAppKey(String secondAppKey);
}