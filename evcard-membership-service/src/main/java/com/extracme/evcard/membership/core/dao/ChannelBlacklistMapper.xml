<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.extracme.evcard.membership.core.dao.ChannelBlacklistMapper">
    <resultMap id="BaseResultMap"
               type="com.extracme.evcard.membership.core.model.ChannelBlacklist">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="user_name" jdbcType="VARCHAR" property="userName" />
        <result column="mobile_phone" jdbcType="VARCHAR" property="mobilePhone" />
        <result column="blacklist_type" jdbcType="INTEGER" property="blacklistType" />
        <result column="blacklist_source" jdbcType="INTEGER" property="blacklistSource" />
        <result column="certificate_type" jdbcType="INTEGER" property="certificateType" />
        <result column="certificate_num" jdbcType="VARCHAR" property="certificateNum" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="enable_status" jdbcType="INTEGER" property="enableStatus" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    </resultMap>
    <sql id="Base_Column_List">
        id, user_name, mobile_phone, blacklist_type, certificate_type, certificate_num, remark,
        enable_status, create_time, create_by, update_time, update_by
    </sql>
    <select id="listChannelBlacklist" parameterType="com.extracme.evcard.membership.core.dto.blacklist.ListChannelBlacklistDto"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM ${siacSchema}.channel_blacklist
        <where>
        <if test='userName != null and userName != ""'>
            AND user_name = #{userName,jdbcType=VARCHAR}
        </if>
        <if test='mobilePhone != null and mobilePhone != ""'>
            AND mobile_phone = #{mobilePhone,jdbcType=VARCHAR}
        </if>
        <if test='certificateNum != null and certificateNum != ""'>
            AND certificate_num = #{certificateNum,jdbcType=VARCHAR}
        </if>
        <if test='enableStatus != null and enableStatus != ""'>
            AND enable_status = #{enableStatus,jdbcType=INTEGER}
        </if>
        </where>
        ORDER BY create_time DESC, id DESC
        <if test="pageNum != null and pageSize != null">
            LIMIT ${offset} ,#{pageSize}
        </if>
    </select>

    <select id="countChannelBlacklist" parameterType="com.extracme.evcard.membership.core.dto.blacklist.ListChannelBlacklistDto"
            resultType="java.lang.Integer">
        select
        count(1)
        from ${siacSchema}.channel_blacklist
        <where>
            <if test='userName != null and userName != ""'>
                and user_name = #{userName,jdbcType=VARCHAR}
            </if>
            <if test='mobilePhone != null and mobilePhone != ""'>
                and mobile_phone = #{mobilePhone,jdbcType=VARCHAR}
            </if>
            <if test='certificateNum != null and certificateNum != ""'>
                and certificate_num = #{certificateNum,jdbcType=VARCHAR}
            </if>
            <if test='enableStatus != null and enableStatus != ""'>
                and enable_status = #{enableStatus,jdbcType=INTEGER}
            </if>
        </where>

    </select>

    <insert id="insert" parameterType="com.extracme.evcard.membership.core.model.ChannelBlacklist"  useGeneratedKeys="true" keyProperty="id">
        insert into ${siacSchema}.`channel_blacklist`
        (`user_name`,
        `mobile_phone`,
        `blacklist_type`,
        `blacklist_source`,
        `certificate_type`,
        `certificate_num`,
        `remark`,
        `enable_status`,
        `create_time`,
        `create_by`,
        `update_time`,
        `update_by`)
        VALUES (#{userName,jdbcType=VARCHAR},
        #{mobilePhone,jdbcType=VARCHAR},
        #{blacklistType,jdbcType=INTEGER},
        #{blacklistSource,jdbcType=INTEGER},
        #{certificateType,jdbcType=INTEGER},
        #{certificateNum,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enableStatus,jdbcType=INTEGER},
        #{createTime,jdbcType=TIMESTAMP},
        #{createBy,jdbcType=VARCHAR},
        #{updateTime,jdbcType=TIMESTAMP},
        #{updateBy,jdbcType=VARCHAR});
    </insert>

    <update id="updateStatusById" parameterType="com.extracme.evcard.membership.core.model.ChannelBlacklist">
        update ${siacSchema}.`channel_blacklist`
        <set>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="enableStatus != null">
                enable_status = #{enableStatus,jdbcType=INTEGER},
            </if>
<!--            <if test="mobilePhone != null">-->
                mobile_phone = #{mobilePhone,jdbcType=VARCHAR},
<!--            </if>-->
            <if test="certificateNum != null">
                certificate_num = #{certificateNum,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="getById" resultMap="BaseResultMap"
            parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List" />
        from ${siacSchema}.channel_blacklist
        where id = #{id,jdbcType=BIGINT}
    </select>



    <select id="queryByMobileOrCertificateNum" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM ${siacSchema}.channel_blacklist
        <where>
            <if test="mobilePhone != null or certificateNum != null">
                (
                <if test="mobilePhone != null">
                    mobile_phone = #{mobilePhone,jdbcType=VARCHAR}
                    <if test="certificateNum != null"> or </if>
                </if>
                <if test="certificateNum != null">
                    certificate_num = #{certificateNum,jdbcType=VARCHAR}
                </if>
                )
            </if>
            and enable_status = 1
        </where>
    </select>

    <select id="queryByMobileAndCertificateNum" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM ${siacSchema}.channel_blacklist
        <where>
            <if test="mobilePhone != null">
                and mobile_phone = #{mobilePhone,jdbcType=VARCHAR}
            </if>
            <if test="mobilePhone == null">
                and mobile_phone is null
            </if>
            <if test="certificateNum != null">
                and certificate_num = #{certificateNum,jdbcType=VARCHAR}
            </if>
            <if test="id != null">
                and id &lt;&gt; #{id,jdbcType=BIGINT}
            </if>
        </where>
    </select>
</mapper>