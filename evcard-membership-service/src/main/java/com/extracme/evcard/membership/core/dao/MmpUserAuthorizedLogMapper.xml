<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.core.dao.MmpUserAuthorizedLogMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.MmpUserAuthorizedLog" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="auth_id" property="authId" jdbcType="VARCHAR" />
    <result column="authorized_type" property="authorizedType" jdbcType="INTEGER" />
    <result column="authorized_result" property="authorizedResult" jdbcType="INTEGER" />
    <result column="authorized_reason" property="authorizedReason" jdbcType="VARCHAR" />
    <result column="authorized_date_time" property="authorizedDateTime" jdbcType="TIMESTAMP" />
    <result column="authorized_fraction" property="authorizedFraction" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="misc_desc" property="miscDesc" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, auth_id, authorized_type, authorized_result, authorized_reason, authorized_date_time, 
    authorized_fraction, status, misc_desc, create_time, create_oper_id, create_oper_name, 
    update_time, update_oper_id, update_oper_name
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_user_authorized_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from ${issSchema}.mmp_user_authorized_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.membership.core.model.MmpUserAuthorizedLog" >
    insert into ${issSchema}.mmp_user_authorized_log (id, auth_id, authorized_type,
      authorized_result, authorized_reason, authorized_date_time, 
      authorized_fraction, status, misc_desc, 
      create_time, create_oper_id, create_oper_name, 
      update_time, update_oper_id, update_oper_name
      )
    values (#{id,jdbcType=BIGINT}, #{authId,jdbcType=VARCHAR}, #{authorizedType,jdbcType=INTEGER}, 
      #{authorizedResult,jdbcType=INTEGER}, #{authorizedReason,jdbcType=VARCHAR}, #{authorizedDateTime,jdbcType=TIMESTAMP}, 
      #{authorizedFraction,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{miscDesc,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, #{createOperName,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, #{updateOperName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.core.model.MmpUserAuthorizedLog" >
    insert into ${issSchema}.mmp_user_authorized_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="authId != null" >
        auth_id,
      </if>
      <if test="authorizedType != null" >
        authorized_type,
      </if>
      <if test="authorizedResult != null" >
        authorized_result,
      </if>
      <if test="authorizedReason != null" >
        authorized_reason,
      </if>
      <if test="authorizedDateTime != null" >
        authorized_date_time,
      </if>
      <if test="authorizedFraction != null" >
        authorized_fraction,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="miscDesc != null" >
        misc_desc,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateOperId != null" >
        update_oper_id,
      </if>
      <if test="updateOperName != null" >
        update_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="authId != null" >
        #{authId,jdbcType=VARCHAR},
      </if>
      <if test="authorizedType != null" >
        #{authorizedType,jdbcType=INTEGER},
      </if>
      <if test="authorizedResult != null" >
        #{authorizedResult,jdbcType=INTEGER},
      </if>
      <if test="authorizedReason != null" >
        #{authorizedReason,jdbcType=VARCHAR},
      </if>
      <if test="authorizedDateTime != null" >
        #{authorizedDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="authorizedFraction != null" >
        #{authorizedFraction,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="miscDesc != null" >
        #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        #{updateOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.core.model.MmpUserAuthorizedLog" >
    update ${issSchema}.mmp_user_authorized_log
    <set >
      <if test="authId != null" >
        auth_id = #{authId,jdbcType=VARCHAR},
      </if>
      <if test="authorizedType != null" >
        authorized_type = #{authorizedType,jdbcType=INTEGER},
      </if>
      <if test="authorizedResult != null" >
        authorized_result = #{authorizedResult,jdbcType=INTEGER},
      </if>
      <if test="authorizedReason != null" >
        authorized_reason = #{authorizedReason,jdbcType=VARCHAR},
      </if>
      <if test="authorizedDateTime != null" >
        authorized_date_time = #{authorizedDateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="authorizedFraction != null" >
        authorized_fraction = #{authorizedFraction,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="miscDesc != null" >
        misc_desc = #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.core.model.MmpUserAuthorizedLog" >
    update ${issSchema}.mmp_user_authorized_log
    set auth_id = #{authId,jdbcType=VARCHAR},
      authorized_type = #{authorizedType,jdbcType=INTEGER},
      authorized_result = #{authorizedResult,jdbcType=INTEGER},
      authorized_reason = #{authorizedReason,jdbcType=VARCHAR},
      authorized_date_time = #{authorizedDateTime,jdbcType=TIMESTAMP},
      authorized_fraction = #{authorizedFraction,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      misc_desc = #{miscDesc,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryAuthorizedHistoryList" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM ${issSchema}.mmp_user_authorized_log
    <where>
      <if test="authId != null">
        AND auth_id = #{authId}
      </if>
      <if test="authorizedTypes != null and authorizedTypes.size>0 ">
        AND authorized_type in
        <foreach collection="authorizedTypes" item="item" separator="," open="("
                 close=")">
          #{item}
        </foreach>
      </if>
      <if test="authorizedResult != null">
        AND authorized_result = #{authorizedResult}
      </if>
    </where>
    ORDER BY create_time DESC
    <if test="page != null">
      LIMIT #{page.offSet},#{page.limitSet}
    </if>
  </select>

  <select id="countUserAuthorizedHistory" resultType="integer">
    SELECT
    count(*)
    FROM ${issSchema}.mmp_user_authorized_log
    <where>
      <if test="authId != null">
        AND auth_id = #{authId}
      </if>
      <if test="authorizedTypes != null and authorizedTypes.size>0 ">
        AND operation_type in
        <foreach collection="authorizedTypes" item="item" separator="," open="("
                 close=")">
          #{item}
        </foreach>
      </if>
      <if test="authorizedResult != null">
        AND authorized_result = #{authorizedResult}
      </if>
    </where>
  </select>

</mapper>