<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.core.dao.AppKeyManagerMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.AppKeyManager" >
    <id column="APP_KEY" property="appKey" jdbcType="VARCHAR" />
    <result column="APP_SECRET" property="appSecret" jdbcType="VARCHAR" />
    <result column="REQUEST_APP_KEY" property="requestAppKey" jdbcType="VARCHAR" />
    <result column="PLAT_NAME" property="platName" jdbcType="VARCHAR" />
    <result column="REQUEST_APP_SECRET" property="requestAppSecret" jdbcType="VARCHAR" />
    <result column="POST_URL" property="postUrl" jdbcType="VARCHAR" />
    <result column="CLASS_NAME" property="className" jdbcType="VARCHAR" />
    <result column="ORG_ID" property="orgId" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="STATUS" property="status" jdbcType="DOUBLE" />
    <result column="AUTO_REGIST" property="autoRegist" jdbcType="DOUBLE" />
    <result column="LOGIN_RESTRICT" property="loginRestrict" jdbcType="DOUBLE" />
    <result column="AUTO_PAY" property="autoPay" jdbcType="DOUBLE" />
    <result column="ENJOY_BENEFIT" property="enjoyBenefit" jdbcType="DOUBLE" />
    <result column="UPLOAD_ORDER" property="uploadOrder" jdbcType="DOUBLE" />
    <result column="TYPE" property="type" jdbcType="TINYINT" />
    <result column="CREATED_TIME" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="CREATE_OPER_ID" property="createOperId" jdbcType="BIGINT" />
    <result column="CREATED_USER" property="createdUser" jdbcType="VARCHAR" />
    <result column="UPDATED_TIME" property="updatedTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_OPER_ID" property="updateOperId" jdbcType="BIGINT" />
    <result column="UPDATED_USER" property="updatedUser" jdbcType="VARCHAR" />
    <result column="plat_app_key" property="platAppKey" jdbcType="VARCHAR" />
    <result column="plat_app_secret" property="platAppSecret" jdbcType="VARCHAR" />
    <result column="platform_id" property="platformId" jdbcType="BIGINT" />
    <result column="channel_purpose" property="channelPurpose" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    APP_KEY, APP_SECRET, REQUEST_APP_KEY, PLAT_NAME, REQUEST_APP_SECRET, POST_URL, CLASS_NAME, 
    ORG_ID, REMARK, STATUS, AUTO_REGIST, LOGIN_RESTRICT, AUTO_PAY, ENJOY_BENEFIT, UPLOAD_ORDER, 
    TYPE, CREATED_TIME, CREATE_OPER_ID, CREATED_USER, UPDATED_TIME, UPDATE_OPER_ID, UPDATED_USER, 
    plat_app_key, plat_app_secret,platform_id,channel_purpose
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap"  parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from ${siacSchema}.app_key_manager
    where APP_KEY = #{appKey,jdbcType=VARCHAR}
  </select>
  <insert id="insert" parameterType="com.extracme.evcard.membership.core.model.AppKeyManager" >
    insert into ${siacSchema}.app_key_manager (APP_KEY, APP_SECRET, REQUEST_APP_KEY,
      PLAT_NAME, REQUEST_APP_SECRET, POST_URL, 
      CLASS_NAME, ORG_ID, REMARK, 
      STATUS, AUTO_REGIST, LOGIN_RESTRICT, 
      AUTO_PAY, ENJOY_BENEFIT, UPLOAD_ORDER, 
      TYPE, CREATED_TIME, CREATE_OPER_ID, 
      CREATED_USER, UPDATED_TIME, UPDATE_OPER_ID, 
      UPDATED_USER, plat_app_key, plat_app_secret
      )
    values (#{appKey,jdbcType=VARCHAR}, #{appSecret,jdbcType=VARCHAR}, #{requestAppKey,jdbcType=VARCHAR}, 
      #{platName,jdbcType=VARCHAR}, #{requestAppSecret,jdbcType=VARCHAR}, #{postUrl,jdbcType=VARCHAR}, 
      #{className,jdbcType=VARCHAR}, #{orgId,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{status,jdbcType=DOUBLE}, #{autoRegist,jdbcType=DOUBLE}, #{loginRestrict,jdbcType=DOUBLE}, 
      #{autoPay,jdbcType=DOUBLE}, #{enjoyBenefit,jdbcType=DOUBLE}, #{uploadOrder,jdbcType=DOUBLE}, 
      #{type,jdbcType=TINYINT}, #{createdTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, 
      #{createdUser,jdbcType=VARCHAR}, #{updatedTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, 
      #{updatedUser,jdbcType=VARCHAR}, #{platAppKey,jdbcType=VARCHAR}, #{platAppSecret,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.core.model.AppKeyManager" >
    insert into ${siacSchema}.app_key_manager
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="appKey != null" >
        APP_KEY,
      </if>
      <if test="appSecret != null" >
        APP_SECRET,
      </if>
      <if test="requestAppKey != null" >
        REQUEST_APP_KEY,
      </if>
      <if test="platName != null" >
        PLAT_NAME,
      </if>
      <if test="requestAppSecret != null" >
        REQUEST_APP_SECRET,
      </if>
      <if test="postUrl != null" >
        POST_URL,
      </if>
      <if test="className != null" >
        CLASS_NAME,
      </if>
      <if test="orgId != null" >
        ORG_ID,
      </if>
      <if test="remark != null" >
        REMARK,
      </if>
      <if test="status != null" >
        STATUS,
      </if>
      <if test="autoRegist != null" >
        AUTO_REGIST,
      </if>
      <if test="loginRestrict != null" >
        LOGIN_RESTRICT,
      </if>
      <if test="autoPay != null" >
        AUTO_PAY,
      </if>
      <if test="enjoyBenefit != null" >
        ENJOY_BENEFIT,
      </if>
      <if test="uploadOrder != null" >
        UPLOAD_ORDER,
      </if>
      <if test="type != null" >
        TYPE,
      </if>
      <if test="createdTime != null" >
        CREATED_TIME,
      </if>
      <if test="createOperId != null" >
        CREATE_OPER_ID,
      </if>
      <if test="createdUser != null" >
        CREATED_USER,
      </if>
      <if test="updatedTime != null" >
        UPDATED_TIME,
      </if>
      <if test="updateOperId != null" >
        UPDATE_OPER_ID,
      </if>
      <if test="updatedUser != null" >
        UPDATED_USER,
      </if>
      <if test="platAppKey != null" >
        plat_app_key,
      </if>
      <if test="platAppSecret != null" >
        plat_app_secret,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="appKey != null" >
        #{appKey,jdbcType=VARCHAR},
      </if>
      <if test="appSecret != null" >
        #{appSecret,jdbcType=VARCHAR},
      </if>
      <if test="requestAppKey != null" >
        #{requestAppKey,jdbcType=VARCHAR},
      </if>
      <if test="platName != null" >
        #{platName,jdbcType=VARCHAR},
      </if>
      <if test="requestAppSecret != null" >
        #{requestAppSecret,jdbcType=VARCHAR},
      </if>
      <if test="postUrl != null" >
        #{postUrl,jdbcType=VARCHAR},
      </if>
      <if test="className != null" >
        #{className,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null" >
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=DOUBLE},
      </if>
      <if test="autoRegist != null" >
        #{autoRegist,jdbcType=DOUBLE},
      </if>
      <if test="loginRestrict != null" >
        #{loginRestrict,jdbcType=DOUBLE},
      </if>
      <if test="autoPay != null" >
        #{autoPay,jdbcType=DOUBLE},
      </if>
      <if test="enjoyBenefit != null" >
        #{enjoyBenefit,jdbcType=DOUBLE},
      </if>
      <if test="uploadOrder != null" >
        #{uploadOrder,jdbcType=DOUBLE},
      </if>
      <if test="type != null" >
        #{type,jdbcType=TINYINT},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createdUser != null" >
        #{createdUser,jdbcType=VARCHAR},
      </if>
      <if test="updatedTime != null" >
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updatedUser != null" >
        #{updatedUser,jdbcType=VARCHAR},
      </if>
      <if test="platAppKey != null" >
        #{platAppKey,jdbcType=VARCHAR},
      </if>
      <if test="platAppSecret != null" >
        #{platAppSecret,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.core.model.AppKeyManager" >
    update ${siacSchema}.app_key_manager
    <set >
      <if test="appSecret != null" >
        APP_SECRET = #{appSecret,jdbcType=VARCHAR},
      </if>
      <if test="requestAppKey != null" >
        REQUEST_APP_KEY = #{requestAppKey,jdbcType=VARCHAR},
      </if>
      <if test="platName != null" >
        PLAT_NAME = #{platName,jdbcType=VARCHAR},
      </if>
      <if test="requestAppSecret != null" >
        REQUEST_APP_SECRET = #{requestAppSecret,jdbcType=VARCHAR},
      </if>
      <if test="postUrl != null" >
        POST_URL = #{postUrl,jdbcType=VARCHAR},
      </if>
      <if test="className != null" >
        CLASS_NAME = #{className,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null" >
        ORG_ID = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        STATUS = #{status,jdbcType=DOUBLE},
      </if>
      <if test="autoRegist != null" >
        AUTO_REGIST = #{autoRegist,jdbcType=DOUBLE},
      </if>
      <if test="loginRestrict != null" >
        LOGIN_RESTRICT = #{loginRestrict,jdbcType=DOUBLE},
      </if>
      <if test="autoPay != null" >
        AUTO_PAY = #{autoPay,jdbcType=DOUBLE},
      </if>
      <if test="enjoyBenefit != null" >
        ENJOY_BENEFIT = #{enjoyBenefit,jdbcType=DOUBLE},
      </if>
      <if test="uploadOrder != null" >
        UPLOAD_ORDER = #{uploadOrder,jdbcType=DOUBLE},
      </if>
      <if test="type != null" >
        TYPE = #{type,jdbcType=TINYINT},
      </if>
      <if test="createdTime != null" >
        CREATED_TIME = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        CREATE_OPER_ID = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createdUser != null" >
        CREATED_USER = #{createdUser,jdbcType=VARCHAR},
      </if>
      <if test="updatedTime != null" >
        UPDATED_TIME = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        UPDATE_OPER_ID = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updatedUser != null" >
        UPDATED_USER = #{updatedUser,jdbcType=VARCHAR},
      </if>
      <if test="platAppKey != null" >
        plat_app_key = #{platAppKey,jdbcType=VARCHAR},
      </if>
      <if test="platAppSecret != null" >
        plat_app_secret = #{platAppSecret,jdbcType=VARCHAR},
      </if>
    </set>
    where APP_KEY = #{appKey,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.core.model.AppKeyManager" >
    update ${siacSchema}.app_key_manager
    set APP_SECRET = #{appSecret,jdbcType=VARCHAR},
      REQUEST_APP_KEY = #{requestAppKey,jdbcType=VARCHAR},
      PLAT_NAME = #{platName,jdbcType=VARCHAR},
      REQUEST_APP_SECRET = #{requestAppSecret,jdbcType=VARCHAR},
      POST_URL = #{postUrl,jdbcType=VARCHAR},
      CLASS_NAME = #{className,jdbcType=VARCHAR},
      ORG_ID = #{orgId,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=DOUBLE},
      AUTO_REGIST = #{autoRegist,jdbcType=DOUBLE},
      LOGIN_RESTRICT = #{loginRestrict,jdbcType=DOUBLE},
      AUTO_PAY = #{autoPay,jdbcType=DOUBLE},
      ENJOY_BENEFIT = #{enjoyBenefit,jdbcType=DOUBLE},
      UPLOAD_ORDER = #{uploadOrder,jdbcType=DOUBLE},
      TYPE = #{type,jdbcType=TINYINT},
      CREATED_TIME = #{createdTime,jdbcType=TIMESTAMP},
      CREATE_OPER_ID = #{createOperId,jdbcType=BIGINT},
      CREATED_USER = #{createdUser,jdbcType=VARCHAR},
      UPDATED_TIME = #{updatedTime,jdbcType=TIMESTAMP},
      UPDATE_OPER_ID = #{updateOperId,jdbcType=BIGINT},
      UPDATED_USER = #{updatedUser,jdbcType=VARCHAR},
      plat_app_key = #{platAppKey,jdbcType=VARCHAR},
      plat_app_secret = #{platAppSecret,jdbcType=VARCHAR}
    where APP_KEY = #{appKey,jdbcType=VARCHAR}
  </update>


  <!--根据平台和渠道用途获取渠道，不分页，最大8000 -->
  <select id="getAppKeys" resultType="com.extracme.evcard.membership.core.dto.AppKeyDto">
    SELECT
    APP_KEY as appKey,
    PLAT_NAME as platName,
    platform_id as platformId,
    channel_purpose as channelPurpose,
    status as status
    FROM
    ${siacSchema}.app_key_manager
    WHERE 1 = 1
    <if test="platformId != null">
      and platform_id = #{platformId,jdbcType=BIGINT}
    </if>
    <if test="channelPurpose != null">
      and channel_purpose like concat('%', #{channelPurpose} ,'%')
    </if>
    <if test="status != null">
      and status = #{status,jdbcType=INTEGER}
    </if>
    limit 8000
  </select>

  <select id="getEffectiveAppKeyInfo" resultMap="BaseResultMap">
    SELECT<include refid="Base_Column_List" />
    from ${siacSchema}.app_key_manager
    where status = 0
  </select>

</mapper>