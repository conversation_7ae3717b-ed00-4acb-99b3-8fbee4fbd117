package com.extracme.evcard.membership.core.dto;

import com.baosight.imap.json.annotation.JsonIgnoreProperties;
import com.baosight.imap.rest.domain.JsonSession;
import com.baosight.imap.rest.domain.LoginInfo;

@JsonIgnoreProperties(ignoreUnknown = true)
public class AppSession extends JsonSession{
    /**
     * 构造函数
     * @param arg0
     */
    public AppSession(LoginInfo arg0) {
        //Session 超时 1 hour
        super(arg0, 60);
    }

    private String authId;
    private int memberType;
    private String cardNo;
    private String displayName;
    private String ip;
    private String appKey;

    // 0:个人用户 1：企业用户
    private int orgUser;

    // 0:普通 1：管理员
    private int userType;

    //	 登录来源 0： app 1:门户网站 2:车门控制
    private int loginOrigin = 0;

    private String agencyId;

    private String loginName;
    private String passWord;

    //	 APP类型 0：android 1：ios
    private int appType ;

    // APP版本号
    private String appVersion;

    private String orgId;

    public String getLoginName() {
        return loginName;
    }
    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }
    public String getPassWord() {
        return passWord;
    }
    public void setPassWord(String passWord) {
        this.passWord = passWord;
    }
    /**
     * @return the authId
     */
    public String getAuthId() {
        return authId;
    }
    /**
     * @param authId the authId to set
     */
    public void setAuthId(String authId) {
        this.authId = authId;
    }
    /**
     * @return the memberType
     */
    public int getMemberType() {
        return memberType;
    }
    /**
     * @param memberType the memberType to set
     */
    public void setMemberType(int memberType) {
        this.memberType = memberType;
    }
    /**
     * @return the cardNo
     */
    public String getCardNo() {
        return cardNo;
    }
    /**
     * @param cardNo the cardNo to set
     */
    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }


    /**
     * @return the orgUser
     */
    public int getOrgUser() {
        return orgUser;
    }
    /**
     * @param orgUser the orgUser to set
     */
    public void setOrgUser(int orgUser) {
        this.orgUser = orgUser;
    }

    /**
     * @return the userType
     */
    public int getUserType() {
        return userType;
    }

    /**
     * @param userType the userType to set
     */
    public void setUserType(int userType) {
        this.userType = userType;
    }
    /**
     * @return the loginOrigin
     */
    public int getLoginOrigin() {
        return loginOrigin;
    }
    /**
     * @param loginOrigin the loginOrigin to set
     */
    public void setLoginOrigin(int loginOrigin) {
        this.loginOrigin = loginOrigin;
    }
    /**
     * @return the displayName
     */
    public String getDisplayName() {
        return displayName;
    }
    /**
     * @param displayName the displayName to set
     */
    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }
    /**
     * @return the ip
     */
    public String getIp() {
        return ip;
    }
    /**
     * @param ip the ip to set
     */
    public void setIp(String ip) {
        this.ip = ip;
    }
    public String getAgencyId() {
        return agencyId;
    }
    public void setAgencyId(String agencyId) {
        this.agencyId = agencyId;
    }
    /**
     * @return the appType
     */
    public int getAppType() {
        return appType;
    }
    /**
     * @param appType the appType to set
     */
    public void setAppType(int appType) {
        this.appType = appType;
    }
    public String getAppKey() {
        return appKey;
    }
    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }
    /**
     * @return the appVersion
     */
    public String getAppVersion() {
        return appVersion;
    }
    /**
     * @param appVersion the appVersion to set
     */
    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }
    /**
     * @return the orgId
     */
    public String getOrgId() {
        return orgId;
    }
    /**
     * @param orgId the orgId to set
     */
    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

}
