package com.extracme.evcard.membership.core.model;

import java.util.Date;

public class SecondAppKeyManager {
    private Long id;

    /**
     *二级渠道appkey
     */
    private String secondAppKey;

    /**
     * 二级渠道APP_SECRET
     */
    private String secondAppSecret;

    /**
     * 二级渠道名称
     */
    private String secondAppKeyName;

    /**
     * 第三方id
     */
    private Integer thirdid;

    /**
     * 一级渠道appkey
     */
    private String firstAppKey;

    /**
     * 平台ID
     */
    private Long platformId;

    /**
     * '渠道用途'
     */
    private String channelPurpose;

    /**
     * 税务主体公司
     */
    private String taxMainCompany;

    /**
     * 申请机构 运营公司
     */
    private String orgId;

    /**
     * 备注
     */
    private String remark;

    /**
     * '0:有效 1：无效'
     */
    private Integer status;

    private Integer isDeleted;

    private Date createTime;

    private Long createOperId;

    private String createOperName;

    private Date updateTime;

    private Long updateOperId;

    private String updateOperName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSecondAppKey() {
        return secondAppKey;
    }

    public void setSecondAppKey(String secondAppKey) {
        this.secondAppKey = secondAppKey;
    }

    public String getSecondAppSecret() {
        return secondAppSecret;
    }

    public void setSecondAppSecret(String secondAppSecret) {
        this.secondAppSecret = secondAppSecret;
    }

    public String getSecondAppKeyName() {
        return secondAppKeyName;
    }

    public void setSecondAppKeyName(String secondAppKeyName) {
        this.secondAppKeyName = secondAppKeyName;
    }

    public Integer getThirdid() {
        return thirdid;
    }

    public void setThirdid(Integer thirdid) {
        this.thirdid = thirdid;
    }

    public String getFirstAppKey() {
        return firstAppKey;
    }

    public void setFirstAppKey(String firstAppKey) {
        this.firstAppKey = firstAppKey;
    }

    public Long getPlatformId() {
        return platformId;
    }

    public void setPlatformId(Long platformId) {
        this.platformId = platformId;
    }

    public String getChannelPurpose() {
        return channelPurpose;
    }

    public void setChannelPurpose(String channelPurpose) {
        this.channelPurpose = channelPurpose;
    }

    public String getTaxMainCompany() {
        return taxMainCompany;
    }

    public void setTaxMainCompany(String taxMainCompany) {
        this.taxMainCompany = taxMainCompany;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateOperId() {
        return createOperId;
    }

    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    public String getCreateOperName() {
        return createOperName;
    }

    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdateOperId() {
        return updateOperId;
    }

    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    public String getUpdateOperName() {
        return updateOperName;
    }

    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }

}