package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.model.OrgInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface OrgInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(OrgInfo record);

    int insertSelective(OrgInfo record);

    OrgInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OrgInfo record);

    int updateByPrimaryKey(OrgInfo record);

    OrgInfo queryOrgInfoByOrgId(String orgId);

    List<OrgInfo> queryAllOrgInfo();

    List<Map<String,String>> batchQueryOrgName(@Param("list") List<String> orgIdList);
}