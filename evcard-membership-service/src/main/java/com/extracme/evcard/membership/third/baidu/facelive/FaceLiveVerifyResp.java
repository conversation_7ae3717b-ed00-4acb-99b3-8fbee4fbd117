package com.extracme.evcard.membership.third.baidu.facelive;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class FaceLiveVerifyResp {


    @JSONField(name = "score") private Double score;
    @JSONField(name = "maxspoofing") private Double maxSpoofing;
    @JSONField(name = "spoofing_score") private Double spoofingScore;
    @JSONField(name = "code") private CodeDTO code;
    @J<PERSON>NField(name = "lip_language") private String lipLanguage;
    @J<PERSON><PERSON>ield(name = "action_verify") private String actionVerify;
    @JSONField(name = "thresholds") private ThresholdsDTO thresholds;
    @JSONField(name = "best_image") private BestImageDTO bestImage;
    @JSONField(name = "pic_list") private List<PicListDTO> picList;


    @NoArgsConstructor
    @Data
    public static class CodeDTO {
        @JSONField(name = "create") private String create;
        @JSONField(name = "identify") private String identify;
        @JSONField(name = "similarity") private Double similarity;
    }

    @NoArgsConstructor
    @Data
    public static class ThresholdsDTO {
        @JSONField(name = "frr_1e-4") private Double frr1e4;
        @JSONField(name = "frr_1e-3") private Double frr1e3;
        @JSONField(name = "frr_1e-2") private Double frr1e2;
    }

    @NoArgsConstructor
    @Data
    public static class BestImageDTO {
        @JSONField(name = "pic") private String pic;
        @JSONField(name = "face_token") private String faceToken;
        @JSONField(name = "face_id") private String faceId;
        @JSONField(name = "liveness_score") private Double liveNessScore;
        @JSONField(name = "spoofing") private Double spoofing;
    }

    @NoArgsConstructor
    @Data
    public static class PicListDTO {
        @JSONField(name = "pic") private String pic;
        @JSONField(name = "face_token") private String faceToken;
        @JSONField(name = "face_id") private String faceId;
        @JSONField(name = "liveness_score") private Double liveNessScore;
        @JSONField(name = "spoofing") private Double spoofing;
    }
}
