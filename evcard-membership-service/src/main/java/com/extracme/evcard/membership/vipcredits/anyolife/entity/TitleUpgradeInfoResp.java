package com.extracme.evcard.membership.vipcredits.anyolife.entity;

import com.extracme.evcard.membership.core.dto.UserTitleUpgradeDto;
import com.extracme.evcard.membership.core.dto.UserTitleUpgradePointsDto;
import lombok.Data;
import org.apache.commons.beanutils.BeanUtils;

/**
 * <AUTHOR>
 * @date 2021/4/22
 * @remark
 */
@Data
public class TitleUpgradeInfoResp extends TitleUpgradeInfo {
    private Integer points;

    public UserTitleUpgradePointsDto toPointsResultDto(){
        UserTitleUpgradePointsDto dto = new UserTitleUpgradePointsDto();
        UserTitleUpgradeDto upgradeDto = super.toUpgradeDto();
        if(upgradeDto == null) {
            return null;
        }
        dto.setIssueTime(upgradeDto.getIssueTime());
        dto.setOriginTitle(upgradeDto.getOriginTitle());
        dto.setNewTitle(upgradeDto.getNewTitle());
        dto.setGainPoints(points);
        return dto;
    }

}
