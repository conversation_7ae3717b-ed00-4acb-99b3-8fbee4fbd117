package com.extracme.evcard.membership.core.service.auth.inner;


import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.enums.AuthStateCodeEnum;
import com.extracme.evcard.redis.JedisUtil;
import com.extracme.evcard.rpc.dto.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;



@Slf4j
@Component
public class CertSubmitChecker {
    /**
     * 证件提交次数限制 cert_submit_count_type:mid
     */
    private static final String CACHE_KEY_SUBMIT_COUNT_ID = "cert_submit_counts_%s:%s";

    @Value("${cert.submit.limit:3}")
    private int certSubmitLimit;

    private static final int SECONDS_COUNT_CERT_SUBMIT = 60 * 60;

    private String getCertSubmitCountCacheKey(String type, String mid) {
        return String.format(CACHE_KEY_SUBMIT_COUNT_ID, type, mid);
    }

    public synchronized BaseResponse checkSubmitTimes(String type, String mid){
        try(Jedis jedis = JedisUtil.getJedis()){
            String key = getCertSubmitCountCacheKey(type, mid);
            String submitCount = jedis.get(key);
            if(StringUtils.isNotBlank(submitCount)) {
                if(ComUtil.getIntValue(submitCount, 0) > certSubmitLimit) {
                    int minutes = (int) Math.ceil(jedis.ttl(key) * 1.0 / 60);
                    return new BaseResponse(-1, String.valueOf(minutes));
                }
                jedis.incr(key);
                //处理并发情况下，计数器有效期被至为-1的问题
                long seconds = jedis.ttl(key);
                if(seconds < 0) {
                    jedis.expire(key, SECONDS_COUNT_CERT_SUBMIT);
                }
            }else {
                jedis.set(key, "1");
                jedis.expire(key, SECONDS_COUNT_CERT_SUBMIT);
            }
        }catch (Exception e) {
        }
        return null;
    }

    public AuthStateCodeEnum checkExpireDate(String expireDate) {
        try {
            LocalDate now = LocalDate.now();
            DateTimeFormatter ymd = DateTimeFormatter.ofPattern(ComUtil.DATE_TYPE5);
            LocalDate expirationDate = LocalDate.parse(expireDate, ymd);
            if (now.isAfter(expirationDate)) {
                return AuthStateCodeEnum.ID_EXPIRED;
            }
        } catch (Exception e){
            return AuthStateCodeEnum.ID_EXPIRE_DATE_ERROR;
        }
        return null;
    }


    public AuthStateCodeEnum checkDriverLicense(String drivingLicenseType, String expireDate) {
        /**
         * 1.判断驾照有效期
         */
        try {
            LocalDate now = LocalDate.now();
            DateTimeFormatter ymd = DateTimeFormatter.ofPattern(ComUtil.DATE_TYPE5);
            LocalDate expirationDate = LocalDate.parse(expireDate, ymd);
            if (now.isAfter(expirationDate)) {
                return AuthStateCodeEnum.DRIVER_LICENSE_EXPIRE;
            }
        } catch (DateTimeParseException e){
            return AuthStateCodeEnum.LICENSE_EXPIRE_DATE_ERROR;
        }
        /**
         * 2.判断驾照准驾类型
         */
        boolean typeCheckFlag = false;
        for (String type : BussinessConstants.DRIVER_LICENSE_TYPE_LIST) {
            if (drivingLicenseType.startsWith(type)) {
                typeCheckFlag = true;
                break;
            }
        }
        if (!typeCheckFlag) {
            return AuthStateCodeEnum.DRIVER_LICENSE_TYPE_NOT_ALLOW;
        }
        return null;
    }
}
