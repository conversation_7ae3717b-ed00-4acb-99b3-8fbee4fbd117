package com.extracme.evcard.membership.credit.model;

import java.util.Date;

public class MmpCreditEventTypeReport {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_type_report.id
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_type_report.year_num
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    private String yearNum;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_type_report.org_id
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    private String orgId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_type_report.type
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    private Integer type;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_type_report.event_type_id
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    private Long eventTypeId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_type_report.event_name
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    private String eventName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_type_report.month
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    private Integer month;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_type_report.total
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    private Integer total;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_type_report.misc_desc
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    private String miscDesc;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_type_report.status
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_type_report.create_time
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_type_report.create_oper_id
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    private Long createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_type_report.create_oper_name
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    private String createOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_type_report.update_time
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_type_report.update_oper_id
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    private Long updateOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_type_report.update_oper_name
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    private String updateOperName;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_type_report.id
     *
     * @return the value of mmp_credit_event_type_report.id
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_type_report.id
     *
     * @param id the value for mmp_credit_event_type_report.id
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_type_report.year_num
     *
     * @return the value of mmp_credit_event_type_report.year_num
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public String getYearNum() {
        return yearNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_type_report.year_num
     *
     * @param yearNum the value for mmp_credit_event_type_report.year_num
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public void setYearNum(String yearNum) {
        this.yearNum = yearNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_type_report.org_id
     *
     * @return the value of mmp_credit_event_type_report.org_id
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public String getOrgId() {
        return orgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_type_report.org_id
     *
     * @param orgId the value for mmp_credit_event_type_report.org_id
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_type_report.type
     *
     * @return the value of mmp_credit_event_type_report.type
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public Integer getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_type_report.type
     *
     * @param type the value for mmp_credit_event_type_report.type
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_type_report.event_type_id
     *
     * @return the value of mmp_credit_event_type_report.event_type_id
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public Long getEventTypeId() {
        return eventTypeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_type_report.event_type_id
     *
     * @param eventTypeId the value for mmp_credit_event_type_report.event_type_id
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public void setEventTypeId(Long eventTypeId) {
        this.eventTypeId = eventTypeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_type_report.event_name
     *
     * @return the value of mmp_credit_event_type_report.event_name
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public String getEventName() {
        return eventName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_type_report.event_name
     *
     * @param eventName the value for mmp_credit_event_type_report.event_name
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_type_report.month
     *
     * @return the value of mmp_credit_event_type_report.month
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public Integer getMonth() {
        return month;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_type_report.month
     *
     * @param month the value for mmp_credit_event_type_report.month
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public void setMonth(Integer month) {
        this.month = month;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_type_report.total
     *
     * @return the value of mmp_credit_event_type_report.total
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public Integer getTotal() {
        return total;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_type_report.total
     *
     * @param total the value for mmp_credit_event_type_report.total
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public void setTotal(Integer total) {
        this.total = total;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_type_report.misc_desc
     *
     * @return the value of mmp_credit_event_type_report.misc_desc
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public String getMiscDesc() {
        return miscDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_type_report.misc_desc
     *
     * @param miscDesc the value for mmp_credit_event_type_report.misc_desc
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_type_report.status
     *
     * @return the value of mmp_credit_event_type_report.status
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_type_report.status
     *
     * @param status the value for mmp_credit_event_type_report.status
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_type_report.create_time
     *
     * @return the value of mmp_credit_event_type_report.create_time
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_type_report.create_time
     *
     * @param createTime the value for mmp_credit_event_type_report.create_time
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_type_report.create_oper_id
     *
     * @return the value of mmp_credit_event_type_report.create_oper_id
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public Long getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_type_report.create_oper_id
     *
     * @param createOperId the value for mmp_credit_event_type_report.create_oper_id
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_type_report.create_oper_name
     *
     * @return the value of mmp_credit_event_type_report.create_oper_name
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_type_report.create_oper_name
     *
     * @param createOperName the value for mmp_credit_event_type_report.create_oper_name
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_type_report.update_time
     *
     * @return the value of mmp_credit_event_type_report.update_time
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_type_report.update_time
     *
     * @param updateTime the value for mmp_credit_event_type_report.update_time
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_type_report.update_oper_id
     *
     * @return the value of mmp_credit_event_type_report.update_oper_id
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public Long getUpdateOperId() {
        return updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_type_report.update_oper_id
     *
     * @param updateOperId the value for mmp_credit_event_type_report.update_oper_id
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_type_report.update_oper_name
     *
     * @return the value of mmp_credit_event_type_report.update_oper_name
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public String getUpdateOperName() {
        return updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_type_report.update_oper_name
     *
     * @param updateOperName the value for mmp_credit_event_type_report.update_oper_name
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }
}