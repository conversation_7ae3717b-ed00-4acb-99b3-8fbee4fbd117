package com.extracme.evcard.membership.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/8/18
 */
@Getter
@AllArgsConstructor
public enum TextinIdCardItemKeyEnums {
    NAME("name"),
    SEX("sex"),
    NATIONALITY("nationality"),
    BIRTH("birth"),
    ADDRESS("address"),
    ID_NUMBER("id_number"),
    ISSUE_AUTHORITY("issue_authority"),
    VALIDATE_DATE("validate_date"),
    IS_GRAY("is_gray");

    private String key;
}
