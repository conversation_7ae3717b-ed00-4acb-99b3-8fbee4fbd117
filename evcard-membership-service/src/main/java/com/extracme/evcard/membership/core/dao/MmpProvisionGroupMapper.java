package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.model.MmpProvisionGroup;

import java.util.List;

public interface MmpProvisionGroupMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MmpProvisionGroup record);

    int insertSelective(MmpProvisionGroup record);

    MmpProvisionGroup selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MmpProvisionGroup record);

    int updateByPrimaryKey(MmpProvisionGroup record);

    List<MmpProvisionGroup> queryGroups();
}