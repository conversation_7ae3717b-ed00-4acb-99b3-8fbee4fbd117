package com.extracme.evcard.membership.core.service;

import com.extracme.evcard.membership.core.dto.JwtTokenDto;
import com.extracme.evcard.membership.core.dto.TokenValidationResult;
import com.extracme.evcard.membership.core.exception.BusinessException;
import com.extracme.evcard.membership.core.model.UserTokenSession;
import com.extracme.evcard.membership.core.security.JwtTokenUtil;
import com.extracme.evcard.membership.core.security.TokenBlacklistManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Token服务实现类
 * 
 * <AUTHOR>
 * @since 2025-08-20
 */
@Slf4j
@Service
public class TokenServiceImpl implements ITokenService {
    
    @Autowired
    private JwtTokenUtil jwtTokenUtil;
    
    @Autowired
    private TokenBlacklistManager blacklistManager;
    
    // TODO: 需要创建UserTokenSessionMapper
    // @Autowired
    // private UserTokenSessionMapper userTokenSessionMapper;
    
    /**
     * 是否允许多设备登录
     */
    @Value("${jwt.multi-device.enabled:true}")
    private boolean multiDeviceEnabled;
    
    /**
     * 单用户最大会话数
     */
    @Value("${jwt.max-sessions-per-user:5}")
    private int maxSessionsPerUser;
    
    @Override
    @Transactional
    public JwtTokenDto generateTokenPair(Long userId, String mid, String appKey, String deviceId, 
                                         String deviceType, String deviceInfo, String loginIp, String loginLocation) 
                                         throws BusinessException {
        
        if (userId == null || StringUtils.isBlank(mid) || StringUtils.isBlank(appKey)) {
            throw new BusinessException(-1, "用户信息不完整，无法生成令牌");
        }
        
        if (StringUtils.isBlank(deviceId)) {
            deviceId = "unknown_device_" + System.currentTimeMillis();
        }
        
        try {
            // 检查用户是否在黑名单中
            if (blacklistManager.isUserBlacklisted(userId)) {
                throw new BusinessException(-1, "用户已被禁用，无法生成令牌");
            }
            
            // 检查设备是否在黑名单中
            if (blacklistManager.isDeviceBlacklisted(deviceId)) {
                throw new BusinessException(-1, "设备已被禁用，无法生成令牌");
            }
            
            // 如果不允许多设备登录，撤销用户的其他会话
            if (!multiDeviceEnabled) {
                revokeAllUserTokens(userId, "单设备登录限制");
            }
            
            // TODO: 检查用户会话数量限制
            // checkUserSessionLimit(userId);
            
            // 生成访问令牌和刷新令牌
            String accessToken = jwtTokenUtil.generateAccessToken(userId, mid, appKey, deviceId, deviceType);
            String refreshToken = jwtTokenUtil.generateRefreshToken(userId, mid, appKey, deviceId, deviceType);
            
            // 获取令牌的JTI
            String accessTokenJti = jwtTokenUtil.getJtiFromToken(accessToken);
            String refreshTokenJti = jwtTokenUtil.getJtiFromToken(refreshToken);
            
            // 验证令牌以获取过期时间
            TokenValidationResult accessResult = jwtTokenUtil.validateToken(accessToken);
            TokenValidationResult refreshResult = jwtTokenUtil.validateToken(refreshToken);
            
            if (!accessResult.isValid() || !refreshResult.isValid()) {
                throw new BusinessException(-1, "生成的令牌验证失败");
            }
            
            // TODO: 保存会话信息到数据库
            // saveTokenSession(userId, mid, appKey, deviceId, deviceType, deviceInfo, 
            //                  accessTokenJti, refreshTokenJti, accessResult.getExpireTime(), 
            //                  refreshResult.getExpireTime(), loginIp, loginLocation);
            
            // 构建返回结果
            JwtTokenDto tokenDto = new JwtTokenDto();
            tokenDto.setAccessToken(accessToken);
            tokenDto.setRefreshToken(refreshToken);
            tokenDto.setAccessTokenExpireTime(accessResult.getExpireTime());
            tokenDto.setRefreshTokenExpireTime(refreshResult.getExpireTime());
            tokenDto.setTokenType("Bearer");
            tokenDto.setExpiresIn((accessResult.getExpireTime().getTime() - System.currentTimeMillis()) / 1000);
            tokenDto.setUserId(userId);
            tokenDto.setMid(mid);
            tokenDto.setAppKey(appKey);
            tokenDto.setDeviceId(deviceId);
            tokenDto.setDeviceType(deviceType);
            
            log.info("成功生成JWT令牌对，用户ID: {}, 设备ID: {}", userId, deviceId);
            return tokenDto;
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("生成JWT令牌对失败，用户ID: {}, 设备ID: {}", userId, deviceId, e);
            throw new BusinessException(-1, "生成令牌失败");
        }
    }
    
    @Override
    public TokenValidationResult validateAccessToken(String accessToken) {
        if (StringUtils.isBlank(accessToken)) {
            return TokenValidationResult.failure("TOKEN_EMPTY", "访问令牌为空");
        }
        
        // 检查令牌是否在黑名单中
        if (blacklistManager.isTokenBlacklisted(accessToken)) {
            return TokenValidationResult.failure("TOKEN_REVOKED", "令牌已被撤销");
        }
        
        // 验证JWT令牌
        TokenValidationResult result = jwtTokenUtil.validateToken(accessToken);
        if (!result.isValid()) {
            return result;
        }
        
        // 检查用户是否在黑名单中
        if (blacklistManager.isUserBlacklisted(result.getUserId())) {
            return TokenValidationResult.failure("USER_BLACKLISTED", "用户已被禁用");
        }
        
        // 检查设备是否在黑名单中
        if (blacklistManager.isDeviceBlacklisted(result.getDeviceId())) {
            return TokenValidationResult.failure("DEVICE_BLACKLISTED", "设备已被禁用");
        }
        
        // TODO: 检查会话是否存在且有效
        // if (!isSessionValid(result)) {
        //     return TokenValidationResult.failure("SESSION_INVALID", "会话无效");
        // }
        
        return result;
    }
    
    @Override
    public TokenValidationResult validateRefreshToken(String refreshToken) {
        if (StringUtils.isBlank(refreshToken)) {
            return TokenValidationResult.failure("TOKEN_EMPTY", "刷新令牌为空");
        }
        
        // 检查令牌是否在黑名单中
        if (blacklistManager.isTokenBlacklisted(refreshToken)) {
            return TokenValidationResult.failure("TOKEN_REVOKED", "令牌已被撤销");
        }
        
        // 验证JWT令牌
        TokenValidationResult result = jwtTokenUtil.validateToken(refreshToken);
        if (!result.isValid()) {
            return result;
        }
        
        // 检查是否为刷新令牌类型
        Object tokenType = result.getCustomClaims().get("tokenType");
        if (!"REFRESH_TOKEN".equals(tokenType)) {
            return TokenValidationResult.failure("TOKEN_TYPE_INVALID", "令牌类型错误");
        }
        
        // 检查用户是否在黑名单中
        if (blacklistManager.isUserBlacklisted(result.getUserId())) {
            return TokenValidationResult.failure("USER_BLACKLISTED", "用户已被禁用");
        }
        
        // 检查设备是否在黑名单中
        if (blacklistManager.isDeviceBlacklisted(result.getDeviceId())) {
            return TokenValidationResult.failure("DEVICE_BLACKLISTED", "设备已被禁用");
        }
        
        return result;
    }
    
    @Override
    @Transactional
    public JwtTokenDto refreshAccessToken(String refreshToken) throws BusinessException {
        // 验证刷新令牌
        TokenValidationResult result = validateRefreshToken(refreshToken);
        if (!result.isValid()) {
            throw new BusinessException(-1, "刷新令牌无效: " + result.getErrorMessage());
        }
        
        try {
            // 生成新的访问令牌
            String newAccessToken = jwtTokenUtil.generateAccessToken(
                result.getUserId(), result.getMid(), result.getAppKey(), 
                result.getDeviceId(), result.getDeviceType()
            );
            
            // 验证新令牌以获取过期时间
            TokenValidationResult newResult = jwtTokenUtil.validateToken(newAccessToken);
            if (!newResult.isValid()) {
                throw new BusinessException(-1, "生成的新访问令牌验证失败");
            }
            
            // TODO: 更新数据库中的会话信息
            // updateSessionAccessToken(result, newAccessToken, newResult.getExpireTime());
            
            // 构建返回结果
            JwtTokenDto tokenDto = new JwtTokenDto();
            tokenDto.setAccessToken(newAccessToken);
            tokenDto.setRefreshToken(refreshToken); // 刷新令牌保持不变
            tokenDto.setAccessTokenExpireTime(newResult.getExpireTime());
            tokenDto.setRefreshTokenExpireTime(result.getExpireTime());
            tokenDto.setTokenType("Bearer");
            tokenDto.setExpiresIn((newResult.getExpireTime().getTime() - System.currentTimeMillis()) / 1000);
            tokenDto.setUserId(result.getUserId());
            tokenDto.setMid(result.getMid());
            tokenDto.setAppKey(result.getAppKey());
            tokenDto.setDeviceId(result.getDeviceId());
            tokenDto.setDeviceType(result.getDeviceType());
            
            log.info("成功刷新访问令牌，用户ID: {}, 设备ID: {}", result.getUserId(), result.getDeviceId());
            return tokenDto;
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("刷新访问令牌失败，用户ID: {}", result.getUserId(), e);
            throw new BusinessException(-1, "刷新令牌失败");
        }
    }
    
    @Override
    public void revokeToken(String token, String reason) throws BusinessException {
        if (StringUtils.isBlank(token)) {
            throw new BusinessException(-1, "令牌不能为空");
        }
        
        try {
            blacklistManager.addToBlacklist(token, reason);
            log.info("令牌已撤销，原因: {}", reason);
        } catch (Exception e) {
            log.error("撤销令牌失败: {}", e.getMessage(), e);
            throw new BusinessException(-1, "撤销令牌失败");
        }
    }
    
    @Override
    public void revokeAllUserTokens(Long userId, String reason) throws BusinessException {
        if (userId == null) {
            throw new BusinessException(-1, "用户ID不能为空");
        }
        
        try {
            blacklistManager.addUserToBlacklist(userId, reason);
            // TODO: 更新数据库中用户的所有会话状态
            // updateUserSessionsStatus(userId, 1, reason); // 1表示已注销
            log.info("用户所有令牌已撤销，用户ID: {}, 原因: {}", userId, reason);
        } catch (Exception e) {
            log.error("撤销用户所有令牌失败，用户ID: {}", userId, e);
            throw new BusinessException(-1, "撤销用户令牌失败");
        }
    }
    
    @Override
    public void revokeAllDeviceTokens(String deviceId, String reason) throws BusinessException {
        if (StringUtils.isBlank(deviceId)) {
            throw new BusinessException(-1, "设备ID不能为空");
        }
        
        try {
            blacklistManager.addDeviceToBlacklist(deviceId, reason);
            // TODO: 更新数据库中设备的所有会话状态
            // updateDeviceSessionsStatus(deviceId, 3, reason); // 3表示被踢出
            log.info("设备所有令牌已撤销，设备ID: {}, 原因: {}", deviceId, reason);
        } catch (Exception e) {
            log.error("撤销设备所有令牌失败，设备ID: {}", deviceId, e);
            throw new BusinessException(-1, "撤销设备令牌失败");
        }
    }
    
    @Override
    public void logout(String accessToken, String reason) throws BusinessException {
        // 验证访问令牌
        TokenValidationResult result = validateAccessToken(accessToken);
        if (!result.isValid()) {
            throw new BusinessException(-1, "访问令牌无效: " + result.getErrorMessage());
        }
        
        try {
            // 将访问令牌加入黑名单
            blacklistManager.addToBlacklist(accessToken, reason);
            
            // TODO: 同时撤销对应的刷新令牌
            // revokeRefreshTokenBySession(result);
            
            // TODO: 更新数据库中的会话状态
            // updateSessionStatus(result, 1, reason); // 1表示已注销
            
            log.info("用户已登出，用户ID: {}, 设备ID: {}, 原因: {}", 
                    result.getUserId(), result.getDeviceId(), reason);
        } catch (Exception e) {
            log.error("用户登出失败，用户ID: {}", result.getUserId(), e);
            throw new BusinessException(-1, "登出失败");
        }
    }
    
    @Override
    public void logoutAllDevices(Long userId, String reason) throws BusinessException {
        revokeAllUserTokens(userId, reason);
    }
    
    @Override
    public List<JwtTokenDto> getUserActiveSessions(Long userId) throws BusinessException {
        // TODO: 从数据库查询用户的活跃会话
        // 这里返回空列表，实际实现需要查询数据库
        return new ArrayList<>();
    }
    
    @Override
    public void kickoutDevice(Long userId, String deviceId, String reason) throws BusinessException {
        if (userId == null || StringUtils.isBlank(deviceId)) {
            throw new BusinessException(-1, "用户ID和设备ID不能为空");
        }
        
        try {
            // TODO: 查询并撤销指定用户和设备的会话
            // kickoutUserDeviceSession(userId, deviceId, reason);
            
            log.info("设备已被踢出，用户ID: {}, 设备ID: {}, 原因: {}", userId, deviceId, reason);
        } catch (Exception e) {
            log.error("踢出设备失败，用户ID: {}, 设备ID: {}", userId, deviceId, e);
            throw new BusinessException(-1, "踢出设备失败");
        }
    }
    
    @Override
    public void updateLastActiveTime(String accessToken) throws BusinessException {
        // 验证访问令牌
        TokenValidationResult result = validateAccessToken(accessToken);
        if (!result.isValid()) {
            return; // 令牌无效时不更新活跃时间
        }
        
        try {
            // TODO: 更新数据库中的最后活跃时间
            // updateSessionLastActiveTime(result, new Date());
        } catch (Exception e) {
            log.warn("更新最后活跃时间失败，用户ID: {}", result.getUserId(), e);
            // 这里不抛出异常，因为更新活跃时间失败不应该影响主要业务流程
        }
    }
    
    @Override
    public int cleanupExpiredSessions() {
        try {
            // TODO: 清理数据库中过期的会话记录
            // int count = cleanupExpiredSessionsFromDatabase();
            
            // 清理黑名单中过期的记录
            blacklistManager.cleanupExpiredBlacklistEntries();
            
            log.info("会话清理任务执行完成");
            return 0; // 返回清理的记录数
        } catch (Exception e) {
            log.error("清理过期会话失败: {}", e.getMessage(), e);
            return 0;
        }
    }
    
    @Override
    public boolean isTokenExpiringSoon(String accessToken, int minutesBeforeExpiry) {
        return jwtTokenUtil.isTokenExpiringSoon(accessToken, minutesBeforeExpiry);
    }
}
