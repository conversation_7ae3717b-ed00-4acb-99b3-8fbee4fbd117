package com.extracme.evcard.membership.core.service.license;

import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.model.LicenseStatusInfo;
import com.extracme.evcard.rpc.enums.StatusCode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.extracme.evcard.rpc.enums.StatusCode.LICENSE_STATUS_INVALID;

public class LicenseStatusChecker {
    /**
     * 追加点微驾照异常状态 [异常]
     * 追加点微8318 部分驾照异常状态 [异常]
     */
    public static final String[] UNVALID_LICENSE_STATUS = {"超分","暂扣","撤销","吊销","注销","停止使用","锁定","逾期未换证","延期换证","逾期未体检","逾期未审验", "扣留", "异常"
                            , "满分", "延期体检", "转出", "注销可恢复", "撤销", "延期审验", "违法未处理", "事故未处理", "协查", "其他"};
    public  static Integer getLicenseStatus(String licenseStatusMsg) {
        if(StringUtils.containsAny(licenseStatusMsg, UNVALID_LICENSE_STATUS)) {
            return 2;
        }
        return 1;
    }

    public static LicenseStatusInfo check(String drivingLicenseType, String expireDate, String licenseStatusMsg){
        List<StatusCode> licenseCheckResults = checkAllLicenseAuthStatus(drivingLicenseType, expireDate,
                licenseStatusMsg, true);
        LicenseStatusInfo licenseStatusInfo = buildLicenseStatusInfo(licenseCheckResults, licenseStatusMsg);
        return licenseStatusInfo;
    }

    public static StatusCode checkLicenseAuthStatus(String drivingLicenseType, String expireDate, String licenseStatusMsg) {
        return checkLicenseAuthStatus(drivingLicenseType, expireDate, licenseStatusMsg, false);
    }

    public static StatusCode checkLicenseAuthStatus(String drivingLicenseType, String expireDate, String licenseStatusMsg, boolean checkItem) {
        /**
         * 1.判断驾照有效期
         */
        try {
            LocalDate now = LocalDate.now();
            DateTimeFormatter ymd = DateTimeFormatter.ofPattern(ComUtil.DATE_TYPE5);
            LocalDate expirationDate = LocalDate.parse(expireDate, ymd);
            if (now.isAfter(expirationDate)) {
                return StatusCode.DRIVER_EXPIRE;
            }
        } catch (DateTimeParseException e){
            return StatusCode.DRIVER_EXPIRE_DATE_ERROR;
        }
        /**
         * 2.判断驾照准驾类型
         */
        boolean typeCheckFlag = false;
        for (String type : BussinessConstants.DRIVER_LICENSE_TYPE_LIST) {
            if (drivingLicenseType.startsWith(type)) {
                typeCheckFlag = true;
                break;
            }
        }
        if (!typeCheckFlag) {
            return StatusCode.DRIVER_LICENSE_TYPE_ERROR;
        }
        /**
         * 3. 判断驾照状态
         * 已修改为仅保留EVCARD不可用车的驾照状态，因此，此处仅检查此字段不为空即可，
         * 不需要检查具体内容
         */
        if(checkItem) {
            Integer licenseStatus = getLicenseStatus(licenseStatusMsg);
            if (licenseStatus != 1) {
                return LICENSE_STATUS_INVALID;
            }
        }else {
            if (StringUtils.isNotBlank(licenseStatusMsg)) {
                return LICENSE_STATUS_INVALID;
            }
        }
        return null;
    }

    public static List<StatusCode> checkAllLicenseAuthStatus(String drivingLicenseType, String expireDate, String licenseStatusMsg, boolean checkItem) {
        List<StatusCode> checkResults = new ArrayList<>();
        /**
         * 1.判断驾照有效期
         */
        try {
            LocalDate now = LocalDate.now();
            DateTimeFormatter ymd = DateTimeFormatter.ofPattern(ComUtil.DATE_TYPE5);
            LocalDate expirationDate = LocalDate.parse(expireDate, ymd);
            if (now.isAfter(expirationDate)) {
                checkResults.add(StatusCode.DRIVER_EXPIRE);
            }
        } catch (DateTimeParseException e){
            checkResults.add(StatusCode.DRIVER_EXPIRE_DATE_ERROR);
        }
        /**
         * 2.判断驾照准驾类型
         */
        boolean typeCheckFlag = false;
        for (String type : BussinessConstants.DRIVER_LICENSE_TYPE_LIST) {
            if (drivingLicenseType.startsWith(type)) {
                typeCheckFlag = true;
                break;
            }
        }
        if (!typeCheckFlag) {
            checkResults.add(StatusCode.DRIVER_LICENSE_TYPE_ERROR);
        }
        /**
         * 3. 判断驾照状态
         * 已修改为仅保留EVCARD不可用车的驾照状态，因此，此处仅检查此字段不为空即可，
         * 不需要检查具体内容
         */
        if(checkItem) {
            Integer licenseStatus = getLicenseStatus(licenseStatusMsg);
            if (licenseStatus != 1) {
                checkResults.add(LICENSE_STATUS_INVALID);
            }
        }else {
            if (StringUtils.isNotBlank(licenseStatusMsg)) {
                checkResults.add(LICENSE_STATUS_INVALID);
            }
        }
        return checkResults;
    }


    public static LicenseStatusInfo buildLicenseStatusInfo(StatusCode checkResults, String licenseAuthStatusMsg) {
        return buildLicenseStatusInfo(Arrays.asList(checkResults), licenseAuthStatusMsg);
    }

    public static LicenseStatusInfo buildLicenseStatusInfo(List<StatusCode> checkResults, String licenseAuthStatusMsg) {
        LicenseStatusInfo licenseStatusInfo = new LicenseStatusInfo();
        String licenseFileStatusMsg = StringUtils.EMPTY;
        StringBuffer sb = new StringBuffer();
        //单独取出第三方三要素接口返回的驾照证件状态本身，再去跟有效期及准驾车型做
        licenseAuthStatusMsg = getElementsAuthStatusMsg(licenseAuthStatusMsg);
        if(StringUtils.isNotBlank(licenseAuthStatusMsg)) {
            sb.append(licenseAuthStatusMsg);
            licenseStatusInfo.setInvalidLicenseStatus(true);
            licenseFileStatusMsg = licenseAuthStatusMsg;
        }
        if(CollectionUtils.isNotEmpty(checkResults)) {
            for (StatusCode checkItem : checkResults) {
                if(!LICENSE_STATUS_INVALID.equals(checkItem)) {
                    if(StatusCode.DRIVER_EXPIRE.equals(checkItem) || StatusCode.DRIVER_EXPIRE_DATE_ERROR.equals(checkItem)) {
                        licenseStatusInfo.setInvalidExpireDate(true);
                    }else if(StatusCode.DRIVER_LICENSE_TYPE_ERROR.equals(checkItem)) {
                        licenseStatusInfo.setInvalidLicenseType(true);
                    }
                    if(StringUtils.isNotBlank(sb.toString())) {
                        sb.append("；");
                    }
                    sb.append(checkItem.getMsg());
                }
            }
        }
        licenseStatusInfo.setLicenseFileStatusMsg(licenseFileStatusMsg);
        licenseStatusInfo.setLicenseStatusMsg(sb.toString());
        Integer licenseStatus = StringUtils.isNotBlank(licenseStatusInfo.getLicenseStatusMsg()) ? 2 : 1;
        licenseStatusInfo.setLicenseStatus(licenseStatus);
        licenseStatusInfo.setValid(licenseStatus == 1);
        return licenseStatusInfo;
    }


    /**
     * 从会员的licenseStatusMsg字段或者三要素认证接口返回的驾照状态msg中
     * 抽取出evcard不可用的驾照三要素状态值。
     * @param licenseStatusMsg
     * @return
     */
    public static String getElementsAuthStatusMsg(String licenseStatusMsg) {
        if(StringUtils.isBlank(licenseStatusMsg)) {
            return StringUtils.EMPTY;
        }
        StringBuffer sb = new StringBuffer();
        for(String item : UNVALID_LICENSE_STATUS) {
            if(licenseStatusMsg.contains(item)) {
                if(StringUtils.isNotBlank(sb.toString())) {
                    sb.append("/");
                }
                sb.append(item);
            }
        }
        if(StringUtils.isNotBlank(sb.toString())) {
            return String.format(BussinessConstants.PREFIX_LIC_VLALID, sb.toString());
        }
        return StringUtils.EMPTY;
    }

}
