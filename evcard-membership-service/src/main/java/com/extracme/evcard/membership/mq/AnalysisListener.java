package com.extracme.evcard.membership.mq;

import java.math.BigDecimal;

import org.apache.commons.lang.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.extracme.evcard.membership.credit.dao.MmpCreditEventRecordMapper;
import com.extracme.evcard.membership.credit.dao.MmpCreditEventTypeMapper;
import com.extracme.evcard.membership.credit.dao.MmpCreditEventTypeReportMapper;
import com.extracme.evcard.membership.credit.dao.MmpUserAnalysisMapper;
import com.extracme.evcard.membership.credit.dao.MmpUserTagMapper;
import com.extracme.evcard.membership.credit.model.MmpCreditEventRecord;
import com.extracme.evcard.membership.credit.model.MmpCreditEventType;
import com.extracme.evcard.membership.credit.model.MmpCreditEventTypeReport;
import com.extracme.evcard.membership.credit.model.MmpUserAnalysis;
import com.extracme.evcard.mq.bean.AnalysisEnum;
import com.extracme.evcard.mq.bean.analysis.MemberCreditAnnualAward;
import com.extracme.evcard.mq.bean.analysis.MemberCreditEventCount;
import com.extracme.evcard.mq.bean.analysis.MemberCreditMemberCount;
import com.extracme.evcard.mq.bean.analysis.MemberLevelAssess;
import com.extracme.evcard.mq.bean.analysis.MemberLevelCount;
import com.extracme.evcard.protobuf.ProtobufUtil;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: Elin
 * \* Date: 2017/12/5
 * \* Time: 14:15
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 数据统计-信用分
 * \
 */
@Service
public class AnalysisListener implements MessageListener {
    Logger logger = LoggerFactory.getLogger(AnalysisListener.class);

    @Autowired
    private MmpUserTagMapper mmpUserTagMapper;

    @Autowired
    private MmpCreditEventRecordMapper mmpCreditEventRecordMapper;

    @Autowired
    private MmpCreditEventTypeMapper mmpCreditEventTypeMapper;

    @Autowired
    private MmpCreditEventTypeReportMapper mmpCreditEventTypeReportMapper;
    
    @Autowired
    private MmpUserAnalysisMapper mmpUserAnalysisMapper;
    
    String[] gradeListenerEvent = new String[]{
    		AnalysisEnum.MEMBER_LEVEL_ASSESS.getTag(),
    		AnalysisEnum.MEMBER_LEVEL_COUNT.getTag()};
    
    String[] creditListenerEvent = new String[]{
    		AnalysisEnum.MEMBER_CREDIT_ANNUAL_AWARD.toString(),
    		AnalysisEnum.MEMBER_CREDIT_MEMBER_COUNT.toString(),
    		AnalysisEnum.MEMBER_CREDIT_EVENT_COUNT.toString()};

    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        logger.debug("消费: " + message.getMsgID() + ", 标签: " + message.getTag());
        try {
        	 if(ArrayUtils.contains(creditListenerEvent, message.getTag())) {
             	this.creditListener(message, consumeContext);
             }
             if(ArrayUtils.contains(gradeListenerEvent, message.getTag())) {
             	this.gradeListener(message, consumeContext);
             }
            return Action.CommitMessage;
        } catch (Exception e) {
            //消费失败
            logger.error("", e);
            return Action.ReconsumeLater;
        }
    }
    
    public void creditListener(Message message, ConsumeContext consumeContext) {
        if (message.getTag().equals(AnalysisEnum.MEMBER_CREDIT_ANNUAL_AWARD.getTag())) {//会员信用分年度征信奖励
            creditAnnualAwardEvent(message.getBody());
        } else if (message.getTag().equals(AnalysisEnum.MEMBER_CREDIT_MEMBER_COUNT.getTag())) {//会员信用分以会员为维度的统计
            creditMemberCountEvent(message.getBody());
        } else if (message.getTag().equals(AnalysisEnum.MEMBER_CREDIT_EVENT_COUNT.getTag())) {//会员信用分以时间为维度的统计
            creditEventCount(message.getBody());
        }
    }

    /**
     * 会员信用分年度征信奖励
     *
     * @param annualBody
     */
    @Transactional(rollbackFor = Exception.class)
    private void creditAnnualAwardEvent(byte[] annualBody) {
        MemberCreditAnnualAward annualAward = new MemberCreditAnnualAward();
        ProtobufUtil.deserializeProtobuf(annualBody, annualAward);
        MmpCreditEventType creditEventType = mmpCreditEventTypeMapper.selectByPrimaryKey(12L);
        MmpCreditEventRecord mmpCreditEventRecord = new MmpCreditEventRecord();
        mmpCreditEventRecord.setAuthId(annualAward.getAuthId());
        mmpCreditEventRecord.setEventName(creditEventType.getEventName());
        mmpCreditEventRecord.setEventSource("自动触发");
        mmpCreditEventRecord.setEventTypeId(creditEventType.getId());
        mmpCreditEventRecord.setEventNature(creditEventType.getEventNature());
        mmpCreditEventRecord.setAmount(creditEventType.getAmount());
        mmpCreditEventRecord.setEventDesc(creditEventType.getEventName());
        mmpCreditEventRecord.setStatus(1);
        mmpCreditEventRecordMapper.insertSelective(mmpCreditEventRecord);
        mmpUserTagMapper.updateCreditAmountByAuthId(annualAward.getAuthId(), creditEventType.getAmount());
    }


    /**
     * 会员信用分以会员为维度的统计
     *
     * @param memberBody
     */
    @Transactional(rollbackFor = Exception.class)
    private void creditMemberCountEvent(byte[] memberBody) {
        MemberCreditMemberCount memberCount = new MemberCreditMemberCount();
        ProtobufUtil.deserializeProtobuf(memberBody, memberCount);
        MmpCreditEventTypeReport mmpCreditEventTypeReport = new MmpCreditEventTypeReport();
        mmpCreditEventTypeReport.setYearNum(memberCount.getYear());
        mmpCreditEventTypeReport.setOrgId(memberCount.getOrgId());
        mmpCreditEventTypeReport.setType(1);
        mmpCreditEventTypeReport.setEventTypeId(Long.valueOf(memberCount.getEventTypeId()));
        mmpCreditEventTypeReport.setEventName(memberCount.getEventTypeName());
        mmpCreditEventTypeReport.setMonth(Integer.valueOf(memberCount.getMonth()));
        mmpCreditEventTypeReport.setTotal(Integer.valueOf(memberCount.getEventMemberNum()));
        mmpCreditEventTypeReport.setMiscDesc("自动触发");
        mmpCreditEventTypeReport.setCreateOperName("system");
        Integer count = mmpCreditEventTypeReportMapper.selectIsExistTypeReport(mmpCreditEventTypeReport);
        if (count > 0) {
            mmpCreditEventTypeReportMapper.updateExistEventTypeReport(mmpCreditEventTypeReport);
        } else {
            mmpCreditEventTypeReportMapper.insertSelective(mmpCreditEventTypeReport);
        }
    }

    /**
     * 会员信用分以时间为维度的统计
     *
     * @param creditBody
     */
    @Transactional(rollbackFor = Exception.class)
    private void creditEventCount(byte[] creditBody) {
        MemberCreditEventCount creditEventCount = new MemberCreditEventCount();
        ProtobufUtil.deserializeProtobuf(creditBody, creditEventCount);
        MmpCreditEventTypeReport mmpCreditEventTypeReport = new MmpCreditEventTypeReport();
        mmpCreditEventTypeReport.setYearNum(creditEventCount.getYear());
        mmpCreditEventTypeReport.setOrgId(creditEventCount.getOrgId());
        mmpCreditEventTypeReport.setType(0);
        mmpCreditEventTypeReport.setEventTypeId(Long.valueOf(creditEventCount.getEventTypeId()));
        mmpCreditEventTypeReport.setEventName(creditEventCount.getEventTypeName());
        mmpCreditEventTypeReport.setMonth(Integer.valueOf(creditEventCount.getMonth()));
        mmpCreditEventTypeReport.setTotal(Integer.valueOf(creditEventCount.getEventNum()));
        mmpCreditEventTypeReport.setMiscDesc("自动触发");
        mmpCreditEventTypeReport.setCreateOperName("system");
        Integer count = mmpCreditEventTypeReportMapper.selectIsExistTypeReport(mmpCreditEventTypeReport);
        if (count > 0) {
            mmpCreditEventTypeReportMapper.updateExistEventTypeReport(mmpCreditEventTypeReport);
        } else {
            mmpCreditEventTypeReportMapper.insertSelective(mmpCreditEventTypeReport);
        }
    }
    
    public void gradeListener(Message message, ConsumeContext consumeContext) {
        if (message.getTag().equals(AnalysisEnum.MEMBER_LEVEL_ASSESS.getTag())) {//会员等级分析(半年)
            this.memberLevelAssessEventGrade(message.getBody());
        } else if (message.getTag().equals(AnalysisEnum.MEMBER_LEVEL_COUNT.getTag())) {//会员等级统计（每月）
            this.memberLevelCountEventGrade(message.getBody());
        }
    }

    /**
     * 会员等级统计
     *
     * @param levelBody
     */
    @Transactional(rollbackFor = Exception.class)
    private void memberLevelCountEventGrade(byte[] levelBody) {
        MemberLevelCount memberLevelCount = new MemberLevelCount();
        ProtobufUtil.deserializeProtobuf(levelBody, memberLevelCount);
        MmpUserAnalysis analysis = new MmpUserAnalysis();
        BeanUtils.copyProperties(memberLevelCount, analysis);
        analysis.setYear(Integer.valueOf(memberLevelCount.getYear()));
        analysis.setMonth(Integer.valueOf(memberLevelCount.getMonth()));
        analysis.setOrgId(memberLevelCount.getOrgID());
        analysis.setUserLevel(memberLevelCount.getUserLevel());
        analysis.setRemark("自动触发");
        analysis.setCreateOperName("system");
        Integer count = mmpUserAnalysisMapper.selectIsExistUserAnalysis(analysis);
        if (count > 0) {
            mmpUserAnalysisMapper.updateExistUserAnalysis(analysis);
        } else {
            mmpUserAnalysisMapper.insertSelective(analysis);
        }
    }

    /**
     * 会员等级分析
     *
     * @param assessBody
     */
    @Transactional(rollbackFor = Exception.class)
    private void memberLevelAssessEventGrade(byte[] assessBody) {
        MemberLevelAssess memberLevelAssess = new MemberLevelAssess();
        ProtobufUtil.deserializeProtobuf(assessBody, memberLevelAssess);
        String oldValue = memberLevelAssess.getOldValue();
        BigDecimal bOldValue = oldValue == null ? new BigDecimal(0) : new BigDecimal(oldValue);
        String value = memberLevelAssess.getValue();
        BigDecimal bValue = value == null ? new BigDecimal(0) : new BigDecimal(value);
        mmpUserTagMapper.updateEffectiveContdByAuthId(memberLevelAssess.getAuthId(), bValue , bOldValue);
    }

}