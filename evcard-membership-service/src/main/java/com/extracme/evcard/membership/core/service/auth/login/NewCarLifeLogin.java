package com.extracme.evcard.membership.core.service.auth.login;

import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.input.ThirdLoginContext;
import com.extracme.evcard.membership.core.input.ThirdLoginInput;
import com.extracme.evcard.membership.credit.dao.MmpUserTagMapper;
import com.extracme.evcard.membership.credit.model.MmpUserTag;
import com.extracme.evcard.rpc.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 新支付宝车生活 登录注册
 */
@Slf4j
@Service
public class NewCarLifeLogin extends QingLuLogin {

    @Autowired
    private MmpUserTagMapper mmpUserTagMapper;

    @Override
    public String getSecondAppKey() {
        return BussinessConstants.CAR_LIFE_SECOND_APP_KEY;
    }

    @Override
    public String getDescription() {
        return "新支付宝车生活";
    }

    @Override
    public void afterGetMembershipInfo(ThirdLoginContext context) throws BusinessException {
        super.afterGetMembershipInfo(context);

        ThirdLoginInput input = context.getInput();
        String openId = input.getOpenId();
        String zfbUserId = input.getZfbUserId();

        // 创建 user_tag 记录
        MembershipBasicInfo membershipBasicInfo = context.getMembershipBasicInfo();
        String authId = membershipBasicInfo.getAuthId();
        MmpUserTag mmpUserTag = mmpUserTagMapper.selectMmpUserByAuthId(authId);

        MmpUserTag userTag = new MmpUserTag();
        userTag.setAuthId(authId);
        if (StringUtils.isNotBlank(openId)) {
            userTag.setAliOpenId(openId);
        }
        if (StringUtils.isNotBlank(zfbUserId)) {
            userTag.setSpare7(zfbUserId);
        }

        // 判断 user_tag是否存在
        if (mmpUserTag == null) {
            mmpUserTagMapper.insertSelective(userTag);
        }else{
            mmpUserTagMapper.updateZfbIdByAuthId(userTag);
        }
    }

    @Override
    public void paramCheck(ThirdLoginContext context) throws BusinessException {
        super.paramCheck(context);

        ThirdLoginInput input = context.getInput();
        if (StringUtils.isBlank(input.getOpenId()) && StringUtils.isBlank(input.getZfbUserId())) {
            throw new BusinessException(-1, "入参openid或者zfbUserId不能同时为空");
        }
    }
}
