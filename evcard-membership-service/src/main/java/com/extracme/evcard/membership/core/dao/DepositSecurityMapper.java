package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.model.DepositSecurity;

public interface DepositSecurityMapper {
    int deleteByPrimaryKey(Long id);

    int insert(DepositSecurity record);

    int insertSelective(DepositSecurity record);

    DepositSecurity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DepositSecurity record);

    int updateByPrimaryKey(DepositSecurity record);
}