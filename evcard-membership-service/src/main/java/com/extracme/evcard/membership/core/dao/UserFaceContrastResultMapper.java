package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.model.UserFaceContrastResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UserFaceContrastResultMapper {

    int deleteByPrimaryKey(Long id);

    int insert(UserFaceContrastResult record);

    int insertSelective(UserFaceContrastResult record);

    UserFaceContrastResult selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UserFaceContrastResult record);

    int updateByPrimaryKey(UserFaceContrastResult record);
}
