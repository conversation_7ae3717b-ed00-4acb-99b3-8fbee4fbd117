package com.extracme.evcard.membership.core.model;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class DriverLicenseElementsAuthenticateRecord {
    private Long id;

    private Long userId;

    private String name;

    private String driverCode;

    private String fileNo;

    private String driverLicenseImgUrl;

    private String fileNoImgUrl;

    /**
     * 认证状态 0待认证 1认证通过 2认证不通过 3查证中(待重新认证)
     */
    private Integer authenticateStatus;

    /**
     * 驾照状态 0不可用 1可用
     */
    private Integer licenseStatus;

    /**
     * 驾照状态描述
     */
    private String licenseStatusMsg;

    private Integer status;

    private String miscDesc;

    private Date createTime;

    private Long createOperId;

    private String createOperName;

    private Date updateTime;

    private Long updateOperId;

    private String updateOperName;

    /**
     * elementsReviewItems: 驾照号/姓名/档案编号
     * 0不一致 1一致
     * 此列实际不保存
     */
    private String elementsReviewItems;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDriverCode() {
        return driverCode;
    }

    public void setDriverCode(String driverCode) {
        this.driverCode = driverCode;
    }

    public String getFileNo() {
        return fileNo;
    }

    public void setFileNo(String fileNo) {
        this.fileNo = fileNo;
    }

    public String getDriverLicenseImgUrl() {
        return driverLicenseImgUrl;
    }

    public void setDriverLicenseImgUrl(String driverLicenseImgUrl) {
        this.driverLicenseImgUrl = driverLicenseImgUrl;
    }

    public String getFileNoImgUrl() {
        return fileNoImgUrl;
    }

    public void setFileNoImgUrl(String fileNoImgUrl) {
        this.fileNoImgUrl = fileNoImgUrl;
    }

    public Integer getAuthenticateStatus() {
        return authenticateStatus;
    }

    public void setAuthenticateStatus(Integer authenticateStatus) {
        this.authenticateStatus = authenticateStatus;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMiscDesc() {
        return miscDesc;
    }

    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateOperId() {
        return createOperId;
    }

    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    public String getCreateOperName() {
        return createOperName;
    }

    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdateOperId() {
        return updateOperId;
    }

    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    public String getUpdateOperName() {
        return updateOperName;
    }

    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }

    public Integer getLicenseStatus() {
        return licenseStatus;
    }

    public void setLicenseStatus(Integer licenseStatus) {
        this.licenseStatus = licenseStatus;
    }

    public String getLicenseStatusMsg() {
        return licenseStatusMsg;
    }

    public void setLicenseStatusMsg(String licenseStatusMsg) {
        this.licenseStatusMsg = licenseStatusMsg;
    }

    public String getElementsReviewItems() {
        return elementsReviewItems;
    }

    public void setElementsReviewItems(String elementsReviewItems) {
        this.elementsReviewItems = elementsReviewItems;
    }
}