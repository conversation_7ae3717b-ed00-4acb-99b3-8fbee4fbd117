package com.extracme.evcard.membership.common;

import java.util.concurrent.*;

/**
 * 线程池工具类
 * <AUTHOR>
 * @since 2017/6/21
 */
public class ThreadPoolUtils {

    /** 自定义线程池 */
    public static final ExecutorService EXECUTOR =  new ThreadPoolExecutor(4, 30, 60, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(5),new ThreadPoolExecutor.CallerRunsPolicy());

    public static final ScheduledExecutorService SCHEDULED_EXECUTOR = new ScheduledThreadPoolExecutor(4,
            new ThreadPoolExecutor.CallerRunsPolicy());

    public static final ExecutorService EXECUTOR_OCR =  new ThreadPoolExecutor(4, 30, 60, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(5),new ThreadPoolExecutor.CallerRunsPolicy());

    public static final ExecutorService WXQRCODE_EXECUTOR =  new ThreadPoolExecutor(2, 4, 60, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(1000),new ThreadPoolExecutor.CallerRunsPolicy());
}


