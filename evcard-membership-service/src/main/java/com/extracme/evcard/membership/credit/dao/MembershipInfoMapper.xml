<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.credit.dao.MembershipInfoMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.credit.model.MembershipInfo" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 04 15:27:31 CST 2017.
    -->
    <id column="pk_id" property="pkId" jdbcType="BIGINT" />
    <result column="MID" property="mid" jdbcType="VARCHAR" />
    <result column="AUTH_ID" property="authId" jdbcType="VARCHAR" />
    <result column="UID" property="uid" jdbcType="VARCHAR" />
    <result column="DRIVER_CODE" property="driverCode" jdbcType="VARCHAR" />
    <result column="OPEN_ID" property="openId" jdbcType="VARCHAR" />
    <result column="NAME" property="name" jdbcType="VARCHAR" />
    <result column="PASSWORD" property="password" jdbcType="VARCHAR" />
    <result column="GENDER" property="gender" jdbcType="VARCHAR" />
    <result column="MOBILE_PHONE" property="mobilePhone" jdbcType="VARCHAR" />
    <result column="MAIL" property="mail" jdbcType="VARCHAR" />
    <result column="BIRTH_DATE" property="birthDate" jdbcType="VARCHAR" />
    <result column="REG_TIME" property="regTime" jdbcType="VARCHAR" />
    <result column="ZIP" property="zip" jdbcType="VARCHAR" />
    <result column="ADDRESS" property="address" jdbcType="VARCHAR" />
    <result column="PROVINCE" property="province" jdbcType="VARCHAR" />
    <result column="CITY" property="city" jdbcType="VARCHAR" />
    <result column="AREA" property="area" jdbcType="VARCHAR" />
    <result column="MEMBERSHIP_TYPE" property="membershipType" jdbcType="DECIMAL" />
    <result column="USER_LEVEL" property="userLevel" jdbcType="DECIMAL" />
    <result column="ORG_ID" property="orgId" jdbcType="VARCHAR" />
    <result column="AGENCY_ID" property="agencyId" jdbcType="VARCHAR" />
    <result column="AUTH_KIND" property="authKind" jdbcType="VARCHAR" />
    <result column="CERTIFICATE_VALIDITY" property="certificateValidity" jdbcType="VARCHAR" />
    <result column="CERTIFICATE_ADDRESS" property="certificateAddress" jdbcType="VARCHAR" />
    <result column="DRIVING_LICENSE" property="drivingLicense" jdbcType="VARCHAR" />
    <result column="OBTAIN_DRIVER_TIMER" property="obtainDriverTimer" jdbcType="VARCHAR" />
    <result column="LICENSE_EXPIRATION_TIME" property="licenseExpirationTime" jdbcType="VARCHAR" />
    <result column="EMERGENCY_CONTACT" property="emergencyContact" jdbcType="VARCHAR" />
    <result column="EMERGENCY_MOBIL" property="emergencyMobil" jdbcType="VARCHAR" />
    <result column="GUARANTEE_NAME" property="guaranteeName" jdbcType="VARCHAR" />
    <result column="GUARANTEE_IC_CARD_KIND" property="guaranteeIcCardKind" jdbcType="DECIMAL" />
    <result column="GUARANTEE_IC_CARD" property="guaranteeIcCard" jdbcType="VARCHAR" />
    <result column="GUARANTEE_MOBIL" property="guaranteeMobil" jdbcType="VARCHAR" />
    <result column="REVIEW_REASON" property="reviewReason" jdbcType="VARCHAR" />
    <result column="REVIEW_STATUS" property="reviewStatus" jdbcType="DECIMAL" />
    <result column="CARD_NO" property="cardNo" jdbcType="VARCHAR" />
    <result column="CREDIT_NO" property="creditNo" jdbcType="VARCHAR" />
    <result column="DATA_ORIGIN" property="dataOrigin" jdbcType="DECIMAL" />
    <result column="ID" property="id" jdbcType="DECIMAL" />
    <result column="STATUS" property="status" jdbcType="DECIMAL" />
    <result column="BLACKLIST_REASON" property="blacklistReason" jdbcType="VARCHAR" />
    <result column="REVIEW_USER" property="reviewUser" jdbcType="VARCHAR" />
    <result column="REVIEW_ITEMS" property="reviewItems" jdbcType="VARCHAR" />
    <result column="ILLEGAL_METHOD" property="illegalMethod" jdbcType="DECIMAL" />
    <result column="DEPOSIT" property="deposit" jdbcType="DECIMAL" />
    <result column="RESERVE_AMOUNT" property="reserveAmount" jdbcType="DECIMAL" />
    <result column="RENT_MINS" property="rentMins" jdbcType="DECIMAL" />
    <result column="EXEMPT_DEPOSIT" property="exemptDeposit" jdbcType="DECIMAL" />
    <result column="EMPNO" property="empno" jdbcType="VARCHAR" />
    <result column="INFO_ORIGIN" property="infoOrigin" jdbcType="VARCHAR" />
    <result column="CREATED_TIME" property="createdTime" jdbcType="VARCHAR" />
    <result column="CREATED_USER" property="createdUser" jdbcType="VARCHAR" />
    <result column="UPDATED_TIME" property="updatedTime" jdbcType="VARCHAR" />
    <result column="UPDATED_USER" property="updatedUser" jdbcType="VARCHAR" />
    <result column="PERSONNEL_STATE" property="personnelState" jdbcType="DECIMAL" />
    <result column="SHARE_UID" property="shareUid" jdbcType="VARCHAR" />
    <result column="EZBIKE" property="ezbike" jdbcType="DECIMAL" />
    <result column="APPLY_STATUS" property="applyStatus" jdbcType="DECIMAL" />
    <result column="SERVICE_VER" property="serviceVer" jdbcType="VARCHAR" />
    <result column="SERVICE_VER_TIME" property="serviceVerTime" jdbcType="VARCHAR" />
    <result column="APP_KEY" property="appKey" jdbcType="VARCHAR" />
    <result column="TYPE_FLAG" property="typeFlag" jdbcType="DECIMAL" />
    <result column="INVOICED_AMOUNT" property="invoicedAmount" jdbcType="DECIMAL" />
    <result column="CHANNEL_ID" property="channelId" jdbcType="VARCHAR" />
    <result column="REGIONID" property="regionid" jdbcType="BIGINT" />
    <result column="deposit_vehicle" property="depositVehicle" jdbcType="DECIMAL" />
    <result column="province_of_origin" property="provinceOfOrigin" jdbcType="VARCHAR" />
    <result column="city_of_origin" property="cityOfOrigin" jdbcType="VARCHAR" />
    <result column="area_of_origin" property="areaOfOrigin" jdbcType="VARCHAR" />
    <result column="idcard_pic_url" property="idcardPicUrl" jdbcType="VARCHAR" />
    <result column="hold_idcard_pic_url" property="holdIdcardPicUrl" jdbcType="VARCHAR" />
    <result column="point" property="point" jdbcType="INTEGER" />
    <result column="REVIEW_TIME" property="reviewTime" jdbcType="VARCHAR" />
    <result column="APP_REVIEW_TIME" property="appReviewTime" jdbcType="VARCHAR" />
    <result column="REVIEW_REMARK" property="reviewRemark" jdbcType="VARCHAR" />
    <result column="REVIEW_ITEM_IDS" property="reviewItemIds" jdbcType="VARCHAR" />
    <result column="REVIEW_ITEM_NAME" property="reviewItemName" jdbcType="VARCHAR" />
    <result column="REVIEW_MODE" property="reviewMode" jdbcType="INTEGER" />
    <result column="FACE_RECOGNITION_IMG_URL" property="faceRecognitionImgUrl" jdbcType="VARCHAR" />
    <result column="customer_id" property="customerId" jdbcType="VARCHAR" />
    <result column="id_card_number" property="idCardNumber" jdbcType="VARCHAR" />
    <result column="authentication_status" property="authenticationStatus" jdbcType="INTEGER"/>
    <result column="national" property="national" jdbcType="VARCHAR" />
    <result column="driving_license_type" property="drivingLicenseType" jdbcType="VARCHAR"/>
    <result column="driver_license_input_type" property="driverLicenseInputType" jdbcType="VARCHAR"/>
    <result column="passport_no" property="passportNo" jdbcType="VARCHAR"/>
    <result column="file_no" property="fileNo" jdbcType="VARCHAR"/>
    <result column="file_no_img_url" property="fileNoImgUrl" jdbcType="VARCHAR"/>
    <result column="id_type" property="idType" jdbcType="INTEGER"/>
    <result column="ACCOUNT_STATUS" property="accountStatus" jdbcType="INTEGER"/>
    <result column="UNREGISTER_TIME" property="unRegisterTime" jdbcType="VARCHAR"/>
    <result column="license_auth_status" property="licenseAuthStatus" jdbcType="INTEGER"/>
    <result column="license_elements_auth_status" property="licenseElementsAuthStatus" jdbcType="INTEGER"/>
    <result column="license_status_msg" property="licenseStatusMsg" jdbcType="VARCHAR"/>
    <result column="elements_review_items" property="elementsReviewItems" jdbcType="VARCHAR"/>

    <result column="identity_id" jdbcType="BIGINT" property="identityId" />
    <result column="FREEZE_DEPOSIT" property="freezeDeposit" jdbcType="DECIMAL" />
    <result column="manufacturer" property="manufacturer" jdbcType="VARCHAR"/>
    <result column="is_fictional" property="isFictional" jdbcType="INTEGER"/>
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs" extends="BaseResultMap" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 04 15:27:31 CST 2017.
    -->
    <result column="USER_IMG_URL" property="userImgUrl" jdbcType="LONGVARCHAR" />
    <result column="DRIVING_LICENSE_IMG_URL" property="drivingLicenseImgUrl" jdbcType="LONGVARCHAR" />
    <result column="IDENTITY_CARD_IMG_URL" property="identityCardImgUrl" jdbcType="LONGVARCHAR" />
    <result column="MARK" property="mark" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 04 15:27:31 CST 2017.
    -->
    pk_id, MID, AUTH_ID, UID, DRIVER_CODE, OPEN_ID, NAME, PASSWORD, GENDER, MOBILE_PHONE, MAIL,
    BIRTH_DATE, REG_TIME, ZIP, ADDRESS, PROVINCE, CITY, AREA, MEMBERSHIP_TYPE, USER_LEVEL, 
    ORG_ID, AGENCY_ID, AUTH_KIND, CERTIFICATE_VALIDITY, CERTIFICATE_ADDRESS, DRIVING_LICENSE, 
    OBTAIN_DRIVER_TIMER, LICENSE_EXPIRATION_TIME, EMERGENCY_CONTACT, EMERGENCY_MOBIL, 
    GUARANTEE_NAME, GUARANTEE_IC_CARD_KIND, GUARANTEE_IC_CARD, GUARANTEE_MOBIL, REVIEW_REASON, 
    REVIEW_STATUS, CARD_NO, CREDIT_NO, DATA_ORIGIN, ID, STATUS, BLACKLIST_REASON, REVIEW_USER, 
    REVIEW_ITEMS, ILLEGAL_METHOD, DEPOSIT, RESERVE_AMOUNT, RENT_MINS, EXEMPT_DEPOSIT, 
    EMPNO, INFO_ORIGIN, CREATED_TIME, CREATED_USER, UPDATED_TIME, UPDATED_USER, PERSONNEL_STATE, 
    SHARE_UID, EZBIKE, APPLY_STATUS, SERVICE_VER, SERVICE_VER_TIME, APP_KEY, TYPE_FLAG, 
    INVOICED_AMOUNT, CHANNEL_ID, REGIONID, deposit_vehicle, province_of_origin, city_of_origin, 
    area_of_origin, idcard_pic_url, hold_idcard_pic_url, point, REVIEW_TIME, APP_REVIEW_TIME,
    REVIEW_REMARK, REVIEW_ITEM_IDS, REVIEW_ITEM_NAME, REVIEW_MODE, FACE_RECOGNITION_IMG_URL,ACCOUNT_STATUS,
    customer_id, id_card_number,authentication_status,national,driving_license_type,driver_license_input_type,passport_no,
    file_no,file_no_img_url,id_type,license_auth_status,license_elements_auth_status,elements_review_items, license_status_msg,
    identity_id,license_first_auth_time,FREEZE_DEPOSIT,is_fictional
  </sql>
  <sql id="Blob_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 04 15:27:31 CST 2017.
    -->
    USER_IMG_URL, DRIVING_LICENSE_IMG_URL, IDENTITY_CARD_IMG_URL, MARK
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 04 15:27:31 CST 2017.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ${siacSchema}.membership_info
    where pk_id = #{pkId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 04 15:27:31 CST 2017.
    -->
    delete from ${siacSchema}.membership_info
    where pk_id = #{pkId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 04 15:27:31 CST 2017.
    -->
    insert into ${siacSchema}.membership_info (pk_id, AUTH_ID, DRIVER_CODE,
      OPEN_ID, NAME, PASSWORD, 
      GENDER, MOBILE_PHONE, MAIL, 
      BIRTH_DATE, REG_TIME, ZIP, 
      ADDRESS, PROVINCE, CITY, 
      AREA, MEMBERSHIP_TYPE, USER_LEVEL, 
      ORG_ID, AGENCY_ID, AUTH_KIND, 
      CERTIFICATE_VALIDITY, CERTIFICATE_ADDRESS,
      DRIVING_LICENSE, OBTAIN_DRIVER_TIMER, LICENSE_EXPIRATION_TIME, 
      EMERGENCY_CONTACT, EMERGENCY_MOBIL, GUARANTEE_NAME, 
      GUARANTEE_IC_CARD_KIND, GUARANTEE_IC_CARD, GUARANTEE_MOBIL, 
      REVIEW_REASON, REVIEW_STATUS, CARD_NO, 
      CREDIT_NO, DATA_ORIGIN, ID, 
      STATUS, BLACKLIST_REASON, REVIEW_USER, 
      REVIEW_ITEMS, ILLEGAL_METHOD, DEPOSIT, 
      RESERVE_AMOUNT, RENT_MINS, EXEMPT_DEPOSIT, 
      EMPNO, INFO_ORIGIN, CREATED_TIME, 
      CREATED_USER, UPDATED_TIME, UPDATED_USER, 
      PERSONNEL_STATE, SHARE_UID, EZBIKE, 
      APPLY_STATUS, SERVICE_VER, SERVICE_VER_TIME, 
      APP_KEY, TYPE_FLAG, INVOICED_AMOUNT, 
      CHANNEL_ID, REGIONID, deposit_vehicle, 
      province_of_origin, city_of_origin, area_of_origin, 
      idcard_pic_url, hold_idcard_pic_url, point, 
      REVIEW_TIME, APP_REVIEW_TIME, REVIEW_REMARK, 
      REVIEW_ITEM_IDS, REVIEW_ITEM_NAME, REVIEW_MODE, 
      FACE_RECOGNITION_IMG_URL, USER_IMG_URL, 
      DRIVING_LICENSE_IMG_URL, IDENTITY_CARD_IMG_URL, 
      MARK)
    values (#{pkId,jdbcType=BIGINT}, #{authId,jdbcType=VARCHAR}, #{driverCode,jdbcType=VARCHAR}, 
      #{openId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR}, 
      #{gender,jdbcType=VARCHAR}, #{mobilePhone,jdbcType=VARCHAR}, #{mail,jdbcType=VARCHAR}, 
      #{birthDate,jdbcType=VARCHAR}, #{regTime,jdbcType=VARCHAR}, #{zip,jdbcType=VARCHAR}, 
      #{address,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, 
      #{area,jdbcType=VARCHAR}, #{membershipType,jdbcType=DECIMAL}, #{userLevel,jdbcType=DECIMAL}, 
      #{orgId,jdbcType=VARCHAR}, #{agencyId,jdbcType=VARCHAR}, #{authKind,jdbcType=VARCHAR}, 
      #{certificateValidity,jdbcType=VARCHAR}, #{certificateAddress,jdbcType=VARCHAR}, 
      #{drivingLicense,jdbcType=VARCHAR}, #{obtainDriverTimer,jdbcType=VARCHAR}, #{licenseExpirationTime,jdbcType=VARCHAR}, 
      #{emergencyContact,jdbcType=VARCHAR}, #{emergencyMobil,jdbcType=VARCHAR}, #{guaranteeName,jdbcType=VARCHAR}, 
      #{guaranteeIcCardKind,jdbcType=DECIMAL}, #{guaranteeIcCard,jdbcType=VARCHAR}, #{guaranteeMobil,jdbcType=VARCHAR}, 
      #{reviewReason,jdbcType=VARCHAR}, #{reviewStatus,jdbcType=DECIMAL}, #{cardNo,jdbcType=VARCHAR}, 
      #{creditNo,jdbcType=VARCHAR}, #{dataOrigin,jdbcType=DECIMAL}, #{id,jdbcType=DECIMAL}, 
      #{status,jdbcType=DECIMAL}, #{blacklistReason,jdbcType=VARCHAR}, #{reviewUser,jdbcType=VARCHAR}, 
      #{reviewItems,jdbcType=VARCHAR}, #{illegalMethod,jdbcType=DECIMAL}, #{deposit,jdbcType=DECIMAL}, 
      #{reserveAmount,jdbcType=DECIMAL}, #{rentMins,jdbcType=DECIMAL}, #{exemptDeposit,jdbcType=DECIMAL}, 
      #{empno,jdbcType=VARCHAR}, #{infoOrigin,jdbcType=VARCHAR}, #{createdTime,jdbcType=VARCHAR}, 
      #{createdUser,jdbcType=VARCHAR}, #{updatedTime,jdbcType=VARCHAR}, #{updatedUser,jdbcType=VARCHAR}, 
      #{personnelState,jdbcType=DECIMAL}, #{shareUid,jdbcType=VARCHAR}, #{ezbike,jdbcType=DECIMAL}, 
      #{applyStatus,jdbcType=DECIMAL}, #{serviceVer,jdbcType=VARCHAR}, #{serviceVerTime,jdbcType=VARCHAR}, 
      #{appKey,jdbcType=VARCHAR}, #{typeFlag,jdbcType=DECIMAL}, #{invoicedAmount,jdbcType=DECIMAL}, 
      #{channelId,jdbcType=VARCHAR}, #{regionid,jdbcType=BIGINT}, #{depositVehicle,jdbcType=DECIMAL}, 
      #{provinceOfOrigin,jdbcType=VARCHAR}, #{cityOfOrigin,jdbcType=VARCHAR}, #{areaOfOrigin,jdbcType=VARCHAR}, 
      #{idcardPicUrl,jdbcType=VARCHAR}, #{holdIdcardPicUrl,jdbcType=VARCHAR}, #{point,jdbcType=INTEGER}, 
      #{reviewTime,jdbcType=VARCHAR}, #{appReviewTime,jdbcType=VARCHAR}, #{reviewRemark,jdbcType=VARCHAR}, 
      #{reviewItemIds,jdbcType=VARCHAR}, #{reviewItemName,jdbcType=VARCHAR}, #{reviewMode,jdbcType=INTEGER}, 
      #{faceRecognitionImgUrl,jdbcType=VARCHAR}, #{userImgUrl,jdbcType=LONGVARCHAR}, 
      #{drivingLicenseImgUrl,jdbcType=LONGVARCHAR}, #{identityCardImgUrl,jdbcType=LONGVARCHAR}, 
      #{mark,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 04 15:27:31 CST 2017.
    -->
    insert into ${siacSchema}.membership_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="pkId != null" >
        pk_id,
      </if>
      <if test="mid != null" >
        mid,
      </if>
      <if test="authId != null" >
        AUTH_ID,
      </if>
      <if test="uid != null" >
        UID,
      </if>
      <if test="driverCode != null" >
        DRIVER_CODE,
      </if>
      <if test="openId != null" >
        OPEN_ID,
      </if>
      <if test="name != null" >
        NAME,
      </if>
      <if test="password != null" >
        PASSWORD,
      </if>
      <if test="gender != null" >
        GENDER,
      </if>
      <if test="mobilePhone != null" >
        MOBILE_PHONE,
      </if>
      <if test="mail != null" >
        MAIL,
      </if>
      <if test="birthDate != null" >
        BIRTH_DATE,
      </if>
      <if test="regTime != null" >
        REG_TIME,
      </if>
      <if test="zip != null" >
        ZIP,
      </if>
      <if test="address != null" >
        ADDRESS,
      </if>
      <if test="province != null" >
        PROVINCE,
      </if>
      <if test="city != null" >
        CITY,
      </if>
      <if test="area != null" >
        AREA,
      </if>
      <if test="membershipType != null" >
        MEMBERSHIP_TYPE,
      </if>
      <if test="userLevel != null" >
        USER_LEVEL,
      </if>
      <if test="orgId != null" >
        ORG_ID,
      </if>
      <if test="agencyId != null" >
        AGENCY_ID,
      </if>
      <if test="authKind != null" >
        AUTH_KIND,
      </if>
      <if test="certificateValidity != null" >
        CERTIFICATE_VALIDITY,
      </if>
      <if test="certificateAddress != null" >
        CERTIFICATE_ADDRESS,
      </if>
      <if test="drivingLicense != null" >
        DRIVING_LICENSE,
      </if>
      <if test="obtainDriverTimer != null" >
        OBTAIN_DRIVER_TIMER,
      </if>
      <if test="licenseExpirationTime != null" >
        LICENSE_EXPIRATION_TIME,
      </if>
      <if test="emergencyContact != null" >
        EMERGENCY_CONTACT,
      </if>
      <if test="emergencyMobil != null" >
        EMERGENCY_MOBIL,
      </if>
      <if test="guaranteeName != null" >
        GUARANTEE_NAME,
      </if>
      <if test="guaranteeIcCardKind != null" >
        GUARANTEE_IC_CARD_KIND,
      </if>
      <if test="guaranteeIcCard != null" >
        GUARANTEE_IC_CARD,
      </if>
      <if test="guaranteeMobil != null" >
        GUARANTEE_MOBIL,
      </if>
      <if test="reviewReason != null" >
        REVIEW_REASON,
      </if>
      <if test="reviewStatus != null" >
        REVIEW_STATUS,
      </if>
      <if test="cardNo != null" >
        CARD_NO,
      </if>
      <if test="creditNo != null" >
        CREDIT_NO,
      </if>
      <if test="dataOrigin != null" >
        DATA_ORIGIN,
      </if>
      <if test="id != null" >
        ID,
      </if>
      <if test="status != null" >
        STATUS,
      </if>
      <if test="blacklistReason != null" >
        BLACKLIST_REASON,
      </if>
      <if test="reviewUser != null" >
        REVIEW_USER,
      </if>
      <if test="reviewItems != null" >
        REVIEW_ITEMS,
      </if>
      <if test="illegalMethod != null" >
        ILLEGAL_METHOD,
      </if>
      <if test="deposit != null" >
        DEPOSIT,
      </if>
      <if test="reserveAmount != null" >
        RESERVE_AMOUNT,
      </if>
      <if test="rentMins != null" >
        RENT_MINS,
      </if>
      <if test="exemptDeposit != null" >
        EXEMPT_DEPOSIT,
      </if>
      <if test="empno != null" >
        EMPNO,
      </if>
      <if test="infoOrigin != null" >
        INFO_ORIGIN,
      </if>
      <if test="createdTime != null" >
        CREATED_TIME,
      </if>
      <if test="createdUser != null" >
        CREATED_USER,
      </if>
      <if test="updatedTime != null" >
        UPDATED_TIME,
      </if>
      <if test="updatedUser != null" >
        UPDATED_USER,
      </if>
      <if test="personnelState != null" >
        PERSONNEL_STATE,
      </if>
      <if test="shareUid != null" >
        SHARE_UID,
      </if>
      <if test="ezbike != null" >
        EZBIKE,
      </if>
      <if test="applyStatus != null" >
        APPLY_STATUS,
      </if>
      <if test="serviceVer != null" >
        SERVICE_VER,
      </if>
      <if test="serviceVerTime != null" >
        SERVICE_VER_TIME,
      </if>
      <if test="appKey != null" >
        APP_KEY,
      </if>
      <if test="typeFlag != null" >
        TYPE_FLAG,
      </if>
      <if test="invoicedAmount != null" >
        INVOICED_AMOUNT,
      </if>
      <if test="channelId != null" >
        CHANNEL_ID,
      </if>
      <if test="regionid != null" >
        REGIONID,
      </if>
      <if test="depositVehicle != null" >
        deposit_vehicle,
      </if>
      <if test="provinceOfOrigin != null" >
        province_of_origin,
      </if>
      <if test="cityOfOrigin != null" >
        city_of_origin,
      </if>
      <if test="areaOfOrigin != null" >
        area_of_origin,
      </if>
      <if test="idcardPicUrl != null" >
        idcard_pic_url,
      </if>
      <if test="holdIdcardPicUrl != null" >
        hold_idcard_pic_url,
      </if>
      <if test="point != null" >
        point,
      </if>
      <if test="reviewTime != null" >
        REVIEW_TIME,
      </if>
      <if test="appReviewTime != null" >
        APP_REVIEW_TIME,
      </if>
      <if test="reviewRemark != null" >
        REVIEW_REMARK,
      </if>
      <if test="reviewItemIds != null" >
        REVIEW_ITEM_IDS,
      </if>
      <if test="reviewItemName != null" >
        REVIEW_ITEM_NAME,
      </if>
      <if test="reviewMode != null" >
        REVIEW_MODE,
      </if>
      <if test="faceRecognitionImgUrl != null" >
        FACE_RECOGNITION_IMG_URL,
      </if>
      <if test="drivingLicenseType != null">
        driving_license_type,
      </if>
      <if test="userImgUrl != null" >
        USER_IMG_URL,
      </if>
      <if test="drivingLicenseImgUrl != null" >
        DRIVING_LICENSE_IMG_URL,
      </if>
      <if test="identityCardImgUrl != null" >
        IDENTITY_CARD_IMG_URL,
      </if>
      <if test="mark != null" >
        MARK,
      </if>
      <if test="authenticationStatus != null">
        authentication_status,
      </if>
      <if test="unRegisterTime != null">
        UNREGISTER_TIME,
      </if>
      <if test="identityId != null">
        identity_id,
      </if>
      <if test="secondAppKey != null">
        second_app_key,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
      <if test="passportNo != null">
        passport_no,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="isFictional != null">
        is_fictional,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="pkId != null" >
        #{pkId,jdbcType=BIGINT},
      </if>
      <if test="mid != null" >
        #{mid,jdbcType=VARCHAR},
      </if>
      <if test="authId != null" >
        #{authId,jdbcType=VARCHAR},
      </if>
      <if test="uid != null" >
        #{uid,jdbcType=VARCHAR},
      </if>
      <if test="driverCode != null" >
        #{driverCode,jdbcType=VARCHAR},
      </if>
      <if test="openId != null" >
        #{openId,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="password != null" >
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="gender != null" >
        #{gender,jdbcType=VARCHAR},
      </if>
      <if test="mobilePhone != null" >
        #{mobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="mail != null" >
        #{mail,jdbcType=VARCHAR},
      </if>
      <if test="birthDate != null" >
        #{birthDate,jdbcType=VARCHAR},
      </if>
      <if test="regTime != null" >
        #{regTime,jdbcType=VARCHAR},
      </if>
      <if test="zip != null" >
        #{zip,jdbcType=VARCHAR},
      </if>
      <if test="address != null" >
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="province != null" >
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null" >
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="membershipType != null" >
        #{membershipType,jdbcType=DECIMAL},
      </if>
      <if test="userLevel != null" >
        #{userLevel,jdbcType=DECIMAL},
      </if>
      <if test="orgId != null" >
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="agencyId != null" >
        #{agencyId,jdbcType=VARCHAR},
      </if>
      <if test="authKind != null" >
        #{authKind,jdbcType=VARCHAR},
      </if>
      <if test="certificateValidity != null" >
        #{certificateValidity,jdbcType=VARCHAR},
      </if>
      <if test="certificateAddress != null" >
        #{certificateAddress,jdbcType=VARCHAR},
      </if>
      <if test="drivingLicense != null" >
        #{drivingLicense,jdbcType=VARCHAR},
      </if>
      <if test="obtainDriverTimer != null" >
        #{obtainDriverTimer,jdbcType=VARCHAR},
      </if>
      <if test="licenseExpirationTime != null" >
        #{licenseExpirationTime,jdbcType=VARCHAR},
      </if>
      <if test="emergencyContact != null" >
        #{emergencyContact,jdbcType=VARCHAR},
      </if>
      <if test="emergencyMobil != null" >
        #{emergencyMobil,jdbcType=VARCHAR},
      </if>
      <if test="guaranteeName != null" >
        #{guaranteeName,jdbcType=VARCHAR},
      </if>
      <if test="guaranteeIcCardKind != null" >
        #{guaranteeIcCardKind,jdbcType=DECIMAL},
      </if>
      <if test="guaranteeIcCard != null" >
        #{guaranteeIcCard,jdbcType=VARCHAR},
      </if>
      <if test="guaranteeMobil != null" >
        #{guaranteeMobil,jdbcType=VARCHAR},
      </if>
      <if test="reviewReason != null" >
        #{reviewReason,jdbcType=VARCHAR},
      </if>
      <if test="reviewStatus != null" >
        #{reviewStatus,jdbcType=DECIMAL},
      </if>
      <if test="cardNo != null" >
        #{cardNo,jdbcType=VARCHAR},
      </if>
      <if test="creditNo != null" >
        #{creditNo,jdbcType=VARCHAR},
      </if>
      <if test="dataOrigin != null" >
        #{dataOrigin,jdbcType=DECIMAL},
      </if>
      <if test="id != null" >
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="status != null" >
        #{status,jdbcType=DECIMAL},
      </if>
      <if test="blacklistReason != null" >
        #{blacklistReason,jdbcType=VARCHAR},
      </if>
      <if test="reviewUser != null" >
        #{reviewUser,jdbcType=VARCHAR},
      </if>
      <if test="reviewItems != null" >
        #{reviewItems,jdbcType=VARCHAR},
      </if>
      <if test="illegalMethod != null" >
        #{illegalMethod,jdbcType=DECIMAL},
      </if>
      <if test="deposit != null" >
        #{deposit,jdbcType=DECIMAL},
      </if>
      <if test="reserveAmount != null" >
        #{reserveAmount,jdbcType=DECIMAL},
      </if>
      <if test="rentMins != null" >
        #{rentMins,jdbcType=DECIMAL},
      </if>
      <if test="exemptDeposit != null" >
        #{exemptDeposit,jdbcType=DECIMAL},
      </if>
      <if test="empno != null" >
        #{empno,jdbcType=VARCHAR},
      </if>
      <if test="infoOrigin != null" >
        #{infoOrigin,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=VARCHAR},
      </if>
      <if test="createdUser != null" >
        #{createdUser,jdbcType=VARCHAR},
      </if>
      <if test="updatedTime != null" >
        #{updatedTime,jdbcType=VARCHAR},
      </if>
      <if test="updatedUser != null" >
        #{updatedUser,jdbcType=VARCHAR},
      </if>
      <if test="personnelState != null" >
        #{personnelState,jdbcType=DECIMAL},
      </if>
      <if test="shareUid != null" >
        #{shareUid,jdbcType=VARCHAR},
      </if>
      <if test="ezbike != null" >
        #{ezbike,jdbcType=DECIMAL},
      </if>
      <if test="applyStatus != null" >
        #{applyStatus,jdbcType=DECIMAL},
      </if>
      <if test="serviceVer != null" >
        #{serviceVer,jdbcType=VARCHAR},
      </if>
      <if test="serviceVerTime != null" >
        #{serviceVerTime,jdbcType=VARCHAR},
      </if>
      <if test="appKey != null" >
        #{appKey,jdbcType=VARCHAR},
      </if>
      <if test="typeFlag != null" >
        #{typeFlag,jdbcType=DECIMAL},
      </if>
      <if test="invoicedAmount != null" >
        #{invoicedAmount,jdbcType=DECIMAL},
      </if>
      <if test="channelId != null" >
        #{channelId,jdbcType=VARCHAR},
      </if>
      <if test="regionid != null" >
        #{regionid,jdbcType=BIGINT},
      </if>
      <if test="depositVehicle != null" >
        #{depositVehicle,jdbcType=DECIMAL},
      </if>
      <if test="provinceOfOrigin != null" >
        #{provinceOfOrigin,jdbcType=VARCHAR},
      </if>
      <if test="cityOfOrigin != null" >
        #{cityOfOrigin,jdbcType=VARCHAR},
      </if>
      <if test="areaOfOrigin != null" >
        #{areaOfOrigin,jdbcType=VARCHAR},
      </if>
      <if test="idcardPicUrl != null" >
        #{idcardPicUrl,jdbcType=VARCHAR},
      </if>
      <if test="holdIdcardPicUrl != null" >
        #{holdIdcardPicUrl,jdbcType=VARCHAR},
      </if>
      <if test="point != null" >
        #{point,jdbcType=INTEGER},
      </if>
      <if test="reviewTime != null" >
        #{reviewTime,jdbcType=VARCHAR},
      </if>
      <if test="appReviewTime != null" >
        #{appReviewTime,jdbcType=VARCHAR},
      </if>
      <if test="reviewRemark != null" >
        #{reviewRemark,jdbcType=VARCHAR},
      </if>
      <if test="reviewItemIds != null" >
        #{reviewItemIds,jdbcType=VARCHAR},
      </if>
      <if test="reviewItemName != null" >
        #{reviewItemName,jdbcType=VARCHAR},
      </if>
      <if test="reviewMode != null" >
        #{reviewMode,jdbcType=INTEGER},
      </if>
      <if test="faceRecognitionImgUrl != null" >
        #{faceRecognitionImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="drivingLicenseType != null">
        #{drivingLicenseType,jdbcType=INTEGER},
      </if>
      <if test="userImgUrl != null" >
        #{userImgUrl,jdbcType=LONGVARCHAR},
      </if>
      <if test="drivingLicenseImgUrl != null" >
        #{drivingLicenseImgUrl,jdbcType=LONGVARCHAR},
      </if>
      <if test="identityCardImgUrl != null" >
        #{identityCardImgUrl,jdbcType=LONGVARCHAR},
      </if>
      <if test="mark != null" >
        #{mark,jdbcType=LONGVARCHAR},
      </if>
      <if test="authenticationStatus != null">
        #{authenticationStatus,jdbcType=DECIMAL},
      </if>
      <if test="unRegisterTime != null">
        #{unRegisterTime,jdbcType=VARCHAR},
      </if>
      <if test="identityId != null">
        #{identityId,jdbcType=BIGINT},
      </if>
      <if test="secondAppKey != null">
        #{secondAppKey,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=BIGINT},
      </if>
      <if test="passportNo != null">
        #{passportNo,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="isFictional != null">
        #{isFictional,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 04 15:27:31 CST 2017.
    -->
    update ${siacSchema}.membership_info
    <set >
      <if test="driverCode != null" >
        DRIVER_CODE = #{driverCode,jdbcType=VARCHAR},
      </if>
      <if test="openId != null" >
        OPEN_ID = #{openId,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="password != null" >
        PASSWORD = #{password,jdbcType=VARCHAR},
      </if>
      <if test="gender != null" >
        GENDER = #{gender,jdbcType=VARCHAR},
      </if>
      <if test="mobilePhone != null" >
        MOBILE_PHONE = #{mobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="mail != null" >
        MAIL = #{mail,jdbcType=VARCHAR},
      </if>
      <if test="birthDate != null" >
        BIRTH_DATE = #{birthDate,jdbcType=VARCHAR},
      </if>
      <if test="regTime != null" >
        REG_TIME = #{regTime,jdbcType=VARCHAR},
      </if>
      <if test="zip != null" >
        ZIP = #{zip,jdbcType=VARCHAR},
      </if>
      <if test="address != null" >
        ADDRESS = #{address,jdbcType=VARCHAR},
      </if>
      <if test="province != null" >
        PROVINCE = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        CITY = #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null" >
        AREA = #{area,jdbcType=VARCHAR},
      </if>
      <if test="membershipType != null" >
        MEMBERSHIP_TYPE = #{membershipType,jdbcType=DECIMAL},
      </if>
      <if test="userLevel != null" >
        USER_LEVEL = #{userLevel,jdbcType=DECIMAL},
      </if>
      <if test="orgId != null" >
        ORG_ID = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="agencyId != null" >
        AGENCY_ID = #{agencyId,jdbcType=VARCHAR},
      </if>
      <if test="authKind != null" >
        AUTH_KIND = #{authKind,jdbcType=VARCHAR},
      </if>
      <if test="certificateValidity != null" >
        CERTIFICATE_VALIDITY = #{certificateValidity,jdbcType=VARCHAR},
      </if>
      <if test="certificateAddress != null" >
        CERTIFICATE_ADDRESS = #{certificateAddress,jdbcType=VARCHAR},
      </if>
      <if test="drivingLicense != null" >
        DRIVING_LICENSE = #{drivingLicense,jdbcType=VARCHAR},
      </if>
      <if test="obtainDriverTimer != null" >
        OBTAIN_DRIVER_TIMER = #{obtainDriverTimer,jdbcType=VARCHAR},
      </if>
      <if test="licenseExpirationTime != null" >
        LICENSE_EXPIRATION_TIME = #{licenseExpirationTime,jdbcType=VARCHAR},
      </if>
      <if test="emergencyContact != null" >
        EMERGENCY_CONTACT = #{emergencyContact,jdbcType=VARCHAR},
      </if>
      <if test="emergencyMobil != null" >
        EMERGENCY_MOBIL = #{emergencyMobil,jdbcType=VARCHAR},
      </if>
      <if test="guaranteeName != null" >
        GUARANTEE_NAME = #{guaranteeName,jdbcType=VARCHAR},
      </if>
      <if test="guaranteeIcCardKind != null" >
        GUARANTEE_IC_CARD_KIND = #{guaranteeIcCardKind,jdbcType=DECIMAL},
      </if>
      <if test="guaranteeIcCard != null" >
        GUARANTEE_IC_CARD = #{guaranteeIcCard,jdbcType=VARCHAR},
      </if>
      <if test="guaranteeMobil != null" >
        GUARANTEE_MOBIL = #{guaranteeMobil,jdbcType=VARCHAR},
      </if>
      <if test="reviewReason != null" >
        REVIEW_REASON = #{reviewReason,jdbcType=VARCHAR},
      </if>
      <if test="reviewStatus != null" >
        REVIEW_STATUS = #{reviewStatus,jdbcType=DECIMAL},
      </if>
      <if test="cardNo != null" >
        CARD_NO = #{cardNo,jdbcType=VARCHAR},
      </if>
      <if test="creditNo != null" >
        CREDIT_NO = #{creditNo,jdbcType=VARCHAR},
      </if>
      <if test="dataOrigin != null" >
        DATA_ORIGIN = #{dataOrigin,jdbcType=DECIMAL},
      </if>
      <if test="id != null" >
        ID = #{id,jdbcType=DECIMAL},
      </if>
      <if test="status != null" >
        STATUS = #{status,jdbcType=DECIMAL},
      </if>
      <if test="blacklistReason != null" >
        BLACKLIST_REASON = #{blacklistReason,jdbcType=VARCHAR},
      </if>
      <if test="reviewUser != null" >
        REVIEW_USER = #{reviewUser,jdbcType=VARCHAR},
      </if>
      <if test="reviewItems != null" >
        REVIEW_ITEMS = #{reviewItems,jdbcType=VARCHAR},
      </if>
      <if test="illegalMethod != null" >
        ILLEGAL_METHOD = #{illegalMethod,jdbcType=DECIMAL},
      </if>
      <if test="deposit != null" >
        DEPOSIT = #{deposit,jdbcType=DECIMAL},
      </if>
      <if test="reserveAmount != null" >
        RESERVE_AMOUNT = #{reserveAmount,jdbcType=DECIMAL},
      </if>
      <if test="rentMins != null" >
        RENT_MINS = #{rentMins,jdbcType=DECIMAL},
      </if>
      <if test="exemptDeposit != null" >
        EXEMPT_DEPOSIT = #{exemptDeposit,jdbcType=DECIMAL},
      </if>
      <if test="empno != null" >
        EMPNO = #{empno,jdbcType=VARCHAR},
      </if>
      <if test="infoOrigin != null" >
        INFO_ORIGIN = #{infoOrigin,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        CREATED_TIME = #{createdTime,jdbcType=VARCHAR},
      </if>
      <if test="createdUser != null" >
        CREATED_USER = #{createdUser,jdbcType=VARCHAR},
      </if>
      <if test="updatedTime != null" >
        UPDATED_TIME = #{updatedTime,jdbcType=VARCHAR},
      </if>
      <if test="updatedUser != null" >
        UPDATED_USER = #{updatedUser,jdbcType=VARCHAR},
      </if>
      <if test="personnelState != null" >
        PERSONNEL_STATE = #{personnelState,jdbcType=DECIMAL},
      </if>
      <if test="shareUid != null" >
        SHARE_UID = #{shareUid,jdbcType=VARCHAR},
      </if>
      <if test="ezbike != null" >
        EZBIKE = #{ezbike,jdbcType=DECIMAL},
      </if>
      <if test="applyStatus != null" >
        APPLY_STATUS = #{applyStatus,jdbcType=DECIMAL},
      </if>
      <if test="serviceVer != null" >
        SERVICE_VER = #{serviceVer,jdbcType=VARCHAR},
      </if>
      <if test="serviceVerTime != null" >
        SERVICE_VER_TIME = #{serviceVerTime,jdbcType=VARCHAR},
      </if>
      <if test="appKey != null" >
        APP_KEY = #{appKey,jdbcType=VARCHAR},
      </if>
      <if test="typeFlag != null" >
        TYPE_FLAG = #{typeFlag,jdbcType=DECIMAL},
      </if>
      <if test="invoicedAmount != null" >
        INVOICED_AMOUNT = #{invoicedAmount,jdbcType=DECIMAL},
      </if>
      <if test="channelId != null" >
        CHANNEL_ID = #{channelId,jdbcType=VARCHAR},
      </if>
      <if test="regionid != null" >
        REGIONID = #{regionid,jdbcType=BIGINT},
      </if>
      <if test="depositVehicle != null" >
        deposit_vehicle = #{depositVehicle,jdbcType=DECIMAL},
      </if>
      <if test="provinceOfOrigin != null" >
        province_of_origin = #{provinceOfOrigin,jdbcType=VARCHAR},
      </if>
      <if test="cityOfOrigin != null" >
        city_of_origin = #{cityOfOrigin,jdbcType=VARCHAR},
      </if>
      <if test="areaOfOrigin != null" >
        area_of_origin = #{areaOfOrigin,jdbcType=VARCHAR},
      </if>
      <if test="idcardPicUrl != null" >
        idcard_pic_url = #{idcardPicUrl,jdbcType=VARCHAR},
      </if>
      <if test="holdIdcardPicUrl != null" >
        hold_idcard_pic_url = #{holdIdcardPicUrl,jdbcType=VARCHAR},
      </if>
      <if test="point != null" >
        point = #{point,jdbcType=INTEGER},
      </if>
      <if test="reviewTime != null" >
        REVIEW_TIME = #{reviewTime,jdbcType=VARCHAR},
      </if>
      <if test="appReviewTime != null" >
        APP_REVIEW_TIME = #{appReviewTime,jdbcType=VARCHAR},
      </if>
      <if test="reviewRemark != null" >
        REVIEW_REMARK = #{reviewRemark,jdbcType=VARCHAR},
      </if>
      <if test="reviewItemIds != null" >
        REVIEW_ITEM_IDS = #{reviewItemIds,jdbcType=VARCHAR},
      </if>
      <if test="reviewItemName != null" >
        REVIEW_ITEM_NAME = #{reviewItemName,jdbcType=VARCHAR},
      </if>
      <if test="reviewMode != null" >
        REVIEW_MODE = #{reviewMode,jdbcType=INTEGER},
      </if>
      <if test="faceRecognitionImgUrl != null" >
        FACE_RECOGNITION_IMG_URL = #{faceRecognitionImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="drivingLicenseType != null">
        driving_license_type = #{drivingLicenseType,jdbcType=INTEGER},
      </if>
      <if test="national != null">
        national = #{national,jdbcType=VARCHAR},
      </if>
      <if test="userImgUrl != null" >
        USER_IMG_URL = #{userImgUrl,jdbcType=LONGVARCHAR},
      </if>
      <if test="drivingLicenseImgUrl != null" >
        DRIVING_LICENSE_IMG_URL = #{drivingLicenseImgUrl,jdbcType=LONGVARCHAR},
      </if>
      <if test="identityCardImgUrl != null" >
        IDENTITY_CARD_IMG_URL = #{identityCardImgUrl,jdbcType=LONGVARCHAR},
      </if>
      <if test="mark != null" >
        MARK = #{mark,jdbcType=LONGVARCHAR},
      </if>
      <if test="authenticationStatus != null">
        authentication_status = #{authenticationStatus,jdbcType=INTEGER},
      </if>
      <if test="driverLicenseInputType != null">
        driver_license_input_type = #{driverLicenseInputType,jdbcType=INTEGER},
      </if>
      <if test="passportNo != null and passportNo !=''">
        passport_no = #{passportNo,jdbcType=VARCHAR},
      </if>
      <if test="fileNo != null and fileNo !=''">
        file_no = #{fileNo,jdbcType=VARCHAR},
      </if>
      <if test="fileNoImgUrl != null and fileNoImgUrl !=''">
        file_no_img_url = #{fileNoImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="idType != null">
        id_type = #{idType,jdbcType=INTEGER},
      </if>
      <if test="idCardNumber != null and idCardNumber !=''">
        id_card_number = #{idCardNumber},
      </if>
      <if test="licenseElementsAuthStatus != null">
        license_elements_auth_status = #{licenseElementsAuthStatus,jdbcType=INTEGER},
      </if>
      <if test="elementsReviewItems != null">
        elements_review_items = #{elementsReviewItems,jdbcType=VARCHAR},
      </if>
      <if test="licenseAuthStatus != null">
        license_auth_status = #{licenseAuthStatus,jdbcType=INTEGER},
      </if>
      <if test="licenseStatusMsg != null">
        license_status_msg = #{licenseStatusMsg,jdbcType=VARCHAR},
      </if>


      <if test="identityId != null">
        identity_id = #{identityId,jdbcType=BIGINT},
      </if>
      <if test="identityFirstAuthTime != null">
        identity_first_auth_time = #{identityFirstAuthTime,jdbcType=TIMESTAMP},
      </if>

      <if test="licenseReviewStatus != null">
        license_review_status = #{licenseReviewStatus,jdbcType=INTEGER},
      </if>
      <if test="licenseFirstAuthTime != null">
        license_first_auth_time = #{licenseFirstAuthTime,jdbcType=TIMESTAMP},
      </if>
      <if test="licenseSubmitAppkey != null">
        license_submit_appkey = #{licenseSubmitAppkey,jdbcType=VARCHAR},
      </if>
      <if test="licenseImgType != null">
        license_img_type = #{licenseImgType,jdbcType=INTEGER},
      </if>
      <if test="secondAppKey != null and secondAppKey !=''">
        second_app_key = #{secondAppKey,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=BIGINT},
      </if>
    </set>
    where pk_id = #{pkId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 04 15:27:31 CST 2017.
    -->
    update ${siacSchema}.membership_info
    set 
      DRIVER_CODE = #{driverCode,jdbcType=VARCHAR},
      OPEN_ID = #{openId,jdbcType=VARCHAR},
      NAME = #{name,jdbcType=VARCHAR},
      PASSWORD = #{password,jdbcType=VARCHAR},
      GENDER = #{gender,jdbcType=VARCHAR},
      MOBILE_PHONE = #{mobilePhone,jdbcType=VARCHAR},
      MAIL = #{mail,jdbcType=VARCHAR},
      BIRTH_DATE = #{birthDate,jdbcType=VARCHAR},
      REG_TIME = #{regTime,jdbcType=VARCHAR},
      ZIP = #{zip,jdbcType=VARCHAR},
      ADDRESS = #{address,jdbcType=VARCHAR},
      PROVINCE = #{province,jdbcType=VARCHAR},
      CITY = #{city,jdbcType=VARCHAR},
      AREA = #{area,jdbcType=VARCHAR},
      MEMBERSHIP_TYPE = #{membershipType,jdbcType=DECIMAL},
      USER_LEVEL = #{userLevel,jdbcType=DECIMAL},
      ORG_ID = #{orgId,jdbcType=VARCHAR},
      AGENCY_ID = #{agencyId,jdbcType=VARCHAR},
      AUTH_KIND = #{authKind,jdbcType=VARCHAR},
      CERTIFICATE_VALIDITY = #{certificateValidity,jdbcType=VARCHAR},
      CERTIFICATE_ADDRESS = #{certificateAddress,jdbcType=VARCHAR},
      DRIVING_LICENSE = #{drivingLicense,jdbcType=VARCHAR},
      OBTAIN_DRIVER_TIMER = #{obtainDriverTimer,jdbcType=VARCHAR},
      LICENSE_EXPIRATION_TIME = #{licenseExpirationTime,jdbcType=VARCHAR},
      EMERGENCY_CONTACT = #{emergencyContact,jdbcType=VARCHAR},
      EMERGENCY_MOBIL = #{emergencyMobil,jdbcType=VARCHAR},
      GUARANTEE_NAME = #{guaranteeName,jdbcType=VARCHAR},
      GUARANTEE_IC_CARD_KIND = #{guaranteeIcCardKind,jdbcType=DECIMAL},
      GUARANTEE_IC_CARD = #{guaranteeIcCard,jdbcType=VARCHAR},
      GUARANTEE_MOBIL = #{guaranteeMobil,jdbcType=VARCHAR},
      REVIEW_REASON = #{reviewReason,jdbcType=VARCHAR},
      REVIEW_STATUS = #{reviewStatus,jdbcType=DECIMAL},
      CARD_NO = #{cardNo,jdbcType=VARCHAR},
      CREDIT_NO = #{creditNo,jdbcType=VARCHAR},
      DATA_ORIGIN = #{dataOrigin,jdbcType=DECIMAL},
      ID = #{id,jdbcType=DECIMAL},
      STATUS = #{status,jdbcType=DECIMAL},
      BLACKLIST_REASON = #{blacklistReason,jdbcType=VARCHAR},
      REVIEW_USER = #{reviewUser,jdbcType=VARCHAR},
      REVIEW_ITEMS = #{reviewItems,jdbcType=VARCHAR},
      ILLEGAL_METHOD = #{illegalMethod,jdbcType=DECIMAL},
      DEPOSIT = #{deposit,jdbcType=DECIMAL},
      RESERVE_AMOUNT = #{reserveAmount,jdbcType=DECIMAL},
      RENT_MINS = #{rentMins,jdbcType=DECIMAL},
      EXEMPT_DEPOSIT = #{exemptDeposit,jdbcType=DECIMAL},
      EMPNO = #{empno,jdbcType=VARCHAR},
      INFO_ORIGIN = #{infoOrigin,jdbcType=VARCHAR},
      CREATED_TIME = #{createdTime,jdbcType=VARCHAR},
      CREATED_USER = #{createdUser,jdbcType=VARCHAR},
      UPDATED_TIME = #{updatedTime,jdbcType=VARCHAR},
      UPDATED_USER = #{updatedUser,jdbcType=VARCHAR},
      PERSONNEL_STATE = #{personnelState,jdbcType=DECIMAL},
      SHARE_UID = #{shareUid,jdbcType=VARCHAR},
      EZBIKE = #{ezbike,jdbcType=DECIMAL},
      APPLY_STATUS = #{applyStatus,jdbcType=DECIMAL},
      SERVICE_VER = #{serviceVer,jdbcType=VARCHAR},
      SERVICE_VER_TIME = #{serviceVerTime,jdbcType=VARCHAR},
      APP_KEY = #{appKey,jdbcType=VARCHAR},
      TYPE_FLAG = #{typeFlag,jdbcType=DECIMAL},
      INVOICED_AMOUNT = #{invoicedAmount,jdbcType=DECIMAL},
      CHANNEL_ID = #{channelId,jdbcType=VARCHAR},
      REGIONID = #{regionid,jdbcType=BIGINT},
      deposit_vehicle = #{depositVehicle,jdbcType=DECIMAL},
      province_of_origin = #{provinceOfOrigin,jdbcType=VARCHAR},
      city_of_origin = #{cityOfOrigin,jdbcType=VARCHAR},
      area_of_origin = #{areaOfOrigin,jdbcType=VARCHAR},
      idcard_pic_url = #{idcardPicUrl,jdbcType=VARCHAR},
      hold_idcard_pic_url = #{holdIdcardPicUrl,jdbcType=VARCHAR},
      point = #{point,jdbcType=INTEGER},
      REVIEW_TIME = #{reviewTime,jdbcType=VARCHAR},
      APP_REVIEW_TIME = #{appReviewTime,jdbcType=VARCHAR},
      REVIEW_REMARK = #{reviewRemark,jdbcType=VARCHAR},
      REVIEW_ITEM_IDS = #{reviewItemIds,jdbcType=VARCHAR},
      REVIEW_ITEM_NAME = #{reviewItemName,jdbcType=VARCHAR},
      REVIEW_MODE = #{reviewMode,jdbcType=INTEGER},
      FACE_RECOGNITION_IMG_URL = #{faceRecognitionImgUrl,jdbcType=VARCHAR},
      USER_IMG_URL = #{userImgUrl,jdbcType=LONGVARCHAR},
      DRIVING_LICENSE_IMG_URL = #{drivingLicenseImgUrl,jdbcType=LONGVARCHAR},
      IDENTITY_CARD_IMG_URL = #{identityCardImgUrl,jdbcType=LONGVARCHAR},
      MARK = #{mark,jdbcType=LONGVARCHAR}
    where pk_id = #{pkId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.credit.model.MembershipInfo" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 04 15:27:31 CST 2017.
    -->
    update ${siacSchema}.membership_info
    set 
      DRIVER_CODE = #{driverCode,jdbcType=VARCHAR},
      OPEN_ID = #{openId,jdbcType=VARCHAR},
      NAME = #{name,jdbcType=VARCHAR},
      PASSWORD = #{password,jdbcType=VARCHAR},
      GENDER = #{gender,jdbcType=VARCHAR},
      MOBILE_PHONE = #{mobilePhone,jdbcType=VARCHAR},
      MAIL = #{mail,jdbcType=VARCHAR},
      BIRTH_DATE = #{birthDate,jdbcType=VARCHAR},
      REG_TIME = #{regTime,jdbcType=VARCHAR},
      ZIP = #{zip,jdbcType=VARCHAR},
      ADDRESS = #{address,jdbcType=VARCHAR},
      PROVINCE = #{province,jdbcType=VARCHAR},
      CITY = #{city,jdbcType=VARCHAR},
      AREA = #{area,jdbcType=VARCHAR},
      MEMBERSHIP_TYPE = #{membershipType,jdbcType=DECIMAL},
      USER_LEVEL = #{userLevel,jdbcType=DECIMAL},
      ORG_ID = #{orgId,jdbcType=VARCHAR},
      AGENCY_ID = #{agencyId,jdbcType=VARCHAR},
      AUTH_KIND = #{authKind,jdbcType=VARCHAR},
      CERTIFICATE_VALIDITY = #{certificateValidity,jdbcType=VARCHAR},
      CERTIFICATE_ADDRESS = #{certificateAddress,jdbcType=VARCHAR},
      DRIVING_LICENSE = #{drivingLicense,jdbcType=VARCHAR},
      OBTAIN_DRIVER_TIMER = #{obtainDriverTimer,jdbcType=VARCHAR},
      LICENSE_EXPIRATION_TIME = #{licenseExpirationTime,jdbcType=VARCHAR},
      EMERGENCY_CONTACT = #{emergencyContact,jdbcType=VARCHAR},
      EMERGENCY_MOBIL = #{emergencyMobil,jdbcType=VARCHAR},
      GUARANTEE_NAME = #{guaranteeName,jdbcType=VARCHAR},
      GUARANTEE_IC_CARD_KIND = #{guaranteeIcCardKind,jdbcType=DECIMAL},
      GUARANTEE_IC_CARD = #{guaranteeIcCard,jdbcType=VARCHAR},
      GUARANTEE_MOBIL = #{guaranteeMobil,jdbcType=VARCHAR},
      REVIEW_REASON = #{reviewReason,jdbcType=VARCHAR},
      REVIEW_STATUS = #{reviewStatus,jdbcType=DECIMAL},
      CARD_NO = #{cardNo,jdbcType=VARCHAR},
      CREDIT_NO = #{creditNo,jdbcType=VARCHAR},
      DATA_ORIGIN = #{dataOrigin,jdbcType=DECIMAL},
      ID = #{id,jdbcType=DECIMAL},
      STATUS = #{status,jdbcType=DECIMAL},
      BLACKLIST_REASON = #{blacklistReason,jdbcType=VARCHAR},
      REVIEW_USER = #{reviewUser,jdbcType=VARCHAR},
      REVIEW_ITEMS = #{reviewItems,jdbcType=VARCHAR},
      ILLEGAL_METHOD = #{illegalMethod,jdbcType=DECIMAL},
      DEPOSIT = #{deposit,jdbcType=DECIMAL},
      RESERVE_AMOUNT = #{reserveAmount,jdbcType=DECIMAL},
      RENT_MINS = #{rentMins,jdbcType=DECIMAL},
      EXEMPT_DEPOSIT = #{exemptDeposit,jdbcType=DECIMAL},
      EMPNO = #{empno,jdbcType=VARCHAR},
      INFO_ORIGIN = #{infoOrigin,jdbcType=VARCHAR},
      CREATED_TIME = #{createdTime,jdbcType=VARCHAR},
      CREATED_USER = #{createdUser,jdbcType=VARCHAR},
      UPDATED_TIME = #{updatedTime,jdbcType=VARCHAR},
      UPDATED_USER = #{updatedUser,jdbcType=VARCHAR},
      PERSONNEL_STATE = #{personnelState,jdbcType=DECIMAL},
      SHARE_UID = #{shareUid,jdbcType=VARCHAR},
      EZBIKE = #{ezbike,jdbcType=DECIMAL},
      APPLY_STATUS = #{applyStatus,jdbcType=DECIMAL},
      SERVICE_VER = #{serviceVer,jdbcType=VARCHAR},
      SERVICE_VER_TIME = #{serviceVerTime,jdbcType=VARCHAR},
      APP_KEY = #{appKey,jdbcType=VARCHAR},
      TYPE_FLAG = #{typeFlag,jdbcType=DECIMAL},
      INVOICED_AMOUNT = #{invoicedAmount,jdbcType=DECIMAL},
      CHANNEL_ID = #{channelId,jdbcType=VARCHAR},
      REGIONID = #{regionid,jdbcType=BIGINT},
      deposit_vehicle = #{depositVehicle,jdbcType=DECIMAL},
      province_of_origin = #{provinceOfOrigin,jdbcType=VARCHAR},
      city_of_origin = #{cityOfOrigin,jdbcType=VARCHAR},
      area_of_origin = #{areaOfOrigin,jdbcType=VARCHAR},
      idcard_pic_url = #{idcardPicUrl,jdbcType=VARCHAR},
      hold_idcard_pic_url = #{holdIdcardPicUrl,jdbcType=VARCHAR},
      point = #{point,jdbcType=INTEGER},
      REVIEW_TIME = #{reviewTime,jdbcType=VARCHAR},
      APP_REVIEW_TIME = #{appReviewTime,jdbcType=VARCHAR},
      REVIEW_REMARK = #{reviewRemark,jdbcType=VARCHAR},
      REVIEW_ITEM_IDS = #{reviewItemIds,jdbcType=VARCHAR},
      REVIEW_ITEM_NAME = #{reviewItemName,jdbcType=VARCHAR},
      REVIEW_MODE = #{reviewMode,jdbcType=INTEGER},
      FACE_RECOGNITION_IMG_URL = #{faceRecognitionImgUrl,jdbcType=VARCHAR}
    where pk_id = #{pkId,jdbcType=BIGINT}
  </update>

  <update id="updateMemberShipStatusByAuthId">
     UPDATE ${siacSchema}.membership_info
     SET STATUS=1
     WHERE AUTH_ID= #{authId,jdbcType=VARCHAR}
  </update>

  <!-- 检查用户邮寄地址是否上海 -->
  <select id="checkMemberShipAddress" parameterType="string" resultType="integer">
    select count(*) from ${siacSchema}.membership_info where AUTH_ID = #{authId} and (CITY = '上海市' or ADDRESS like '上海市%' ) AND MEMBERSHIP_TYPE=0
  </select>

  <!-- 检查用户所属是否成都分公司 -->
  <select id="checkMemberShipAddressCD" parameterType="string" resultType="integer">
    select count(*) from
    ${siacSchema}.membership_info t1
    LEFT JOIN ${siacSchema}.CITY t4 ON t1.CITY_OF_ORIGIN = t4.CITY
    LEFT JOIN ${isvSchema}.ORG_INFO t5 ON t4.ORG_ID = t5.ORG_ID
    WHERE t1.AUTH_ID = #{authId}
    and t1.MEMBERSHIP_TYPE = 0
    and t5.ORG_ID = '003V'
  </select>

  <select id="queryAppKey" parameterType="string" resultType="string">
    SELECT APP_KEY
    FROM
    ${siacSchema}.MEMBERSHIP_INFO
    WHERE
    AUTH_ID = #{authId} and MEMBERSHIP_TYPE = 0
  </select>

  <!-- 判断该会员是否注册成功 -->
  <select id="checkReward" parameterType="string" resultType="integer">
    select count(1) from ${siacSchema}.USER_SHARE_REWARD_INFO where AUTH_ID = #{authId} and REWARD_TYPE = '成功注册'
  </select>

  <!-- 查询好友ID -->
  <select id="queryUid" resultType="map" parameterType="string">
    select ifnull(SHARE_UID, '') as "shareUid", ifnull(NAME,'') as "name" from ${siacSchema}.MEMBERSHIP_INFO where
    AUTH_ID = #{authId}  and MEMBERSHIP_TYPE = 0
  </select>

  <select id="queryShareUser" parameterType="string"
          resultType="java.util.HashMap">
    select b.AUTH_ID as "authId", b.uid as "uid", b.NAME as "name", b.MOBILE_PHONE  as "mobilePhone"
    from ${siacSchema}.SHARE_INFO A, ${siacSchema}.MEMBERSHIP_INFO B
    where a.SHARE_ID = #{shareUid} and A.OLD_AUTHID = b.AUTH_ID and  B.MEMBERSHIP_TYPE = 0
  </select>

  <insert id="rewardInsert" parameterType="map">
    INSERT INTO ${siacSchema}.USER_SHARE_REWARD_INFO (
    AUTH_ID,
    REWARD_TYPE,
    REWARD_CONTENT,
    EAMOUNT,
    ORGIN_USERNAME,
    ORGIN_MOBILE_PHONE,
    ORGIN_AUTH_ID,
    CREATED_USER,
    UPDATED_USER,
    CREATED_TIME,
    UPDATED_TIME
    )
    VALUES (
    #{insertMap.authId},
    #{insertMap.rewardType},
    #{insertMap.rewardContent},
    #{insertMap.eamount},
    #{insertMap.orginUsername},
    #{insertMap.orginMobilephone},
    #{insertMap.orginAuthId},
    #{insertMap.createdUser},
    #{insertMap.updatedUser},
    #{insertMap.createdTime},
    #{insertMap.updatedTime})
  </insert>


  <select id="selectAuthInfoByAuthId" resultType="java.lang.Integer">
    SELECT
     COUNT(1)
     FROM  ${siacSchema}.membership_info
     WHERE MEMBERSHIP_TYPE = 0
     and (CARD_NO is NULL or CARD_NO = '' )
     and AUTH_ID = #{authId}
  </select>
  <select id="countByMobilePhone" resultType="int">
    SELECT
    COUNT(1)
    FROM  ${siacSchema}.membership_info
    WHERE MEMBERSHIP_TYPE = 0
    and MOBILE_PHONE = #{mobilePhone}
    <if test="${orgIdCheck} == 1 ">
      and ORG_ID = #{orgId}
    </if>
  </select>

  <select id="queryCityEqualsArea" resultType="java.lang.String">
    SELECT city FROM  ${siacSchema}.city where CITY like #{area}
  </select>

  <select id="isExistAppKey" resultType="int">
    SELECT
     COUNT(1)
    FROM  ${siacSchema}.APP_KEY_MANAGER
    WHERE `status` = 0
    and APP_KEY = #{appKey}
  </select>


  <select id="selectOneByAppKey" resultType="java.util.Map">
    SELECT
     APP_KEY as appKey,
     APP_SECRET as appSecret,
     POST_URL as postUrl
    FROM  ${siacSchema}.APP_KEY_MANAGER
    WHERE `status` = 0
    and APP_KEY = #{appKey}
  </select>

  <update id="saveCustomerId">
     UPDATE ${siacSchema}.membership_info
       set customer_id = #{customerId}
     where auth_id = #{authId}
       and MEMBERSHIP_TYPE = 0
  </update>

  <select id="countContract" resultType="int">
    SELECT COUNT(1)  FROM  ${siacSchema}.user_contract
    where auth_id = #{authId}
     and customer_id = #{customerId}
     and template_id = #{templateId}
  </select>

  <select id="countContractByTemplateId" resultType="int">
    SELECT COUNT(1)  FROM  ${siacSchema}.user_contract
    where auth_id = #{authId}
    and template_id = #{templateId}
  </select>


  <select id="getIdentityByPkId" resultType="com.extracme.evcard.membership.credit.model.MembershipInfo">
    SELECT IFNULL(NULLIF(md.identity_no,''),mi.passport_no) passportNo,mi.name,
    IFNULL(NULLIF(md.identity_type,''),mi.id_type) idType
    FROM ${siacSchema}.membership_info mi
    left join  ${siacSchema}.member_identity_document md on mi.identity_id = md.id
    where mi.pk_id=#{pkId} limit 1
  </select>
  <select id="getInfoByPassportNoAndPhone" resultType="com.extracme.evcard.membership.credit.model.MembershipInfo">
    SELECT IFNULL(NULLIF(md.identity_no,''),mi.passport_no) passportNo,mi.name,mi.pk_id pkId,mi.mid,mi.AUTH_ID authId,mi.MOBILE_PHONE mobilePhone,
    IFNULL(NULLIF(md.identity_type,''),mi.id_type) idType
    FROM ${siacSchema}.membership_info mi
    left join ${siacSchema}.member_identity_document md on mi.identity_id = md.id
    where md.status = 0 and mi.MEMBERSHIP_TYPE = 0 and mi.ACCOUNT_STATUS = 0
    and IFNULL(NULLIF(md.identity_no,''),mi.passport_no) = #{passportNo}
    <if test="mobilePhone != null">
          and mi.MOBILE_PHONE = #{mobilePhone}
    </if>

  </select>



  <select id="selectByAuthId" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ${siacSchema}.membership_info
    where auth_id = #{authId}
    and MEMBERSHIP_TYPE = #{membershipType}
  </select>

  <select id="selectByAuthId2" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ${siacSchema}.membership_info
    where auth_id = #{authId}
  </select>

  <select id="selectByUid" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ${siacSchema}.membership_info
    where uid = #{uid}
  </select>

  <update id="updateMemberUidByPkId">
    update ${siacSchema}.membership_info
    set UID=#{uid},
    updated_time=date_format(sysdate(), '%Y%m%d%H%i%s000')
    where pk_id=#{pkId}
  </update>

  <select id="selectByPkIds" resultMap="ResultMapWithBLOBs">
      select
      <include refid="Base_Column_List" />
      ,
      <include refid="Blob_Column_List" />
      from ${siacSchema}.membership_info
      where 1 =1
    <if test="list != null and list.size>0">
      AND pk_id in
      <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
          ${item}
      </foreach>
    </if>
      and MEMBERSHIP_TYPE = 0
  </select>


  <select id="selectByAuthIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${siacSchema}.membership_info
    where 1 =1
    AND auth_id in
    <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
    and MEMBERSHIP_TYPE = 0
  </select>

  <select id="selectByMobilePhones" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${siacSchema}.membership_info
    where 1 =1
    AND MOBILE_PHONE in
    <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
    and MEMBERSHIP_TYPE = 0
    and ACCOUNT_STATUS = 0
  </select>

  <select id="selectByMobilePhoneAndType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${siacSchema}.membership_info
    where
    MOBILE_PHONE = #{mobilePhone}
    and MEMBERSHIP_TYPE = #{membershipType}
    and ACCOUNT_STATUS = 0
  </select>


  <select id="selectFirstUserByMobilePhone" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${siacSchema}.membership_info
    where
    MOBILE_PHONE = #{mobilePhone}
    and MEMBERSHIP_TYPE = 0
    order by REG_TIME asc
    limit 1
  </select>


  <select id="selectInnerMemberByMobile" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    FROM ${siacSchema}.membership_info
    where MEMBERSHIP_TYPE = 1
    and MOBILE_PHONE = #{mobilePhone}
    and ORG_ID = #{orgId}
    and id_card_number = #{idCardNumber}
  </select>

  <select id="countInnerMemberByMobile" resultType="int">
    SELECT
    COUNT(1)
    FROM  ${siacSchema}.membership_info
    WHERE MEMBERSHIP_TYPE = 1
    and MOBILE_PHONE = #{mobilePhone}
  </select>

  <!-- 更新数据 -->
  <update id="updateInnerMemberCardNoByAuthId" parameterType="com.extracme.evcard.membership.credit.model.MembershipInfo" >
    update
      ${siacSchema}.membership_info
    set CARD_NO = #{cardNo,jdbcType=VARCHAR}
    where MEMBERSHIP_TYPE = 1
    and AUTH_ID = #{authId,jdbcType=VARCHAR}
  </update>

  <select id="getMemberInfoByPhone" resultType="com.extracme.evcard.membership.credit.dto.MembershipInfoDto">
    SELECT A.pk_id AS pkId, A.AUTH_ID AS authId, A.UID AS uid, A.NAME AS name, A.PASSWORD AS password, A.REVIEW_STATUS AS reviewStatus, A.authentication_status as authenticationStatus,A.MEMBERSHIP_TYPE AS membershipType,
    A.CARD_NO AS cardNo,B.ACTIVATE_STATUS AS activateStatus, B.STATUS AS status,A.AGENCY_ID AS agencyId, IFNULL(A.SERVICE_VER,'')  AS serviceVer, A.APP_KEY AS appKey,
    A.city_of_origin AS cityOfOrigin, A.ORG_ID AS orgId
    FROM ${siacSchema}.membership_info A
    left join ${siacSchema}.CARD_INFO B on A.CARD_NO = B.CARD_NO and A.AUTH_ID = B.AUTH_ID
    left join ${siacSchema}.APP_KEY_MANAGER C on A.APP_KEY = C.APP_KEY
    WHERE A.MOBILE_PHONE = #{mobilePhone} AND A.STATUS = 0 and A.MEMBERSHIP_TYPE = 0 and A.ACCOUNT_STATUS =0
    <if test="${orgIdCheck} == 1">
      and A.ORG_ID = #{orgId}
    </if>
  </select>

  <update id="updateChannelId">
    update ${siacSchema}.membership_info
    set channel_id=#{channelId},updated_time=date_format(sysdate(), '%Y%m%d%H%i%s000') where auth_id=#{authId} and MEMBERSHIP_TYPE=#{memberType}
  </update>

	<!-- 根据会员id查询会员信息 -->
  <select id="selectBaseByAuthId" resultType="com.extracme.evcard.membership.credit.model.MembershipBaseInfo">
    select pk_id AS pkId, UID as uid, city_of_origin AS cityOfOrigin,APP_KEY AS appKey,DATA_ORIGIN AS dataOrigin,REVIEW_STATUS AS reviewStatus,name as name,
    SERVICE_VER AS serverVer, AUTH_ID as authId, DRIVER_CODE as driverCode, MEMBERSHIP_TYPE AS membershipType, MOBILE_PHONE as mobilePhone,
    CARD_NO AS cardNo,CREATED_TIME AS createdTime,password as password,authentication_status AS authenticationStatus
    from ${siacSchema}.membership_info
    where auth_id = #{authId}
    and MEMBERSHIP_TYPE = #{membershipType}
  </select>
  
  <!-- 根据会员证件号/会员类型查询会员信息 -->
  <select id="selectBaseByDriverCode" resultType="com.extracme.evcard.membership.credit.model.MembershipBaseInfo">
    select pk_id AS pkId, UID as uid, city_of_origin AS cityOfOrigin,APP_KEY AS appKey,DATA_ORIGIN AS dataOrigin,REVIEW_STATUS AS reviewStatus,CARD_NO AS cardNo,name as name,
    SERVICE_VER AS serverVer, AUTH_ID as authId, DRIVER_CODE as driverCode, MEMBERSHIP_TYPE AS membershipType, MOBILE_PHONE as mobilePhone,authentication_status AS authenticationStatus
    from ${siacSchema}.membership_info
    where driver_code = #{driverCode}
    and MEMBERSHIP_TYPE = #{membershipType}
    and ACCOUNT_STATUS =0
    <if test="${orgIdCheck} == 1">
      and ORG_ID = #{orgId}
    </if>
  </select>
  
  <!-- 根据手机号/会员类型查询会员信息 -->
  <select id="selectBaseByPhone" resultType="com.extracme.evcard.membership.credit.model.MembershipBaseInfo">
    select pk_id AS pkId,UID as uid,city_of_origin AS cityOfOrigin,APP_KEY AS appKey,DATA_ORIGIN AS dataOrigin,REVIEW_STATUS AS reviewStatus,name as name,
    SERVICE_VER AS serverVer, AUTH_ID as authId, DRIVER_CODE as driverCode, MEMBERSHIP_TYPE AS membershipType, MOBILE_PHONE as mobilePhone,mid
    , REG_TIME as regTime
    from ${siacSchema}.membership_info
    where MOBILE_PHONE = #{mobilePhone}
    and MEMBERSHIP_TYPE = #{membershipType}
  </select>

    <!-- 根据手机号/会员类型查询会员信息 -->
    <select id="selectBaseByPhone2" resultType="com.extracme.evcard.membership.credit.model.MembershipBaseInfo">
    select pk_id AS pkId,UID as uid,city_of_origin AS cityOfOrigin,APP_KEY AS appKey,DATA_ORIGIN AS dataOrigin,REVIEW_STATUS AS reviewStatus,name as name,
    SERVICE_VER AS serverVer, AUTH_ID as authId, DRIVER_CODE as driverCode, MEMBERSHIP_TYPE AS membershipType, MOBILE_PHONE as mobilePhone,mid
    , REG_TIME as regTime
    from ${siacSchema}.membership_info
    where MOBILE_PHONE = #{mobilePhone}
    and MEMBERSHIP_TYPE = #{membershipType}
    and ACCOUNT_STATUS = 0
    and STATUS = 0
  </select>

  <select id="queryExemptDeposit" resultType="com.extracme.evcard.membership.core.dto.MemberExemptDepositDto">
    select
		a.EXEMPT_DEPOSIT as exemptDeposit,
		a.DATA_ORIGIN,
		b.EXEMPT_DEPOSIT as agencyExemptDeposit,
		a.STATUS as userStatus,
		c.STATUS as cardStatus
		from
		${siacSchema}.MEMBERSHIP_INFO a
		left join ${siacSchema}.agency_info b on a.AGENCY_ID = b.AGENCY_ID
		left join ${siacSchema}.card_info c on a.CARD_NO = c.CARD_NO and a.AUTH_ID = c.AUTH_ID
		where a.AUTH_ID = #{authId} AND a.MEMBERSHIP_TYPE = 0
  </select>

  <select id="getAllAuthIdByUser" resultType="String">
    select AUTH_ID
    from ${siacSchema}.membership_info
    where ACCOUNT_STATUS = 0 and  DRIVER_CODE is not null and DRIVER_CODE != '' and DRIVER_CODE in (select DRIVER_CODE FROM ${siacSchema}.membership_info WHERE AUTH_ID=#{authId})
  </select>
  
  <resultMap type="com.extracme.evcard.membership.core.dto.MembershipBasicInfo" id="memberBaseInfoMap">
  	<id column="pk_id" property="pkId" jdbcType="BIGINT" />
    <result column="mid" property="mid" jdbcType="VARCHAR" />
    <result column="AUTH_ID" property="authId" jdbcType="VARCHAR" />
    <result column="UID" property="uid" jdbcType="VARCHAR" />
    <result column="DRIVER_CODE" property="driverCode" jdbcType="VARCHAR" />
    <result column="MEMBERSHIP_TYPE" property="membershipType" jdbcType="SMALLINT" />
    <result column="NAME" property="name" jdbcType="VARCHAR" />
    <result column="PASSWORD" property="password" jdbcType="VARCHAR" />
    <result column="MOBILE_PHONE" property="mobilePhone" jdbcType="VARCHAR" />
    <result column="REG_TIME" property="regTime" jdbcType="VARCHAR" />
    <result column="AGENCY_ID" property="agencyId" jdbcType="VARCHAR" />
    <result column="LICENSE_EXPIRATION_TIME" property="licenseExpirationTime" jdbcType="VARCHAR" />
    <result column="REVIEW_STATUS" property="reviewStatus" jdbcType="INTEGER" />
    <result column="CARD_NO" property="cardNo" jdbcType="VARCHAR" />
    <result column="DATA_ORIGIN" property="dataOrigin" jdbcType="INTEGER" />
    <result column="STATUS" property="status" jdbcType="INTEGER" />
    <result column="BLACKLIST_REASON" property="blacklistReason" jdbcType="VARCHAR" />
    <result column="REVIEW_USER" property="reviewUser" jdbcType="VARCHAR" />
    <result column="REVIEW_ITEMS" property="reviewItems" jdbcType="VARCHAR" />
    <result column="DEPOSIT" property="deposit" jdbcType="DECIMAL" />
    <result column="RENT_MINS" property="rentMins" jdbcType="DECIMAL" />
    <result column="EXEMPT_DEPOSIT" property="exemptDeposit" jdbcType="INTEGER" />
    <result column="CREATED_TIME" property="createdTime" jdbcType="VARCHAR" />
    <result column="CREATED_USER" property="createdUser" jdbcType="VARCHAR" />
    <result column="UPDATED_TIME" property="updatedTime" jdbcType="VARCHAR" />
    <result column="UPDATED_USER" property="updatedUser" jdbcType="VARCHAR" />
    <result column="PERSONNEL_STATE" property="personnelState" jdbcType="INTEGER" />
    <result column="APPLY_STATUS" property="applyStatus" jdbcType="INTEGER" />
    <result column="SERVICE_VER" property="serviceVer" jdbcType="VARCHAR" />
    <result column="SERVICE_VER_TIME" property="serviceVerTime" jdbcType="VARCHAR" />
    <result column="APP_KEY" property="appKey" jdbcType="VARCHAR" />
    <result column="CHANNEL_ID" property="channelId" jdbcType="VARCHAR" />
    <result column="REGIONID" property="regionid" jdbcType="BIGINT" />
    <result column="deposit_vehicle" property="depositVehicle" jdbcType="DECIMAL" />
    <result column="province_of_origin" property="provinceOfOrigin" jdbcType="VARCHAR" />
    <result column="city_of_origin" property="cityOfOrigin" jdbcType="VARCHAR" />
    <result column="area_of_origin" property="areaOfOrigin" jdbcType="VARCHAR" />
    <result column="REVIEW_TIME" property="reviewTime" jdbcType="VARCHAR" />
    <result column="APP_REVIEW_TIME" property="appReviewTime" jdbcType="VARCHAR" />
    <result column="REVIEW_REMARK" property="reviewRemark" jdbcType="VARCHAR" />
    <result column="REVIEW_ITEM_IDS" property="reviewItemIds" jdbcType="VARCHAR" />
    <result column="REVIEW_ITEM_NAME" property="reviewItemName" jdbcType="VARCHAR" />
    <result column="REVIEW_MODE" property="reviewMode" jdbcType="INTEGER" />
    <result column="FACE_RECOGNITION_IMG_URL" property="faceRecognitionImgUrl" jdbcType="VARCHAR" />
    <result column="customer_id" property="customerId" jdbcType="VARCHAR" />
    <result column="authentication_status" property="authenticationStatus" jdbcType="INTEGER"/>
    <result column="national" property="national" jdbcType="VARCHAR" />
    <result column="driving_license_type" property="drivingLicenseType" jdbcType="VARCHAR"/>
    <result column="driver_license_input_type" property="driverLicenseInputType" jdbcType="VARCHAR"/>
    <result column="foreign_nationality" property="foreignNationality" jdbcType="INTEGER"/>
    <result column="ACCOUNT_STATUS" property="accountStatus" jdbcType="INTEGER"/>
    <result column="UNREGISTER_TIME" property="unregisterTime" jdbcType="VARCHAR" />
    <result column="ACCOUNT_STATUS" property="accountStatus" jdbcType="INTEGER"/>
    <result column="hold_idcard_pic_url" property="holdIdcardPicUrl" jdbcType="VARCHAR" />
    <result column="INFO_ORIGIN" property="infoOrigin" jdbcType="VARCHAR" />
    <result column="OBTAIN_DRIVER_TIMER" property="obtainDriverTimer" jdbcType="VARCHAR" />
    <result column="MAIL" property="mail" jdbcType="VARCHAR" />
    <result column="file_no" property="fileNo" jdbcType="VARCHAR"/>
    <result column="file_no_img_url" property="fileNoImgUrl" jdbcType="VARCHAR"/>
    <result column="ORG_ID" property="orgId" jdbcType="VARCHAR" />
    <result column="AREA" property="area" jdbcType="VARCHAR" />
    <result column="DRIVING_LICENSE_IMG_URL" property="drivingLicenseImgUrl" jdbcType="VARCHAR"/>
    <result column="license_auth_status" property="licenseAuthStatus" jdbcType="INTEGER"/>
    <result column="license_elements_auth_status" property="licenseElementsAuthStatus" jdbcType="INTEGER" />
    <result column="id_card_number" property="idCardNumber" jdbcType="VARCHAR" />
    <result column="identity_id" property="identityId" jdbcType="BIGINT" />
    <result column="identity_first_auth_time" property="identityFirstAuthTime" jdbcType="TIMESTAMP" />
    <result column="license_review_status" property="licenseReviewStatus" jdbcType="VARCHAR" />
    <result column="license_first_auth_time" property="licenseFirstAuthTime" jdbcType="TIMESTAMP" />
    <result column="license_submit_appkey" property="licenseSubmitAppkey" jdbcType="VARCHAR" />
    <result column="license_img_type" property="licenseImgType" jdbcType="INTEGER" />

    <result column="authentication_status_new" property="authenticationStatusNew" jdbcType="INTEGER" />
    <result column="id_type" property="idType" jdbcType="INTEGER" />
    <result column="second_app_key" property="secondAppKey" jdbcType="VARCHAR" />
    <result column="passport_no" property="passportNo" jdbcType="VARCHAR" />
  </resultMap>
  
  <sql id="MembershipBasicInfo_Column_List">
  		pk_id,
		mid,
		AUTH_ID,
		UID,
		DRIVER_CODE,
		MEMBERSHIP_TYPE,
		NAME,
		PASSWORD,
		MOBILE_PHONE,
		REG_TIME,
		AGENCY_ID,
		LICENSE_EXPIRATION_TIME,
		REVIEW_STATUS,
		CARD_NO,
		DATA_ORIGIN,
		STATUS,
		BLACKLIST_REASON,
		REVIEW_USER,
		REVIEW_ITEMS,
		DEPOSIT,
		RENT_MINS,
		EXEMPT_DEPOSIT,
		CREATED_TIME,
		CREATED_USER,
		UPDATED_TIME,
		UPDATED_USER,
		PERSONNEL_STATE,
		APPLY_STATUS,
		SERVICE_VER,
		SERVICE_VER_TIME,
		APP_KEY,
		CHANNEL_ID,
		REGIONID,
		deposit_vehicle,
		province_of_origin,
		city_of_origin,
		area_of_origin,
		REVIEW_TIME,
		APP_REVIEW_TIME,
		REVIEW_REMARK,
		REVIEW_ITEM_IDS,
		REVIEW_ITEM_NAME,
		REVIEW_MODE,
		FACE_RECOGNITION_IMG_URL,
		customer_id,
		authentication_status,
		national,
		driving_license_type,
		driver_license_input_type,
		foreign_nationality,
		ACCOUNT_STATUS,
		UNREGISTER_TIME,
        hold_idcard_pic_url,
        INFO_ORIGIN,
        OBTAIN_DRIVER_TIMER,
        MAIL,
        file_no,
        file_no_img_url,
        ORG_ID,
        area,
        DRIVING_LICENSE_IMG_URL,
        license_auth_status,
        license_elements_auth_status,
        id_card_number,
        identity_id,
        identity_first_auth_time,
        license_review_status,
        license_first_auth_time,
        license_submit_appkey,
        license_img_type,
        second_app_key,
        passport_no,
        id_type
  </sql>

  <select id="getUserBasicInfo" resultMap="memberBaseInfoMap">
    select <include refid="MembershipBasicInfo_Column_List"/>
    FROM ${siacSchema}.membership_info
    WHERE AUTH_ID = #{authId} AND MEMBERSHIP_TYPE = #{membershipType} AND ACCOUNT_STATUS != 2
  </select>

  <select id="getUserBasicInfoByPkId" resultMap="memberBaseInfoMap" parameterType="java.lang.Long">
    select <include refid="MembershipBasicInfo_Column_List"/>
    FROM ${siacSchema}.membership_info m
    WHERE pk_id = #{pkId}
  </select>

  <select id="getUserBasicInfoByMid" resultMap="memberBaseInfoMap" parameterType="java.lang.String">
    select <include refid="MembershipBasicInfo_Column_List"/>
    FROM ${siacSchema}.membership_info m
    WHERE mid = #{mid}
  </select>

  <select id="getMembershipByPhone" resultMap="memberBaseInfoMap">
    select <include refid="MembershipBasicInfo_Column_List"/>
    FROM ${siacSchema}.membership_info
    WHERE MOBILE_PHONE = #{phone} AND MEMBERSHIP_TYPE = #{membershipType} AND ACCOUNT_STATUS != 2 limit 1
  </select>


    <select id="reviewAndCardStatus" resultType="com.extracme.evcard.membership.core.dto.ReviewAndCarStatusDto">
        SELECT A.CARD_NO AS cardNo,A.REVIEW_STATUS AS reviewStatus,A.STATUS AS userstatus,B.ACTIVATE_STATUS AS activeStatus,B.STATUS AS carStatus ,A.authentication_status as authenticationStatus
        FROM ${siacSchema}.membership_info A left join ${siacSchema}.CARD_INFO B on  A.CARD_NO = B.CARD_NO and A.AUTH_ID = B.AUTH_ID
        where A.AUTH_ID = #{authId} and A.MEMBERSHIP_TYPE = #{memberType} AND A.ACCOUNT_STATUS != 2
    </select>

  <update id="updateServiceVer" parameterType="com.extracme.evcard.membership.credit.dto.UpdateServiceVerDto">
    UPDATE ${siacSchema}.MEMBERSHIP_INFO
    SET UPDATED_USER = #{updateUser},SERVICE_VER = #{serviceVer},UPDATED_TIME = #{updateTime}
    WHERE auth_id = #{authId}
    and MEMBERSHIP_TYPE = #{memberType} AND ACCOUNT_STATUS != 2
  </update>
  
  <!-- 修改会员手机号 -->
  <update id="updateMobilePhone">
  	UPDATE ${siacSchema}.MEMBERSHIP_INFO
    SET UPDATED_USER = #{optUser},UPDATED_TIME = #{updateTime}, MOBILE_PHONE = #{mobilePhone}
    WHERE auth_id = #{authId}
    and MEMBERSHIP_TYPE = #{membershipType} AND ACCOUNT_STATUS != 2
    <if test="${orgIdCheck} == 1">
      and ORG_ID = #{orgId}
    </if>
  </update>

  <!-- 修改会员邮箱 -->
  <update id="updateMail">
  	UPDATE ${siacSchema}.MEMBERSHIP_INFO
    SET UPDATED_USER = #{updateUser},UPDATED_TIME = #{updateTime}, MAIL = #{mail}
    WHERE auth_id = #{authId}
    AND MEMBERSHIP_TYPE = 0 AND ACCOUNT_STATUS != 2
  </update>
  
  <!-- 修改会员地址 -->
  <update id="updateAddress">
  	UPDATE ${siacSchema}.MEMBERSHIP_INFO
    SET UPDATED_USER = #{updateUser},UPDATED_TIME = #{updateTime}, 
    ADDRESS = #{address},PROVINCE = #{province},CITY = #{city},AREA = #{area}
    WHERE AUTH_ID = #{authId}
    AND MEMBERSHIP_TYPE = 0 AND ACCOUNT_STATUS != 2
  </update>
  
  <!-- 修改会员审核状态为由审核不通过变为待审核 -->
  <update id="updateReviewStatus">
  	UPDATE ${siacSchema}.MEMBERSHIP_INFO
    SET REVIEW_STATUS = 0
    WHERE REVIEW_STATUS = 2
    AND AUTH_ID = #{authId}
    AND MEMBERSHIP_TYPE = 0
  </update>
  
  <!-- 重置外部会员密码 -->
  <update id="updatePassword">
  	UPDATE ${siacSchema}.MEMBERSHIP_INFO
    SET PASSWORD = #{password},UPDATED_USER = #{updateUser},UPDATED_TIME = #{updateTime}
    WHERE AUTH_ID = #{authId}
    AND MEMBERSHIP_TYPE = 0 AND ACCOUNT_STATUS != 2
  </update>


    <!-- 查询会员账号状态 -->
    <select id="getAccountStatusByPhone" resultType="com.extracme.evcard.membership.core.dto.AccountStatusDto">
        SELECT
        A.pk_id as pkId, A.AUTH_ID AS authId,
        A.UID as uid,
        A.MEMBERSHIP_TYPE AS membershipType,
        A.REVIEW_STATUS AS reviewStatus,
        A.STATUS AS userStatus,
		A.UNREGISTER_TIME AS unregisterTime,
		A.ACCOUNT_STATUS as accountStatus,
        A.MOBILE_PHONE as mobilePhone,
        A.DRIVER_CODE as driverCode
        FROM ${siacSchema}.membership_info A
        where A.MEMBERSHIP_TYPE = #{membershipType}
        and A.MOBILE_PHONE = #{mobilePhone}
      <if test="${orgIdCheck} == 1">
        and A.ORG_ID = #{orgId}
      </if>
        order by ACCOUNT_STATUS asc, UNREGISTER_TIME desc
        limit 1
    </select>

    <!-- 查询会员账号状态 -->
    <select id="getAccountStatusByDriverCode" resultType="com.extracme.evcard.membership.core.dto.AccountStatusDto">
        SELECT
        A.pk_id as pkId, A.AUTH_ID AS authId,
        A.UID as uid,
        A.MEMBERSHIP_TYPE AS membershipType,
        A.REVIEW_STATUS AS reviewStatus,
        A.STATUS AS userStatus,
		A.UNREGISTER_TIME AS unregisterTime,
		A.ACCOUNT_STATUS as accountStatus,
        A.MOBILE_PHONE as mobilePhone,
        A.DRIVER_CODE as driverCode
        FROM ${siacSchema}.membership_info A
        where A.MEMBERSHIP_TYPE = #{membershipType}
        and A.DRIVER_CODE = #{driverCode}
        order by ACCOUNT_STATUS asc, UNREGISTER_TIME desc
        limit 1
    </select>

  <!-- 账号注销/恢复 -->
  <update id="accountUnregister">
  	UPDATE ${siacSchema}.MEMBERSHIP_INFO
    SET UPDATED_USER = #{optUser}, UPDATED_TIME = #{updateTime},
    UNREGISTER_TIME = #{unregisterTime},
    ACCOUNT_STATUS = #{accountStatus},
    channel_id = ''
    WHERE auth_id = #{authId}
    and MEMBERSHIP_TYPE = #{membershipType}
  </update>

  <!-- 注销当日超过冻结期的会员 -->
  <update id="autoCompleteUnregister">
  	UPDATE ${siacSchema}.MEMBERSHIP_INFO ignore index (idx_me3_membership_info)
    SET ACCOUNT_STATUS = 2,
    UPDATED_USER = #{optUser}, UPDATED_TIME = #{updateTime}
    WHERE MEMBERSHIP_TYPE = 0
    and ACCOUNT_STATUS = 1
    and UNREGISTER_TIME &lt;= CONCAT(#{registerDate},'235959')
  </update>


  <select id="selectAutoCompleteUnregister" resultType="com.extracme.evcard.membership.core.dto.AccountStatusDto">
  	SELECT A.pk_id as pkId, A.AUTH_ID AS authId,
        A.UID as uid,
        A.MEMBERSHIP_TYPE AS membershipType,
        A.REVIEW_STATUS AS reviewStatus,
        A.STATUS AS userStatus,
		A.UNREGISTER_TIME AS unregisterTime,
		A.ACCOUNT_STATUS as accountStatus,
        A.MOBILE_PHONE as mobilePhone,
        A.MID as mid
    FROM ${siacSchema}.MEMBERSHIP_INFO A ignore index (idx_me3_membership_info)
    WHERE A.MEMBERSHIP_TYPE = 0
    and A.ACCOUNT_STATUS = 1
    and A.UNREGISTER_TIME &lt;= CONCAT(#{registerDate},'235959')
  </select>


  <!-- 会员会员账号信息 -->
  <select id="getMemberAccountInfo" resultType="com.extracme.evcard.membership.core.model.MembershipAccountInfo">
        SELECT
        A.pk_id as pkId,
        A.AUTH_ID as authId,
        A.UID as uid,
        A.mid as mid,
        A.NAME as name,
		A.MOBILE_PHONE as mobilePhone,
		A.REG_TIME as regTime,
		A.UNREGISTER_TIME as unregisterTime,
		A.AGENCY_ID as agencyId, A.city_of_origin as cityOfOrigin,
		A.DATA_ORIGIN as dataOrigin, A.APP_KEY as appKey, A.CHANNEL_ID as channelId,

		A.RENT_MINS as rentMins,
		A.DEPOSIT as deposit,
		A.deposit_vehicle as depositVehicle,
		A.EXEMPT_DEPOSIT as exemptDeposit,

        A.STATUS AS status,
        A.REVIEW_STATUS AS reviewStatus,
        A.CARD_NO AS cardNo,
        B.ACTIVATE_STATUS AS cardActiveStatus,
        B.STATUS AS cardStatus,
		A.authentication_status as authenticationStatus

        FROM ${siacSchema}.membership_info A
        left join ${siacSchema}.CARD_INFO B on  A.CARD_NO = B.CARD_NO and A.AUTH_ID = B.AUTH_ID
        where A.AUTH_ID = #{authId} and A.MEMBERSHIP_TYPE = #{membershipType}
  </select>
  <!-- 通过手机号和会员类型修改密码 -->
	<update id="updatePasswordByMobilePhone">
		UPDATE ${siacSchema}.membership_info
		SET `PASSWORD` = #{password},
		UPDATED_USER = #{optUserName}, 
		UPDATED_TIME = #{updateTime}
		WHERE
		MOBILE_PHONE = #{mobilePhone}
		AND MEMBERSHIP_TYPE = 0
		AND ACCOUNT_STATUS = 0
      <if test="${orgIdCheck} == 1">
        and ORG_ID = #{orgId}
      </if>
	</update>

  <select id="checkMobilRegistered" resultType="com.extracme.evcard.membership.core.dto.AccountStatusDto">
        SELECT
        A.pk_id as pkId, A.AUTH_ID AS authId,
        A.UID as uid,
        A.MEMBERSHIP_TYPE AS membershipType,
        A.REVIEW_STATUS AS reviewStatus,
        A.STATUS AS userStatus,
		A.UNREGISTER_TIME AS unregisterTime,
		A.ACCOUNT_STATUS as accountStatus
        FROM ${siacSchema}.membership_info A
        where A.MEMBERSHIP_TYPE = #{membershipType}
        and A.MOBILE_PHONE = #{mobilePhone}
        AND AUTH_ID != #{authId}
        order by ACCOUNT_STATUS asc, UNREGISTER_TIME desc
        limit 1
    </select>
  <select id="getUserRegionInfo" resultType="com.extracme.evcard.membership.core.dto.MembershipRegionInfo">
      SELECT
          t.MOBILE_PHONE as mobilePhone,
          t.`NAME` as name,
          o.REGION as region
      FROM
          ${siacSchema}.membership_info t
      LEFT JOIN ${siacSchema}.operation_region o ON t.REGIONID = o.REGIONID
      where t.MEMBERSHIP_TYPE = #{membershipType}
      AND t.AUTH_ID = #{authId}
  </select>

  <!-- 会员邀请相关信息, 仅查询账号状态为正常的会员 -->
  <select id="getUserInviteInfo" resultType="com.extracme.evcard.membership.core.dto.MemberInviteInfoDto">
        SELECT
        A.pk_id as pkId,
        A.AUTH_ID as authId,
        A.UID as uid,
        A.MEMBERSHIP_TYPE AS membershipType,
        A.NAME as name,
		A.MOBILE_PHONE as mobilePhone,
		A.REG_TIME as regTime,
		A.APP_KEY as appKey,
		A.city_of_origin as cityOfOrigin,
		c.ORG_ID as orgId,
		c.ORG_NAME as orgName,
		a.review_status as reviewStatus,
		a.review_time as reviewTime,
		A.SHARE_UID as shareUid
        FROM ${siacSchema}.membership_info A
        LEFT JOIN siac.city b ON A.city_of_origin = b.CITY
        LEFT JOIN isv.org_info c ON b.org_id = c.ORG_ID
        where A.AUTH_ID = #{authId} and A.MEMBERSHIP_TYPE = #{membershipType}
        and A.ACCOUNT_STATUS = 0
  </select>

  <select id="selectMembershipInfoByAuthId" resultMap="BaseResultMap">
    SELECT
      <include refid="Base_Column_List" />
      ,
      <include refid="Blob_Column_List" />
    FROM ${siacSchema}.membership_info
    where MEMBERSHIP_TYPE = 0
    AND ACCOUNT_STATUS = 0
    and auth_id = #{authId}
    <if test="${orgIdCheck} == 1">
      AND ORG_ID = #{orgId}
    </if>
  </select>

  <!-- 修改会员头像 -->
  <update id="updateUserImage">
    UPDATE ${siacSchema}.MEMBERSHIP_INFO
    SET UPDATED_USER = #{updateUser},UPDATED_TIME = #{updateTime}, USER_IMG_URL = #{imageUrl}
    WHERE auth_id = #{authId}
    <if test="${orgIdCheck} == 1">
      AND ORG_ID = #{orgId}
    </if>
    AND MEMBERSHIP_TYPE = 0 AND ACCOUNT_STATUS = 0
  </update>
    <update id="updateUserFileNo">
      UPDATE ${siacSchema}.MEMBERSHIP_INFO
      SET file_no = #{fileNo},file_no_img_url = #{fileNoImgUrl},license_auth_status=#{licenseAuthStatus},license_elements_auth_status=#{licenseElementsAuthStatus}
      WHERE auth_id = #{authId}
      AND MEMBERSHIP_TYPE = 0 AND ACCOUNT_STATUS = 0
    </update>
  <update id="updateUserIdCardPic">
      UPDATE ${siacSchema}.MEMBERSHIP_INFO
      <set>
        <if test="idcardPicUrl != null">
          idcard_pic_url = #{idcardPicUrl},
        </if>
        <if test="holdIdcardPicUrl != null">
          hold_idcard_pic_url = #{holdIdcardPicUrl},
        </if>
        <if test="authenticationStatus != null">
          authentication_status = #{authenticationStatus},
        </if>
        <if test="reviewMode != null">
          REVIEW_MODE = #{reviewMode},
        </if>
        <if test="updatedTime != null">
          UPDATED_TIME = #{updatedTime},
        </if>
        <if test="updatedUser != null">
          UPDATED_USER = #{updatedUser},
        </if>
        <if test="appReviewTime != null">
          APP_REVIEW_TIME = #{appReviewTime},
        </if>
        <if test="reviewStatus != null">
          REVIEW_STATUS = #{reviewStatus},
        </if>
        <if test="faceRecognitionImgUrl != null">
          FACE_RECOGNITION_IMG_URL = #{faceRecognitionImgUrl},
        </if>
        <if test="idType != null">
          id_type = #{idType},
        </if>
        <if test="passportNo != null">
          passport_no = #{passportNo},
        </if>
        <if test="national != null">
          national = #{national},
        </if>
      </set>
      where AUTH_ID = #{authId} and MEMBERSHIP_TYPE =0
  </update>
  <update id="updateFaceRecognitionImgUrl">
    UPDATE ${siacSchema}.MEMBERSHIP_INFO
    <set>
      <if test="idCardNumber != null">
        id_card_number = #{idCardNumber},
      </if>
      <if test="reviewStatus != null">
        REVIEW_STATUS = #{reviewStatus},
      </if>
      <if test="licenseAuthStatus != null">
        license_auth_status = #{licenseAuthStatus},
      </if>
      <if test="reviewMode != null">
        REVIEW_MODE = #{reviewMode},
      </if>
      <if test="authenticationStatus != null">
        authentication_status = #{authenticationStatus},
      </if>
      <if test="faceRecognitionImgUrl != null">
        FACE_RECOGNITION_IMG_URL = #{faceRecognitionImgUrl},
      </if>
      <if test="updatedTime != null">
        UPDATED_TIME = #{updatedTime},
      </if>
      <if test="appReviewTime != null">
      APP_REVIEW_TIME = #{appReviewTime},
    </if>
      <if test="reviewTime != null">
        REVIEW_TIME = #{reviewTime},
      </if>
      <if test="updatedUser != null">
        UPDATED_USER = #{updatedUser},
      </if>
      <if test="reviewItems != null">
        REVIEW_ITEMS = #{reviewItems},
      </if>
    </set>
    where AUTH_ID = #{authId} and MEMBERSHIP_TYPE =0
    <if test="appKey == null">
      and authentication_status !=2
    </if>
  </update>
  <update id="updateMemberCardNoByAuthId">
    update
      ${siacSchema}.membership_info
    set CARD_NO = #{cardNo,jdbcType=VARCHAR}
    where MEMBERSHIP_TYPE = 0
    and AUTH_ID = #{authId,jdbcType=VARCHAR}
  </update>

  <update id="updateMemberCardNoByAuthIdAndType">
    update
      ${siacSchema}.membership_info
    set CARD_NO = #{cardNo,jdbcType=VARCHAR}
    where MEMBERSHIP_TYPE = #{membershipType,jdbcType=INTEGER}
    and AUTH_ID = #{authId,jdbcType=VARCHAR}
  </update>


  <select id="selectSimpleMembershipInfo"  resultType="com.extracme.evcard.membership.core.dto.SimpleMembershipInfoDTO">
    SELECT
      a.AUTH_ID as authId,
      a.UID as uid,
      a.MOBILE_PHONE as mobilePhone,
      a.DRIVER_CODE as driverCode ,
      a.NAME,
      c.ORG_NAME as orgName,
      s.second_app_key as secondAppKey,
      s.second_app_key_name as secondAppKeyName,
      k.APP_KEY as firstAppKey,
      k.PLAT_NAME as firstAppKeyName,
      a.mid
    FROM
      siac.membership_info a
      LEFT JOIN siac.city b ON a.city_of_origin = b.CITY
      LEFT JOIN isv.org_info c ON b.org_id = c.ORG_ID
      LEFT JOIN siac.second_app_key_manager s on a.second_app_key = s.second_app_key
      LEFT JOIN siac.app_key_manager k on a.APP_KEY = k.APP_KEY
    where
     ACCOUNT_STATUS != 2
      <if test="authId != null">
       and  a.AUTH_ID = #{authId}
      </if>
      <if test="driverCode != null">
        and  a.DRIVER_CODE = #{driverCode}
      </if>
      <if test="mobilePhone != null">
        and a.MOBILE_PHONE = #{mobilePhone}
      </if>
      <if test="name != null">
        and a.name like concat(#{name},'%')
      </if>
      <if test="platFormId != null and platFormId != 0">
        and s.platform_id = #{platFormId}
      </if>
      LIMIT #{page.offSet},#{page.limitSet}
  </select>

  <!-- 检查驾照占用 -->
  <select id="checkDriverCodeOccupied" resultType="com.extracme.evcard.membership.credit.model.MembershipBaseInfo">
    select pk_id AS pkId,UID as uid, city_of_origin AS cityOfOrigin,APP_KEY AS appKey,DATA_ORIGIN AS dataOrigin,REVIEW_STATUS AS reviewStatus,CARD_NO AS cardNo,name as name,
    SERVICE_VER AS serverVer, AUTH_ID as authId, DRIVER_CODE as driverCode, MEMBERSHIP_TYPE AS membershipType, MOBILE_PHONE as mobilePhone,authentication_status AS authenticationStatus,
    MID AS mid
    FROM ${siacSchema}.membership_info
    where
      AUTH_ID != #{authId}
    AND DRIVER_CODE = #{driverCode}
    AND MEMBERSHIP_TYPE = #{membershipType}
    AND ACCOUNT_STATUS = 0
  </select>

  <!-- 查询需要重新ocr提取档案编号的用户数据  3.2.0上线后可删除 -->
  <select id="ocrDriverFileNoJob" resultMap="ResultMapWithBLOBs" >
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    FROM ${siacSchema}.membership_info
    WHERE REVIEW_STATUS = 1 and ACCOUNT_STATUS = 0
  </select>

  <!-- 根据条件查询单个会员 -->
  <select id="queryOneMemberByCondition" resultMap="memberBaseInfoMap" parameterType="com.extracme.evcard.membership.core.input.QueryMemberInputDTO">
    select <include refid="MembershipBasicInfo_Column_List"/>
    FROM ${siacSchema}.membership_info
    <where>
      <if test="memberType != null">
        MEMBERSHIP_TYPE = #{memberType}
      </if>
      <if test="authId != null">
       and  AUTH_ID = #{authId}
      </if>
      <if test="driverCode != null">
        and  DRIVER_CODE = #{driverCode}
      </if>
      <if test="orgId != null">
        and ORG_ID = #{orgId}
      </if>
      <if test="mobilePhone != null">
        and MOBILE_PHONE = #{mobilePhone}
      </if>
      <if test="name != null">
        and `NAME` = #{name}
      </if>
      <if test="email != null">
        and MAIL = #{email}
      </if>
      <if test="personnelState != null">
        and PERSONNEL_STATE in
        <foreach item="item" collection="personnelState" index="index" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
    </where>
    limit 1
  </select>

  <!-- 根据条件查询会员列表 -->
  <select id="queryMemberListByCondition" resultMap="memberBaseInfoMap" parameterType="com.extracme.evcard.membership.core.input.QueryMemberListInputDTO">
    select <include refid="MembershipBasicInfo_Column_List"/>
    FROM ${siacSchema}.membership_info
    <where>
      <if test="memberType != null">
        and  MEMBERSHIP_TYPE = #{memberType}
      </if>
      <if test="driverCode != null">
        and  DRIVER_CODE = #{driverCode}
      </if>
      <if test="authIdList != null">
        and AUTH_ID in
        <foreach item="item" collection="authIdList" index="index" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="regionId != null">
        and REGIONID = #{regionId}
      </if>
      <if test="orgId != null ">
        <choose>
          <when test="isLikeQueryOrgId == 1">
            and ORG_ID like concat('%',#{orgId})
          </when>
          <when test="isLikeQueryOrgId == 2">
            and ORG_ID like concat(#{orgId},'%')
          </when>
          <when test="isLikeQueryOrgId == 3">
            and ORG_ID like concat('%',#{orgId},'%')
          </when>
          <otherwise>
            and ORG_ID = #{orgId}
          </otherwise>
        </choose>
      </if>
      <if test="mobilePhone != null ">
        <choose>
          <when test="isLikeQueryMobilePhone == 1">
            and MOBILE_PHONE like concat('%',#{mobilePhone})
          </when>
          <when test="isLikeQueryMobilePhone == 2">
            and MOBILE_PHONE like concat(#{mobilePhone},'%')
          </when>
          <when test="isLikeQueryMobilePhone == 3">
            and MOBILE_PHONE like concat('%',#{mobilePhone},'%')
          </when>
          <otherwise>
            and MOBILE_PHONE = #{mobilePhone}
          </otherwise>
        </choose>
      </if>
      <if test="name != null ">
        <choose>
          <when test="isLikeQueryName == 1">
            and `NAME` like concat('%',#{name})
          </when>
          <when test="isLikeQueryName == 2">
            and `NAME` like concat(#{name},'%')
          </when>
          <when test="isLikeQueryName == 3">
            and `NAME` like concat('%',#{name},'%')
          </when>
          <otherwise>
            and `NAME` = #{name}
          </otherwise>
        </choose>
      </if>
      <if test="personnelState != null">
        and PERSONNEL_STATE in
        <foreach item="item" collection="personnelState" index="index" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
    </where>
    <if test="page != null">
      limit #{page.offSet},#{page.limitSet}
    </if>
  </select>

  <update id="updateMemberInfo" parameterType="com.extracme.evcard.membership.core.input.UpdateMemberInputDTO" >
    update ${siacSchema}.membership_info
    <set >
      <if test="name != null" >
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="mobilePhone != null" >
        MOBILE_PHONE = #{mobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="email != null" >
        MAIL = #{email,jdbcType=VARCHAR},
      </if>
      <if test="area != null" >
        AREA = #{area,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        CITY = #{city,jdbcType=VARCHAR},
      </if>
      <if test="province != null" >
        PROVINCE = #{province,jdbcType=VARCHAR},
      </if>
      <if test="personnelState != null" >
        PERSONNEL_STATE = #{personnelState,jdbcType=DECIMAL},
      </if>
      <if test="regionId != null" >
        REGIONID = #{regionId,jdbcType=BIGINT},
      </if>
      <if test="orgId != null" >
        ORG_ID = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="cardNo != null ">
        CARD_NO = #{cardNo}
      </if>
      <if test="updateTime != null" >
        UPDATED_TIME = #{updateTime,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null" >
        UPDATED_USER = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="driverCode != null" >
        DRIVER_CODE = #{driverCode},
      </if>
    </set>
    where AUTH_ID = #{authId,jdbcType=VARCHAR}
    and MEMBERSHIP_TYPE =
    #{memberType,jdbcType=DECIMAL}
  </update>
  <select id="checkFileNoOccupied" resultType="com.extracme.evcard.membership.credit.model.MembershipBaseInfo">
    select pk_id AS pkId,city_of_origin AS cityOfOrigin,APP_KEY AS appKey,DATA_ORIGIN AS dataOrigin,REVIEW_STATUS AS reviewStatus,CARD_NO AS cardNo,name as name,
    SERVICE_VER AS serverVer, AUTH_ID as authId, DRIVER_CODE as driverCode, MEMBERSHIP_TYPE AS membershipType, MOBILE_PHONE as mobilePhone,authentication_status AS authenticationStatus,
    MID as mid
    FROM ${siacSchema}.membership_info
    where
      AUTH_ID != #{authId}
    AND file_no = #{fileNo}
    AND MEMBERSHIP_TYPE = #{membershipType}
    AND ACCOUNT_STATUS = 0
  </select>
  <select id="checkPassPortOccupied"
          resultType="com.extracme.evcard.membership.credit.model.MembershipBaseInfo">
    select pk_id AS pkId, UID as uid, city_of_origin AS cityOfOrigin,APP_KEY AS appKey,DATA_ORIGIN AS dataOrigin,REVIEW_STATUS AS reviewStatus,CARD_NO AS cardNo,name as name,
    SERVICE_VER AS serverVer, AUTH_ID as authId, DRIVER_CODE as driverCode, MEMBERSHIP_TYPE AS membershipType, MOBILE_PHONE as mobilePhone,authentication_status AS authenticationStatus
    FROM ${siacSchema}.membership_info
    where
      AUTH_ID != #{authId}
    AND passport_no = #{passPort}
    AND MEMBERSHIP_TYPE = #{membershipType}
    AND ACCOUNT_STATUS = 0
  </select>


  <select id="selectMemberInfoByUpdateTime" resultMap="memberBaseInfoMap">
    select
    a.pk_id,a.mid,a.AUTH_ID,a.UID,a.DRIVER_CODE,a.MEMBERSHIP_TYPE,a.NAME,a.PASSWORD,a.MOBILE_PHONE,a.REG_TIME,a.AGENCY_ID,a.LICENSE_EXPIRATION_TIME,a.REVIEW_STATUS,a.CARD_NO
,a.DATA_ORIGIN,a.STATUS,a.BLACKLIST_REASON,a.REVIEW_USER,a.REVIEW_ITEMS,a.DEPOSIT,a.RENT_MINS,a.EXEMPT_DEPOSIT,a.CREATED_TIME,a.CREATED_USER,a.UPDATED_TIME,a.UPDATED_USER
,a.PERSONNEL_STATE,a.APPLY_STATUS,a.SERVICE_VER,a.SERVICE_VER_TIME,a.APP_KEY,a.CHANNEL_ID,a.REGIONID,a.deposit_vehicle,a.province_of_origin,a.city_of_origin,a.area_of_origin
,a.REVIEW_TIME,a.APP_REVIEW_TIME,a.REVIEW_REMARK,a.REVIEW_ITEM_IDS,a.REVIEW_ITEM_NAME,a.REVIEW_MODE,a.FACE_RECOGNITION_IMG_URL,a.customer_id,a.authentication_status,a.national
,a.driving_license_type,a.driver_license_input_type,a.foreign_nationality,a.ACCOUNT_STATUS,a.UNREGISTER_TIME,a.hold_idcard_pic_url,a.INFO_ORIGIN,a.OBTAIN_DRIVER_TIMER,a.MAIL,a.file_no
,a.file_no_img_url,a.ORG_ID,a.area,a.DRIVING_LICENSE_IMG_URL,a.license_auth_status,a.license_elements_auth_status,a.id_card_number,a.identity_id,a.identity_first_auth_time
,a.license_review_status,a.license_first_auth_time,a.license_submit_appkey,a.license_img_type,b.authentication_status as authentication_status_new
    from ${siacSchema}.membership_info a left join ${siacSchema}.member_identity_document b on a.identity_id = b.id
    where a.UPDATED_TIME >= #{startTimeStr}
   and a.UPDATED_TIME  <![CDATA[ <= #{endTimeStr} ]]>
   </select>

  <select id="queryEffectiveMemberForCq"
          resultType="com.extracme.evcard.membership.core.dto.MembershipBasicInfo">
    SELECT
      m.pk_id AS pkId,
      m.AUTH_ID AS authId,
      m.MOBILE_PHONE AS mobilePhone,
      m.`NAME` AS 'name',
      m.DRIVER_CODE AS driverCode,
      m.REG_TIME AS regTime,
      m.CREATED_TIME AS createdTime,
      m.UPDATED_TIME AS updatedTime,
      t4.AREAID as areaOfOrigin
    FROM
      ${siacSchema}.membership_info m
        LEFT JOIN ${siacSchema}.CITY t2 ON m.CITY_OF_ORIGIN = t2.CITY
        LEFT JOIN ${isvSchema}.ORG_INFO t3 ON t2.ORG_ID = t3.ORG_ID
        LEFT JOIN ${siacSchema}.area t4 ON t4.area = m.area_of_origin AND t4.FATHERID = '500100'
    WHERE
      m.`REVIEW_STATUS` = 1
      AND m.`authentication_status` = 2
      AND t3.org_id = #{orgId}
      AND m.MEMBERSHIP_TYPE = 0
      AND m.CREATED_TIME >= #{createdTime}
      AND m.UPDATED_TIME >= #{updatedTime}
  </select>


  <update id="updateReviewStatusByAuthId" parameterType="com.extracme.evcard.membership.credit.model.MembershipInfo" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 04 15:27:31 CST 2017.
    -->
    update ${siacSchema}.membership_info
    <set>
      <if test="reviewStatus != null" >
        REVIEW_STATUS = #{reviewStatus,jdbcType=DECIMAL},
      </if>
      <if test="authenticationStatus != null">
        authentication_status = #{authenticationStatus,jdbcType=INTEGER},
      </if>
      <if test="licenseElementsAuthStatus != null">
        license_elements_auth_status = #{licenseElementsAuthStatus,jdbcType=INTEGER},
      </if>
      <if test="licenseAuthStatus != null">
        license_auth_status = #{licenseAuthStatus,jdbcType=INTEGER},
      </if>
      <if test="licenseStatusMsg != null">
        license_status_msg = #{licenseStatusMsg,jdbcType=VARCHAR},
      </if>
      <if test="elementsReviewItems != null">
        elements_review_items = #{elementsReviewItems,jdbcType=VARCHAR},
      </if>
      <if test="updatedTime != null" >
        UPDATED_TIME = #{updatedTime,jdbcType=VARCHAR},
      </if>
      <if test="updatedUser != null" >
        UPDATED_USER = #{updatedUser,jdbcType=VARCHAR},
      </if>
      <if test="reviewUser != null" >
        REVIEW_USER = #{reviewUser,jdbcType=VARCHAR},
      </if>
      <if test="reviewItems != null" >
        REVIEW_ITEMS = #{reviewItems,jdbcType=VARCHAR},
      </if>
      <if test="reviewReason != null" >
        REVIEW_REASON = #{reviewReason,jdbcType=VARCHAR},
      </if>
       <if test="reviewTime != null" >
        REVIEW_TIME = #{reviewTime,jdbcType=VARCHAR},
      </if>
      <if test="appReviewTime != null" >
        APP_REVIEW_TIME = #{appReviewTime,jdbcType=VARCHAR},
      </if>
      <if test="reviewRemark != null" >
        REVIEW_REMARK = #{reviewRemark,jdbcType=VARCHAR},
      </if>
      <if test="reviewItemIds != null" >
        REVIEW_ITEM_IDS = #{reviewItemIds,jdbcType=VARCHAR},
      </if>
      <if test="reviewItemName != null" >
        REVIEW_ITEM_NAME = #{reviewItemName,jdbcType=VARCHAR},
      </if>
      <if test="reviewMode != null" >
        REVIEW_MODE = #{reviewMode,jdbcType=INTEGER},
      </if>
    </set>

    where 1 = 1
    <if test="pkId != null" >
      AND PK_ID = #{pkId,jdbcType=BIGINT}
    </if>
    <if test="authId != null" >
      AND auth_id = #{authId,jdbcType=VARCHAR}
    </if>
    and membership_type = #{membershipType}
  </update>

  <update id="updateUserFileNoAndStatus">
    update ${siacSchema}.membership_info
    <set>
        file_no = #{fileNo},
        file_no_img_url = #{fileNoImgUrl},
      <if test="reviewStatus != null" >
        REVIEW_STATUS = #{reviewStatus,jdbcType=DECIMAL},
      </if>
      <if test="authenticationStatus != null">
        authentication_status = #{authenticationStatus,jdbcType=INTEGER},
      </if>
      <if test="licenseElementsAuthStatus != null">
        license_elements_auth_status = #{licenseElementsAuthStatus,jdbcType=INTEGER},
      </if>
      <if test="licenseAuthStatus != null">
        license_auth_status = #{licenseAuthStatus,jdbcType=INTEGER},
      </if>
      <if test="licenseStatusMsg != null">
        license_status_msg = #{licenseStatusMsg,jdbcType=VARCHAR},
      </if>
      <if test="elementsReviewItems != null">
        elements_review_items = #{elementsReviewItems,jdbcType=VARCHAR},
      </if>
      <if test="updatedTime != null" >
        UPDATED_TIME = #{updatedTime,jdbcType=VARCHAR},
      </if>
      <if test="updatedUser != null" >
        UPDATED_USER = #{updatedUser,jdbcType=VARCHAR},
      </if>
      <if test="reviewUser != null" >
        REVIEW_USER = #{reviewUser,jdbcType=VARCHAR},
      </if>
      <if test="reviewItems != null" >
        REVIEW_ITEMS = #{reviewItems,jdbcType=VARCHAR},
      </if>
      <if test="reviewReason != null" >
        REVIEW_REASON = #{reviewReason,jdbcType=VARCHAR},
      </if>
      <if test="reviewTime != null" >
        REVIEW_TIME = #{reviewTime,jdbcType=VARCHAR},
      </if>
      <if test="appReviewTime != null" >
        APP_REVIEW_TIME = #{appReviewTime,jdbcType=VARCHAR},
      </if>
      <if test="reviewRemark != null" >
        REVIEW_REMARK = #{reviewRemark,jdbcType=VARCHAR},
      </if>
      <if test="reviewItemIds != null" >
        REVIEW_ITEM_IDS = #{reviewItemIds,jdbcType=VARCHAR},
      </if>
      <if test="reviewItemName != null" >
        REVIEW_ITEM_NAME = #{reviewItemName,jdbcType=VARCHAR},
      </if>
      <if test="reviewMode != null" >
        REVIEW_MODE = #{reviewMode,jdbcType=INTEGER},
      </if>
    </set>
    where auth_id = #{authId,jdbcType=VARCHAR}
    and membership_type = 0
    AND ACCOUNT_STATUS = 0
    </update>


   <update id="updateFromAdditionalInfoByAuthId">
		UPDATE ${siacSchema}.membership_info
			SET
			    driving_license_img_url = #{drivingLicenseImgUrl},
			    obtain_driver_timer = #{obtainDriverTimer},
			    license_expiration_time = #{licenseExpirationTime},
			    file_no = #{fileNo},
			    file_no_img_url = #{fileNoImgUrl}
			WHERE AUTH_ID =  #{authId} and membership_type = 0
   </update>


  <select id="getUserOrgInfo"
          resultType="com.extracme.evcard.membership.core.dto.UserOrgInfoDto">
    SELECT
      m.pk_id AS pkId,
      m.CITY_OF_ORIGIN as cityOfOrigin,
      c.city as city,
      c.cityId as cityId,
      o.id as orgSeq,
      o.org_id as orgId,
      o.org_name as orgName,
      m.agency_id as agencyId,
      a.agency_name as agencyName
    FROM ${siacSchema}.membership_info m
        LEFT JOIN ${siacSchema}.CITY c ON m.CITY_OF_ORIGIN = c.CITY
        LEFT JOIN ${isvSchema}.ORG_INFO o ON c.ORG_ID = o.ORG_ID
        LEFT JOIN ${siacSchema}.agency_info a ON m.agency_id = a.agency_id
    WHERE
      m.auth_id = #{authId}
      AND m.membership_type = #{memberType}
      AND m.account_status != 2
  </select>

  <!-- 新增会员 -->
  <insert id="addMembershipInfoForMmp"  useGeneratedKeys="true" keyProperty="pkId"  parameterType="com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs">
    insert into ${siacSchema}.membership_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="authId!=null">AUTH_ID,</if>
      <if test="mid!=null">MID,</if>
      <if test="driverCode!=null">DRIVER_CODE,</if>
      <if test="openId!=null">OPEN_ID,</if>
      <if test="name!=null">NAME,</if>
      <if test="password!=null">PASSWORD,</if>
      <if test="gender!=null">GENDER,</if>
      <if test="mobilePhone!=null">MOBILE_PHONE,</if>
      <if test="mail!=null">MAIL,</if>
      <if test="birthDate!=null">BIRTH_DATE,</if>
      <if test="regTime!=null">REG_TIME,</if>
      <if test="zip!=null">ZIP,</if>
      <if test="address!=null">ADDRESS,</if>
      <if test="province!=null">PROVINCE,</if>
      <if test="city!=null"> CITY,</if>
      <if test="area!=null">AREA,</if>
      <if test="membershipType!=null">MEMBERSHIP_TYPE,</if>
      <if test="userLevel!=null">USER_LEVEL,</if>
      <if test="orgId!=null">ORG_ID,</if>
      <if test="agencyId!=null">AGENCY_ID,</if>
      <if test="authKind!=null">AUTH_KIND,</if>
      <if test="certificateValidity!=null">CERTIFICATE_VALIDITY,</if>
      <if test="certificateAddress!=null">CERTIFICATE_ADDRESS,</if>
      <if test="obtainDriverTimer!=null">OBTAIN_DRIVER_TIMER,</if>
      <if test="licenseExpirationTime!=null">LICENSE_EXPIRATION_TIME,</if>
      <if test="userImgUrl!=null">USER_IMG_URL,</if>
      <if test="drivingLicenseImgUrl!=null">DRIVING_LICENSE_IMG_URL,</if>
      <if test="identityCardImgUrl!=null">IDENTITY_CARD_IMG_URL,</if>
      <if test="emergencyContact!=null">EMERGENCY_CONTACT,</if>
      <if test="emergencyMobil!=null">EMERGENCY_MOBIL,</if>
      <if test="guaranteeName!=null">GUARANTEE_NAME,</if>
      <if test="guaranteeIcCardKind!=null">GUARANTEE_IC_CARD_KIND,</if>
      <if test="guaranteeIcCard!=null">GUARANTEE_IC_CARD,</if>
      <if test="guaranteeMobil!=null">GUARANTEE_MOBIL,</if>
      <if test="reviewReason!=null">REVIEW_REASON,</if>
      <if test="reviewStatus!=null">REVIEW_STATUS,</if>
      <if test="cardNo!=null">CARD_NO,</if>
      <if test="creditNo!=null">CREDIT_NO,</if>
      <if test="dataOrigin!=null">DATA_ORIGIN,</if>
      <if test="id!=null">ID,</if>
      <if test="status!=null">STATUS,</if>
      <if test="blacklistReason!=null">BLACKLIST_REASON,</if>
      <if test="reviewUser!=null">REVIEW_USER,</if>
      <if test="reviewItems!=null">REVIEW_ITEMS,</if>
      <if test="illegalMethod!=null">ILLEGAL_METHOD,</if>
      <if test="deposit!=null">DEPOSIT,</if>
      <if test="reserveAmount!=null">RESERVE_AMOUNT,</if>
      <if test="rentMins!=null">RENT_MINS,</if>
      <if test="exemptDeposit!=null">EXEMPT_DEPOSIT,</if>
      <if test="empno!=null">EMPNO,</if>
      <if test="infoOrigin!=null">INFO_ORIGIN,</if>
      <if test="createdTime!=null">CREATED_TIME,</if>
      <if test="createdUser!=null">CREATED_USER,</if>
      <if test="updatedTime!=null">UPDATED_TIME,</if>
      <if test="updatedUser!=null">UPDATED_USER,</if>
      <if test="personnelState!=null">PERSONNEL_STATE,</if>
      <if test="shareUid!=null">SHARE_UID,</if>
      <if test="ezbike!=null">EZBIKE,</if>
      <if test="applyStatus!=null">APPLY_STATUS,</if>
      <if test="mark!=null"> MARK,</if>
      <if test="serviceVer!=null">SERVICE_VER,</if>
      <if test="serviceVerTime!=null">SERVICE_VER_TIME,</if>
      <if test="appKey!=null">APP_KEY,</if>
      <if test="typeFlag!=null">TYPE_FLAG,</if>
      <if test="invoicedAmount!=null">INVOICED_AMOUNT,</if>
      <if test="channelId!=null"> CHANNEL_ID,</if>
      <if test="regionid!=null">REGIONID,</if>
      <if test="depositVehicle!=null">deposit_vehicle,</if>
      <if test="provinceOfOrigin!=null">province_of_origin,</if>
      <if test="cityOfOrigin!=null">city_of_origin,</if>
      <if test="areaOfOrigin!=null">area_of_origin,</if>
      <if test="idcardPicUrl!=null">idcard_pic_url,</if>
      <if test="holdIdcardPicUrl!=null">hold_idcard_pic_url,</if>
      <if test="point!=null">point,</if>
      <if test="reviewTime!=null">REVIEW_TIME,</if>
      <if test="reviewRemark!=null">REVIEW_REMARK,</if>
      <if test="drivingLicenseType!=null">driving_license_type,</if>
      <if test="national!=null">national,</if>
      <if test="authenticationStatus!=null">authentication_status,</if>
      <if test="needFace!=null">need_face,</if>
      <if test="driverCode!=null">id_card_number,</if>
      <!--1.25-->
      <if test="faceRecognitionImgUrl!=null and faceRecognitionImgUrl!=''">FACE_RECOGNITION_IMG_URL,</if>
      <if test="passportNo!=null and passportNo!=''">passport_no,</if>
      <if test="fileNo!=null and fileNo!=''">file_no,</if>
      <if test="fileNoImgUrl!=null and fileNoImgUrl!=''">file_no_img_url,</if>
      <if test="licenseReviewStatus!=null and licenseReviewStatus!=''">license_review_status,</if>
      <if test="manufacturer != null">manufacturer,</if>
      <if test="idType!=null and idType !='' ">id_type</if>
      <!--驾照三要素-->
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="authId!=null">#{authId,jdbcType=VARCHAR},</if>
      <if test="mid!=null">#{mid,jdbcType=VARCHAR},</if>
      <if test="driverCode!=null">#{driverCode,jdbcType=VARCHAR},</if>
      <if test="openId!=null">#{openId,jdbcType=VARCHAR},</if>
      <if test="name!=null">#{name,jdbcType=VARCHAR},</if>
      <if test="password!=null">#{password,jdbcType=VARCHAR},</if>
      <if test="gender!=null">#{gender,jdbcType=VARCHAR},</if>
      <if test="mobilePhone!=null">#{mobilePhone,jdbcType=VARCHAR},</if>
      <if test="mail!=null">#{mail,jdbcType=VARCHAR},</if>
      <if test="birthDate!=null">#{birthDate,jdbcType=VARCHAR},</if>
      <if test="regTime!=null">#{regTime,jdbcType=VARCHAR},</if>
      <if test="zip!=null">#{zip,jdbcType=VARCHAR},</if>
      <if test="address!=null">#{address,jdbcType=VARCHAR},</if>
      <if test="province!=null">#{province,jdbcType=VARCHAR},</if>
      <if test="city!=null">#{city,jdbcType=VARCHAR},</if>
      <if test="area!=null">#{area,jdbcType=VARCHAR},</if>
      <if test="membershipType!=null">#{membershipType,jdbcType=DECIMAL},</if>
      <if test="userLevel!=null">#{userLevel,jdbcType=DECIMAL},</if>
      <if test="orgId!=null">#{orgId,jdbcType=VARCHAR},</if>
      <if test="agencyId!=null">#{agencyId,jdbcType=VARCHAR},</if>
      <if test="authKind!=null">#{authKind,jdbcType=VARCHAR},</if>
      <if test="certificateValidity!=null">#{certificateValidity,jdbcType=VARCHAR},</if>
      <if test="certificateAddress!=null">#{certificateAddress,jdbcType=VARCHAR},</if>
      <if test="obtainDriverTimer!=null">#{obtainDriverTimer,jdbcType=VARCHAR},</if>
      <if test="licenseExpirationTime!=null">#{licenseExpirationTime,jdbcType=VARCHAR},</if>
      <if test="userImgUrl!=null">#{userImgUrl,jdbcType=LONGVARCHAR},</if>
      <if test="drivingLicenseImgUrl!=null">#{drivingLicenseImgUrl,jdbcType=LONGVARCHAR},</if>
      <if test="identityCardImgUrl!=null">#{identityCardImgUrl,jdbcType=LONGVARCHAR},</if>
      <if test="emergencyContact!=null">#{emergencyContact,jdbcType=VARCHAR},</if>
      <if test="emergencyMobil!=null">#{emergencyMobil,jdbcType=VARCHAR},</if>
      <if test="guaranteeName!=null">#{guaranteeName,jdbcType=VARCHAR},</if>
      <if test="guaranteeIcCardKind!=null">#{guaranteeIcCardKind,jdbcType=DECIMAL},</if>
      <if test="guaranteeIcCard!=null">#{guaranteeIcCard,jdbcType=VARCHAR},</if>
      <if test="guaranteeMobil!=null">#{guaranteeMobil,jdbcType=VARCHAR},</if>
      <if test="reviewReason!=null">#{reviewReason,jdbcType=VARCHAR},</if>
      <if test="reviewStatus!=null">#{reviewStatus,jdbcType=DECIMAL},</if>
      <if test="cardNo!=null">#{cardNo,jdbcType=VARCHAR},</if>
      <if test="creditNo!=null">#{creditNo,jdbcType=VARCHAR},</if>
      <if test="dataOrigin!=null">#{dataOrigin,jdbcType=DECIMAL},</if>
      <if test="id!=null">#{id,jdbcType=DECIMAL},</if>
      <if test="status!=null">#{status,jdbcType=DECIMAL},</if>
      <if test="blacklistReason!=null">#{blacklistReason,jdbcType=VARCHAR},</if>
      <if test="reviewUser!=null">#{reviewUser,jdbcType=VARCHAR},</if>
      <if test="reviewItems!=null">#{reviewItems,jdbcType=VARCHAR},</if>
      <if test="illegalMethod!=null">#{illegalMethod,jdbcType=DECIMAL},</if>
      <if test="deposit!=null">#{deposit,jdbcType=DECIMAL},</if>
      <if test="reserveAmount!=null">#{reserveAmount,jdbcType=DECIMAL},</if>
      <if test="rentMins!=null">#{rentMins,jdbcType=DECIMAL},</if>
      <if test="exemptDeposit!=null">#{exemptDeposit,jdbcType=DECIMAL},</if>
      <if test="empno!=null">#{empno,jdbcType=VARCHAR},</if>
      <if test="infoOrigin!=null">#{infoOrigin,jdbcType=VARCHAR},</if>
      <if test="createdTime!=null">#{createdTime,jdbcType=VARCHAR},</if>
      <if test="createdUser!=null">#{createdUser,jdbcType=VARCHAR},</if>
      <if test="updatedTime!=null">#{updatedTime,jdbcType=VARCHAR},</if>
      <if test="updatedUser!=null">#{updatedUser,jdbcType=VARCHAR},</if>
      <if test="personnelState!=null">#{personnelState,jdbcType=DECIMAL},</if>
      <if test="shareUid!=null">#{shareUid,jdbcType=VARCHAR},</if>
      <if test="ezbike!=null">#{ezbike,jdbcType=DECIMAL},</if>
      <if test="applyStatus!=null">#{applyStatus,jdbcType=DECIMAL},</if>
      <if test="mark!=null">#{mark,jdbcType=LONGVARCHAR},</if>
      <if test="serviceVer!=null">#{serviceVer,jdbcType=VARCHAR},</if>
      <if test="serviceVerTime!=null">#{serviceVerTime,jdbcType=VARCHAR},</if>
      <if test="appKey!=null">#{appKey,jdbcType=VARCHAR},</if>
      <if test="typeFlag!=null">#{typeFlag,jdbcType=DECIMAL},</if>
      <if test="invoicedAmount!=null">#{invoicedAmount,jdbcType=DECIMAL},</if>
      <if test="channelId!=null">#{channelId,jdbcType=VARCHAR},</if>
      <if test="regionid!=null">#{regionid,jdbcType=BIGINT},</if>
      <if test="depositVehicle!=null">#{depositVehicle,jdbcType=DECIMAL},</if>
      <if test="provinceOfOrigin!=null">#{provinceOfOrigin,jdbcType=VARCHAR},</if>
      <if test="cityOfOrigin!=null">#{cityOfOrigin,jdbcType=VARCHAR},</if>
      <if test="areaOfOrigin!=null">#{areaOfOrigin,jdbcType=VARCHAR},</if>
      <if test="idcardPicUrl!=null">#{idcardPicUrl,jdbcType=VARCHAR},</if>
      <if test="holdIdcardPicUrl!=null">#{holdIdcardPicUrl,jdbcType=VARCHAR},</if>
      <if test="point!=null">#{point,jdbcType=INTEGER},</if>
      <if test="reviewTime!=null">#{reviewTime,jdbcType=VARCHAR},</if>
      <if test="reviewRemark!=null">#{reviewRemark,jdbcType=VARCHAR},</if>
      <if test="drivingLicenseType!=null">#{drivingLicenseType,jdbcType=VARCHAR},</if>
      <if test="national!=null">#{national,jdbcType=VARCHAR},</if>
      <if test="authenticationStatus!=null">#{authenticationStatus,jdbcType=INTEGER},</if>
      <if test="needFace!=null">#{needFace,jdbcType=INTEGER},</if>
      <if test="driverCode!=null">#{driverCode,jdbcType=VARCHAR},</if>
      <!--1.25-->
      <if test="faceRecognitionImgUrl!=null and faceRecognitionImgUrl!=''">#{faceRecognitionImgUrl,jdbcType=VARCHAR},</if>
      <if test="passportNo!=null and passportNo!=''">#{passportNo, jdbcType=VARCHAR},</if>
      <if test="fileNo!=null and fileNo!=''">#{fileNo, jdbcType=VARCHAR},</if>
      <if test="fileNoImgUrl!=null and fileNoImgUrl!=''">#{fileNoImgUrl, jdbcType=VARCHAR},</if>
      <if test="licenseReviewStatus!=null and licenseReviewStatus!=''">#{licenseReviewStatus, jdbcType=INTEGER},</if>
      <if test="manufacturer != null">#{manufacturer, jdbcType=VARCHAR},</if>
      <if test="idType!=null and idType!= ''">#{idType, jdbcType=INTEGER}</if>
      <!--驾照三要素-->
    </trim>
  </insert>

  <!-- 更新用户审核状态 -->
  <update id="updateDrivingLicenceReviewStatus">
    UPDATE ${siacSchema}.MEMBERSHIP_INFO
    SET
    <if test="operatorUserName != null" >
    REVIEW_USER = #{operatorUserName,jdbcType=VARCHAR},
    </if>
    <if test="reviewTime != null" >
    REVIEW_TIME = #{reviewTime,jdbcType=VARCHAR},
    </if>
    <if test="reviewTime != null" >
    UPDATED_TIME = #{reviewTime,jdbcType=VARCHAR},
    </if>
    <if test="firstAuthTime != null" >
      license_first_auth_time = #{firstAuthTime,jdbcType=TIMESTAMP},
    </if>
    <if test="operatorUserName != null" >
    UPDATED_USER = #{operatorUserName,jdbcType=VARCHAR},
    </if>
    <if test="failReason != null" >
    REVIEW_REMARK = #{failReason,jdbcType=VARCHAR},
    </if>
    <if test="reviewItems != null" >
    REVIEW_ITEMS = #{reviewItems,jdbcType=VARCHAR},
    </if>
    <if test="authStatus != null" >
      license_auth_status = #{authStatus,jdbcType=DECIMAL},
    </if>
    <if test="auditStatus != null" >
      license_review_status = #{auditStatus,jdbcType=DECIMAL}
    </if>
    where mid = #{mid,jdbcType=VARCHAR}
  </update>

  <!-- 检验该用户是否制作过卡 -->
  <select id="checkHavecard" parameterType="string" resultType="integer">
		SELECT COUNT(*) FROM ${siacSchema}.MEMBERSHIP_INFO m
		left join ${siacSchema}.CARD_INFO c on m.CARD_NO=c.CARD_NO
		WHERE
		m.AUTH_ID = #{authId}
		and m.MEMBERSHIP_TYPE=0
		and (m.CARD_NO is null or c.STATUS=2 or m.CARD_NO ='')
	</select>


    <!-- 检验该用户是否 已完成认证 1：代表 该用户已经首次认证过了 0，代表该用户 未首次认证过-->
    <select id="checkUserHavaAuth"  resultType="integer">
		SELECT COUNT(*) FROM ${siacSchema}.MEMBERSHIP_INFO
		WHERE
		AUTH_ID = #{authId}
		and MEMBERSHIP_TYPE=0
        <![CDATA[ AND license_first_auth_time < #{endTime} ]]>
	</select>

  <!-- 易观埋点，根据用户authid 查询状态-->
  <select id="selectByAuthIdsForYG" resultMap="memberBaseInfoMap">
    SELECT
    a.pk_id,a.mid,a.AUTH_ID,a.UID,b.authentication_status as authentication_status_new,a.license_review_status,a.REVIEW_STATUS
    from ${siacSchema}.membership_info a left join ${siacSchema}.member_identity_document b on a.identity_id = b.id
    where 1 =1
    AND a.auth_id in
    <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
    and a.MEMBERSHIP_TYPE = 0
  </select>

  <select id="getOrderCount"  resultType="integer">
		SELECT COUNT(1) FROM
        ${siacSchema}.order_info
        WHERE
        AUTH_ID = #{authId}
  </select>

  <select id="getOssUrlByVersion" resultType="java.lang.String">
    select provision_address from ${issSchema}.mmp_provision_info
    where provision_type=#{provisionType}
    and provision_status=2 and `status`=1 and version=#{version}
  </select>

  <select id="getMemberList" resultMap="memberBaseInfoMap">
    select <include refid="MembershipBasicInfo_Column_List"/>
    FROM ${siacSchema}.membership_info
    where 1 = 1
    <if test="secondAppKey != null" >
      AND second_app_key = #{secondAppKey,jdbcType=VARCHAR}
    </if>
    <if test="mobilePhone != null" >
      AND MOBILE_PHONE = #{mobilePhone,jdbcType=VARCHAR}
    </if>
    <if test="secondAppKey != null" >
      AND second_app_key = #{secondAppKey,jdbcType=VARCHAR}
    </if>
    <if test="passport != null" >
      AND passport_no = #{passport,jdbcType=VARCHAR}
    </if>
    <if test="memberType != null" >
      AND membership_type = #{memberType,jdbcType=INTEGER}
    </if>
    AND ACCOUNT_STATUS = 0
  </select>

  <!-- 批量查询渠道会员 -->
  <select id="getChannelMember" resultMap="BaseResultMap">
    select pk_id,MOBILE_PHONE
    from ${siacSchema}.membership_info
    where membership_type = 2
    and pk_id > #{id}
    and ACCOUNT_STATUS != 2
    order by pk_id asc
    limit #{limit}
  </select>

  <!-- 清除身份信息 -->
  <update id="clearIdentityByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs" >
    update ${siacSchema}.membership_info
    <set>
      <if test="name != null" >
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="updatedTime != null" >
        UPDATED_TIME = #{updatedTime,jdbcType=VARCHAR},
      </if>
      <if test="updatedUser != null" >
        UPDATED_USER = #{updatedUser,jdbcType=VARCHAR},
      </if>
      <if test="idcardPicUrl != null" >
        idcard_pic_url = #{idcardPicUrl,jdbcType=VARCHAR},
      </if>
      <if test="reviewUser != null" >
        REVIEW_USER = #{reviewUser,jdbcType=VARCHAR},
      </if>
      <if test="reviewTime != null" >
        REVIEW_TIME = #{reviewTime,jdbcType=VARCHAR},
      </if>
      <if test="reviewItemIds != null" >
        REVIEW_ITEM_IDS = #{reviewItemIds,jdbcType=VARCHAR},
      </if>
      <if test="faceRecognitionImgUrl != null" >
        FACE_RECOGNITION_IMG_URL = #{faceRecognitionImgUrl,jdbcType=VARCHAR},
      </if>

      <if test="userImgUrl != null" >
        USER_IMG_URL = #{userImgUrl,jdbcType=LONGVARCHAR},
      </if>
      <if test="identityCardImgUrl != null" >
        IDENTITY_CARD_IMG_URL = #{identityCardImgUrl,jdbcType=LONGVARCHAR},
      </if>

      <if test="authenticationStatus != null">
        authentication_status = #{authenticationStatus,jdbcType=INTEGER},
      </if>
      <if test="passportNo != null">
        passport_no = #{passportNo,jdbcType=VARCHAR},
      </if>
      <if test="idType != null">
        id_type = #{idType,jdbcType=INTEGER},
      </if>
      <if test="idCardNumber != null">
        id_card_number = #{idCardNumber},
      </if>
    </set>
    where pk_id = #{pkId,jdbcType=BIGINT}
  </update>
  <!-- 清除驾照信息 -->
  <update id="clearLicenseByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs" >
    update ${siacSchema}.membership_info
    <set>
      <if test="driverCode != null" >
        DRIVER_CODE = #{driverCode,jdbcType=VARCHAR},
      </if>
      <if test="fileNo != null">
        file_no = #{fileNo,jdbcType=VARCHAR},
      </if>
      <if test="drivingLicenseImgUrl != null" >
        DRIVING_LICENSE_IMG_URL = #{drivingLicenseImgUrl,jdbcType=LONGVARCHAR},
      </if>
      <if test="fileNoImgUrl != null">
        file_no_img_url = #{fileNoImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="licenseExpirationTime != null" >
        LICENSE_EXPIRATION_TIME = #{licenseExpirationTime,jdbcType=VARCHAR},
      </if>
      <if test="obtainDriverTimer != null" >
        OBTAIN_DRIVER_TIMER = #{obtainDriverTimer,jdbcType=VARCHAR},
      </if>
      <if test="reviewUser != null" >
        REVIEW_USER = #{reviewUser,jdbcType=VARCHAR},
      </if>
      <if test="reviewItems != null" >
        REVIEW_ITEMS = #{reviewItems,jdbcType=VARCHAR},
      </if>
      <if test="licenseReviewStatus != null">
        license_review_status = #{licenseReviewStatus,jdbcType=INTEGER},
      </if>
      <if test="updatedTime != null" >
        UPDATED_TIME = #{updatedTime,jdbcType=VARCHAR},
      </if>
      <if test="updatedUser != null" >
        UPDATED_USER = #{updatedUser,jdbcType=VARCHAR},
      </if>



      <if test="drivingLicense != null" >
        DRIVING_LICENSE = #{drivingLicense,jdbcType=VARCHAR},
      </if>
      <if test="reviewReason != null" >
        REVIEW_REASON = #{reviewReason,jdbcType=VARCHAR},
      </if>
      <if test="reviewStatus != null" >
        REVIEW_STATUS = #{reviewStatus,jdbcType=DECIMAL},
      </if>
      <if test="reviewTime != null" >
        REVIEW_TIME = #{reviewTime,jdbcType=VARCHAR},
      </if>
      <if test="appReviewTime != null" >
        APP_REVIEW_TIME = #{appReviewTime,jdbcType=VARCHAR},
      </if>
      <if test="reviewItemIds != null" >
        REVIEW_ITEM_IDS = #{reviewItemIds,jdbcType=VARCHAR},
      </if>
      <if test="reviewMode != null" >
        REVIEW_MODE = #{reviewMode,jdbcType=INTEGER},
      </if>
      <if test="drivingLicenseType != null">
        driving_license_type = #{drivingLicenseType,jdbcType=INTEGER},
      </if>
      <if test="userImgUrl != null" >
        USER_IMG_URL = #{userImgUrl,jdbcType=LONGVARCHAR},
      </if>
      <if test="identityCardImgUrl != null" >
        IDENTITY_CARD_IMG_URL = #{identityCardImgUrl,jdbcType=LONGVARCHAR},
      </if>
      <if test="driverLicenseInputType != null">
        driver_license_input_type = #{driverLicenseInputType,jdbcType=INTEGER},
      </if>
      <if test="licenseElementsAuthStatus != null">
        license_elements_auth_status = #{licenseElementsAuthStatus,jdbcType=INTEGER},
      </if>
      <if test="elementsReviewItems != null">
        elements_review_items = #{elementsReviewItems,jdbcType=VARCHAR},
      </if>
      <if test="licenseAuthStatus != null">
        license_auth_status = #{licenseAuthStatus,jdbcType=INTEGER},
      </if>
      <if test="licenseStatusMsg != null">
        license_status_msg = #{licenseStatusMsg,jdbcType=VARCHAR},
      </if>
      <if test="licenseFirstAuthTime != null">
        license_first_auth_time = #{licenseFirstAuthTime,jdbcType=TIMESTAMP},
      </if>
      <if test="licenseSubmitAppkey != null">
        license_submit_appkey = #{licenseSubmitAppkey,jdbcType=VARCHAR},
      </if>
      <if test="licenseImgType != null">
        license_img_type = #{licenseImgType,jdbcType=INTEGER},
      </if>
    </set>
    where pk_id = #{pkId,jdbcType=BIGINT}
  </update>
  <update id="updateFictional" parameterType="com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs">
    update ${siacSchema}.membership_info set is_fictional = #{isFictional,jdbcType=INTEGER} WHERE pk_id = #{pkId,jdbcType=BIGINT}
  </update>
  <update id="updateSecondAppKey" parameterType="com.extracme.evcard.membership.credit.model.MembershipInfo">
    update ${siacSchema}.membership_info
    <set>
      <if test="appKey != null" >
        APP_KEY = #{appKey,jdbcType=VARCHAR},
      </if>
      <if test="secondAppKey != null and secondAppKey !=''">
        second_app_key = #{secondAppKey,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=BIGINT}
      </if>
    </set>
    WHERE AUTH_ID = #{authId,jdbcType=VARCHAR}
  </update>

</mapper>