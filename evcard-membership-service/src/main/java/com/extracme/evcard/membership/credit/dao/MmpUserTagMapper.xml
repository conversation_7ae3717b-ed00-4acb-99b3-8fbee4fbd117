<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.credit.dao.MmpUserTagMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.credit.model.MmpUserTag">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="auth_id" jdbcType="VARCHAR" property="authId" />
    <result column="real_amount" jdbcType="DECIMAL" property="realAmount" />
    <result column="effective_contd" jdbcType="DECIMAL" property="effectiveContd" />
    <result column="credit_amount" jdbcType="DOUBLE" property="creditAmount" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_oper_id" jdbcType="BIGINT" property="createOperId" />
    <result column="create_oper_name" jdbcType="VARCHAR" property="createOperName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_oper_id" jdbcType="BIGINT" property="updateOperId" />
    <result column="update_oper_name" jdbcType="VARCHAR" property="updateOperName" />
    <result column="spare1" jdbcType="VARCHAR" property="spare1" />
    <result column="spare2" jdbcType="VARCHAR" property="spare2" />
    <result column="spare3" jdbcType="VARCHAR" property="spare3" />
    <result column="spare4" jdbcType="VARCHAR" property="spare4" />
    <result column="spare5" jdbcType="VARCHAR" property="spare5" />
    <result column="spare6" jdbcType="VARCHAR" property="spare6" />
    <result column="spare7" jdbcType="VARCHAR" property="spare7" />
    <result column="spare8" jdbcType="VARCHAR" property="spare8" />
    <result column="spare9" jdbcType="VARCHAR" property="spare9" />
    <result column="spare10" jdbcType="VARCHAR" property="spare10" />
    <result column="shop_limit" jdbcType="VARCHAR" property="shopLimit" />
    <result column="profession" property="profession" jdbcType="INTEGER" />
    <result column="educational" property="educational" jdbcType="INTEGER" />
    <result column="ownCar" property="ownCar" jdbcType="INTEGER" />
    <result column="student_card_url" property="studentCardUrl" jdbcType="VARCHAR" />
    <result column="alipay_order_authority" property="alipayOrderAuthority" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List">
    id, auth_id, real_amount, effective_contd, credit_amount, remark, create_time, create_oper_id, 
    create_oper_name, update_time, update_oper_id, update_oper_name, spare1, spare2, 
    spare3, spare4, spare5, spare6, spare7, spare8, spare9, spare10, shop_limit,profession,educational,ownCar,student_card_url,alipay_order_authority,ali_open_id
  </sql>

  <update id="updateCreditAmountByAuthId">
    UPDATE  ${issSchema}.mmp_user_tag
    SET credit_amount = credit_amount + #{amount}
    WHERE auth_id = #{authId,jdbcType=VARCHAR}
  </update>

  <update id="updateCreditZeroAmountByAuthId">
    UPDATE  ${issSchema}.mmp_user_tag
    SET credit_amount = 0
    WHERE auth_id = #{authId,jdbcType=VARCHAR}
    AND credit_amount >= 0
  </update>

  <insert id="saveUserTag" useGeneratedKeys="true" keyProperty="id">
     insert into ${issSchema}.mmp_user_tag (auth_id, credit_amount)
     SELECT
      #{authId}, #{creditAmount}
     FROM dual
     WHERE
	 NOT EXISTS (
		SELECT  *  FROM ${issSchema}.mmp_user_tag
		WHERE auth_id = #{authId,jdbcType=VARCHAR} )
  </insert>
  
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.credit.model.MmpUserTag">
    insert into ${issSchema}.mmp_user_tag
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="authId != null">
        auth_id,
      </if>
      <if test="realAmount != null">
        real_amount,
      </if>
      <if test="effectiveContd != null">
        effective_contd,
      </if>
      <if test="creditAmount != null">
        credit_amount,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createOperId != null">
        create_oper_id,
      </if>
      <if test="createOperName != null">
        create_oper_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateOperId != null">
        update_oper_id,
      </if>
      <if test="updateOperName != null">
        update_oper_name,
      </if>
      <if test="spare1 != null">
        spare1,
      </if>
      <if test="spare2 != null">
        spare2,
      </if>
      <if test="spare3 != null">
        spare3,
      </if>
      <if test="spare4 != null">
        spare4,
      </if>
      <if test="spare5 != null">
        spare5,
      </if>
      <if test="spare6 != null">
        spare6,
      </if>
      <if test="spare7 != null">
        spare7,
      </if>
      <if test="spare8 != null">
        spare8,
      </if>
      <if test="spare9 != null">
        spare9,
      </if>
      <if test="spare10 != null">
        spare10,
      </if>
      <if test="shopLimit != null">
        shop_limit,
      </if>
      <if test="aliOpenId != null">
        ali_open_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="authId != null">
        #{authId,jdbcType=VARCHAR},
      </if>
      <if test="realAmount != null">
        #{realAmount,jdbcType=DECIMAL},
      </if>
      <if test="effectiveContd != null">
        #{effectiveContd,jdbcType=DECIMAL},
      </if>
      <if test="creditAmount != null">
        #{creditAmount,jdbcType=DOUBLE},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="spare1 != null">
        #{spare1,jdbcType=VARCHAR},
      </if>
      <if test="spare2 != null">
        #{spare2,jdbcType=VARCHAR},
      </if>
      <if test="spare3 != null">
        #{spare3,jdbcType=VARCHAR},
      </if>
      <if test="spare4 != null">
        #{spare4,jdbcType=VARCHAR},
      </if>
      <if test="spare5 != null">
        #{spare5,jdbcType=VARCHAR},
      </if>
      <if test="spare6 != null">
        #{spare6,jdbcType=VARCHAR},
      </if>
      <if test="spare7 != null">
        #{spare7,jdbcType=VARCHAR},
      </if>
      <if test="spare8 != null">
        #{spare8,jdbcType=VARCHAR},
      </if>
      <if test="spare9 != null">
        #{spare9,jdbcType=VARCHAR},
      </if>
      <if test="spare10 != null">
        #{spare10,jdbcType=VARCHAR},
      </if>
      <if test="shopLimit != null">
        #{shopLimit,jdbcType=VARCHAR},
      </if>
      <if test="aliOpenId != null">
        #{aliOpenId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateEffectiveContdByAuthId">
    UPDATE ${issSchema}.mmp_user_tag
    SET effective_contd = effective_contd + #{amount} - #{oldAmount}
    WHERE auth_id = #{authId,jdbcType=VARCHAR}
  </update>
	<!-- 根据会员id查询会员标签 -->
  <select id="selectMmpUserByAuthId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_user_tag
    where auth_id = #{authId,jdbcType=VARCHAR}
  </select>

  <!-- 根据会员id集合查询会员标签 -->
  <select id="selectMmpUserListByAuthIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_user_tag
    where auth_id  in
    <foreach item="item" collection="authIds" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>

 
  <!-- 根据会员的imei查询会员标签 -->
  <select id="selectByImei" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_user_tag
    where spare5 = #{imei,jdbcType=VARCHAR}
  </select>
  <!-- 修改会员标签 -->
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.credit.model.MmpUserTag">
    update ${issSchema}.mmp_user_tag
    <set>
      <if test="authId != null">
        auth_id = #{authId,jdbcType=VARCHAR},
      </if>
      <if test="realAmount != null">
        real_amount = #{realAmount,jdbcType=DECIMAL},
      </if>
      <if test="effectiveContd != null">
        effective_contd = #{effectiveContd,jdbcType=DECIMAL},
      </if>
      <if test="creditAmount != null">
        credit_amount = #{creditAmount,jdbcType=DOUBLE},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="spare1 != null">
        spare1 = #{spare1,jdbcType=VARCHAR},
      </if>
      <if test="spare2 != null">
        spare2 = #{spare2,jdbcType=VARCHAR},
      </if>
      <if test="spare3 != null">
        spare3 = #{spare3,jdbcType=VARCHAR},
      </if>
      <if test="spare4 != null">
        spare4 = #{spare4,jdbcType=VARCHAR},
      </if>
      <if test="spare5 != null">
        spare5 = #{spare5,jdbcType=VARCHAR},
      </if>
      <if test="spare6 != null">
        spare6 = #{spare6,jdbcType=VARCHAR},
      </if>
      <if test="spare7 != null">
        spare7 = #{spare7,jdbcType=VARCHAR},
      </if>
      <if test="spare8 != null">
        spare8 = #{spare8,jdbcType=VARCHAR},
      </if>
      <if test="spare9 != null">
        spare9 = #{spare9,jdbcType=VARCHAR},
      </if>
      <if test="spare10 != null">
        spare10 = #{spare10,jdbcType=VARCHAR},
      </if>
      <if test="shopLimit != null">
        shop_limit = #{shopLimit,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
    <update id="updateFaceSimilarity">
      update ${issSchema}.mmp_user_tag
      <set>
        <if test="spare6 != null">
          spare6 = #{spare6},
        </if>
        <if test="updateTime != null">
          update_time = #{updateTime},
        </if>
        <if test="updateOperName != null">
          update_oper_name = #{updateOperName},
        </if>
      </set>
      where auth_id =#{authId}
    </update>

  <update id="updateZfbIdByAuthId">
    update ${issSchema}.mmp_user_tag
    <set>
      <if test="spare7 != null and spare7 !=''">
        spare7 = #{spare7},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
      <if test="updateOperName != null">
        update_oper_name = #{updateOperName},
      </if>
      <if test="aliOpenId != null and aliOpenId !='' ">
        ali_open_id = #{aliOpenId,jdbcType=VARCHAR},
      </if>
    </set>
    where auth_id =#{authId}
  </update>

  <update id="updateByAuthId" parameterType="com.extracme.evcard.membership.credit.model.MmpUserTag">
    update ${issSchema}.mmp_user_tag
    <set>
      <if test="authId != null">
        auth_id = #{authId,jdbcType=VARCHAR},
      </if>
      <if test="realAmount != null">
        real_amount = #{realAmount,jdbcType=DECIMAL},
      </if>
      <if test="effectiveContd != null">
        effective_contd = #{effectiveContd,jdbcType=DECIMAL},
      </if>
      <if test="creditAmount != null">
        credit_amount = #{creditAmount,jdbcType=DOUBLE},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="spare1 != null">
        spare1 = #{spare1,jdbcType=VARCHAR},
      </if>
      <if test="spare2 != null">
        spare2 = #{spare2,jdbcType=VARCHAR},
      </if>
      <if test="spare3 != null">
        spare3 = #{spare3,jdbcType=VARCHAR},
      </if>
      <if test="spare4 != null">
        spare4 = #{spare4,jdbcType=VARCHAR},
      </if>
      <if test="spare5 != null">
        spare5 = #{spare5,jdbcType=VARCHAR},
      </if>
      <if test="spare6 != null">
        spare6 = #{spare6,jdbcType=VARCHAR},
      </if>
      <if test="spare7 != null">
        spare7 = #{spare7,jdbcType=VARCHAR},
      </if>
      <if test="spare8 != null">
        spare8 = #{spare8,jdbcType=VARCHAR},
      </if>
      <if test="spare9 != null">
        spare9 = #{spare9,jdbcType=VARCHAR},
      </if>
      <if test="spare10 != null">
        spare10 = #{spare10,jdbcType=VARCHAR},
      </if>
      <if test="aliOpenId != null">
        ali_open_id = #{aliOpenId,jdbcType=VARCHAR},
      </if>
      <if test="shopLimit != null">
        shop_limit = #{shopLimit,jdbcType=VARCHAR},
      </if>
      <if test="profession != null">
        profession = #{profession},
      </if>
      <if test="educational != null">
        educational = #{educational},
      </if>
      <if test="ownCar != null">
        ownCar = #{ownCar},
      </if>
      <if test="alipayOrderAuthority != null">
        alipay_order_authority = #{alipayOrderAuthority},
      </if>
    </set>
    where auth_id = #{authId}
  </update>

  <select id="queryWithHoldSignInfo" parameterType="string" resultType="string" >
    SELECT spare4  FROM ${issSchema}.mmp_user_tag
    WHERE auth_id = #{authId}
  </select>


  <update id="updateUserFirstOrderSeq">
    update ${issSchema}.mmp_user_tag
    set spare1 = #{orderSeq},
    update_time = CURRENT_TIMESTAMP (),
    update_oper_name = #{authId},
    remark = 'order'
    where auth_id = #{authId}
    and (spare1 is null or spare1 = '' or (spare1 not like 'C%' and spare1 not like 'D%'))
  </update>

  <update id="updateUserStudentCardUrl">
    update ${issSchema}.mmp_user_tag
    set student_card_url = #{studentCardUrl},
    update_time = CURRENT_TIMESTAMP (),
    update_oper_name = #{authId}
    where auth_id = #{authId}
  </update>

  <!-- 根据会员id集合查询会员标签 -->
  <select id="getSpare4NotEmptyList" resultMap="BaseResultMap">
    select t.auth_id,t.spare4
    from ${issSchema}.mmp_user_tag t left join ${siacSchema}.membership_info m on  t.auth_id = m.AUTH_ID
    where t.spare4 != ''
    and t.id > #{id}
    and m.ACCOUNT_STATUS != 2
    order by t.id asc
    limit #{limit}
  </select>

</mapper>