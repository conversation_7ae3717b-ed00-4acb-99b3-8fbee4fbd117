package com.extracme.evcard.membership.core.model;

import com.extracme.framework.core.model.Model;

import java.util.Date;

public class MmpDiscountRule extends Model {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_discount_rule.agency_id
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    private String agencyId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_discount_rule.discount_type
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    private Integer discountType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_discount_rule.discount_rate
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    private Double discountRate;

    private Double peakSeasonDiscountRate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_discount_rule.beneficiary_number
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    private Integer beneficiaryNumber;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_discount_rule.valid_start_time
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    private Date validStartTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_discount_rule.valid_end_time
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    private Date validEndTime;


    public Double getPeakSeasonDiscountRate() {
        return peakSeasonDiscountRate;
    }

    public void setPeakSeasonDiscountRate(Double peakSeasonDiscountRate) {
        this.peakSeasonDiscountRate = peakSeasonDiscountRate;
    }

    public String getAgencyId() {
        return agencyId;
    }

    public void setAgencyId(String agencyId) {
        this.agencyId = agencyId;
    }

    public Integer getDiscountType() {
        return discountType;
    }

    public void setDiscountType(Integer discountType) {
        this.discountType = discountType;
    }

    public Double getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(Double discountRate) {
        this.discountRate = discountRate;
    }

    public Integer getBeneficiaryNumber() {
        return beneficiaryNumber;
    }

    public void setBeneficiaryNumber(Integer beneficiaryNumber) {
        this.beneficiaryNumber = beneficiaryNumber;
    }

    public Date getValidStartTime() {
        return validStartTime;
    }

    public void setValidStartTime(Date validStartTime) {
        this.validStartTime = validStartTime;
    }

    public Date getValidEndTime() {
        return validEndTime;
    }

    public void setValidEndTime(Date validEndTime) {
        this.validEndTime = validEndTime;
    }
}
