package com.extracme.evcard.membership.core.dto.md;

import lombok.Data;

@Data
public class MdRentalContract {
    private String mid;   //  用户id
    private String contractId;  //  租车合同号
    private Integer contractStatus; //  合同状态 1:预订中(备车中) 2：已预约(待取车) 3：已取车 4：还车中(收车中) 5：已还车(待支付) 6：已支付 7:已取消
    private String pickUpOrgCode;   // 计划取车公司id（有实际取车取实际取车）
    private Long pickUpStoreId; //  计划取车门店id（有实际取车取实际取车）
    private Long pickUpShopSeq; //  计划取车虚拟门店id（有实际取车取实际取车）
    private String pickUpStoreName; //  计划取车门店名称（有实际取车取实际取车）
    private String returnOrgCode;   // 计划还车公司id（有实际还车取实际还车）
    private Long returnStoreId; //  计划还车门店id（有实际还车取实际还车）
    private Long returnShopSeq; //  计划还车虚拟门店id（有实际还车取实际还车）
    private String returnStoreName; //  计划还车门店名称（有实际还车取实际还车）
    private String pickUpDateTime;  //  计划取车时间 yyyyMMddHHmmss
    private String returnDateTime;  //  计划还车时间 yyyyMMddHHmmss
    private Long goodsModelId;  // 商品车型编号
    private String goodsModelName;  //  商品车型名称
    private String vin;  //  车架号

    private Integer depositState; // 押金状态 0:未缴纳押金 , 1:押金 ，2:免押 ，3:预授权 ，4:芝麻信用 , 5:支付宝共享汽车, 6:飞猪租车
    private Integer cancelWay; // 取消方式 1用户取消  2自动取消 3平台取消
    private Integer maxUseDays; // 最大用车时长

    private String canReRentStartTime; //  可续租开始时间	yyyyMMddHHmmss
    private String latestReturnTime; //  最晚还车时间	yyyyMMddHHmmss
    private String contractCreateTime; // 订单创建时间 yyyyMMddHHmmss
    private String realPickUpTime; // 实际取车时间 yyyyMMddHHmmss
    private String realReturnTime; // 实际还车时间 yyyyMMddHHmmss
    private String billingStartTime; // 计费开始时间 yyyyMMddHHmmss
    private String billingEndTime; // 计费截止时间 yyyyMMddHHmmss
    private String payTime; // 支付时间 yyyyMMddHHmmss
    private String cancelTime; // 取消时间 yyyyMMddHHmmss

    // 订单同步第三方需要使用
    private Long cityId;  //下单城市id
    private String orderChannel;  // 下单渠道（分包渠道）
    private Long realPickUpStoreId; //  实际取车门店id
    private Long realReturnStoreId; // 实际还车门店id
    private Long planPickUpStoreId; //  计划取车门店id
    private Long planReturnStoreId; // 计划还车门店id

    private Integer chooseSendService;  // 是否选择送车上门服务 1是 2否
    private Integer choosePickUpService;  // 是否选择上门取车服务 1是 2否
    private String orderOrgCode;//订单机构信息
    private Integer costTime; // 实际用车时长  单位：分钟
    private String amount; // 应收总金额
    private String realAmount; // 实付总金额
}
