<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.core.dao.UserContractMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.UserContract" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="auth_id" property="authId" jdbcType="VARCHAR" />
    <result column="customer_id" property="customerId" jdbcType="VARCHAR" />
    <result column="template_id" property="templateId" jdbcType="VARCHAR" />
    <result column="transaction_id" property="transactionId" jdbcType="VARCHAR" />
    <result column="contract_id" property="contractId" jdbcType="VARCHAR" />
    <result column="supplier" property="supplier" jdbcType="INTEGER" />
    <result column="archive_url" property="archiveUrl" jdbcType="VARCHAR" />
    <result column="download_url" property="downloadUrl" jdbcType="VARCHAR" />
    <result column="viewpdf_url" property="viewpdfUrl" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="misc_desc" property="miscDesc" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
    <result column="version_id" property="versionId" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, auth_id, customer_id,version_id, template_id, transaction_id, contract_id, download_url,
    viewpdf_url, status, misc_desc, create_time, create_oper_id, create_oper_name, update_time, 
    update_oper_id, update_oper_name,archive_url,supplier
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.extracme.evcard.membership.core.model.UserContractExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ${siacSchema}.user_contract
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from ${siacSchema}.user_contract
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from ${siacSchema}.user_contract
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.extracme.evcard.membership.core.model.UserContractExample" >
    delete from ${siacSchema}.user_contract
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.membership.core.model.UserContract" >
    insert into ${siacSchema}.user_contract (id, auth_id, customer_id,version_id,
      template_id, transaction_id, contract_id, 
      download_url, viewpdf_url, status,supplier,archive_url,
      misc_desc, create_time, create_oper_id, 
      create_oper_name, update_time, update_oper_id, 
      update_oper_name)
    values (#{id,jdbcType=BIGINT}, #{authId,jdbcType=VARCHAR}, #{customerId,jdbcType=VARCHAR},  #{versionId,jdbcType=VARCHAR},
      #{templateId,jdbcType=VARCHAR}, #{transactionId,jdbcType=VARCHAR}, #{contractId,jdbcType=VARCHAR}, 
      #{downloadUrl,jdbcType=VARCHAR}, #{viewpdfUrl,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER},
      #{supplier,jdbcType=INTEGER}, #{archiveUrl,jdbcType=VARCHAR},
      #{miscDesc,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, 
      #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, 
      #{updateOperName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.core.model.UserContract" keyProperty="id" useGeneratedKeys="true">
    insert into ${siacSchema}.user_contract
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="authId != null" >
        auth_id,
      </if>
      <if test="customerId != null" >
        customer_id,
      </if>
      <if test="versionId != null" >
        version_id,
      </if>
      <if test="templateId != null" >
        template_id,
      </if>
      <if test="transactionId != null" >
        transaction_id,
      </if>
      <if test="contractId != null" >
        contract_id,
      </if>
      <if test="downloadUrl != null" >
        download_url,
      </if>
      <if test="viewpdfUrl != null" >
        viewpdf_url,
      </if>

      <if test="supplier != null" >
        supplier,
      </if>
      <if test="archiveUrl != null" >
        archive_url,
      </if>

      <if test="status != null" >
        status,
      </if>
      <if test="miscDesc != null" >
        misc_desc,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateOperId != null" >
        update_oper_id,
      </if>
      <if test="updateOperName != null" >
        update_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="authId != null" >
        #{authId,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null" >
        #{customerId,jdbcType=VARCHAR},
      </if>
      <if test="versionId != null" >
        #{versionId,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null" >
        #{templateId,jdbcType=VARCHAR},
      </if>
      <if test="transactionId != null" >
        #{transactionId,jdbcType=VARCHAR},
      </if>
      <if test="contractId != null" >
        #{contractId,jdbcType=VARCHAR},
      </if>
      <if test="downloadUrl != null" >
        #{downloadUrl,jdbcType=VARCHAR},
      </if>
      <if test="viewpdfUrl != null" >
        #{viewpdfUrl,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="supplier != null" >
        #{supplier,jdbcType=INTEGER},
      </if>
      <if test="archiveUrl != null" >
        #{archiveUrl,jdbcType=VARCHAR},
      </if>
      <if test="miscDesc != null" >
        #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        #{updateOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.extracme.evcard.membership.core.model.UserContractExample" resultType="java.lang.Integer" >
    select count(*) from ${siacSchema}.user_contract
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update ${siacSchema}.user_contract
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.authId != null" >
        auth_id = #{record.authId,jdbcType=VARCHAR},
      </if>
      <if test="record.customerId != null" >
        customer_id = #{record.customerId,jdbcType=VARCHAR},
      </if>
      <if test="record.templateId != null" >
        template_id = #{record.templateId,jdbcType=VARCHAR},
      </if>
      <if test="record.transactionId != null" >
        transaction_id = #{record.transactionId,jdbcType=VARCHAR},
      </if>
      <if test="record.contractId != null" >
        contract_id = #{record.contractId,jdbcType=VARCHAR},
      </if>
      <if test="record.downloadUrl != null" >
        download_url = #{record.downloadUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.viewpdfUrl != null" >
        viewpdf_url = #{record.viewpdfUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.archiveUrl != null" >
        archive_url = #{record.archiveUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.supplier != null" >
        supplier = #{record.supplier,jdbcType=INTEGER},
      </if>
      <if test="record.miscDesc != null" >
        misc_desc = #{record.miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createOperId != null" >
        create_oper_id = #{record.createOperId,jdbcType=BIGINT},
      </if>
      <if test="record.createOperName != null" >
        create_oper_name = #{record.createOperName,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateOperId != null" >
        update_oper_id = #{record.updateOperId,jdbcType=BIGINT},
      </if>
      <if test="record.updateOperName != null" >
        update_oper_name = #{record.updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update ${siacSchema}.user_contract
    set id = #{record.id,jdbcType=BIGINT},
      auth_id = #{record.authId,jdbcType=VARCHAR},
      customer_id = #{record.customerId,jdbcType=VARCHAR},
      template_id = #{record.templateId,jdbcType=VARCHAR},
      transaction_id = #{record.transactionId,jdbcType=VARCHAR},
      contract_id = #{record.contractId,jdbcType=VARCHAR},
      download_url = #{record.downloadUrl,jdbcType=VARCHAR},
      viewpdf_url = #{record.viewpdfUrl,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      archive_url = #{record.archiveUrl,jdbcType=VARCHAR},
      supplier = #{record.supplier,jdbcType=INTEGER},
      misc_desc = #{record.miscDesc,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{record.createOperId,jdbcType=BIGINT},
      create_oper_name = #{record.createOperName,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{record.updateOperId,jdbcType=BIGINT},
      update_oper_name = #{record.updateOperName,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.core.model.UserContract" >
    update ${siacSchema}.user_contract
    <set >
      <if test="authId != null" >
        auth_id = #{authId,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null" >
        customer_id = #{customerId,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null" >
        template_id = #{templateId,jdbcType=VARCHAR},
      </if>
      <if test="transactionId != null" >
        transaction_id = #{transactionId,jdbcType=VARCHAR},
      </if>
      <if test="contractId != null" >
        contract_id = #{contractId,jdbcType=VARCHAR},
      </if>
      <if test="downloadUrl != null" >
        download_url = #{downloadUrl,jdbcType=VARCHAR},
      </if>
      <if test="viewpdfUrl != null" >
        viewpdf_url = #{viewpdfUrl,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="archiveUrl != null" >
        archive_url = #{archiveUrl,jdbcType=VARCHAR},
      </if>
      <if test="supplier != null" >
        supplier = #{supplier,jdbcType=INTEGER},
      </if>
      <if test="miscDesc != null" >
        misc_desc = #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.core.model.UserContract" >
    update ${siacSchema}.user_contract
    set auth_id = #{authId,jdbcType=VARCHAR},
      customer_id = #{customerId,jdbcType=VARCHAR},
      template_id = #{templateId,jdbcType=VARCHAR},
      transaction_id = #{transactionId,jdbcType=VARCHAR},
      contract_id = #{contractId,jdbcType=VARCHAR},
      download_url = #{downloadUrl,jdbcType=VARCHAR},
      viewpdf_url = #{viewpdfUrl,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      archive_url = #{archiveUrl,jdbcType=VARCHAR},
      supplier = #{supplier,jdbcType=INTEGER},
      misc_desc = #{miscDesc,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectMemberClauseRecord" parameterType="string" resultType="com.extracme.evcard.membership.core.dto.MemberClauseRecord">
    SELECT
      t.provision_type AS type,
      t.version AS version,
      t.start_time AS startTime,
      t.end_time AS endTime,
      u.create_time AS createTime
    FROM
      ${siacSchema}.user_contract u
    LEFT JOIN ${issSchema}.mmp_provision_info t ON u.template_id = (
    CASE t.provision_type
    WHEN '1' THEN
    CONCAT('SZ', t.version)
    WHEN '2' THEN
    CONCAT('YS', t.version)
    END
    )
    AND t.`status` = 1
    WHERE
    u.auth_id = #{authId}
    ORDER BY
    u.create_time DESC
    limit #{pageNum},${pageSize}
  </select>


  <select id="selectMemberLastContract" parameterType="string" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${siacSchema}.user_contract
    where auth_id = #{authId}
    and template_id like concat(#{prefix}, '%')
    and create_time &lt;= #{date}
    order by id desc
    limit 1
  </select>


  <select id="selectMemberCloseContract" parameterType="string" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${siacSchema}.user_contract
    where auth_id = #{authId}
    and template_id like concat(#{prefix}, '%')
    and create_time &gt;= #{date}
    <if test="versionId != null" >
     and version_id &gt;= #{versionId}
    </if>
    order by id asc
    limit 1
  </select>

  <select id="selectProvisionInfo" resultType="com.extracme.evcard.membership.core.dto.ProvisionInfoDto">
    SELECT
      t.id as id,
      t.template_id AS templateId,
      t.version AS version,
      t.provision_type AS provisionType,
      t.supplier AS supplier
    FROM ${issSchema}.mmp_provision_info t
    WHERE
    t.`status` = 1
    AND t.provision_type = #{provisionType}
    and t.version = #{version}
    limit 1
  </select>

  <select id="selectUserContractInfo" resultType="com.extracme.evcard.membership.core.dto.UserContractInfo">
          SELECT
            u.id AS id,
            u.auth_id AS authId,
            u.customer_id AS customerId,
            u.supplier AS supplier,
            u.version_id AS versionId,
            u.template_id AS templateId,
            u.contract_id AS contractId,
            u.transaction_id as transactionId,
            u.archive_url AS archiveUrl,
            u.create_time AS acceptTime,
            u.download_url AS downloadUrl,
            u.viewpdf_url AS viewUrl
        FROM
            ${siacSchema}.user_contract  u
        WHERE
            u.auth_id = #{authId}
            and u.status = 1
            and template_id = #{templateId}
        limit 1
    </select>


  <select id="selectFddContracts" resultType="com.extracme.evcard.membership.core.dto.UserContractKeyDto"  >
    select
    id as id,
    auth_id as authId,
    customer_id as customerId,
    version_id as versionId,
    contract_id as contractId,
    supplier as supplier,
    download_url as downloadUrl
    from ${siacSchema}.user_contract
    where
    id &gt; #{sp,jdbcType=BIGINT}
    and id &lt; #{ep,jdbcType=BIGINT}
    and (archive_url is null or archive_url = '')
    and supplier = 0
    order by id asc
    limit #{limit}
  </select>

  <update id="updateArchiveUrl" >
    update ${siacSchema}.user_contract
    set
    archive_url = CONCAT(#{prefix}, auth_id, '/', supplier, '_', contract_id, '.pdf')
    where
    supplier = #{supplier}
    <if test="list != null and list.size>0">
      AND contract_id in
      <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>

  </update>



  <select id="selectContractsByIds" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from ${siacSchema}.user_contract
    where 1=1
    <if test="list != null and list.size>0">
      AND id in
      <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
  </select>

  <select id="selectUnArchiveContracts" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from ${siacSchema}.user_contract
    where transaction_id is not null and contract_id = ''
    <if test="startTime != null">
      and CREATE_TIME &gt; #{startTime}
    </if>
    and CREATE_TIME &lt; #{endTime}
  </select>

</mapper>