package com.extracme.evcard.membership.core.service.license;


import com.alibaba.fastjson.JSON;
import com.extracme.evcard.third.dianwei.entity.DianweiConfig;
import com.extracme.evcard.third.dianwei.entity.DianweiGetResponse;
import com.extracme.evcard.third.dianwei.entity.DianweiReq;
import com.extracme.evcard.third.dianwei.entity.DianweiResult;
import com.extracme.evcard.third.dianwei.util.DianWeiConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 基于点微8318接口的
 * 三要素核查服务提供者
 */
@Slf4j
@Component
public class DianWeiAuthPlusService extends DianWeiAuthService implements ILicenseAuthenticateService {

    protected Integer getSupplier() {
        return 3;
    }

    @Override
    public DianweiConfig getConfig() {
        if (Objects.isNull(this.dianweiConfig)){
            this.dianweiConfig = new DianweiConfig();
            this.dianweiConfig.setChannelId(dianWeiServiceConfig.getChannelId());
            this.dianweiConfig.setEnkey(dianWeiServiceConfig.getEnkey());
            this.dianweiConfig.setProductCode(dianWeiServiceConfig.getProductCodeGet());
            this.dianweiConfig.setSalt(dianWeiServiceConfig.getSalt());
            this.dianweiConfig.setSubChannelName(dianWeiServiceConfig.getSubChannelName());
            this.dianweiConfig.setUrl(dianWeiServiceConfig.getUrl());
        }
        return this.dianweiConfig;
    }

    private static final String CHECK_OK = "1";
    private static final String CHECK_FAIL = "2";
    private static final String CHECK_RETRY = "3";
    private static final String CHECK_UNKNOWN = "4";

    public LicenseAuthenticateResult licenseAuthenticate(String name, String cardNo, String archviesNo, Integer readTimeOut) {
        /**
         * 1. 调用供应商三要素核验接口
         *    请求流水号：大写 & 唯一
         */
        DianweiResult<DianweiGetResponse> resp;
        DianweiReq req = DianweiReq.builder().name(name).cid(cardNo.toUpperCase()).build();
        req.setReqNo(seqGenerator.genDianWeiReqSeq());
        try {
            //lambda与AtomicReference结合使用。
            AtomicReference<DianweiResult<DianweiGetResponse>> result = new AtomicReference<>();
            super.getData(req, false, response -> {
                log.info("点微三要素核查：查询结果，input={}, response={}", JSON.toJSONString(req), JSON.toJSONString(response));
                response.setReqNo(req.getReqNo());
                result.set(response);
            }, (request, e)-> {
                log.error("点微三要素核查：查询失败，input=" + JSON.toJSONString(req), e);
                result.set(new DianweiResult<>(req.getReqNo(), "-1", e.getMessage(), null));
            });
            resp = result.get();
        }catch (Exception e) {
            log.error("点微三要素核查：查询异常，input=" + JSON.toJSONString(req), e);
            resp = new DianweiResult<>(req.getReqNo(), "-1", e.getMessage(), null);
        }

        /**
         * 2. 组织及转化核查结果
         */
        LicenseAuthenticateResult result = getLicenseCheckResult(resp, archviesNo);
        result.setRequestId(req.getReqNo());
        log.warn("点微三要素核查：核查完成，name={}, cardNo={}, fileNo={}, result={}", name, cardNo, archviesNo, JSON.toJSON(result));
        return result;
    }


    /**
     * 2. 组织及转化核验结果
     *    - 8318接口不返回驾照档案编号，因此默认档案编号校验通过
     *    - 接口返回驾照状态
     */
    /**
     * 根据8318返回的驾照信息，组织核查结果
     * @param resp
     * @return
     */
    private LicenseAuthenticateResult getLicenseCheckResult(DianweiResult<DianweiGetResponse> resp, String archviesNo) {
        String response = JSON.toJSONString(resp);
        if(StringUtils.equals(resp.getCode(), "-1")) {
            return LicenseAuthenticateResult.getException(response, StringUtils.abbreviate(resp.getMsg(), 50));
        }
        /**
         * 请求失败场景
         * 请求code 仅code= C0 或 C11表示有查询结果，做后续解析
         * 其他错误 跟产品配置、调用余额、白名单有关，不要做查无重试。
         * C0 查询成功
         *      C0 code=0 一致/成功
         *      C0 code=1 不一致
         *      C0 code=25 身份证未命中
         * C11 身份证号错误
         * ELSE 查询未成功(签名错误 渠道停用 产品不正确 白名单 余额不足等)
         */
        /**
         * 1. 非C0/C11，查询未成功
         */
        DianweiGetResponse verifyRes = resp.getData();
        if(resp == null || !Arrays.asList(DianWeiConstants.CODE_STATUS_OK, DianWeiConstants.CODE_STATUS_INVALID_CID).contains(resp.getCode())) {
            return LicenseAuthenticateResult.getFail(response, resp.getMsg());
        }

        /**
         * 2 C11 身份证号错误
         */
        if(DianWeiConstants.CODE_STATUS_INVALID_CID.equals(resp.getCode())) {
            return new LicenseAuthenticateResult(CHECK_FAIL, true, false, true,  response, StringUtils.EMPTY, resp.getMsg());
        }

        /**
         * 3.1 姓名/驾照匹配，驾照档案编号匹配
         */
        LicenseAuthenticateResult result = new LicenseAuthenticateResult();
        String verifyDesc = StringUtils.join('[', verifyRes.getRespCode(), ']', verifyRes.getRespDesc());
        if(DianWeiConstants.CODE_PASS.equals(verifyRes.getRespCode())) {
            //一致 + 档案编号存在 -> 默认档案编号一致
            if(StringUtils.isNotBlank(archviesNo)) {
                String devierStatus = verifyRes.getDetail().getDriveCardStatus();
                result = LicenseAuthenticateResult.getSuccess(response, devierStatus);
            }else {
                //档案编号缺失--> 档案编号不一致
                result.set(CHECK_FAIL, true, true, false,  StringUtils.EMPTY);
            }
        } else if(DianWeiConstants.CODE_FAIL.equals(verifyRes.getRespCode())) {
            //1 不一致
            result.set(CHECK_FAIL, false, false, true, StringUtils.EMPTY);
        }else if(DianWeiConstants.CODE_VALID_AUTH.equals(verifyRes.getRespCode())) {
            //25 身份证未命中
            result.set(CHECK_FAIL, true, false, true, StringUtils.EMPTY);
        }else if(DianWeiConstants.CODE_EXCEPTION.equals(verifyRes.getRespCode())) {
            //999 上游异常
            result.set(CHECK_RETRY, true, true, true, StringUtils.EMPTY);
        } else {
            return LicenseAuthenticateResult.getFail(response, verifyDesc);
        }
        result.setResponse(StringUtils.abbreviate(response,500));
        //result.setReason(StringUtils.join(resp.getMsg(), ':', verifyDesc));
        result.setReason(StringUtils.abbreviate(verifyDesc, 50));
        return result;
    }
}
