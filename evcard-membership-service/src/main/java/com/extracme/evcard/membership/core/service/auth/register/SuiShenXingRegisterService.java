package com.extracme.evcard.membership.core.service.auth.register;

import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.input.NotifyLoginDto;
import com.extracme.evcard.membership.core.input.NotifyRegisterDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SuiShenXingRegisterService extends AbstractRegister {

    @Value("${ssx.agencyId:00IL}")
    private String ssxAgencyId;
    @Value("${ssx.default.roleId:818}")
    private String ssxRoleId;


    @Override
    public String getBindAgencyId() {
        return ssxAgencyId;
    }

    @Override
    public String getBindAgencyRoleId() {
        return ssxRoleId;
    }

    @Override
    public String getDescription() {
        return "随身行";
    }

    @Override
    public String getSecondAppKey() {
        return ComUtil.SUISHENXING_SECOND_APP_KEY;
    }

    @Override
    public void notifyRegisterSuccess(NotifyRegisterDto notifyRegisterDto) {
        super.bindAgency(notifyRegisterDto);
    }

    @Override
    public void notifyLoginSuccess(NotifyLoginDto dto) {
        NotifyRegisterDto notifyRegisterDto  = new NotifyRegisterDto();
        BeanUtils.copyProperties(dto,notifyRegisterDto);
        super.bindAgency(notifyRegisterDto);
    }
}
