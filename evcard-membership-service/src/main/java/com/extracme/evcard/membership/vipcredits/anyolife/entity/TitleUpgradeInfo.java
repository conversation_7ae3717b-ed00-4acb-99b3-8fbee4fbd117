package com.extracme.evcard.membership.vipcredits.anyolife.entity;

import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.dto.UserTitleDto;
import com.extracme.evcard.membership.core.dto.UserTitleUpgradeDto;
import com.extracme.evcard.rpc.util.DateType;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2021/4/22
 * @remark
 */
@Data
public class TitleUpgradeInfo {
    /**
     * 原始称号/等级（例如T0/L0，T1/L1，T2/L1）
     */
    private String beforeClass;
    /**
     * 升级后称号/等级（例如T0/L0，T1/L1，T2/L1）
     */
    private String afterClass;
    /**
     * 升级时间，yyyy-MM-dd HH:mm:ss
     */
    private String upgradeTime;

    public void fromUpgradeDto(UserTitleUpgradeDto dto) {
        this.setBeforeClass(getTitleKey(dto.getOriginTitle()));
        this.setAfterClass(getTitleKey(dto.getNewTitle()));
        this.setUpgradeTime(DateFormatUtils.format(dto.getIssueTime(), DateType.DATE_TYPE1));
    }

    public static final String getTitleKey(UserTitleDto titleDto) {
        return "T" + titleDto.getTitleName() + "/L" + titleDto.getTitleLevel();
    }

    public static Pattern TITLE_PATTERN = Pattern.compile("T[0-9]/L[0-9]");
    public static final UserTitleDto fromTitleKey(String titleKey) {
        UserTitleDto titleDto = new UserTitleDto();
        if(!TITLE_PATTERN.matcher(titleKey).matches()) {
            return null;
        }
        titleDto.setTitleName(titleKey.charAt(1) - '0');
        titleDto.setTitleLevel(titleKey.charAt(4) - '0');
        return titleDto;
    }

    public UserTitleUpgradeDto toUpgradeDto() {
        UserTitleUpgradeDto dto = new UserTitleUpgradeDto();
        dto.setIssueTime(ComUtil.getDateFromStr(upgradeTime, DateType.DATE_TYPE1));
        dto.setOriginTitle(fromTitleKey(beforeClass));
        dto.setNewTitle(fromTitleKey(afterClass));
        if(dto.getIssueTime() == null || dto.getNewTitle() == null || dto.getOriginTitle() == null) {
            return null;
        }
        return dto;
    }
}
