package com.extracme.evcard.membership.core.service;

import java.security.MessageDigest;
import java.util.Formatter;
import java.util.UUID;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.extracme.evcard.activity.dto.UserBindRecordDto;
import com.extracme.evcard.activity.dto.UserThirdBindInput;
import com.extracme.evcard.activity.dubboService.IUserThirdBindService;
import com.extracme.evcard.membership.common.HttpClientUtils;
import com.extracme.evcard.membership.core.bean.LoginBeanResult;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.dto.WeixinSign;
import com.extracme.evcard.membership.core.input.LoginInput;
import com.extracme.evcard.rpc.enums.StatusCode;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.saic.bean.TokenRes;
import com.extracme.evcard.saic.service.ISaicMemberService;

@Service
public class WeChatH5Service implements IWeChatH5Service {
	
	Logger log = LoggerFactory.getLogger(this.getClass());
	
	@Autowired
	IMemberOperateServ memberOperateServ; 
	@Autowired
	IUserThirdBindService userThirdBindService;
	@Autowired
	ISaicMemberService saicMemberService;
	
	/**
	 * 用来验证openid合法性的秘钥.
	 */
	static final String OPENID_SECRET = "0dgnxSId9D3GXWdf";
	
	@Override
	public LoginBeanResult weChatLogin(String openId, String v, String mobile, String authcode, String access_token, String appKey) throws BusinessException {
		/**
		 * 校验openId
		 */
		String md5v = DigestUtils.md5Hex(openId + OPENID_SECRET);
		if(!StringUtils.equalsIgnoreCase(md5v, v)) {
			throw new BusinessException(StatusCode.ILLEGAL_PARAM);
		}
		/**
		 * 验证码登录.
		 */
		LoginInput loginInput = new LoginInput();
		loginInput.setLoginName(mobile);
		loginInput.setVerifyCode(authcode);
		loginInput.setCheckType(1);
		TokenRes tokenRes = saicMemberService.authcodeLogin(mobile, authcode, appKey, null);
		if(tokenRes == null || tokenRes.isLackCity() || tokenRes.getMember() == null) {
			throw new BusinessException(StatusCode.USER_NOT_EXITED);
		}
		MembershipBasicInfo member = tokenRes.getMember();
		LoginBeanResult loginResult = new LoginBeanResult();
		BeanUtils.copyProperties(member, loginResult);
		loginResult.setToken(tokenRes.getToken());
		/**
		 * openId和用户进行绑定.
		 */
		try {
//			UserBindRecordDto bindRecord = userThirdBindService.queryThirdBindRecord(openId, 0);
//			if(bindRecord != null && StringUtils.equals(bindRecord.getAuthId(), member.getAuthId())) {
//				return loginResult;
//			}
//			if(bindRecord != null && !StringUtils.equals(bindRecord.getAuthId(), member.getAuthId())) {
//				userThirdBindService.unBindUserRecord(bindRecord.getAuthId(), 0, "system");
//			}
			UserThirdBindInput bindInput = new UserThirdBindInput();
			bindInput.setAppKey(appKey);
			bindInput.setAuthId(member.getAuthId());
			bindInput.setMobile(member.getMobilePhone());
			bindInput.setOpenId(openId);
			bindInput.setChannel(0);
			bindInput.setAccessToken(access_token);
			userThirdBindService.userThirdBind(bindInput);
			return loginResult;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return loginResult;
		
	}
	
	@Override
	public LoginBeanResult weChatLogin(String openId, String v, String appKey) throws BusinessException {
		/**
		 * 校验openId
		 */
		String md5v = DigestUtils.md5Hex(openId + OPENID_SECRET);
		if(!StringUtils.equalsIgnoreCase(md5v, v)) {
			log.error("微信公众号授权登录失败 {}, openId={}, v={}, appKey={}", StatusCode.ILLEGAL_PARAM.getMsg(), openId, v, appKey);
			throw new BusinessException(StatusCode.ILLEGAL_PARAM);
		}
		/**
		 * 查询绑定关系.
		 */
		UserBindRecordDto bindRecord = userThirdBindService.queryThirdBindRecord(openId, 0);
		if(bindRecord == null || StringUtils.isBlank(bindRecord.getAuthId())) {
			log.error("微信公众号授权登录失败 {}, openId={}, v={}, appKey={}", StatusCode.OPENID_NOT_BIND.getMsg(), openId, v, appKey);
			throw new BusinessException(StatusCode.OPENID_NOT_BIND);
		}
		/**
		 * 登录.
		 */
		TokenRes tokenRes = saicMemberService.miniLogin(bindRecord.getMobilePhone(), openId, "wx2b75b2aa924c17a1", appKey);
		if(tokenRes == null || tokenRes.isLackCity() || tokenRes.getMember() == null) {
			log.error("微信公众号授权登录失败 {}, openId={}, v={}, appKey={}", StatusCode.USER_NOT_EXITED.getMsg(), openId, v, appKey);
			throw new BusinessException(StatusCode.USER_NOT_EXITED);
		}
		MembershipBasicInfo member = tokenRes.getMember();
		LoginBeanResult loginResult = new LoginBeanResult();
		BeanUtils.copyProperties(member, loginResult);
		loginResult.setToken(tokenRes.getToken());
		return loginResult;
	}

	@Override
	public WeixinSign weChatH5Sign(String url) {
		String nonce_str = create_nonce_str();
		String timestamp = create_timestamp();
		String jsapi_ticket = this.getJsapi_ticket();
		if(StringUtils.isBlank(jsapi_ticket)) {
			return null;
		}
		// 注意这里参数名必须全部小写，且必须有序
		String string1 = "jsapi_ticket=" + jsapi_ticket + "&noncestr=" + nonce_str + "&timestamp=" + timestamp + "&url=" + url;

		try {
			MessageDigest crypt = MessageDigest.getInstance("SHA-1");
			crypt.reset();
			crypt.update(string1.getBytes("UTF-8"));
			String signature = byteToHex(crypt.digest());
			WeixinSign sign = new WeixinSign();
			sign.setUrl(url);
			sign.setNonceStr(nonce_str);
			sign.setTimestamp(timestamp);
			sign.setSignature(signature);
			sign.setJsapi_ticket(jsapi_ticket);
			sign.setAppId("wx2b75b2aa924c17a1");
			return sign;
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return null;
		}
	}
	
	private static String create_nonce_str() {
		String uuid = UUID.randomUUID().toString();	//获取UUID并转化为String对象
		return uuid.replace("-", "");
	}
	
	private static String create_timestamp() {
		long time = System.currentTimeMillis()/1000;
		return String.valueOf(time);
	}

	private static String byteToHex(final byte[] hash) {
		Formatter formatter = new Formatter();
		for (byte b : hash) {
			formatter.format("%02x", b);
		}
		String result = formatter.toString();
		formatter.close();
		return result;
	}

	public String getJsapi_ticket() {
		String requestUrl = "http://crm-wx.evcard.com/w/rest/get_js_ticket?no=1&key=BB8E6B12F0E8691B49D54496A337E342";
		String result = HttpClientUtils.httpGetRequest(requestUrl);
		log.debug("获取微信getticket，结果是：{}", result);
		String jsapi_ticket = JSONObject.parseObject(result).getString("ticket");
		return jsapi_ticket;
	}

	public String getAccess_Token() {
		String requestUrl = "http://crm-wx.evcard.com/w/rest/get_token?no=1&key=BB8E6B12F0E8691B49D54496A337E342";
		String result = HttpClientUtils.httpGetRequest(requestUrl);
		log.debug("获取微信access_token，结果是：{}", result);
		String access_token = JSONObject.parseObject(result).getString("token");
		return access_token;
	}

}
