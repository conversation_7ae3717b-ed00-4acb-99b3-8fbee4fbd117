package com.extracme.evcard.membership.job;

import com.alibaba.fastjson.JSON;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.extracme.evcard.elasticjob.ElasticSimpleJob;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.dao.DriverLicenseElementsAuthenticateRecordMapper;
import com.extracme.evcard.membership.core.dao.DriverLicenseElementsReauthenticateListMapper;
import com.extracme.evcard.membership.core.dao.UserOperatorLogMapper;
import com.extracme.evcard.membership.core.dto.DrivingLicenseReviewResult;
import com.extracme.evcard.membership.core.dto.LicenseReAuthListQueryDto;
import com.extracme.evcard.membership.core.dto.UpdateUserDto;
import com.extracme.evcard.membership.core.enums.MemOperateTypeEnum;
import com.extracme.evcard.membership.core.enums.MemReviewTypeEnum;
import com.extracme.evcard.membership.core.exception.AuthenticationException;
import com.extracme.evcard.membership.core.input.UserOperationLogInput;
import com.extracme.evcard.membership.core.model.DriverLicenseElementsReauthenticateList;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.membership.core.service.MemberReviewService;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs;
import com.extracme.evcard.rpc.entity.HidLog;
import com.extracme.evcard.rpc.entity.LogPoint;
import com.extracme.framework.core.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 会员驾照三要素查无定时任务
 * 非大陆国籍不校验
 * 非驾照三要素
 * @history
 *       20200608
 * 		查无逻辑变更， 不判断用户审核状态和城市是否开启自动验证，均直接做一次三要素重新认证
 * 	    （审核状态将在用户下次登录时，做重新校准）
 * 	    2022.9.16 下线驾照三要素核验相关JOB
 */
@Slf4j
@Component
@Deprecated
//@ElasticSimpleJob(jobName = "evcard-membership-memberLicenseAuthJob",
//		cron = "0 */5 * * * ?", description = "三要素查无任务", overwrite = true)
public class MemberLicenseAuthJob implements SimpleJob{
	@Autowired
	private MembershipInfoMapper membershipInfoMapper;
	@Resource
	private MemberReviewService memberReviewService;
	@Resource
	private IMemberShipService memberShipService;

	@Autowired
	private UserOperatorLogMapper userOperatorLogMapper;

	@Autowired
	private DriverLicenseElementsAuthenticateRecordMapper licenseElementsAuthenticateRecordMapper;
	@Autowired
	private DriverLicenseElementsReauthenticateListMapper licenseElementsReauthenticateListMapper;

	/**
	 * 重试频率
	 * 5min/10min/20min/30min/1h/3h/6h/12h/24h/2*24h/3*24h/4*24h/5*24h/6*24h/7*24h（共15次）
	 */
	private static final int[] RETRY_TIME_SPANS = {5, 5, 10, 10, 30, 60*2, 60*3, 6*60, 12*60, 24*60, 24*60, 24*60, 24*60, 24*60, 24*60};
	private static final int MAX_RETRY_TIMES = 15;
	private static final int MAX_RE_AUTH_DAYS = 10;

    /**
     * 查无逻辑中，超时请求超时时间设置为20s。
     */
    public static final Integer RE_AUTH_READ_TIMEOUT = 20000;

	@Override
	public void execute(ShardingContext arg0) {
		/**
		 *   每5分钟扫描一次待查列表的有效记录，若满足以下
		 *   创建时间在7日内且仍有效中(status=1) 且
		 *   1.  复查次数=0 且上次校验时间>5分钟内
		 *   2.  复查次数=1 且上次校验时间>10分钟内
		 *   ...
		 *  定时扫描7日内，驾照三要素状态为查无的会员，对这些会员进行驾照三要素校准。
		 *  且只更新三要素及驾照认证状态，不变更审核状态。
		 */
		log.info("驾照三要素查无job，开始执行...");
		try {
			LicenseReAuthListQueryDto queryDto = getListQueryInput();
			List<DriverLicenseElementsReauthenticateList> list = licenseElementsReauthenticateListMapper.selectReAuthenticateList(queryDto);
			if(CollectionUtils.isEmpty(list)) {
				log.debug("驾照三要素查无job，待复查列表为空...");
				return;
			}
			Map<Long, DriverLicenseElementsReauthenticateList> reAuthUsers = new HashMap<>();
			for(DriverLicenseElementsReauthenticateList reAuthRecord : list) {
				reAuthUsers.put(reAuthRecord.getUserId(), reAuthRecord);
			}
			List<MembershipInfoWithBLOBs> members = membershipInfoMapper.selectByPkIds(reAuthUsers.keySet());
			log.warn("驾照三要素查无job：待复查三要素认证中的用户记录数={}", members.size());
			int count = 0;
			for (MembershipInfoWithBLOBs member : members) {
				DriverLicenseElementsReauthenticateList reAuthRecord = reAuthUsers.get(member.getPkId());
				String content = String.format("查无job，authId=%s，retryTimes=%d, reAuthRecord=%s", member.getAuthId(),
						reAuthRecord.getRetryTimes(), JSON.toJSONString(reAuthRecord));
				HidLog.membership(BussinessConstants.MEM_RVIEW_CORRECT_JOB, "查无逻辑开始-" + content, member.getAuthId(), false);
				if(!NumberUtils.INTEGER_ZERO.equals(member.getAccountStatus())) {
					log.warn("驾照三要素查无job：用户已注销或冻结中，不做重新验证, authId={}, licenseElementsAuthStatus={}",
							member.getAuthId(), member.getLicenseElementsAuthStatus());
					disableAuthenticateList(member.getAuthId(), reAuthRecord,"accountInvalid");
					continue;
				}
                if(member.getLicenseElementsAuthStatus() != null && member.getLicenseElementsAuthStatus() != 3) {
                    log.warn("驾照三要素查无job：用户三要素最新状态非查证中, 不做重新验证，authId={}, licenseElementsAuthStatus={}",
                            member.getAuthId(), member.getLicenseElementsAuthStatus());
					disableAuthenticateList(member.getAuthId(), reAuthRecord,"statusNotAuthIng");
					continue;
                }
				if(!ComUtil.checkIsUsersInMainlandResidents(member.getNational())) {
					log.warn("驾照三要素查无job：用户三要素状态查证中，但国籍非中国大陆, 不做重新验证，authId={}, national={}",
							member.getAuthId(), member.getNational());
					disableAuthenticateList(member.getAuthId(), reAuthRecord,"notMainlandUser");
					continue;
				}
				//进行驾照三要素校准
				try {
                    HidLog.membership(BussinessConstants.MEM_RVIEW_CORRECT_JOB, "三要素重新验证-" + content, member.getAuthId(), true);
                    //开始调用三要素接口，执行校验。
					DrivingLicenseReviewResult resp = memberReviewService.reAuthenticateDrivingLicense(member, RE_AUTH_READ_TIMEOUT);
					log.info("驾照三要素查无job：校准会员驾照审核状态，authId={}, result={}", member.getAuthId(), JSON.toJSON(resp));
                    HidLog.membership(BussinessConstants.MEM_RVIEW_CORRECT_JOB, "三要素重新验证-查无job，结果=" + JSON.toJSON(resp),
                            member.getAuthId(), false);
                    if(resp != null) {
						updateAuthenticateListWhenReAuth(member, resp, reAuthRecord);
					}else{
						log.error("驾照三要素查无job：校准会员驾照审核状态失败，authId={}, result=null", member.getAuthId());
					}
					HidLog.membership(BussinessConstants.MEM_RVIEW_CORRECT_JOB, "三要素重新验证完成, result=" + JSON.toJSONString(resp),
							member.getAuthId(), true);
					if(count ++ == 10) {
						Thread.sleep(1000);
					}
				}catch (Exception ex) {
					log.warn("驾照三要素查无job：重新验证三要素失败，authId=" + member.getAuthId(), ex);
				}
			}
		} catch (Exception e) {
			log.error("驾照三要素查无job：执行异常...", e);
		}
        log.info("驾照三要素查无job，完成执行...");
    }

    public void updateAuthenticateListWhenReAuth(MembershipInfoWithBLOBs member, DrivingLicenseReviewResult result, DriverLicenseElementsReauthenticateList reAuthRecord) throws AuthenticationException {
		String authId = member.getAuthId();
        DriverLicenseElementsReauthenticateList updateRecord = new DriverLicenseElementsReauthenticateList();
        BeanCopyUtils.copyProperties(result, updateRecord);
        updateRecord.setId(reAuthRecord.getId());
        updateRecord.setLastestAuthenticateTime(new Date());
        //若仍然查无，则仅更新次数和下次触发时间
        int retryTimes = reAuthRecord.getRetryTimes();
        if(result.getAuthenticateStatus() == 3) {
            updateRecord.setRetryTimes(1);//增一
            updateRecord.setStatus(null);
            if((++retryTimes) >= MAX_RETRY_TIMES) {
                log.warn("驾照三要素查无job：达到重试次数上线，结束复查，authId={}, reAuthRecordId={}", authId, reAuthRecord.getId());
                updateRecord.setStatus(2);
            }else{
                int minutes = RETRY_TIME_SPANS[retryTimes];
                Calendar calendar = Calendar.getInstance(ComUtil.timeZoneChina);
                calendar.add(Calendar.MINUTE, minutes);
                updateRecord.setNextAuthenticateTime(calendar.getTime());
            }
        }else {
            //有其他结果或会员当前状态已经不必再查无，则结束此用户的查无
            log.warn("驾照三要素查无job：本次查询已有结果，结束复查，authId={}, reAuthRecordId={}", authId, reAuthRecord.getId());
			updateRecord.setMiscDesc(StringUtils.join(reAuthRecord.getMiscDesc(), "/status:" + result.getAuthenticateStatus()));
			updateRecord.setStatus(0);
        }
        licenseElementsReauthenticateListMapper.updateWhenReAuthByPrimaryKey(updateRecord);

		//查有结果且结果为一致时，若驾照认证通过且身份认证为认证通过，
		//审核状态为审核不通过/资料不全 则根据驾照提交方式，确定是提交人工待审核or后台审核通过
		if(member.getAuthenticationStatus() == 2 && result.getAuthenticateStatus() == 1 && result.getLicenseStatus() == 1) {
			log.warn("驾照三要素查无job：本次结果驾照认证一致，驾照状态正常，authId={}, result={}", authId, JSON.toJSONString(result));
			String content = "驾照三要素查无job-本次结果驾照认证一致，驾照状态正常";
			//仅非审核通过状态下才变更用户审核状态
			if(!NumberUtils.SHORT_ONE.equals(member.getReviewStatus())) {
				//驾照录入方式为手动，则提交人工审核
				if (member.getDriverLicenseInputType() == 1) {
					//手动修改过，则若驾照认证通过则提交人工审核(仅老用户，新用户刷脸过后再变更)
					UpdateUserDto operator = UpdateUserDto.buildUserOperator(authId);
					memberReviewService.autoMemberReviewSuccess(member, 0, operator, MemReviewTypeEnum.SYSTEM_AUTO_REVIEW);
					content += "，系统自动审核通过";
					HidLog.membership(BussinessConstants.MEM_RVIEW_CORRECT_JOB, content, authId, true);
				} else {
					//手动修改过，则若驾照认证通过则提交人工审核(仅老用户，新用户刷脸过后再变更)
					memberReviewService.submitManualReview(member, content);
					content += "，提交人工审核";
					HidLog.membership(BussinessConstants.MEM_RVIEW_CORRECT_JOB, content, authId, true);
				}
			}
			//保存操作日志
			ComUtil.insertOperatorLog(content, authId, null, authId, userOperatorLogMapper);
			UserOperationLogInput userOperationLogInput = new UserOperationLogInput();
			userOperationLogInput.setOperationType(MemOperateTypeEnum.UPDATE_FILE_NO.getCode());
			userOperationLogInput.setOperationContent(content);
			userOperationLogInput.setOperationTime(new Date());
			userOperationLogInput.setOperator(authId);
			userOperationLogInput.setRefKey1(authId);
			userOperationLogInput.setUserId(member.getPkId());
			userOperationLogInput.setAuthId(authId);
			memberShipService.saveUserOperationLog(userOperationLogInput);
			HidLog.membership(LogPoint.UPDATE_DRIVER_FILE_NO, content, authId, true);
		}
    }

	public void disableAuthenticateList(String authId, DriverLicenseElementsReauthenticateList reAuthRecord, String reason) {
		try {
			DriverLicenseElementsReauthenticateList updateRecord = new DriverLicenseElementsReauthenticateList();
			updateRecord.setUpdateOperId(1L);
			updateRecord.setUpdateOperName("system");
			updateRecord.setUpdateTime(new Date());
			updateRecord.setId(reAuthRecord.getId());
			updateRecord.setMiscDesc(StringUtils.join(reAuthRecord.getMiscDesc(), "/" + reason));
			log.warn("驾照三要素查无job：本次已不符合重新查验条件，结束复查，authId={}, reAuthRecordId={}", authId, reAuthRecord.getId());
			updateRecord.setStatus(0);
			licenseElementsReauthenticateListMapper.updateWhenReAuthByPrimaryKey(updateRecord);
		}catch (Exception e) {
			log.warn("驾照三要素查无job：变更待复查记录为无效失败，authId=" + authId + ", reAuthRecordId=" + reAuthRecord.getId(), e);
		}
	}

	private LicenseReAuthListQueryDto getListQueryInput(){
		LicenseReAuthListQueryDto queryDto = new LicenseReAuthListQueryDto();
		Calendar calendar = Calendar.getInstance(ComUtil.timeZoneChina);
		//本周内，查无的会员且当前仍为查无状态
		Date endTime = calendar.getTime();
		calendar.add(Calendar.DATE, 0 - MAX_RE_AUTH_DAYS);
		Date startTime = calendar.getTime();
		//只查询3日内创建的待复查记录
		queryDto.setCreateStartTime(startTime);
		queryDto.setCreateEndTime(endTime);
		queryDto.setMaxRetryTimes(MAX_RETRY_TIMES);
		queryDto.setMinLatestAuthenticateTime(startTime);
		//保存了下次验证时间后，不需要再组织此查询条件
		//支持上次验证时间及次数查询条件
		//queryDto.buildRetryConditions(RETRY_TIME_SPANS);
		return queryDto;
	}


	public static void main(String[] args) {
		MemberLicenseAuthJob job = new MemberLicenseAuthJob();
		job.execute(null);
	}
}
