package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.model.UserToken;
import com.extracme.evcard.membership.core.model.UserTokenExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UserTokenMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_token
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    int countByExample(UserTokenExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_token
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_token
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    int insert(UserToken record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_token
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    int insertSelective(UserToken record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_token
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    List<UserToken> selectByExample(UserTokenExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_token
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    UserToken selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_token
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    int updateByExampleSelective(@Param("record") UserToken record, @Param("example") UserTokenExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_token
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    int updateByExample(@Param("record") UserToken record, @Param("example") UserTokenExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_token
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    int updateByPrimaryKeySelective(UserToken record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_token
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    int updateByPrimaryKey(UserToken record);

    UserToken selectByToken(@Param("token") String token);

    UserToken selectByUserId(@Param("userId") Long userId);

}