package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.model.MembershipOcrCallLog;

public interface MembershipOcrCallLogMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MembershipOcrCallLog record);

    int insertSelective(MembershipOcrCallLog record);

    MembershipOcrCallLog selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MembershipOcrCallLog record);

    int updateByPrimaryKey(MembershipOcrCallLog record);
}