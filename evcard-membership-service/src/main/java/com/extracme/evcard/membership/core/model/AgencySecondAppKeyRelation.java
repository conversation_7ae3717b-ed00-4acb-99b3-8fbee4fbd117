package com.extracme.evcard.membership.core.model;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 二级渠道与代理商关系实体类
 */
@Data
public class AgencySecondAppKeyRelation {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 企业ID
     */
    private String agencyId;

    /**
     * 二级渠道AppKey
     */
    private String secondAppKey;

    /**
     * 规则ID
     */
    private Long agencyRoleId;

    /**
     * 微信太阳码图片URL地址
     */
    private String wechatQrPicUrl;

    /**
     * 状态（0=正常 1=已删除）
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    private Long createOperId;

    /**
     * 创建人名称
     */
    private String createOperName;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人ID
     */
    private Long updateOperId;

    /**
     * 更新人名称
     */
    private String updateOperName;


}
