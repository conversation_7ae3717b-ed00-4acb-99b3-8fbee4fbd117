package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.model.ExpressInfo;

import java.util.List;

public interface ExpressInfoMapper {
    int deleteByPrimaryKey(String expressNo);

    int insert(ExpressInfo record);

    int insertSelective(ExpressInfo record);

    ExpressInfo selectByPrimaryKey(String expressNo);

    int updateByPrimaryKeySelective(ExpressInfo record);

    int updateByPrimaryKey(ExpressInfo record);

    List<ExpressInfo> selectByAuthId(String authId);
}