package com.extracme.evcard.membership.core.service;

import com.aliyun.oss.model.OSSObject;
import com.extracme.evcard.membership.common.*;
import com.extracme.evcard.membership.config.OssConfigUtil;
import com.extracme.evcard.membership.core.dao.MmpShortLinkLogMapper;
import com.extracme.evcard.membership.core.dao.MmpShortLinkManagementMapper;
import com.extracme.evcard.membership.core.dto.CreateWxQrCodeResp;
import com.extracme.evcard.membership.core.dto.MmpShortLinkLogDTO;
import com.extracme.evcard.membership.core.dto.MmpShortLinkManagementDTO;
import com.extracme.evcard.membership.core.dto.OperatorDto;
import com.extracme.evcard.membership.core.dto.input.CreateWxQrCodeDto;
import com.extracme.evcard.membership.core.dto.input.CreateWxQrCodeInput;
import com.extracme.evcard.membership.core.dto.input.WxQrCodeDto;
import com.extracme.evcard.membership.core.input.MmpWeChatResponse;
import com.extracme.evcard.membership.core.model.MmpShortLinkLog;
import com.extracme.evcard.membership.core.model.MmpShortLinkManagement;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.framework.core.bo.PageBO;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.util.BeanCopyUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ForkJoinPool;
import java.util.stream.Collectors;

/**
 * 短链接相关逻辑实现
 * <AUTHOR>
 * @date 2024.07.01
 */
@Service("shortlinkManagementService")
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class ShortlinkManagementServiceImpl implements IShortlinkManagementService {

	@Resource
	private MmpShortLinkManagementMapper mmpShortLinkManagementMapper;
	@Resource
	private MmpShortLinkLogMapper mmpShortLinkLogMapper;
	@Autowired
	private WeChatService weChatService;

	public final static String ENV = OssConfigUtil.getENV();

	@Value("${mmp.shortlink.domain}")
	private String shortlinkDomain;

	@Value("${oss.web_url}")
    private String ossFilePath;

	/**
	 * 插入或更新短链信息
	 * @param mmpShortLinkManagementDTO
	 * @param operatorDto
	 * @return
	 */
	@Override
	public MmpShortLinkManagementDTO insertOrUpdateShortlink(MmpShortLinkManagementDTO mmpShortLinkManagementDTO, OperatorDto operatorDto) {
		if (ObjectUtils.isEmpty(mmpShortLinkManagementDTO)){
			return null;
		}
		Long id = mmpShortLinkManagementDTO.getId();

		String createOperName = null;
		Long createOperId = null;
		if (operatorDto == null){
			createOperName = mmpShortLinkManagementDTO.getCreateOperName();
			createOperId = mmpShortLinkManagementDTO.getCreateOperId();
		}else {
			createOperName = operatorDto.getOperatorName();
			createOperId = operatorDto.getOperatorId();
		}



		MmpShortLinkManagement mmpShortLinkManagement = new MmpShortLinkManagement();
		BeanCopyUtils.copyProperties(mmpShortLinkManagementDTO,mmpShortLinkManagement);
		mmpShortLinkManagement.setUpdateOperId(createOperId);
		mmpShortLinkManagement.setUpdateOperName(createOperName);
		mmpShortLinkManagement.setEffectiveTime(mmpShortLinkManagementDTO.getEffectiveTime() == null?null: DateUtils.localDateToDate(mmpShortLinkManagementDTO.getEffectiveTime()));
		mmpShortLinkManagement.setFailureTime(mmpShortLinkManagementDTO.getFailureTime() == null?null:DateUtils.localDateToDate(mmpShortLinkManagementDTO.getFailureTime()));
		if (id == null){
			//id为空，做新增操作
			String randomStr = ShortLinkUtil.createRandomStr(6);
			if (ShortLinkUtil.checkRandomStr(randomStr)){
				String shortUrl = shortlinkDomain.concat("/").concat(randomStr);
				String qrCodePath = createQrCode(shortUrl, randomStr);
				mmpShortLinkManagement.setCreateTime(new Timestamp(System.currentTimeMillis()));
				mmpShortLinkManagement.setCreateOperId(createOperId);
				mmpShortLinkManagement.setCreateOperName(createOperName);
				mmpShortLinkManagement.setUpdateOperId(createOperId);
				mmpShortLinkManagement.setUpdateOperName(createOperName);
				mmpShortLinkManagement.setStatus(1);
				mmpShortLinkManagement.setShortLinkUrl(shortUrl);
				mmpShortLinkManagement.setRandomCode(randomStr);
				mmpShortLinkManagement.setQrcodePath(qrCodePath);
				mmpShortLinkManagement.setIsHidden(mmpShortLinkManagement.getIsHidden());
				mmpShortLinkManagementMapper.insertSelective(mmpShortLinkManagement);
				this.saveLog(createOperName,createOperId, Long.valueOf(0),mmpShortLinkManagement.getId());
			}
		}else {
			//id不为空，做更新操作
			mmpShortLinkManagementMapper.updateByDto(mmpShortLinkManagement);
			Long operationType = mmpShortLinkManagement.getStatus() == 0 ? Long.valueOf(1) : Long.valueOf(2);
			this.saveLog(createOperName,createOperId, operationType,mmpShortLinkManagement.getId());

		}

		MmpShortLinkManagementDTO returnShortLinkManagementDTO = new MmpShortLinkManagementDTO();
		BeanCopyUtils.copyProperties(mmpShortLinkManagement,returnShortLinkManagementDTO);
		//returnShortLinkManagementDTO.setMmpShortLinkLogList(this.getLogByLinkedId(returnShortLinkManagementDTO.getId()));
		returnShortLinkManagementDTO.setEffectiveTime(mmpShortLinkManagement.getEffectiveTime() == null ? null : DateUtils.dateToLocalDate(mmpShortLinkManagement.getEffectiveTime(),DateUtils.DATE_TYPE5));
		returnShortLinkManagementDTO.setFailureTime(mmpShortLinkManagement.getFailureTime() == null ? null : DateUtils.dateToLocalDate(mmpShortLinkManagement.getFailureTime(),DateUtils.DATE_TYPE5));
		return returnShortLinkManagementDTO;
	}


	/**
	 * 获得短链信息列表
	 * @param mmpShortLinkManagementDTO
	 * @return
	 */
	@Override
	public PageBeanBO<MmpShortLinkManagementDTO> getShortlinkList(MmpShortLinkManagementDTO mmpShortLinkManagementDTO) {
		PageBeanBO<MmpShortLinkManagementDTO> mmpShortLinkManagementDTOPage = new PageBeanBO<>();
		List<MmpShortLinkManagementDTO> mmpShortLinkManagementDTOS = new ArrayList<>();
		Integer isAll = mmpShortLinkManagementDTO.getIsAll() == null ? 0 : mmpShortLinkManagementDTO.getIsAll();
		Integer pageNum = mmpShortLinkManagementDTO.getPageNum() == null ? 1 : mmpShortLinkManagementDTO.getPageNum();
		Integer pageSize = mmpShortLinkManagementDTO.getPageSize() == null ? 10 : mmpShortLinkManagementDTO.getPageSize();
		if (isAll == 0) {
			PageHelper.startPage(pageNum, pageSize, false);
		} else {
			PageHelper.startPage(pageNum, pageSize);
		}
		List<MmpShortLinkManagement> mmpShortLinkManagements = mmpShortLinkManagementMapper.selectByKeys(mmpShortLinkManagementDTO.getShortLinkName(),mmpShortLinkManagementDTO.getShortLinkUrl(),mmpShortLinkManagementDTO.getOriginalUrl(),null, null,mmpShortLinkManagementDTO.getStatus());
		mmpShortLinkManagements.forEach(mmpShortLinkManagement -> {
			if (StringUtils.isBlank(mmpShortLinkManagement.getQrcodePath())){
				//二维码为空，重新生成二维码
				mmpShortLinkManagement = reCreateQRCode(mmpShortLinkManagement);
			}
			MmpShortLinkManagementDTO shortLinkManagementDTO = new MmpShortLinkManagementDTO();
			BeanCopyUtils.copyProperties(mmpShortLinkManagement,shortLinkManagementDTO);
			//shortLinkManagementDTO.setMmpShortLinkLogList(this.getLogByLinkedId(shortLinkManagementDTO.getId()));
			shortLinkManagementDTO.setEffectiveTime(mmpShortLinkManagement.getEffectiveTime() == null ? null : DateUtils.dateToLocalDate(mmpShortLinkManagement.getEffectiveTime(),DateUtils.DATE_TYPE5));
			shortLinkManagementDTO.setFailureTime(mmpShortLinkManagement.getFailureTime() == null ? null : DateUtils.dateToLocalDate(mmpShortLinkManagement.getFailureTime(),DateUtils.DATE_TYPE5));
			mmpShortLinkManagementDTOS.add(shortLinkManagementDTO);
		});

		PageInfo<MmpShortLinkManagement> pageOrderDTO = new PageInfo<>(mmpShortLinkManagements);
		PageBO pageBO = new PageBO();
		BeanCopyUtils.copyProperties(pageOrderDTO, pageBO);

		mmpShortLinkManagementDTOPage.setList(mmpShortLinkManagementDTOS);
		mmpShortLinkManagementDTOPage.setPage(pageBO);
		return mmpShortLinkManagementDTOPage;
	}

	/**
	 * 重新生成二维码
	 * @param mmpShortLinkManagementDTO
	 * @return
	 */
	@Override
	public MmpShortLinkManagementDTO reCreateQRCode(MmpShortLinkManagementDTO mmpShortLinkManagementDTO) {
		MmpShortLinkManagement mmpShortLinkManagement = new MmpShortLinkManagement();
		BeanCopyUtils.copyProperties(mmpShortLinkManagementDTO,mmpShortLinkManagement);
		mmpShortLinkManagement = reCreateQRCode(mmpShortLinkManagement);
		BeanCopyUtils.copyProperties(mmpShortLinkManagement,mmpShortLinkManagementDTO);
		return mmpShortLinkManagementDTO;
	}

	/**
	 * 重新生成二维码
	 * @param mmpShortLinkManagement
	 * @return
	 */
	private MmpShortLinkManagement reCreateQRCode(MmpShortLinkManagement mmpShortLinkManagement){
		String qrCodePath = createQrCode(mmpShortLinkManagement.getShortLinkUrl(), mmpShortLinkManagement.getRandomCode());
		mmpShortLinkManagement.setQrcodePath(qrCodePath);
		mmpShortLinkManagementMapper.updateByDto(mmpShortLinkManagement);
		return mmpShortLinkManagement;
	}

	/**
	 * 创建二维码
	 * @param shortUrl
	 */
	@Override
	public String createQrCode(String shortUrl,String randomCode) {
		InputStream qrCodeInputStream  = null;
		try {
			qrCodeInputStream = CreateQRCodeUtil.drawLogoQRCode(null, shortUrl, null, 1000, 1000);
			String uploadFileUrl = this.getUploadFileUrl(randomCode);
			//downloadQRPicture(qrCodeInputStream,uploadFileUrl);

			UploadImgUtil.uploadStreamSyn(qrCodeInputStream, uploadFileUrl.toString());

			return uploadFileUrl;
		}catch (Exception e){
			e.printStackTrace();
			return null;
		}finally {
			IOUtils.closeQuietly(qrCodeInputStream);
		}
	}

	/**
	 * 通过短链获取长连接
	 * @param randomCode
	 * @return
	 */
	@Override
	public String getLongUrl(String randomCode){
		List<MmpShortLinkManagement> mmpShortLinkManagements = mmpShortLinkManagementMapper.selectByKeys(null,null,null,randomCode, null,1);
		if (CollectionUtils.isNotEmpty(mmpShortLinkManagements)){
			MmpShortLinkManagement mmpShortLinkManagement = mmpShortLinkManagements.get(0);
			return mmpShortLinkManagement.getOriginalUrl();
		}
		return null;
	}

	@Override
	public String getLongUrlParams(String randomCode) {
		try {
			String longUrl = this.getLongUrl(randomCode);
			if (StringUtils.isNotBlank(longUrl)){
				String[] splits = longUrl.split("\\?");
				if (splits.length==2){
					return splits[1];
				}
			}
		}catch (Exception e){
			return null;
		}
		return null;
	}


	/**
	 * 把二维码下载到本地
	 * @param qrCodeInputStream
	 * @param fileName
	 */
	private void downloadQRPicture(InputStream qrCodeInputStream,String fileName){
//		String path= "C:\\Users\\<USER>\\Desktop\\";
		String path= "";
		try {
			OutputStream outputStream = new FileOutputStream(path.concat(fileName));
			byte[] buffer = new byte[1024];
			int bytesRead;
			while ((bytesRead = qrCodeInputStream.read(buffer)) != -1) {
				outputStream.write(buffer, 0, bytesRead);
			}
		}catch (Exception e){

		}
	}

	/**
	 * 日志记录
	 * @param comModel
	 * @param operationType
	 * @param linkedId
	 */
	private void saveLog(OperatorDto operatorDto,Long operationType,Long linkedId){
		this.saveLog(operatorDto.getOperatorName(),operatorDto.getOperatorId(),operationType,linkedId);
	}
	private void saveLog(String createOperName,Long createOperId,Long operationType,Long linkedId){
		try {
			MmpShortLinkLog mmpShortLinkLog = new MmpShortLinkLog();
			mmpShortLinkLog.setStatus(1);
			mmpShortLinkLog.setCreateOperId(createOperId);
			mmpShortLinkLog.setCreateOperName(createOperName);
			mmpShortLinkLog.setUpdateOperId(createOperId);
			mmpShortLinkLog.setUpdateOperName(createOperName);
			mmpShortLinkLog.setLinkedId(linkedId);
			mmpShortLinkLog.setOperationType(operationType);
			mmpShortLinkLogMapper.insert(mmpShortLinkLog);
		}catch (Exception e){
			log.error("短链日志记录失败：LinkedId->{},operationType->{}",linkedId,operationType);
		}
	}

	/**
	 * 查询关联日志
	 * @param linkedId
	 */
	private List<MmpShortLinkLogDTO> getLogByLinkedId(Long linkedId){
		List<MmpShortLinkLogDTO> mmpShortLinkLogDTOS = new ArrayList<>();
		List<MmpShortLinkLog> mmpShortLinkLogs = mmpShortLinkLogMapper.selectByLinkedId(linkedId);
		Integer id = 1;
		for (MmpShortLinkLog mmpShortLinkLog : mmpShortLinkLogs) {
			MmpShortLinkLogDTO mmpShortLinkLogDTO = new MmpShortLinkLogDTO();
			BeanCopyUtils.copyProperties(mmpShortLinkLog,mmpShortLinkLogDTO);
			mmpShortLinkLogDTO.setId(id);
			mmpShortLinkLogDTOS.add(mmpShortLinkLogDTO);
			id++;
		}
		return mmpShortLinkLogDTOS;
	}

	private String getUploadFileUrl(String randomCode) {
		StringBuffer fileUrl = new StringBuffer("/qrCode/shortLink/");
		String suffix = ".png";
		fileUrl.append(ComUtil.getSystemDate(ComUtil.DATE_TYPE3) + "_" + randomCode + suffix);
		return fileUrl.toString();
	}

	@Override
	public String createWxQrCode(String shortLinkName, String originalUrl, String weChatPage, int isHidden, String dir, OperatorDto operatorDto) {
		return createWxQrCodeRetry(shortLinkName, originalUrl, weChatPage, isHidden, dir, operatorDto, 0);
	}

	@Override
	public String createWxQrCodeRetry(String shortLinkName, String originalUrl, String weChatPage, int isHidden, String dir, OperatorDto operatorDto, int retryNum) {
		//生成短链
		MmpShortLinkManagementDTO mmpShortLinkManagementDTO = new MmpShortLinkManagementDTO();
		//短链名称 ID-一级渠道/二级渠道
		mmpShortLinkManagementDTO.setShortLinkName(shortLinkName);
		mmpShortLinkManagementDTO.setOriginalUrl(originalUrl);
		mmpShortLinkManagementDTO.setEffectiveTime(LocalDate.now());
		mmpShortLinkManagementDTO.setIsHidden(isHidden);
		mmpShortLinkManagementDTO = this.insertOrUpdateShortlink(mmpShortLinkManagementDTO, operatorDto);

		//生成微信小程序码
		MmpWeChatResponse mmpWeChatResponse = weChatService.getwxacodeunlimit(weChatPage, mmpShortLinkManagementDTO.getRandomCode(), retryNum, dir, shortLinkName);
		return mmpWeChatResponse.getWeChatQrUrl();
	}

	@Override
	public Map<String, WxQrCodeDto> batchCreateWxQrCode(List<CreateWxQrCodeDto> createWxQrCodeDtos, String dir, OperatorDto operatorDto) throws BusinessException {
		if (CollectionUtils.isEmpty(createWxQrCodeDtos)) {
			return new HashMap<>();
		}
		if (createWxQrCodeDtos.size() > 100) {
			throw new BusinessException("待生成列表不能超过100个");
		}

		List<CompletableFuture<String>> futures = createWxQrCodeDtos.stream()
				.map(dto -> CompletableFuture.supplyAsync(() -> createWxQrCode(dto.getShortLinkName(), dto.getOriginalUrl(), dto.getWeChatPage(), 1, dir, operatorDto), ThreadPoolUtils.WXQRCODE_EXECUTOR)).collect(Collectors.toList());

		Map<String, WxQrCodeDto> result = new ConcurrentHashMap<>();
		CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
            .thenRun(() -> futures.forEach(future -> {
                try {
                    String wxQrCode = future.get();
					CreateWxQrCodeDto dto = createWxQrCodeDtos.get(futures.indexOf(future));
					WxQrCodeDto wxQrCodeDto = new WxQrCodeDto();
					if (StringUtils.isEmpty(wxQrCode)) {
                        log.error("生成二维码失败, key:{}", dto.getShortLinkName());
                    }
					else {
						wxQrCodeDto.setKey(wxQrCode);
						wxQrCodeDto.setBucket(UploadImgUtil.OSS_BUCKET);
						wxQrCodeDto.setUrl(ossFilePath + wxQrCode);
					}
					result.put(dto.getShortLinkName(), wxQrCodeDto);
                } catch (Exception e) {
                    log.error("处理 CompletableFuture 结果时发生错误: {}", e.getMessage());
                }
            })).join();
		return result;
	}

	@Override
	public List<MmpShortLinkManagementDTO> getMmpShortLinkManagementByOriginUrl(String originUrl) {
		List<MmpShortLinkManagement> mmpShortLinkManagements = mmpShortLinkManagementMapper.selectByKeys(null, null, originUrl, null, null, 1);
		List<MmpShortLinkManagementDTO> mmpShortLinkManagementDTOS = new ArrayList<>();
		for (MmpShortLinkManagement mmpShortLinkManagement : mmpShortLinkManagements) {
			MmpShortLinkManagementDTO mmpShortLinkManagementDTO = new MmpShortLinkManagementDTO();
			BeanCopyUtils.copyProperties(mmpShortLinkManagement,mmpShortLinkManagementDTO);
			mmpShortLinkManagementDTOS.add(mmpShortLinkManagementDTO);
		}
		return mmpShortLinkManagementDTOS;
	}

}
