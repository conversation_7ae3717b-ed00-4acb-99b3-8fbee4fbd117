package com.extracme.evcard.membership.core.service.license;


import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.common.HttpClientUtils;
import com.extracme.evcard.membership.config.ShoukalaServiceConfig;
import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.dto.DriverLicenseElementsAuthenticateLogDTO;
import com.extracme.evcard.membership.core.dto.DriverLicenseQueryResultDTO;
import com.extracme.evcard.membership.core.dto.DriverLicenseValidResultDto;
import com.extracme.evcard.membership.core.input.SaveDriverElementsAuthenticateLogInput;
import com.extracme.evcard.rpc.util.BeanCopyUtils;
import com.extracme.evcard.third.shoukala.entity.ShoukalaResp;
import com.extracme.evcard.third.shoukala.entity.ShoukalaResult;
import com.extracme.evcard.third.shoukala.utils.ShoukalaConstants;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class ShoukalaAuthService implements ILicenseAuthenticateService {

    protected Integer getSupplier() {
        return 4;
    }


    @Autowired
    protected OkHttpClient okHttpClient;

    @Autowired
    protected ShoukalaServiceConfig shoukalaServiceConfig;

    @Override
    public SaveDriverElementsAuthenticateLogInput authenticate(String driverCode, String name, String fileNo, Integer readTimeOut) {
        SaveDriverElementsAuthenticateLogInput logInput = new SaveDriverElementsAuthenticateLogInput();
        logInput.setName(name);
        logInput.setDriverCode(driverCode);
        logInput.setFileNo(fileNo);
        logInput.setLogType(0);
        logInput.setSupplier(getSupplier());
        logInput.setServiceName(shoukalaServiceConfig.getUrl());
        LicenseAuthenticateResult resp = licenseAuthenticate(name, driverCode, fileNo, readTimeOut);
        /**
         * 返回结果
         */
        logInput.setRequestId(resp.getRequestId());
        logInput.setResult(resp.getResult());
        logInput.setResultCode(String.valueOf(resp.getErrCode()));
        logInput.setResultMsg(resp.getReason());
        logInput.setResponse(resp.getResponse());
        //三要素认证结果：驾照号/姓名/档案编号，0不一致，1一致，如010
        String itemCheckResult = StringUtils.join(resp.getCardNoCheckResult(), resp.getNameCheckResult(), resp.getArchviesNoCheckResult());
        logInput.setElementsReviewItems(itemCheckResult);
        logInput.setLicenseStatusMsg(resp.getMsg());
        return logInput;
    }

    public LicenseAuthenticateResult licenseAuthenticate(String name, String cardNo, String archviesNo, Integer readTimeOut) {
        /**
         * 1. 调用供应商三要素核验接口
         */
        Map<String, Object> params = new HashMap<>();
        params.put("name", name);
        params.put("cardNo", cardNo);
        params.put("archviesNo", archviesNo);
        params.put("key", shoukalaServiceConfig.getEnkey());
        String response = StringUtils.EMPTY;
        ShoukalaResp resp;
        try {
            response = HttpClientUtils.httpGetRequest(shoukalaServiceConfig.getUrl(), params, readTimeOut);
            resp = JSON.parseObject(response, ShoukalaResp.class);
        } catch (Exception e) {
            log.error("收卡拉三要素核查：请求异常， driverCode=" + cardNo + ", result=" + response, e);
            resp = new ShoukalaResp(StringUtils.EMPTY, null, -1, null);
        }
        if(resp == null) {
            log.error("收卡拉三要素核查：请求失败， driverCode=" + cardNo + ", result=" + response);
            resp = new ShoukalaResp(StringUtils.EMPTY, null, -1, null);
        }
        /**
         * 2. 组织及转化核查结果
         */
        LicenseAuthenticateResult result = getLicenseCheckResult(resp);
        log.warn("收卡拉三要素核查：核查完成，name={}, cardNo={}, fileNo={}, result={}", name, cardNo, archviesNo, JSON.toJSON(result));
        return result;
    }

    private LicenseAuthenticateResult getLicenseCheckResult(ShoukalaResp resp) {
        String response = JSON.toJSONString(resp);
        ShoukalaResult verifyRes = resp.getResult();

        LicenseAuthenticateResult checkResult = new LicenseAuthenticateResult();
        /**
         * 组织认证结果
         * result=三要素结果(1认证一致 2不一致 3查无记录/无法核查 4异常情况)
         */
        String result;
        String licenseStatusMsg = StringUtils.EMPTY;
        if (resp.getError_code() == 0) {
            /**
             * 1. 三项均认证一致
             */
            if (ShoukalaConstants.ITEM_CHECK_RESULT_OK.equals(verifyRes.getNameCheckResult())
                    && ShoukalaConstants.ITEM_CHECK_RESULT_OK.equals(verifyRes.getCardNoCheckResult())
                    && ShoukalaConstants.ITEM_CHECK_RESULT_OK.equals(verifyRes.getArchviesNoCheckResult())) {
                result = LicenseAuthUtils.CHECK_OK;
                checkResult.setItems(true, true, true);
                //1. 三要素验证已经通过, 校验驾照状态描述
                if(StringUtils.isNotBlank(verifyRes.getMsg()) && !"证件参数错误".equals(verifyRes.getMsg())) {
                    /** 驾照参数有误，正常/超分/转出/暂扣/撤销/吊销/注销/违法未处理/事故未处理/停止使用/
                     * 协查/扣押/锁定/逾期未换证/延期换证/延期体检/逾期未体检/延期审验/逾期未审验/扣留/其他 **/
                    result = "1";
                    licenseStatusMsg = verifyRes.getMsg();
                }
            }
            /**
             * 2. 存在认证不一致
             */
            else if (ShoukalaConstants.ITEM_CHECK_RESULT_FAIL.equals(verifyRes.getNameCheckResult())
                    || ShoukalaConstants.ITEM_CHECK_RESULT_FAIL.equals(verifyRes.getCardNoCheckResult())
                    || ShoukalaConstants.ITEM_CHECK_RESULT_FAIL.equals(verifyRes.getArchviesNoCheckResult())) {
                result = LicenseAuthUtils.CHECK_FAIL;
                buildCheckItemResult(verifyRes, checkResult);
            } else {
                //三要素均不为全部一致， 且不包含不一致，-> 无法核查，则认为查无记录，需要重新查验。
                result = LicenseAuthUtils.CHECK_RETRY;
            }
        } else {
            result = LicenseAuthUtils.CHECK_UNKNOWN;
            if(ShoukalaConstants.ERROR_REQUEST_PARAMS.equals(String.valueOf(resp.getError_code()))) {
                //若为： 姓名/证件号/档案编号错误
                if (StringUtils.contains(resp.getReason(), ShoukalaConstants.MSG_NAME_INVALID) ||
                        StringUtils.contains(resp.getReason(), ShoukalaConstants.MSG_LICENSE_CODE_INVALID) ||
                        StringUtils.contains(resp.getReason(), ShoukalaConstants.MSG_FILE_NO_INVALID)) {
                    //此类错误等同于三要素-->认证不通过
                    result = LicenseAuthUtils.CHECK_FAIL;
                    buildCheckItemResult(resp.getReason(), checkResult);
                }
            }
        }
        checkResult.setRequestId(resp.getOrder_no());
        checkResult.setResult(result);
        checkResult.setErrCode(resp.getError_code());
        checkResult.setResponse(response);
        checkResult.setReason(resp.getReason());
        checkResult.setMsg(licenseStatusMsg);
        return checkResult;
    }


    @Override
    public SaveDriverElementsAuthenticateLogInput queryDriverDeduction(String driverCode, String name, String fileNo) {
        SaveDriverElementsAuthenticateLogInput logInput = new SaveDriverElementsAuthenticateLogInput();
        logInput.setName(name);
        logInput.setDriverCode(driverCode);
        logInput.setFileNo(fileNo);
        logInput.setLogType(1);
        logInput.setSupplier(getSupplier());
        logInput.setServiceName("shoukala-deduction-none");

        int resultCode = 0;
        String resultMsg = "success";
        String result = "{\"error_code\":0,\"reason\":\"success\",\"result\":\"0\"}";
        logInput.setResult(String.valueOf(resultCode));
        logInput.setResultCode(String.valueOf(resultCode));
        logInput.setResultMsg(resultMsg);
        logInput.setResponse(result);
        return logInput;
    }

    @Override
    public DriverLicenseQueryResultDTO queryDriverLicenseInfo(String driverCode, String name) {
        return null;
    }

    @Override
    public String buildAuthResultDetail(DriverLicenseElementsAuthenticateLogDTO logDTO) {
        String msg = StringUtils.EMPTY;
        try {
            String result = logDTO.getResult().trim();
            String itemCheckResult = logDTO.getElementsReviewItems();
            switch (result) {
                case LicenseAuthUtils.CHECK_OK:
                    if(StringUtils.isBlank(logDTO.getLicenseStatusMsg())) {
                        logDTO.setLicenseStatusMsg("正常");
                    }
                    return "认证成功";
                case LicenseAuthUtils.CHECK_FAIL:
                    StringBuffer sb = new StringBuffer();
                    if (itemCheckResult == null || itemCheckResult.length() != 3) {
                        return "认证不一致";
                    }
                    sb.append("【身份证号码核查结果】：").append(LicenseAuthUtils.itemToDesc(itemCheckResult, 0)).append(";   ");
                    sb.append("【姓名核查结果】：").append(LicenseAuthUtils.itemToDesc(itemCheckResult, 1)).append(";   ");
                    sb.append("【档案编号核查结果】：").append(LicenseAuthUtils.itemToDesc(itemCheckResult, 2)).append(";   ");
                    return sb.toString();
                case LicenseAuthUtils.CHECK_RETRY:
                    msg = "查无记录或无法核查 ";
                    if (StringUtils.isNotBlank(logDTO.getResultMsg())) {
                        msg += "，" + logDTO.getResultMsg();
                    }
                    break;
                case LicenseAuthUtils.CHECK_UNKNOWN:
                    msg = "核查异常 ";
                    if (StringUtils.equals(ShoukalaConstants.ERROR_NETWORK_TIMEOUT, logDTO.getResultCode().trim())) {
                        return "网络超时";
                    }
                    if (StringUtils.isNotBlank(logDTO.getResultMsg())) {
                        msg += logDTO.getResultMsg();
                    }
                    break;
            }
        }catch (Exception ex) {
            log.error("组织三要素核查结果异常", ex);
            msg = "查询失败 ";
        }
        return msg;
    }

    @Override
    public DriverLicenseValidResultDto checkDriverLicenseValid(String name, String cardNo, String archviesNo) {
        LicenseAuthenticateResult authenticateResult = licenseAuthenticate(name, cardNo, archviesNo, null);
        if(authenticateResult == null) {
            return null;
        }
        DriverLicenseValidResultDto driverLicenseValidResultDto = new DriverLicenseValidResultDto();
        BeanCopyUtils.copyProperties(authenticateResult, driverLicenseValidResultDto);
        return driverLicenseValidResultDto;
    }


    private void buildCheckItemResult(String reason, LicenseAuthenticateResult checkResult) {
        boolean codeInvalid = StringUtils.contains(reason, ShoukalaConstants.MSG_LICENSE_CODE_INVALID);
        boolean nameInvalid = StringUtils.contains(reason, ShoukalaConstants.MSG_NAME_INVALID);
        boolean fileNoInvalid = StringUtils.contains(reason, ShoukalaConstants.MSG_FILE_NO_INVALID);
        //返回为某一项不合法
        if(codeInvalid || nameInvalid || fileNoInvalid) {
            checkResult.setItems(!nameInvalid, !codeInvalid, !fileNoInvalid);
        }
    }

    private void buildCheckItemResult(ShoukalaResult verifyRes, LicenseAuthenticateResult checkResult){
        boolean codeFlag = BussinessConstants.AUTHENTICATE_RESULT_2.equals(verifyRes.getCardNoCheckResult());
        boolean nameFlag = BussinessConstants.AUTHENTICATE_RESULT_2.equals(verifyRes.getNameCheckResult());
        boolean fileNoFlag = BussinessConstants.AUTHENTICATE_RESULT_2.equals(verifyRes.getArchviesNoCheckResult());
        checkResult.setItems(!nameFlag, !codeFlag, !fileNoFlag);
    }


}
