package com.extracme.evcard.membership.core.enums;

public enum PostCertSubmitEnum {
    TO_FACE_REC(1, "资料提交/刷脸->待刷脸"),
    AUTO_AUTH_SUCEESS(2, "资料提交/刷脸->自动认证通过"),
    TO_MANUAL_REVIEW(3, "资料提交/刷脸-提交人工审核");

    PostCertSubmitEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /** 状态值 */
    private Integer value;

    /** 描述 */
    private String desc;

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public boolean eq(Integer value) {
        return this.value.equals(value);
    }
}
