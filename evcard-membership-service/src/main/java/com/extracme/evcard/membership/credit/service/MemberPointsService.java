package com.extracme.evcard.membership.credit.service;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.*;
import com.aliyun.openservices.ons.api.bean.ProducerBean;
import com.aliyun.openservices.ons.api.exception.ONSClientException;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.dto.*;
import com.extracme.evcard.membership.core.exception.MemberException;
import com.extracme.evcard.membership.credit.dao.MemberPointsPushRecordMapper;
import com.extracme.evcard.membership.credit.dto.*;
import com.extracme.evcard.membership.credit.model.MemberPointsPushRecord;
import com.extracme.evcard.mq.bean.EventEnum;
import com.extracme.evcard.mq.bean.MemPointsPushEnum;
import com.extracme.evcard.mq.bean.event.MemberInviteSuccess;
import com.extracme.evcard.mq.bean.event.MemberOrderAchieve;
import com.extracme.evcard.mq.bean.event.MemberPointsPush;
import com.extracme.evcard.mq.bean.event.OrderShareSuccess;
import com.extracme.evcard.protobuf.ProtobufUtil;
import com.extracme.evcard.redis.JedisUtil;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.dto.PageBeanDto;
import com.extracme.evcard.rpc.entity.HidLog;
import com.extracme.evcard.rpc.entity.LogPoint;
import com.extracme.evcard.rpc.exception.BusinessRuntimeException;
import com.extracme.evcard.rpc.messagepush.service.IMessagepushServ;
import com.extracme.evcard.rpc.messagepush.service.ISensorsdataService;
import com.extracme.evcard.rpc.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;


/**
 * 会员积分服务
 * <AUTHOR>
 * @Discription
 * @date 2020/8/12
 */
@Slf4j
@Service
public class MemberPointsService implements IMemberPointsService {
    @Resource(name = "producer")
    private ProducerBean producer;

    @Resource
    MemberPointsPushRecordMapper memberPointsPushRecordMapper;

    @Autowired
    ISensorsdataService sensorsdataService;

    @Autowired
    private IMessagepushServ messagepushServ;

    private static final String PREFIX_EVCARD_POINTS = "EVPOINTS_";

    /**
     * 服务提供方为：安悦e商城积分商城
     * @remark 可切换为其他供应商
     */
    @Resource(name = "anyolifeVipCreditsService")
    private IMemberCreditsServiceProvider memberCreditsServiceProvider;


    /**
     * member.points.offer.enable = 0则，
     * 推送积分发放事件，保留推送记录，但不调用第三方发放接口
     * 可通过重置消费位点恢复2日内积分信息
     */
    @Value("${member.points.push.enable}")
    private String MEMBER_POINTS_PUSH_ENABLE;

    @Override
    public BaseResponse asyncOfferPoints(MemberPointsOfferDto offerDto) {
        //若推送
        if(StringUtils.equals("0", MEMBER_POINTS_PUSH_ENABLE)) {
            return new BaseResponse();
        }
        if(checkEventExsist(offerDto)) {
            log.warn("积分事件推送, 已推送无需再次推送，authId={}，type={}, refKey={}.",
                    offerDto.getAuthId(), offerDto.getEventType(), offerDto.getEventRefSeq());
            return new BaseResponse();
        }
        /**
         * 1. 保存积分发放事件记录
         */
        MemberPointsPushRecord record = savePushRecord(offerDto);
        /**
         * 2. 推送EVCARD积分事件
         */
        MemberPointsPush memberPointsPush = new MemberPointsPush();
        String requestKey = genRequestKey(record.getId(), offerDto.getEventType(), offerDto.getEventRefSeq());
        BeanCopyUtils.copyProperties(offerDto, memberPointsPush);
        memberPointsPush.setRequestKey(requestKey);
        memberPointsPush.setRecordId(record.getId());
        boolean resp = pushMemberPointsToMq(requestKey, memberPointsPush);
        if(!resp) {
            MemberPointsPushRecord updateRecord = new MemberPointsPushRecord();
            updateRecord.setId(record.getId());
            updateRecord.setResult(-1);
            memberPointsPushRecordMapper.updateByPrimaryKeySelective(updateRecord);
        }
        return new BaseResponse();
    }

    private static final int[] EVENT_IDEMPOTENT = {MemPointsPushEnum.CARBON_ORDER_SHARE.getCode()};
    public Boolean checkEventExsist(MemberPointsOfferDto offerDto) {
        String authId = offerDto.getAuthId();
        String eventRefKey = offerDto.getEventRefSeq();
        Integer eventType = offerDto.getEventType();
        if(ArrayUtils.contains(EVENT_IDEMPOTENT, eventType) && StringUtils.isNotBlank(eventRefKey) ) {
            MemberPointsPushRecord record = memberPointsPushRecordMapper.selectOneByRefKey(authId, eventType, eventRefKey);
            return (record != null);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MemberPointsOfferResp offerPoints(MemberPointsOfferDto offerDto) throws MemberException {
        Long recordId = offerDto.getRecordId();
        if(recordId == null) {
            recordId = getIdFromRequestKey(offerDto.getRequestKey());
            if(recordId == null) {
                // 0. 保存积分发放事件记录
                MemberPointsPushRecord record = savePushRecord(offerDto);
                recordId = record.getId();
                offerDto.setRequestKey(genRequestKey(recordId, offerDto.getEventType(), offerDto.getEventRefSeq()));
            }
        }
        //清除缓存
        String redisKey = KEY_MEMBER_POINTS_PREFIX + offerDto.getPointsType() + "_" + offerDto.getAuthId();
        JedisUtil.del(redisKey);
        String gainRedisKey = KEY_MEMBER_GAINPOINTS_PREFIX +
                offerDto.getPointsType() + "_" + offerDto.getAuthId() + "_" + offerDto.getEventType();
        JedisUtil.del(gainRedisKey);
        if(offerDto.getPointsNum() != null) {
            gainRedisKey = gainRedisKey + "_" + offerDto.getPointsNum();
            JedisUtil.del(gainRedisKey);
        }

        /**
         * 1. 调用第三放积分发放接口，发放积分
         */
        MemberPointsOfferResp resp = memberCreditsServiceProvider.offerPoints(offerDto);
        log.info("MemberPoints：调用第三方积分发放接口: input={}，result={}", JSON.toJSONString(offerDto), JSON.toJSONString(resp));
        //发放结果：  1成功  2失败
        Integer result = resp.getSuccess() != null &&
                resp.getSuccess() && StringUtils.isNotBlank(resp.getPayOrderId()) ? 1 : 2;
        /**
         * 2. 更新积分发放结果
         */
        MemberPointsPushRecord updateRecord = new MemberPointsPushRecord();
        updateRecord.setId(recordId);
        updateRecord.setResult(result);
        updateRecord.setMessage(StringUtils.abbreviate(resp.getCode() + ":" + resp.getMessage(), 128));
        updateRecord.setResultCode(resp.getCode());
        updateRecord.setMessage(StringUtils.abbreviate(resp.getMessage(), 128));
        updateRecord.setPayOrderId(resp.getPayOrderId());
        updateRecord.setGainPoints(resp.getGainPoints());
        updateRecord.setExpireDate(resp.getExpireDate());
        updateRecord.setUpdateTime(new Date());
        updateRecord.setUpdateOperId(0L);
        updateRecord.setUpdateOperName("evcard-mall");
        memberPointsPushRecordMapper.updateByPrimaryKeySelective(updateRecord);
        try {
            if (result == 1 && offerDto.getMessageFlag() == 1) {
                Map<String, String> param = new HashMap<>();
                if (MemPointsPushEnum.MD_ORDER_RETURN_ASSESS.getCode().equals(offerDto.getEventType()) && Integer.valueOf(2).equals(offerDto.getPointsNum())) {
                    param.put("pointNum", "50");
                }
                messagepushServ.syncPush(offerDto.getAuthId(), 0, 286, 2, param, "evcard-membership");
            }
        } catch (Exception e) {
            log.error("发放积分到账消息通知异常", e);
        }
        HidLog.membership(LogPoint.MEMBER_POINTS_PUSH,"推送EVCARD积分发放事件，input=" + JSON.toJSONString(offerDto), offerDto.getAuthId());
        return resp;
    }

    @Override
    public PageBeanDto<MemberPointsHistoryDto> queryUserHistoryPage(MemberPointsHistoryQueryDto queryDto) {
        //请求积分服务获得积分变更数据
        PageBeanDto<MemberPointsHistoryDto> resp = memberCreditsServiceProvider.queryUserHistoryPage(queryDto);
        //组织积分发放详情
        log.info("MemberPoints：调用第三方积分履历查询接口: input={}，result={}", JSON.toJSONString(queryDto), JSON.toJSONString(resp));
        if(CollectionUtils.isEmpty(resp.getList())){
            return resp;
        }
        List<MemberPointsHistoryDto> list = resp.getList();
        Map<Long, Integer> recordIds = new HashMap<>();
        Map<String, Integer> refKeys = new HashMap<>();

        for(int i = 0; i < list.size(); i ++) {
            MemberPointsHistoryDto dto = list.get(i);
            dto.setCauseTypeName(getCauseTypeName(dto));
            if(StringUtils.isBlank(dto.getChangeReason())) {
                dto.setChangeReason(dto.getCauseTypeName());
            }
            if("02".equals(dto.getOptionType())) {
                if(StringUtils.equals(dto.getCauseType(), String.valueOf(MemPointsPushEnum.CARBON_ORDER_SHARE.getCode()))) {
                    String key = StringUtils.substringAfterLast(dto.getRecordNo(), "_");
                    refKeys.put(key, i);
                    continue;
                }
                //evcard触发积分发放
                Long recordId = getIdFromRequestKey(dto.getRecordNo());
                if(recordId != null) {
                    recordIds.put(recordId, i);
                    dto.setPushRecordId(recordId);
                }
            }
        }
        //组织发放记录：发放详情
        if(CollectionUtils.isNotEmpty(recordIds.keySet())) {
            List<MemberPointsPushRecord> pushRecords = memberPointsPushRecordMapper.queryByIds(recordIds.keySet());
            for(MemberPointsPushRecord record : pushRecords) {
                MemberPointsHistoryDto dto = list.get(recordIds.get(record.getId()));
                MemberPointsPushRecordDto recordDto = new MemberPointsPushRecordDto();
                BeanCopyUtils.copyProperties(record, recordDto);
                dto.setPushRecord(recordDto);
            }
        }
        if(CollectionUtils.isNotEmpty(refKeys.keySet())) {
            List<MemberPointsPushRecord> pushRecords = memberPointsPushRecordMapper.queryByIdRefKeys(refKeys.keySet());
            for(MemberPointsPushRecord record : pushRecords) {
                MemberPointsHistoryDto dto = list.get(refKeys.get(record.getEventRefSeq()));
                MemberPointsPushRecordDto recordDto = new MemberPointsPushRecordDto();
                BeanCopyUtils.copyProperties(record, recordDto);
                dto.setPushRecord(recordDto);
            }
        }
        for(MemberPointsHistoryDto dto : list) {
            if(StringUtils.isBlank(dto.getChangeReason())) {
                dto.setChangeReason(dto.getCauseTypeName());
            }
            if(dto.getPushRecord() != null) {
                dto.setPushRecordId(dto.getPushRecord().getId());
                dto.setPushDetails(buildPushDetails(dto.getPushRecord()));
            }
        }
        return resp;
    }


    private static final String KEY_MEMBER_POINTS_PREFIX = "evpoints_";
    private static final int EXPIRE_POINTS_SENCONDS = 60 * 10;
    @Override
    public MemberPointsAccountDto queryUserAccount(MemberPointsAccountQueryDto queryDto) {
        log.debug("查询会员积分，authId=" + queryDto.getAuthId());
        MemberPointsAccountDto memberPoints;
        try {
            if (StringUtils.isBlank(queryDto.getPointsType())) {
                queryDto.setPointsType("01");
            }
            String redisKey = KEY_MEMBER_POINTS_PREFIX + queryDto.getPointsType() + "_" + queryDto.getAuthId();
            String value = JedisUtil.get(redisKey);
            if (value == null) {
                synchronized (this) {
                    String cacheValue = JedisUtil.get(redisKey);
                    //redis中存在，则直接从redis中读取。
                    if (cacheValue != null) {
                        memberPoints = JSON.parseObject(cacheValue, MemberPointsAccountDto.class);
                    } else {
                        //redis中不存在，则读数据库，并更新缓存。
                        memberPoints = memberCreditsServiceProvider.queryUserAccount(queryDto);
                        JedisUtil.setCover(redisKey, JSON.toJSONString(memberPoints), EXPIRE_POINTS_SENCONDS);
                    }
                    return memberPoints;
                }
            }
            memberPoints = JSON.parseObject(value, MemberPointsAccountDto.class);
        }catch (Exception ex) {
            log.warn("查询会员积分失败，authId=" + queryDto.getAuthId(), ex);
            memberPoints = new MemberPointsAccountDto();
            memberPoints.setCredits(0);
            memberPoints.setUserStatus("0");
            memberPoints.setStatus("0");
        }
        return memberPoints;
    }



    private static final String KEY_MEMBER_GAINPOINTS_PREFIX = "evpoints_gain_";
    private static final int EXPIRE_GAINPOINTS_SENCONDS = 60 * 10;
    @Override
    public MemberPointsGainQueryResp getGainCredits(MemberPointsGainQueryDto queryDto) {
        log.debug("获取会员可获取的积分数值，input=" + JSON.toJSONString(queryDto));
        MemberPointsGainQueryResp gainPoints;
        if (StringUtils.isBlank(queryDto.getPointsType())) {
            queryDto.setPointsType("01");
        }
        String redisKey = KEY_MEMBER_GAINPOINTS_PREFIX +
                queryDto.getPointsType() + "_" + queryDto.getAuthId() + "_" + queryDto.getFeeType();
        if(queryDto.getGainCredits() != null) {
            redisKey = redisKey + "_" + queryDto.getGainCredits();
        }
        String value = JedisUtil.get(redisKey);
        if (value == null) {
            synchronized (this) {
                String cacheValue = JedisUtil.get(redisKey);
                //redis中存在，则直接从redis中读取。
                if (cacheValue != null) {
                    gainPoints = JSON.parseObject(cacheValue, MemberPointsGainQueryResp.class);
                } else {
                    //redis中不存在，则读数据库，并更新缓存。
                    gainPoints = queryGainCredits(queryDto);
                    JedisUtil.setCover(redisKey, JSON.toJSONString(gainPoints), EXPIRE_GAINPOINTS_SENCONDS);
                }
                return gainPoints;
            }
        }
        gainPoints = JSON.parseObject(value, MemberPointsGainQueryResp.class);
        return gainPoints;
    }


    @Override
    public MemberPointsGainQueryResp queryGainCredits(MemberPointsGainQueryDto queryDto) {
        log.debug("查询会员可获取的积分数值，authId=" + queryDto.getAuthId());
        MemberPointsGainQueryResp queryResp = null;
        try {
            if (StringUtils.isBlank(queryDto.getPointsType())) {
                queryDto.setPointsType("01");
            }
            if(MemPointsPushEnum.CARBON_ORDER_SHARE.getCode().equals(queryDto.getFeeType())) {
                if(StringUtils.isBlank(queryDto.getEventRefSeq())) {
                    throw BusinessRuntimeException.PARAM_EXEPTION;
                }
            }
            queryResp = memberCreditsServiceProvider.queryGainCredits(queryDto);
        }catch (Exception ex) {
            log.warn("查询会员可获取的积分数值，authId=" + queryDto.getAuthId(), ex);
        }
        if(queryResp == null || queryResp.getGainCredits() == null) {
            queryResp = new MemberPointsGainQueryResp();
            queryResp.setGainCredits(0);
        }
        return queryResp;
    }

    public MemberPointsPushRecord savePushRecord(MemberPointsOfferDto offerDto){
        MemberPointsPushRecord record = new MemberPointsPushRecord();
        BeanCopyUtils.copyProperties(offerDto, record);
        record.setDetails(ComUtil.toJSONString(offerDto.getDetails()));
        record.setCreateOperId(0L);
        record.setCreateOperName(offerDto.getAuthId());
        memberPointsPushRecordMapper.insertSelective(record);
        return record;
    }

    private static Long getIdFromRequestKey(String requestKey) {
        if(StringUtils.isBlank(requestKey) || !StringUtils.startsWith(requestKey, PREFIX_EVCARD_POINTS)) {
            return null;
        }
        String key = StringUtils.substringAfter(requestKey, PREFIX_EVCARD_POINTS);
        try {
            if (StringUtils.isNotBlank(key) && !StringUtils.contains(key, "_")) {
                return Long.valueOf(key);
            }
        }catch (Exception e) {}
        return null;
    }

    private static String genRequestKey(Long recordId, Integer eventType, String eventRefKey) {
        if(ArrayUtils.contains(EVENT_IDEMPOTENT, eventType)) {
            return PREFIX_EVCARD_POINTS + eventType + "_" + eventRefKey;
        }
        return PREFIX_EVCARD_POINTS + recordId;
    }

    private static String buildPushDetails(MemberPointsPushRecordDto record){
        MemPointsPushEnum pushEnum = MemPointsPushEnum.get(record.getEventType());
        if(pushEnum == null) {
            return StringUtils.EMPTY;
        }
        StringBuffer desc = new StringBuffer();
        switch (pushEnum){
            case ORDER_PAY:
                BigDecimal realAmount = record.getAmount().divide(new BigDecimal(100));
                desc.append(String.format("订单号%s，实付金额%s元", record.getEventRefSeq(), realAmount));
                break;
            case RECHARGE_DEPOSIT:
                desc.append(String.format("充值金额%s元", record.getAmount()));
                break;
            case RECHARGE_E_AMOUNT:
                desc.append(String.format("交易号%s，充值e币金额%s元", record.getEventRefSeq(),
                        record.getAmount().divide(new BigDecimal(100), 2, RoundingMode.CEILING)));
                break;
            case VEHICLE_FEEDBACK:
                if(StringUtils.isNotBlank(record.getEventRefSeq())){
                    desc.append("订单编号").append(record.getEventRefSeq());
                }
                if(record.getAmount() != null){
                    desc.append("(图片数量").append(record.getAmount()).append(")");
                }
                break;
            case TAKE_PHOTO_RETURN_VEHICLE:
            case ORDER_ASSESS:
            case ORDER_SHARE:
            case MEMBER_FIRST_ORDER:
                if(StringUtils.isNotBlank(record.getEventRefSeq())){
                    desc.append("订单编号").append(record.getEventRefSeq());
                }
                if(record.getAmount() != null){
                    desc.append("(金额").append(record.getAmount()).append(")");
                }
                break;
            case MEMBER_INVITATION:
                desc.append("新用户").append(record.getEventRefSeq());
                MemberInviteSuccess inviteSuccess = ComUtil.parseObjectNonEx(record.getDetails(), MemberInviteSuccess.class);
                if(inviteSuccess != null) {
                    desc.append("，手机号").append(inviteSuccess.getMobilePhone());
                    desc.append("，注册时间").append(inviteSuccess.getRegisterTime());
                }
                break;
            case ORDER_ACHIEVEMENT:
                desc.append("订单号").append(record.getEventRefSeq());
                MemberOrderAchieve orderAchieve = ComUtil.parseObjectNonEx(record.getDetails(), MemberOrderAchieve.class);
                if(orderAchieve != null) {
                    desc.append("，任务编号").append(orderAchieve.getTaskId()).append("，级别").append(orderAchieve.getLevel());
                }
                break;
            case MEMBER_TITLE_REWARD:
//                String detail = record.getDetails();
//                detail = StringUtils.replace(detail, "carbonTitle: ", "领取节碳称号奖励：");
//                desc.append(detail);
                break;
            case CARBON_ORDER_SHARE:
                if(StringUtils.isNotBlank(record.getEventRefSeq())){
                    desc.append("订单编号").append(record.getEventRefSeq());
                }
                break;
        }
        return desc.toString();
    }

    private static String getCauseTypeName(MemberPointsHistoryDto record){
        if("01".equals(record.getOptionType())) {
            if("01".equals(record.getCauseType())) {
                //TODO 积分消费用途
                return "商品购物";
            }
        }
        else if("02".equals(record.getOptionType())) {
            try {
                MemPointsPushEnum pushEnum = MemPointsPushEnum.get(Integer.valueOf(record.getCauseType()));
                if (pushEnum != null) {
                    return pushEnum.getTitle();
                }
            }catch (Exception ex) {}
        }
        return StringUtils.EMPTY;
    }


    @Value("${ons.credits.topic}")
    private String EPOINTS_PUSH_EVENT_TOPIC;

    /**
     * 推送积分发放事件
     * @param pushBean
     */
    private boolean pushMemberPointsToMq(String requestKey, MemberPointsPush pushBean){
        Message message = new Message();
        message.setTopic(EPOINTS_PUSH_EVENT_TOPIC);
        message.setTag(EventEnum.MEMBER_POINTS_PUSH.getTag());
        message.setBody(ProtobufUtil.serializeProtobuf(pushBean));
        String messageKey = "mem#" + pushBean.getEventType() + "_" + requestKey + "_" + UUID.randomUUID();
        message.setKey(messageKey);
        try {
            producer.send(message);
            return true;
        }catch (Exception e) {
            log.warn("MemberPoints：推送EVCARD积分发放事件失败，重试一次: requestKey=" + requestKey + "，message=" + JSON.toJSONString(pushBean), e);
            try{
                producer.send(message);
                return true;
            }catch (Exception ex) {
                HidLog.membership(LogPoint.MEMBER_POINTS_PUSH,"推送EVCARD积分发放事件失败，type=" + pushBean.getEventType(), pushBean.getAuthId(), false);
                log.error("MemberPoints：推送EVCARD积分发放事件失败，重试仍然失败: requestKey=" + requestKey + "，message=" + JSON.toJSONString(pushBean), e);
            }
        }
        return false;
    }

    private void asyncSendToMq(Message message){
        producer.sendAsync(message, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
            }
            @Override
            public void onException(OnExceptionContext onExceptionContext) {
                ONSClientException exception = onExceptionContext.getException();
                log.error(exception.getMessage(),exception);
                //重试
                SendResult sendResult = producer.send(message);
                log.info(sendResult.toString());
            }
        });
    }

    @Override
    public List<UserTitleUpgradePointsDto> queryGainCredits(UserTitlePointsQueryDto queryDto) {
        log.debug("查询会员可获取的称号奖励积分数值，authId=" + queryDto.getAuthId());
        List<UserTitleUpgradePointsDto> queryResp = null;
        try {
            queryResp = memberCreditsServiceProvider.queryCarbonReduceTitlePoints(queryDto);
        }catch (Exception ex) {
            log.warn("查询会员可获取的称号奖励积分数值，authId=" + queryDto.getAuthId(), ex);
        }
        return queryResp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse offerTitleRewardPoints(UserTitlePointsOfferDto offerDto) throws MemberException {
        if(CollectionUtils.isEmpty(offerDto.getUpgradeRecords())) {
            return new BaseResponse();
        }
        for(UserTitleUpgradeDto record : offerDto.getUpgradeRecords()) {
            UserTitlePointsOfferSingleDto singleDto = new UserTitlePointsOfferSingleDto();
            BeanCopyUtils.copyProperties(record, singleDto);
            singleDto.setAuthId(offerDto.getAuthId());
            singleDto.setTitleClass(offerDto.getTitleClass());
            /**
             * 推送积分发放消息，推送失败，提示发放失败，回滚推送记录
             */
            boolean result = pushUserTitlePointsToMq(singleDto);
            if(!result) {
                throw new MemberException(-1, "领取失败");
            }
        }
        return new BaseResponse();
    }

    /**
     * 同步发放单笔荣誉称号奖励
     * @param offerDto
     */
    @Override
    public MemberPointsOfferResp offerTitleRewardPointsSync(UserTitlePointsOfferSingleDto offerDto) {
        //发放前清除用户积分缓存
        String redisKey = KEY_MEMBER_POINTS_PREFIX + "01" + "_" + offerDto.getAuthId();
        JedisUtil.del(redisKey);

        //暂时仅仅支持节碳称号奖励
        if(offerDto.getTitleClass() != 0) {
            return new MemberPointsOfferResp();
        }
        MemberPointsOfferResp resp = memberCreditsServiceProvider.offerCarbonReduceTitlePoints(offerDto);
        log.info("MemberPoints：调用第三方积分发放接口: input={}，result={}", JSON.toJSONString(offerDto), JSON.toJSONString(resp));
        //if(!resp.getSuccess()) {
        if(!resp.getSuccess() && resp.getRetry() == 1) {
            throw new MemberException(-1, "调用第三方积分发放接口, 申请积分发放失败");
        }

        if(resp.getSuccess()) {
            /**
             * 记录积分推送记录
             */
            Date now = new Date();
            MemberPointsPushRecord record = new MemberPointsPushRecord();
            record.setAuthId(offerDto.getAuthId());
            record.setEventType(MemPointsPushEnum.MEMBER_TITLE_REWARD.getCode());
            record.setEventRefSeq("Title_" + offerDto.getTitleClass() + "_" + String.valueOf(offerDto.getId()));
            record.setPointsNum(0);
            String detail = buildUserTitleUpgradeDesc(offerDto.getTitleClass(), offerDto);
            record.setDetails(detail);
            record.setCreateOperId(0L);
            record.setCreateOperName(offerDto.getAuthId());
            record.setCreateTime(now);
            record.setUpdateOperId(0L);
            record.setUpdateOperName(offerDto.getAuthId());
            record.setUpdateTime(now);
            memberPointsPushRecordMapper.insertSelective(record);
        }

        /**
         * 2. 不更新积分发放结果
         */
        //Integer result = resp.getSuccess() != null && resp.getSuccess() && StringUtils.isNotBlank(resp.getPayOrderId()) ? 1 : 2;
        //MemberPointsPushRecord updateRecord = new MemberPointsPushRecord();
        //updateRecord.setId(recordId);
        //updateRecord.setResult(result);
        //updateRecord.setMessage(StringUtils.abbreviate(resp.getCode() + ":" + resp.getMessage(), 128));
        //updateRecord.setResultCode(resp.getCode());
        //updateRecord.setMessage(resp.getMessage());
        //updateRecord.setPayOrderId(resp.getPayOrderId());
        //updateRecord.setGainPoints(resp.getGainPoints());
        //updateRecord.setExpireDate(resp.getExpireDate());
        //updateRecord.setUpdateTime(new Date());
        //updateRecord.setUpdateOperId(0L);
        //updateRecord.setUpdateOperName("evcard-mall");
        //memberPointsPushRecordMapper.updateByPrimaryKeySelective(updateRecord);
        HidLog.membership(LogPoint.MEMBER_POINTS_PUSH,"推送EVCARD用户荣誉称号积分发放事件，input=" + JSON.toJSONString(offerDto), offerDto.getAuthId());
        return resp;
    }

    private String buildUserTitleUpgradeDesc(int titleClass, UserTitleUpgradeDto dto) {
        if(titleClass == 0) {
            String ori = dto.getOriginTitle().buildTitle();
            String end = dto.getNewTitle().buildTitle();
            String msg = "carbonTitle: " + ori + "->" + end;
            return msg;
        }
        return StringUtils.EMPTY;
    }

    private boolean pushUserTitlePointsToMq(UserTitlePointsOfferSingleDto pushBean){
        Message message = new Message();
        message.setTopic(EPOINTS_PUSH_EVENT_TOPIC);
        message.setTag(EventEnum.MEMBER_TITLE_REWARD_POINTS.getTag());
        message.setBody(ProtobufUtil.serializeProtobuf(pushBean));
        String messageKey = "mem#titlePoints_" + pushBean.getTitleClass() + pushBean.getId() + UUID.randomUUID();
        message.setKey(messageKey);
        try {
            producer.send(message);
            return true;
        }catch (Exception e) {
            log.warn("MemberPoints：推送EVCARD用户荣誉称号积分发放事件失败，重试一次: id=" +
                    pushBean.getId() + "，message=" + JSON.toJSONString(pushBean), e);
            try{
                producer.send(message);
                return true;
            }catch (Exception ex) {
                HidLog.membership(LogPoint.MEMBER_POINTS_PUSH,"推送EVCARD用户荣誉称号积分发放事件失败，id=" +
                        pushBean.getId(), pushBean.getAuthId(), false);
                log.error("MemberPoints：推送EVCARD用户荣誉称号积分发放事件失败，重试仍然失败: id=" + pushBean.getId() +
                        "，message=" + JSON.toJSONString(pushBean), e);
            }
        }
        return false;
    }
}
