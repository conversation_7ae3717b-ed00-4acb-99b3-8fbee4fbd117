package com.extracme.evcard.membership.core.service;


import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.dao.MmpUserOperationLogMapper;
import com.extracme.evcard.membership.core.dao.UserOperatorLogMapper;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.dto.OperatorDto;
import com.extracme.evcard.membership.core.enums.MemOperateTypeEnum;
import com.extracme.evcard.membership.core.input.UserOperationLogInput;
import com.extracme.evcard.membership.core.model.MmpUserOperationLog;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

@Component
public class MemberOptLogService {

    @Resource
    private UserOperatorLogMapper userOperatorLogMapper;

    @Resource
    private MembershipInfoMapper membershipInfoMapper;

    @Resource
    private MmpUserOperationLogMapper mmpUserOperationLogMapper;

    /**
     * 由MembershipService迁移该方法
     * @param
     * <AUTHOR>
     */
    public void saveUserOperationLog(UserOperationLogInput userOperationLogInput) {
        Long userId = userOperationLogInput.getUserId();
        String authId = userOperationLogInput.getAuthId();
        //获取会员主键
        if(null == userOperationLogInput.getUserId()
                && StringUtils.isNotBlank(userOperationLogInput.getAuthId())) {
            MembershipInfoWithBLOBs membershipInfo = membershipInfoMapper.selectByAuthId(authId,
                    userOperationLogInput.getMembershipType());
            if(membershipInfo != null){
                userId = membershipInfo.getPkId();
            }
        }
        //操作时间缺省为当前时间
        if(null == userOperationLogInput.getOperationTime()) {
            userOperationLogInput.setOperationTime(new Date());
        }
        if(null == userOperationLogInput.getOperatorId()) {
            userOperationLogInput.setOperatorId(-1L);
        }
        //日志记录写入
        if(null != userId) {
            MmpUserOperationLog mmpUserOperationLog = new MmpUserOperationLog();
            BeanUtils.copyProperties(userOperationLogInput,mmpUserOperationLog);
            mmpUserOperationLog.setUserId(userId);
            mmpUserOperationLog.setCreateOperId(userOperationLogInput.getOperatorId());
            mmpUserOperationLog.setUpdateOperId(userOperationLogInput.getOperatorId());
            mmpUserOperationLog.setCreateTime(userOperationLogInput.getOperationTime());
            mmpUserOperationLog.setUpdateTime(userOperationLogInput.getOperationTime());
            mmpUserOperationLog.setCreateOperName(userOperationLogInput.getOperator());
            mmpUserOperationLog.setUpdateOperName(userOperationLogInput.getOperator());
            mmpUserOperationLogMapper.insertSelective(mmpUserOperationLog);
        }
    }


    public void saveCertOptLog(String content, MembershipBasicInfo member, MemOperateTypeEnum opType,
                               String refKey1, String refKe2,
                               boolean saveOldLog, OperatorDto operator) {
        //已经有单独的认证日志，是否有必要再写旧日志表
        if(saveOldLog) {
            ComUtil.insertOperatorLog(content, member.getAuthId(), null,
                    StringUtils.abbreviate(operator.getOperatorName(), 20), userOperatorLogMapper);
        }
        UserOperationLogInput userOperationLogInput = new UserOperationLogInput();
        userOperationLogInput.setOperationType(opType.getCode());
        userOperationLogInput.setOperationContent(content);
        userOperationLogInput.setOperationTime(new Date());
        userOperationLogInput.setOperator(StringUtils.abbreviate(operator.getOperatorName(), 20));
        userOperationLogInput.setOperatorId(operator.getOperatorId());
        userOperationLogInput.setRefKey1(refKey1);
        userOperationLogInput.setRefKey2(refKe2);
        userOperationLogInput.setUserId(member.getPkId());
        //系统自动审核通过无单独入口，此处不单独记录日志
        saveUserOperationLog(userOperationLogInput);
    }
}
