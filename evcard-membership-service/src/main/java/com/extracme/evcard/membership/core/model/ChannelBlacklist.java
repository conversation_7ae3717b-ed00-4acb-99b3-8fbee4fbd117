package com.extracme.evcard.membership.core.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *  渠道黑名单表
 */
@Data
public class ChannelBlacklist implements Serializable {

    /**
     * 主键
     *
     */
    private Long id;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 手机号
     */
    private String mobilePhone;

    /**
     * 黑名单类型：1=黑名单
     */
    private Integer blacklistType;

    /**
     * 证件类型: 1=身份证 2=其他
     */
    private Integer certificateType;

    /**
     * 证件号码
     */
    private String certificateNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 启用状态:1= 启用 2=禁用
     */
    private Integer enableStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 黑名单来源：1=手动添加，2=会员系统同步
     */
    private Integer blacklistSource;
}
