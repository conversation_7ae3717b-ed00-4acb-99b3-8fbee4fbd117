package com.extracme.evcard.membership.core.model;

import java.util.Date;

public class DriverLicenseElementsReauthenticateList {
    private Long id;

    private Long userId;

    private Integer authenticateStatus;

    private Integer licenseStatus;

    private String licenseStatusMsg;

    private Date lastestAuthenticateTime;

    private Date nextAuthenticateTime;

    private Integer retryTimes;

    private Integer status;

    private String miscDesc;

    private Date createTime;

    private Long createOperId;

    private String createOperName;

    private Date updateTime;

    private Long updateOperId;

    private String updateOperName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getAuthenticateStatus() {
        return authenticateStatus;
    }

    public void setAuthenticateStatus(Integer authenticateStatus) {
        this.authenticateStatus = authenticateStatus;
    }

    public Integer getLicenseStatus() {
        return licenseStatus;
    }

    public void setLicenseStatus(Integer licenseStatus) {
        this.licenseStatus = licenseStatus;
    }

    public String getLicenseStatusMsg() {
        return licenseStatusMsg;
    }

    public void setLicenseStatusMsg(String licenseStatusMsg) {
        this.licenseStatusMsg = licenseStatusMsg;
    }

    public Date getLastestAuthenticateTime() {
        return lastestAuthenticateTime;
    }

    public void setLastestAuthenticateTime(Date lastestAuthenticateTime) {
        this.lastestAuthenticateTime = lastestAuthenticateTime;
    }

    public Integer getRetryTimes() {
        return retryTimes;
    }

    public void setRetryTimes(Integer retryTimes) {
        this.retryTimes = retryTimes;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMiscDesc() {
        return miscDesc;
    }

    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateOperId() {
        return createOperId;
    }

    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    public String getCreateOperName() {
        return createOperName;
    }

    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdateOperId() {
        return updateOperId;
    }

    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    public String getUpdateOperName() {
        return updateOperName;
    }

    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }

    public Date getNextAuthenticateTime() {
        return nextAuthenticateTime;
    }

    public void setNextAuthenticateTime(Date nextAuthenticateTime) {
        this.nextAuthenticateTime = nextAuthenticateTime;
    }
}