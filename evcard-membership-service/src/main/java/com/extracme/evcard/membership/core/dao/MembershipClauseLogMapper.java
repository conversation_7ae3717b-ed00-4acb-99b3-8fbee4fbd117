package com.extracme.evcard.membership.core.dao;


import com.extracme.evcard.membership.core.model.MembershipClauseLog;
import com.extracme.evcard.membership.core.model.MembershipClauseLogExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MembershipClauseLogMapper {
    int countByExample(MembershipClauseLogExample example);

    int deleteByExample(MembershipClauseLogExample example);

    int deleteByPrimaryKey(Long id);

    int insert(MembershipClauseLog record);

    int insertSelective(MembershipClauseLog record);

    List<MembershipClauseLog> selectByExample(MembershipClauseLogExample example);

    MembershipClauseLog selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MembershipClauseLog record, @Param("example") MembershipClauseLogExample example);

    int updateByExample(@Param("record") MembershipClauseLog record, @Param("example") MembershipClauseLogExample example);

    int updateByPrimaryKeySelective(MembershipClauseLog record);

    int updateByPrimaryKey(MembershipClauseLog record);

    /**
     * 通过邀请码查询相应的shareId
     * @param invitationCode
     * @return
     */
    String queryShareIdByInvitationCode(String invitationCode);

    List<MembershipClauseLog> queryListsByAuthIdAndCreateTime(@Param("authId") String authId,@Param("createTime") String createTime);
}