package com.extracme.evcard.membership.core.service.auth.login;

import com.alibaba.dubbo.common.Constants;
import com.alibaba.fastjson.JSON;
import com.baosight.iplat4j.security.util.Crypto;
import com.extracme.evcard.bvm.bo.ServiceResult;
import com.extracme.evcard.bvm.service.IAgencyMemberService;
import com.extracme.evcard.bvm.to.InitialAgencyMembershipTO;
import com.extracme.evcard.membership.common.MidGenerator;
import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.dao.CityMapper;
import com.extracme.evcard.membership.core.dao.SecondAppKeyManagerMapper;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.enums.MemberTypeEnum;
import com.extracme.evcard.membership.core.input.ThirdLoginContext;
import com.extracme.evcard.membership.core.input.ThirdLoginInput;
import com.extracme.evcard.membership.core.input.ThirdLoginOtherDto;
import com.extracme.evcard.membership.core.model.City;
import com.extracme.evcard.membership.core.model.SecondAppKeyManager;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs;
import com.extracme.evcard.redis.JedisLock;
import com.extracme.evcard.redis.JedisUtil;
import com.extracme.evcard.rpc.enums.StatusCode;
import com.extracme.evcard.rpc.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import static com.extracme.evcard.membership.common.ComUtil.*;

/**
 * 赛乐通 登录注册
 */
@Slf4j
@Service
public class SaicTravelLogin extends ThirdLoginAbstract {

    @Autowired
    private IMemberShipService memberShipService;
    @Autowired
    private CityMapper cityMapper;
    @Resource
    private MidGenerator midGenerator;
    @Autowired
    private MembershipInfoMapper membershipInfoMapper;
    @Resource
    private SecondAppKeyManagerMapper secondAppKeyManagerMapper;

    @Value("${slt.agencyId:00BX}")
    private String agencyId;
    @Value("${slt.default.roleId:1}")
    private String roleId;

    @Resource(name = "agencyMemberService")
    private IAgencyMemberService agencyMemberService;

    @Override
    public String getSecondAppKey() {
        return BussinessConstants.SAI_LE_TONG;
    }

    @Override
    public String getDescription() {
        return "赛乐通";
    }

    @Override
    public void paramCheck(ThirdLoginContext context) throws BusinessException {
        ThirdLoginOtherDto otherDto = context.getOtherDto();
        ThirdLoginInput input = context.getInput();

        String cityId = input.getCityId();
        String mobilePhone = input.getMobilePhone();
        String appKey = input.getAppKey();

        if (StringUtils.isBlank(mobilePhone) || StringUtils.isBlank(appKey)) {
            throw new BusinessException(-1, "入参格式不能为空");
        }

        if (!pattern.matcher(mobilePhone).matches()) {
            throw new BusinessException(StatusCode.MOBILE_FORMATE_ERROR);
        }

        SecondAppKeyManager secondAppKeyManager = secondAppKeyManagerMapper.selectBySecondAppKey(appKey);
        if (secondAppKeyManager == null) {
            throw new BusinessException(StatusCode.APPKEY_INVALID);
        }

        otherDto.setFirstAppKey(secondAppKeyManager.getFirstAppKey());
        otherDto.setPlatFormId(secondAppKeyManager.getPlatformId());

        try {
            String cityName = "";
            if (StringUtils.isNotEmpty(cityId)) {
                City city = cityMapper.getCityByCityId(cityId);
                if (city != null) {
                    cityName = city.getCity();
                }
            }
            if (StringUtils.isBlank(cityName)) {
                cityName = "上海市";
            }

            otherDto.setCityName(cityName);
        } catch (Exception e) {
            log.error("赛乐通，获取注册城市异常，input={}", JSON.toJSONString(input), e);
        }

    }

    @Override
    public void sendMessage(ThirdLoginContext context) {
        // 不处理
    }

    @Override
    protected MembershipBasicInfo doRegisterMembershipInfo(ThirdLoginContext context) throws BusinessException {
        ThirdLoginOtherDto otherDto = context.getOtherDto();
        String cityName = otherDto.getCityName();
        ThirdLoginInput input = context.getInput();
        log.info("赛乐通，doRegisterMembershipInfo,input[{}]", JSON.toJSONString(input));
        String mobilePhone = input.getMobilePhone();
        String appKey = input.getAppKey();
        String userName = input.getUserName();

        String redisKey = mobilePhone + Constants.COMMA_SEPARATOR + appKey + Constants.COMMA_SEPARATOR + Constants.COMMA_SEPARATOR + getMembershipType();
        Jedis jedis = null;
        JedisLock jedisLock = null;
        try {
            //防止手机号并发注册
            jedis = JedisUtil.getJedis();
            jedisLock = new JedisLock(redisKey);
            if (!jedisLock.acquire(jedis)) {
                throw new BusinessException(StatusCode.SYSTEM_ERROR);
            }

            // 密码校验
            String password = mobilePhone.substring(1, 6).trim();
            //加密密码
            password = Crypto.encryptDES(password, ENCRYPT_KEY);
            //生成会员ID
            LocalDateTime localDateTime = LocalDateTime.now();
            String authId = mobilePhone + localDateTime.format(DateTimeFormatter.ofPattern(DATE_TYPE6));
            //插入记录
            MembershipInfoWithBLOBs member = new MembershipInfoWithBLOBs();
            member.setAuthId(authId);
            member.setName(userName);
            member.setPassword(password);
            member.setMobilePhone(mobilePhone);
            member.setCityOfOrigin(cityName);
            member.setMembershipType((short)getMembershipType());

            //审核状态
            member.setReviewStatus((short) -1);
            //member.setDataOrigin((short) registerDto.getRegisterOrigin());
            member.setCreatedTime(localDateTime.format(DateTimeFormatter.ofPattern(DATE_TYPE3)));
            member.setCreatedUser(userName);
            member.setUpdatedTime(localDateTime.format(DateTimeFormatter.ofPattern(DATE_TYPE3)));
            member.setUpdatedUser(userName);
            member.setRegTime(localDateTime.format(DateTimeFormatter.ofPattern(DATE_TYPE4)));

            // 注册来源
            member.setSecondAppKey(appKey);
            member.setAppKey(otherDto.getFirstAppKey());
            member.setPlatformId(otherDto.getPlatFormId());
            member.setMid(midGenerator.getMid());
            // 设置了 企业，强行关联到企业。
     /*       if (StringUtils.isNotBlank(agencyId)) {
                member.setAgencyId(agencyId);
            }*/
            int insertSelective = membershipInfoMapper.insertSelective(member);
            if (insertSelective > 0) {
                log.info("赛乐通mobile=" + mobilePhone + "insertSelective成功");
                MembershipBasicInfo membership = membershipInfoMapper.getUserBasicInfoByMid(member.getMid());
                context.setMembershipBasicInfo(membership);
                return membership;
            } else {
                throw new BusinessException(-1, "新增会员失败");
            }
        } catch (Exception e) {
            log.error("赛乐通注册失败,原因：创建会员记录失败，" + e.getMessage(), e);
            throw new BusinessException(StatusCode.SYSTEM_ERROR);
        } finally {
            if (jedisLock != null && jedis != null) {
                jedisLock.releaseLua(jedis);
                jedis.close();
                log.debug("赛乐通注册服务-释放redis锁和链接成功");
            }
        }

    }

    @Override
    public void afterGetMembershipInfo(ThirdLoginContext context) throws BusinessException {
        MembershipBasicInfo membershipInfo = context.getMembershipBasicInfo();
        //制卡，该方法已实现幂等
        if (StringUtils.isBlank(membershipInfo.getCardNo())) {
            log.info("赛乐通membershipInfo=[{}],开始制卡", JSON.toJSONString(membershipInfo));
            memberShipService.setVirtualCard(membershipInfo.getAuthId(), getMembershipType());
            log.info("赛乐通membershipInfo=[{}],制卡成功", JSON.toJSONString(membershipInfo));
        }



    }

    @Override
    protected void asynchronousNotify(ThirdLoginContext context) {
        MembershipBasicInfo membershipInfo = context.getMembershipBasicInfo();
        //关联企业
        if (StringUtils.isNotBlank(agencyId) && StringUtils.isNotBlank(roleId)) {
            InitialAgencyMembershipTO dto = new InitialAgencyMembershipTO();
            dto.setAgencyId(agencyId);
            dto.setAgencyRoleId(Long.valueOf(roleId));
            dto.setMemberShipId(membershipInfo.getPkId());
            dto.setOperatorId(-1L);
            dto.setOperatorName("evcard-membership");
            ServiceResult serviceResult = agencyMemberService.initialAgencyMembership(dto);
            if (serviceResult != null && serviceResult.isSuccess()) {
                log.info("initialAgencyMembership  关联企业成功，dto={}，serviceResult={}",JSON.toJSONString(dto),JSON.toJSONString(serviceResult));
            }else{
                log.error("initialAgencyMembership  失败，dto={}，serviceResult={}",JSON.toJSONString(dto),JSON.toJSONString(serviceResult));
            }
        }
    }

    @Override
    public boolean isNeedToken(ThirdLoginInput object) {
        return object.isNeedToken();
    }
}
