package com.extracme.evcard.membership.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/8/16
 */
@AllArgsConstructor
@Getter
public enum TencentSideEnums {
    FRONT("FRONT"),
    BACK("BACK");
    private String side;

    public static boolean isValid(String side) {
        return FRONT.getSide().equals(side) || BACK.getSide().equals(side);
    }
}
