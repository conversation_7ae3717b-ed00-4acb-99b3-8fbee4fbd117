package com.extracme.evcard.membership.common;

import com.extracme.evcard.rpc.exception.BusinessException;
import org.apache.commons.lang3.ArrayUtils;

import java.io.*;
import java.util.zip.CRC32;
import java.util.zip.CheckedOutputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

public class ZipUtil {

    public final static int BUFFER = 1024;

    public static String compressAndUpload(String fromPath, String toPath, String objectName) throws Exception {
        compress(fromPath, toPath);
        try (FileInputStream inputStream = new FileInputStream(toPath)) {
            UploadImgUtil.uploadStreamSyn(inputStream, objectName);
        }
        return objectName;
    }

    public static void compress(String fromPath, String toPath) throws Exception {
        File fromFile = new File(fromPath);
        File toFile = new File(toPath);
        if (!fromFile.exists()) {
            throw new BusinessException(fromPath + "不存在！");
        }
        try (FileOutputStream outputStream = new FileOutputStream(toFile); CheckedOutputStream checkedOutputStream = new CheckedOutputStream(outputStream, new CRC32()); ZipOutputStream zipOutputStream = new ZipOutputStream(checkedOutputStream)) {
            String baseDir = "";
            compress(fromFile, zipOutputStream, baseDir);
        }
    }

    private static void compress(File file, ZipOutputStream zipOut, String baseDir) throws IOException {
        if (file.isDirectory()) {
            compressDirectory(file, zipOut, baseDir);
        } else {
            if (baseDir.equals("packs" + File.separator)) {
                baseDir = File.separator;
            } else if (baseDir.equals("packs" + File.separator + "examineeInfo" + File.separator)) {
                baseDir = "examineeInfo" + File.separator;
            }
            compressFile(file, zipOut, baseDir);
        }
    }

    private static void compressFile(File file, ZipOutputStream zipOut, String baseDir) throws IOException {
        if (!file.exists()) {
            return;
        }
        try (BufferedInputStream bis = new BufferedInputStream(new FileInputStream(file))) {
            ZipEntry entry = new ZipEntry(baseDir + file.getName());
            zipOut.putNextEntry(entry);
            int count;
            byte[] data = new byte[BUFFER];
            while ((count = bis.read(data, 0, BUFFER)) != -1) {
                zipOut.write(data, 0, count);
            }
        }
    }

    private static void compressDirectory(File dir, ZipOutputStream zipOut, String baseDir) throws IOException {
        File[] files = dir.listFiles();
        if (files != null && ArrayUtils.isNotEmpty(files)) {
            for (File file : files) {
                compress(file, zipOut, baseDir + dir.getName() + File.separator);
            }
        }
    }
}
