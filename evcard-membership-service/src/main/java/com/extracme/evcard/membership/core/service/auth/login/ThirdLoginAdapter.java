package com.extracme.evcard.membership.core.service.auth.login;

import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.service.auth.IDescription;
import com.extracme.evcard.membership.core.service.auth.IThirdLogin;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
public class ThirdLoginAdapter {

    @Resource
    private AliCarLifeLogin aliCarLifeLogin;
    @Resource
    private QingLuLogin qingLuLogin;
    @Resource
    private LongShortMixLogin longShortMixLogin;
    @Resource
    private SaicTravelLogin saicTravelLogin;
    @Resource
    private NewCarLifeLogin newCarLifeLogin;

    @Resource
    private TongChengZuCheLogin tongChengZuCheLogin;

    @Resource
    private CommonUserLogin commonUserLogin;

    @Resource
    private OrderUseCarMemberLogin orderUseCarMemberLogin;

    public IThirdLogin getThirdLoginService(String appKey){
        if (BussinessConstants.CAR_LIFE_CHANNEL.equals(appKey)) {
            return aliCarLifeLogin;
        }else if(BussinessConstants.QING_LU_CHANNEL.equals(appKey)){
            return qingLuLogin;
        }else if(BussinessConstants.LONG_SHORT_MIX.equals(appKey)){
            return longShortMixLogin;
        }else if(BussinessConstants.SAI_LE_TONG.equals(appKey)){
            return saicTravelLogin;
        }else if(BussinessConstants.CAR_LIFE_SECOND_APP_KEY.equals(appKey)){
            return newCarLifeLogin;
        } else if (BussinessConstants.TONGCHENG_SECOND_APP_KEY.equals(appKey)) {
            return tongChengZuCheLogin;
        }
        return null;
    }

    public List<IThirdLogin> getSpecialLoginServices(){
        List<IThirdLogin> list = new ArrayList<>();
        list.add(orderUseCarMemberLogin);
        return list;
   }

    /**
     * 获取 会员注册登陆 服务
     * @param secondAppKey
     * @return
     */
    public IThirdLogin getCommonLoginService(String secondAppKey){
        if (StringUtils.isEmpty(secondAppKey)) {
            return null;
        }

        List<IThirdLogin> commonLoginServices = getSpecialLoginServices();
        if (CollectionUtils.isEmpty(commonLoginServices)) {
            return commonUserLogin;
        }

        for (IThirdLogin commonLoginService : commonLoginServices) {
            IDescription description = (IDescription) commonLoginService;
            if (description.getSecondAppKey().equals(secondAppKey)) {
                return commonLoginService;
            }
        }
        return commonUserLogin;
    }


}
