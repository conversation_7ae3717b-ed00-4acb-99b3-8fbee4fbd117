package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.dto.AppKeyDto;
import com.extracme.evcard.membership.core.model.AppKeyManager;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AppKeyManagerMapper {

    int insert(AppKeyManager record);

    int insertSelective(AppKeyManager record);

    AppKeyManager selectByPrimaryKey(String appKey);

    int updateByPrimaryKeySelective(AppKeyManager record);

    int updateByPrimaryKey(AppKeyManager record);

    /**
     * 根据平台和渠道用途获取渠道，不分页
     * @param platformId
     * @param platformId
     * @param status
     * @return
     */
    List<AppKeyDto> getAppKeys(@Param("platformId") Long platformId,
                                 @Param("channelPurpose") String channelPurpose,
                                 @Param("status") Integer status);

    /**
     * 获取所有生效中的appkey信息
     * @return
     */
    List<AppKeyManager> getEffectiveAppKeyInfo();
}