package com.extracme.evcard.membership.core.service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.extracme.evcard.membership.core.dao.AreaMapper;
import com.extracme.evcard.membership.core.dao.CardInfoMapper;
import com.extracme.evcard.membership.core.dao.OrgInfoMapper;
import com.extracme.evcard.membership.core.dao.RegionMapper;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.input.QueryMemberInputDTO;
import com.extracme.evcard.membership.core.input.QueryMemberListInputDTO;
import com.extracme.evcard.membership.core.input.UpdateMemberInputDTO;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 调度会员服务
 * </p>
 *
 * <AUTHOR>
 * @date 2019/8/26
 */
@Slf4j
@Service("memberForIdsService")
public class MemberForIdsServiceImpl implements IMemberForIdsService {

    @Autowired
    private MembershipInfoMapper membershipInfoMapper;

    @Autowired
    private CardInfoMapper cardInfoMapper;

    @Autowired
    private AreaMapper areaMapper;

    @Autowired
    private RegionMapper regionMapper;

    @Autowired
    private OrgInfoMapper orgInfoMapper;

    @Override
    public MembershipBasicInfo queryMemberInfo(QueryMemberInputDTO queryMemberInputDTO) {
        if(log.isDebugEnabled()){
            log.debug("入参={}",queryMemberInputDTO.toString());
        }
        if(queryMemberInputDTO != null){
            return membershipInfoMapper.queryOneMemberByCondition(queryMemberInputDTO);
        }
        return null;
    }

    @Override
    public List<MembershipBasicInfo> queryMemberList(QueryMemberListInputDTO queryMemberListInputDTO) {
        if(log.isDebugEnabled()){
            log.debug("入参={}",queryMemberListInputDTO.toString());
        }
        if(queryMemberListInputDTO != null){
            List<MembershipBasicInfo> membershipBasicInfoList = membershipInfoMapper.queryMemberListByCondition(queryMemberListInputDTO);
            if(CollectionUtils.isNotEmpty(membershipBasicInfoList)){
                //卡号信息
                Map<String, String> cardNoMap = null;
                List<String> cardNoList = membershipBasicInfoList.stream().filter(b -> StringUtils.isNotBlank(b.getCardNo())).map(MembershipBasicInfo::getCardNo).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(cardNoList)){
                    List<Map<String, String>> cardNoMapList = cardInfoMapper.batchQueryInternalNo(cardNoList);
                    cardNoMap = cardNoMapList.stream().collect(Collectors.toMap(p -> p.get("cardNo"), p -> p.get("internalNo")));
                }
                 //区信息
                Map<String, String> areaMap = null;
                if(queryMemberListInputDTO.getMemberType() != null && queryMemberListInputDTO.getMemberType() == 1){
                    List<String> areaIdList = membershipBasicInfoList.stream().filter(b -> StringUtils.isNotBlank(b.getArea())).map(MembershipBasicInfo::getArea).collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(areaIdList)){
                        List<Map<String, Object>> areaMapList = areaMapper.batchQueryAreaName(areaIdList);
                        areaMap = areaMapList.stream().collect(Collectors.toMap(p -> String.valueOf(p.get("areaId")), p -> String.valueOf(p.get("areaName"))));
                    }
                }
                //地区信息
                Map<String, String> regionMap = null;
                List<Long> regionIdList = membershipBasicInfoList.stream().filter(b -> b.getRegionid() != null).map(MembershipBasicInfo::getRegionid).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(regionIdList)) {
                	List<Map<String, Object>> regionMapList = regionMapper.batchQueryRegionName(regionIdList);
                	regionMap = regionMapList.stream().collect(Collectors.toMap(p ->  String.valueOf(p.get("regionId")), p -> String.valueOf(p.get("regionName"))));
                }
                //机构信息
                Map<String, String> orgMap = null;
                List<String> orgIdList = membershipBasicInfoList.stream().filter(b -> StringUtils.isNotBlank(b.getOrgId())).map(MembershipBasicInfo::getOrgId).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(orgIdList)){
                    List<Map<String, String>> orgMapList = orgInfoMapper.batchQueryOrgName(orgIdList);
                    orgMap = orgMapList.stream().collect(Collectors.toMap(p -> p.get("orgId"), p -> p.get("orgName")));
                }

                for(MembershipBasicInfo p : membershipBasicInfoList){
                    if(MapUtils.isNotEmpty(cardNoMap) && StringUtils.isNotBlank(p.getCardNo())){
                        p.setInternalNo(cardNoMap.get(p.getCardNo()));
                    }
                    if(MapUtils.isNotEmpty(areaMap) && StringUtils.isNotBlank(p.getArea())){
                        p.setAreaName(areaMap.get(p.getArea()));
                    }
                    if(MapUtils.isNotEmpty(regionMap) && p.getRegionid() != null){
                        p.setRegionName(regionMap.get(String.valueOf(p.getRegionid())));
                    }
                    if(MapUtils.isNotEmpty(orgMap) && StringUtils.isNotBlank(p.getOrgId())){
                        p.setOrgName(orgMap.get(p.getOrgId()));
                    }
                }
            }
            return membershipBasicInfoList;
        }
        return null;
    }

    @Override
    public List<String> queryInnerMemberNames(String name) {
        QueryMemberListInputDTO queryMemberListInputDTO = new QueryMemberListInputDTO();
        queryMemberListInputDTO.setMemberType(1);
        queryMemberListInputDTO.setName(name);
        queryMemberListInputDTO.setIsLikeQueryName(3);
        List<MembershipBasicInfo> membershipBasicInfoList = this.queryMemberList(queryMemberListInputDTO);
        if(CollectionUtils.isNotEmpty(membershipBasicInfoList)){
            return membershipBasicInfoList.stream().map(MembershipBasicInfo::getName).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public List<String> queryInnerMemberMobiles(String mobile) {
        QueryMemberListInputDTO queryMemberListInputDTO = new QueryMemberListInputDTO();
        queryMemberListInputDTO.setMemberType(1);
        queryMemberListInputDTO.setMobilePhone(mobile);
        queryMemberListInputDTO.setIsLikeQueryMobilePhone(3);
        List<MembershipBasicInfo> membershipBasicInfoList = this.queryMemberList(queryMemberListInputDTO);
        if(CollectionUtils.isNotEmpty(membershipBasicInfoList)){
            return membershipBasicInfoList.stream().map(MembershipBasicInfo::getMobilePhone).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public boolean updateMemberInfo(UpdateMemberInputDTO updateMemberInputDTO) {
        try {
            int i = membershipInfoMapper.updateMemberInfo(updateMemberInputDTO);
            if(i > 0){
                return true;
            }
        }catch (Exception e){
            log.error(e.getMessage(),e);
        }
        return false;
    }


}
