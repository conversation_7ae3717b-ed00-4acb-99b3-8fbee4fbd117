package com.extracme.evcard.membership.core.enums;

/**
 * 公司用章枚举
 */
public enum CompanySealEnum {

    /**
     * 环球车享汽车租赁有限公司
     */
    GLOBAL_RENTAL("00", "环球车享汽车租赁有限公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/00.png"),

    /**
     * 上海环行汽车租赁有限公司乌鲁木齐分公司
     */
    SHANGHAI_RING_XING_URUMQI("009W", "上海环行汽车租赁有限公司乌鲁木齐分公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/009W.png"),

    /**
     * 大理赛可汽车租赁有限公司
     */
    DALI_SAICHI("009V", "大理赛可汽车租赁有限公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/009V.png"),

    /**
     * 上海环行汽车租赁有限公司昆明分公司
     */
    SHANGHAI_RING_XING_KUNMING("009F", "上海环行汽车租赁有限公司昆明分公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/009F.png"),

    /**
     * 上海环行汽车租赁有限公司丽江分公司
     */
    SHANGHAI_RING_XING_LIJANG("009D", "上海环行汽车租赁有限公司丽江分公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/009D.png"),

    /**
     * 上海环行汽车租赁有限公司西双版纳分公司
     */
    SHANGHAI_RING_XING_XISHUANBANNA("009C", "上海环行汽车租赁有限公司西双版纳分公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/009C.png"),

    /**
     * 上海环行汽车租赁有限公司
     */
    SHANGHAI_RING_XING("009B", "上海环行汽车租赁有限公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/009B.png"),

    /**
     * 环球车享（福建）汽车租赁有限公司佛山分公司
     */
    GLOBAL_RENTAL_FUJIAN_FOSHAN("009A", "环球车享（福建）汽车租赁有限公司佛山分公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/009A.png"),

    /**
     * 环球车享（杭州）汽车租赁有限公司南京分公司
     */
    GLOBAL_RENTAL_HANGZHOU_NANJING("0099", "环球车享（杭州）汽车租赁有限公司南京分公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/0099.png"),

    /**
     * 陕西赛可汽车租赁有限公司重庆分公司
     */
    SHANXI_SAICHI_CHONGQING("0098", "陕西赛可汽车租赁有限公司重庆分公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/0098.png"),

    /**
     * 陕西赛可汽车租赁有限公司贵阳分公司
     */
    SHANXI_SAICHI_GUIYANG("0096", "陕西赛可汽车租赁有限公司贵阳分公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/0096.png"),

    /**
     * 环球车享（杭州）汽车租赁有限公司合肥分公司
     */
    GLOBAL_RENTAL_HANGZHOU_HEFEI("0095", "环球车享（杭州）汽车租赁有限公司合肥分公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/0095.png"),

    /**
     * 环球车享（福建）汽车租赁有限公司济南分公司
     */
    GLOBAL_RENTAL_FUJIAN_JINAN("0094", "环球车享（福建）汽车租赁有限公司济南分公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/0094.png"),

    /**
     * 环球车享（杭州）汽车租赁有限公司宁波分公司
     */
    GLOBAL_RENTAL_HANGZHOU_NINGBO("0093", "环球车享（杭州）汽车租赁有限公司宁波分公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/0093.png"),

    /**
     * 陕西赛可汽车租赁有限公司
     */
    SHANXI_SAICHI("0091", "陕西赛可汽车租赁有限公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/0091.png"),

    /**
     * 环球车享（厦门）汽车租赁有限公司
     */
    GLOBAL_RENTAL_XIAMEN("008Z", "环球车享（厦门）汽车租赁有限公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/008Z.png"),

    /**
     * 环球车享（杭州）汽车租赁有限公司江山分公司
     */
    GLOBAL_RENTAL_HANGZHOU_JIANGSHAN("008N0901", "环球车享（杭州）汽车租赁有限公司江山分公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/008N0901.png"),

    /**
     * 环球车享（杭州）汽车租赁有限公司丽水分公司
     */
    GLOBAL_RENTAL_HANGZHOU_LISHUI("008N0900", "环球车享（杭州）汽车租赁有限公司丽水分公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/008N0900.png"),

    /**
     * 环球车享（临沂）汽车租赁有限公司
     */
    GLOBAL_RENTAL_LINYI("008W", "环球车享（临沂）汽车租赁有限公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/008W.png"),

    /**
     * 上海挚极信息科技有限公司
     */
    SHANGHAI_ZHIJI("008V", "上海挚极信息科技有限公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/008V.png"),

    /**
     * 环球车享（杭州）汽车租赁有限公司
     */
    GLOBAL_RENTAL_HANGZHOU("008N09", "环球车享（杭州）汽车租赁有限公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/008N09.png"),

    /**
     * 环球车享（福州）汽车租赁有限公司
     */
    GLOBAL_RENTAL_FUZHOU("008U", "环球车享（福州）汽车租赁有限公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/008U.png"),

    /**
     * 环球车享（福建）汽车租赁有限公司
     */
    GLOBAL_RENTAL_FUJIAN("008T", "环球车享（福建）汽车租赁有限公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/008T.png"),

    /**
     * 环球车享（济南）汽车租赁有限公司
     */
    GLOBAL_RENTAL_JINAN("008R", "环球车享（济南）汽车租赁有限公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/008R.png"),

    /**
     * 环球车享（太原）汽车租赁有限公司
     */
    GLOBAL_RENTAL_TAIYUAN("008Q", "环球车享（太原）汽车租赁有限公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/008Q.png"),

    /**
     * 环球车享（烟台）汽车租赁有限公司
     */
    GLOBAL_RENTAL_YANTAI("008O", "环球车享（烟台）汽车租赁有限公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/008O.png"),

    /**
     * 环球车享（金华）汽车租赁有限公司
     */
    GLOBAL_RENTAL_JINHUA("008N04", "环球车享（金华）汽车租赁有限公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/008N04.png"),

    /**
     * 环球车享扬州汽车租赁有限公司
     */
    GLOBAL_RENTAL_YANGZHOU("008P06", "环球车享扬州汽车租赁有限公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/008P06.png"),

    /**
     * 环球车享（三亚）汽车租赁有限公司
     */
    GLOBAL_RENTAL_SANYA("008L", "环球车享（三亚）汽车租赁有限公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/008L.png"),

    /**
     * 环球车享（天津）汽车租赁有限公司
     */
    GLOBAL_RENTAL_TIANJIN("008F", "环球车享（天津）汽车租赁有限公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/008F.png"),

    /**
     * 环球车享武汉汽车租赁有限公司
     */
    GLOBAL_RENTAL_WUHAN("008C", "环球车享武汉汽车租赁有限公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/008C.png"),

    /**
     * 环球车享（长沙）汽车租赁有限公司
     */
    GLOBAL_RENTAL_CHANGSHA("008A", "环球车享（长沙）汽车租赁有限公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/008A.png"),

    /**
     * 环球车享贵阳汽车租赁有限公司
     */
    GLOBAL_RENTAL_GUIYANG("0088", "环球车享贵阳汽车租赁有限公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/0088.png"),

    /**
     * 环球车享郑州汽车租赁有限公司
     */
    GLOBAL_RENTAL_ZHENGZHOU("0079", "环球车享郑州汽车租赁有限公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/0079.png"),

    /**
     * 环球车享合肥汽车租赁有限公司
     */
    GLOBAL_RENTAL_HEFEI("006Z", "环球车享合肥汽车租赁有限公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/006Z.png"),

    /**
     * 环球车享（青岛）汽车租赁有限公司
     */
    GLOBAL_RENTAL_QINGDAO("006A", "环球车享（青岛）汽车租赁有限公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/006A.png"),

    /**
     * 环球车享镇江汽车租赁有限公司
     */
    GLOBAL_RENTAL_ZHENJIANG("008P04", "环球车享镇江汽车租赁有限公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/008P04.png"),

    /**
     * 环球车享（广州）汽车租赁有限公司
     */
    GLOBAL_RENTAL_GUANGZHOU("005P", "环球车享（广州）汽车租赁有限公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/005P.png"),

    /**
     * 环球车享（海口）汽车租赁有限公司
     */
    GLOBAL_RENTAL_HAIKOU("005F", "环球车享（海口）汽车租赁有限公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/005F.png"),

    /**
     * 上海赛可汽车租赁有限公司
     */
    SHANGHAI_SAICHI("005E", "上海赛可汽车租赁有限公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/005E.png"),

    /**
     * 环球车享（重庆）汽车租赁有限公司
     */
    GLOBAL_RENTAL_CHONGQING("0054", "环球车享（重庆）汽车租赁有限公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/0054.png"),

    /**
     * 环球车享成都汽车租赁有限公司
     */
    GLOBAL_RENTAL_CHENGDU("003V", "环球车享成都汽车租赁有限公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/003V.png"),

    /**
     * 环球车享（苏州）汽车租赁有限公司
     */
    GLOBAL_RENTAL_SUZHOU("008P01", "环球车享（苏州）汽车租赁有限公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/008P01.png"),

    /**
     * 上海国际汽车城新能源汽车运营服务有限公司
     */
    SHANGHAI_INTERNATIONAL_AUTOCITY("000T", "上海国际汽车城新能源汽车运营服务有限公司", "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/000T.png");

    private String orgCode;
    private String orgName;
    private String sealPicUrl;

    CompanySealEnum(String orgCode, String orgName, String sealPicUrl) {
        this.orgCode = orgCode;
        this.orgName = orgName;
        this.sealPicUrl = sealPicUrl;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getSealPicUrl() {
        return sealPicUrl;
    }

    public void setSealPicUrl(String sealPicUrl) {
        this.sealPicUrl = sealPicUrl;
    }

    public static CompanySealEnum getByOrgCode(String orgCode) {
        for (CompanySealEnum companySealEnum : CompanySealEnum.values()) {
            if (orgCode.equals(companySealEnum.getOrgCode())) {
                return companySealEnum;
            }
        }
        return null;
    }

    public static String getSealPicUrlByOrgCode(String orgCode) {
        for (CompanySealEnum companySealEnum : CompanySealEnum.values()) {
            if (orgCode.equals(companySealEnum.getOrgCode())) {
                return companySealEnum.getSealPicUrl();
            }
        }
        return null;
    }
}
