package com.extracme.evcard.membership.credit.dao;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.extracme.evcard.membership.core.dto.*;
import com.extracme.evcard.membership.core.input.QueryMemberInputDTO;
import com.extracme.evcard.membership.core.input.QueryMemberListInputDTO;
import com.extracme.evcard.membership.core.input.QuerySimpleMemberInput;
import com.extracme.evcard.membership.core.input.UpdateFileNoInput;
import com.extracme.evcard.membership.core.input.UpdateMemberInputDTO;
import com.extracme.evcard.membership.core.model.MembershipAdditionalInfo;
import com.extracme.evcard.membership.credit.model.MmpUserTag;
import org.apache.ibatis.annotations.Param;

import com.extracme.evcard.membership.core.model.MembershipAccountInfo;
import com.extracme.evcard.membership.credit.dto.MembershipInfoDto;
import com.extracme.evcard.membership.credit.dto.UpdateServiceVerDto;
import com.extracme.evcard.membership.credit.model.MembershipBaseInfo;
import com.extracme.evcard.membership.credit.model.MembershipInfo;
import com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs;

public interface MembershipInfoMapper {
	/**
	 * This method was generated by MyBatis Generator. This method corresponds
	 * to the database table membership_info
	 *
	 * @mbggenerated Mon Dec 04 15:27:31 CST 2017
	 */
	int deleteByPrimaryKey(Long pkId);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds
	 * to the database table membership_info
	 *
	 * @mbggenerated Mon Dec 04 15:27:31 CST 2017
	 */
	int insert(MembershipInfoWithBLOBs record);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds
	 * to the database table membership_info
	 *
	 * @mbggenerated Mon Dec 04 15:27:31 CST 2017
	 */
	int insertSelective(MembershipInfoWithBLOBs record);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds
	 * to the database table membership_info
	 *
	 * @mbggenerated Mon Dec 04 15:27:31 CST 2017
	 */
	MembershipInfoWithBLOBs selectByPrimaryKey(Long pkId);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds
	 * to the database table membership_info
	 *
	 * @mbggenerated Mon Dec 04 15:27:31 CST 2017
	 */
	int updateByPrimaryKeySelective(MembershipInfoWithBLOBs record);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds
	 * to the database table membership_info
	 *
	 * @mbggenerated Mon Dec 04 15:27:31 CST 2017
	 */
	int updateByPrimaryKeyWithBLOBs(MembershipInfoWithBLOBs record);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds
	 * to the database table membership_info
	 *
	 * @mbggenerated Mon Dec 04 15:27:31 CST 2017
	 */
	int updateByPrimaryKey(MembershipInfo record);

	int updateMemberShipStatusByAuthId(String authId);

	// 检查用户邮寄地址是否上海
	Integer checkMemberShipAddress(String authId);

	// 查询客户是否为成都用户
	Integer checkMemberShipAddressCD(String authId);

	// 通过会员id查询APP_KEY
	String queryAppKey(@Param("authId") String authId);

	// 判断该会员是否注册成功
	List<Integer> checkReward(@Param("authId") String authId);

	// 查询好友ID
	List<Map> queryUid(@Param("authId") String authId);

	// 查询好友信息
	List<Map> queryShareUser(@Param("shareUid") String shareUid);

	void rewardInsert(@Param("insertMap") Map insertMap);

	Integer selectAuthInfoByAuthId(String authId);

	/**
	 * 根据手机号查询会员
	 * 
	 * @param mobilePhone
	 * @return
	 */
	int countByMobilePhone(@Param("mobilePhone") String mobilePhone,@Param("orgId")String orgId);

	/**
	 * 查询city和area相同的城市名
	 * 
	 * @param area
	 * @return
	 */
	String queryCityEqualsArea(@Param("area") String area);

	/**
	 * 是否存在appKey
	 * 
	 * @param appKey
	 * @return
	 */
	int isExistAppKey(@Param("appKey") String appKey);

	/**
	 * appKey是否有效
	 * 
	 * @param appKey
	 * @return
	 */
	Map<String, String> selectOneByAppKey(@Param("appKey") String appKey);

	/**
	 * 保存法大大返回的会员关联ID
	 * 
	 * @param authId
	 * @param customId
	 */
	void saveCustomerId(@Param("authId") String authId, @Param("customerId") String customId);

	/**
	 * 查询是否签署过合同
	 * 
	 * @param authId
	 * @param templateId
	 * @param customerId
	 * @return
	 */
	int countContract(@Param("authId") String authId, @Param("customerId") String customerId,
			@Param("templateId") String templateId);

	/**
	 * 查询是否签署过合同
	 *
	 * @param authId
	 * @param templateId
	 * @return
	 */
	int countContractByTemplateId(@Param("authId") String authId, @Param("templateId") String templateId);

	/**
	 * 根据authid查询会员信息
	 * 
	 * @param authId
	 * @param membershipType
	 * @return
	 */
	MembershipInfoWithBLOBs selectByAuthId(@Param("authId") String authId,
			@Param("membershipType") Integer membershipType);
	MembershipInfo getIdentityByPkId(@Param("pkId") Long pkId);

	//根据证件号和手机号反查用户信息
	List<MembershipInfo> getInfoByPassportNoAndPhone(@Param("passportNo") String passportNo,@Param("mobilePhone")String mobilePhone);

	/**
	 * 根据authid查询会员信息
	 *
	 * @param authId
	 * @return
	 */
	MembershipInfoWithBLOBs selectByAuthId2(@Param("authId") String authId);

	/**
	 * 根据uid查询会员信息
	 *
	 * @param uid
	 * @return
	 * @since 3.8.0
	 */
	MembershipInfoWithBLOBs selectByUid(@Param("uid") String uid);

	/**
	 * 更新指定pkId的用户的享道统一用户ID
	 * @param pkId
	 * @param uid
	 * @return
	 * @since 3.8.0
	 */
	int updateMemberUidByPkId(@Param("pkId") Long pkId, @Param("uid") String uid);

    /**
     * 根据pkId批量查询用户信息
     * @param userIds
     * @return
     * @since 3.2.0
     */
    List<MembershipInfoWithBLOBs> selectByPkIds(@Param("list") Set<Long> userIds);

	/**
	 * 根据authIds批量查询用户信息
	 * @param authIds
	 * @return
	 * @since 3.2.0
	 */
	List<MembershipInfo> selectByAuthIds(@Param("list") Set<String> authIds);

	/**
	 * 根据mobilePhones批量查询用户信息
	 * @param mobilePhones
	 * @return
	 * @since 4.12.4
	 */
	List<MembershipInfo> selectByMobilePhones(@Param("list") Set<String> mobilePhones);

	/**
	 * 根据mobilePhones批量查询用户信息
	 * @param mobilePhones
	 * @return
	 * @since 4.12.4
	 */
	List<MembershipInfo> selectByMobilePhoneAndType(@Param("mobilePhone") String mobilePhone,@Param("membershipType") Integer membershipType);
	/**
	 * 根据mobilePhone 查询第一个用户信息 按照注册时间升序
	 * @param mobilePhone
	 * @return
	 */
	MembershipInfo selectFirstUserByMobilePhone(@Param("mobilePhone") String mobilePhone);

	/**
	 * 查询会员的基础信息（只存在部分字段，避免查询整个membership，需要可自己添加）
	 * 
	 * @param authId
	 * @param membershipType
	 * @return
	 */
	MembershipBaseInfo selectBaseByAuthId(@Param("authId") String authId,
			@Param("membershipType") Integer membershipType);

	/**
	 * 根据证件号和会员类型查询会员基础信息.<br>
	 * 
	 * @param driverCode
	 *            会员证件号.<br>
	 * @param membershipType
	 *            会员类型.<br>
	 * @return
	 */
	MembershipBaseInfo selectBaseByDriverCode(@Param("driverCode") String driverCode,
			@Param("membershipType") Integer membershipType , @Param("orgId") String orgId);

	/**
	 * 修改用户的手机号.<br>
	 * 
	 * @param authId
	 *            会员id.<br>
	 * @param membershipType
	 *            会员类型.0:外部会员, 1:内部会员<br>
	 * @param mobilePhone
	 *            手机号.<br>
	 * @param updateTime
	 *            修改时间.<br>
	 * @param optUser
	 *            修改人.<br>
	 * @return 修改条数.<br>
	 */
	int updateMobilePhone(@Param("authId") String authId, @Param("membershipType") int membershipType,
			@Param("mobilePhone") String mobilePhone, @Param("updateTime") String updateTime,
			@Param("optUser") String optUser,@Param("orgId") String orgId);

	/**
	 * 根据证件号和会员类型查询会员基础信息.<br>
	 * 
	 * @param mobilePhone
	 *            会员手机号.<br>
	 * @param membershipType
	 *            会员类型.<br>
	 * @return
	 */
	MembershipBaseInfo selectBaseByPhone(@Param("mobilePhone") String mobilePhone,
			@Param("membershipType") Integer membershipType);


    MembershipBaseInfo selectBaseByPhone2(@Param("mobilePhone") String mobilePhone,
                                         @Param("membershipType") Integer membershipType);
	/**
	 * 根据手机号查询内部会员
	 * 
	 * @param mobilePhone
	 * @return
	 */
	int countInnerMemberByMobile(String mobilePhone);

	// 更新内部会员cardNo
	Integer updateInnerMemberCardNoByAuthId(MembershipInfo membershipInfo);

	//更细外部会员cardno
	Integer updateMemberCardNoByAuthId(MembershipInfo membershipInfo);

	//更新会员cardno
	Integer updateMemberCardNoByAuthIdAndType(MembershipInfo membershipInfo);

	// 根据手机号和机构查询内部会员
	MembershipInfo selectInnerMemberByMobile(@Param("mobilePhone")String mobilePhone,@Param("orgId")String orgId,@Param("idCardNumber")String idCardNumber );

	/**
	 * 根据手机号查询会员信息
	 * @param mobilePhone
	 * @param orgId
	 * @return
	 */
	MembershipInfoDto getMemberInfoByPhone(@Param("mobilePhone") String mobilePhone, @Param("orgId") String orgId);

	/**
	 * 更新用户推送渠道
	 * 
	 * @param channelId
	 * @param authId
	 * @param memberType
	 * @return
	 */
	int updateChannelId(@Param("channelId") String channelId, @Param("authId") String authId,
			@Param("memberType") Short memberType);

	/**
	 * 查询会员免押情况
	 * 
	 * @param authId
	 * @return
	 */
	MemberExemptDepositDto queryExemptDeposit(@Param("authId") String authId);

	/**
	 * 根据authid获取该用户所有的账户id
	 * 
	 * @param authId
	 * @return
	 */
	List<String> getAllAuthIdByUser(@Param("authId") String authId);

	MembershipBasicInfo getUserBasicInfo(@Param("authId") String authId, @Param("membershipType") Short membershipType);

	/**
	 * 根据手机号查询会员的基础信息.<br>
	 * @param phone
	 * @param membershipType
	 * @return
	 */
	MembershipBasicInfo getMembershipByPhone(@Param("phone") String phone, @Param("membershipType") Integer membershipType);

	/**
	 * 根据会员id和type获取会员状态和会员卡状态
	 * 
	 * @param authId
	 * @param memberType
	 * @return
	 */
	ReviewAndCarStatusDto reviewAndCardStatus(@Param("authId") String authId, @Param("memberType") Integer memberType);

	/**
	 * 根据authid和会员类型更新会员版本信息
	 * 
	 * @param updateServiceVerDto
	 * @return
	 */
	int updateServiceVer(UpdateServiceVerDto updateServiceVerDto);

	/**
	 * 根据authId修改外部会员的邮箱
	 * 
	 * @param mail
	 * @param authId
	 * @param updateUser
	 * @param updateTime
	 * @return int
	 */
	int updateMail(@Param("mail") String mail, @Param("authId") String authId, @Param("updateUser") String updateUser,
			@Param("updateTime") String updateTime);

	/**
	 * 根据authId修改外部会员的地址
	 * 
	 * @param authId
	 * @param address
	 * @param province
	 * @param city
	 * @param area
	 * @param updateUser
	 * @param updateTime
	 * @return int
	 */
	int updateAddress(@Param("authId") String authId, @Param("address") String address,
			@Param("province") String province, @Param("city") String city, @Param("area") String area,
			@Param("updateUser") String updateUser, @Param("updateTime") String updateTime);

	/**
	 * 根据authId修改修改会员审核状态由审核不通过变为待审核
	 * 
	 * @param authId
	 * @return int
	 */
	int updateReviewStatus(@Param("authId") String authId);

	/**
	 * 根据authId重置会员密码
	 * 
	 * @param authId
	 * @param password
	 * @param updateUser
	 * @param updateTime
	 * @return int
	 */
	int updatePassword(@Param("authId") String authId, @Param("password") String password,
			@Param("updateUser") String updateUser, @Param("updateTime") String updateTime);

    /**
     * 根据手机号和type获取会员状态, 包括是否注销、审核状态、是否在黑名单
     *
     * @param mobilePhone
     * @param membershipType
     * @return
     * @since 1.9.1
     */
	AccountStatusDto getAccountStatusByPhone(@Param("mobilePhone") String mobilePhone, @Param("membershipType") Integer membershipType ,@Param("orgId") String orgId);

    /**
     * 根据手机号和type获取会员状态, 包括是否注销、审核状态、是否在黑名单
     *
     * @param driverCode
     * @param membershipType
     * @return
     * @since 1.9.1
     */
	AccountStatusDto getAccountStatusByDriverCode(@Param("driverCode") String driverCode, @Param("membershipType") Integer membershipType);

	/**
	 *
	 * @param authId
	 * @param membershipType
	 * @return
	 */
	MembershipAccountInfo getMemberAccountInfo(@Param("authId") String authId, @Param("membershipType") Integer membershipType);

	/**
	 * 账号注销.<br>
	 *
	 * @param authId 会员id.<br>
	 * @param membershipType
	 *            会员类型.0:外部会员, 1:内部会员<br>
	 * @param unregisterTime
	 *            注销时间.<br>
	 * @param updateTime
	 *            修改时间.<br>
	 * @param optUser
	 *            修改人.<br>
	 * @return 修改条数.<br>
	 * @since 1.9.1
	 */
	int accountUnregister(@Param("authId") String authId, @Param("membershipType") int membershipType,
						  @Param("unregisterTime") String unregisterTime, @Param("accountStatus") Integer accountStatus,
						  @Param("updateTime") String updateTime, @Param("optUser") String optUser);

	/**
	 * 定时任务-逻辑删除当日注销冻结期满的外部会员
	 * @param registerDate
	 * @param updateTime
	 * @param optUser
	 * @return
	 */
	int autoCompleteUnregister(@Param("registerDate") String registerDate,
								@Param("updateTime") String updateTime, @Param("optUser") String optUser);

	List<AccountStatusDto> selectAutoCompleteUnregister(@Param("registerDate") String registerDate);
	
	/**
	 * 通过手机号修改个人会员密码
	 * @param mobilePhone
	 * @param password
	 * @param optUserName
	 * @param updateTime
	 * @return
	 */
	int updatePasswordByMobilePhone(@Param("mobilePhone") String mobilePhone, @Param("password") String password,
			@Param("optUserName") String optUserName, @Param("updateTime") String updateTime,@Param("orgId")String orgId);
	
	/**
	 * check手机号是否已经被注册(排除自己使用的情况)
	 * @param mobile
	 * @param authId
	 * @return
	 */
	AccountStatusDto checkMobilRegistered(@Param("mobilePhone")String mobilePhone,@Param("authId") String authId, @Param("membershipType") Integer membershipType);

	/**
	 * 查询巡检基础信息
	 * @param authId
	 * @param membershipType
	 * @return
	 */
	MembershipRegionInfo getUserRegionInfo(@Param("authId") String authId, @Param("membershipType") Short membershipType);

	/**
	 * 获取用户邀请信息
	 * @param authId
	 * @param membershipType
	 * @return
	 */
	MemberInviteInfoDto getUserInviteInfo(@Param("authId") String authId, @Param("membershipType") Short membershipType);

	/**
	 * 根据authId查询外部会员信息
	 * @param authId
	 * @param orgId
	 * @return
	 */
	MembershipInfo selectMembershipInfoByAuthId(@Param("authId")String authId, @Param("orgId")String orgId);


	/**
	 * 根据authId修改用户头像
	 * @param imageUrl
	 * @param authId
	 * @param orgId
	 * @param updateUser
	 * @param updateTime
	 * @return
	 */
	int updateUserImage(@Param("imageUrl") String imageUrl,
						@Param("authId") String authId,
						@Param("orgId") String orgId,
						@Param("updateUser") String updateUser,
						@Param("updateTime") String updateTime);


	List<SimpleMembershipInfoDTO> selectSimpleMembershipInfo(QuerySimpleMemberInput querySimpleMemberInput);

	/**
	 * 检查驾照占用
	 * @param authId
	 * @param driverCode
	 * @param membershipType
	 * @return
	 */
	List<MembershipBaseInfo> checkDriverCodeOccupied(@Param("authId") String authId,
													 @Param("driverCode") String driverCode,
													 @Param("membershipType") Integer membershipType);

	/**
	 * 检查档案编号占用
	 * @param authId
	 * @param fileNo
	 * @param membershipType
	 * @return
	 */
	List<MembershipBaseInfo> checkFileNoOccupied(@Param("authId") String authId,
												 @Param("fileNo") String fileNo,
												 @Param("membershipType") Integer membershipType);

	/**
	 * 检查档案编号占用
	 * @param authId
	 * @param passPort
	 * @param membershipType
	 * @return
	 */
	List<MembershipBaseInfo> checkPassPortOccupied(@Param("authId") String authId,
												 @Param("passPort") String passPort,
												 @Param("membershipType") Integer membershipType);
	/**
	 * 更新用户档案编号
	 * @return
	 */
	int updateUserFileNo(UpdateFileNoInput input);

	/**
	 * 更新用户身份证图片信息
	 * @param membershipInfo
	 * @return
	 */
	int updateUserIdCardPic(MembershipInfo membershipInfo);

	/**
	 * 更新人脸图片
	 * @param membershipInfo
	 * @return
	 */
	int updateFaceRecognitionImgUrl(MembershipInfo membershipInfo);

	/**
	 * 查询需要重新ocr提取档案编号的用户数据  3.2.0上线后可删除
	 * @return
	 */
	List<MembershipInfoWithBLOBs> ocrDriverFileNoJob();


	/**
	 * 根据条件查询单个会员
	 * @param queryMemberInputDTO
	 * @return
	 */
    MembershipBasicInfo queryOneMemberByCondition(QueryMemberInputDTO queryMemberInputDTO);

	/**
	 * 根据条件查询会员列表
	 * @param queryMemberListInputDTO
	 * @return
	 */
	List<MembershipBasicInfo> queryMemberListByCondition(QueryMemberListInputDTO queryMemberListInputDTO);


	/**
	 * 更新会员信息
	 * @param updateMemberInputDTO
	 * @return
	 */
	int updateMemberInfo(UpdateMemberInputDTO updateMemberInputDTO);

	/**
	 * 查询更新时间在某个时间段的会员数据
	 * @param startTimeStr
	 * @param endTimeStr
	 * @return
	 */
	List<MembershipBasicInfo> selectMemberInfoByUpdateTime(@Param("startTimeStr") String startTimeStr, @Param("endTimeStr") String endTimeStr);

	/**
	 * 根据会员pkId获取会员基本信息
	 * @param pkId
	 * @return
	 */
    MembershipBasicInfo getUserBasicInfoByPkId(Long pkId);

	/**
	 * 根据会员mid获取会员基本信息
	 * @param mid
	 * @return
	 */
	MembershipBasicInfo getUserBasicInfoByMid(@Param("mid")String mid);

	/**
	 * 更新审核相关信息
	 * @param membershipInfo
	 * @return
	 */
	int updateReviewStatusByAuthId(MembershipInfo membershipInfo);

	/**
	 * 更新用户档案编号
	 * @return
	 */
	int updateUserFileNoAndStatus(MembershipInfo membershipInfo);

	/**
	 * 更新会员新驾照信息到会员表中
	 * @param membershipInfo
	 * @return
	 */
	int updateFromAdditionalInfoByAuthId(MembershipAdditionalInfo membershipInfo);

	/**
	 * 查询重庆会员-认证通过的会员
	 * @param orgId
	 * @param createdTime
	 * @param updatedTime
	 * @return
	 */
	List<MembershipBasicInfo> queryEffectiveMemberForCq(@Param("orgId")String orgId,@Param("createdTime")String createdTime,@Param("updatedTime")String updatedTime);

	/**
	 * 获取会员归属企业信息
	 * @param authId
	 * @param memberType
	 * @return
	 */
	UserOrgInfoDto getUserOrgInfo(@Param("authId")String authId,@Param("memberType")Integer memberType);


	/**
	 * 新增会员mmp
	 */
	Integer addMembershipInfoForMmp(MembershipInfo membershipInfo);


	/**
	 * 更新驾驶证审核状态
	 */
	 void updateDrivingLicenceReviewStatus(Map<String, Object> empMap);

	// 检验该用户是否制作过卡
	Integer checkHavecard(String authId);


	// 判断这个用户 是否在规定时间前 完成过认证
	int checkUserHavaAuth(@Param("authId")String authId,@Param("endTime")String licenseFirstAuthTimeLimit);


	/**
	 * 根据authIds批量查询用户信息
	 * @param authIds
	 * @return
	 * @since 3.2.0
	 */
	List<MembershipBasicInfo> selectByAuthIdsForYG(@Param("list") List<String> authIds);

	int getOrderCount(@Param("authId") String authId);

	/**
	 *  获取用户  通过mobilePhone、passport、memberType
	 */
	List<MembershipBasicInfo> getMemberList(@Param("mobilePhone")String mobilePhone,@Param("secondAppKey")String secondAppKey,@Param("passport")String passport,@Param("memberType")Integer memberType);

	String getOssUrlByVersion(@Param("version")String version,
							  @Param("provisionType") Integer provisionType);

	List<MembershipInfo> getChannelMember(@Param("id") Long id, @Param("limit") Integer limit);

	int clearIdentityByPrimaryKeySelective(MembershipInfoWithBLOBs record);

	int clearLicenseByPrimaryKeySelective(MembershipInfoWithBLOBs record);

	int updateFictional(MembershipInfoWithBLOBs record);

	int updateSecondAppKey(MembershipInfo membershipInfo);
}