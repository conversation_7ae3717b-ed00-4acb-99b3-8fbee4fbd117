<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.extracme.evcard.membership.core.dao.RegionMapper">

    <select id="batchQueryRegionName" resultType="map" parameterType="list">
        SELECT
         REGIONID as regionId,
         REGION as regionName
        FROM ${siacSchema}.operation_region
        where
        REGIONID in
        <foreach item="item" collection="list" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>