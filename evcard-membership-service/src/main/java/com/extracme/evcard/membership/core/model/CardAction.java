package com.extracme.evcard.membership.core.model;

public class CardAction {
	
    private Integer actionSeq;

    private String cardNo;

    private String vin;

    private Double doorStatus;

    private String createdTime;

    private String createdUser;

    private String updatedTime;

    private String updatedUser;

    private Integer returnCode;

    public Integer getActionSeq() {
        return actionSeq;
    }

    public void setActionSeq(Integer actionSeq) {
        this.actionSeq = actionSeq;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public Double getDoorStatus() {
        return doorStatus;
    }

    public void setDoorStatus(Double doorStatus) {
        this.doorStatus = doorStatus;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public String getCreatedUser() {
        return createdUser;
    }

    public void setCreatedUser(String createdUser) {
        this.createdUser = createdUser;
    }

    public String getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(String updatedTime) {
        this.updatedTime = updatedTime;
    }

    public String getUpdatedUser() {
        return updatedUser;
    }

    public void setUpdatedUser(String updatedUser) {
        this.updatedUser = updatedUser;
    }

    public Integer getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(Integer returnCode) {
        this.returnCode = returnCode;
    }
}