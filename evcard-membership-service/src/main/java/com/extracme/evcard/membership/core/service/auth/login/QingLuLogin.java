package com.extracme.evcard.membership.core.service.auth.login;

import com.alibaba.dubbo.common.Constants;
import com.alibaba.fastjson.JSON;
import com.baosight.iplat4j.security.util.Crypto;
import com.extracme.evcard.membership.common.MidGenerator;
import com.extracme.evcard.membership.core.dao.CityMapper;
import com.extracme.evcard.membership.core.dao.MemberIdentityDocumentMapper;
import com.extracme.evcard.membership.core.dao.MemberRelationMapper;
import com.extracme.evcard.membership.core.dao.SecondAppKeyManagerMapper;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.enums.MemberRelationTypeEnums;
import com.extracme.evcard.membership.core.enums.MemberTypeEnum;
import com.extracme.evcard.membership.core.input.ThirdLoginContext;
import com.extracme.evcard.membership.core.input.ThirdLoginInput;
import com.extracme.evcard.membership.core.input.ThirdLoginOtherDto;
import com.extracme.evcard.membership.core.model.*;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.membership.core.service.auth.MemberRelationService;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.model.MembershipInfo;
import com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs;
import com.extracme.evcard.redis.JedisLock;
import com.extracme.evcard.redis.JedisUtil;
import com.extracme.evcard.rpc.enums.StatusCode;
import com.extracme.evcard.rpc.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static com.extracme.evcard.membership.common.ComUtil.*;

/**
 * 擎路 登录注册
 */
@Slf4j
@Service
public class QingLuLogin extends ThirdLoginAbstract {

    @Autowired
    private IMemberShipService memberShipService;
    @Autowired
    private CityMapper cityMapper;
    @Resource
    private MidGenerator midGenerator;
    @Autowired
    private MembershipInfoMapper membershipInfoMapper;
    @Resource
    private MemberIdentityDocumentMapper memberIdentityDocumentMapper;
    @Resource
    private SecondAppKeyManagerMapper secondAppKeyManagerMapper;

    @Resource
    private MemberRelationService memberRelationService;

    @Override
    public String getSecondAppKey() {
        return "麒麟平台对应二级渠道";
    }

    @Override
    public String getDescription() {
        return "麒麟";
    }

    @Override
    protected int getMembershipType() {
        return MemberTypeEnum.QINGLU_USER.getValue();
    }

    @Override
    public void paramCheck(ThirdLoginContext context) throws BusinessException {
        ThirdLoginOtherDto otherDto = context.getOtherDto();
        ThirdLoginInput input = context.getInput();
        String cityId = input.getCityId();
        String idCardNo = input.getIdCardNo();
        String mobilePhone = input.getMobilePhone();
        String appKey = input.getAppKey();

        if (StringUtils.isBlank(mobilePhone) || StringUtils.isBlank(appKey) || StringUtils.isBlank(idCardNo)) {
            throw new BusinessException(-1, "入参格式不能为空");
        }

        if (!pattern.matcher(mobilePhone).matches()) {
            throw new BusinessException(StatusCode.MOBILE_FORMATE_ERROR);
        }

        SecondAppKeyManager secondAppKeyManager = secondAppKeyManagerMapper.selectBySecondAppKey(appKey);
        if (secondAppKeyManager == null) {
            throw new BusinessException(StatusCode.APPKEY_INVALID);
        }

        otherDto.setFirstAppKey(secondAppKeyManager.getFirstAppKey());
        otherDto.setPlatFormId(secondAppKeyManager.getPlatformId());

        try {
            String cityName = "";
            if (StringUtils.isNotEmpty(cityId)) {
                City city = cityMapper.getCityByCityId(cityId);
                if (city != null) {
                    cityName = city.getCity();
                }
            }
            if (StringUtils.isBlank(cityName)) {
                cityName = "上海市";
            }

            otherDto.setCityName(cityName);
        } catch (Exception e) {
            log.error("{}，获取注册城市异常，input={}", getDescription(),JSON.toJSONString(input), e);
        }

    }

    @Override
    public boolean userIsExist(ThirdLoginContext context) throws BusinessException {
        ThirdLoginInput input = context.getInput();
        String idCardNo = input.getIdCardNo();
        String mobilePhone = input.getMobilePhone();
        String appKey = input.getAppKey();

        List<MembershipBasicInfo> memberList = membershipInfoMapper.getMemberList(mobilePhone, appKey, idCardNo, getMembershipType());
        if (CollectionUtils.isNotEmpty(memberList)) {
            if (memberList.size() == 1) {
                MembershipBasicInfo membershipBasicInfo = memberList.get(0);
                context.setMembershipBasicInfo(membershipBasicInfo);
                return true;
            }
            log.error("{}，判断用户是否存在，查到多条记录，ThirdLoginInput=[{}]", getDescription(),JSON.toJSONString(input));
            throw new BusinessException("查询到多条用户记录");
        } else {
            return false;
        }
    }

    @Override
    public void sendMessage(ThirdLoginContext context) {
        // 不处理
    }

    @Override
    protected MembershipBasicInfo doRegisterMembershipInfo(ThirdLoginContext context) throws BusinessException {
        ThirdLoginOtherDto otherDto = context.getOtherDto();
        String cityName = otherDto.getCityName();
        ThirdLoginInput input = context.getInput();
        log.info("{}，doRegisterMembershipInfo,input[{}]", getDescription(),JSON.toJSONString(input));
        String mobilePhone = input.getMobilePhone();
        String appKey = input.getAppKey();
        String idCardNo = input.getIdCardNo();
        String userName = input.getUserName();

        String redisKey = mobilePhone + Constants.COMMA_SEPARATOR + appKey + Constants.COMMA_SEPARATOR
                + idCardNo + Constants.COMMA_SEPARATOR + getMembershipType();
        Jedis jedis = null;
        JedisLock jedisLock = null;
        try {
            //防止手机号并发注册
            jedis = JedisUtil.getJedis();
            jedisLock = new JedisLock(redisKey);
            if (!jedisLock.acquire(jedis)) {
                throw new BusinessException(StatusCode.SYSTEM_ERROR);
            }

            // 密码校验
            String password = mobilePhone.substring(1, 6).trim();
            //加密密码
            password = Crypto.encryptDES(password, ENCRYPT_KEY);
            //生成会员ID
            LocalDateTime localDateTime = LocalDateTime.now();
            String authId = mobilePhone + localDateTime.format(DateTimeFormatter.ofPattern(DATE_TYPE6));
            //插入记录
            MembershipInfoWithBLOBs member = new MembershipInfoWithBLOBs();
            member.setAuthId(authId);
            member.setName(userName);
            member.setPassword(password);
            member.setMobilePhone(mobilePhone);
            member.setCityOfOrigin(cityName);
            member.setMembershipType((short)getMembershipType());
            member.setIdCardNumber(idCardNo);
            member.setPassportNo(idCardNo);
            //审核状态
            member.setReviewStatus((short) -1);
            // TODO  注册来源擎路
            //member.setDataOrigin((short) registerDto.getRegisterOrigin());
            member.setCreatedTime(localDateTime.format(DateTimeFormatter.ofPattern(DATE_TYPE3)));
            member.setCreatedUser(userName);
            member.setUpdatedTime(localDateTime.format(DateTimeFormatter.ofPattern(DATE_TYPE3)));
            member.setUpdatedUser(userName);
            member.setRegTime(localDateTime.format(DateTimeFormatter.ofPattern(DATE_TYPE4)));

            // 注册来源
            member.setSecondAppKey(appKey);
            member.setAppKey(otherDto.getFirstAppKey());
            member.setPlatformId(otherDto.getPlatFormId());
            member.setMid(midGenerator.getMid());

            // 新增记录
            MemberIdentityDocument insertDocument = insertIdentityDocument(input, member.getMid());
            member.setIdentityId(insertDocument.getId());
            int insertSelective = membershipInfoMapper.insertSelective(member);

            if (insertSelective > 0) {
                log.info(getDescription()+",mobile=" + mobilePhone + "insertSelective成功");
                MembershipBasicInfo membership = membershipInfoMapper.getUserBasicInfoByMid(member.getMid());
                context.setMembershipBasicInfo(membership);
                return membership;
            } else {
                throw new BusinessException(-1, "新增会员失败");
            }
        } catch (Exception e) {
            log.error(getDescription()+",注册失败,原因：创建会员记录失败，" + e.getMessage(), e);
            throw new BusinessException(StatusCode.SYSTEM_ERROR);
        } finally {
            if (jedisLock != null && jedis != null) {
                jedisLock.releaseLua(jedis);
                jedis.close();
                log.debug("擎路注册服务-释放redis锁和链接成功");
            }
        }

    }

    /**
     * 新增 MemberIdentityDocument记录
     *
     * @param input
     * @param mid
     * @return
     */
    private MemberIdentityDocument insertIdentityDocument(ThirdLoginInput input, String mid) throws BusinessException {
        MemberIdentityDocument insertDocument = new MemberIdentityDocument();
        insertDocument.setMid(mid);
        insertDocument.setIdentityNo(input.getIdCardNo());
        insertDocument.setName(StringUtils.EMPTY);
        insertDocument.setCreateTime(new Date());
        insertDocument.setCreateOperName(input.getUserName());
        int i = memberIdentityDocumentMapper.insertSelective(insertDocument);
        if (i < 0) {
            throw new BusinessException(-1, "新增MemberIdentityDocument记录失败");
        }
        return insertDocument;
    }


    @Override
    public void afterGetMembershipInfo(ThirdLoginContext context) throws BusinessException {
        MembershipBasicInfo membershipInfo = context.getMembershipBasicInfo();
        //制卡，该方法已实现幂等
        if (StringUtils.isBlank(membershipInfo.getCardNo())) {
            log.info("{}membershipInfo=[{}],开始制卡", getDescription(),JSON.toJSONString(membershipInfo));
            memberShipService.setVirtualCard(membershipInfo.getAuthId(),getMembershipType());
            log.info("{}membershipInfo=[{}],制卡成功", getDescription(),JSON.toJSONString(membershipInfo));
        }

        // 保存渠道和内部会员 关联关系
        memberRelationService.bindRelation(membershipInfo.getMobilePhone(),1,membershipInfo.getPkId());
    }


    @Override
    public boolean isNeedToken(ThirdLoginInput object) {
        return false;
    }
}
