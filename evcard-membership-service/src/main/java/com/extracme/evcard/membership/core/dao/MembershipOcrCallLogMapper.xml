<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.extracme.evcard.membership.core.dao.MembershipOcrCallLogMapper">
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.MembershipOcrCallLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="mid" jdbcType="VARCHAR" property="mid" />
    <result column="channel" jdbcType="INTEGER" property="channel" />
    <result column="ocr_type" jdbcType="INTEGER" property="ocrType" />
    <result column="req_url" jdbcType="VARCHAR" property="reqUrl" />
    <result column="req_params" jdbcType="VARCHAR" property="reqParams" />
    <result column="resp_params" jdbcType="VARCHAR" property="respParams" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_oper_id" jdbcType="BIGINT" property="createOperId" />
    <result column="create_oper_name" jdbcType="VARCHAR" property="createOperName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_oper_id" jdbcType="BIGINT" property="updateOperId" />
    <result column="update_oper_name" jdbcType="VARCHAR" property="updateOperName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, mid, channel, ocr_type, req_url, req_params, resp_params, is_deleted, create_time, 
    create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ${siacSchema}.membership_ocr_call_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ${siacSchema}.membership_ocr_call_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.membership.core.model.MembershipOcrCallLog">
    insert into ${siacSchema}.membership_ocr_call_log (id, mid, channel,
      ocr_type, req_url, req_params, 
      resp_params, is_deleted, create_time, 
      create_oper_id, create_oper_name, update_time, 
      update_oper_id, update_oper_name)
    values (#{id,jdbcType=BIGINT}, #{mid,jdbcType=VARCHAR}, #{channel,jdbcType=INTEGER}, 
      #{ocrType,jdbcType=INTEGER}, #{reqUrl,jdbcType=VARCHAR}, #{reqParams,jdbcType=VARCHAR}, 
      #{respParams,jdbcType=VARCHAR}, #{isDeleted,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{createOperId,jdbcType=BIGINT}, #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updateOperId,jdbcType=BIGINT}, #{updateOperName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.core.model.MembershipOcrCallLog">
    insert into ${siacSchema}.membership_ocr_call_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="mid != null">
        mid,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="ocrType != null">
        ocr_type,
      </if>
      <if test="reqUrl != null">
        req_url,
      </if>
      <if test="reqParams != null">
        req_params,
      </if>
      <if test="respParams != null">
        resp_params,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createOperId != null">
        create_oper_id,
      </if>
      <if test="createOperName != null">
        create_oper_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateOperId != null">
        update_oper_id,
      </if>
      <if test="updateOperName != null">
        update_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="mid != null">
        #{mid,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=INTEGER},
      </if>
      <if test="ocrType != null">
        #{ocrType,jdbcType=INTEGER},
      </if>
      <if test="reqUrl != null">
        #{reqUrl,jdbcType=VARCHAR},
      </if>
      <if test="reqParams != null">
        #{reqParams,jdbcType=VARCHAR},
      </if>
      <if test="respParams != null">
        #{respParams,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        #{updateOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.core.model.MembershipOcrCallLog">
    update ${siacSchema}.membership_ocr_call_log
    <set>
      <if test="mid != null">
        mid = #{mid,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=INTEGER},
      </if>
      <if test="ocrType != null">
        ocr_type = #{ocrType,jdbcType=INTEGER},
      </if>
      <if test="reqUrl != null">
        req_url = #{reqUrl,jdbcType=VARCHAR},
      </if>
      <if test="reqParams != null">
        req_params = #{reqParams,jdbcType=VARCHAR},
      </if>
      <if test="respParams != null">
        resp_params = #{respParams,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.core.model.MembershipOcrCallLog">
    update ${siacSchema}.membership_ocr_call_log
    set mid = #{mid,jdbcType=VARCHAR},
      channel = #{channel,jdbcType=INTEGER},
      ocr_type = #{ocrType,jdbcType=INTEGER},
      req_url = #{reqUrl,jdbcType=VARCHAR},
      req_params = #{reqParams,jdbcType=VARCHAR},
      resp_params = #{respParams,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>