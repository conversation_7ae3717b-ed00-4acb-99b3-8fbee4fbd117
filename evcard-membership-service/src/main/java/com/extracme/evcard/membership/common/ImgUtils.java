package com.extracme.evcard.membership.common;

import org.aspectj.util.FileUtil;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.stream.ImageInputStream;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

/**
 * 功能：图片
 *
 * <AUTHOR>
 * @since 2017/4/18
 */
@SuppressWarnings("restriction")
public class ImgUtils {

    /**
     * 将byte数组以Base64方式编码为字符串
     * 
     * @param bytes
     *            待编码的byte数组
     * @return 编码后的字符串
     */
    public static String encode(byte[] bytes) {
        return new BASE64Encoder().encode(bytes);
    }

    /**
     * 将以Base64方式编码的字符串解码为byte数组
     * 
     * @param encodeStr
     *            待解码的字符串
     * @return 解码后的byte数组
     * @throws IOException
     */
    public static byte[] decode(String encodeStr) throws IOException {
        byte[] bt = null;
        BASE64Decoder decoder = new BASE64Decoder();
        bt = decoder.decodeBuffer(encodeStr);
        return bt;
    }

    /**
     * 将两个byte数组连接起来后，返回连接后的Byte数组
     * 
     * @param front
     *            拼接后在前面的数组
     * @param after
     *            拼接后在后面的数组
     * @return 拼接后的数组
     */
    public static byte[] connectBytes(byte[] front, byte[] after) {
        byte[] result = new byte[front.length + after.length];
        System.arraycopy(front, 0, result, 0, after.length);
        System.arraycopy(after, 0, result, front.length, after.length);
        return result;
    }

    /**
     * 将图片以Base64方式编码为字符串
     * 
     * @param imgUrl
     *            图片的绝对路径（例如：D:\\jsontest\\abc.jpg）
     * @return 编码后的字符串
     * @throws IOException
     */
    public static String encodeImage(String imgUrl) throws IOException {
        FileInputStream fis = new FileInputStream(imgUrl);
        byte[] rs = new byte[fis.available()];
        fis.read(rs);
        fis.close();
        return encode(rs);
    }

    /***
     * 压缩Zip
     * 
     * @param data
     * @return
     */
    public static byte[] zip(byte[] data) {
        byte[] b = null;
        try {
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            ZipOutputStream zip = new ZipOutputStream(bos);
            ZipEntry entry = new ZipEntry("zip");
            entry.setSize(data.length);
            zip.putNextEntry(entry);
            zip.write(data);
            zip.closeEntry();
            zip.close();
            b = bos.toByteArray();
            bos.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return b;
    }

    /***
     * 解压Zip
     * 
     * @param data
     * @return
     */
    public static byte[] unZip(byte[] data) {
        byte[] b = null;
        try {
            ByteArrayInputStream bis = new ByteArrayInputStream(data);
            ZipInputStream zip = new ZipInputStream(bis);
            while (zip.getNextEntry() != null) {
                byte[] buf = new byte[1024];
                int num = -1;
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                while ((num = zip.read(buf, 0, buf.length)) != -1) {
                    baos.write(buf, 0, num);
                }
                b = baos.toByteArray();
                baos.flush();
                baos.close();
            }
            zip.close();
            bis.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return b;
    }
    
    /**
     * 图片后缀的格式检验
     * @param file  文件
     * @param imageType  后缀格式，如"JPEG,png.."
     * @return true:符合imageType格式; false:不符合
     * @throws IOException
     */
    public static boolean checkImageType(File file, String imageType) throws IOException
    {
        if(!file.exists())
        {
            return false;
        }
        boolean result = false;
        ImageInputStream iis = ImageIO.createImageInputStream(file);
        Iterator<ImageReader> readers = ImageIO.getImageReaders(iis);
        ImageReader reader = null;
        if(readers.hasNext())
        {
            reader = readers.next();
        }
        if(reader.getFormatName().equals(imageType))
        {
            result = true;
        }
        //System.out.println(reader.getFormatName());
        return result;
    }
    
    /**
     * 图片的像素判断
     * @param file  文件
     * @param imageWidth  图片宽度
     * @param imageHeight   图片高度
     * @return true:上传图片宽度和高度都小于等于规定最大值
     * @throws IOException
     */
    public static boolean checkImageElement(File file, int imageWidth, int imageHeight) throws IOException
    {
        boolean result = false;
        if(!file.exists())
        {
            return false;
        }
        BufferedImage bufferedImage = ImageIO.read(file);
        if(bufferedImage != null && bufferedImage.getHeight() == imageHeight && bufferedImage.getWidth() == imageWidth)
        {
            result = true;
        }
        return result;
    }
    
    /**
     * 检测图片的大小
     * @param file 文件
     * @param imageSize 图片最大值(KB)
     * @return true:上传图片小于图片的最大值
     */
    public static boolean checkImageSize(File file, int imageSize)
    {
        boolean result = false;
        if(!file.exists())
        {
            return false;
        }
        if((file.length() / 1024) <= imageSize)
        {
            result = true;
        }
        
        return result;
    }


    /**
     * 从zip的inputStream中读出map<文件名，内容>
     * @param inputStream
     * @return
     * @throws IOException
     */
    public static Map<String, byte[]> readZipByInputStream(InputStream inputStream) throws IOException {
        Map<String, byte[]> map = new HashMap<>();
        try {
            ZipInputStream zip;
            zip = new ZipInputStream(inputStream);
            ZipEntry zipEntry = null;
            while ((zipEntry = zip.getNextEntry()) != null) {
                String filename = zipEntry.getName();
                if (zipEntry.isDirectory()) {
                    //if(FileUtil.isDirEnd(filename)) {
                    filename += "\\";
                    //}
                }
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                byte[] byteTemp = new byte[1024];
                int num = -1;
                while ((num = zip.read(byteTemp, 0, byteTemp.length)) > -1) {
                    byteArrayOutputStream.write(byteTemp, 0, num);
                }
                byte[] bytes = byteArrayOutputStream.toByteArray();
                map.put(filename, bytes);
            }
        }finally {
            inputStream.close();
        }
        return map;
    }

}
