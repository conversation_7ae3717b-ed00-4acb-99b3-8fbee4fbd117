package com.extracme.evcard.membership.core.service;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import com.extracme.evcard.membership.core.input.CardInfoHistoryInputDTO;
import com.extracme.evcard.membership.core.input.QueryCardInfoConditionInput;
import com.extracme.evcard.membership.core.input.QueryCardInfoHistoryListIConditionInput;
import com.extracme.evcard.rpc.dto.Page;
import com.extracme.evcard.rpc.dto.PageBeanDto;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.dao.CardInfoHistroyMapper;
import com.extracme.evcard.membership.core.dao.CardInfoMapper;
import com.extracme.evcard.membership.core.dao.CardPauseLogMapper;
import com.extracme.evcard.membership.core.dao.MmpUserOperationLogMapper;
import com.extracme.evcard.membership.core.dao.OrgCardManageMapper;
import com.extracme.evcard.membership.core.dao.UserOperatorLogMapper;
import com.extracme.evcard.membership.core.dto.CardInfoDTO;
import com.extracme.evcard.membership.core.dto.CardInfoHistoryDTO;
import com.extracme.evcard.membership.core.dto.CardOperateDto;
import com.extracme.evcard.membership.core.dto.CardPauseLogDTO;
import com.extracme.evcard.membership.core.dto.CardPauseQueryDto;
import com.extracme.evcard.membership.core.dto.UpdateUserDto;
import com.extracme.evcard.membership.core.enums.MemOperateTypeEnum;
import com.extracme.evcard.membership.core.exception.CardOperateException;
import com.extracme.evcard.membership.core.input.UserOperationLogInput;
import com.extracme.evcard.membership.core.model.CardInfo;
import com.extracme.evcard.membership.core.model.CardInfoHistroy;
import com.extracme.evcard.membership.core.model.CardPauseLog;
import com.extracme.evcard.membership.core.model.OrgCardManage;
import com.extracme.evcard.membership.core.model.UserOperatorLog;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.model.MembershipBaseInfo;

/**
 * <AUTHOR>
 */
@Service("memberCardService")
public class MemberCardServiceImpl implements IMemberCardService {
	private static Logger logger = LoggerFactory.getLogger(MemberCardServiceImpl.class);

	@Autowired
	private MembershipInfoMapper membershipInfoMapper;

	@Autowired
	private MmpUserOperationLogMapper mmpUserOperationLogMapper;

	@Autowired
	private CardInfoMapper cardInfoMapper;

	@Autowired
	private CardInfoHistroyMapper cardInfoHistroyMapper;

	@Autowired
	private CardPauseLogMapper cardPauseLogMapper;
	@Autowired
	private OrgCardManageMapper orgCardManageMapper;

	@Autowired
	private UserOperatorLogMapper userOperatorLogMapper;

	@Autowired
	IMemberShipService memberShipService;

	/**
	 * 卡状态：0：暂停 1:恢复 2:注销
	 */
	private Integer CARD_STATUS_COMMON = 0; // 已激活
	private Integer CARD_STATUS_PAUSED = 1; // 已暂停

	/**
	 * 操作类别：0：暂停 1:恢复 2:注销
	 */
	private Integer CARD_OPERATE_PAUSE = 0; // 卡暂停
	private Integer CARD_OPERATE_RESUME = 1; // 卡恢复
	private Integer CARD_OPERATE_CANCEL = 2; // 卡注销

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void cardPause(CardOperateDto cardOperateDto, UpdateUserDto updateUserDto) throws CardOperateException {
		logger.info("会员卡服务-卡暂停|入参={}", JSON.toJSONString(cardOperateDto));
		// 1. 输入参数校验
		cardOperateDto.setOptionType(CARD_OPERATE_PAUSE);
		checkCardOperator(cardOperateDto);

		// 2. 卡状态校验
		String cardNo = cardOperateDto.getCardNo();
		String authId = cardOperateDto.getAuthId();
		// 卡信息不存在
		List<CardInfo> cardList = cardInfoMapper.getCardInfo(cardNo);
		if (CollectionUtils.isEmpty(cardList)) {
			throw CardOperateException.CARDNO_NOT_EXESIT;
		}
		// 会员信息不存在
		MembershipBaseInfo member = membershipInfoMapper.selectBaseByAuthId(authId, cardOperateDto.getMembershipType());
		if (member == null) {
			throw CardOperateException.MEMBER_NOT_EXESIT;
		}
		// 卡未绑定
		CardInfo cardInfo = cardList.get(0);
		if (!authId.equals(cardInfo.getAuthId())) {
			logger.warn("会员卡服务-卡暂停|authId={}当前未绑定卡号={}，不能暂停.", authId, cardNo);
			throw CardOperateException.CARDNO_AUTHID_NOTMATCH;
		}
		if (!CARD_STATUS_COMMON.equals(cardInfo.getStatus())) {
			logger.warn("会员卡服务-卡暂停|authId={}，卡号={}，卡已暂停或注销，不能再次暂停.", authId, cardNo);
			throw CardOperateException.FAILED_ALREADY_STOPED;
		}

		CardPauseLog pauseLog = cardPauseLogMapper.selectPauseLogByCardNo(cardNo);
		if (pauseLog != null && pauseLog.getPauseStatus() != 1) {
			logger.error("会员卡服务-卡暂停|authId={}，卡号={}，卡已暂停无需再次暂停.", authId, cardNo);
			throw CardOperateException.FAILED_ALREADY_STOPED;
		}

		// 3. 卡状态更新
		String updateTime = ComUtil.getFormatDate(updateUserDto.getUpdateTime(), ComUtil.DATE_TYPE3);

		// 更新会员卡表
		CardInfo cardInfoRecord = new CardInfo();
		cardInfoRecord.setAuthId(cardOperateDto.getAuthId());
		cardInfoRecord.setCardNo(cardOperateDto.getCardNo());
		cardInfoRecord.setStatus(CARD_STATUS_PAUSED); // 已暂停
		cardInfoRecord.setUpdatedTime(updateTime);
		cardInfoRecord.setUpdatedUser(updateUserDto.getUserName());
		int result = cardInfoMapper.updateCardStatusByCardNo(cardInfoRecord);
		if (result == 0) {
			throw CardOperateException.FAILED_PAUSE_CARD;
		}

		// 更新会员卡历史记录
		CardInfoHistroy cardInfoHistroy = new CardInfoHistroy();
		cardInfoHistroy.setAuthId(cardOperateDto.getAuthId());
		cardInfoHistroy.setCardNo(cardOperateDto.getCardNo());
		cardInfoHistroy.setRemark(cardOperateDto.getRemark());
		cardInfoHistroy.setCardStatus(2D);
		cardInfoHistroy.setUpdatedTime(updateTime);
		cardInfoHistroy.setUpdatedUser(updateUserDto.getUserName());
		cardInfoHistroyMapper.updateCardInfoHistroy(cardInfoHistroy);

		// 更新企业会员卡管理表
		OrgCardManage orgCardManage = new OrgCardManage();
		orgCardManage.setAuthId(authId);
		orgCardManage.setCardNo(cardNo);
		orgCardManage.setStatus("1");
		orgCardManage.setUpdatedTime(updateTime);
		orgCardManage.setUpdatedUser(updateUserDto.getUserName());
		orgCardManageMapper.updateOrgCardManaStatus(orgCardManage);

		// 更新callcenter相关操作记录
		CardPauseLog cardPauseLog = new CardPauseLog();
		cardPauseLog.setAuthId(authId);
		cardPauseLog.setCardNo(cardNo);
		cardPauseLog.setName(StringUtils.isBlank(member.getName()) ? "" : member.getName());
		cardPauseLog.setMobilePhone(StringUtils.isBlank(member.getMobilePhone()) ? "" : member.getMobilePhone());
		cardPauseLog.setPauseReason(cardOperateDto.getRemark());
		cardPauseLog.setPauseStatus(CARD_OPERATE_PAUSE);
		cardPauseLog.setCreatedUser(updateUserDto.getUserName());
		cardPauseLog.setCreatedTime(updateTime);
		cardPauseLog.setUpdatedUser(updateUserDto.getUserName());
		cardPauseLog.setUpdatedTime(updateTime);
		String recoverTimeDesc = buildCardRecoverTime(cardOperateDto, cardPauseLog);
		cardPauseLogMapper.insert(cardPauseLog);

		// 4. 添加操作日志
		StringBuffer content = new StringBuffer("暂停会员卡【" + cardInfo.getInternalNo() + "】");
		if (StringUtils.isNotBlank(recoverTimeDesc)) {
			content.append("至").append(recoverTimeDesc);
		}
		content.append(",原因：").append(cardOperateDto.getRemark());
		UserOperatorLog userOperatorLog = new UserOperatorLog();
		userOperatorLog.setForeignKey(authId);
		userOperatorLog.setForeignKey2(updateUserDto.getUserId().toString());
		userOperatorLog.setOperatorContent(content.toString());
		userOperatorLog.setCreatedUser(updateUserDto.getUserName());
		userOperatorLog.setCreatedTime(updateTime);
		userOperatorLogMapper.saveSelective(userOperatorLog);
		// 记录会员操作日志表
		UserOperationLogInput mmpUserLog = new UserOperationLogInput();
		mmpUserLog.setUserId(member.getPkId());
		mmpUserLog.setRefKey1(authId);
		mmpUserLog.setRefKey2(cardNo);
		mmpUserLog.setRefKey3(cardInfo.getInternalNo());
		mmpUserLog.setOperationType(MemOperateTypeEnum.CARD_PAUSE.getCode());
		mmpUserLog.setOperationContent(content.toString());
		mmpUserLog.setOperatorId(updateUserDto.getUserId());
		mmpUserLog.setOperator(updateUserDto.getUserName());
		memberShipService.saveUserOperationLog(mmpUserLog);

		logger.info("会员卡服务-卡暂停|完成，入参={}" + JSON.toJSONString(cardOperateDto));
	}

	private String buildCardRecoverTime(CardOperateDto cardOperateDto, CardPauseLog cardPauseLog) {
		Integer pauseTimeType = cardOperateDto.getPauseTimeType();
		String recoverTimeDesc = "";
		if (null != pauseTimeType) {
			// 获取当前时间并设置为0点
			Calendar calendar = Calendar.getInstance();
			if (pauseTimeType == 2) {
				// 永久暂停
				cardPauseLog.setRecoverTime(0L);
				cardPauseLog.setPauseStatus(CARD_OPERATE_CANCEL);
				recoverTimeDesc = "-";
			} else {
				if (pauseTimeType == 0) { // +7天
					calendar.add(Calendar.DAY_OF_MONTH, 7);
				} else if (pauseTimeType == 1) { // 加30天
					calendar.add(Calendar.DAY_OF_MONTH, 30);
				}
				cardPauseLog.setRecoverTime(calendar.getTimeInMillis());
				recoverTimeDesc = DateFormatUtils.ISO_DATE_FORMAT.format(calendar);
			}
		} else if (StringUtils.isNotBlank(cardOperateDto.getRecoverTime())) {
			try {
				Date recoverDate = DateFormatUtils.ISO_DATE_FORMAT.parse(cardOperateDto.getRecoverTime());
				cardPauseLog.setRecoverTime(recoverDate.getTime());
				recoverTimeDesc = cardOperateDto.getRecoverTime();
			} catch (Exception e) {
				cardPauseLog.setRecoverTime(0L);
				logger.error("会员卡服务-卡暂停|卡恢复时间参数转换失败，authId=" + cardOperateDto.getAuthId() + "卡号="
						+ cardOperateDto.getCardNo() + "recoverTime=" + cardOperateDto.getRecoverTime(), e);
			}
		}
		return recoverTimeDesc;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void cardRecover(CardOperateDto cardOperateDto, UpdateUserDto updateUserDto) throws CardOperateException {
		logger.info("会员卡服务-卡恢复|开始，入参={}", JSON.toJSONString(cardOperateDto));
		// 1. 输入参数校验
		cardOperateDto.setOptionType(CARD_OPERATE_RESUME);
		checkCardOperator(cardOperateDto);

		// 2. 卡信息校验
		String cardNo = cardOperateDto.getCardNo();
		String authId = cardOperateDto.getAuthId();
		Integer memberType = cardOperateDto.getMembershipType();
		List<CardInfo> cardList = cardInfoMapper.getCardInfo(cardNo);
		if (CollectionUtils.isEmpty(cardList)) {
			throw CardOperateException.CARDNO_NOT_EXESIT;
		}
		// 会员信息不存在
		MembershipBaseInfo member = membershipInfoMapper.selectBaseByAuthId(authId, memberType);
		if (member == null) {
			throw CardOperateException.MEMBER_NOT_EXESIT;
		}
		// 卡未绑定
		CardInfo cardInfo = cardList.get(0);
		if (!authId.equals(cardInfo.getAuthId())) {
			logger.warn("会员卡服务-卡恢复|authId={}当前未绑定卡号={}，不能恢复.", authId, cardNo);
			throw CardOperateException.CARDNO_AUTHID_NOTMATCH;
		}
		if (CARD_STATUS_COMMON.equals(cardInfo.getStatus())) {
			logger.warn("会员卡服务-卡恢复|authId={}，卡号={}，卡状态正常无需恢复.", authId, cardNo);
			throw CardOperateException.FAILED_NOT_NEED_RECOVER;
		}

		// 3. 卡状态更新
		String updateTime = ComUtil.getFormatDate(updateUserDto.getUpdateTime(), ComUtil.DATE_TYPE3);

		// 更新会员卡表
		CardInfo cardInfoRecord = new CardInfo();
		cardInfoRecord.setAuthId(cardOperateDto.getAuthId());
		cardInfoRecord.setCardNo(cardOperateDto.getCardNo());
		cardInfoRecord.setStatus(CARD_STATUS_COMMON); // 已激活
		cardInfoRecord.setUpdatedTime(updateTime);
		cardInfoRecord.setUpdatedUser(updateUserDto.getUserName());
		int result = cardInfoMapper.updateCardStatusByCardNo(cardInfoRecord);
		if (result == 0) {
			throw CardOperateException.FAILED_RECOVER_CARD;
		}

		// 更新会员卡历史记录
		CardInfoHistroy cardInfoHistroy = new CardInfoHistroy();
		cardInfoHistroy.setAuthId(cardOperateDto.getAuthId());
		cardInfoHistroy.setCardNo(cardOperateDto.getCardNo());
		cardInfoHistroy.setRemark(cardOperateDto.getRemark());
		cardInfoHistroy.setCardStatus(cardInfo.getActivateStatus());
		cardInfoHistroy.setUpdatedTime(updateTime);
		cardInfoHistroy.setUpdatedUser(updateUserDto.getUserName());
		cardInfoHistroyMapper.updateCardInfoHistroy(cardInfoHistroy);

		// 更新企业会员卡管理表
		OrgCardManage orgCardManage = new OrgCardManage();
		orgCardManage.setAuthId(authId);
		orgCardManage.setCardNo(cardNo);
		orgCardManage.setStatus("0");
		orgCardManage.setUpdatedTime(updateTime);
		orgCardManage.setUpdatedUser(updateUserDto.getUserName());
		orgCardManageMapper.updateOrgCardManaStatus(orgCardManage);

		// 更新callcenter相关操作记录
		CardPauseLog cardPauseLog = new CardPauseLog();
		cardPauseLog.setCardNo(cardOperateDto.getCardNo());
		cardPauseLog.setUpdatedUser(updateUserDto.getUserName());
		cardPauseLog.setUpdatedTime(updateTime);
		cardPauseLogMapper.updateAllPauseStatusByCardNo(cardPauseLog);

		// 4. 添加操作日志
		String content = "恢复会员卡【" + cardInfo.getInternalNo() + "】";
		UserOperatorLog userOperatorLog = new UserOperatorLog();
		userOperatorLog.setForeignKey(authId);
		userOperatorLog.setForeignKey2(updateUserDto.getUserId().toString());
		userOperatorLog.setOperatorContent(content);
		userOperatorLog.setCreatedUser(updateUserDto.getUserName());
		userOperatorLog.setCreatedTime(updateTime);
		userOperatorLogMapper.saveSelective(userOperatorLog);
		// 记录会员操作日志表
		UserOperationLogInput mmpUserLog = new UserOperationLogInput();
		mmpUserLog.setUserId(member.getPkId());
		mmpUserLog.setRefKey1(authId);
		mmpUserLog.setRefKey2(cardNo);
		mmpUserLog.setRefKey3(cardInfo.getInternalNo());
		mmpUserLog.setOperationType(MemOperateTypeEnum.CARD_RESUME.getCode());
		mmpUserLog.setOperationContent(content);
		mmpUserLog.setOperatorId(updateUserDto.getUserId());
		mmpUserLog.setOperator(updateUserDto.getUserName());
		memberShipService.saveUserOperationLog(mmpUserLog);

		logger.info("会员卡服务-卡恢复|完成，入参={}" + JSON.toJSONString(cardOperateDto));
	}

	@Override
	public void cardCancel(CardOperateDto cardOperateDto, UpdateUserDto updateUserDto) throws CardOperateException {

	}

	private void checkCardOperator(CardOperateDto cardOperateDto) throws CardOperateException {
		// 预处理
		if (null == cardOperateDto.getMembershipType()) {
			cardOperateDto.setMembershipType(0);
		}
		if (null == cardOperateDto.getRemark()) {
			cardOperateDto.setRemark("");
		}

		// 参数校验
		if (StringUtils.isBlank(cardOperateDto.getAuthId())) {
			throw CardOperateException.EMPTY_AUTHID;
		}
		if (StringUtils.isBlank(cardOperateDto.getCardNo())) {
			throw CardOperateException.EMPTY_CARD_ID;
		}
		if (CARD_OPERATE_PAUSE.equals(cardOperateDto.getOptionType())) {
			// 暂停必须注明原因
			if (StringUtils.isBlank(cardOperateDto.getRemark())) {
				throw CardOperateException.EMPTY_REMARK;
			}
			if (cardOperateDto.getPauseTimeType() == null) {
				// 既未指定恢复时间，又未指定暂停模式, 则默认永久暂停
				if (StringUtils.isBlank(cardOperateDto.getRecoverTime())) {
					cardOperateDto.setPauseTimeType(2);
				}
				// 指定了恢复时间，则校验时间格式。
				else {
					try {
						Date recoverDate = DateFormatUtils.ISO_DATE_FORMAT.parse(cardOperateDto.getRecoverTime());
						if (recoverDate.getTime() < System.currentTimeMillis()) {
							throw CardOperateException.EARLY_RECOVERTIME;
						}
					} catch (Exception e) {
						logger.error(
								"会员卡服务-卡暂停|卡恢复时间参数转换失败，authId=" + cardOperateDto.getAuthId() + "卡号="
										+ cardOperateDto.getCardNo() + "recoverTime=" + cardOperateDto.getRecoverTime(),
								e);
						throw CardOperateException.ERROR_RECOVERTIME;
					}
				}
			}
		}

		if (StringUtils.isNotBlank(cardOperateDto.getRemark()) && cardOperateDto.getRemark().length() > 100) {
			throw CardOperateException.UNVALID_REMARK;
		}
	}

	@Override
	public CardPauseQueryDto queryCardPauseLogByCardNo(String cardNo) {
		return cardPauseLogMapper.queryPauseLogByCardNo(cardNo);
	}

	@Override
	public Integer queryCardStatus(String cardNo) {
		List<CardInfo> list = cardInfoMapper.getCardInfo(cardNo);
		if (CollectionUtils.isNotEmpty(list)) {
			CardInfo cardInfo = list.get(0);
			return cardInfo.getStatus();
		}
		return null;
	}

	@Override
	public CardInfoDTO queryCardInfo(String cardNo) {
		List<CardInfo> list = cardInfoMapper.getCardInfo(cardNo);
		if (CollectionUtils.isNotEmpty(list)) {
			CardInfo cardInfo = list.get(0);
			CardInfoDTO cardInfoDTO = new CardInfoDTO();
			BeanUtils.copyProperties(cardInfo,cardInfoDTO);
			cardInfoDTO.setCardType(cardInfo.getCardType().intValue());
			cardInfoDTO.setActivateStatus(cardInfo.getActivateStatus().intValue());
			return cardInfoDTO;
		}
		return null;
	}

	@Override
	public List<CardInfoDTO> queryCardInfoList(String authId) {
		List<CardInfoDTO> cardInfoDTOList = null;
		List<CardInfo> cardInfoList = cardInfoMapper.queryCardInfoByAuthId(authId);
        if(CollectionUtils.isNotEmpty(cardInfoList)){
			cardInfoDTOList = new ArrayList<>(cardInfoList.size());
        	for(CardInfo cardInfo : cardInfoList){
				CardInfoDTO cardInfoDTO = new CardInfoDTO();
				BeanUtils.copyProperties(cardInfo,cardInfoDTO);
				cardInfoDTOList.add(cardInfoDTO);
			}
		}
		return cardInfoDTOList;
	}

	@Override
	public List<CardInfoHistoryDTO> queryCardInfoHistoryList(String authId) {
		QueryCardInfoHistoryListIConditionInput conditionInput = new QueryCardInfoHistoryListIConditionInput();
		conditionInput.setAuthId(authId);
		conditionInput.setMemberType(0);
		conditionInput.addOrderCondition(QueryCardInfoHistoryListIConditionInput.OrderColumnNameEnum.CREATE_TIME,true);
		return cardInfoMapper.queryCardInfoHistoryList(conditionInput);
	}
	
	@Override
	public List<CardPauseLogDTO> queryCardPauseLogByAuthId(String authId, int rowStart, int pageSize) {
		return cardPauseLogMapper.queryCardPauseLogByAuthId(authId, rowStart, pageSize);
	}

	@Override
	public boolean updateCardInfoHistory(CardInfoHistoryInputDTO cardInfoHistoryDTO) {
		if(cardInfoHistoryDTO != null){
			try {
				CardInfoHistroy cardInfoHistroy = new CardInfoHistroy();
				BeanUtils.copyProperties(cardInfoHistoryDTO,cardInfoHistroy);
				Integer result = cardInfoHistroyMapper.updateCardInfoHistroy(cardInfoHistroy);
				return result > 0;
			}catch (Exception e){
				logger.error(e.getMessage(),e);
			}
		}
		return false;
	}

	@Override
	public boolean insertCardInfoHistory(CardInfoHistoryInputDTO cardInfoHistoryDTO) {
		if(cardInfoHistoryDTO != null){
			try {
				CardInfoHistroy cardInfoHistroy = new CardInfoHistroy();
				BeanUtils.copyProperties(cardInfoHistoryDTO,cardInfoHistroy);
				Integer result = cardInfoHistroyMapper.addCardInfoHistroy(cardInfoHistroy);
				return result > 0;
			}catch (Exception e){
				logger.error(e.getMessage(),e);
			}
		}
		return false;
	}

	@Override
	public List<CardInfoDTO> queryCardInfoByCondition(QueryCardInfoConditionInput queryCardInfoConditionInput) {
		List<CardInfo> cardInfoList = cardInfoMapper.queryCardInfoByCondition(queryCardInfoConditionInput);
		if(CollectionUtils.isNotEmpty(cardInfoList)){
			List<CardInfoDTO> cardInfoDTOList = new ArrayList<>(cardInfoList.size());
			for(CardInfo cardInfo : cardInfoList){
				CardInfoDTO cardInfoDTO = new CardInfoDTO();
				BeanUtils.copyProperties(cardInfo,cardInfoDTO);
				cardInfoDTOList.add(cardInfoDTO);
			}
			return cardInfoDTOList;
		}
		return null;
	}

	@Override
	public boolean updateCardInfo(CardInfoDTO cardInfoDTO) {
		if(cardInfoDTO != null){
			try {
				CardInfo cardInfo = new CardInfo();
				BeanUtils.copyProperties(cardInfoDTO,cardInfo);
				int i = cardInfoMapper.updateByPrimaryKeySelective(cardInfo);
				return i > 0;
			}catch (Exception e){
				logger.error(e.getMessage(),e);
			}
		}
		return false;
	}

	@Override
	public boolean insertCardInfo(CardInfoDTO cardInfoDTO) {
		if(cardInfoDTO != null){
			try {
				CardInfo cardInfo = new CardInfo();
				BeanUtils.copyProperties(cardInfoDTO,cardInfo);
				if(cardInfoDTO.getCardType() != null){
					cardInfo.setCardType(Double.valueOf(cardInfoDTO.getCardType()));
				}
				if(cardInfoDTO.getActivateStatus() != null){
					cardInfo.setActivateStatus(Double.valueOf(cardInfoDTO.getActivateStatus()));
				}
				int i = cardInfoMapper.insertSelective(cardInfo);
				return i > 0;
			}catch (Exception e){
				logger.error(e.getMessage(),e);
			}
		}
		return false;
	}

	@Override
	public boolean updateCardPauseLog(CardPauseLogDTO cardPauseLogDTO) {
		if(cardPauseLogDTO != null){
			try {
				CardPauseLog cardPauseLog = new CardPauseLog();
				BeanUtils.copyProperties(cardPauseLogDTO,cardPauseLog);
				int i = cardPauseLogMapper.updateByPrimaryKeySelective(cardPauseLog);
				return i > 0;
			}catch (Exception e){
				logger.error(e.getMessage(),e);

			}
		}
		return false;
	}

	@Override
	public PageBeanDto<CardInfoHistoryDTO> queryCardInfoHistoryListByCondition(QueryCardInfoHistoryListIConditionInput conditionInput) {
		if(conditionInput != null ){
			Page page = conditionInput.getPage();
			if(page == null){
				page = new Page(1,10);
				conditionInput.setPage(page);
			}
			List<CardInfoHistoryDTO> cardInfoHistoryDTOList = cardInfoMapper.queryCardInfoHistoryList(conditionInput);
			Integer total = cardInfoMapper.countCardInfoHistoryList(conditionInput);
			page.setCount(total);
			PageBeanDto<CardInfoHistoryDTO> pageBeanDto = new PageBeanDto<>();
			pageBeanDto.setPage(page);
			pageBeanDto.setList(cardInfoHistoryDTOList);
			return pageBeanDto;
		}
		return null;
	}

}
