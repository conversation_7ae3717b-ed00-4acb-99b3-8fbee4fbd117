package com.extracme.evcard.membership.core.security;

import com.extracme.evcard.membership.core.dto.TokenValidationResult;
import com.extracme.evcard.membership.core.enums.TokenTypeEnum;
import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.crypto.SecretKey;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * JWT Token 工具类
 * 
 * <AUTHOR>
 * @since 2025-08-20
 */
@Slf4j
@Component
public class JwtTokenUtil {
    
    /**
     * JWT密钥（从配置文件读取，生产环境应使用更安全的密钥管理）
     */
    @Value("${jwt.secret:evcard-membership-jwt-secret-key-2025-very-long-and-secure}")
    private String jwtSecret;
    
    /**
     * 访问令牌有效期（秒）
     */
    @Value("${jwt.access-token.expiration:7200}")
    private Long accessTokenExpiration;
    
    /**
     * 刷新令牌有效期（秒）
     */
    @Value("${jwt.refresh-token.expiration:2592000}")
    private Long refreshTokenExpiration;
    
    /**
     * JWT签发者
     */
    @Value("${jwt.issuer:evcard-membership}")
    private String issuer;
    
    private SecretKey secretKey;
    
    @PostConstruct
    public void init() {
        // 确保密钥长度足够（至少256位）
        if (jwtSecret.length() < 32) {
            jwtSecret = jwtSecret + "0123456789abcdef0123456789abcdef";
        }
        this.secretKey = Keys.hmacShaKeyFor(jwtSecret.getBytes());
    }
    
    /**
     * 生成访问令牌
     */
    public String generateAccessToken(Long userId, String mid, String appKey, String deviceId, String deviceType) {
        return generateToken(userId, mid, appKey, deviceId, deviceType, TokenTypeEnum.ACCESS_TOKEN, accessTokenExpiration);
    }
    
    /**
     * 生成刷新令牌
     */
    public String generateRefreshToken(Long userId, String mid, String appKey, String deviceId, String deviceType) {
        return generateToken(userId, mid, appKey, deviceId, deviceType, TokenTypeEnum.REFRESH_TOKEN, refreshTokenExpiration);
    }
    
    /**
     * 生成JWT令牌
     */
    private String generateToken(Long userId, String mid, String appKey, String deviceId, String deviceType, 
                                TokenTypeEnum tokenType, Long expiration) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration * 1000);
        
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        claims.put("mid", mid);
        claims.put("appKey", appKey);
        claims.put("deviceId", deviceId);
        claims.put("deviceType", deviceType);
        claims.put("tokenType", tokenType.getCode());
        
        return Jwts.builder()
                .setClaims(claims)
                .setSubject(mid)
                .setIssuer(issuer)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .setId(UUID.randomUUID().toString()) // JWT ID (jti)
                .signWith(secretKey, SignatureAlgorithm.HS256)
                .compact();
    }
    
    /**
     * 验证JWT令牌
     */
    public TokenValidationResult validateToken(String token) {
        try {
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(secretKey)
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
            
            TokenValidationResult result = TokenValidationResult.success();
            result.setUserId(claims.get("userId", Long.class));
            result.setMid(claims.get("mid", String.class));
            result.setAppKey(claims.get("appKey", String.class));
            result.setDeviceId(claims.get("deviceId", String.class));
            result.setDeviceType(claims.get("deviceType", String.class));
            result.setExpireTime(claims.getExpiration());
            result.setIssuedAt(claims.getIssuedAt());
            
            // 设置自定义声明
            Map<String, Object> customClaims = new HashMap<>();
            customClaims.put("tokenType", claims.get("tokenType"));
            customClaims.put("jti", claims.getId());
            result.setCustomClaims(customClaims);
            
            return result;
            
        } catch (ExpiredJwtException e) {
            log.warn("JWT token已过期: {}", e.getMessage());
            return TokenValidationResult.failure("TOKEN_EXPIRED", "令牌已过期");
        } catch (UnsupportedJwtException e) {
            log.warn("不支持的JWT token: {}", e.getMessage());
            return TokenValidationResult.failure("TOKEN_UNSUPPORTED", "不支持的令牌格式");
        } catch (MalformedJwtException e) {
            log.warn("JWT token格式错误: {}", e.getMessage());
            return TokenValidationResult.failure("TOKEN_MALFORMED", "令牌格式错误");
        } catch (SecurityException e) {
            log.warn("JWT token签名验证失败: {}", e.getMessage());
            return TokenValidationResult.failure("TOKEN_SIGNATURE_INVALID", "令牌签名验证失败");
        } catch (IllegalArgumentException e) {
            log.warn("JWT token参数错误: {}", e.getMessage());
            return TokenValidationResult.failure("TOKEN_INVALID", "令牌参数错误");
        } catch (Exception e) {
            log.error("JWT token验证异常: {}", e.getMessage(), e);
            return TokenValidationResult.failure("TOKEN_VALIDATION_ERROR", "令牌验证异常");
        }
    }
    
    /**
     * 从JWT令牌中获取JTI（JWT ID）
     */
    public String getJtiFromToken(String token) {
        try {
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(secretKey)
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
            return claims.getId();
        } catch (Exception e) {
            log.warn("无法从token中获取JTI: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 从JWT令牌中获取用户ID
     */
    public Long getUserIdFromToken(String token) {
        try {
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(secretKey)
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
            return claims.get("userId", Long.class);
        } catch (Exception e) {
            log.warn("无法从token中获取用户ID: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 检查令牌是否即将过期（在指定分钟内过期）
     */
    public boolean isTokenExpiringSoon(String token, int minutesBeforeExpiry) {
        try {
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(secretKey)
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
            
            Date expiration = claims.getExpiration();
            Date now = new Date();
            long timeUntilExpiry = expiration.getTime() - now.getTime();
            long minutesUntilExpiry = timeUntilExpiry / (1000 * 60);
            
            return minutesUntilExpiry <= minutesBeforeExpiry;
        } catch (Exception e) {
            return true; // 如果无法解析，认为即将过期
        }
    }
}
