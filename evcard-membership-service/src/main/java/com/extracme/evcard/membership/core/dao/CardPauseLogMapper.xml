<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.extracme.evcard.membership.core.dao.CardPauseLogMapper">
	<resultMap id="BaseResultMap"
		type="com.extracme.evcard.membership.core.model.CardPauseLog">
		<id column="CARD_RECOVER_SEQ" jdbcType="BIGINT" property="cardRecoverSeq" />
		<result column="CARD_NO" jdbcType="VARCHAR" property="cardNo" />
		<result column="AUTH_ID" jdbcType="VARCHAR" property="authId" />
		<result column="NAME" jdbcType="VARCHAR" property="name" />
		<result column="MOBILE_PHONE" jdbcType="VARCHAR" property="mobilePhone" />
		<result column="PAUSE_REASON" jdbcType="VARCHAR" property="pauseReason" />
		<result column="PAUSE_STATUS" jdbcType="INTEGER" property="pauseStatus" />
		<result column="RECOVER_TIME" jdbcType="BIGINT" property="recoverTime" />
		<result column="CREATED_USER" jdbcType="VARCHAR" property="createdUser" />
		<result column="CREATED_TIME" jdbcType="VARCHAR" property="createdTime" />
		<result column="UPDATED_USER" jdbcType="VARCHAR" property="updatedUser" />
		<result column="UPDATED_TIME" jdbcType="VARCHAR" property="updatedTime" />
	</resultMap>
	<sql id="Base_Column_List">
		CARD_RECOVER_SEQ, CARD_NO, AUTH_ID, NAME, MOBILE_PHONE, PAUSE_REASON, PAUSE_STATUS,
		RECOVER_TIME, CREATED_USER, CREATED_TIME, UPDATED_USER, UPDATED_TIME
	</sql>
	<select id="selectByPrimaryKey" parameterType="java.lang.Long"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from ${siacSchema}.card_pause_log
		where CARD_RECOVER_SEQ = #{cardRecoverSeq,jdbcType=BIGINT}
	</select>
	<delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
		delete from ${siacSchema}.card_pause_log
		where CARD_RECOVER_SEQ = #{cardRecoverSeq,jdbcType=BIGINT}
	</delete>
	<insert id="insert" parameterType="com.extracme.evcard.membership.core.model.CardPauseLog">
		insert into ${siacSchema}.card_pause_log (CARD_RECOVER_SEQ, CARD_NO, AUTH_ID,
		NAME, MOBILE_PHONE, PAUSE_REASON,
		PAUSE_STATUS, RECOVER_TIME, CREATED_USER,
		CREATED_TIME, UPDATED_USER, UPDATED_TIME
		)
		values (#{cardRecoverSeq,jdbcType=BIGINT}, #{cardNo,jdbcType=VARCHAR},
		#{authId,jdbcType=VARCHAR},
		#{name,jdbcType=VARCHAR}, #{mobilePhone,jdbcType=VARCHAR}, #{pauseReason,jdbcType=VARCHAR},
		#{pauseStatus,jdbcType=INTEGER}, #{recoverTime,jdbcType=BIGINT},
		#{createdUser,jdbcType=VARCHAR},
		#{createdTime,jdbcType=VARCHAR}, #{updatedUser,jdbcType=VARCHAR}, #{updatedTime,jdbcType=VARCHAR}
		)
	</insert>
	<insert id="insertSelective" parameterType="com.extracme.evcard.membership.core.model.CardPauseLog">
		insert into ${siacSchema}.card_pause_log
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="cardRecoverSeq != null">
				CARD_RECOVER_SEQ,
			</if>
			<if test="cardNo != null">
				CARD_NO,
			</if>
			<if test="authId != null">
				AUTH_ID,
			</if>
			<if test="name != null">
				NAME,
			</if>
			<if test="mobilePhone != null">
				MOBILE_PHONE,
			</if>
			<if test="pauseReason != null">
				PAUSE_REASON,
			</if>
			<if test="pauseStatus != null">
				PAUSE_STATUS,
			</if>
			<if test="recoverTime != null">
				RECOVER_TIME,
			</if>
			<if test="createdUser != null">
				CREATED_USER,
			</if>
			<if test="createdTime != null">
				CREATED_TIME,
			</if>
			<if test="updatedUser != null">
				UPDATED_USER,
			</if>
			<if test="updatedTime != null">
				UPDATED_TIME,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="cardRecoverSeq != null">
				#{cardRecoverSeq,jdbcType=BIGINT},
			</if>
			<if test="cardNo != null">
				#{cardNo,jdbcType=VARCHAR},
			</if>
			<if test="authId != null">
				#{authId,jdbcType=VARCHAR},
			</if>
			<if test="name != null">
				#{name,jdbcType=VARCHAR},
			</if>
			<if test="mobilePhone != null">
				#{mobilePhone,jdbcType=VARCHAR},
			</if>
			<if test="pauseReason != null">
				#{pauseReason,jdbcType=VARCHAR},
			</if>
			<if test="pauseStatus != null">
				#{pauseStatus,jdbcType=INTEGER},
			</if>
			<if test="recoverTime != null">
				#{recoverTime,jdbcType=BIGINT},
			</if>
			<if test="createdUser != null">
				#{createdUser,jdbcType=VARCHAR},
			</if>
			<if test="createdTime != null">
				#{createdTime,jdbcType=VARCHAR},
			</if>
			<if test="updatedUser != null">
				#{updatedUser,jdbcType=VARCHAR},
			</if>
			<if test="updatedTime != null">
				#{updatedTime,jdbcType=VARCHAR},
			</if>
		</trim>
	</insert>
	<update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.core.model.CardPauseLog">
		update ${siacSchema}.card_pause_log
		<set>
			<if test="cardNo != null">
				CARD_NO = #{cardNo,jdbcType=VARCHAR},
			</if>
			<if test="authId != null">
				AUTH_ID = #{authId,jdbcType=VARCHAR},
			</if>
			<if test="name != null">
				NAME = #{name,jdbcType=VARCHAR},
			</if>
			<if test="mobilePhone != null">
				MOBILE_PHONE = #{mobilePhone,jdbcType=VARCHAR},
			</if>
			<if test="pauseReason != null">
				PAUSE_REASON = #{pauseReason,jdbcType=VARCHAR},
			</if>
			<if test="pauseStatus != null">
				PAUSE_STATUS = #{pauseStatus,jdbcType=INTEGER},
			</if>
			<if test="recoverTime != null">
				RECOVER_TIME = #{recoverTime,jdbcType=BIGINT},
			</if>
			<if test="createdUser != null">
				CREATED_USER = #{createdUser,jdbcType=VARCHAR},
			</if>
			<if test="createdTime != null">
				CREATED_TIME = #{createdTime,jdbcType=VARCHAR},
			</if>
			<if test="updatedUser != null">
				UPDATED_USER = #{updatedUser,jdbcType=VARCHAR},
			</if>
			<if test="updatedTime != null">
				UPDATED_TIME = #{updatedTime,jdbcType=VARCHAR},
			</if>
		</set>
		where CARD_RECOVER_SEQ = #{cardRecoverSeq,jdbcType=BIGINT}
	</update>
	<update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.core.model.CardPauseLog">
		update ${siacSchema}.card_pause_log
		set CARD_NO = #{cardNo,jdbcType=VARCHAR},
		AUTH_ID = #{authId,jdbcType=VARCHAR},
		NAME = #{name,jdbcType=VARCHAR},
		MOBILE_PHONE = #{mobilePhone,jdbcType=VARCHAR},
		PAUSE_REASON = #{pauseReason,jdbcType=VARCHAR},
		PAUSE_STATUS = #{pauseStatus,jdbcType=INTEGER},
		RECOVER_TIME = #{recoverTime,jdbcType=BIGINT},
		CREATED_USER = #{createdUser,jdbcType=VARCHAR},
		CREATED_TIME = #{createdTime,jdbcType=VARCHAR},
		UPDATED_USER = #{updatedUser,jdbcType=VARCHAR},
		UPDATED_TIME = #{updatedTime,jdbcType=VARCHAR}
		where CARD_RECOVER_SEQ = #{cardRecoverSeq,jdbcType=BIGINT}
	</update>
	<select id="selectPauseLogByCardNo" resultMap="BaseResultMap"
		parameterType="java.lang.String">
		SELECT
		PAUSE_REASON, PAUSE_STATUS, RECOVER_TIME,
		CREATED_USER, CREATED_TIME
		FROM ${siacSchema}.CARD_PAUSE_LOG
		WHERE CARD_NO = #{cardNo,jdbcType=VARCHAR}
		ORDER BY CREATED_TIME DESC
		LIMIT 1
	</select>
	<update id="updatePauseStatusByCardNo" parameterType="com.extracme.evcard.membership.core.model.CardPauseLog">
		update ${siacSchema}.card_pause_log
		<set>
			PAUSE_STATUS = 1,
			<if test="updatedUser != null">
				UPDATED_USER = #{updatedUser,jdbcType=VARCHAR},
			</if>
			<if test="updatedTime != null">
				UPDATED_TIME = #{updatedTime,jdbcType=VARCHAR},
			</if>
		</set>
		where CARD_NO = #{cardNo,jdbcType=VARCHAR}
		ORDER BY CREATED_TIME DESC LIMIT 1
	</update>

	<update id="updateAllPauseStatusByCardNo" parameterType="com.extracme.evcard.membership.core.model.CardPauseLog">
        UPDATE ${siacSchema}.card_pause_log
            SET
            PAUSE_STATUS = 1,
            UPDATED_USER = #{updatedUser,jdbcType=VARCHAR},
            UPDATED_TIME = #{updatedTime,jdbcType=VARCHAR}
        WHERE
            CARD_NO = #{cardNo,jdbcType=VARCHAR} AND PAUSE_STATUS != 1
    </update>
    
    <select id="queryPauseLogByCardNo" resultType="com.extracme.evcard.membership.core.dto.CardPauseQueryDto" parameterType="java.lang.String">
	  SELECT
		PAUSE_REASON as pauseReason,
		PAUSE_STATUS as pauseStatus, 
		RECOVER_TIME as recoverTime,
		CREATED_USER as createdUser,
		CREATED_TIME as createdTime
	  FROM ${siacSchema}.CARD_PAUSE_LOG
	  WHERE CARD_NO = #{cardNo,jdbcType=VARCHAR}
	  ORDER BY CREATED_TIME DESC
	  LIMIT 1
	</select>
	
	<select id="queryCardPauseLogByAuthId" resultType="com.extracme.evcard.membership.core.dto.CardPauseLogDTO">
	  SELECT
	    CARD_NO as cardNo,
	    AUTH_ID as authId,
	    NAME as name,
	    MOBILE_PHONE as mobilePhone,
		PAUSE_REASON as pauseReason,
		PAUSE_STATUS as pauseStatus, 
		RECOVER_TIME as recoverTime,
		CREATED_USER as createdUser,
		CREATED_TIME as createdTime,
		UPDATED_USER as updatedUser,
		UPDATED_TIME as updatedTime
	  FROM ${siacSchema}.CARD_PAUSE_LOG
	  WHERE AUTH_ID = #{authId,jdbcType=VARCHAR}
	  ORDER BY CREATED_TIME DESC
	  limit #{rowStart},#{pageSize}
	</select>
</mapper>