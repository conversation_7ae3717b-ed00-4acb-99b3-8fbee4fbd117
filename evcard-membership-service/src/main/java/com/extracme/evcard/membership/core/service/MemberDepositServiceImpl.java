package com.extracme.evcard.membership.core.service;

import static com.extracme.evcard.membership.common.DecimalCalculateUtil.add;
import static com.extracme.evcard.membership.common.DecimalCalculateUtil.sub;

import java.math.BigDecimal;
import java.util.*;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.dao.MmpUserAuthorizedLogMapper;
import com.extracme.evcard.membership.core.dao.OrgCityRelationMapper;
import com.extracme.evcard.membership.core.dto.*;
import com.extracme.evcard.membership.core.dto.input.*;
import com.extracme.evcard.membership.core.input.CheckDepositOrderInput;
import com.extracme.evcard.membership.core.model.MmpUserAuthorizedLog;
import com.extracme.evcard.membership.core.model.OrgCityRelation;
import com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs;
import com.extracme.evcard.membership.credit.model.MmpUserTag;
import com.extracme.evcard.rpc.dto.Page;
import com.extracme.evcard.rpc.dto.PageBeanDto;
import com.extracme.evcard.rpc.pay.dto.AppChargeHistoryDTO;
import com.extracme.evcard.rpc.pay.dto.ReturnRegulatoryAccount;
import com.extracme.evcard.rpc.pay.exception.DepositSuperviseException;
import com.extracme.evcard.rpc.pay.service.ISuperviseService;
import com.extracme.evcard.rpc.util.BeanCopyUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.rpc.protocol.rest.support.ContentType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.extracme.evcard.bvm.service.IAgencyPriceService;
import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.dao.AgencyInfoMapper;
import com.extracme.evcard.membership.core.dao.MmpUserZhimaLevelQueryLogMapper;
import com.extracme.evcard.membership.core.enums.OrderVehicleStatusEnum;
import com.extracme.evcard.membership.core.exception.ExceptionEnum;
import com.extracme.evcard.membership.core.exception.MemberException;
import com.extracme.evcard.membership.core.input.CheckDepositInput;
import com.extracme.evcard.membership.core.model.MmpUserZhimaLevelQueryLog;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.dao.MmpUserTagMapper;
import com.extracme.evcard.rpc.pay.bo.AntLimitLevelBO;
import com.extracme.evcard.rpc.pay.bo.MemberApplyingDepositResultBo;
import com.extracme.evcard.rpc.pay.bo.MemberDepositResultBO;
import com.extracme.evcard.rpc.pay.service.IAliService;
import com.extracme.evcard.rpc.pay.service.IMemAccountService;
import com.extracme.evcard.rpc.shop.dto.ShopInfoDto;
import com.extracme.evcard.rpc.shop.service.IShopService;
import com.extracme.evcard.rpc.vehicle.dto.VehicleModelGradeInfoDto;
import com.extracme.evcard.rpc.vehicle.service.IVehicleModelService;

/**
 * <p>
 *  押金服务实现
 * </p>
 *
 * <AUTHOR>
 * @since 2018/7/13
 */
@Service("memberDepositService")
public class MemberDepositServiceImpl implements IMemberDepositService{

    Logger logger = (Logger) LoggerFactory.getLogger(MemberDepositServiceImpl.class);

    @Autowired
    private MembershipInfoMapper membershipInfoMapper;

    @Autowired
    private IVehicleModelService vehicleModelService;

    @Resource
    private IMemAccountService memAccountService;

    @Resource
    private IAliService aliService;

    @Autowired
    private MmpUserTagMapper mmpUserTagMapper;
    @Autowired
    private IShopService shopService;
    @Autowired
    private AgencyInfoMapper agencyInfoMapper;
    @Autowired
    private OrgCityRelationMapper orgCityRelationMapper;

    @Autowired
    private MmpUserZhimaLevelQueryLogMapper mmpUserZhimaLevelQueryLogMapper;
    @Resource(name = "agencyPriceService")
    private IAgencyPriceService agencyPriceService;

    @Autowired
    private MmpUserAuthorizedLogMapper mmpUserAuthorizedLogMapper;

//    @Autowired
//    private ISuperviseService superviseService;

    @Override
    public DepositInfoDto queryDepositInfo(QueryDepositInfoInputDTO inputDTO) {
        String authId = inputDTO.getAuthId();
        if(StringUtils.isEmpty(authId)){
            throw new MemberException(ExceptionEnum.PARAM_ERROR.getErrCode(),ExceptionEnum.PARAM_ERROR.getErrMsg());
        }
        DepositInfoDto depositInfoDto = new DepositInfoDto();
        //查询机构免押信息和会员免押信息
        MemberExemptDepositDto exemptDepositDto = membershipInfoMapper.queryExemptDeposit(authId);
        if(exemptDepositDto == null){
            throw new MemberException(ExceptionEnum.PARAM_ERROR.getErrCode(),ExceptionEnum.PARAM_ERROR.getErrMsg());
        }
        int freeDepositLevel = 0;
        //修改调用企业服务查询会员免押等级
        try {
            Integer exemptDepositInfo = agencyPriceService.getExemptDepositInfo(authId);
            logger.debug("查询会员押金等级服务|返回数据：" + exemptDepositInfo);
            if(exemptDepositInfo !=null ){
                freeDepositLevel = exemptDepositInfo;
            }
        }catch (Exception e){
            logger.error("查询会员押金等级服务调用失败|" + e.getMessage(),e);
        }
        BigDecimal baseDeposit = new BigDecimal(0);
        BigDecimal vehicleDeposit = new BigDecimal(0);
        BigDecimal drivingDeposit = new BigDecimal(0);
        BigDecimal freeDrivingDeposit = BigDecimal.ZERO;
        BigDecimal cashDeposit = BigDecimal.ZERO;
        if (freeDepositLevel > 0) {
            BigDecimal tempVehicleDepositAmount = vehicleModelService.queryVehicleModelDepositByLevel(freeDepositLevel);
            logger.debug("vehicleModelService.queryVehicleModelDepositByLevel : " + tempVehicleDepositAmount);
            baseDeposit = BussinessConstants.BASE_DEPOSIT_VALUE;
            vehicleDeposit = tempVehicleDepositAmount;
            drivingDeposit = baseDeposit.add(vehicleDeposit);
            freeDrivingDeposit = drivingDeposit;
            depositInfoDto.setFreeDepositLevel(freeDepositLevel);
            depositInfoDto.setFreeBaseDeposit(baseDeposit);
            depositInfoDto.setFreeVehicleDeposit(vehicleDeposit);
            depositInfoDto.setBaseDeposit(baseDeposit);
            depositInfoDto.setVehicleDeposit(vehicleDeposit);
            depositInfoDto.setDrivingDeposit(drivingDeposit);
        }
        //芝麻信用免押等级查询
        Integer freeZhiMaFlag = 0;
        MmpUserTag mmpUserTag = mmpUserTagMapper.selectMmpUserByAuthId(authId);
        if (mmpUserTag != null && StringUtils.isNoneBlank(mmpUserTag.getSpare4())) {
            freeZhiMaFlag = 1;
        }
        //判断城市是否支持芝麻免押
        Integer citySupportZhiMaFree = 0;
        if (StringUtils.isNoneBlank(inputDTO.getCity())) {
            Integer flag = this.getZhiMaCreditFlagByCityName(inputDTO.getCity());
            if (flag > 0) {
                citySupportZhiMaFree = 1;
            }
        }
        if (freeZhiMaFlag == 1 && citySupportZhiMaFree == 1) {
            drivingDeposit = drivingDeposit.add(BussinessConstants.ZHI_MA_FREE_DEPOSIT_VALUE);
            freeDrivingDeposit  = freeDrivingDeposit.add(BussinessConstants.ZHI_MA_FREE_DEPOSIT_VALUE);
        }
        depositInfoDto.setCitySupportZhiMaFree(citySupportZhiMaFree);
        depositInfoDto.setFreeZhiMaFlag(freeZhiMaFlag);
        //查询基础押金预授权
        List<PreAuthRecordDto> preList = new ArrayList<>();
        List<com.extracme.evcard.rpc.pay.dto.PreAuthorizationRecordDto> preAuthList = memAccountService.queryEffectivePreAuthorizationList(authId);
        if (CollectionUtils.isNotEmpty(preAuthList)) {
            BigDecimal tempVehDeposit = BigDecimal.ZERO;
            for (com.extracme.evcard.rpc.pay.dto.PreAuthorizationRecordDto p : preAuthList) {
                if (p.getType() == 1) {
                    baseDeposit = p.getAmount().add(baseDeposit);
                    drivingDeposit =  p.getAmount().add(drivingDeposit);
                    depositInfoDto.setBaseDeposit(p.getAmount());
                    depositInfoDto.setBaseDepositType(2);
                } else if (p.getType() == 2) {
                    tempVehDeposit = p.getAmount();
                }
                PreAuthRecordDto dto = new PreAuthRecordDto();
                dto.setAmount(p.getAmount());
                dto.setTermOfValidity(p.getTermOfValidity());
                preList.add(dto);
            }
            if (tempVehDeposit.compareTo(BigDecimal.ZERO) > 0) {
                vehicleDeposit = tempVehDeposit.add(vehicleDeposit);
                drivingDeposit =  tempVehDeposit.add(drivingDeposit);
                depositInfoDto.setVehicleDeposit(tempVehDeposit);
                depositInfoDto.setVehicleDepositType(2);
            }
        }
        //到期未解冻的车辆预授权金额
        depositInfoDto.setPreAuthorizationRecordList(preList);
        //4.查询充值记录
        com.extracme.evcard.rpc.pay.dto.RefundDepositInfoDto rechargeRecordDto = memAccountService.queryRefundableDepositInfo(authId);
        if (rechargeRecordDto.getBaseDeposit().compareTo(BigDecimal.ZERO) != 0) {
            baseDeposit = rechargeRecordDto.getBaseDeposit().add(baseDeposit);
            drivingDeposit = rechargeRecordDto.getBaseDeposit().add(drivingDeposit);
            cashDeposit = rechargeRecordDto.getBaseDeposit().add(cashDeposit);
            depositInfoDto.setBaseDeposit(rechargeRecordDto.getBaseDeposit());
            depositInfoDto.setBaseDepositType(1);
        }
        if (rechargeRecordDto.getVehicleDeposit().compareTo(BigDecimal.ZERO) != 0) {
            vehicleDeposit = rechargeRecordDto.getVehicleDeposit().add(vehicleDeposit);
            drivingDeposit = rechargeRecordDto.getVehicleDeposit().add(drivingDeposit);
            cashDeposit = rechargeRecordDto.getVehicleDeposit().add(cashDeposit);
            depositInfoDto.setVehicleDeposit(rechargeRecordDto.getVehicleDeposit());
            depositInfoDto.setVehicleDepositType(1);
        }
        depositInfoDto.setFreeDrivingDeposit(freeDrivingDeposit);
        depositInfoDto.setDrivingDeposit(drivingDeposit);
        depositInfoDto.setCashDeposit(cashDeposit);
        //个人银行监管
//        try {
            /**
             * frozenStatue
             * 1长期未发生资金变动,已被冻结,去解冻
             * 2账户证件已过期,已被冻结,去解冻
             * 3新证件信息错误,解冻失败,去查看
             * 4由于其他原因已被冻结,去联系客服
             * 5证件即将到期，去更新
             */
//            Integer frozenStatue = null;
//            ReturnRegulatoryAccount returnRegulatoryAccount = superviseService.queryAccount(authId);
//            logger.info(authId + "查询个人监管信息，返回：" + JSON.toJSONString(returnRegulatoryAccount));
//            if (returnRegulatoryAccount != null) {
//                //个人监管账户状态   0:休眠  1:正常  2、冻结
//                Integer accountStatus = returnRegulatoryAccount.getAccountStatus();
//                //身份证件状态 0:未上传 1:已上传 2:证件错误 3:正常 4:即将过期 5:过期 6:新证件审核中 7:新证件审核失败
//                Integer idStatus = returnRegulatoryAccount.getIdStatus();
//                if (accountStatus != 1) {
//                    if (accountStatus == 0) {
//                        frozenStatue = 1;
//                    } else {
//                        if (idStatus == 5) {
//                            frozenStatue = 2;
//                        } else if (idStatus == 7) {
//                            frozenStatue = 3;
//                        } else {
//                            frozenStatue = 4;
//                            depositInfoDto.setFrozenDesc(returnRegulatoryAccount.getAccountExceptionReason());
//                        }
//                    }
//                    if (Arrays.asList(1,2,3,4).contains(frozenStatue)) {
//                        //冻结押金
//                        depositInfoDto.setFrozenDeposit(returnRegulatoryAccount.getBalance());
//                        //drivingDeposit = drivingDeposit.subtract(returnRegulatoryAccount.getBalance());
//                        //depositInfoDto.setDrivingDeposit(drivingDeposit);
//                        //cashDeposit = cashDeposit.subtract(returnRegulatoryAccount.getBalance());
//                        //depositInfoDto.setCashDeposit(cashDeposit);
//                    }
//                } else {
//                    if (idStatus == 7) {
//                        frozenStatue = 3;
//                    } else if (idStatus == 4) {
//                        frozenStatue = 5;
//                    }
//                }
//                depositInfoDto.setFrozenStatue(frozenStatue);
//            }
//        } catch (DepositSuperviseException e) {
//            logger.error("", e);
//        }

        //计算押金等级和押金列表
        QueryUpgradeDepositInputDTO upgradeDepositInputDTO = new QueryUpgradeDepositInputDTO();
        upgradeDepositInputDTO.setDrivingDepositAmount(depositInfoDto.getDrivingDeposit());
        UpgradeDepositDto upgradeDepositDto = this.queryUpgradeDepositList(upgradeDepositInputDTO);
        depositInfoDto.setDepositLevel(upgradeDepositDto.getDepositLevel());
        depositInfoDto.setDepositLevelDesc(upgradeDepositDto.getDepositLevelDesc());
        depositInfoDto.setUpgradeDepositList(upgradeDepositDto.getUpgradeDepositList());
        depositInfoDto.setStandardBaseDeposit(BussinessConstants.BASE_DEPOSIT_VALUE);
        depositInfoDto.setTotalDepositAmount(drivingDeposit);

        // app5.8查询冻结金额
        MembershipInfoWithBLOBs membership = membershipInfoMapper.selectByAuthId(authId,0);
        if (membership != null) {
            BigDecimal freezeDeposit = BigDecimal.ZERO;
            if (membership.getFreezeDeposit()!= null) {
                freezeDeposit = membership.getFreezeDeposit();
            }
            BigDecimal deposit =  BigDecimal.ZERO;
            if (rechargeRecordDto != null) {
                deposit = rechargeRecordDto.getBaseDeposit();
            }/*else{
                deposit = membership.getDeposit();
            }*/
            depositInfoDto.setFrozenDeposit(freezeDeposit);
            depositInfoDto.setNewTotalDeposit(freezeDeposit.add(deposit));
        }
        return depositInfoDto;

    }

    public static BigDecimal BASE_DEPOSIT = new BigDecimal(1000);
    @Override
    public List<DepositDetailDto> queryDepositDetailList(String authId) {

        List<DepositDetailDto> depositDetailDtos = new ArrayList<>();
        //查询机构免押信息和会员免押信息
        MemberExemptDepositDto exemptDepositDto = membershipInfoMapper.queryExemptDeposit(authId);
        if(exemptDepositDto == null){
            throw new MemberException(ExceptionEnum.PARAM_ERROR.getErrCode(),ExceptionEnum.PARAM_ERROR.getErrMsg());
        }
        //修改调用企业服务查询会员免押等级
        try {
            Integer exemptDepositInfo = agencyPriceService.getExemptDepositInfo(authId);
            logger.debug("查询会员押金等级服务|返回数据：" + exemptDepositInfo);
            if(exemptDepositInfo !=null && !exemptDepositInfo.equals(0)){
                DepositDetailDto depositDetailDto = new DepositDetailDto(BASE_DEPOSIT.multiply(new BigDecimal(exemptDepositInfo)),null,null,1);
                depositDetailDtos.add(depositDetailDto);
            }
        }catch (Exception e){
            logger.error("查询会员押金等级服务调用失败|" + e.getMessage(),e);
        }

        //预授权
        List<com.extracme.evcard.rpc.pay.dto.PreAuthorizationRecordDto> preAuthList = memAccountService.queryEffectivePreAuthorizationList(authId);
        if (CollectionUtils.isNotEmpty(preAuthList)) {
            preAuthList.stream().forEach(p->{
                if (StringUtils.isNotBlank(p.getGmtPayment())){
                    p.setGmtPayment(ComUtil.getFormatDate(p.getGmtPayment(),ComUtil.DATE_TYPE1,ComUtil.DATE_TYPE4));
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(ComUtil.getDateFromStr(p.getGmtPayment(),ComUtil.DATE_TYPE4));
                    calendar.add(Calendar.DATE,14);
                    p.setTermOfValidity(ComUtil.getFormatDate(calendar.getTime(),ComUtil.DATE_TYPE4));
                }
                DepositDetailDto depositDetailDto = new DepositDetailDto(p.getAmount(),p.getGmtPayment(),p.getTermOfValidity(),2);
                depositDetailDtos.add(depositDetailDto);
            });
        }

        //查询押金监管信息
        //个人银行监管
        Integer accountType = 1;
        Integer accountStatus = null;
//        try {
//            ReturnRegulatoryAccount returnRegulatoryAccount = superviseService.queryAccount(authId);
//            if (returnRegulatoryAccount != null) {
//                accountType = 2;
//                //个人监管账户状态   0:休眠  1:正常  2、冻结
//                accountStatus = returnRegulatoryAccount.getAccountStatus();
//            }
//        } catch (DepositSuperviseException e) {
//            logger.error("", e);
//        }

        //3充值押金
        List<AppChargeHistoryDTO> resultList  = memAccountService.queryChargeHistory(authId,"");
        if(CollectionUtils.isNotEmpty(resultList)){
            for (AppChargeHistoryDTO p : resultList) {
                if ((p.getType().equals(1) || p.getType().equals(3)||p.getType().equals(8)) && p.getReturnStatus().equals(0)){
                    //冲押金
                    DepositDetailDto depositDetailDto = new DepositDetailDto(new BigDecimal(p.getAmount()),p.getTime(),null,3);
                    depositDetailDto.setAccountType(0);//TODO:2020.12.25 4.3.0上线押金 p.getDepositMode());
                    if (p.getDepositMode() == 2) {
                        depositDetailDto.setAccountStatus(accountStatus);
                    }
                    depositDetailDtos.add(depositDetailDto);
                }else if (Arrays.asList(new Integer[]{5,6,7}).contains(p.getType())){
                    //扣除
                    DepositDetailDto depositDetailDto = new DepositDetailDto(new BigDecimal(p.getAmount()),p.getTime(),p.getBody(),4);
                    depositDetailDtos.add(depositDetailDto);
                }
            }
        }

        //5芝麻信用
        MmpUserTag mmpUserTag = mmpUserTagMapper.selectMmpUserByAuthId(authId);
        if (mmpUserTag != null && StringUtils.isNotBlank(mmpUserTag.getSpare4())){
            //以签约芝麻信用
            DepositDetailDto depositDetailDto = new DepositDetailDto(BussinessConstants.ZHI_MA_FREE_DEPOSIT_VALUE,null,null,5);
            depositDetailDtos.add(depositDetailDto);
        }

        return depositDetailDtos;
    }

    @Override
    public UpgradeDepositDto queryUpgradeDepositList(QueryUpgradeDepositInputDTO inputDTO) {
        BigDecimal drivingDeposit = inputDTO.getDrivingDepositAmount() == null ? new BigDecimal(0):inputDTO.getDrivingDepositAmount();
        //查询车型押金等级信息
        List<VehicleModelGradeInfoDto> vehicleModelGradeInfoDtoList = vehicleModelService.queryVehicleModelGradeInfoList();
        if(CollectionUtils.isEmpty(vehicleModelGradeInfoDtoList)){
            throw new MemberException(ExceptionEnum.DEPOSIT_BASE_INFO_NOT_EXIST.getErrCode(),ExceptionEnum.DEPOSIT_BASE_INFO_NOT_EXIST.getErrMsg());
        }
        UpgradeDepositDto upgradeDepositDto = new UpgradeDepositDto();
        Integer depositLevel = 0;
        String  depositLevelDesc = StringUtils.EMPTY;
        List<ListPageDto>  listPageDtoList = new ArrayList<>();
        //押金升级列表
        Integer vehicleModelSeq = inputDTO.getVehicleModelSeq();
        for (VehicleModelGradeInfoDto vehicleModelGradeInfoDto : vehicleModelGradeInfoDtoList) {
            ListPageDto listPageDto = new ListPageDto();
            listPageDto.setDepositLevel(vehicleModelGradeInfoDto.getVehicleModelType());
            listPageDto.setDepositLevelDesc(vehicleModelGradeInfoDto.getName());
            //不传车型,只需要显示可升级押金列表
            if (vehicleModelSeq == null) {
                listPageDto.setIsEnableSelect(1);
                if (drivingDeposit.compareTo(new BigDecimal(vehicleModelGradeInfoDto.getVehicleDeposit()).add(BussinessConstants.BASE_DEPOSIT_VALUE).setScale(2, BigDecimal.ROUND_HALF_UP)) >= 0) {
                    //赋值当前押金等级和描述
                    depositLevel = vehicleModelGradeInfoDto.getVehicleModelType();
                    depositLevelDesc = vehicleModelGradeInfoDto.getName();
                    //车辆满足时跳出，不添加到押金升级列表
                    continue;
                }
            } else {
                //传入车型，显示全部押金列表，需要设置不可勾选
                if (drivingDeposit.compareTo(new BigDecimal(vehicleModelGradeInfoDto.getVehicleDeposit()).add(BussinessConstants.BASE_DEPOSIT_VALUE).setScale(2, BigDecimal.ROUND_HALF_UP)) >= 0) {
                    //赋值当前押金等级和描述
                    depositLevel = vehicleModelGradeInfoDto.getVehicleModelType();
                    depositLevelDesc = vehicleModelGradeInfoDto.getName();
                }
                //比较传入的车型所需车辆押金和当前车型列表押金金额来设置是否可以勾选
                BigDecimal needVehicleDeposit = vehicleModelService.queryVehicleModelDepositBySeq(vehicleModelSeq).add(BussinessConstants.BASE_DEPOSIT_VALUE).setScale(2, BigDecimal.ROUND_HALF_UP);
                //所需押金小于等于当前车型列表押金金额
                if (needVehicleDeposit.compareTo(new BigDecimal(vehicleModelGradeInfoDto.getVehicleDeposit()).add(BussinessConstants.BASE_DEPOSIT_VALUE)) <= 0) {
                    //押金全部为0可选或者输入车辆押金小于传入的车型所需车辆押金
                    if ((drivingDeposit.compareTo(BigDecimal.ZERO) == 0)
                            || (drivingDeposit.compareTo(needVehicleDeposit) < 0 )) {
                        listPageDto.setIsEnableSelect(1);
                    } else if (depositLevel == 0) {
                        //押金等级为0可选(除去押金为0)
                        listPageDto.setIsEnableSelect(1);
                    }
                }
            }
            //计算缺的押金金额
            getLackDepositAmount(drivingDeposit,new BigDecimal(vehicleModelGradeInfoDto.getVehicleDeposit()).add(BussinessConstants.BASE_DEPOSIT_VALUE).setScale(2, BigDecimal.ROUND_HALF_UP) ,listPageDto);
            listPageDto.setStandardVehicleDeposit(BigDecimal.valueOf(vehicleModelGradeInfoDto.getVehicleDeposit()));
            if (vehicleModelSeq != null) {
                if (listPageDto.getIsEnableSelect() == 1) {
                    listPageDtoList.add(listPageDto);
                }
            } else {
                listPageDtoList.add(listPageDto);
            }
        }
        //升级列表排序
        Collections.sort(listPageDtoList);
        upgradeDepositDto.setDepositLevel(depositLevel);
        upgradeDepositDto.setDepositLevelDesc(depositLevelDesc);
        upgradeDepositDto.setUpgradeDepositList(listPageDtoList);
        return upgradeDepositDto;
    }

    @Override
    public CheckOrderDepositResult checkDepositOrder(CheckDepositInput checkDepositInput) {
        String orgCode = StringUtils.EMPTY;
        ShopInfoDto shopInfoDto = shopService.getShopInfoById(checkDepositInput.getShopSeq());
        if (shopInfoDto != null){
            orgCode = shopInfoDto.getOrgId();
        }
        CheckDepositOrderInput checkDepositOrderInput = new CheckDepositOrderInput();
        BeanCopyUtils.copyProperties(checkDepositInput, checkDepositOrderInput);
        checkDepositOrderInput.setOrgCode(orgCode);
        CheckOrderDepositResp resp = checkDepositOrderCommon(checkDepositOrderInput);

        CheckOrderDepositResult result = new CheckOrderDepositResult();
        BeanCopyUtils.copyProperties(resp, result);
        result.setShopSeq(checkDepositInput.getShopSeq());
        return result;
    }

    @Override
    public CheckOrderDepositResp checkDepositOrderCommon(CheckDepositOrderInput checkDepositDto) {
        //1:押金  2:免押  3:预授权  4:芝麻信用
        CheckOrderDepositResp result = new CheckOrderDepositResp();
        result.setVehicleModelType(checkDepositDto.getVehicleModelType());
        result.setVehicleModelInfo(checkDepositDto.getVehicleModelInfo());
        result.setOrgCode(checkDepositDto.getOrgCode());
        result.setAuthId(checkDepositDto.getAuthId());
        result.setNeedDeposit(new BigDecimal(BussinessConstants.BASE_DEPOSIT));
        float modelVehicleDeposit = checkDepositDto.getVehicleModelType()* BussinessConstants.BASE_DEPOSIT - BussinessConstants.BASE_DEPOSIT;
        result.setNeedVehicleDeposit(BigDecimal.valueOf(modelVehicleDeposit));
        int vehicleModelType = checkDepositDto.getVehicleModelType();
        //1 .判断押金
        //会员基础押金(临时)、车辆押金(临时)
        float baseDeposit = 0f;//有企业或者会员免押等级,基础押金1000，否则为0；
        float vehicleDeposit = 0f;//免押车辆金额
        // 押金等级 车辆押金
        List<VehicleModelGradeInfoDto> vehicleModelGradeInfoDtoList = vehicleModelService.queryVehicleModelGradeInfoList();
        // 查询机构免押信息和会员免押信息
        MemberExemptDepositDto exemptDepositDto = membershipInfoMapper.queryExemptDeposit(checkDepositDto.getAuthId());
        if(exemptDepositDto == null){
            throw new MemberException(ExceptionEnum.PARAM_ERROR.getErrCode(),ExceptionEnum.PARAM_ERROR.getErrMsg());
        }
        BigDecimal freeDeposit = BigDecimal.ZERO;
        int freeDepositLevel = 0;
        //修改调用企业服务查询会员免押等级
        try {
            Integer exemptDepositInfo = agencyPriceService.getExemptDepositInfo(checkDepositDto.getAuthId());
            logger.warn("查询会员押金等级服务|返回数据：" + exemptDepositInfo);
            if(exemptDepositInfo !=null ){
                freeDepositLevel = exemptDepositInfo;
            }
        }catch (Exception e){
            logger.error("查询会员押金等级服务调用失败|" + e.getMessage(),e);
        }
        if (freeDepositLevel > 0) {
            freeDeposit = new BigDecimal(1000).multiply(new BigDecimal(freeDepositLevel));
            result.setDepositStatus(1);
            baseDeposit = BussinessConstants.BASE_DEPOSIT;
            for (VehicleModelGradeInfoDto dto :vehicleModelGradeInfoDtoList) {
                if (dto.getVehicleModelType().equals(freeDepositLevel)) {
                    vehicleDeposit = dto.getVehicleDeposit();
                }
            }
        }else {
            result.setDepositStatus(0);
        }
//        //e享天开基础押金只需要500
//        int dataOrigin = exemptDepositDto.getDataOrigin();
//        if (dataOrigin == 5 || dataOrigin == 6) {
//            baseDeposit = add(BussinessConstants.REDUCE_DEPOSIT, baseDeposit);
//        }
        //押金充值记录
        List<MemberDepositResultBO> addDeposit = memAccountService.queryMemberDepositList(checkDepositDto.getAuthId(),0,1);
        //基础押金退款标识

//        List<MemberApplyingDepositResultBo> memberApplyingDepositResultBos = memAccountService.queryApplyingDrawbackList(checkDepositDto.getAuthId(),1);
        boolean baseDepositDrawBackFlag = false;
//        if (CollectionUtils.isNotEmpty(memberApplyingDepositResultBos)){
//            baseDepositDrawBackFlag = true;
//        }
        float addBaseDeposit = 0f;
//        if (!baseDepositDrawBackFlag){
            //充值押金金额
            if (CollectionUtils.isNotEmpty(addDeposit)){
                for (MemberDepositResultBO memberDepositResultBO : addDeposit){
                    if (memberDepositResultBO.getDepositType()==1){
                        addBaseDeposit = add(addBaseDeposit,(float) memberDepositResultBO.getTotalFree());
                    }
                }
                result.setDeposit(new BigDecimal(addBaseDeposit));
            }else {
                result.setDeposit(new BigDecimal(0));
            }
//            float memberBaseDeposit = add(baseDeposit, addBaseDeposit);

//        }else {
//            result.setDeposit(new BigDecimal(0));
//        }

        //车辆押金退款
//        List<MemberApplyingDepositResultBo> vehicleMemberApplyingDepositResultBos = memAccountService.queryApplyingDrawbackList(checkDepositDto.getAuthId(),2);
//        boolean vehicleDepositDrawBackFlag = false;
//        if (CollectionUtils.isNotEmpty(vehicleMemberApplyingDepositResultBos)){
//            vehicleDepositDrawBackFlag = true;
//        }
        float addVehicleDeposit = 0f;
//        if (!vehicleDepositDrawBackFlag){
            if (CollectionUtils.isNotEmpty(addDeposit)){
                for (MemberDepositResultBO memberDepositResultBO : addDeposit){
                    if (memberDepositResultBO.getDepositType()==2){
                        addVehicleDeposit = add(addVehicleDeposit,(float) memberDepositResultBO.getTotalFree());
                    }
                }
                result.setVehicleDeposit(new BigDecimal(addVehicleDeposit));
            }else {
                result.setVehicleDeposit(new BigDecimal(0));
            }
//            float memberVehicleDeposit = add(vehicleDeposit, addBaseDeposit);
//        }else {
//            result.setVehicleDeposit(new BigDecimal(0));
//        }

        //预授权金额
        List<MemberDepositResultBO> depositResultBOList = memAccountService.queryMemberDepositList(checkDepositDto.getAuthId(), 0, 2);
        final BigDecimal[] preAuthAmount = {BigDecimal.ZERO};
        if (CollectionUtils.isNotEmpty(depositResultBOList)){
            depositResultBOList.stream().forEach(p->{
                preAuthAmount[0] = preAuthAmount[0].add(new BigDecimal(p.getTotalFree()));
            });
        }

        //判断基础押金(不免基础押金)
        if (baseDeposit < BussinessConstants.BASE_DEPOSIT) {
//            //基础押金预授权
//            float basePreAuthAmount = 0f;
//            if (CollectionUtils.isNotEmpty(depositResultBOList)){
//                basePreAuthAmount = (float) depositResultBOList.get(0).getTotalFree();
//            }
            //预授权无法使用
            if (add(addBaseDeposit, preAuthAmount[0].floatValue()) < BussinessConstants.BASE_DEPOSIT) {
                //基础押金退款无法约车
                if (baseDepositDrawBackFlag) {
                    result.setResult(OrderVehicleStatusEnum.BASE_APPLY_DRAWBACK);
                }else{
                    //基础押金
                    float tempDeposit = add(baseDeposit, addBaseDeposit);
                    if (tempDeposit < BussinessConstants.BASE_DEPOSIT) {
                        result.setResult(OrderVehicleStatusEnum.DEPOSIT_LESS);
                        result.setNeedDepositAmount(new BigDecimal(String.valueOf(sub(BussinessConstants.BASE_DEPOSIT, tempDeposit))));
                    }else{
                        //使用现金押金
                        result.setDepositType(1);
                    }
                }
            }else{
                //使用预授权
                result.setDepositType(3);
            }
        }else{
            //使用免押
            result.setDepositType(2);
        }


        //判断车辆押金
        float needVehicleDeposit = 0f;
        for (VehicleModelGradeInfoDto dto :vehicleModelGradeInfoDtoList) {
            if (dto.getVehicleModelType().equals(vehicleModelType)) {
                needVehicleDeposit = dto.getVehicleDeposit();
            }
        }
//总金额= 车辆押金充值金额（addVehicleDeposit） + 基础押金充值金额（addBaseDeposit） + 预充值金额（preAuthAmount[0]） + 免押金额（freeDeposit，根据用户免押等级或企业免押等级*1000）
        BigDecimal allAmount = new BigDecimal(add(new BigDecimal(add(addVehicleDeposit,addBaseDeposit)).floatValue(), preAuthAmount[0].floatValue())).add(freeDeposit);
        if (allAmount.compareTo(new BigDecimal(needVehicleDeposit).add(new BigDecimal(1000)))>=0 ) {
            //免车辆押金 小于需要的车辆押金
            if (vehicleDeposit <= needVehicleDeposit) {
                //车辆押金预授权
//                List<MemberDepositResultBO> vehicleDepositResultBOList = memAccountService.queryMemberDepositList(checkDepositDto.getAuthId(), 2, 2);
//                float vehiclePreAuthAmount = 0f;
//                if (CollectionUtils.isNotEmpty(vehicleDepositResultBOList)){
//                    vehiclePreAuthAmount = (float) vehicleDepositResultBOList.get(0).getTotalFree();
//                }
                //押金总额
                BigDecimal totalDeposit = new BigDecimal(add(addVehicleDeposit,addBaseDeposit)).subtract(new BigDecimal(1000));
                //押金总额小于所需车辆押金
                if (add(totalDeposit.floatValue(), preAuthAmount[0].floatValue()) < needVehicleDeposit) {

                    //车辆押金退款无法约高端车型
//                    if (vehicleDepositDrawBackFlag) {
//                        result.setResult(OrderVehicleStatusEnum.VEHICLE_APPLY_DRAWBACK);
//                    }else{


                        //车型押金
                        float tempDeposit = add(vehicleDeposit, addVehicleDeposit);

                        if (totalDeposit.floatValue() < needVehicleDeposit) {
                            result.setResult(OrderVehicleStatusEnum.DEPOSIT_LESS);
                            BigDecimal needVehicleDepositAmount = new BigDecimal(String.valueOf(sub(needVehicleDeposit, tempDeposit)));
                            BigDecimal needDepositAmount = result.getNeedDepositAmount();
                            if (needDepositAmount == null) {
                                result.setNeedDepositAmount(needVehicleDepositAmount);
                            } else {
                                result.setNeedDepositAmount(needDepositAmount.add(needVehicleDepositAmount));
                            }
                        }else{
                            //使用现金押金
                            result.setDepositType(1);
                        }
//                    }
                }else{
                    if (preAuthAmount[0].floatValue() > needVehicleDeposit){
                        //使用预授权
                        result.setDepositType(3);
                    }else {
                        //使用现金押金
                        result.setDepositType(1);
                    }
                }
            }else{
                //使用免押
                result.setDepositType(2);
            }
            result.setResult(OrderVehicleStatusEnum.SUCCESS);
        }else {
            result.setResult(OrderVehicleStatusEnum.DEPOSIT_LESS);
        }
        //2..押金不足或者退款中 判断会员芝麻授权
        if (result.getResultStatus() != 0) {
            // 网点可以使用芝麻授权

            int isSupportShopZhima = getZhiMaCreditFlagByOrgId(checkDepositDto.getOrgCode());
            if (isSupportShopZhima == 1) {
            /*    // 会员芝麻信用等级足够
                try {
                    AntLimitLevelBO antLimitLevelBO = aliService.getAntLimitLevel(checkDepositDto.getAuthId());
                    if (antLimitLevelBO.getStatus() == 0) {
                        String limitLevel = antLimitLevelBO.getLimitLevel();
                        if (checkChiMaLevel(limitLevel, vehicleModelType)) {
                            result.setZhimaStatus(3);
                            logger.info("会员" + checkDepositDto.getAuthId() + "芝麻信用等级为" + limitLevel + "免押金约车" );
                        } else {
                            String needLevel = getVehicleModelZhiMaLevel(vehicleModelType);
                            result.setZhimaStatus(2);
                            result.setResultDesc("哎呀～芝麻信用未达标");
                            result.setZhimaMessage(checkDepositDto.getVehicleModelInfo() + "需要芝麻信用等级"+ needLevel +"以上&您当前的芝麻信用等级为"+ limitLevel);
                            logger.warn("会员" + checkDepositDto.getAuthId() + "芝麻信用等级为" + limitLevel + ",无法使用" + vehicleModelType + "等级车型");
                        }
                    } else if (antLimitLevelBO.getStatus() == -1) {
                        //会员未授权或授权已过期
                        result.setZhimaStatus(1);
                        result.setResultDesc("当前押金不足");
                        logger.warn("会员" + checkDepositDto.getAuthId() + antLimitLevelBO.getMessage());
                    } else if (antLimitLevelBO.getStatus() == -2) {
                        //非常抱歉，信用综合评估未通过，暂无法使用免押金租车服务
                        result.setZhimaStatus(4);
                        result.setResultDesc("信用综合评估未通过");
                        logger.warn("会员" + checkDepositDto.getAuthId() + antLimitLevelBO.getMessage());
                    }
                    //记录查询芝麻信用等级履历
                    try {
                        MmpUserZhimaLevelQueryLog mmpUserZhimaLevelQueryLog = new MmpUserZhimaLevelQueryLog();
                        mmpUserZhimaLevelQueryLog.setAuthId(checkDepositDto.getAuthId());
                        mmpUserZhimaLevelQueryLog.setQueryStatus(antLimitLevelBO.getStatus());
                        mmpUserZhimaLevelQueryLog.setZhimaLevel(antLimitLevelBO.getLimitLevel());
                        mmpUserZhimaLevelQueryLogMapper.insertSelective(mmpUserZhimaLevelQueryLog);
                    }catch (Exception e){
                        logger.warn("芝麻等级查询履历记录失败|" + e.getMessage(),e);
                    }
                } catch (Exception e) {
                    logger.error("会员" + checkDepositDto.getAuthId() + "查询芝麻信用等级失败|" + e.getMessage(), e);
                }*/
               // 默认芝麻授权字段已通过让前端直接去做代扣授权
                result.setZhimaStatus(3);
                //查询用户是否已经签约代扣
                String withHoldSignInfo = mmpUserTagMapper.queryWithHoldSignInfo(checkDepositDto.getAuthId());
                if(StringUtils.isNotBlank(withHoldSignInfo)){
                    Map<String, String> responseResult = aliService.queryZhimaPassed(checkDepositDto.getAuthId());
                    if("0".equals(responseResult.get("status")) && "true".equals(responseResult.get("passed"))){
                        result.setWithHoldSignStatus(1);
                        result.setResultStatus(0);
                        result.setDepositType(4);
                        logger.debug("会员" + checkDepositDto.getAuthId() + "芝麻免押约车" );
                    }else{
                        result.setZhimaStatus(2);
                        result.setResultDesc("哎呀～信用未达700分");
                        result.setZhimaMessage("无法免押用车");
                        logger.warn("会员" + checkDepositDto.getAuthId() + "信用未达700分,无法免押用车");
                    }
                }else{
                    //押金不足或者押金退款的状态也能使用芝麻信用
                    result.setResultStatus(3);
                    logger.warn("会员" + checkDepositDto.getAuthId() + "未签约代扣" );
                }
            }
        }
        return result;
    }

    @Override
    public CheckDepositResult checkDepositForOrderVehicle(CheckDepositInput checkDepositDto) {
        //1:押金  2:免押  3:预授权  4:芝麻信用
        CheckDepositResult result = new CheckDepositResult();
        int vehicleModelType = checkDepositDto.getVehicleModelType();
        //1 .判断押金
        //会员基础押金(临时)、车辆押金(临时)
        float baseDeposit = 0f;
        float vehicleDeposit = 0f;
        // 押金等级 车辆押金
        List<VehicleModelGradeInfoDto> vehicleModelGradeInfoDtoList = vehicleModelService.queryVehicleModelGradeInfoList();
        // 查询机构免押信息和会员免押信息
        MemberExemptDepositDto exemptDepositDto = membershipInfoMapper.queryExemptDeposit(checkDepositDto.getAuthId());
        if(exemptDepositDto == null){
            throw new MemberException(ExceptionEnum.PARAM_ERROR.getErrCode(),ExceptionEnum.PARAM_ERROR.getErrMsg());
        }
        int freeDepositLevel = 0;
        //修改调用企业服务查询会员免押等级
        try {
            Integer exemptDepositInfo = agencyPriceService.getExemptDepositInfo(checkDepositDto.getAuthId());
            logger.debug("查询会员押金等级服务|返回数据：" + exemptDepositInfo);
            if(exemptDepositInfo !=null ){
                freeDepositLevel = exemptDepositInfo;
            }
        }catch (Exception e){
            logger.error("查询会员押金等级服务调用失败|" + e.getMessage(),e);
        }
        if (freeDepositLevel > 0) {
            baseDeposit = BussinessConstants.BASE_DEPOSIT;
            for (VehicleModelGradeInfoDto dto :vehicleModelGradeInfoDtoList) {
                if (dto.getVehicleModelType().equals(freeDepositLevel)) {
                    vehicleDeposit = dto.getVehicleDeposit();
                }
            }
        }
        //e享天开基础押金只需要500
        int dataOrigin = exemptDepositDto.getDataOrigin();
        if (dataOrigin == 5 || dataOrigin == 6) {
            baseDeposit = add(BussinessConstants.REDUCE_DEPOSIT, baseDeposit);
        }
        //押金充值记录
        List<MemberDepositResultBO> addDeposit = memAccountService.queryMemberDepositList(checkDepositDto.getAuthId(),0,1);
        //判断基础押金(不免基础押金)
        if (baseDeposit < BussinessConstants.BASE_DEPOSIT) {
            //基础押金预授权
            List<MemberDepositResultBO> depositResultBOList = memAccountService.queryMemberDepositList(checkDepositDto.getAuthId(), 1, 2);
            float basePreAuthAmount = 0f;
            if (CollectionUtils.isNotEmpty(depositResultBOList)){
                basePreAuthAmount = (float) depositResultBOList.get(0).getTotalFree();
            }
            //基础押金退款标识
            List<MemberApplyingDepositResultBo> memberApplyingDepositResultBos = memAccountService.queryApplyingDrawbackList(checkDepositDto.getAuthId(),1);
            boolean baseDepositDrawBackFlag = false;
            if (CollectionUtils.isNotEmpty(memberApplyingDepositResultBos)){
                baseDepositDrawBackFlag = true;
            }
            //预授权无法使用
            if (add(baseDeposit, basePreAuthAmount) < BussinessConstants.BASE_DEPOSIT) {
                //基础押金退款无法约车
                if (baseDepositDrawBackFlag) {
                    result.setResult(OrderVehicleStatusEnum.BASE_APPLY_DRAWBACK);
                }else{
                    float addBaseDeposit = 0f;
                    if (CollectionUtils.isNotEmpty(addDeposit)){
                        for (MemberDepositResultBO memberDepositResultBO : addDeposit){
                            if (memberDepositResultBO.getDepositType()==1){
                                addBaseDeposit = add(addBaseDeposit,(float) memberDepositResultBO.getTotalFree());
                            }
                        }
                    }
                    //基础押金
                    float tempDeposit = add(baseDeposit, addBaseDeposit);
                    if (tempDeposit < BussinessConstants.BASE_DEPOSIT) {
                        result.setResult(OrderVehicleStatusEnum.DEPOSIT_LESS);
                        result.setNeedDepositAmount(new BigDecimal(String.valueOf(sub(BussinessConstants.BASE_DEPOSIT, tempDeposit))));
                    }else{
                        //使用现金押金
                        result.setDepositType(1);
                    }
                }
            }else{
                //使用预授权
                result.setDepositType(3);
            }
        }else{
            //使用免押
            result.setDepositType(2);
        }
        //判断车辆押金
        float needVehicleDeposit = 0f;
        for (VehicleModelGradeInfoDto dto :vehicleModelGradeInfoDtoList) {
            if (dto.getVehicleModelType().equals(vehicleModelType)) {
                needVehicleDeposit = dto.getVehicleDeposit();
            }
        }
        if (result.getResultStatus() == 0 && needVehicleDeposit > 0) {
            //免车辆押金 小于需要的车辆押金
            if (vehicleDeposit < needVehicleDeposit) {
                //车辆押金预授权
                List<MemberDepositResultBO> vehicleDepositResultBOList = memAccountService.queryMemberDepositList(checkDepositDto.getAuthId(), 2, 2);
                float vehiclePreAuthAmount = 0f;
                if (CollectionUtils.isNotEmpty(vehicleDepositResultBOList)){
                    vehiclePreAuthAmount = (float) vehicleDepositResultBOList.get(0).getTotalFree();
                }
                //预授权无法使用
                if (add(vehicleDeposit, vehiclePreAuthAmount) < needVehicleDeposit) {
                    //车辆押金退款

                    List<MemberApplyingDepositResultBo> vehicleMemberApplyingDepositResultBos = memAccountService.queryApplyingDrawbackList(checkDepositDto.getAuthId(),2);
                    boolean vehicleDepositDrawBackFlag = false;
                    if (CollectionUtils.isNotEmpty(vehicleMemberApplyingDepositResultBos)){
                        vehicleDepositDrawBackFlag = true;
                    }
                    //车辆押金退款无法约高端车型
                    if (vehicleDepositDrawBackFlag) {
                        result.setResult(OrderVehicleStatusEnum.VEHICLE_APPLY_DRAWBACK);
                    }else{

                        float addVehicleDeposit = 0f;
                        if (CollectionUtils.isNotEmpty(addDeposit)){
                            for (MemberDepositResultBO memberDepositResultBO : addDeposit){
                                if (memberDepositResultBO.getDepositType()==2){
                                    addVehicleDeposit = add(addVehicleDeposit,(float) memberDepositResultBO.getTotalFree());
                                }
                            }
                        }
                        //车型押金
                        float tempDeposit = add(vehicleDeposit, addVehicleDeposit);
                        if (tempDeposit < needVehicleDeposit) {
                            result.setResult(OrderVehicleStatusEnum.DEPOSIT_LESS);
                            BigDecimal needVehicleDepositAmount = new BigDecimal(String.valueOf(sub(needVehicleDeposit, tempDeposit)));
                            BigDecimal needDepositAmount = result.getNeedDepositAmount();
                            if (needDepositAmount == null) {
                                result.setNeedDepositAmount(needVehicleDepositAmount);
                            } else {
                                result.setNeedDepositAmount(needDepositAmount.add(needVehicleDepositAmount));
                            }
                        }else{
                            //使用现金押金
                            result.setDepositType(1);
                        }
                    }
                }else{
                    //使用预授权
                    result.setDepositType(3);
                }
            }else{
                //使用免押
                result.setDepositType(2);
            }
        }
        //2..押金不足或者退款中 判断会员芝麻授权
        if (result.getResultStatus() != 0) {
            // 网点可以使用芝麻授权

            int isSupportShopZhima = getZhiMaCreditFlagByShopSeq(checkDepositDto.getShopSeq());
            if (isSupportShopZhima == 1 && vehicleModelType != 4) {
                // 会员芝麻信用等级足够
                try {
                    AntLimitLevelBO antLimitLevelBO = aliService.getAntLimitLevel(checkDepositDto.getAuthId());
                    if (antLimitLevelBO.getStatus() == 0) {
                        String limitLevel = antLimitLevelBO.getLimitLevel();
                        if (checkChiMaLevel(limitLevel, vehicleModelType)) {
                            result.setZhimaStatus(3);
                            logger.debug("会员" + checkDepositDto.getAuthId() + "芝麻信用等级为" + limitLevel + "免押金约车" );
                        } else {
                            String needLevel = getVehicleModelZhiMaLevel(vehicleModelType);
                            result.setZhimaStatus(2);
                            result.setResultDesc("哎呀～芝麻信用未达标");
                            result.setZhimaMessage(checkDepositDto.getVehicleModelInfo() + "需要芝麻信用等级"+ needLevel +"以上&您当前的芝麻信用等级为"+ limitLevel);
                            logger.warn("会员" + checkDepositDto.getAuthId() + "芝麻信用等级为" + limitLevel + ",无法使用" + vehicleModelType + "等级车型");
                        }
                    } else if (antLimitLevelBO.getStatus() == -1) {
                        //会员未授权或授权已过期
                        result.setZhimaStatus(1);
                        result.setResultDesc("当前押金不足");
                        logger.warn("会员" + checkDepositDto.getAuthId() + antLimitLevelBO.getMessage());
                    } else if (antLimitLevelBO.getStatus() == -2) {
                        //非常抱歉，信用综合评估未通过，暂无法使用免押金租车服务
                        result.setZhimaStatus(4);
                        result.setResultDesc("信用综合评估未通过");
                        logger.warn("会员" + checkDepositDto.getAuthId() + antLimitLevelBO.getMessage());
                    }
                    //记录查询芝麻信用等级履历
                    try {
                        MmpUserZhimaLevelQueryLog mmpUserZhimaLevelQueryLog = new MmpUserZhimaLevelQueryLog();
                        mmpUserZhimaLevelQueryLog.setAuthId(checkDepositDto.getAuthId());
                        mmpUserZhimaLevelQueryLog.setQueryStatus(antLimitLevelBO.getStatus());
                        mmpUserZhimaLevelQueryLog.setZhimaLevel(antLimitLevelBO.getLimitLevel());
                        mmpUserZhimaLevelQueryLogMapper.insertSelective(mmpUserZhimaLevelQueryLog);
                    }catch (Exception e){
                        logger.warn("芝麻等级查询履历记录失败|" + e.getMessage(),e);
                    }
                } catch (Exception e) {
                    logger.error("会员" + checkDepositDto.getAuthId() + "查询芝麻信用等级失败|" + e.getMessage(), e);
                }
                //查询用户是否已经签约代扣
                String withHoldSignInfo = mmpUserTagMapper.queryWithHoldSignInfo(checkDepositDto.getAuthId());
                if(StringUtils.isNotBlank(withHoldSignInfo)){
                    result.setWithHoldSignStatus(1);
                }else{
                    logger.warn("会员" + checkDepositDto.getAuthId() + "未签约代扣" );
                }
                if(result.getZhimaStatus() == 3 && result.getWithHoldSignStatus() == 1){
                    //使用芝麻信用
                    result.setResultStatus(0);
                    result.setDepositType(4);
                    logger.debug("会员" + checkDepositDto.getAuthId() + "芝麻免押约车" );
                }else if(result.getZhimaStatus() == 1 || result.getZhimaStatus() == 3 || result.getWithHoldSignStatus() == 0 ){
                    //押金不足或者押金退款的状态也能使用芝麻信用
                    result.setResultStatus(3);
                }
            }
        }
        return result;
    }

    /**
     *  计算缺的押金金额
     * @param hasDrivingDeposit
     * @param needVehicleDeposit
     * @param listPageDto
     */
    private static void getLackDepositAmount(BigDecimal hasDrivingDeposit, BigDecimal needVehicleDeposit, ListPageDto listPageDto) {
        BigDecimal lackDrivingDeposit = hasDrivingDeposit.compareTo(needVehicleDeposit) > 0 ? BigDecimal.ZERO : needVehicleDeposit.subtract(hasDrivingDeposit);
        listPageDto.setDepositAmount(lackDrivingDeposit);
    }

    /**
     * check芝麻信用等级
     * @param level
     * @param vehicleLevel
     * @return
     */
    public static boolean checkChiMaLevel(String level,int vehicleLevel){
        List<String> list = BussinessConstants.V_2_ZHIMAI_LEVEL.get(vehicleLevel);
        if(CollectionUtils.isNotEmpty(list) && list.contains(level)){
            return true;
        }
        return false;
    }

    public static String getVehicleModelZhiMaLevel(int vehicleLevel){
        List<String> list = BussinessConstants.V_2_ZHIMAI_LEVEL.get(vehicleLevel);
        return list.get(list.size()-1);
    }

    @Override
    public Integer getZhiMaCreditFlagByShopSeq(int shopSeq){
        ShopInfoDto shopInfoDto = shopService.getShopInfoById(shopSeq);
        if (shopInfoDto == null){
            return 0;
        }
        Integer flag = agencyInfoMapper.getZhiMaCreditFlagByOrgId(shopInfoDto.getOrgId());
        if (flag == null){
            return 0;
        }
        return flag;
    }


    public Integer getZhiMaCreditFlagByOrgId(String orgCode) {
        if(StringUtils.isBlank(orgCode)) {
            return 0;
        }
        Integer flag = agencyInfoMapper.getZhiMaCreditFlagByOrgId(orgCode);
        if (flag == null){
            return 0;
        }
        return flag;
    }

    @Override
    public Integer getZhiMaCreditFlagByCityName(String cityName) {
        Integer flag = 0;
        if(StringUtils.isBlank(cityName)){
            return flag;
        }
        OrgCityRelation orgCityRelation = orgCityRelationMapper.selectByCityName(cityName);
        if (orgCityRelation == null || StringUtils.isBlank(orgCityRelation.getCityName())){
            return 1;
        }
        flag = agencyInfoMapper.getZhiMaCreditFlagByOrgId(orgCityRelation.getOrgId());
        if (flag == null){
            return 0;
        }
        return flag;
    }

    @Override
    public List<CityDto> canUseZhimaCitys() {

        List<String> orgIds = agencyInfoMapper.canUseZhimaCitys();

        if(CollectionUtils.isEmpty(orgIds)){
            return null;
        }

        List<OrgCityRelation> orgCityRelations = orgCityRelationMapper.selectByOrgId(orgIds);

        if (CollectionUtils.isEmpty(orgCityRelations)){
            return null;
        }
        List<CityDto> cityDtos = new ArrayList<>();

        orgCityRelations.stream().forEach(p->{
            CityDto cityDto = new CityDto();
            cityDto.setCity(p.getCityName());
            cityDto.setCityid(p.getCityId());
            cityDtos.add(cityDto);
        });
        return cityDtos;
    }

    @Override
    public DepositAuthorizonDto queryAuthorizedDepositInfo(QueryDepositInfoInputDTO inputDTO) {
        //接口：不获取 押金、免押相关信息，只提供预授权 、芝麻信用代扣状态查询
        String authId = inputDTO.getAuthId();
        if(StringUtils.isBlank(authId)){
            return null;
        }
        DepositAuthorizonDto depositAuthorDto = new DepositAuthorizonDto();

        //1. 查询基础押金预授权，存在预授权的情况下不会有现金充值。
        List<MemberDepositResultBO> depositResultBOList = memAccountService.queryMemberDepositList(authId, 1, 2);
        logger.debug("memAccountService.queryMemberDepositList(authId, 1, 2) : " + depositResultBOList);
        if(CollectionUtils.isNotEmpty(depositResultBOList)){
            MemberDepositResultBO depositResultBO = depositResultBOList.get(0);
            depositAuthorDto.setBaseDeposit(BigDecimal.valueOf(depositResultBO.getTotalFree())
                    .add(depositAuthorDto.getBaseDeposit()));
            depositAuthorDto.setBaseDepositType(2);
        }
        //2. 查询车辆押金预授权，存在预授权的情况下不会有现金充值。
        depositResultBOList = memAccountService.queryMemberDepositList(authId, 2, 2);
        logger.debug("memAccountService.queryMemberDepositList(authId, 2, 2) : " + depositResultBOList);
        if(CollectionUtils.isNotEmpty(depositResultBOList)){
            MemberDepositResultBO depositResultBO = depositResultBOList.get(0);
            depositAuthorDto.setVehicleDeposit(BigDecimal.valueOf(depositResultBO.getTotalFree())
                    .add(depositAuthorDto.getVehicleDeposit()));
            depositAuthorDto.setVehicleDepositType(2);
        }

        //3. 查询芝麻信用代扣授权状态
        //查询用户是否已经签约代扣
        String withHoldSignInfo = mmpUserTagMapper.queryWithHoldSignInfo(authId);
        if(StringUtils.isNotBlank(withHoldSignInfo)){
            depositAuthorDto.setWithHoldSignStatus(1);
        }

        return depositAuthorDto;
    }

    @Override
    public Integer queryAuthZhiMaRefund(Integer shopSeq, String authId) {

        int isSupportShopZhima = getZhiMaCreditFlagByShopSeq(shopSeq);
        if (isSupportShopZhima != 1){
            return -1;
        }
        //查询用户是否已经签约代扣
        String withHoldSignInfo = mmpUserTagMapper.queryWithHoldSignInfo(authId);
        if(StringUtils.isNotBlank(withHoldSignInfo)){
            Map<String, String> responseResult = aliService.queryZhimaPassed(authId);
            if("0".equals(responseResult.get("status")) && "true".equals(responseResult.get("passed"))){
               return 1;
            }else{
                return -1;
            }
        }else{
            return -1;
        }
    }


    @Override
    public void saveZhimaAuthorizedRecord(MemberAuthorizedRecordInput input) {
        logger.info("保存用户芝麻授权日志，入参=" + JSON.toJSONString(input));
        //1. 参数校验
        if(input == null || StringUtils.isBlank(input.getAuthId())
                || null == input.getAuthorizedType()
                || null == input.getAuthorizedResult()) {
            throw new MemberException(ExceptionEnum.PARAM_ERROR.getErrCode(), ExceptionEnum.PARAM_ERROR.getErrMsg());
        }
        if(input.getAuthorizedType() != 2 && input.getAuthorizedResult() == 1 && input.getAuthorizedFraction() == null) {
            throw new MemberException(ExceptionEnum.PARAM_ERROR.getErrCode(), ExceptionEnum.PARAM_ERROR.getErrMsg());
        }
        if(StringUtils.isBlank(input.getAuthorizedReason())) {
            input.setAuthorizedReason(StringUtils.EMPTY);
        }
        if(input.getAuthorizedDateTime() == null) {
            input.setAuthorizedDateTime(new Date());
        }
        if(input.getOperatorId() == null || StringUtils.isBlank(input.getOperatorName())) {
            input.setOperatorId(-1L);
            input.setOperatorName("membership-rpc");
        }
        //2. 授权记录保存
        MmpUserAuthorizedLog mmpUserAuthorizedLog = new MmpUserAuthorizedLog();
        BeanUtils.copyProperties(input, mmpUserAuthorizedLog);
        mmpUserAuthorizedLog.setStatus(1);
        mmpUserAuthorizedLog.setCreateOperId(input.getOperatorId());
        mmpUserAuthorizedLog.setCreateTime(new Date());
        mmpUserAuthorizedLog.setCreateOperName(input.getOperatorName());
        mmpUserAuthorizedLog.setMiscDesc(StringUtils.EMPTY);
        mmpUserAuthorizedLog.setUpdateOperId(input.getOperatorId());
        mmpUserAuthorizedLog.setUpdateTime(new Date());
        mmpUserAuthorizedLog.setUpdateOperName(input.getOperatorName());
        mmpUserAuthorizedLogMapper.insert(mmpUserAuthorizedLog);
    }

    @Override
    public PageBeanDto<MemberAuthorizedLogDTO> queryZhimaAuthorizedRecordPage(MemberAuthorizedRecordQueryInput queryInput) {
        if(queryInput == null) {
            return null;
        }
        Page page = queryInput.getPage();
        if(page == null){
            page = new Page(1,10, false);
            queryInput.setPage(page);
        }
        if(page.getCountFlag()){
            int num = mmpUserAuthorizedLogMapper.countUserAuthorizedHistory(queryInput);
            page.setCount(num);
        }
        List<MemberAuthorizedLogDTO> list = queryZhimaAuthorizedRecords(queryInput);
        PageBeanDto<MemberAuthorizedLogDTO> pageBeanDto = new PageBeanDto<>();
        pageBeanDto.setPage(page);
        pageBeanDto.setList(list);
        return pageBeanDto;
    }


    @Override
    public List<MemberAuthorizedLogDTO> queryUserZhimaAuthorizedRecords(String authId, List<Integer> authorizedTypes, Integer authorizedResult) {
        if(StringUtils.isBlank(authId)) {
            logger.warn("查询会员芝麻授权记录需提供会员id");
            return null;
        }
        MemberAuthorizedRecordQueryInput queryInput = new MemberAuthorizedRecordQueryInput();
        queryInput.setAuthId(authId);
        queryInput.setAuthorizedTypes(authorizedTypes);
        queryInput.setAuthorizedResult(authorizedResult);
        List<MemberAuthorizedLogDTO> list = queryZhimaAuthorizedRecords(queryInput);
        return list;
    }

    private List<MemberAuthorizedLogDTO> queryZhimaAuthorizedRecords(MemberAuthorizedRecordQueryInput queryInput){
        List<MmpUserAuthorizedLog> records = mmpUserAuthorizedLogMapper.queryAuthorizedHistoryList(queryInput);
        List<MemberAuthorizedLogDTO> list = new ArrayList<>(records.size());
        if(CollectionUtils.isNotEmpty(records)){
            for(MmpUserAuthorizedLog userAuthorizedLog : records){
                MemberAuthorizedLogDTO userAuthorizedLogDTO = new MemberAuthorizedLogDTO();
                BeanUtils.copyProperties(userAuthorizedLog, userAuthorizedLogDTO);
                list.add(userAuthorizedLogDTO);
            }
        }
        return list;
    }
}
