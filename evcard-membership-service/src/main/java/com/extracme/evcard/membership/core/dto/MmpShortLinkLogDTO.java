package com.extracme.evcard.membership.core.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

@Data
public class MmpShortLinkLogDTO implements Serializable {


    private static final long serialVersionUID = 8041473638371503975L;
    private Integer id;
    @JsonFormat(timezone="GMT+8", pattern="yyyy-MM-dd")
    private LocalDate createTime;
    private String createOperName;
    private Long operationType;
    private String operationTypeName;
}
