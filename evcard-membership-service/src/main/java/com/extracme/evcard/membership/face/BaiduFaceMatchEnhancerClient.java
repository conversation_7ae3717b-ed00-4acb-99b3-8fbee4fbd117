package com.extracme.evcard.membership.face;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.extracme.evcard.membership.third.baidu.BaseResult;
import com.extracme.evcard.membership.third.baidu.FaceMatchEnhancerRequest;
import com.extracme.evcard.membership.third.baidu.FaceMatchEnhancerResult;
import com.extracme.evcard.membership.third.baidu.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


/**
 * 人脸比对接口（增强版）
 * 接口文档： https://cloud.baidu.com/doc/FACE/s/6knhjw4yq
 */
@Slf4j
@Component
public class BaiduFaceMatchEnhancerClient {

    @Value("j5rgtVrKpk5y9aETFQwzK9pf")
    private String ak;

    @Value("61BLGR2CUS31xoDU4irSRwKl761wOXvK")
    private String sk;

    @Value("${baidu.face.api}")
    private String baseUrl;

    @Autowired
    private BaiduAccessTokenClient accessTokenClient;

    /**
     * 人脸比对
     */
    public BaseResult<FaceMatchEnhancerResult> faceMatch(FaceMatchEnhancerRequest request) {
        // 请求url
        String url = baseUrl + "rest/2.0/face/v3/matchsec";
        try {
            String param = JSON.toJSONString(request);
            String accessToken = getAuthToken();
//            String accessToken = "test";
            if(StringUtils.isBlank(accessToken)) {
                log.error("获取baidubce accessToken失败");
                return null;
            }
            //请求人脸身份认证接口
            String result = HttpUtil.post(url, accessToken, "application/json", param);
//            String result = "{\"log_id\":1467668066039704962,\"result\":{\"score\":95.51338196}}";
            BaseResult<FaceMatchEnhancerResult> resp = JSON.parseObject(result, new TypeReference<BaseResult<FaceMatchEnhancerResult>>() {});
//            log.warn("baidu:matchsec input={}, result={}， {}", param, resp.getError_code(), resp.getError_msg());
            return resp;
        } catch (Exception e) {
            log.error("baidu:matchsec input={}", JSON.toJSONString(request), e);
            log.error("", e);
        }
        return null;
    }

    public String getAuthToken(){
        return accessTokenClient.getAuthToken(ak, sk);
    }

}
