package com.extracme.evcard.membership.core.dto.md;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/8/30
 */
@Data
public class SearchCityConfigurationRequest {
    private long cityId; // 运营城市ID  0=查全部
    private String orgCode; //  运营公司CODE
    private int storeBusinessStatus; // 门店业务状态 0=查全部 2=否 1=是
    private int crossRegionReturnCar; // 跨区还车  0=查全部 2=否 1=是
    private int crossStoreReturnCar; // 跨店还车  0=查全部 2=否 1=是
    private int pageNum; // 页码
    private int pageSize; // 每页大小
    private long tenantId; // 租户ID
}
