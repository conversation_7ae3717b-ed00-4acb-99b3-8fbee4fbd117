package com.extracme.evcard.membership.credit.dao;

import com.extracme.evcard.membership.credit.dto.CreditEventTypeReportPageDto;
import com.extracme.evcard.membership.credit.dto.CreditEventTypeReportParamsDto;
import com.extracme.evcard.membership.credit.model.MmpCreditEventTypeReport;
import com.extracme.evcard.rpc.dto.Page;

import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MmpCreditEventTypeReportMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_credit_event_type_report
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_credit_event_type_report
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    int insert(MmpCreditEventTypeReport record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_credit_event_type_report
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    int insertSelective(MmpCreditEventTypeReport record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_credit_event_type_report
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    MmpCreditEventTypeReport selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_credit_event_type_report
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    int updateByPrimaryKeySelective(MmpCreditEventTypeReport record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_credit_event_type_report
     *
     * @mbggenerated Tue Nov 28 14:08:44 CST 2017
     */
    int updateByPrimaryKey(MmpCreditEventTypeReport record);

    Integer getCreditEventTypeReportCount(CreditEventTypeReportParamsDto paramsDto);

    List<CreditEventTypeReportPageDto> getCreditEventTypeReportPages(
            @Param("paramsDto") CreditEventTypeReportParamsDto paramsDto,
            @Param("list") List<Integer> eventTypeIds);

    List<Integer> getCreditEventTypeIdReportPages(@Param("paramsDto") CreditEventTypeReportParamsDto paramsDto,
                                                  @Param("page") Page page);

    List<Integer> getCreditEventTypeIdReportExport(@Param("paramsDto") CreditEventTypeReportParamsDto paramsDto);

    Integer selectIsExistTypeReport(@Param("paramsDto") MmpCreditEventTypeReport mmpCreditEventTypeReport);

    int updateExistEventTypeReport(@Param("paramsDto") MmpCreditEventTypeReport mmpCreditEventTypeReport);
}