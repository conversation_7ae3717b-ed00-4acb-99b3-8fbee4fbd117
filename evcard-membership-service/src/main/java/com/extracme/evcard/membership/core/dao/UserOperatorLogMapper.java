package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.dto.UserOperatorLogDTO;
import com.extracme.evcard.membership.core.model.QueryLikeKey;
import com.extracme.evcard.membership.core.model.UserOperatorLog;
import com.extracme.evcard.rpc.dto.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Mapper类，，对应表user_operator_log
 */
public interface UserOperatorLogMapper {

	/**
	 * 插入固定资产操作日志的记录
	 *
	 * @param userOperatorLog
	 * @return
	 */
	Integer saveSelective(UserOperatorLog userOperatorLog);

	/**
	 * 由会员操作日志中统计指定操作的执行次数
	 *
	 * @param authId
	 * @param keywords
	 * @param startTime
	 * @return
	 */
	Integer countMemChangeOperatorLog(@Param("authId") String authId,
									  @Param("keywords") List<QueryLikeKey> keywords,
									  @Param("startTime") String startTime);

	/**
	 * 查询会员操作日志列表（分页）
	 *
	 * @param foreignKey
	 * @param foreignKey2
	 * @param page
	 * @return
	 */
	List<UserOperatorLogDTO> queryUserOperateLog(@Param("foreignKey") String foreignKey,
												 @Param("foreignKey2") String foreignKey2,
												 @Param("page") Page page);
}