package com.extracme.evcard.membership.config;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @Discription
 * @date 2021/12/10
 */
@Configuration
@Data
public class CommConfig {
    /** 埋点开关 */
    @Value("${sa_isEnable:0}")
    private String saIsEnable;

    /** 接口-登出 */
    @Value("${logout_api}")
    private String logoutUrl;

    @Value("${mas_api_addr}")
    private String appApiAddress;

    /**
     * 合同-fadada
     */
    @Value("${fadada_app_id}")
    private String fadadaApiUrl;

    @Value("${fadada_api_url}")
    private String fadadaAppId;

    @Value("${fadada_app_secret}")
    private String fadadaAppSecret;

    @Value("${fadada_version}")
    private String fadadaVersion;
}
