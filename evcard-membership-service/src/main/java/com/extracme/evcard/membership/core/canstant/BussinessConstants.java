package com.extracme.evcard.membership.core.canstant;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Pattern;

import com.extracme.evcard.membership.core.dto.AppKeyDataBean;
import com.extracme.evcard.membership.core.dto.CommBaseResponse;
import com.extracme.evcard.membership.core.enums.MemOperateTypeEnum;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.entity.LogPoint;

public class BussinessConstants {
    // 车生活
    public static final String CAR_LIFE_CHANNEL = "ALI_PAY_CAR_LIFE";
    // 擎路
    public static final String QING_LU_CHANNEL = "Qing_Lu";
    // 长短租融合
    public static final String LONG_SHORT_MIX = "LONG_SHORT_MIX";
    // 赛乐通
    public static final String SAI_LE_TONG = "second_saic_travel";

    //用户渠道rediskey
    public static final String MEMBER_CHANNEL_ID_KEY = "Member_channelId";

    //channelId对应的用户
    public static final String CHANNEL_ID_MEMBER_KEY="ChannelId_member";

    //用户appType
    public static final String MEMBER_APPTYPE="Member_appType";

   /* //用户imei
    public static final String MEMBER_IMEI = "imei_";

    //唯一imei
    public static final String UNIQUE_IMMEI = "unique_imei_";*/

    //imei更换次数
    public static final String IMEI_CHANGE_NUM = "imeiChangeNum_";

    //会员条款版本key
    public static final String TYPE_VERSION_MAP= "typeVersionMap";

    // 基础押金
    public static final float BASE_DEPOSIT = 1000f;
//    public static final float BASE_DEPOSIT = 0.01f;

    //减免金额
    public static final float REDUCE_DEPOSIT = 500f;

    public static final String CHINA_NATIONAL = "中国";

    public static final String NATIONAL_HK_MACAO_TAIWAN = "港澳台";

    public static final String CHINA_NATIONAL_SOLIER = "中国（军人）";

    /** 用户信息编辑全局开关 */
    public static final String IS_EDIT_REDIS_KEY = "redis:isEdit";

    //图片网址
    public static final String PIC_ADDRESS = "web_url";

    /** 敏感信息图片网址 */
    public static final String SENSITIVE_PIC_ADDRESS = "sensitive_web_url";

    //环境配置
    public static final String ENV = "env";
    //环境配置
    public static final String ENV_SUB = "env_sub";
    public static final String ENV_SUB_STRESS = "stress";

    //验证码重试次数
    public static final String VERIFYCODE_NUM= "verifycode_nume";

    //发送短信数量
    public static final String SEND_SMS_NUM = "send_sms_num_";

    //发送登录或者注册短信数量
    public static final String SEND_LRSMS_NUM = "send_lrsms_num_";

    //正存在的短信key
    public static final String SMS_EXIT ="sms_";

    //多步校验时需要的rediskey
    public static final String CHECK_VERIFY_STATUS = "redis:checkVerifyStatus_";

    /** 老用户区分时间点 */
    public static final LocalDateTime OLD_USER_DATETIME = LocalDateTime.of(2018,1,16,0,0,0);

    //第三方key集合
    public static Map<String, AppKeyDataBean> appkeyMap = new HashMap<String, AppKeyDataBean>();

    /**  基础押金值 */
    public static final BigDecimal BASE_DEPOSIT_VALUE = new BigDecimal(1000);

    /** 芝麻免押金额 */
    public static final BigDecimal ZHI_MA_FREE_DEPOSIT_VALUE = new BigDecimal(4000);

    /**  减免押金值 */
    public static final BigDecimal REDUCE_DEPOSIT_VALUE = new BigDecimal(500);

    public static final LocalDateTime SEND_COUPON_DATETIME = LocalDateTime.of(2018,7,16,0,0,0);
    
    public static final String CHECK_AUTHID_STATUS = "redis:checkAuthIdStatus_";

    /** 老用户区分时间点 */

    /** 车型等级对应可以免押的芝麻信用等级 */
    public static List<String> ZHIMA_LEVEL_ONE = Arrays.asList("A","B","C","D","E","F");
    public static  List<String> ZHIMA_LEVEL_TWO = Arrays.asList("A","B","C","D","E");
    public static  List<String> ZHIMA_LEVEL_THREE = Arrays.asList("A","B","C","D");


    public static  Map<Integer,List<String>> V_2_ZHIMAI_LEVEL = new HashMap<Integer,List<String>>() ;

    static {
        V_2_ZHIMAI_LEVEL.put(1,ZHIMA_LEVEL_ONE );
        V_2_ZHIMAI_LEVEL.put(2, ZHIMA_LEVEL_TWO);
        V_2_ZHIMAI_LEVEL.put(3, ZHIMA_LEVEL_THREE);
    }

    public static final String SYSTEM = "member-rpc";

    public static final Pattern MOBILE_PATTERN = Pattern.compile("([0-9]){11}");

    /** 获取sts的token region的值*/
    public static final String REGION_CN_HANGZHOU = "cn-hangzhou";

    /** 获取sts的token 当前 STS API 版本*/
    public static final String STS_API_VERSION = "2015-04-01";

    /** 驾照准驾类型 */
    public static final List<String> DRIVER_LICENSE_TYPE_LIST = Arrays.asList("A1","A2","A3","B1","B2","C1","C2");

    /** 缓存accsessToken  */
    public static String ACCESS_TOKEN_KEY = "redis:accessToken";

    /** 应用标识 */
    public static final String SAIC_APP_FLAG = "6xq6hBj1Vx";

    /** 应用密钥 */
    public static final String SAIC_APP_SECRET = "de9de19e-8a8e-4aca-886e-87703c18afe5";

    /** 获取上汽平台token链接 */
    public static final String GET_SAIC_ASSESSTOKEN_URL = "https://openapi.saicmotor.com:9443/api/am/store/v0.12/applications/getAccessToken";

    /** 置信度阈值 */
    public static String THRESHOLDS = "le4";
    /** 上汽平台 身份鉴定服务地址  */
    public static String SAIC_AUTHENTICATION = "https://openapi.saicmotor.com:9443/services/cloud/face/v1.0.0/authentication";

    /** 商汤 身份鉴定服务地址  */
    public static String Sensetime_AUTHENTICATION = "https://v2-auth-api.visioncloudapi.com/identity/idnumber_verification/stateless";
    /** 上汽人脸识别 公安部不存在图片数据  */
    public static String FACE_IMAGE_NOT_EXIST_MSG = "IMAGE_ERROR_UNSUPPORTED_FORMAT: data_source";

    /** 三要素认证 聚合返回结果 */
    public static String AUTHENTICATE_RESULT_1 = "一致";
    /** 三要素认证 聚合返回结果 */
    public static String AUTHENTICATE_RESULT_2 = "不一致";

    /** 三要素认证 是否优先聚合  true:先聚合再小视；false:先小视，再聚合 */
    public static Boolean AUTHENTICATE_SEQUENCE_JU_HE = true;

    /** APPCODE */
    public static final String APP_CODE = "1682b34ff7ea439989f66649bb1062ba";

    /** 人脸验证 */
    public static final String FACE_VERIFY = "http://rlsbbd.market.alicloudapi.com/face/verify";

    /**  人脸对比信任度阀值*/
    public static final double CONFIDENCE = 70d;

    /** 上汽平台 驾照识别服务地址  */
    public static String SAIC_DRIVER_LICENSE_OCR = "https://openapi.saicmotor.com:9443/services/cloud/driverlicense/v1.0.0/driver";

    /** 设备黑名单  redis的key */
    public static String IMEI_BLACK_LIST_KEY = "imeiBlackList";


    /**
     * 会员条款最新
     */
    public static final String MEMEBER_SZ = "/evcard-mas/#/nowRyles";
    /**
     * 隐私政策最新
     */
    public static final String MEMEBER_YS = "/evcard-mas/#/nowPrivacy";

    public static final String MEMBER_WT_FMT = "/member_provision/WT%s.html";

    /**
     * 三要素认证 0仅聚合 1仅小视 01先聚合再小视 10先小视，再聚合
     * 此参数改为配置项 elements.authenticate.sequence
     */
    public static String LICENSE_AUTHENTICATE_SEQUENCE = "0";
    public static String[] LICENSE_AUTHENTICATE_SUPPLIERS = {"聚合", "小视", "点微-8320", "点微-8318", "收卡拉"};
    public static String[] LICENSE_AUTHENTICATE_SUPPLIER_NAMES = {"juHeAuthService", "miniVisionAuthService", "dianWeiAuthService", "dianWeiAuthPlusService", "shoukalaAuthService"};

    /** 特殊渠道 */
    public static final String APP_KEY_SGM = "sgm_car_sharing";
    public static final String APP_KEY_IBUICK = "ibuick_car_sharing";
    public static final String APP_KEY_SGM_SONGJIANG = "sgm_songjiang";
    /**
     * 共享汽车
     */
    public static final String APP_KEY_CAR_SHARING = "alipay_carsharing";

    public static final String PREFIX_LIC_VLALID = "您的驾照状态为%s，请处理后重新上传";
    public static final int REVIEW_TODO = 0;
    public static final int REVIEW_SUCCESS = 1;
    public static final int REVIEW_NO_PASS = 2;

    public static final LogPoint MEM_LIC_AUTH = new LogPoint("MR0001", "三要素校验", "evcard-membership");
    public static final LogPoint MEM_RVIEW_CORRECT = new LogPoint("MR0002", "会员驾照状态校准", "evcard-membership");
    public static final LogPoint MEM_UPDATE_LICENSE_STATUS = new LogPoint("MR0003", "修改会员驾照状态", "evcard-membership");
    public static final LogPoint MEM_RVIEW_CORRECT_JOB = new LogPoint("MR0004", "会员驾照状态校准-查无", "evcard-membership");


    /***
     * 审核项-未审核(会员管理系统常量)
     * 驾照
     */
    public static final String  REVIEW_ITEMS_INIT = "0000000";

    /***
     * 审核项-未审核(会员管理系统常量)
     * 身份证
     */
    public static final String  IDCARD_REVIEW_ITEMS_INIT = "0000000";

    /**
     * 审核项-审核通过(会员管理系统常量)
     * 身份证
     */
    public static final String IDCARD_REVIEW_ITEMS_OK= "1111111";

    /**
     * 审核项-审核通过(会员管理系统常量)
     */
    public static final String REVIEW_ITEMS_OK = "111111111111";

    public static final String APP_KEY_EVCARD_MMP = "evcard-mmp";
    public static final String APP_KEY_EVCARD_MEMBERSHIP_RPC = "evcard-membership-rpc";

    public static final String MANUFACTURER_EVCARD_MMP = "后台";

    // base64图片格式前缀
    public static final String PREFIX_BASE64 = "data:image/png;base64,";

    /**
     * OCR长期驾照
     */
    public static final String MAX_CERT_EXPIRE_TIME_DESC = "长期";
    public static final String MAX_CERT_EXPIRE_TIME = "2099-12-31";

    // 履約 appkey
    public static final String APPKEY_OFC = "ofc";
    public static final String APPKEY_QL = "ql";

    public static final List<String> FACE_NO_CHECK_CHANNEL = Arrays.asList("zhifubao_02");

    /**
     * 身份证 审核类型集合
     */
    public static List<Long> IDCARD_TYPES = Arrays.asList(
            MemOperateTypeEnum.FACE_REC.getCode(),
            MemOperateTypeEnum.IDCARD_SUBMIT_DATA.getCode(),
            MemOperateTypeEnum.IDCARD_AUTO_AUDIT_PASS.getCode(),
            MemOperateTypeEnum.IDCARD_AUTO_AUDIT_NOT_PASS.getCode(),
            MemOperateTypeEnum.IDCARD_MANUAL_AUDIT_PASS.getCode(),
            MemOperateTypeEnum.IDCARD_MANUAL_AUDIT_NOT_PASS.getCode(),
            MemOperateTypeEnum.IDCARD_MANUAL_REAUDIT.getCode()
    );

    /**
     * 驾驶证 审核类型集合
     */
    public static List<Long> LICENCE_TYPES = Arrays.asList(
            MemOperateTypeEnum.LICENSE_SUBMIT_DATA.getCode(),
            MemOperateTypeEnum.LICENSE_MANUAL_AUDIT_PASS.getCode(),
            MemOperateTypeEnum.LICENSE_MANUAL_AUDIT_NOT_PASS.getCode(),
            MemOperateTypeEnum.LICENSE_MANUAL_REAUDIT.getCode()
    );

    // 二级渠道 appkey
    public static String SECOND_CHANNEL_PRE = "second_";
    // 哈啰
    public final static String HELLO_SECOND_APP_KEY = "second_hello";

    // 车生活
    public final static String CAR_LIFE_SECOND_APP_KEY = "second_ALI_PAY_CAR_LIFE";


    public final static String TIEXING_SECOND_APP_KEY = "second_tiexing";

    // 同程租车
    public final static String TONGCHENG_SECOND_APP_KEY = "second_tongchengzuche";

    public final static String TONGCHENG_APP_KEY = "tongchengzuche";

    public static final int TONGCHENG_PLATFORM_ID = 113;

    /**
     * 黑名单类型：1=黑名单
     */
    public static final Integer BLACKLIST_TYPE_BLACK = 1;


    public static String HTML_JIANAME = "<h id=\"jianame\"></h>";
    public static String HTML_YINAME = "<h id=\"yiname\"></h>";
    public static String HTML_IDCARD = "<h id=\"idcard\"></h>";
    public static String HTML_DATE = "<h id=\"date\">      年    月    日</h>";
    // 签字区html
    //public static String HTML_SIGN = "https://evcard.oss-cn-shanghai.aliyuncs.com/sign.jpg";
    public static String HTML_SIGN = "<img src=\"https://evcard.oss-cn-shanghai.aliyuncs.com/sign.jpg\" alt=\"\" style=\"margin-bottom: -110px;width: 200px;height: 156px;\">";
    // 甲的公司用章
    public static String HTML_JIA_SEAL = "<img id=\"jia\" src=\"https://evcard.oss-cn-shanghai.aliyuncs.com/sign.jpg\" alt=\"\" style=\"margin-bottom: -110px;width: 200px;height: 156px;\">";
    public static String HTML_SIGN2 = "<img src=\"\" alt=\"\" style=\"width: 200px;margin-bottom: -60px;\">";

    // 门店电话
    public static String SOTRE_MOBILE ="<id=\"s-m\">";
    // 门店店长电话
    public static String SOTRE_MANAGER_MOBILE ="<id=\"s-m-m\">";

    /**
     * 设备号类型 （1IMEI  2 UUID  3 IDFA）
     *
     */
    public static final List<Integer> DEVICE_CODE_TYPE = Arrays.asList(1,2,3);

    /** 头部传图的app类型 Android */
    public static final String HEADER_APPTYPE_ANDROID = "Android";

    /** 头部传图的app类型 iOS */
    public static final String HEADER_APPTYPE_IOS = "iOS";


    public static final String ENTERPRISE_DISCOUNT_EXCEEDS_QUOTA = "企业折扣收益人数已满";
    public static final String ENTERPRISE_DISCOUNT_EXPIRED = "企业折扣有效期已结束";

    public static final List<String> ENTERPRISE_DISCOUNT_EXCEEDS_QUOTA_LIST = Arrays.asList("-1003","-1005");
    public static final List<String> ENTERPRISE_DISCOUNT_EXPIRED_LIST = Arrays.asList("-1002","-1004");

    public static final BaseResponse BASERESPONSE_SUCCESS = new BaseResponse(0, "成功");


}
