package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.dto.OrgUserInfoDto;
import com.extracme.evcard.membership.core.model.TEsUser;
import org.apache.ibatis.annotations.Param;

public interface TEsUserMapper {

    int insert(TEsUser record);

    int insertSelective(TEsUser record);

    TEsUser selectByPrimaryKey(String name);

    int updateByPrimaryKeySelective(TEsUser record);

    int updateByPrimaryKey(TEsUser record);

    OrgUserInfoDto getByNameAndPass(@Param("name") String name, @Param("password") String password);

    /**
     * 查询企业用户预留手机号
     * @param name
     * @return
     */
    String getOrgPhoneByName(@Param("name") String name);

	int updateCodedPasswordByName(@Param("name") String name, @Param("password") String password);
}