<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.extracme.evcard.membership.core.dao.MmpAgencyDiscountLogMapper">
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.MmpAgencyDiscountLog">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 01 16:59:50 CST 2020.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="AGENCY_ID" jdbcType="VARCHAR" property="agencyId" />
    <result column="COOPERATE_START_TIME" jdbcType="TIMESTAMP" property="cooperateStartTime" />
    <result column="COOPERATE_END_TIME" jdbcType="TIMESTAMP" property="cooperateEndTime" />
    <result column="COOPERATE_STATUS" jdbcType="INTEGER" property="cooperateStatus" />
    <result column="DISCOUNT_RULE" jdbcType="INTEGER" property="discountRule" />
    <result column="DISCOUNT_ID" jdbcType="BIGINT" property="discountId" />
    <result column="DISCOUNT_PERSONAL_ID" jdbcType="BIGINT" property="discountPersonalId" />
    <result column="VEHICLE_NO" jdbcType="VARCHAR" property="vehicleNo" />
    <result column="misc_desc" jdbcType="VARCHAR" property="miscDesc" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_oper_id" jdbcType="BIGINT" property="createOperId" />
    <result column="create_oper_name" jdbcType="VARCHAR" property="createOperName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_oper_id" jdbcType="BIGINT" property="updateOperId" />
    <result column="update_oper_name" jdbcType="VARCHAR" property="updateOperName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 01 16:59:50 CST 2020.
    -->
    id, AGENCY_ID, COOPERATE_START_TIME, COOPERATE_END_TIME, COOPERATE_STATUS, DISCOUNT_RULE,
    DISCOUNT_ID, DISCOUNT_PERSONAL_ID, VEHICLE_NO, misc_desc, status, create_time, create_oper_id,
    create_oper_name, update_time, update_oper_id, update_oper_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 01 16:59:50 CST 2020.
    -->
    select
    <include refid="Base_Column_List" />
    from mmp_agency_discount_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 01 16:59:50 CST 2020.
    -->
    delete from mmp_agency_discount_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.membership.core.model.MmpAgencyDiscountLog">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 01 16:59:50 CST 2020.
    -->
    insert into mmp_agency_discount_log (id, AGENCY_ID, COOPERATE_START_TIME,
    COOPERATE_END_TIME, COOPERATE_STATUS, DISCOUNT_RULE,
    DISCOUNT_ID, DISCOUNT_PERSONAL_ID, VEHICLE_NO,
    misc_desc, status, create_time,
    create_oper_id, create_oper_name, update_time,
    update_oper_id, update_oper_name)
    values (#{id,jdbcType=BIGINT}, #{agencyId,jdbcType=VARCHAR}, #{cooperateStartTime,jdbcType=TIMESTAMP},
    #{cooperateEndTime,jdbcType=TIMESTAMP}, #{cooperateStatus,jdbcType=INTEGER}, #{discountRule,jdbcType=INTEGER},
    #{discountId,jdbcType=BIGINT}, #{discountPersonalId,jdbcType=BIGINT}, #{vehicleNo,jdbcType=VARCHAR},
    #{miscDesc,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP},
    #{createOperId,jdbcType=BIGINT}, #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
    #{updateOperId,jdbcType=BIGINT}, #{updateOperName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.core.model.MmpAgencyDiscountLog">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 01 16:59:50 CST 2020.
    -->
    insert into mmp_agency_discount_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="agencyId != null">
        AGENCY_ID,
      </if>
      <if test="cooperateStartTime != null">
        COOPERATE_START_TIME,
      </if>
      <if test="cooperateEndTime != null">
        COOPERATE_END_TIME,
      </if>
      <if test="cooperateStatus != null">
        COOPERATE_STATUS,
      </if>
      <if test="discountRule != null">
        DISCOUNT_RULE,
      </if>
      <if test="discountId != null">
        DISCOUNT_ID,
      </if>
      <if test="discountPersonalId != null">
        DISCOUNT_PERSONAL_ID,
      </if>
      <if test="vehicleNo != null">
        VEHICLE_NO,
      </if>
      <if test="miscDesc != null">
        misc_desc,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createOperId != null">
        create_oper_id,
      </if>
      <if test="createOperName != null">
        create_oper_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateOperId != null">
        update_oper_id,
      </if>
      <if test="updateOperName != null">
        update_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="agencyId != null">
        #{agencyId,jdbcType=VARCHAR},
      </if>
      <if test="cooperateStartTime != null">
        #{cooperateStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cooperateEndTime != null">
        #{cooperateEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cooperateStatus != null">
        #{cooperateStatus,jdbcType=INTEGER},
      </if>
      <if test="discountRule != null">
        #{discountRule,jdbcType=INTEGER},
      </if>
      <if test="discountId != null">
        #{discountId,jdbcType=BIGINT},
      </if>
      <if test="discountPersonalId != null">
        #{discountPersonalId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null">
        #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="miscDesc != null">
        #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        #{updateOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.core.model.MmpAgencyDiscountLog">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 01 16:59:50 CST 2020.
    -->
    update mmp_agency_discount_log
    <set>
      <if test="agencyId != null">
        AGENCY_ID = #{agencyId,jdbcType=VARCHAR},
      </if>
      <if test="cooperateStartTime != null">
        COOPERATE_START_TIME = #{cooperateStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cooperateEndTime != null">
        COOPERATE_END_TIME = #{cooperateEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cooperateStatus != null">
        COOPERATE_STATUS = #{cooperateStatus,jdbcType=INTEGER},
      </if>
      <if test="discountRule != null">
        DISCOUNT_RULE = #{discountRule,jdbcType=INTEGER},
      </if>
      <if test="discountId != null">
        DISCOUNT_ID = #{discountId,jdbcType=BIGINT},
      </if>
      <if test="discountPersonalId != null">
        DISCOUNT_PERSONAL_ID = #{discountPersonalId,jdbcType=BIGINT},
      </if>
      <if test="vehicleNo != null">
        VEHICLE_NO = #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="miscDesc != null">
        misc_desc = #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.core.model.MmpAgencyDiscountLog">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Apr 01 16:59:50 CST 2020.
    -->
    update mmp_agency_discount_log
    set AGENCY_ID = #{agencyId,jdbcType=VARCHAR},
    COOPERATE_START_TIME = #{cooperateStartTime,jdbcType=TIMESTAMP},
    COOPERATE_END_TIME = #{cooperateEndTime,jdbcType=TIMESTAMP},
    COOPERATE_STATUS = #{cooperateStatus,jdbcType=INTEGER},
    DISCOUNT_RULE = #{discountRule,jdbcType=INTEGER},
    DISCOUNT_ID = #{discountId,jdbcType=BIGINT},
    DISCOUNT_PERSONAL_ID = #{discountPersonalId,jdbcType=BIGINT},
    VEHICLE_NO = #{vehicleNo,jdbcType=VARCHAR},
    misc_desc = #{miscDesc,jdbcType=VARCHAR},
    status = #{status,jdbcType=INTEGER},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    create_oper_id = #{createOperId,jdbcType=BIGINT},
    create_oper_name = #{createOperName,jdbcType=VARCHAR},
    update_time = #{updateTime,jdbcType=TIMESTAMP},
    update_oper_id = #{updateOperId,jdbcType=BIGINT},
    update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectLatestByTime" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from ${issSchema}.mmp_agency_discount_log
    where agency_id = #{agencyId}
    and <![CDATA[create_time <= #{time}]]>
    ORDER BY create_time DESC
    LIMIT 1
  </select>

</mapper>