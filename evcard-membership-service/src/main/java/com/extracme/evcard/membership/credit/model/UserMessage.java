package com.extracme.evcard.membership.credit.model;

import java.math.BigDecimal;

/**
 * 模型类，，对应表user_message.
 */
public class UserMessage {
    /** 用户消息SEQ(SEQ:MESSAGE_SEQ) */
    private Long messageSeq;
    /** 证件号 */
    private String authId;
    /** 会员类型(0:外部会员 1：内部员工） */
    private BigDecimal membershipType;
    /** 消息内容 */
    private String messageContent;
    /** 消息标题 */
    private String messageTitle;
    /** 消息类型 1：网点消息 2：订单消息 3 : 系统消息 */
    private BigDecimal type;
    /** 发送类型:(0:短信 app 1:短信 2:app) */
    private BigDecimal sendType;
    /** 广告URL */
    private String url;
    /** 来源 */
    private String infoOrigin;
    /** 创建用户 */
    private String createdUser;
    /** 创建时间 */
    private String createdTime;
    /** 更新用户 */
    private String updatedUser;
    /** 更新时间 */
    private String updatedTime;

    /** 用户消息SEQ(SEQ:MESSAGE_SEQ) */
    public Long getMessageSeq() {
        return messageSeq;
    }

    /** 用户消息SEQ(SEQ:MESSAGE_SEQ) */
    public void setMessageSeq(Long messageSeq) {
        this.messageSeq = messageSeq;
    }

    /** 证件号 */
    public String getAuthId() {
        return authId;
    }

    /** 证件号 */
    public void setAuthId(String authId) {
        this.authId = authId;
    }

    /** 会员类型(0:外部会员 1：内部员工） */
    public BigDecimal getMembershipType() {
        return membershipType;
    }

    /** 会员类型(0:外部会员 1：内部员工） */
    public void setMembershipType(BigDecimal membershipType) {
        this.membershipType = membershipType;
    }

    /** 消息内容 */
    public String getMessageContent() {
        return messageContent;
    }

    /** 消息内容 */
    public void setMessageContent(String messageContent) {
        this.messageContent = messageContent;
    }

    /** 消息标题 */
    public String getMessageTitle() {
        return messageTitle;
    }

    /** 消息标题 */
    public void setMessageTitle(String messageTitle) {
        this.messageTitle = messageTitle;
    }

    /** 消息类型 1：网点消息 2：订单消息 3 : 系统消息 */
    public BigDecimal getType() {
        return type;
    }

    /** 消息类型 1：网点消息 2：订单消息 3 : 系统消息 */
    public void setType(BigDecimal type) {
        this.type = type;
    }

    /** 发送类型:(0:短信 app 1:短信 2:app) */
    public BigDecimal getSendType() {
        return sendType;
    }

    /** 发送类型:(0:短信 app 1:短信 2:app) */
    public void setSendType(BigDecimal sendType) {
        this.sendType = sendType;
    }

    /** 广告URL */
    public String getUrl() {
        return url;
    }

    /** 广告URL */
    public void setUrl(String url) {
        this.url = url;
    }

    /** 来源 */
    public String getInfoOrigin() {
        return infoOrigin;
    }

    /** 来源 */
    public void setInfoOrigin(String infoOrigin) {
        this.infoOrigin = infoOrigin;
    }

    /** 创建用户 */
    public String getCreatedUser() {
        return createdUser;
    }

    /** 创建用户 */
    public void setCreatedUser(String createdUser) {
        this.createdUser = createdUser;
    }

    /** 创建时间 */
    public String getCreatedTime() {
        return createdTime;
    }

    /** 创建时间 */
    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    /** 更新用户 */
    public String getUpdatedUser() {
        return updatedUser;
    }

    /** 更新用户 */
    public void setUpdatedUser(String updatedUser) {
        this.updatedUser = updatedUser;
    }

    /** 更新时间 */
    public String getUpdatedTime() {
        return updatedTime;
    }

    /** 更新时间 */
    public void setUpdatedTime(String updatedTime) {
        this.updatedTime = updatedTime;
    }

}