package com.extracme.evcard.membership.core.enums;

public enum AccountStatusEnum {
    /**
     * 账号状态Normal
     */
    NORMAL(0, "账号正常"),

    /**
     * 账号注销冻结期内
     */
    FREEZED(1, "账号已注销，在冻结期内"),

    /**
     * 账号注销冻结期外
     */
    UNREGISTERED(2, "账号已注销，在冻结期外");

    AccountStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    /** 异常描述信息 */
    private Integer code;

    /** 描述 */
    private String msg;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
