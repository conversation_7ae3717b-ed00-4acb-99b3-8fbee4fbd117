package com.extracme.evcard.membership.core.service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import com.extracme.evcard.membership.core.dao.AppKeyManagerMapper;
import com.extracme.evcard.membership.core.dao.MmpPlatformInfoMapper;
import com.extracme.evcard.membership.core.dto.AppKeyDto;
import com.extracme.evcard.membership.core.dto.PlatformInfoDTO;
import com.extracme.evcard.membership.core.model.AppKeyManager;
import com.extracme.evcard.membership.core.model.EntityCache;
import com.extracme.evcard.rpc.util.BeanCopyUtils;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;

@Service("platformService")
public class PlatformServiceImpl implements IPlatformService {
	
	@Resource
	MmpPlatformInfoMapper MmpPlatformInfoMapper;

	@Resource
	AppKeyManagerMapper appKeyManagerMapper;
	
	Cache<String, AppKeyDto> appKeyCache = CacheBuilder.newBuilder().maximumSize(10000).expireAfterWrite(30,TimeUnit.MINUTES).build();
	Cache<Long, PlatformInfoDTO> platformCache = CacheBuilder.newBuilder().maximumSize(100).expireAfterWrite(30,TimeUnit.MINUTES).build();

	@Override
	public List<PlatformInfoDTO> getAllPlatforms() {
		List<PlatformInfoDTO> platformInfoDtos = MmpPlatformInfoMapper.queryAllPlatform();
		for(PlatformInfoDTO platformInfoDto:platformInfoDtos) {
			platformCache.put(platformInfoDto.getId(), platformInfoDto);
		}
		return platformInfoDtos;
	}
	
	public PlatformInfoDTO getPlatform(Long platformId) {
		PlatformInfoDTO dto = platformCache.getIfPresent(platformId);
		if(dto != null) {
			return dto;
		}
		//重新初始换缓存
		this.getAllPlatforms();
		return platformCache.getIfPresent(platformId);
	}

	@Override
	public List<String> getAppKeysByPlatformId(Long platformId, Boolean filterUnabled) {
		Integer status = null ;
		if(filterUnabled != null && filterUnabled) {
			status = 0;
		}
		List<String> list = MmpPlatformInfoMapper.getAppKeysByPlatformId(platformId, status);
		return list;
	}

	@Override
	public List<AppKeyDto> getAppKeys(Long platformId, String channelPurpose, Boolean filterUnabled) {
		Integer status = null ;
		if(filterUnabled != null && filterUnabled) {
			status = 0;
		}
		List<AppKeyDto> list = appKeyManagerMapper.getAppKeys(platformId, channelPurpose, status);
		for(AppKeyDto dto:list) {
			PlatformInfoDTO platform = this.getPlatform(dto.getPlatformId());
			if(platform != null) {
				dto.setPlatformName(platform.getPlatformName());
			}
			appKeyCache.put(dto.getAppKey(), dto);
		}
		//获取
		return list;
	}

	@Override
	public AppKeyDto getAppKey(String appKey) {
		 AppKeyDto appKeyDto = appKeyCache.getIfPresent(appKey); 
		 if(appKeyDto != null){ 
			 return appKeyDto; 
		 }
		AppKeyManager appKeyManager = appKeyManagerMapper.selectByPrimaryKey(appKey);
		if(appKeyManager != null) {
			appKeyDto = new AppKeyDto();
			BeanCopyUtils.copyProperties(appKeyManager, appKeyDto);
			PlatformInfoDTO platform = this.getPlatform(appKeyDto.getPlatformId());
			if(platform != null) {
				appKeyDto.setPlatformName(platform.getPlatformName());
			}
			appKeyCache.put(appKey, appKeyDto);
			return appKeyDto;
		}
		return null;
	}

	@Override
	public List<AppKeyDto> getEffectiveAppKeyInfo() {
		List<AppKeyDto> list = new ArrayList<>();
		List<AppKeyManager> appKeyManagerList = appKeyManagerMapper.getEffectiveAppKeyInfo();
		if(CollectionUtils.isNotEmpty(appKeyManagerList)) {
			for (AppKeyManager appKeyManager : appKeyManagerList) {
				AppKeyDto appKeyDto = new AppKeyDto();
				BeanCopyUtils.copyProperties(appKeyManager, appKeyDto);
				PlatformInfoDTO platform = this.getPlatform(appKeyDto.getPlatformId());
				if(platform != null) {
					appKeyDto.setPlatformName(platform.getPlatformName());
				}
				appKeyCache.put(appKeyManager.getAppKey(), appKeyDto);
				list.add(appKeyDto);
			}
		}
		return list;
	}
}
