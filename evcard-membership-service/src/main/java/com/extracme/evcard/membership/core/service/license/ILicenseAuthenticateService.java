package com.extracme.evcard.membership.core.service.license;

import com.extracme.evcard.membership.core.dto.DriverLicenseElementsAuthenticateLogDTO;
import com.extracme.evcard.membership.core.dto.DriverLicenseQueryResultDTO;
import com.extracme.evcard.membership.core.dto.DriverLicenseValidResultDto;
import com.extracme.evcard.membership.core.input.SaveDriverElementsAuthenticateLogInput;

/**
 * <AUTHOR>
 * @Discription
 * @date 2019/12/16
 */
public interface ILicenseAuthenticateService {
    /**
     * 驾照三要素认证
     * @param driverCode
     * @param name
     * @param fileNo
     * @return
     */
    SaveDriverElementsAuthenticateLogInput authenticate(String driverCode, String name, String fileNo, Integer readTimeOut);

    /**
     * 查询驾照扣分情况
     * @param driverCode
     * @param name
     * @param fileNo
     * @return
     */
    SaveDriverElementsAuthenticateLogInput queryDriverDeduction(String driverCode, String name, String fileNo);

    /**
     * 根据驾照code和姓名获取驾照信息
     * @param driverCode 必须
     * @param name 必须
     * @return
     */
    DriverLicenseQueryResultDTO queryDriverLicenseInfo(String driverCode, String name);

    /**
     * 三要素认证详情解析
     * @param logDTO
     * @return
     */
    String buildAuthResultDetail(DriverLicenseElementsAuthenticateLogDTO logDTO);
    
    /**
     * 检验驾照真实性和有效性
    * @Title: checkDriverLicenseValid  
    * @Description: 检验驾照真实性和有效性  
    * @param @param name 姓名
    * @param @param cardNo 驾驶证号
    * @param @param archviesNo 档案编号
    * @param @return    参数  
    * @return DriverLicenseValidResultDto    返回类型  
    * @throws
     */
    DriverLicenseValidResultDto checkDriverLicenseValid(String name,String cardNo,String archviesNo);
}
