<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.extracme.evcard.membership.core.dao.MmpDiscountPackageShareRuleMapper">
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.MmpDiscountPackageShareRule">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="agency_id" jdbcType="VARCHAR" property="agencyId" />
    <result column="package_type_list" jdbcType="VARCHAR" property="packageTypeList" />
    <result column="share_type" jdbcType="INTEGER" property="shareType" />
    <result column="misc_desc" jdbcType="VARCHAR" property="miscDesc" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_oper_id" jdbcType="BIGINT" property="createOperId" />
    <result column="create_oper_name" jdbcType="VARCHAR" property="createOperName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_oper_id" jdbcType="BIGINT" property="updateOperId" />
    <result column="update_oper_name" jdbcType="VARCHAR" property="updateOperName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, agency_id, package_type_list, share_type, misc_desc, status, create_time, create_oper_id, 
    create_oper_name, update_time, update_oper_id, update_oper_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_discount_package_share_rule
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ${issSchema}.mmp_discount_package_share_rule
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.membership.core.model.MmpDiscountPackageShareRule">
    insert into ${issSchema}.mmp_discount_package_share_rule (id, agency_id, package_type_list, 
      share_type, misc_desc, status, 
      create_time, create_oper_id, create_oper_name, 
      update_time, update_oper_id, update_oper_name
      )
    values (#{id,jdbcType=BIGINT}, #{agencyId,jdbcType=VARCHAR}, #{packageTypeList,jdbcType=VARCHAR}, 
      #{shareType,jdbcType=INTEGER}, #{miscDesc,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, #{createOperName,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, #{updateOperName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.core.model.MmpDiscountPackageShareRule" keyProperty="id" useGeneratedKeys="true">
    insert into ${issSchema}.mmp_discount_package_share_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="agencyId != null">
        agency_id,
      </if>
      <if test="packageTypeList != null">
        package_type_list,
      </if>
      <if test="shareType != null">
        share_type,
      </if>
      <if test="miscDesc != null">
        misc_desc,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createOperId != null">
        create_oper_id,
      </if>
      <if test="createOperName != null">
        create_oper_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateOperId != null">
        update_oper_id,
      </if>
      <if test="updateOperName != null">
        update_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="agencyId != null">
        #{agencyId,jdbcType=VARCHAR},
      </if>
      <if test="packageTypeList != null">
        #{packageTypeList,jdbcType=VARCHAR},
      </if>
      <if test="shareType != null">
        #{shareType,jdbcType=INTEGER},
      </if>
      <if test="miscDesc != null">
        #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        #{updateOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.core.model.MmpDiscountPackageShareRule">
    update ${issSchema}.mmp_discount_package_share_rule
    <set>
      <if test="agencyId != null">
        agency_id = #{agencyId,jdbcType=VARCHAR},
      </if>
      <if test="packageTypeList != null">
        package_type_list = #{packageTypeList,jdbcType=VARCHAR},
      </if>
      <if test="shareType != null">
        share_type = #{shareType,jdbcType=INTEGER},
      </if>
      <if test="miscDesc != null">
        misc_desc = #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.core.model.MmpDiscountPackageShareRule">
    update ${issSchema}.mmp_discount_package_share_rule
    set agency_id = #{agencyId,jdbcType=VARCHAR},
      package_type_list = #{packageTypeList,jdbcType=VARCHAR},
      share_type = #{shareType,jdbcType=INTEGER},
      misc_desc = #{miscDesc,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectLatestByTime" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from ${issSchema}.mmp_discount_package_share_rule
    where agency_id = #{agencyId}
    and <![CDATA[create_time <= #{time}]]>
    ORDER BY create_time DESC
    LIMIT 1
  </select>

  <update id="updateByLatestTimeLapse">
    update ${issSchema}.mmp_discount_package_share_rule a set a.status = 0 where a.id = #{id}
  </update>
</mapper>