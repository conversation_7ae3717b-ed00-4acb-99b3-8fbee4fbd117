package com.extracme.evcard.membership.credit.dao;

import com.extracme.evcard.membership.credit.dto.CreditEventAppealHandleDto;
import com.extracme.evcard.membership.credit.dto.CreditEventAppealRecordDetailDto;
import com.extracme.evcard.membership.credit.dto.CreditEventAppealRecordPageDto;
import com.extracme.evcard.membership.credit.dto.CreditEventAppealRecordParamsDto;
import com.extracme.evcard.membership.credit.model.MmpCreditEventAppealRecord;
import com.extracme.evcard.rpc.dto.Page;

import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MmpCreditEventAppealRecordMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_credit_event_appeal_record
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_credit_event_appeal_record
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    int insert(MmpCreditEventAppealRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_credit_event_appeal_record
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    int insertSelective(MmpCreditEventAppealRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_credit_event_appeal_record
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    MmpCreditEventAppealRecord selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_credit_event_appeal_record
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    int updateByPrimaryKeySelective(MmpCreditEventAppealRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_credit_event_appeal_record
     *
     * @mbggenerated Thu Nov 23 13:31:43 CST 2017
     */
    int updateByPrimaryKey(MmpCreditEventAppealRecord record);

    CreditEventAppealRecordDetailDto getCreditEventAppealDetailRecordByAuthIdAndEventId(@Param("authId") String authId,
                                                                                        @Param("eventId") Long eventId);

    Integer getCreditEventAppealRecordCount(CreditEventAppealRecordParamsDto paramsDto);

    List<CreditEventAppealRecordPageDto> getCreditEventAppealRecordPages(
            @Param("paramsDto") CreditEventAppealRecordParamsDto paramsDto, @Param("page") Page page);

    MmpCreditEventAppealRecord getCreditEventAppealRecordById(@Param("appealId") Long appealId,
                                                              @Param("authId") String authId);

    int updateAppealEventHandleStatus(CreditEventAppealHandleDto creditEventAppealHandleDto);

    MmpCreditEventAppealRecord getCreditEventAppealRecordByAuthIdAndEventId(@Param("authId") String authId,
                                                                            @Param("eventId") Long eventId);

    int saveNewCreditEventAppealRecord(MmpCreditEventAppealRecord appealRecord);
}