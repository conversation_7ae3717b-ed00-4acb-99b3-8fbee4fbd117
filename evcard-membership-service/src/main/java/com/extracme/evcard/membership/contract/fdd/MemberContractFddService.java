package com.extracme.evcard.membership.contract.fdd;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.common.ImgUtils;
import com.extracme.evcard.membership.common.UploadImgUtil;
import com.extracme.evcard.membership.config.CommConfig;
import com.extracme.evcard.membership.contract.service.*;
import com.extracme.evcard.membership.core.dto.ContractVersionDto;
import com.extracme.evcard.membership.core.dto.UserContractInfo;
import com.extracme.evcard.membership.core.dto.UserContractKeyDto;
import com.extracme.evcard.membership.core.exception.Assert;
import com.extracme.evcard.membership.core.exception.MemberException;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.fadada.sdk.client.FddClientBase;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.InputStream;
import java.util.*;


@Slf4j
@Component("fddContractService")
public class MemberContractFddService implements IMemberContractSignProvider {

    @Resource
    private MembershipInfoMapper membershipInfoMapper;

    @Autowired
    private CommConfig commConfig;

    private FddSignClientWrap client;

    @PostConstruct
    public void initComponents() {
        client = new FddSignClientWrap(commConfig.getFadadaAppId(), commConfig.getFadadaAppSecret(),
                commConfig.getFadadaVersion(), commConfig.getFadadaApiUrl());
    }

    private ContractSupplier supplier;

    @Override
    public UpdateTemplateResponse uploadTemplate(String templateKey, String docUrl) {
        UpdateTemplateResponse response = new UpdateTemplateResponse();
        response.setSupplier(supplier);

        /**
         * 调用法大大客户端上传文件
         * 法大大供应商直接使用业务端的模板关键字作为模板id
         */
        String templateId = templateKey;
        //FddClientBase clientbase = new FddClientBase(fadada_app_id, fadada_app_secret, fadada_version, fadada_api_url);
        String result = client.invokeUploadTemplate(templateId, null, docUrl);

        log.debug("调用法大大接口invokeUploadTemplate返回：" + response);
        Map<String, Object> resultMap = (Map<String, Object>) JSONObject.parse(result);
        if (null != resultMap && resultMap.size() > 0) {
            String code = (String) resultMap.get("code");
            String msg = (String) resultMap.get("msg");
            if (!"1".equals(code)) {
                log.error("----调用法大大invokeUploadTemplate接口失败----" + msg + "，templateId：" + templateId);
                return new UpdateTemplateResponse(-1, msg);
            }
        }
        response.setTemplateId(templateId);
        return response;
    }


    /**
     * 法大大原签署逻辑
     * 存在姓名变更漏洞
     * @param input
     * @return
     * @throws MemberException
     */
    @Override
    public ContractSignOutput autoSignContract(ContractSignInput input) throws MemberException {
        log.info("法大大：合同自动签署开始，input={}", JSON.toJSONString(input));
        ContractSignOutput output = new ContractSignOutput();
        String authId = input.getAuthId();
        String customerId = input.getCustomerId();
        if (StringUtils.isBlank(customerId)) {
            //先调用法大大个人CA申请
            try {
                String identType = ComUtil.checkIDCard(input.getCertNo()) ? "0" : "1";
                String response = client.invokeSyncPersonAuto(input.getName(), "", input.getCertNo(), identType, input.getMobilePhone());
                log.info("法大大个人CA申请接口, authId={}, 返回={}", input.getAuthId(), response);
                JSONObject jsonObject = JSONObject.parseObject(response);
                Assert.equals(jsonObject.getString("code"), "1000", "个人CA申请失败");
                customerId = jsonObject.getString("customer_id");
                //保存customId
                membershipInfoMapper.saveCustomerId(input.getAuthId(), customerId);
            } catch (Exception e) {
                log.error("自动签署合同异常， authId=" + authId, e);
                throw new MemberException(-1, "个人CA申请失败");
            }
        }
        List<ContractSignResult> results = new ArrayList<>();
        List<ContractVersionDto> contractVersionDtoList = input.getContractVersionDtoList();
        if (CollectionUtils.isNotEmpty(contractVersionDtoList)) {
            for (ContractVersionDto p : contractVersionDtoList) {
                //模板编号
                String templateId = p.getTemplateId();
                //合同标题
                String docTitle = p.getDocTitle();
                if (StringUtils.isBlank(authId) || StringUtils.isBlank(templateId) || StringUtils.isBlank(docTitle)) {
                    throw new MemberException(-1, "请检查参数完整性");
                }
                //检查当前版本是否已经签署过
                if (membershipInfoMapper.countContract(authId, customerId, templateId) == 0) {
                    try {
                        ContractSignResult signResult = new ContractSignResult();
                        //通过模板先生成合同
                        JSONObject templateParam = new JSONObject();
                        templateParam.put("evcard", "");
                        String contractId = UUID.randomUUID().toString().replace("-", "");
                        String response = client.invokeGenerateContract(templateId, contractId, p.getDocTitle(), null, null, templateParam.toJSONString(), null);
                        log.info("法大大生成合同接口, authId={}, 返回={}", authId, response);
                        JSONObject jsonObject = JSONObject.parseObject(response);
                        Assert.equals(jsonObject.getString("code"), "1000", "生成合同失败");
                        //自动签署合同
                        JSONObject searchLocation = new JSONObject();
                        searchLocation.put("pagenum", 0);
                        searchLocation.put("x", 420);
                        searchLocation.put("y", 180);
                        JSONArray signaturePositions = new JSONArray();
                        signaturePositions.add(searchLocation);
                        String transactionId = UUID.randomUUID().toString().replace("-", "");
                        String response1;
                        try {
                            response1 = client.invokeExtSignAutoXY(transactionId, customerId, "1", contractId, p.getDocTitle(), signaturePositions.toJSONString(), null);
                        }catch (Exception e){
                            //超时异常重试一次
                            response1 = client.invokeExtSignAutoXY(transactionId, customerId, "1", contractId, p.getDocTitle(), signaturePositions.toJSONString(), null);
                        }
                        log.info("法大大生自动签署合同接口, authId={}, 返回={}", authId, response1);
                        JSONObject jsonObject1 = JSONObject.parseObject(response1);
                        Assert.equals(jsonObject1.getString("code"), "1000", "自动签署合同失败");
                        //合同归档
                        try {
                            String response2 = client.invokeContractFilling(contractId);
                            log.info("法大大生归档合同接口, authId={}, 返回={}", authId, response2);
                        }catch (Exception e){
                            log.warn("法大大生归档合同接口调用失败, authId=" + authId + "，contractId="+contractId, e);
                            //超时异常重试一次
                            try {
                                String response2 = client.invokeContractFilling(contractId);
                                log.info("法大大生归档合同接口, authId={}, 返回={}", response2);
                            }catch (Exception e1){
                                log.warn("法大大生归档合同接口, 调用失败,authId=" + authId + "contractId="+contractId, e1);
                            }
                        }
                        signResult.setCustomerId(customerId);
                        signResult.setTemplateId(templateId);
                        signResult.setVersionId(p.getVersionId());
                        signResult.setViewUrl(jsonObject1.getString("viewpdf_url"));
                        signResult.setDownloadUrl(jsonObject1.getString("download_url"));
                        signResult.setSigned(1);
                        signResult.setContractId(contractId);
                        signResult.setTransactionId(transactionId);
                        results.add(signResult);
                        log.info("authid=" + authId + " 签署法大大合同成功");
                    } catch (Exception e) {
                        log.error("自动签署合同失败，authId=" + authId, e);
                        throw new MemberException(-1, "签署合同失败");
                    }
                }
            }
        }
        output.setSignResults(results);
        return output;
    }

    @Override
    public Map<String, String> batchDownloadContract(Map<String, UserContractKeyDto> contractMap) {
        Map<String, String> outMap = new HashMap<>();
        try{
            Map<String, String> idMap = new HashMap<>();
            for (String key : contractMap.keySet()) {
                idMap.put(key, contractMap.get(key).getContractId());
            }
            InputStream inputStream = client.batchDownloadContracts(idMap);
            if(inputStream != null) {
                /**
                 * 解压zip文件
                 */
                Map<String, byte[]> fileMap = ImgUtils.readZipByInputStream(inputStream);
                if(fileMap != null) {
                    for(String fileName : fileMap.keySet()) {
                        if(!StringUtils.endsWithIgnoreCase(fileName,".pdf")){
                            continue;
                        }
                        //上传合同文件到oss
                        try {
                            byte[] data = fileMap.get(fileName);
                            String contractId = StringUtils.removeEnd(fileName,".pdf");
                            UserContractKeyDto contract = contractMap.get(contractId);
                            if(contract == null) {
                                log.warn("batchArchive:归档,无对应签约记录信息, contractId={}, fileName={}", contractId, fileName);
                            }
                            String objectName = uploadUserContractFile(contract.getAuthId(), 0, contractId, data);
                            outMap.put(contractId, objectName);
                        }catch (Exception e) {
                            log.error("batchArchive:归档法大大合同失败，contractId=" + fileName, e);
                        }
                    }
                }
            }
        }catch (Exception ex) {
            log.error("归档法大大合同失败", ex);
        }
        return outMap;
    }

    /**
     * 上传协议文件
     * @param contractId
     * @param byteArray
     * @return
     * @throws BusinessException
     */
    public String uploadUserContractFile(String authId, Integer supplier, String contractId, byte[] byteArray) throws BusinessException {
        String fileName = authId + "/" + supplier + "_" + contractId;
        String objectName = "sensitiveBucket/userContract/" +  fileName + ".pdf";
        UploadImgUtil.uploadByteArray(byteArray, objectName, UploadImgUtil.OSS_SENSITIVE_BUCKET);
        return objectName;
    }

    @Override
    public UserContractInfo getContractUrl(UserContractInfo contract) {
        if(StringUtils.isBlank(contract.getArchiveUrl())) {
            contract.setArchiveUrl(contract.getDownloadUrl());
        }
        return contract;
    }
}
