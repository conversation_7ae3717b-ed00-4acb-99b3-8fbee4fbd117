package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.dto.input.MemberAuthorizedRecordQueryInput;
import com.extracme.evcard.membership.core.model.MmpUserAuthorizedLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MmpUserAuthorizedLogMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MmpUserAuthorizedLog record);

    int insertSelective(MmpUserAuthorizedLog record);

    MmpUserAuthorizedLog selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MmpUserAuthorizedLog record);

    int updateByPrimaryKey(MmpUserAuthorizedLog record);

    /**
     * 会员芝麻信用授权记录分页查询
     * @param queryInput
     * @return
     */
    List<MmpUserAuthorizedLog> queryAuthorizedHistoryList(MemberAuthorizedRecordQueryInput queryInput);

    /**
     * 查询会员授权日志总数
     * @param queryInput
     * @return
     */
    Integer countUserAuthorizedHistory(MemberAuthorizedRecordQueryInput queryInput);
}