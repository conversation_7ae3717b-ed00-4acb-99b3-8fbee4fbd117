<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.core.dao.ExpressInfoMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.ExpressInfo" >
    <id column="EXPRESS_NO" property="expressNo" jdbcType="VARCHAR" />
    <result column="MOBILE_PHONE" property="mobilePhone" jdbcType="VARCHAR" />
    <result column="EXPRESS_NAME" property="expressName" jdbcType="VARCHAR" />
    <result column="SUPPLEMENTS_FLAG" property="supplementsFlag" jdbcType="INTEGER" />
    <result column="EXPIRE_FLAG" property="expireFlag" jdbcType="INTEGER" />
    <result column="EXPRESS_ADDRESS" property="expressAddress" jdbcType="VARCHAR" />
    <result column="CREATED_TIME" property="createdTime" jdbcType="VARCHAR" />
    <result column="CREATED_USER" property="createdUser" jdbcType="VARCHAR" />
    <result column="AUTH_ID" property="authId" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    EXPRESS_NO, MOBILE_PHONE, EXPRESS_NAME, SUPPLEMENTS_FLAG, EXPIRE_FLAG, EXPRESS_ADDRESS, 
    CREATED_TIME, CREATED_USER, AUTH_ID
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from ${siacSchema}.express_info
    where EXPRESS_NO = #{expressNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from ${siacSchema}.express_info
    where EXPRESS_NO = #{expressNo,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.membership.core.model.ExpressInfo" >
    insert into ${siacSchema}.express_info (EXPRESS_NO, MOBILE_PHONE, EXPRESS_NAME,
      SUPPLEMENTS_FLAG, EXPIRE_FLAG, EXPRESS_ADDRESS, 
      CREATED_TIME, CREATED_USER, AUTH_ID
      )
    values (#{expressNo,jdbcType=VARCHAR}, #{mobilePhone,jdbcType=VARCHAR}, #{expressName,jdbcType=VARCHAR}, 
      #{supplementsFlag,jdbcType=INTEGER}, #{expireFlag,jdbcType=INTEGER}, #{expressAddress,jdbcType=VARCHAR}, 
      #{createdTime,jdbcType=VARCHAR}, #{createdUser,jdbcType=VARCHAR}, #{authId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.core.model.ExpressInfo" >
    insert into ${siacSchema}.express_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="expressNo != null" >
        EXPRESS_NO,
      </if>
      <if test="mobilePhone != null" >
        MOBILE_PHONE,
      </if>
      <if test="expressName != null" >
        EXPRESS_NAME,
      </if>
      <if test="supplementsFlag != null" >
        SUPPLEMENTS_FLAG,
      </if>
      <if test="expireFlag != null" >
        EXPIRE_FLAG,
      </if>
      <if test="expressAddress != null" >
        EXPRESS_ADDRESS,
      </if>
      <if test="createdTime != null" >
        CREATED_TIME,
      </if>
      <if test="createdUser != null" >
        CREATED_USER,
      </if>
      <if test="authId != null" >
        AUTH_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="expressNo != null" >
        #{expressNo,jdbcType=VARCHAR},
      </if>
      <if test="mobilePhone != null" >
        #{mobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="expressName != null" >
        #{expressName,jdbcType=VARCHAR},
      </if>
      <if test="supplementsFlag != null" >
        #{supplementsFlag,jdbcType=INTEGER},
      </if>
      <if test="expireFlag != null" >
        #{expireFlag,jdbcType=INTEGER},
      </if>
      <if test="expressAddress != null" >
        #{expressAddress,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=VARCHAR},
      </if>
      <if test="createdUser != null" >
        #{createdUser,jdbcType=VARCHAR},
      </if>
      <if test="authId != null" >
        #{authId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.core.model.ExpressInfo" >
    update ${siacSchema}.express_info
    <set >
      <if test="mobilePhone != null" >
        MOBILE_PHONE = #{mobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="expressName != null" >
        EXPRESS_NAME = #{expressName,jdbcType=VARCHAR},
      </if>
      <if test="supplementsFlag != null" >
        SUPPLEMENTS_FLAG = #{supplementsFlag,jdbcType=INTEGER},
      </if>
      <if test="expireFlag != null" >
        EXPIRE_FLAG = #{expireFlag,jdbcType=INTEGER},
      </if>
      <if test="expressAddress != null" >
        EXPRESS_ADDRESS = #{expressAddress,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        CREATED_TIME = #{createdTime,jdbcType=VARCHAR},
      </if>
      <if test="createdUser != null" >
        CREATED_USER = #{createdUser,jdbcType=VARCHAR},
      </if>
      <if test="authId != null" >
        AUTH_ID = #{authId,jdbcType=VARCHAR},
      </if>
    </set>
    where EXPRESS_NO = #{expressNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.core.model.ExpressInfo" >
    update ${siacSchema}.express_info
    set MOBILE_PHONE = #{mobilePhone,jdbcType=VARCHAR},
      EXPRESS_NAME = #{expressName,jdbcType=VARCHAR},
      SUPPLEMENTS_FLAG = #{supplementsFlag,jdbcType=INTEGER},
      EXPIRE_FLAG = #{expireFlag,jdbcType=INTEGER},
      EXPRESS_ADDRESS = #{expressAddress,jdbcType=VARCHAR},
      CREATED_TIME = #{createdTime,jdbcType=VARCHAR},
      CREATED_USER = #{createdUser,jdbcType=VARCHAR},
      AUTH_ID = #{authId,jdbcType=VARCHAR}
    where EXPRESS_NO = #{expressNo,jdbcType=VARCHAR}
  </update>

  <select id="selectByAuthId" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from ${siacSchema}.express_info
    where   AUTH_ID = #{authId,jdbcType=VARCHAR}
  </select>
</mapper>