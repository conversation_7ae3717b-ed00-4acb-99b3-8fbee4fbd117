<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.core.dao.ReportFeedbackInfoMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.ReportFeedbackInfo" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="auth_id" property="authId" jdbcType="VARCHAR" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="mobile_phone" property="mobilePhone" jdbcType="VARCHAR" />
    <result column="report_type" property="reportType" jdbcType="INTEGER" />
    <result column="shop_address_name" property="shopAddressName" jdbcType="VARCHAR" />
    <result column="shop_address_desc" property="shopAddressDesc" jdbcType="VARCHAR" />
    <result column="feedback_desc" property="feedbackDesc" jdbcType="VARCHAR" />
    <result column="image_urls" property="imageUrls" jdbcType="VARCHAR" />
    <result column="vehicle_no" property="vehicleNo" jdbcType="VARCHAR" />
    <result column="shop_seq" property="shopSeq" jdbcType="INTEGER" />
    <result column="shop_name" property="shopName" jdbcType="VARCHAR" />
    <result column="park_num" property="parkNum" jdbcType="VARCHAR" />
    <result column="latitude" property="latitude" jdbcType="DECIMAL" />
    <result column="longitude" property="longitude" jdbcType="DECIMAL" />
    <result column="order_seq" property="orderSeq" jdbcType="VARCHAR" />
    <result column="report_status" property="reportStatus" jdbcType="INTEGER" />
    <result column="task_id" property="taskId" jdbcType="VARCHAR" />
    <result column="info_type" property="infoType" jdbcType="INTEGER" />
    <result column="report_feedback_info_id" property="reportFeedbackInfoId" jdbcType="BIGINT" />
    <result column="reply_desc" property="replyDesc" jdbcType="VARCHAR" />
    <result column="reply_time" property="replyTime" jdbcType="TIMESTAMP" />
    <result column="is_read" property="isRead" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="misc_desc" property="miscDesc" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="INTEGER" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
    <result column="is_order_vehicle" property="isOrderVehicle" jdbcType="INTEGER"/>
  </resultMap>
  <sql id="Base_Column_List" >
    id, auth_id, name, mobile_phone,report_type, shop_address_name, shop_address_desc, feedback_desc, image_urls,
    vehicle_no, shop_seq, shop_name, park_num, latitude, longitude, order_seq, report_status, task_id,
    info_type, report_feedback_info_id, reply_desc, reply_time, is_read, status, misc_desc, 
    create_time, create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name,is_order_vehicle
  </sql>
  <insert id="insert" parameterType="com.extracme.evcard.membership.core.model.ReportFeedbackInfo" >
    insert into ${siacSchema}.report_feedback_info (id, auth_id, name,mobile_phone,report_type,
      shop_address_name, shop_address_desc, feedback_desc, 
      image_urls, vehicle_no, shop_seq, shop_name,
      park_num, latitude, longitude, 
      order_seq, report_status, task_id, 
      info_type, report_feedback_info_id, reply_desc, 
      reply_time, is_read, status, 
      misc_desc, create_time, create_oper_id, 
      create_oper_name, update_time, update_oper_id, 
      update_oper_name,is_order_vehicle)
    values (#{id,jdbcType=BIGINT}, #{authId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR},#{mobilePhone,jdbcType=VARCHAR},#{reportType,jdbcType=INTEGER},
      #{shopAddressName,jdbcType=VARCHAR}, #{shopAddressDesc,jdbcType=VARCHAR}, #{feedbackDesc,jdbcType=VARCHAR}, 
      #{imageUrls,jdbcType=VARCHAR}, #{vehicleNo,jdbcType=VARCHAR}, #{shopSeq,jdbcType=INTEGER}, #{shopName,jdbcType=VARCHAR},
      #{parkNum,jdbcType=VARCHAR}, #{latitude,jdbcType=DECIMAL}, #{longitude,jdbcType=DECIMAL}, 
      #{orderSeq,jdbcType=VARCHAR}, #{reportStatus,jdbcType=INTEGER}, #{taskId,jdbcType=VARCHAR}, 
      #{infoType,jdbcType=INTEGER}, #{reportFeedbackInfoId,jdbcType=BIGINT}, #{replyDesc,jdbcType=VARCHAR}, 
      #{replyTime,jdbcType=TIMESTAMP}, #{isRead,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, 
      #{miscDesc,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, 
      #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=INTEGER},
      #{updateOperName,jdbcType=VARCHAR},#{isOrderVehicle,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="com.extracme.evcard.membership.core.model.ReportFeedbackInfo" >
    insert into ${siacSchema}.report_feedback_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="authId != null" >
        auth_id,
      </if>
      <if test="name !=null">
        name,
      </if>
      <if test="mobilePhone !=null">
        mobile_phone,
      </if>
      <if test="reportType != null" >
        report_type,
      </if>
      <if test="shopAddressName != null" >
        shop_address_name,
      </if>
      <if test="shopAddressDesc != null" >
        shop_address_desc,
      </if>
      <if test="feedbackDesc != null" >
        feedback_desc,
      </if>
      <if test="imageUrls != null" >
        image_urls,
      </if>
      <if test="vehicleNo != null" >
        vehicle_no,
      </if>
      <if test="shopSeq != null" >
        shop_seq,
      </if>
      <if test="shopName != null" >
        shop_name,
      </if>
      <if test="parkNum != null" >
        park_num,
      </if>
      <if test="latitude != null" >
        latitude,
      </if>
      <if test="longitude != null" >
        longitude,
      </if>
      <if test="orderSeq != null" >
        order_seq,
      </if>
      <if test="reportStatus != null" >
        report_status,
      </if>
      <if test="taskId != null" >
        task_id,
      </if>
      <if test="infoType != null" >
        info_type,
      </if>
      <if test="reportFeedbackInfoId != null" >
        report_feedback_info_id,
      </if>
      <if test="replyDesc != null" >
        reply_desc,
      </if>
      <if test="replyTime != null" >
        reply_time,
      </if>
      <if test="isRead != null" >
        is_read,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="miscDesc != null" >
        misc_desc,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateOperId != null" >
        update_oper_id,
      </if>
      <if test="updateOperName != null" >
        update_oper_name,
      </if>
      <if test="isOrderVehicle != null" >
        is_order_vehicle,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="authId != null" >
        #{authId,jdbcType=VARCHAR},
      </if>
      <if test="name !=null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="mobilePhone !=null">
        #{mobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="reportType != null" >
        #{reportType,jdbcType=INTEGER},
      </if>
      <if test="shopAddressName != null" >
        #{shopAddressName,jdbcType=VARCHAR},
      </if>
      <if test="shopAddressDesc != null" >
        #{shopAddressDesc,jdbcType=VARCHAR},
      </if>
      <if test="feedbackDesc != null" >
        #{feedbackDesc,jdbcType=VARCHAR},
      </if>
      <if test="imageUrls != null" >
        #{imageUrls,jdbcType=VARCHAR},
      </if>
      <if test="vehicleNo != null" >
        #{vehicleNo,jdbcType=VARCHAR},
      </if>
      <if test="shopSeq != null" >
        #{shopSeq,jdbcType=INTEGER},
      </if>
      <if test="shopName != null" >
        #{shopName,jdbcType=VARCHAR},
      </if>
      <if test="parkNum != null" >
        #{parkNum,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null" >
        #{latitude,jdbcType=DECIMAL},
      </if>
      <if test="longitude != null" >
        #{longitude,jdbcType=DECIMAL},
      </if>
      <if test="orderSeq != null" >
        #{orderSeq,jdbcType=VARCHAR},
      </if>
      <if test="reportStatus != null" >
        #{reportStatus,jdbcType=INTEGER},
      </if>
      <if test="taskId != null" >
        #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="infoType != null" >
        #{infoType,jdbcType=INTEGER},
      </if>
      <if test="reportFeedbackInfoId != null" >
        #{reportFeedbackInfoId,jdbcType=BIGINT},
      </if>
      <if test="replyDesc != null" >
        #{replyDesc,jdbcType=VARCHAR},
      </if>
      <if test="replyTime != null" >
        #{replyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isRead != null" >
        #{isRead,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="miscDesc != null" >
        #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=INTEGER},
      </if>
      <if test="updateOperName != null" >
        #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="isOrderVehicle != null" >
        #{isOrderVehicle,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

  <select id="getFeedbackByAuthId" resultType="com.extracme.evcard.membership.core.dto.FeedbackBaseDto"
  parameterType="com.extracme.evcard.membership.core.input.FeedbackBaseInput">
    SELECT id,auth_id AS authId,report_type AS reportType,shop_address_name AS shopAddressName,shop_address_desc AS shopAddressDesc,shop_name AS shopName,
    feedback_desc AS feedbackDesc,image_urls AS imageUrls,vehicle_no AS vehicleNo,shop_seq AS shopSeq,park_num AS parkNum,order_seq AS orderSeq,
    report_status AS reportStatus,info_type AS infoType,name AS name,mobile_phone AS mobilePhone,create_time AS createTime
    FROM ${siacSchema}.report_feedback_info
    WHERE info_type =0
    <if test="queryDate != null">
      AND create_time >= #{queryDate}
    </if>
    <if test="authId != null">
      AND auth_id = #{authId}
    </if>
    <if test="name != null">
      AND name =#{name}
    </if>
    <if test="vehicleNo != null">
      AND vehicle_no = #{vehicleNo}
    </if>
    <if test="orderSeq != null">
      AND order_seq = #{orderSeq}
    </if>
    <if test="mobilePhone != null">
      AND mobile_phone = #{mobilePhone}
    </if>
    <if test="reportStatus != null">
      AND report_status = #{reportStatus}
    </if>
    <if test="startTime != null and endTime != null">
      AND create_time &gt;= #{startTime} AND create_time &lt;= #{endTime}
    </if>
    AND report_type IN
    <foreach collection="types" item="item" open="(" close=")" index="index" separator=",">
      #{item}
    </foreach>
    ORDER BY create_time DESC
  </select>

  <select id="getFeedbackPage" resultType="com.extracme.evcard.membership.core.dto.FeedbackBaseDto"
          parameterType="com.extracme.evcard.membership.core.input.QueryFeedbackInput">
    SELECT id,auth_id AS authId,report_type AS reportType,shop_address_name AS shopAddressName,shop_address_desc AS shopAddressDesc,shop_name AS shopName,
    feedback_desc AS feedbackDesc,image_urls AS imageUrls,vehicle_no AS vehicleNo,shop_seq AS shopSeq,park_num AS parkNum,order_seq AS orderSeq,
    report_status AS reportStatus,info_type AS infoType,name AS name,mobile_phone AS mobilePhone,create_time AS createTime
    FROM ${siacSchema}.report_feedback_info
    WHERE 1=1
    AND info_type =0 AND
    report_type IN
    <foreach collection="types" item="item" open="(" close=")" index="index" separator=",">
      #{item}
    </foreach>
    <if test="authId != null">
      AND auth_id = #{authId}
    </if>
    <if test="name != null">
      AND name = #{name}
    </if>
    <if test="mobilePhone != null">
      AND mobile_phone = #{mobilePhone}
    </if>
    <if test="reportStatus != null">
      AND report_status = #{reportStatus}
    </if>
    <if test="vehicleNo != null">
      AND vehicle_no = #{vehicleNo}
    </if>
    <if test="startTime != null and endTime != null">
      AND create_time &gt;= #{startTime} AND create_time &lt;= #{endTime}
    </if>
    ORDER BY create_time DESC
    LIMIT #{pageSize} OFFSET #{pageNum}
  </select>


  <update id="updateFeedbackRead" parameterType="com.extracme.evcard.membership.core.input.FeedbackReadInput">
      UPDATE ${siacSchema}.report_feedback_info
      SET is_read = 1
      WHERE auth_id = #{authId} AND is_read =0
      <if test="reportId !=null">
          AND id =#{reportId}
      </if>
  </update>

  <!-- 查询未读的反馈回复数量 -->
  <select id="queryUnReadFeedbackReplyCount" resultType="java.lang.Integer">
    SELECT
    count(*)
    FROM
    ${siacSchema}.report_feedback_info
    WHERE
    `status` = 1
    AND auth_id = #{authId}
    <if test="name != null" >
      AND `name` = #{name}
    </if>
    AND info_type = 1
    AND is_read = 0
    AND report_type = 3
  </select>

    <select id="getFeedbackReplyById" resultType="com.extracme.evcard.membership.core.dto.FeedbackReplyMess">
        SELECT reply_desc AS message,reply_time AS replyTime,update_oper_name AS operName
        FROM ${siacSchema}.report_feedback_info
        WHERE report_feedback_info_id = #{id}
    </select>

   <select id="queryFeedbackInfoById" resultType="com.extracme.evcard.membership.core.dto.FeedbackBaseDto">
     SELECT id,auth_id AS authId,report_type AS reportType,shop_address_name AS shopAddressName,shop_address_desc AS shopAddressDesc,shop_name AS shopName,
    feedback_desc AS feedbackDesc,image_urls AS imageUrls,vehicle_no AS vehicleNo,shop_seq AS shopSeq,park_num AS parkNum,order_seq AS orderSeq,
    report_status AS reportStatus,info_type AS infoType,name AS name,mobile_phone AS mobilePhone,create_time AS createTime
    FROM ${siacSchema}.report_feedback_info
    WHERE id = #{id}
   </select>

  <update id="updataUndealReport" parameterType="com.extracme.evcard.membership.core.dto.UpdataUndealReport">
    UPDATE ${siacSchema}.report_feedback_info
    SET report_status = #{reportStatus},update_time = #{updateTime} ,update_oper_name = #{updateOperName}
    WHERE id = #{id}
  </update>
</mapper>