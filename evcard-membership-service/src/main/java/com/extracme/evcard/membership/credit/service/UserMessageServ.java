package com.extracme.evcard.membership.credit.service;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.extracme.evcard.membership.credit.dao.UserMessageMapper;
import com.extracme.evcard.membership.credit.dto.UserMessageDto;

@Service
public class UserMessageServ implements IUserMessageServ {

    private static final Logger logger= LoggerFactory.getLogger(UserMessageServ.class);

    @Resource
    private UserMessageMapper userMessageMapper;

    @Override
//    @Transactional
    public Long saveUserMessage(UserMessageDto userMessageDto) {
        try{
            userMessageMapper.addUserMessage(userMessageDto);
            Long messageSeq = userMessageDto.getMessageSeq();
            return messageSeq;
        }catch (Exception e){
            logger.error("",e);
            return 0L;
        }
    }
}
