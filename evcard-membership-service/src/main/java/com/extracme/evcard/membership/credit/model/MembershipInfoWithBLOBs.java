package com.extracme.evcard.membership.credit.model;

public class MembershipInfoWithBLOBs extends MembershipInfo {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.USER_IMG_URL
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String userImgUrl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.DRIVING_LICENSE_IMG_URL
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String drivingLicenseImgUrl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.IDENTITY_CARD_IMG_URL
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String identityCardImgUrl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.MARK
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String mark;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.USER_IMG_URL
     *
     * @return the value of membership_info.USER_IMG_URL
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getUserImgUrl() {
        return userImgUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.USER_IMG_URL
     *
     * @param userImgUrl the value for membership_info.USER_IMG_URL
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setUserImgUrl(String userImgUrl) {
        this.userImgUrl = userImgUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.DRIVING_LICENSE_IMG_URL
     *
     * @return the value of membership_info.DRIVING_LICENSE_IMG_URL
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getDrivingLicenseImgUrl() {
        return drivingLicenseImgUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.DRIVING_LICENSE_IMG_URL
     *
     * @param drivingLicenseImgUrl the value for membership_info.DRIVING_LICENSE_IMG_URL
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setDrivingLicenseImgUrl(String drivingLicenseImgUrl) {
        this.drivingLicenseImgUrl = drivingLicenseImgUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.IDENTITY_CARD_IMG_URL
     *
     * @return the value of membership_info.IDENTITY_CARD_IMG_URL
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getIdentityCardImgUrl() {
        return identityCardImgUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.IDENTITY_CARD_IMG_URL
     *
     * @param identityCardImgUrl the value for membership_info.IDENTITY_CARD_IMG_URL
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setIdentityCardImgUrl(String identityCardImgUrl) {
        this.identityCardImgUrl = identityCardImgUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.MARK
     *
     * @return the value of membership_info.MARK
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getMark() {
        return mark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.MARK
     *
     * @param mark the value for membership_info.MARK
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setMark(String mark) {
        this.mark = mark;
    }
}