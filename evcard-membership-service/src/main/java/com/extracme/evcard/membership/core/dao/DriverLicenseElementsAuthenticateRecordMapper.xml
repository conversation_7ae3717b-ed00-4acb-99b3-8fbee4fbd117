<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.core.dao.DriverLicenseElementsAuthenticateRecordMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.DriverLicenseElementsAuthenticateRecord" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="user_id" property="userId" jdbcType="BIGINT" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="driver_code" property="driverCode" jdbcType="VARCHAR" />
    <result column="file_no" property="fileNo" jdbcType="VARCHAR" />
    <result column="driver_license_img_url" property="driverLicenseImgUrl" jdbcType="VARCHAR" />
    <result column="file_no_img_url" property="fileNoImgUrl" jdbcType="VARCHAR" />
    <result column="authenticate_status" property="authenticateStatus" jdbcType="INTEGER" />
    <result column="license_status" property="licenseStatus" jdbcType="INTEGER" />
    <result column="license_status_msg" property="licenseStatusMsg" jdbcType="VARCHAR" />
    <result column="elements_review_items" property="elementsReviewItems" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="misc_desc" property="miscDesc" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, user_id, name, driver_code, file_no, driver_license_img_url, file_no_img_url, authenticate_status, license_status,
    license_status_msg, elements_review_items,
    status, misc_desc, create_time, create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from ${siacSchema}.driver_license_elements_authenticate_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.extracme.evcard.membership.core.model.DriverLicenseElementsAuthenticateRecord" >
    insert into ${siacSchema}.driver_license_elements_authenticate_record (id, user_id, name,
      driver_code, file_no, driver_license_img_url, file_no_img_url, authenticate_status, license_status, license_status_msg,
      elements_review_items, status, misc_desc, create_time,
      create_oper_id, create_oper_name, update_time, 
      update_oper_id, update_oper_name)
    values (#{id,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, 
      #{driverCode,jdbcType=VARCHAR}, #{fileNo,jdbcType=VARCHAR}, #{driverLicenseImgUrl,jdbcType=VARCHAR}, #{fileNoImgUrl,jdbcType=VARCHAR},
      #{authenticateStatus,jdbcType=INTEGER}, #{licenseStatus,jdbcType=INTEGER}, #{licenseStatusMsg,jdbcType=VARCHAR},
      #{elementsReviewItems,jdbcType=VARCHAR},
      #{status,jdbcType=INTEGER}, #{miscDesc,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{createOperId,jdbcType=BIGINT}, #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updateOperId,jdbcType=BIGINT}, #{updateOperName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.core.model.DriverLicenseElementsAuthenticateRecord" keyProperty="id" useGeneratedKeys="true">
    insert into ${siacSchema}.driver_license_elements_authenticate_record
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="userId != null" >
        user_id,
      </if>
      <if test="name != null" >
        name,
      </if>
      <if test="driverCode != null" >
        driver_code,
      </if>
      <if test="fileNo != null" >
        file_no,
      </if>
      <if test="driverLicenseImgUrl != null" >
        driver_license_img_url,
      </if>
      <if test="fileNoImgUrl != null" >
        file_no_img_url,
      </if>
      <if test="authenticateStatus != null" >
        authenticate_status,
      </if>
      <if test="licenseStatus != null" >
        license_status,
      </if>
      <if test="licenseStatusMsg != null" >
        license_status_msg,
      </if>
      <if test="elementsReviewItems != null" >
        elements_review_items,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="miscDesc != null" >
        misc_desc,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateOperId != null" >
        update_oper_id,
      </if>
      <if test="updateOperName != null" >
        update_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="driverCode != null" >
        #{driverCode,jdbcType=VARCHAR},
      </if>
      <if test="fileNo != null" >
        #{fileNo,jdbcType=VARCHAR},
      </if>
      <if test="driverLicenseImgUrl != null" >
        #{driverLicenseImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="fileNoImgUrl != null" >
        #{fileNoImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="authenticateStatus != null" >
        #{authenticateStatus,jdbcType=INTEGER},
      </if>
      <if test="licenseStatus != null" >
        #{licenseStatus,jdbcType=INTEGER},
      </if>
      <if test="licenseStatusMsg != null" >
        #{licenseStatusMsg,jdbcType=VARCHAR},
      </if>
      <if test="elementsReviewItems != null" >
        #{elementsReviewItems,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="miscDesc != null" >
        #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        #{updateOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.core.model.DriverLicenseElementsAuthenticateRecord" >
    update ${siacSchema}.driver_license_elements_authenticate_record
    <set >
      <if test="userId != null" >
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="name != null" >
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="driverCode != null" >
        driver_code = #{driverCode,jdbcType=VARCHAR},
      </if>
      <if test="fileNo != null" >
        file_no = #{fileNo,jdbcType=VARCHAR},
      </if>
      <if test="driverLicenseImgUrl != null" >
        driver_license_img_url = #{driverLicenseImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="fileNoImgUrl != null" >
        file_no_img_url = #{fileNoImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="authenticateStatus != null" >
        authenticate_status = #{authenticateStatus,jdbcType=INTEGER},
      </if>
      <if test="licenseStatus != null" >
        license_status = #{licenseStatus,jdbcType=INTEGER},
      </if>
      <if test="licenseStatusMsg != null" >
        license_status_msg = #{licenseStatusMsg,jdbcType=VARCHAR},
      </if>
      <if test="elementsReviewItems != null" >
        elements_review_items = #{elementsReviewItems,jdbcType=VARCHAR},
      </if>

      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="miscDesc != null" >
        misc_desc = #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.core.model.DriverLicenseElementsAuthenticateRecord" >
    update ${siacSchema}.driver_license_elements_authenticate_record
    set user_id = #{userId,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      driver_code = #{driverCode,jdbcType=VARCHAR},
      file_no = #{fileNo,jdbcType=VARCHAR},
      driver_license_img_url = #{driverLicenseImgUrl,jdbcType=VARCHAR},
      file_no_img_url = #{fileNoImgUrl,jdbcType=VARCHAR},
      authenticate_status = #{authenticateStatus,jdbcType=INTEGER},
      license_status = #{licenseStatus,jdbcType=INTEGER},
      license_status_msg = #{licenseStatusMsg,jdbcType=VARCHAR},
      elements_review_items = #{elementsReviewItems,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      misc_desc = #{miscDesc,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectUserWaitReviewInfo" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
      <include refid="Base_Column_List" />
    from ${siacSchema}.driver_license_elements_authenticate_record
    where `status` = 1
    and user_id = #{userId,jdbcType=BIGINT}
    ORDER BY id DESC LIMIT 1
  </select>

  <select id="getLastestRecordByUser" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from ${siacSchema}.driver_license_elements_authenticate_record
    where `status` = 1
    and  user_Id = #{userId,jdbcType=BIGINT}
    order by id desc
    limit 1
  </select>

  <select id="getLastAuthenticateRecordByUser" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from ${siacSchema}.driver_license_elements_authenticate_record
    where `status` = 1
    and authenticate_status = 1
    and  user_Id = #{userId,jdbcType=BIGINT}
    order by id desc
    limit 1
  </select>

  <select id="getLastLicAuthRecordsByUser" resultMap="BaseResultMap" >
    select <include refid="Base_Column_List" />
    from ${siacSchema}.driver_license_elements_authenticate_record
    where `status` = 1
    and  user_Id = #{userId,jdbcType=BIGINT}
    order by id desc
    limit #{limit}
  </select>

  <select id="getAuthIngUsers" resultType="java.lang.Long" >
    select distinct user_Id
    from ${siacSchema}.driver_license_elements_authenticate_record
    where `status` = 1
    AND authenticate_status = 3
    AND update_time BETWEEN #{startTime} AND #{endTime}
    order by id desc
    limit 1000
  </select>
</mapper>