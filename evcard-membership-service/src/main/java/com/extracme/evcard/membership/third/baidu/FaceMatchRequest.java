package com.extracme.evcard.membership.third.baidu;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class FaceMatchRequest {


    @JSONField(name = "image") private String image;
    @JSONField(name = "image_type") private String imageType;
    @JSONField(name = "face_type") private String faceType;
    /**
     * 图片质量控制
     * NONE: 不进行控制
     * LOW:较低的质量要求
     * NORMAL: 一般的质量要求
     * HIGH: 较高的质量要求
     * 默认 NONE
     * 若图片质量不满足要求，则返回结果中会提示质量检测失败
     */
    @JSONField(name = "quality_control") private String qualityControl;
    /**
     * 活体检测控制
     * NONE: 不进行控制
     * LOW:较低的活体要求(高通过率 低攻击拒绝率)
     * NORMAL: 一般的活体要求(平衡的攻击拒绝率, 通过率)
     * HIGH: 较高的活体要求(高攻击拒绝率 低通过率)
     * 默认 NONE
     * 若活体检测结果不满足要求，则返回结果中会提示活体检测失败
     */
    @JSONField(name = "liveness_control") private String livenessControl;
}
