<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.core.dao.AgencySecondAppKeyRelationMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.AgencySecondAppKeyRelation" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="agency_id" property="agencyId" jdbcType="VARCHAR" />
    <result column="second_app_key" property="secondAppKey" jdbcType="VARCHAR" />
    <result column="agency_role_id" property="agencyRoleId" jdbcType="BIGINT" />
    <result column="wechat_qr_pic_url" property="wechatQrPicUrl" jdbcType="VARCHAR" />
    <result column="is_deleted" property="isDeleted" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, agency_id, second_app_key, agency_role_id, wechat_qr_pic_url, is_deleted, create_time, 
    create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.extracme.evcard.membership.core.model.AgencySecondAppKeyRelationExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from  ${siacSchema}.agency_second_app_key_relation
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from  ${siacSchema}.agency_second_app_key_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from  ${siacSchema}.agency_second_app_key_relation
    where id = #{id,jdbcType=BIGINT}
  </delete>

    <update id="deleteByAgencyId">
      update  ${siacSchema}.agency_second_app_key_relation set is_deleted = 1, update_time = now(),update_oper_name = #{operatorName}, update_oper_id = #{operatorId}
      where agency_id = #{agencyId}
    </update>

    <insert id="insert" parameterType="com.extracme.evcard.membership.core.model.AgencySecondAppKeyRelation" useGeneratedKeys="true" keyProperty="id" >
    insert into  ${siacSchema}.agency_second_app_key_relation (id, agency_id, second_app_key, 
      agency_role_id, wechat_qr_pic_url, is_deleted, 
      create_time, create_oper_id, create_oper_name, 
      update_time, update_oper_id, update_oper_name
      )
    values (#{id,jdbcType=BIGINT}, #{agencyId,jdbcType=VARCHAR}, #{secondAppKey,jdbcType=VARCHAR}, 
      #{agencyRoleId,jdbcType=BIGINT}, #{wechatQrPicUrl,jdbcType=VARCHAR}, #{isDeleted,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, #{createOperName,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, #{updateOperName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.core.model.AgencySecondAppKeyRelation" >
    insert into  ${siacSchema}.agency_second_app_key_relation
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="agencyId != null" >
        agency_id,
      </if>
      <if test="secondAppKey != null" >
        second_app_key,
      </if>
      <if test="agencyRoleId != null" >
        agency_role_id,
      </if>
      <if test="wechatQrPicUrl != null" >
        wechat_qr_pic_url,
      </if>
      <if test="isDeleted != null" >
        is_deleted,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateOperId != null" >
        update_oper_id,
      </if>
      <if test="updateOperName != null" >
        update_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="agencyId != null" >
        #{agencyId,jdbcType=VARCHAR},
      </if>
      <if test="secondAppKey != null" >
        #{secondAppKey,jdbcType=VARCHAR},
      </if>
      <if test="agencyRoleId != null" >
        #{agencyRoleId,jdbcType=BIGINT},
      </if>
      <if test="wechatQrPicUrl != null" >
        #{wechatQrPicUrl,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null" >
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        #{updateOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.extracme.evcard.membership.core.model.AgencySecondAppKeyRelationExample" resultType="java.lang.Integer" >
    select count(*) from  ${siacSchema}.agency_second_app_key_relation
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>

  <select id="selectRelationByAgencyId" resultMap="BaseResultMap" resultType="com.extracme.evcard.membership.core.model.AgencySecondAppKeyRelation">
    select
    <include refid="Base_Column_List" />
    from  ${siacSchema}.agency_second_app_key_relation
    where agency_id = #{agencyId}
    and is_deleted = 0
    order by id desc
  </select>

  <select id="selectBySecondAppKey" resultMap="BaseResultMap" resultType="com.extracme.evcard.membership.core.model.AgencySecondAppKeyRelation">
    select
    <include refid="Base_Column_List" />
    from  ${siacSchema}.agency_second_app_key_relation
    where second_app_key= #{secondAppKey}
    and is_deleted = 0
    order by id desc
  </select>



  <update id="updateByExampleSelective" parameterType="map" >
    update  ${siacSchema}.agency_second_app_key_relation
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.agencyId != null" >
        agency_id = #{record.agencyId,jdbcType=VARCHAR},
      </if>
      <if test="record.secondAppKey != null" >
        second_app_key = #{record.secondAppKey,jdbcType=VARCHAR},
      </if>
      <if test="record.agencyRoleId != null" >
        agency_role_id = #{record.agencyRoleId,jdbcType=BIGINT},
      </if>
      <if test="record.wechatQrPicUrl != null" >
        wechat_qr_pic_url = #{record.wechatQrPicUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null" >
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null" >
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createOperId != null" >
        create_oper_id = #{record.createOperId,jdbcType=BIGINT},
      </if>
      <if test="record.createOperName != null" >
        create_oper_name = #{record.createOperName,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null" >
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateOperId != null" >
        update_oper_id = #{record.updateOperId,jdbcType=BIGINT},
      </if>
      <if test="record.updateOperName != null" >
        update_oper_name = #{record.updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update  ${siacSchema}.agency_second_app_key_relation
    set id = #{record.id,jdbcType=BIGINT},
      agency_id = #{record.agencyId,jdbcType=VARCHAR},
      second_app_key = #{record.secondAppKey,jdbcType=VARCHAR},
      agency_role_id = #{record.agencyRoleId,jdbcType=BIGINT},
      wechat_qr_pic_url = #{record.wechatQrPicUrl,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{record.createOperId,jdbcType=BIGINT},
      create_oper_name = #{record.createOperName,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{record.updateOperId,jdbcType=BIGINT},
      update_oper_name = #{record.updateOperName,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.core.model.AgencySecondAppKeyRelation" >
    update  ${siacSchema}.agency_second_app_key_relation
    <set >
      <if test="agencyId != null" >
        agency_id = #{agencyId,jdbcType=VARCHAR},
      </if>
      <if test="secondAppKey != null" >
        second_app_key = #{secondAppKey,jdbcType=VARCHAR},
      </if>
      <if test="agencyRoleId != null" >
        agency_role_id = #{agencyRoleId,jdbcType=BIGINT},
      </if>
      <if test="wechatQrPicUrl != null" >
        wechat_qr_pic_url = #{wechatQrPicUrl,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null" >
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.core.model.AgencySecondAppKeyRelation" >
    update  ${siacSchema}.agency_second_app_key_relation
    set agency_id = #{agencyId,jdbcType=VARCHAR},
      second_app_key = #{secondAppKey,jdbcType=VARCHAR},
      agency_role_id = #{agencyRoleId,jdbcType=BIGINT},
      wechat_qr_pic_url = #{wechatQrPicUrl,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>