package com.extracme.evcard.membership.job;

import com.alibaba.fastjson.JSON;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.extracme.evcard.elasticjob.ElasticSimpleJob;
import com.extracme.evcard.membership.common.YiGuanTraceSearchUsersUtil;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.dto.YiGuanSerachUserDto;
import com.extracme.evcard.membership.core.dto.YiGuanSerachUsersResponse;
import com.extracme.evcard.membership.core.service.MemberShipServiceImpl;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 易观 会员公共信息 埋点
 * 全量更新用户状态
 * user_identity_status身份认证状态
 * review_status  会员审核状态
 * driving_license_verify_status 驾照认证状态
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ElasticSimpleJob(jobName = "evcard-membership-yiguanMemberStatusTrackJob", cron = "0 00 16 20 7 ? 2032", description = "会员状态全量更新易观埋点", overwrite = true)
public class YiguanMemberStatusTrackJob implements SimpleJob {

    @Autowired
    private MembershipInfoMapper membershipInfoMapper;

    @Autowired
    private MemberShipServiceImpl memberShipService;

    @Value("${yiguan.update.member.status.enable:1}")
    private Integer enable;

    @Value("${yiguan.update.member.status.pagesize:3000}")
    private Integer pagesize;


    @Override
    public void execute(ShardingContext arg0) {
        start(1);
    }

    public void start(int page) {
        while (true) {
            if (enable == 1) {
                try {
                    log.info("YiguanMemberStatusTrackJob 开始page[{}]，pagesize[{}]",page,pagesize);
                    String result = YiGuanTraceSearchUsersUtil.searchUser(page, pagesize);
                    if (StringUtils.isNotEmpty(result)) {
                        YiGuanSerachUsersResponse response = JSON.parseObject(result, YiGuanSerachUsersResponse.class);
                        if (response != null) {
                            List<YiGuanSerachUserDto> users = response.getUsers();
                            if (CollectionUtils.isNotEmpty(users)) {
                                List<String> authIds = users.stream().map(YiGuanSerachUserDto::getXwho).collect(Collectors.toList());
                                if (CollectionUtils.isNotEmpty(authIds)) {
                                    List<MembershipBasicInfo> membershipInfoList = membershipInfoMapper.selectByAuthIdsForYG(authIds);
                                    if (CollectionUtils.isNotEmpty(membershipInfoList)) {
                                        for (MembershipBasicInfo p : membershipInfoList) {
                                            memberShipService.traceMemberForStatus(p);
                                        }
                                    }
                                }
                            }

                            int count = response.getCount();
                            int page1 = response.getPage();
                            int size = response.getSize();
                            if (count > ((page1 - 1) * pagesize + size)) {
                                page = page + 1;
                            } else {
                                return;
                            }
                            Thread.sleep(10000);
                        }
                    }else{
                        log.info("YiguanMemberStatusTrackJob 关闭result为空");
                        break;
                    }
                } catch (Exception e) {
                    log.error("YiguanMemberStatusTrackJob 任务异常[{}]",e);
                }
            }else{
                log.info("YiguanMemberStatusTrackJob 关闭enable[{}]",enable);
                break;
            }
        }
    }


}
