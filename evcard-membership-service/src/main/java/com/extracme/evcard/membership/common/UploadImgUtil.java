package com.extracme.evcard.membership.common;

import com.aliyun.oss.*;
import com.aliyun.oss.model.OSSObject;
import com.extracme.evcard.membership.config.OssConfigUtil;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.apache.commons.io.IOUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 上传图片阿里OSS
 *
 * <AUTHOR>
public class UploadImgUtil {

	public final static String OSS_BUCKET = OssConfigUtil.getOssBucket();
	public final static String OSS_SENSITIVE_BUCKET = OssConfigUtil.getOssSensitiveBucket();
	private final static String OSS_ENDPOINT = OssConfigUtil.getOssEndpoint();
	private final static String ALI_ACCESSID = OssConfigUtil.getAliAccessId();
	private final static String ALI_ACCESSKEY = OssConfigUtil.getAliAccessKey();
	public final static String ENV = OssConfigUtil.getENV();


	private static final Log logger =LogFactory.getLog(UploadImgUtil.class);

	private static Executor executor = new ThreadPoolExecutor(5, 20,
			0L, TimeUnit.MILLISECONDS,
			new LinkedBlockingQueue<Runnable>(1024),  new ThreadFactoryBuilder()
			.setNameFormat("demo-pool-%d").build(), new ThreadPoolExecutor.AbortPolicy());
    /**
     * 上传
     * @param sourceFile  文件
     * @param pathUrl    存放路径地址
     * <AUTHOR>
     */
    public static void uploadStream(final InputStream sourceFile,final String pathUrl, String bucketName){
    	executor.execute(()->{
    		// 上传图片到oss服务器
			OSS ossClient = null;
			try {
				ossClient = new OSSClientBuilder().build(OSS_ENDPOINT, ALI_ACCESSID, ALI_ACCESSKEY);
				ossClient.putObject(bucketName,ENV + "/" + pathUrl, sourceFile);
				IOUtils.closeQuietly(sourceFile);
			} catch (OSSException e) {
				// TODO Auto-generated catch block
				logger.error(e);
			} catch (ClientException e) {
				logger.error(e);
			} catch (Exception e) {
				logger.error(e);
			} finally {
				// 关闭client
				if(ossClient!=null){
					ossClient.shutdown();
				}
			}
    	});
    }
    
    /**
     * 上传
     * @param sourceFile  文件
     * @param pathUrl    存放路径地址
     * <AUTHOR>
     */
    public static void uploadStream(final File sourceFile,final String pathUrl, String bucketName){
    	executor.execute(()->{
    		// 上传图片到oss服务器
			OSS ossClient = null;
			   FileInputStream fileInputStream = null;
				try {
					fileInputStream = new FileInputStream(sourceFile);
					ossClient = new OSSClientBuilder().build(OSS_ENDPOINT, ALI_ACCESSID, ALI_ACCESSKEY);
					ossClient.putObject(bucketName,ENV + "/" + pathUrl,fileInputStream);
				} catch (OSSException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				} catch (ClientException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				} catch (Exception e) {
					// TODO: handle exception
				} finally {
					IOUtils.closeQuietly(fileInputStream);
					// 关闭client
					if(ossClient!=null){
						ossClient.shutdown();
					}
				}	
    	});
    }

	/**
	 * 上传 同步上传
	 * @param sourceFile 文件
	 * @param pathUrl 存放路径地址
	 * <AUTHOR>
	 */
	public static boolean uploadStreamSyn(final InputStream sourceFile, final String pathUrl) {
		// 上传图片到oss服务器
		OSSClient ossClient = null;
		try {
			ossClient = new OSSClient(OSS_ENDPOINT, ALI_ACCESSID, ALI_ACCESSKEY);
			ossClient.putObject(OSS_BUCKET, ENV + pathUrl, sourceFile);
			IOUtils.closeQuietly(sourceFile);
			return true;
		} catch (OSSException e) {
			logger.error(e);
		} catch (ClientException e) {
			logger.error(e);
		} catch (Exception e) {
			logger.error(e);
		} finally {
			// 关闭client
			if (ossClient != null) {
				ossClient.shutdown();
			}
		}
		return false;
	}
    
    /**
     * 上传  同步上传
     * @param sourceFile  文件
     * @param pathUrl    存放路径地址
     * <AUTHOR>
     */
    public static void uploadStreamSyn(final InputStream sourceFile,final String pathUrl, String bucketName){
		// 上传图片到oss服务器
		OSS ossClient = null;
		try {
			ossClient = new OSSClientBuilder().build(OSS_ENDPOINT, ALI_ACCESSID, ALI_ACCESSKEY);
			ossClient.putObject(bucketName, ENV + pathUrl, sourceFile);
			IOUtils.closeQuietly(sourceFile);
		} catch (OSSException e) {
			logger.error("uploadStreamSyn失败，pathUrl=" + pathUrl, e);
			logger.error(e);
		} catch (ClientException e) {
			logger.error("uploadStreamSyn失败，pathUrl=" + pathUrl, e);
			logger.error(e);
		} catch (Exception e) {
			logger.error("uploadStreamSyn失败，pathUrl=" + pathUrl, e);
			logger.error(e);
		} finally {
			// 关闭client
			if (ossClient != null) {
				ossClient.shutdown();
			}
		}
    }

	/**
	 * 上传 同步上传
	 * @param sourceFile  文件
	 * @param pathUrl    存放路径地址
	 * @param bucketName    bucketName
	 * <AUTHOR>
	 */
	public static void uploadStreamSyn(final File sourceFile,final String pathUrl, String bucketName){
		// 上传图片到oss服务器
		OSS ossClient = null;
		FileInputStream fileInputStream = null;
		try {
			fileInputStream = new FileInputStream(sourceFile);
			ossClient = new OSSClientBuilder().build(OSS_ENDPOINT, ALI_ACCESSID, ALI_ACCESSKEY);
			ossClient.putObject(bucketName,ENV + "/" + pathUrl,fileInputStream);
		} catch (OSSException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (ClientException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (Exception e) {
			// TODO: handle exception
		} finally {
			IOUtils.closeQuietly(fileInputStream);
			// 关闭client
			if(ossClient!=null){
				ossClient.shutdown();
			}
		}
	}

	/**
	 * 上传
	 * @param byteArray  文件
	 * @param pathUrl    存放路径地址
	 * <AUTHOR>
	 */
	public static void uploadByteArray(final byte[] byteArray,final String pathUrl, String bucketName) throws BusinessException {
		// 上传图片到oss服务器
		OSS ossClient = null;
		try {
			ossClient = new OSSClientBuilder().build(OSS_ENDPOINT, ALI_ACCESSID, ALI_ACCESSKEY);
			ossClient.putObject(bucketName, ENV + "/" + pathUrl, new ByteArrayInputStream(byteArray));
		} catch (OSSException e) {
			logger.error(e);
			throw new BusinessException(-1, "上传失败");
		} catch (ClientException e) {
			logger.error(e);
			throw new BusinessException(-1, "上传失败");
		} catch (Exception e) {
			logger.error(e);
			throw new BusinessException(-1, "上传失败");
		} finally {
			// 关闭client
			if (ossClient != null) {
				ossClient.shutdown();
			}
		}
	}

	/**
	 * 根据文件url转化为文件流 ，上传到OSS
	 * @param fileUrl  文件网络地址
	 * @param objectName 对象名称
	 * @param bucketName 目录名称
	 */
	public static void uploadSynByFileUrl(final String fileUrl, String objectName, String bucketName) {
		HttpURLConnection conn = null;
		InputStream stream = null;
		try {
			URL url = new URL(fileUrl);
			conn = (HttpURLConnection) url.openConnection();
			conn.setRequestMethod("GET");
			conn.setConnectTimeout(5000);
			conn.setReadTimeout(5000);
			conn.connect();
			stream = conn.getInputStream();
			uploadStreamSyn(stream, objectName, bucketName);
		} catch (Exception ex) {
			logger.error("uploadSynByFileUrl失败，fileUrl=" + fileUrl, ex);
			ex.printStackTrace();
		} finally {
			IOUtils.closeQuietly(stream);
			IOUtils.close(conn);
		}
	}

	public static OSSObject downloadStream(String key) {
		OSS ossClient = new OSSClientBuilder().build(OSS_ENDPOINT, ALI_ACCESSID, ALI_ACCESSKEY);
		OSSObject object = ossClient.getObject(OSS_BUCKET, key);
		return object;
	}
}


