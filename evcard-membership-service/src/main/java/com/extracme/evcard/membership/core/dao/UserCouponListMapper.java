package com.extracme.evcard.membership.core.dao;

import org.apache.ibatis.annotations.Param;

public interface UserCouponListMapper {

    int selectCount(@Param("authId") String authId,@Param("origin")String origin);


    int batchInvalidatedCouponByUserCouponSeq(@Param("authId") String authId,
                                              @Param("optUser") String optUser,
                                              @Param("updatedTime") String updatedTime,
                                              @Param("actionId") String actionId,
                                              @Param("offerType") String offerType,
                                              @Param("createdTime") String createdTime);
}