package com.extracme.evcard.membership.core.enums;

public enum LicenseAuthStatusEnum {
    //驾照认证状态 1:未认证、2:待认证、3:已认证、4:认证失败、 5:即将过期（剩余90天）、6:已过期
    UN_SUBMIT(1, "未认证(资料未上传)"),
    TO_MANUAL_REVIEW(2, "待认证"),
    AUTHENTICATED(3, "已认证"),
    AUTHENTICATE_FAILED(4, "认证不通过"),

    /**
     * 以下为附加状态-数据库字段中实际不存在
     */
    EXTRA_EXPIRING(5, "即将过期"),
    EXTRA_EXPIRED(6, "已过期");

    LicenseAuthStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /** 状态值 */
    private Integer value;

    /** 描述 */
    private String desc;

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public boolean eq(Integer value) {
        return this.value.equals(value);
    }

    /**
     * 124 状态履约显示未认证
     * 356履约显示已认证。
     *
     * @param value
     * @return
     */
    public static final boolean unAuthed(Integer value) {
        return UN_SUBMIT.eq(value) || AUTHENTICATE_FAILED.eq(value) || TO_MANUAL_REVIEW.eq(value) || (0==value);
    }
}
