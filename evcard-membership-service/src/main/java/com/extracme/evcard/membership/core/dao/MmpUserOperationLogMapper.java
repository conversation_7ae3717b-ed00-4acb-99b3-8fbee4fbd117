package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.model.MmpUserOperationLog;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface MmpUserOperationLogMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MmpUserOperationLog record);

    int insertSelective(MmpUserOperationLog record);

    MmpUserOperationLog selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MmpUserOperationLog record);

    int updateByPrimaryKey(MmpUserOperationLog record);

    /**
     * 查询指定会员操作日志
     * @param userId
     * @param operateTime
     * @param operationType
     * @return
     */
    MmpUserOperationLog selectOneMemOptLogByType(@Param("userId") Long userId,
                          @Param("operateTime") Date operateTime,
                          @Param("operationType") Long operationType);

    /**
     * 查询指定会员操作日志
     * @param userId
     * @param operateTime
     * @param operationTypes
     * @return
     */
    MmpUserOperationLog selectOneMemOptLogByTypes(@Param("userId") Long userId,
                                                 @Param("operateTime") Date operateTime,
                                                 @Param("operationTypes") List<Long> operationTypes);

    /**
     * 查询指定会员的最新操作日志
     * @param userId
     * @param operationType
     * @return
     */
    MmpUserOperationLog selectlastestMemOptLogByType(@Param("userId") Long userId,
                                                 @Param("operationType") Long operationType);


    /**
     * 查询会员日志
     * @param userId
     * @param pageNum
     * @param pageSize
     * @return
     */
    List<MmpUserOperationLog> selectMmpUserOperationLog(@Param("userId")Long userId,@Param("pageNum") int pageNum,@Param("pageSize") int pageSize);

    /**
     * 查询会员日志
     *
     * @param userId   用户id
     * @param operationTypes  类型
     * @return
     */
    List<MmpUserOperationLog> selectOperationLogsByType(@Param("userId")Long userId,@Param("operationTypes") List<Long> operationTypes);
}