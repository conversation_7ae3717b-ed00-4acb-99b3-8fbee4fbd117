package com.extracme.evcard.membership.core.service.agency;

import com.alibaba.dubbo.common.Constants;
import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.core.dao.*;
import com.extracme.evcard.membership.core.dto.MemberDiscountDTO;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.dto.agency.AgencyDiscountConfig;
import com.extracme.evcard.membership.core.dto.agency.AgencyDiscountDTO;
import com.extracme.evcard.membership.core.dto.agency.DiscountRuleDTO;
import com.extracme.evcard.membership.core.dto.agency.PersonalDiscountDTO;
import com.extracme.evcard.membership.core.dto.discount.MmpDiscountPackageShareRuleDTO;
import com.extracme.evcard.membership.core.model.AgencyInfo;
import com.extracme.evcard.membership.core.model.MmpAgencyDiscountLog;
import com.extracme.evcard.membership.core.model.MmpDiscountPackageShareRule;
import com.extracme.evcard.membership.core.model.MmpDiscountRule;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.redis.JedisUtil;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.framework.core.util.BeanCopyUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 企业会员折扣信息
 * <AUTHOR>
 */
@Service("agencyDiscountService")
public class AgencyDiscountServiceImpl implements IAgencyDiscountService {
    private static Logger logger = LoggerFactory.getLogger(AgencyDiscountServiceImpl.class);

    @Autowired
    IMemberShipService memberShipService;

    @Resource
    private MmpDiscountRuleMapper mmpDiscountRuleMapper;

    @Resource
    private AgencyInfoMapper agencyInfoMapper;

    @Resource
    private MmpAgencyDiscountLogMapper mmpAgencyDiscountLogMapper;

    @Resource
    private MmpPersonalDiscountMapper mmpPersonalDiscountMapper;

    @Resource
    private MmpDiscountPackageShareRuleMapper mmpDiscountPackageShareRuleMapper;

    private static final String KEY_AGENCY_DISCOUNT_PREFIX = "agency_top_discount_";
    private static final int EXPIRE_AGENCY_SENCONDS = 60 * 10;
    @Override
    public MemberDiscountDTO getMemberDiscount(String authId, Integer membershipType) {
        MembershipBasicInfo memberInfo = memberShipService.getUserBasicInfo(authId, membershipType.shortValue());
        if(memberInfo == null || StringUtils.isBlank(memberInfo.getAgencyId())) {
            //会员不存在，或无企业信息，无折扣
            return null;
        }
        //1. 优先从缓存中获取折扣
        MemberDiscountDTO memberDiscount = null;
        String agencyId = memberInfo.getAgencyId();
        String redisKey = KEY_AGENCY_DISCOUNT_PREFIX + agencyId;
        String value = JedisUtil.get(redisKey);
        //2. 缓存中不存在，则从数据库中获取
        if (value == null) {
            synchronized (this) {
                String cacheValue = JedisUtil.get(redisKey);
                //redis中存在，则直接从redis中读取。
                if(cacheValue != null) {
                    memberDiscount = JSON.parseObject(cacheValue, MemberDiscountDTO.class);
                }else {
                    //redis中不存在，则读数据库，并更新缓存。
                    memberDiscount = queryAgencyCurrentDiscount(agencyId);
                    JedisUtil.setCover(redisKey, JSON.toJSONString(memberDiscount), EXPIRE_AGENCY_SENCONDS);
                }
                return memberDiscount;
            }
        }
        memberDiscount = JSON.parseObject(value, MemberDiscountDTO.class);
        return memberDiscount;
    }

    @Override
    public DiscountRuleDTO getDiscount(String agencyId, Integer discountType, boolean validOnly) {
        AgencyDiscountConfig discountConfig = getPartAgencyDiscountConfig(agencyId, null, validOnly);
        return getDiscount(discountConfig, discountType, null, validOnly);
    }

    private DiscountRuleDTO getDiscount(AgencyDiscountConfig agencyConfig, Integer discountType, Date time, boolean validOnly) {
        if(agencyConfig == null) {
            return null;
        }
        String agencyId = agencyConfig.getAgencyId();
        MmpDiscountRule mmpDiscountRule = null;

        //查询企业折扣
        Long discountId = agencyConfig.getDiscountId();
        if(discountType != null && discountType == 1) {
            discountId = agencyConfig.getDiscountPersonalId();
        }
        if(discountId != null) {
            mmpDiscountRule = mmpDiscountRuleMapper.selectByPrimaryKey(discountId);
        }else {
            List<DiscountRuleDTO> discountRuleDTOS = this.find(agencyId, discountType);
            if(discountRuleDTOS.size() > 0){
                mmpDiscountRule = new MmpDiscountRule();
                BeanCopyUtils.copyProperties(discountRuleDTOS.get(0), mmpDiscountRule);
            }
        }
        if(mmpDiscountRule == null) {
            return null;
        }
        //增加时间校验
        if(validOnly && !isValidRule(mmpDiscountRule, time)) {
            return null;
        }
        //个人阶梯折扣获取(规则为广铁)
        DiscountRuleDTO discountRuleDTO = null;
        if(mmpDiscountRule != null){
            discountRuleDTO = new DiscountRuleDTO();
            BeanCopyUtils.copyProperties(mmpDiscountRule, discountRuleDTO);
            Integer discountRule = agencyConfig.getDiscountRule();
            if(discountRule != null && discountRule == 2 && discountType == 1){
                discountRuleDTO.setDiscountRule(discountRule);
                List<PersonalDiscountDTO> personalDetailDTOS = mmpPersonalDiscountMapper.findPersonalList(mmpDiscountRule.getId());
                discountRuleDTO.setPersonalRules(personalDetailDTOS);
            }
        }
        /**
         * 兼容配置了规则，但折扣率为NULL的老企业(缺省折扣率为100%)
         */
        if(discountRuleDTO != null && discountRuleDTO.getDiscountRate() == null) {
            discountRuleDTO.setDiscountRate(100D);
        }
        return discountRuleDTO;
    }

    @Override
    public List<DiscountRuleDTO> find(String agencyId, Integer discountType) {
        List<DiscountRuleDTO> discountRuleDTOS = mmpDiscountRuleMapper.findByAgencyId(agencyId,discountType);
        return discountRuleDTOS;
    }

    @Override
    public BaseResponse saveDiscountPackageShareRule(MmpDiscountPackageShareRuleDTO discountPackageShareRuleDTO) {
        BaseResponse response = new BaseResponse();
        if (discountPackageShareRuleDTO != null) {
            response = checkDiscountPackageParam(discountPackageShareRuleDTO);
            if (response.getCode() == -1){
                return response;
            }
            //将旧的套餐状态更新为 0 失效
            MmpDiscountPackageShareRule oldDiscountPackageShareRule = mmpDiscountPackageShareRuleMapper.selectLatestByTime(
                    discountPackageShareRuleDTO.getAgencyId(), discountPackageShareRuleDTO.getCreateTime());
            if (oldDiscountPackageShareRule != null) {
                mmpDiscountPackageShareRuleMapper.updateByLatestTimeLapse(oldDiscountPackageShareRule.getId());
            }
            //保存新的套餐
            MmpDiscountPackageShareRule discountPackageShareRule = new MmpDiscountPackageShareRule();
            BeanCopyUtils.copyProperties(discountPackageShareRuleDTO, discountPackageShareRule);
            String packageTypeStr = StringUtils.join(discountPackageShareRuleDTO.getPackageTypeList(),Constants.COMMA_SEPARATOR);
            if(packageTypeStr == null) {
                packageTypeStr = StringUtils.EMPTY;
            }
            discountPackageShareRule.setPackageTypeList(packageTypeStr);
            int num = mmpDiscountPackageShareRuleMapper.insertSelective(discountPackageShareRule);
            if (num >0){
                response.setCode(0);
                response.setMessage(discountPackageShareRule.getId().toString());
                return response;
            }else{
                response.setCode(-1);
                response.setMessage("失败");
                return response;
            }
        }else {
            response.setCode(-1);
            return response;
        }
    }

    @Override
    public MmpDiscountPackageShareRuleDTO findMemberDisCountPackageShareRule(String authId, Date time) {
        MembershipBasicInfo memberInfo = memberShipService.getUserBasicInfo(authId, Short.parseShort("0"));
        if(memberInfo == null || StringUtils.isBlank(memberInfo.getAgencyId())) {
            //会员不存在，或无企业信息
            return null;
        }
        String agencyId = memberInfo.getAgencyId();
        MmpDiscountPackageShareRuleDTO packageShareRuleDTO = findDisCountPackageShareRule(agencyId, time);
        return packageShareRuleDTO;
    }

    @Override
    public MmpDiscountPackageShareRuleDTO findDisCountPackageShareRule(String agencyId, Date time) {
        if(StringUtils.isBlank(agencyId)) {
            return null;
        }
        if(time == null) {
            time = new Date();
        }
        MmpDiscountPackageShareRuleDTO packageShareRuleDTO = null;
        MmpDiscountPackageShareRule mmpDiscountPackageShareRule = mmpDiscountPackageShareRuleMapper.selectLatestByTime(agencyId, time);
        if(mmpDiscountPackageShareRule != null) {
            packageShareRuleDTO = new MmpDiscountPackageShareRuleDTO();
            BeanCopyUtils.copyProperties(mmpDiscountPackageShareRule, packageShareRuleDTO);
            String packageTypeStr = mmpDiscountPackageShareRule.getPackageTypeList();
            String[] packageTypes = StringUtils.split(packageTypeStr, Constants.COMMA_SEPARATOR);
            packageShareRuleDTO.setPackageTypeList(Arrays.asList(packageTypes));
        }
        return packageShareRuleDTO;
    }

    public MemberDiscountDTO queryAgencyCurrentDiscount(String agencyId) {
        if(StringUtils.isBlank(agencyId)) {
            //无企业信息，无折扣
            return null;
        }
        //1. 优先获取企业折扣
        DiscountRuleDTO agencyDiscount = getDiscount(agencyId, 0, true);
        MemberDiscountDTO memberDiscount = new MemberDiscountDTO();
        //企业折扣为100%时，视为无企业折扣-->尝试获取个人折扣
        if(agencyDiscount != null && agencyDiscount.getDiscountRate() != null
                && !agencyDiscount.getDiscountRate().equals(100D)) {
            BeanCopyUtils.copyProperties(agencyDiscount, memberDiscount);
            return memberDiscount;
        }
        //2. 无企业折扣则获取个人折扣
        DiscountRuleDTO personDiscount = getDiscount(agencyId, 1, true);
        //无企业折扣且无个人折扣
        if(personDiscount == null) {
            return null;
        }
        List<PersonalDiscountDTO> personalRules = personDiscount.getPersonalRules();
        Double discount = personDiscount.getDiscountRate();
        if(CollectionUtils.isNotEmpty(personalRules)) {
            Double maxDiscount = personalRules.stream().max(
                    (a, b) -> {
                        return a.getDiscountRate().compareTo(b.getDiscountRate());
                    }).get().getDiscountRate();
            //if(Double.compare(maxDiscount, discount) > 0) {
            discount = maxDiscount;
            //}
        }
        BeanCopyUtils.copyProperties(personDiscount, memberDiscount);
        memberDiscount.setDiscountRate(discount);
        return memberDiscount;
    }

    private boolean isValidRule(MmpDiscountRule rule, Date time) {
        Date date = (time == null) ? new Date() : time;
        if(rule.getValidStartTime() != null && date.compareTo(rule.getValidStartTime()) < 0) {
            return false;
        }
        if(rule.getValidEndTime() != null && date.compareTo(rule.getValidEndTime()) > 0) {
            return false;
        }
        return true;
    }

    public BaseResponse checkDiscountPackageParam(MmpDiscountPackageShareRuleDTO discountPackageShareRuleDTO){
        BaseResponse response = new BaseResponse();
        if (StringUtils.isEmpty(discountPackageShareRuleDTO.getAgencyId())){
            response.setCode(-1);
            response.setMessage("企业会员ID缺失");
            return response;
        }
        if (CollectionUtils.isEmpty(discountPackageShareRuleDTO.getPackageTypeList())) {
            response.setCode(-1);
            response.setMessage("套餐类型缺失");
            return response;
        }
        //默认不同享
        if(discountPackageShareRuleDTO.getShareType() == null) {
            discountPackageShareRuleDTO.setShareType(0);
        }
        if(discountPackageShareRuleDTO.getShareType() != 0 && discountPackageShareRuleDTO.getShareType() != 1){
            response.setMessage("共享类型错误");
            response.setCode(-1);
            return response;
        }
        return response;
    }

    @Override
    public BaseResponse checkVehicleNoLimit(String agencyId, String vehicleNo) {
        return checkVehicleNoLimit(agencyId, null, vehicleNo);
    }

    @Override
    public BaseResponse checkVehicleNoLimit(String agencyId, Date time, String vehicleNo) {
        AgencyDiscountConfig discountConfig = getPartAgencyDiscountConfig(agencyId, time, false);
        if(discountConfig == null) {
            return new BaseResponse(-1, "企业信息不存在或当前不在合作中");
        }
        String vehicleNoLimit = discountConfig.getVehicleNo();
        if(StringUtils.isNotBlank(vehicleNoLimit)) {
            String[] vehicleNos = StringUtils.split(vehicleNoLimit, Constants.COMMA_SEPARATOR);
            if(vehicleNos.length > 0) {
                //if(!StringUtils.containsAny(vehicleNo, vehicleNos)) {
                if(!StringUtils.startsWithAny(vehicleNo, vehicleNos)) {
                    return new BaseResponse(-2, "不符合企业车牌限制");
                }
            }
        }
        return new BaseResponse(0, "success");
    }

    @Override
    public AgencyDiscountConfig getAgencyDiscountConfig(String agencyId, Date time, boolean validOnly) {
        //企业状态&折扣&车牌限制
        AgencyDiscountConfig discountConfig = getPartAgencyDiscountConfig(agencyId, time, validOnly);
        if(discountConfig == null) {
            return null;
        }
        //与套餐不同享规则
        Date date = (time == null) ? new Date() : time;
        MmpDiscountPackageShareRuleDTO shareRuleDTO = findDisCountPackageShareRule(agencyId, date);
        if(shareRuleDTO != null) {
            discountConfig.setPackageTypeList(shareRuleDTO.getPackageTypeList());
        }
        return discountConfig;
    }

    @Override
    public AgencyDiscountDTO getAgencyDiscountDetail(String agencyId, Date time, boolean validOnly) {
        AgencyDiscountDTO agencyDiscount = new AgencyDiscountDTO();
        //优先从履历表中查询指定折扣配置信息
        AgencyDiscountConfig discountConfig = getAgencyDiscountConfig(agencyId, time, validOnly);
        if(discountConfig == null) {
            return null;
        }
        BeanCopyUtils.copyProperties(discountConfig, agencyDiscount);
        //1. 企业折扣信息与个人折扣信息
        DiscountRuleDTO discountRule = getDiscount(discountConfig, 0, null, validOnly);
        DiscountRuleDTO personDiscountRule = getDiscount(discountConfig, 1, null, validOnly);
        agencyDiscount.setAgencyDiscountRule(discountRule);
        agencyDiscount.setPersonDiscountRule(personDiscountRule);
        //2. 车牌限制
        agencyDiscount.setVehicleNo(discountConfig.getVehicleNo());
        //3. 与套餐不同享规则
        agencyDiscount.setPackageTypeList(discountConfig.getPackageTypeList());
        return agencyDiscount;
    }

    //此接口不包含与套餐同享信息
    private AgencyDiscountConfig getPartAgencyDiscountConfig(String agencyId, Date time, boolean validOnly) {
        Integer status = validOnly ? 1 : null;
        AgencyDiscountConfig discountConfig = null;
        //1. 若指定了时间，则优先从履历表中查询指定折扣配置信息
        if(time != null) {
            MmpAgencyDiscountLog agencyDiscountRecord = mmpAgencyDiscountLogMapper.selectLatestByTime(agencyId, time);
            if(agencyDiscountRecord != null) {
                discountConfig = new AgencyDiscountConfig();
                BeanCopyUtils.copyProperties(agencyDiscountRecord, discountConfig);
            }
        }
        //2.未指定时间，或履历表中无指定时间点的企业配置（为兼容旧的无变更履历的企业配置，暂也取最新配置）
        //TODO 确定无变更履历的旧数据，是否直接取机构最新折扣信息
        if(discountConfig == null) {
            AgencyInfo agencyInfo = agencyInfoMapper.selectByPrimaryKeyAndStatus(agencyId, status);
            if(agencyInfo == null) {
                return null;
            }
            discountConfig = new AgencyDiscountConfig();
            BeanCopyUtils.copyProperties(agencyInfo, discountConfig);
            if(agencyInfo.getStatus() != null) {
                discountConfig.setStatus(agencyInfo.getStatus().intValue());
            }
        }
        //过滤非合作中状态的机构
        if(validOnly && !NumberUtils.INTEGER_ONE.equals(discountConfig.getStatus())) {
            return null;
        }
        return discountConfig;
    }

}
