package com.extracme.evcard.membership;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.extracme.evcard.elasticjob.EnableElasticJob;
import com.extracme.evcard.redis.spring.EnableRedisUtil;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication
@EnableAsync
@EnableApolloConfig
//@EnableApolloConfig(value = {"redis","config","application","aliyunConfig","third"})
////@PropertySources({
////		@PropertySource("classpath:config.properties"),
////		@PropertySource("classpath:aliyunConfig.properties"),
////		@PropertySource("classpath:redis.properties"),
////		@PropertySource("classpath:third.properties")
////})
@EnableDubbo
@EnableRedisUtil
@EnableElasticJob
@MapperScan(value = {"com.extracme.evcard.membership.**.dao"})
@ImportResource(locations = {"classpath:dubbo.xml"})
public class App {

		public static void main(String[] args) {
			try {
				SpringApplication.run(App.class, args);
			}catch (Exception e) {
				e.printStackTrace();
				throw e;
			}
		}
}
