package com.extracme.evcard.membership.core.model;

public class EntityCache {
	/**
	 * 保存的数据
	 */
	private Object data;
	/**
	 * 设置数据失效时间,为0表示永不失效
	 */
	private long timeOut;
	/**
	 * 最后刷新时间
	 */
	private long lastRefeshTime;
	
	public EntityCache() {
		super();
	}
	public EntityCache(Object data, long timeOut, long lastRefeshTime) {
		super();
		this.data = data;
		this.timeOut = timeOut;
		this.lastRefeshTime = lastRefeshTime;
	}
	public Object getData() {
		return data;
	}
	public void setData(Object data) {
		this.data = data;
	}
	public long getTimeOut() {
		return timeOut;
	}
	public void setTimeOut(long timeOut) {
		this.timeOut = timeOut;
	}
	public long getLastRefeshTime() {
		return lastRefeshTime;
	}
	public void setLastRefeshTime(long lastRefeshTime) {
		this.lastRefeshTime = lastRefeshTime;
	}
}
