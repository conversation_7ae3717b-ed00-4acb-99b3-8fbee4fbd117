<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.extracme.evcard.membership.core.dao.AreaMapper">

    <select id="batchQueryAreaName" resultType="map" parameterType="list">
        SELECT
          AREAID as areaId,
          area as areaName
        FROM ${siacSchema}.area
        where AREAID in
        <foreach item="item" collection="list" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>