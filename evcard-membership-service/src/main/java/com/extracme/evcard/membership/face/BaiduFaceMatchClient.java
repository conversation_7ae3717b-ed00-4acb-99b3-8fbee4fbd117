package com.extracme.evcard.membership.face;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.extracme.evcard.membership.third.baidu.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 人脸对比
 * 接口文档： https://cloud.baidu.com/doc/FACE/s/Lk37c1tpf
 */
@Component
@Slf4j
public class BaiduFaceMatchClient {


    @Value("j5rgtVrKpk5y9aETFQwzK9pf")
    private String ak;
    @Value("61BLGR2CUS31xoDU4irSRwKl761wOXvK")
    private String sk;
    @Value("https://aip.baidubce.com/")
    private String baseUrl;
    @Autowired
    private BaiduAccessTokenClient accessTokenClient;

    public static final String FACE_MATCH_URL = "rest/2.0/face/v3/match";

    /**
     * 人脸比对
     */
    public BaseResult<FaceMatchResult> faceMatch(List<FaceMatchRequest> request) {
        // 请求url
        String url = baseUrl + FACE_MATCH_URL;
        try {
            String param = JSON.toJSONString(request);
            String accessToken = getAuthToken();
            if(StringUtils.isBlank(accessToken)) {
                log.error("获取baidubce accessToken失败");
                return null;
            }
            //请求人脸身份认证接口
            log.info("baidu:face/v3/match input={}", param);
            String result = HttpUtil.post(url, accessToken, "application/json", param);
            BaseResult<FaceMatchResult> resp = JSON.parseObject(result, new TypeReference<BaseResult<FaceMatchResult>>() {});
            log.info("baidu:face/v3/match result={}, {}", resp.getError_code(), resp.getError_msg());
            return resp;
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }

    private String getAuthToken(){
        return accessTokenClient.getAuthToken(ak, sk);
    }
}
