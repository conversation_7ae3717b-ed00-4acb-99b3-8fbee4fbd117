package com.extracme.evcard.membership.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Data
public class ShoukalaServiceConfig {
    @Value("${elements.authenticate.shoukala.enkey}")
    private String enkey; //MHsm9XWPGNI4BwD2Ro7gbvjEP3q077kA
    @Value("${elements.authenticate.shoukala.url}")
    private String url; //https://api.80chong.com/jszhc/query
}
