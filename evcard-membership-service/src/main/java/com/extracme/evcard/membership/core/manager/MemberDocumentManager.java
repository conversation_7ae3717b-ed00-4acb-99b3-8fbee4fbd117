package com.extracme.evcard.membership.core.manager;

import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.dao.MemberIdentityDocumentMapper;
import com.extracme.evcard.membership.core.dto.MemberStatusDto;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.dto.MembershipForMmpDTO;
import com.extracme.evcard.membership.core.dto.OperatorDto;
import com.extracme.evcard.membership.core.dto.md.GetOrderCountByStatusRequest;
import com.extracme.evcard.membership.core.dto.md.GetOrderCountByStatusResponse;
import com.extracme.evcard.membership.core.dto.md.OrderCount;
import com.extracme.evcard.membership.core.enums.IdTypeEnum;
import com.extracme.evcard.membership.core.enums.IdentityAuthStatusEnum;
import com.extracme.evcard.membership.core.enums.PostCertSubmitEnum;
import com.extracme.evcard.membership.core.input.SubmitIdCardInput;
import com.extracme.evcard.membership.core.model.MemberIdentityDocument;
import com.extracme.evcard.membership.core.model.MemberIdentityDocumentExample;
import com.extracme.evcard.membership.core.service.md.MdOrderService;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.model.MembershipInfo;
import com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class MemberDocumentManager {
    @Resource
    private MemberIdentityDocumentMapper memberIdentityDocumentMapper;

    @Resource
    private MembershipInfoMapper membershipInfoMapper;

    @Resource
    MemberDocumentManager memberDocumentManager;

    public MemberIdentityDocument getMemberIdentity(String mid) {
        if(StringUtils.isBlank(mid)) {
            return null;
        }
        return memberIdentityDocumentMapper.selectOneByMid(mid);
    }

    public List<MemberIdentityDocument> selectByIdentityNo(String identityNo, List<Integer> idTypes, String mid) {
        if(StringUtils.isBlank(identityNo)) {
            return null;
        }
        MemberIdentityDocumentExample example = new MemberIdentityDocumentExample();
        MemberIdentityDocumentExample.Criteria criteria = example.createCriteria().andIdentityNoEqualTo(identityNo)
                .andIdentityTypeIn(idTypes).andStatusEqualTo(0);
        if(StringUtils.isNotBlank(mid)) {
            criteria.andMidNotEqualTo(mid);
        }
        return memberIdentityDocumentMapper.selectByExample(example);
    }

    public MemberStatusDto selectUserWithSameIdentityNo(String identityNo, List<Integer> idTypes, String mid) {
        if(StringUtils.isBlank(identityNo)) {
            return null;
        }
        // TODO 暂时使用联合查询简单处理，待优化
        // List<MemberIdentityDocument> list = selectByIdentityNo(identityNo, idTypes, mid);
        //根据mid批量查询membership表
        return memberIdentityDocumentMapper.selectOneOtherByIdentityNo(identityNo, idTypes, mid);
    }

    public Integer updateIdCardExpireDate(Long id, SubmitIdCardInput input, Integer authenticationStatus, OperatorDto operator) {
        MemberIdentityDocument record = new MemberIdentityDocument();
        record.setId(id);
        record.setCertInputType(input.getCertInputType());
        record.setExpireType(input.getExpireType());
        record.setExpirationDate(input.getExpirationDate());
        record.setIdentityCardImgUrl(input.getIdcardPicUrl());
        record.setReverseIdentityCardImgUrl(input.getIdcardPicBackUrl());
        if(input.getExpireType().equals(2)) {
            record.setExpirationDate(StringUtils.EMPTY);
        }
        record.setAuthenticationStatus(authenticationStatus);
        record.setUpdateOperId(operator.getOperatorId());
        record.setUpdateOperName(operator.getOperatorName());
        record.setUpdateTime(new Date());
        return memberIdentityDocumentMapper.updateByPrimaryKeySelective(record);
    }

    @Transactional
    public Integer saveIdentityDocument(SubmitIdCardInput input, MembershipBasicInfo member,
                                        Long oriIdentityId, PostCertSubmitEnum nextOpType, OperatorDto operator) {
        MemberIdentityDocument record = new MemberIdentityDocument();
        MembershipInfoWithBLOBs updateMember = new MembershipInfoWithBLOBs();
        record.setMid(input.getMid());
        record.setCertInputType(input.getCertInputType());
        record.setIdentityNo(input.getIdCardNumber());
        record.setIdentityType(input.getIdType());
        record.setName(input.getName());
        record.setExpireType(input.getExpireType());
        record.setIdentityCardImgUrl(input.getIdcardPicUrl());
        record.setReverseIdentityCardImgUrl(input.getIdcardPicBackUrl());
        record.setHoldIdcardPicUrl(input.getHoldIdcardPicUrl());
        record.setFaceRecognitionImgUrl(input.getFacePicUrl());
        record.setExpirationDate(input.getExpirationDate());
        if(!IdTypeEnum.isMainlandId(input.getIdType())) {
            record.setExpireType(2);
        }
        if(record.getExpireType().equals(2)) {
            record.setExpirationDate(StringUtils.EMPTY);
        }
        record.setSubmitAppkey(input.getAppKey());
        record.setSubmitTime(new Date());
        record.setCreateOperId(operator.getOperatorId());
        record.setCreateOperName(StringUtils.abbreviate(operator.getOperatorName(), 20));
        record.setCreateTime(new Date());

        //同步更新证件信息
        updateMember.setName(record.getName());
        updateMember.setPassportNo(record.getIdentityNo());
        updateMember.setIdType(record.getIdentityType());
        updateMember.setIdCardNumber(record.getIdentityNo());
        updateMember.setNational(IdTypeEnum.getByType(record.getIdentityType()).getNational());

        //照片 同步更新到老表
        if (StringUtils.isNotBlank(input.getHoldIdcardPicUrl())) {
            updateMember.setHoldIdcardPicUrl(input.getHoldIdcardPicUrl());
        }
        if (StringUtils.isNotBlank(input.getFacePicUrl())) {
            updateMember.setFaceRecognitionImgUrl(input.getFacePicUrl());
        }

        if(PostCertSubmitEnum.AUTO_AUTH_SUCEESS.equals(nextOpType)) {
            record.setAuthenticationStatus(IdentityAuthStatusEnum.AUTHENTICATED.getValue());
            record.setReviewMode(2);
            record.setReviewTime(new Date());
            record.setReviewRemark("提交资料->自动认证通过");
            record.setReviewIds(StringUtils.EMPTY);
            record.setReviewItems(StringUtils.EMPTY);
            record.setReviewItemNames(StringUtils.EMPTY);
            record.setReviewUser(operator.getOperatorName());
            //更新审核状态 (0:待审核 1：审核通过 2: 审核不通过 3：用户无效 4: 重新审核）
            updateMember.setReviewStatus((short)1);
            //是否仍新旧认证状态为已认证
            updateMember.setAuthenticationStatus(2);
            if(null == member.getIdentityFirstAuthTime()) {
                updateMember.setIdentityFirstAuthTime(new Date());
            }
        } else if(PostCertSubmitEnum.TO_MANUAL_REVIEW.equals(nextOpType)){
            record.setAuthenticationStatus(IdentityAuthStatusEnum.TO_MANUAL_REVIEW.getValue());
            record.setReviewMode(1);
        } else if(PostCertSubmitEnum.TO_FACE_REC.equals(nextOpType)){
            record.setAuthenticationStatus(IdentityAuthStatusEnum.TO_FACE_REC.getValue());
            //去刷脸，更新原身份认证状态为1为刷脸，进行重新刷脸
            updateMember.setAuthenticationStatus(1);
        }
        if(null == record.getName()) {
            record.setName(StringUtils.EMPTY);
        }
        // app5.7.1 之前失效一条，现在失效全部
        //disableIdentityDocument(oriIdentityId, operator);
        memberDocumentManager.disableIdentityDocument2(input.getMid(), operator);
        memberIdentityDocumentMapper.insertSelective(record);

        /**
         * 增加历史会员license_first_auth_time初始化逻辑(补全)
         * 提交证件时触发
         */
        log.info("判断用户是否需要补齐，入参review_status[{}],LicenseFirstAuthTime[{}],mid[{}],authid[{}]",member.getReviewStatus(),member.getLicenseFirstAuthTime(),member.getMid(),member.getAuthId());
        if((member.getReviewStatus().equals(1) && member.getLicenseFirstAuthTime() == null) || hasOrder(member.getMid(),member.getAuthId())) {
            log.info("历史会员license_first_auth_time补齐, mid={}", input.getMid());
            String reviewTime = StringUtils.isNotBlank(member.getReviewTime()) ? member.getReviewTime() : member.getRegTime();
            Date preReviewTime = ComUtil.getDateFromStr(reviewTime, ComUtil.DATE_TYPE4);
            if(preReviewTime == null) {
                preReviewTime = new Date();
            }
            updateMember.setLicenseFirstAuthTime(preReviewTime);
        }
        Long identityRecordId = record.getId();
        updateMember.setMid(input.getMid());
        updateMember.setPkId(member.getPkId());
        updateMember.setIdentityId(identityRecordId);
        membershipInfoMapper.updateByPrimaryKeySelective(updateMember);
        return 0;
    }

    @Resource
    private MdOrderService mdOrderService;

    public boolean hasOrder(String mid, String authId) {
        log.info("mid{},authId{},判断用户是否下过订单开始", mid,authId);
        //门店查询订单
        try {
            GetOrderCountByStatusRequest request = new GetOrderCountByStatusRequest();
            List<Integer> list = Arrays.asList(1, 2, 3, 4, 5, 6, 7);
            request.setMid(mid);
            request.setContractStatus(list);
            GetOrderCountByStatusResponse orderCountByStatus = mdOrderService.getOrderCountByStatus(request);
            if (orderCountByStatus != null) {
                OrderCount data = orderCountByStatus.getData();
                if (data != null && data.getResult() != null && data.getResult() > 0) {
                    log.info("mid{},authId{},判断用户下过门店订单", mid,authId);
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("mid{},authId{},查询门店订单异常{}", mid,authId,e);
        }

        //大库查询订单
        try {
            int orderCount = membershipInfoMapper.getOrderCount(authId);
            if (orderCount > 0) {
                log.info("mid{},authId{},判断用户下过大库订单", mid,authId);
                return true;
            }
        } catch (Exception e) {
            log.error("mid{},authId{},查询大库订单异常{}", mid,authId,e);
        }
        log.info("mid{},authId{},判断用户未下过订单", mid,authId);
        return false;
    }

    /**
     * 新增会员--保存身份证信息到 新表member_identity_document
     *
     * @param membershipDto
     * @param membershipInfo
     */
    public void saveIdentityDocumentFromMmp(MembershipForMmpDTO membershipDto, MembershipInfoWithBLOBs membershipInfo) {
        MemberIdentityDocument record = new MemberIdentityDocument();
        MembershipInfoWithBLOBs updateMember = new MembershipInfoWithBLOBs();
        record.setMid(membershipInfo.getMid());
        record.setIdentityNo(membershipDto.getPassportNo());
        record.setCertInputType(Integer.valueOf(membershipDto.getIdType()));
        record.setName(membershipDto.getName());

        //身份证到期类型
        record.setExpireType(membershipDto.getIdCardExpirationType());

        // 有效期类型为1 ，才设置证件到期日期
        if (membershipDto.getIdCardExpirationType() == 1) {
            record.setExpirationDate(membershipDto.getIdCardExpirationTime());
        }

        record.setIdentityCardImgUrl(ComUtil.splitPicUrl(membershipDto.getIdcardPicUrl()));
        record.setReverseIdentityCardImgUrl(ComUtil.splitPicUrl(membershipDto.getIdCardcBackPicUrl()));
        if (StringUtils.isNotBlank(membershipDto.getFaceRecognitionImgUrl())) {
            record.setFaceRecognitionImgUrl(ComUtil.splitPicUrl(membershipDto.getFaceRecognitionImgUrl()));
        }
        if (StringUtils.isNotBlank(membershipDto.getHoldIdcardPicUrl())) {
            record.setHoldIdcardPicUrl(ComUtil.splitPicUrl(membershipDto.getHoldIdcardPicUrl()));
        }
        //录入方式 2 手动录入
        record.setCertInputType(2);

        /**
         *勾选人脸识别（0：勾选，1：未勾选）
         *  身份认证状态  1未认证(未上传) 2未刷脸 3待认证(待人工认证) 4已认证 5认证不通过
         */
        //没有人脸照片 ，，属于 未刷脸
        if (StringUtils.isBlank(membershipDto.getFaceRecognitionImgUrl())) {
            record.setAuthenticationStatus(2);
        }else{
            record.setAuthenticationStatus(3);
        }
        record.setSubmitTime(new Date());
        //渠道
        record.setSubmitAppkey(BussinessConstants.APP_KEY_EVCARD_MMP);
        record.setReviewItems(BussinessConstants.IDCARD_REVIEW_ITEMS_INIT);
        record.setReviewMode(1);

        record.setCreateOperId(Long.valueOf(membershipDto.getCreateOperId()));
        record.setCreateOperName(StringUtils.abbreviate(membershipDto.getCreateOperName(), 20));
        record.setCreateTime(new Date());
        record.setUpdateOperId(Long.valueOf(membershipDto.getCreateOperId()));
        record.setUpdateOperName(StringUtils.abbreviate(membershipDto.getCreateOperName(), 20));
        record.setUpdateTime(new Date());
        memberIdentityDocumentMapper.insertSelective(record);

        Long identityRecordId = record.getId();
        updateMember.setPkId(membershipInfo.getPkId());
        updateMember.setIdentityId(identityRecordId);
        membershipInfoMapper.updateByPrimaryKeySelective(updateMember);
    }

    public void disableIdentityDocument(Long id, OperatorDto user) {
        if(id == null) {
            return;
        }
        MemberIdentityDocument record = new MemberIdentityDocument();
        record.setId(id);
        record.setStatus(1);
        record.setUpdateOperId(user.getOperatorId());
        record.setUpdateOperName(StringUtils.abbreviate(user.getOperatorName(), 20));
        record.setUpdateTime(new Date());
        memberIdentityDocumentMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 根据mid，将记录 全部失效
     * @param mid
     * @param user
     */
    public void disableIdentityDocument2(String mid, OperatorDto user) {
        if(StringUtils.isBlank(mid)) {
            return;
        }
        try {
            MemberIdentityDocument record = new MemberIdentityDocument();
            record.setStatus(1);
            record.setUpdateOperId(user.getOperatorId());
            record.setUpdateOperName(StringUtils.abbreviate(user.getOperatorName(), 20));
            record.setUpdateTime(new Date());

            MemberIdentityDocumentExample example = new MemberIdentityDocumentExample();
            example.createCriteria().andMidEqualTo(mid).andStatusEqualTo(0);
            int i = memberIdentityDocumentMapper.updateByExampleSelective(record,example);
            log.info("disableIdentityDocument2,mid={}，更新数目i={}",mid,i);
        } catch (Exception e) {
            log.error("disableIdentityDocument2,mid={}，异常",mid,e);
            throw e;
        }
    }

    public void updateReviewStatusAfterFaceRec(String mid, Long identityId, String faceRecUrl, String identityNo,
                                               MembershipBasicInfo member, PostCertSubmitEnum nextOpType, OperatorDto operator) {
        MemberIdentityDocument record = new MemberIdentityDocument();
        MembershipInfoWithBLOBs updateMember = new MembershipInfoWithBLOBs();
        record.setId(identityId);

        //防止 “”覆盖图片
        if (StringUtils.isNotBlank(faceRecUrl)) {
            record.setFaceRecognitionImgUrl(faceRecUrl);
        }
        if (StringUtils.isNotBlank(faceRecUrl)) {
            updateMember.setFaceRecognitionImgUrl(faceRecUrl);
        }
        if(StringUtils.isNotBlank(identityNo)) {
            record.setIdentityNo(identityNo);
        }
        record.setUpdateOperId(operator.getOperatorId());
        record.setUpdateOperName(StringUtils.abbreviate(operator.getOperatorName(), 20));
        record.setUpdateTime(new Date());

        if(PostCertSubmitEnum.AUTO_AUTH_SUCEESS.equals(nextOpType)) {
            record.setAuthenticationStatus(IdentityAuthStatusEnum.AUTHENTICATED.getValue());
            record.setReviewMode(2);
            record.setReviewTime(new Date());
            record.setReviewRemark("人脸识别->自动认证通过");
            record.setReviewIds(StringUtils.EMPTY);
            record.setReviewItems(StringUtils.EMPTY);
            record.setReviewItemNames(StringUtils.EMPTY);
            record.setReviewUser(operator.getOperatorName());
            //更新审核状态 (0:待审核 1：审核通过 2: 审核不通过 3：用户无效 4: 重新审核）
            updateMember.setReviewStatus((short)1);
            //是否仍新旧认证状态为已认证
            updateMember.setAuthenticationStatus(2);
            if(null == member.getIdentityFirstAuthTime()) {
                updateMember.setIdentityFirstAuthTime(new Date());
            }
        } else if(PostCertSubmitEnum.TO_MANUAL_REVIEW.equals(nextOpType)){
            //TODO 是否修改reviewStatus
            record.setAuthenticationStatus(IdentityAuthStatusEnum.TO_MANUAL_REVIEW.getValue());
            record.setReviewMode(1);
        }
        memberIdentityDocumentMapper.updateByPrimaryKeySelective(record);

        updateMember.setMid(mid);
        updateMember.setPkId(member.getPkId());
        updateMember.setIdCardNumber(record.getIdentityNo());
        updateMember.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE4));
        membershipInfoMapper.updateByPrimaryKeySelective(updateMember);
    }

    @Transactional(rollbackFor = Exception.class)
    public void addMemberForMmp(MembershipForMmpDTO membershipForMmpDTO,MembershipInfoWithBLOBs membershipInfo) throws BusinessException {
        Integer num1 = membershipInfoMapper.addMembershipInfoForMmp(membershipInfo);
        if (num1 > 0) {
            //新增会员身份证相关信息 存到新表 member_identity_document
            memberDocumentManager.saveIdentityDocumentFromMmp(membershipForMmpDTO, membershipInfo);
        }else{
            throw new BusinessException("add member fail");
        }
    }
}
