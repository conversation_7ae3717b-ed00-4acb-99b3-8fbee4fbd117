package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.dto.ProvisionNodeDto;
import com.extracme.evcard.membership.core.model.MmpProvisionGroupNode;

import java.util.List;

public interface MmpProvisionGroupNodeMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MmpProvisionGroupNode record);

    int insertSelective(MmpProvisionGroupNode record);

    MmpProvisionGroupNode selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MmpProvisionGroupNode record);

    int updateByPrimaryKey(MmpProvisionGroupNode record);

    List<MmpProvisionGroupNode> selectByGroupId(Long groupId);

    List<ProvisionNodeDto> selectNodesByGroupId(Long groupId);
}