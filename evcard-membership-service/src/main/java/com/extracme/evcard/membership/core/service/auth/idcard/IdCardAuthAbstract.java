package com.extracme.evcard.membership.core.service.auth.idcard;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.OnExceptionContext;
import com.aliyun.openservices.ons.api.SendCallback;
import com.aliyun.openservices.ons.api.SendResult;
import com.aliyun.openservices.ons.api.bean.ProducerBean;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.canstant.OcrChannelConstants;
import com.extracme.evcard.membership.core.dao.ApplyProgressMapper;
import com.extracme.evcard.membership.core.dao.MemberIdentityDocumentMapper;
import com.extracme.evcard.membership.core.dto.AuditIdCardDTO;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.dto.input.AuditIdCardInput;
import com.extracme.evcard.membership.core.enums.MemOperateTypeEnum;
import com.extracme.evcard.membership.core.input.UserOperationLogInput;
import com.extracme.evcard.membership.core.model.ApplyProgress;
import com.extracme.evcard.membership.core.model.MemberIdentityDocument;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.membership.core.service.auth.IAuth;
import com.extracme.evcard.membership.core.service.auth.inner.IdentityCertService;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs;
import com.extracme.evcard.mq.bean.EventEnum;
import com.extracme.evcard.mq.bean.event.MemberAudit;
import com.extracme.evcard.mq.bean.event.MemberInvitation;
import com.extracme.evcard.mq.bean.event.NewMemberAudit;
import com.extracme.evcard.protobuf.ProtobufUtil;
import com.extracme.evcard.rpc.enums.StatusCode;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
public abstract class IdCardAuthAbstract implements IAuth {

    @Resource(name = "producer")
    protected ProducerBean producer;
    @Value("${ons.raw.topic}")
    protected String evcardRawDataTopic;
    @Resource
    protected MembershipInfoMapper membershipInfoMapper;

    @Resource
    protected MemberIdentityDocumentMapper memberIdentityDocumentMapper;

    @Resource
    protected IMemberShipService memberShipService;

    @Resource
    protected ApplyProgressMapper applyProgressMapper;

    @Resource
    protected IdentityCertService identityCertService;

    @Override
    public Pair<Integer, String> auth(Object input) {
        try {
            AuditIdCardInput idCardInput = (AuditIdCardInput) input;
            AuditIdCardDTO auditIdCardDTO = new AuditIdCardDTO();
            auditIdCardDTO.setAuditIdCardInput(idCardInput);

            Pair<Integer, String> result = paramCheck(auditIdCardDTO);
            if (result != null && result.getKey() != 0) {
                return result;
            }

            result = updateDbRecord(auditIdCardDTO);
            if (result != null && result.getKey() != 0) {
                return result;
            }

            messagePush(auditIdCardDTO);

            return result;
        } catch (Exception e) {
            log.error("身份证人工审核异常,入参<{}>,异常<{}>", JSON.toJSONString(input), e);
            return new Pair<>(-1, "身份证人工审核失败，请稍后重试");
        }
    }

    /**
     * 消息 下发
     * 不涉及主流程
     *
     * @param auditIdCardDTO
     */
    protected abstract void messagePush(AuditIdCardDTO auditIdCardDTO);


    /**
     * 操作db
     *
     * @param auditIdCardDTO
     * @return
     */
    protected abstract Pair<Integer, String> updateDbRecord(AuditIdCardDTO auditIdCardDTO);

    /**
     * 业务场景的参数校验
     *
     * @param auditIdCardDTO
     * @return
     */
    protected abstract Pair<Integer, String> serviceCheck(AuditIdCardDTO auditIdCardDTO);

    /**
     * 入参基础校验
     *
     * @param auditIdCardDTO
     * @return
     */
    protected Pair<Integer, String> paramCheck(AuditIdCardDTO auditIdCardDTO) {
        Pair<Integer, String> result = commonParamCheck(auditIdCardDTO);
        if (result != null && result.getKey() != 0) {
            return result;
        }
        return serviceCheck(auditIdCardDTO);
    }

    /**
     * 公共参数 校验
     *
     * @param auditIdCardDTO
     * @return
     */
    private Pair<Integer, String> commonParamCheck(AuditIdCardDTO auditIdCardDTO) {
        AuditIdCardInput idCardInput = auditIdCardDTO.getAuditIdCardInput();
        if (idCardInput == null) {
            return new Pair<>(StatusCode.PARAM_EMPTY.getCode(), StatusCode.PARAM_EMPTY.getMsg());
        }
        String mid = idCardInput.getMid();
        if (StringUtils.isBlank(mid)) {
            return new Pair<>(StatusCode.PARAM_EMPTY.getCode(), "mid" + StatusCode.PARAM_EMPTY.getMsg());
        }

        if (idCardInput.getOperateSourceType() != 0 && idCardInput.getOperateSourceType() != 1) {
            return new Pair<>(StatusCode.ILLEGAL_PARAM.getCode(), "operateSourceType" + StatusCode.ILLEGAL_PARAM.getMsg());
        }

        String operatorId = idCardInput.getOperatorId();
        if (StringUtils.isBlank(operatorId)) {
            return new Pair<>(StatusCode.PARAM_EMPTY.getCode(), "operatorId" + StatusCode.PARAM_EMPTY.getMsg());
        }
        String operatorUserName = idCardInput.getOperatorUserName();
        if (StringUtils.isBlank(operatorUserName)) {
            return new Pair<>(StatusCode.PARAM_EMPTY.getCode(), "operatorUserName" + StatusCode.PARAM_EMPTY.getMsg());
        }

        String reviewItems = idCardInput.getReviewItems();
        if (!checkReviewItems(reviewItems)) {
            return new Pair<>(StatusCode.ILLEGAL_PARAM.getCode(), "reviewItems" + StatusCode.ILLEGAL_PARAM.getMsg());
        }

        // 查询membershipInfo
        MembershipBasicInfo userBasicInfo = membershipInfoMapper.getUserBasicInfoByMid(mid);
        if (userBasicInfo == null) {
            return new Pair<>(StatusCode.USER_INFO_NO_EXIST.getCode(), StatusCode.USER_INFO_NO_EXIST.getMsg());
        }
        auditIdCardDTO.setMembershipBasicInfo(userBasicInfo);

        if (0 != userBasicInfo.getAccountStatus()) {
            return new Pair<>(-1, "会员状态已变更，请刷新后重试");
        }

        Long identityId = userBasicInfo.getIdentityId();
        MemberIdentityDocument memberIdentityDocument = memberIdentityDocumentMapper.selectByPrimaryKey(identityId);
        if (memberIdentityDocument == null) {
            return new Pair<>(-1, "memberIdentityDocument 为空");
        }

        // 认证状态 必须为待认证
        if (3 != memberIdentityDocument.getAuthenticationStatus()) {
            return new Pair<>(-1, "身份证认证状态 不为待认证");
        }
        auditIdCardDTO.setMemberIdentityDocument(memberIdentityDocument);


        // 判断用户是否是新用户
        if (membershipInfoMapper.checkHavecard(userBasicInfo.getAuthId()) == 1) {
            auditIdCardDTO.setNewUser(true);
        } else {
            auditIdCardDTO.setNewUser(false);
        }
        auditIdCardDTO.setOperaterDate(new Date());

        return new Pair<>(0, OcrChannelConstants.IDCARD_AUDIT_OK_MESSAGE);
    }

    public Boolean checkReviewItems(String reviewItems) {
        if (StringUtils.isBlank(reviewItems)) {
            return false;
        }
        reviewItems = reviewItems.trim();
        if (reviewItems.length() != 7) {
            return false;
        }

        for (char c : reviewItems.toCharArray()) {
            if (c != '0' && c != '1' && c != '2') {
                return false;
            }
        }
        return true;
    }

    /**
     * 更新账号申请进度
     *
     * @param auditIdCardDTO
     * @param progressContent
     */
    public void applyProgress(AuditIdCardDTO auditIdCardDTO, String progressContent) {

        AuditIdCardInput auditIdCardInput = auditIdCardDTO.getAuditIdCardInput();
        MembershipBasicInfo membershipBasicInfo = auditIdCardDTO.getMembershipBasicInfo();
        ApplyProgress progress = new ApplyProgress();
        progress.setAuthId(membershipBasicInfo.getAuthId());
        progress.setProgressContent(progressContent);
        progress.setCreatedUser(auditIdCardInput.getOperatorUserName());
        progress.setProgressTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE1));
        applyProgressMapper.addApplyProgress(progress);
    }

    /**
     * @param auditIdCardDTO
     * @param type           (-1 资料不全  0:待审核 1：审核通过 2: 审核不通过 3：用户无效 4:重新审核）
     * @return
     */
    public MembershipInfoWithBLOBs getUpdatedMembershipInfo(AuditIdCardDTO auditIdCardDTO, int type) {
        AuditIdCardInput auditIdCardInput = auditIdCardDTO.getAuditIdCardInput();
        MembershipBasicInfo membershipBasicInfo = auditIdCardDTO.getMembershipBasicInfo();
        //membershipInfoMapper 更新
        MembershipInfoWithBLOBs membershipInfo = new MembershipInfoWithBLOBs();
        //主键
        membershipInfo.setPkId(membershipBasicInfo.getPkId());

        //更新时间
        try {
            membershipInfo.setUpdatedTime(ComUtil.getFormatDate(auditIdCardDTO.getOperaterDate(), ComUtil.DATE_TYPE3));
        } catch (Exception e) {
            membershipInfo.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
        }

        //更新操作人
        membershipInfo.setUpdatedUser(auditIdCardInput.getOperatorUserName());
        //审核状态
        membershipInfo.setReviewStatus((short) type);

        //身份认证状态 0待认证(未认证) 1待认证(未刷脸上传) 2认证通过 3认证不通过
        if (type == 1) {
            membershipInfo.setAuthenticationStatus(2);
        } else if (type == 2) {
            membershipInfo.setAuthenticationStatus(3);
        }
        membershipInfo.setReviewUser(auditIdCardInput.getOperatorUserName());
        return membershipInfo;
    }

    /**
     * @param auditIdCardDTO
     * @param type           1未认证(未上传) 2未刷脸 3待认证(待人工认证) 4已认证 5认证不通过
     * @return
     */
    public MemberIdentityDocument getUpdatedMemberIdentityDocument(AuditIdCardDTO auditIdCardDTO, int type) {
        AuditIdCardInput auditIdCardInput = auditIdCardDTO.getAuditIdCardInput();
        MemberIdentityDocument memberIdentityDocument = auditIdCardDTO.getMemberIdentityDocument();

        MemberIdentityDocument updateMemberIdentityDocument = new MemberIdentityDocument();
        updateMemberIdentityDocument.setId(memberIdentityDocument.getId());

        //认证状态
        updateMemberIdentityDocument.setAuthenticationStatus(type);
        //更新记录人的 id
        updateMemberIdentityDocument.setUpdateOperId(Long.valueOf(auditIdCardInput.getOperatorId()));
        updateMemberIdentityDocument.setUpdateOperName(auditIdCardInput.getOperatorUserName());
        //自动审核
        updateMemberIdentityDocument.setReviewMode(1);
        //审核人
        updateMemberIdentityDocument.setReviewUser(auditIdCardInput.getOperatorUserName());
        //审核时间
        updateMemberIdentityDocument.setReviewTime(auditIdCardDTO.getOperaterDate());
        //审核项审核结果
        if (StringUtils.isNotBlank(auditIdCardInput.getReviewItems())) {
            updateMemberIdentityDocument.setReviewItems(auditIdCardInput.getReviewItems());
        }

        //重新审核 或者 已认证 不通过原因相关字段 置空
        if (type == 3 || type == 4) {
            updateMemberIdentityDocument.setReviewIds("");
            updateMemberIdentityDocument.setReviewRemark("");
            updateMemberIdentityDocument.setReviewItemNames("");
        }

        //更新时间
        updateMemberIdentityDocument.setUpdateTime(auditIdCardDTO.getOperaterDate());
        return updateMemberIdentityDocument;
    }


    /**
     * 生成 日志对象
     *
     * @param auditIdCardDTO
     * @param operatorContent
     * @return
     */
    public UserOperationLogInput getLogRecord(AuditIdCardDTO auditIdCardDTO, String operatorContent, MemOperateTypeEnum typeEnum) {
        MembershipBasicInfo membershipBasicInfo = auditIdCardDTO.getMembershipBasicInfo();
        AuditIdCardInput auditIdCardInput = auditIdCardDTO.getAuditIdCardInput();

        //写入会员操作日志
        UserOperationLogInput operationLogInput = new UserOperationLogInput();
        operationLogInput.setUserId(membershipBasicInfo.getPkId());
        operationLogInput.setAuthId(membershipBasicInfo.getAuthId());
        operationLogInput.setMembershipType(0);
        operationLogInput.setOperationType(typeEnum.getCode());
        operationLogInput.setOperationContent(operatorContent);
        // 用RefKey1 存 来源
        if (auditIdCardInput.getOperateSourceType() == 0) {
            operationLogInput.setRefKey1(BussinessConstants.APP_KEY_EVCARD_MMP);
        } else if (auditIdCardInput.getOperateSourceType() == 1) {
            operationLogInput.setRefKey1("mdzcrz");
        }
        operationLogInput.setOperatorId(Long.valueOf(auditIdCardInput.getOperatorId()));
        operationLogInput.setOperator(auditIdCardInput.getOperatorUserName());
        operationLogInput.setOperationTime(auditIdCardDTO.getOperaterDate());
        return operationLogInput;
    }


    /**
     * 往会员审核旧的mq 继续发消息
     *
     * @param auditIdCardDTO
     * @param reviewStatus   审核状态(0:待审核 1：审核通过 2: 审核不通过 3：用户无效 4:重新审核 5: 未完成人脸识别）
     * @return
     */
    public Boolean pushOldMemberMq(AuditIdCardDTO auditIdCardDTO, int reviewStatus) {
        MembershipBasicInfo membershipBasicInfo = auditIdCardDTO.getMembershipBasicInfo();
        AuditIdCardInput auditIdCardInput = auditIdCardDTO.getAuditIdCardInput();

        // 保持之前的  旧tag的mq MEMBER_AUDIT
        MemberAudit audit = new MemberAudit();
        audit.setAuthId(membershipBasicInfo.getAuthId());
        audit.setMobilePhone(membershipBasicInfo.getMobilePhone());
        audit.setOptUser(auditIdCardInput.getOperatorUserName());
        audit.setNewUser(auditIdCardDTO.getNewUser() ? 1 : 0);
        audit.setReviewStatus(String.valueOf(reviewStatus));
        if (reviewStatus == 2) {
            audit.setReviewRemark(auditIdCardInput.getReviewRemark());
        }
        byte[] messageBody = ProtobufUtil.serializeProtobuf(audit);
        Message msg = new Message(evcardRawDataTopic, EventEnum.MEMBER_AUDIT.getTag(), messageBody);
        producer.sendAsync(msg, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                log.info(EventEnum.MEMBER_AUDIT.getTag() + "发送mq成功");
            }

            @Override
            public void onException(OnExceptionContext onExceptionContext) {
                log.error(EventEnum.MEMBER_AUDIT.getTag() + "发送mq失败，失败原因<{}>", onExceptionContext);
            }
        });


        return true;
    }

    /**
     * tag:MEMBER_AUDIT_IDCARD
     * 新会员审核 发送mq消息
     *
     * @param auditIdCardDTO
     * @param auditType            认证类别   0 身份证照人工审核   1:驾照人工审核
     * @param reviewStatus         审核状态(0:待审核 1：审核通过 2: 审核不通过 3：用户无效 4:重新审核 5: 未完成人脸识别）
     * @param authenticationStatus 认证状态
     *                             身份证     1未认证(未上传) 2未刷脸 3待认证(待人工认证) 4已认证 5认证不通过
     *                             驾驶证     1:未认证 2:待认证 3:已认证 4:认证不通过
     * @return
     */
    public Boolean pushNewMemberMq(AuditIdCardDTO auditIdCardDTO, Integer auditType, Integer reviewStatus, Integer authenticationStatus) {
        MembershipBasicInfo member = auditIdCardDTO.getMembershipBasicInfo();
        AuditIdCardInput auditIdCardInput = auditIdCardDTO.getAuditIdCardInput();

        NewMemberAudit newMemberAudit = new NewMemberAudit();
        //会员id
        newMemberAudit.setMid(member.getMid());
        newMemberAudit.setAuthId(member.getAuthId());
        //
        newMemberAudit.setMobilePhone(member.getMobilePhone());
        //操作人姓名
        newMemberAudit.setOperatorUserName(auditIdCardInput.getOperatorUserName());

        //操作人id
        newMemberAudit.setOperatorId(auditIdCardInput.getOperatorId());
        //是否是新用户
        newMemberAudit.setNewUserFlag(auditIdCardDTO.getNewUser());

        //审核状态(0:待审核 1：审核通过 2: 审核不通过 3：用户无效 4:重新审核 5: 未完成人脸识别）
        newMemberAudit.setReviewStatus(String.valueOf(reviewStatus));

        //失败原因
        if (reviewStatus == 2) {
            newMemberAudit.setReviewRemark(auditIdCardInput.getReviewRemark());
        }

        newMemberAudit.setReviewItems(auditIdCardInput.getReviewItemIds());
        newMemberAudit.setReviewTime(auditIdCardDTO.getOperaterDate());

        //认证状态
        newMemberAudit.setAuthenticationStatus(authenticationStatus);
        //认证类别
        newMemberAudit.setAuditType(auditType);
        newMemberAudit.setMemberType(member.getMembershipType());

        newMemberAudit.setOperateSourceType(auditIdCardInput.getOperateSourceType()); // 调用方 0：会员系统 1：门店小程序

        byte[] messageBody = ProtobufUtil.serializeProtobuf(newMemberAudit);
        Message msg = new Message(evcardRawDataTopic, EventEnum.MEMBER_AUDIT_IDCARD.getTag(), messageBody);
        producer.sendAsync(msg, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                log.info(EventEnum.MEMBER_AUDIT_IDCARD.getTag() + "发送mq成功");
            }

            @Override
            public void onException(OnExceptionContext onExceptionContext) {
                log.error(EventEnum.MEMBER_AUDIT_IDCARD.getTag() + "发送mq失败，失败原因<{}>", onExceptionContext);
            }
        });
        return true;
    }

    /**
     * 推送好友邀请成功事件
     *
     * @param auditIdCardDTO
     * @return
     */
    public boolean pushMemberInvitation(AuditIdCardDTO auditIdCardDTO) {
        MembershipBasicInfo membershipBasicInfo = auditIdCardDTO.getMembershipBasicInfo();
        AuditIdCardInput auditIdCardInput = auditIdCardDTO.getAuditIdCardInput();
        String authId = membershipBasicInfo.getAuthId();
        String mobilePhone = membershipBasicInfo.getMobilePhone();
        // 推送邀请事件服务，会员邀请好友送券，推向会员管理系统中RawMemberListener消费 update by Elin date 2018/03/01
        log.debug("发放会员邀请好友送券事件推送开始----authId:" + authId + " ,mobilePhone" + mobilePhone);
        MemberInvitation memberInvitation = new MemberInvitation();
        memberInvitation.setAuthId(authId);
        memberInvitation.setMobilePhone(mobilePhone);
        // memberInvitation.setOptOrgId(optOrgId);
        memberInvitation.setCreateUser(auditIdCardInput.getOperatorUserName());
        memberInvitation.setCreatedTime(new Date());
        byte[] messageBody = ProtobufUtil.serializeProtobuf(memberInvitation);
        Message msg = new Message(evcardRawDataTopic, EventEnum.MEMBER_INVITATION.getTag(), messageBody);
        producer.sendAsync(msg, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                log.info(EventEnum.MEMBER_INVITATION.getTag() + "发送mq成功");
            }

            @Override
            public void onException(OnExceptionContext onExceptionContext) {
                log.error(EventEnum.MEMBER_INVITATION.getTag() + "发送mq失败，失败原因<{}>", onExceptionContext);
            }
        });
        return true;
    }

}
