package com.extracme.evcard.membership.core.service;

import com.alibaba.fastjson.JSONObject;
import com.extracme.evcard.membership.common.HttpUtils;
import com.extracme.evcard.membership.core.input.MmpWeChatRequestDto;
import com.extracme.evcard.membership.core.input.MmpWeChatResponse;
import com.extracme.evcard.redis.JedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/9/2 11:04
 * @Description: 微信对接相关接口
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class WeChatServiceImpl implements WeChatService{
    @Value("${mmp.wechat.appid}")
    private String wechatAppid;
    @Value("${mmp.wechat.secret}")
    private String wechatSecret;
    @Value("${mmp.wechat.baseUrl}")
    private String wechatBaseUrl;
    @Value("${mmp.wechat.getToken}")
    private String getTokenUrl;
    @Value("${mmp.wechat.getwxacodeunlimit}")
    private String getwxacodeunlimitUrl;
    @Value("${mmp.wechat.genUrlLink}")
    private String getGenUrlLink;

    private final String REDIS_TOKEN_KEY = "mmp_wechat_token";
    private final Integer TRY_NUM = 3;


    /**
     * 获取token
     * refresh  是否强制刷新
     * @return
     */
    @Override
    public String getToken(Boolean refresh) {
        refresh = (refresh == null)?Boolean.FALSE : refresh;
        // 整理url及入参
        String token = JedisUtil.get(REDIS_TOKEN_KEY);
        Integer expireTime = null;
        if (StringUtils.isBlank(token) || refresh){
            String url = getTokenUrl + "?grant_type=client_credential&" +
                    "appid=" + wechatAppid + "&" +
                    "secret=" + wechatSecret;
            // 查询微信token
            JSONObject httpResponse = HttpUtils.httptGet(url);
            if (null != httpResponse && StringUtils.isNotBlank(httpResponse.getString("access_token"))) {
                token = httpResponse.getString("access_token");
                expireTime = Integer.valueOf(httpResponse.getString("expires_in"));
            }
            JedisUtil.setCover(REDIS_TOKEN_KEY, token, expireTime);
        }
        return token;
    }

    /**
     * 获取不限制的小程序码
     * @param page 默认是主页，页面 page
     * @param scene 最大32个可见字符，参数
     * @param tryNum 第几次尝试，第一次请传0或null
     *
     */
    @Override
    public MmpWeChatResponse getwxacodeunlimit(String page, String scene, Integer tryNum, String dir, String name) {
        tryNum = (tryNum == null) ? 0 : tryNum;
        Boolean refreshFlag = Boolean.FALSE;
        if (tryNum > 0){
            refreshFlag =Boolean.TRUE;
        }
        String token = getToken(refreshFlag);
        String url = getwxacodeunlimitUrl + token;
        Map<String,Object> param = new HashMap<>();
        param.put("page",page);
        param.put("scene",scene);
        param.put("check_path",true);
        param.put("env_version","release");
        param.put("is_hyaline", true);

        /**
         * 错误码	错误描述	解决方案
         * -1	    system error	系统繁忙，此时请开发者稍候再试
         * 40001	invalid credential  access_token isinvalid or not latest	获取 access_token 时 AppSecret 错误，或者 access_token 无效。请开发者认真比对 AppSecret 的正确性，或查看是否正在为恰当的公众号调用接口
         * 40129	invalid scene	最大32个可见字符，只支持数字，大小写英文以及部分特殊字符：!#$&'()*+,/:;=?@-._~，其它字符请自行编码为合法字符（因不支持%，中文无法使用 urlencode 处理，请使用其他编码方式）
         * 41030	invalid page	page路径不正确：根路径前不要填加 /，不能携带参数（参数请放在scene字段里），需要保证在现网版本小程序中存在，与app.json保持一致。设置check_path=false可不检查page参数。
         * 85096	not allow include scancode_time field	scancode_time为系统保留参数，不允许配置
         * 40097	invalid args	参数错误
         * 40169	invalid length for scene  or thedata is not json string	scene 不合法
         */
        JSONObject result = HttpUtils.httpJpgPostWithJSON(url, param, dir, name);


        MmpWeChatResponse mmpWeChatResponse = new MmpWeChatResponse();
        String pictureUrl = result.getString("pictureUrl");
        if (StringUtils.isNotBlank(pictureUrl)){
            mmpWeChatResponse.setWeChatQrUrl(result.getString("pictureUrl"));
        }else {
            mmpWeChatResponse.setErrcode(Integer.valueOf(result.getString("errcode")));
            mmpWeChatResponse.setErrmsg(result.getString("errmsg"));
        }
        if (mmpWeChatResponse.getErrcode() != null && mmpWeChatResponse.getErrcode() == 40001 && tryNum < TRY_NUM ){
            //40001代表token失效，重新生成token再次尝试
            getwxacodeunlimit(page,scene,tryNum+1, dir, name);
        }
        return mmpWeChatResponse;
    }

    /**
     * 获取小程序链接
     * @param mmpWeChatRequestDto
     */
    @Override
    public MmpWeChatResponse getgenerateUrllink(MmpWeChatRequestDto mmpWeChatRequestDto) {
        Integer tryNum = mmpWeChatRequestDto.getTryNum();
        tryNum = (tryNum == null) ? 0 : tryNum;
        Boolean refreshFlag = Boolean.FALSE;
        if (tryNum > 0){
            refreshFlag =Boolean.TRUE;
        }
        String token = getToken(refreshFlag);
        String url = getGenUrlLink + token;

        //获取30天的时间戳
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 30);
        Date date = calendar.getTime();
        // 转换为Unix时间戳
        long timestamp = date.getTime() / 1000;

        Map<String,Object> param = new HashMap<>();
        param.put("path",mmpWeChatRequestDto.getPath());
        param.put("query",mmpWeChatRequestDto.getQuery());
        param.put("env_version","release");
        if (mmpWeChatRequestDto.getExpireType() == null || mmpWeChatRequestDto.getExpireType() == 0){
            //默认值0.小程序 URL Link 失效类型，失效时间：0，失效时间最长为30天，unix时间戳格式
            param.put("expire_type",0);
            param.put("expire_time",mmpWeChatRequestDto.getExpireTime() == null ? timestamp: mmpWeChatRequestDto.getExpireTime());

        }else if (mmpWeChatRequestDto.getExpireType() != null || mmpWeChatRequestDto.getExpireType() == 1){
            param.put("expire_type",1);
            param.put("expire_interval",mmpWeChatRequestDto.getExpireInterval() == null ? 30 : mmpWeChatRequestDto.getExpireInterval());
        }
        JSONObject result = HttpUtils.httpPostWithJSON(url, param);

        MmpWeChatResponse mmpWeChatResponse = new MmpWeChatResponse();
        mmpWeChatResponse.setGenerateUrllink(result.getString("url_link"));
        mmpWeChatResponse.setErrcode(Integer.valueOf(result.getString("errcode")));
        mmpWeChatResponse.setErrmsg(result.getString("errmsg"));

        if (mmpWeChatResponse.getErrcode() != null && mmpWeChatResponse.getErrcode() == 40001 && tryNum < TRY_NUM ){
            //40001代表token失效，重新生成token再次尝试
            mmpWeChatRequestDto.setTryNum(tryNum + 1);
            getgenerateUrllink(mmpWeChatRequestDto);
        }
        return mmpWeChatResponse;
    }

}
