package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.model.MmpShortLinkManagement;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
public interface MmpShortLinkManagementMapper {

    int deleteByPrimaryKey(Long id);

    Long insert(MmpShortLinkManagement record);

    int insertSelective(MmpShortLinkManagement record);

    MmpShortLinkManagement selectByPrimaryKey(Long id);

    int updateByDto(MmpShortLinkManagement record);

    int updateByPrimaryKey(MmpShortLinkManagement record);

    List<MmpShortLinkManagement> selectByKeys(String shortLinkName, String shortLinkUrl, String originalUrl, String randomCode, Date failureTime, Integer status);
}