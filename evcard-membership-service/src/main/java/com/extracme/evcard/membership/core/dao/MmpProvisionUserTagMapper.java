package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.dto.ProvisionUserTagDto;
import com.extracme.evcard.membership.core.model.MmpProvisionUserTag;
import org.apache.ibatis.annotations.Param;

public interface MmpProvisionUserTagMapper {

    int deleteByPrimaryKey(Long id);

    int insert(MmpProvisionUserTag record);

    int insertSelective(MmpProvisionUserTag record);

    MmpProvisionUserTag selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MmpProvisionUserTag record);

    int updateByPrimaryKey(MmpProvisionUserTag record);

    ProvisionUserTagDto countTagsByNodeId(@Param("nodeId") String nodeId, @Param("provisionType") int type);

    void removeTag(@Param("id") Long tagId, @Param("authId") String authId);
}