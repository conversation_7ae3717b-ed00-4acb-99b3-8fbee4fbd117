package com.extracme.evcard.membership.core.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.extracme.evcard.membership.common.AgencyIdUtils;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.dao.AgencyInfoBlacklistMapper;
import com.extracme.evcard.membership.core.dao.AgencyInfoMapper;
import com.extracme.evcard.membership.core.dao.AppKeyManagerMapper;
import com.extracme.evcard.membership.core.dao.MmpOperationLogMapper;
import com.extracme.evcard.membership.core.dao.OrgInfoMapper;
import com.extracme.evcard.membership.core.dto.AgencyInfoBlackDTO;
import com.extracme.evcard.membership.core.dto.AgencyInfoDto;
import com.extracme.evcard.membership.core.dto.AppKeyManagerDTO;
import com.extracme.evcard.membership.core.dto.MmpOperationLogDTO;
import com.extracme.evcard.membership.core.dto.OrgInfoDto;
import com.extracme.evcard.membership.core.dto.input.AgencyInfoInput;
import com.extracme.evcard.membership.core.exception.AgencyException;
import com.extracme.evcard.membership.core.input.QueryAgencyInfoBlackConditionInput;
import com.extracme.evcard.membership.core.input.QueryAgencyInfoInput;
import com.extracme.evcard.membership.core.input.QueryAgencyListConditionInput;
import com.extracme.evcard.membership.core.input.QueryAgencyOperationLogInput;
import com.extracme.evcard.membership.core.model.AgencyInfo;
import com.extracme.evcard.membership.core.model.AgencyInfoBlacklist;
import com.extracme.evcard.membership.core.model.AppKeyManager;
import com.extracme.evcard.membership.core.model.MmpOperationLog;
import com.extracme.evcard.membership.core.model.OrgInfo;
import com.extracme.evcard.rpc.dto.Page;
import com.extracme.evcard.rpc.dto.PageBeanDto;
import com.extracme.evcard.rpc.util.DateType;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;

/**
 * <p>
 * 基础信息服务（企业 、 机构 、省市区、字典数据等等）
 * </p>
 *
 * <AUTHOR>
 * @date 2019/3/7
 */
@Service("baseInfoService")
public class BaseInfoServiceImpl implements IBaseInfoService {

    private static final Logger logger = LoggerFactory.getLogger(BaseInfoServiceImpl.class);

    @Autowired
    private AgencyInfoMapper agencyInfoMapper;

    @Autowired
    private OrgInfoMapper orgInfoMapper;

    @Autowired
    private MmpOperationLogMapper mmpOperationLogMapper;

    @Autowired
    private AgencyInfoBlacklistMapper agencyInfoBlacklistMapper;

    @Autowired
    private AppKeyManagerMapper appKeyManagerMapper;

    Cache<String, OrgInfoDto> orgInfoDtoMap = CacheBuilder.newBuilder().maximumSize(1000).expireAfterWrite(30,TimeUnit.MINUTES).build();
	Cache<String, AgencyInfoDto> agencyInfoDtoMap = CacheBuilder.newBuilder().maximumSize(1000).expireAfterWrite(30,TimeUnit.MINUTES).build();
	
    @Override
    public OrgInfoDto queryOrgInfoByOrgId(String orgId) {
        OrgInfoDto orgInfoDto = null;
        if(StringUtils.isNotBlank(orgId)){
            if(orgInfoDtoMap.getIfPresent(orgId) != null){
                orgInfoDto = orgInfoDtoMap.getIfPresent(orgId);
            }else{
                OrgInfo orgInfo = orgInfoMapper.queryOrgInfoByOrgId(orgId);
                if(orgInfo != null){
                    orgInfoDto = new OrgInfoDto();
                    BeanUtils.copyProperties(orgInfo,orgInfoDto);
                    orgInfoDtoMap.put(orgId,orgInfoDto);
                }
            }
        }
        return orgInfoDto;
    }

    @Override
    public AgencyInfoDto queryAgencyInfoByAgencyId(String agencyId) {
        AgencyInfoDto agencyInfoDto = null;
        if(StringUtils.isNotBlank(agencyId)){
            if(agencyInfoDtoMap.getIfPresent(agencyId) != null ){
                agencyInfoDto = agencyInfoDtoMap.getIfPresent(agencyId);
            }else{
                AgencyInfo agencyInfo = agencyInfoMapper.queryAgencyInfoByAgencyId(agencyId);
                if(agencyInfo != null ){
                    agencyInfoDto = new AgencyInfoDto();
                    BeanUtils.copyProperties(agencyInfo,agencyInfoDto);
                    agencyInfoDtoMap.put(agencyId,agencyInfoDto);
                }
            }
        }
        return agencyInfoDto;
    }

    @Override
    public void createAgencyInfo(AgencyInfoInput agencyInfoInput) throws AgencyException{
        agencyInfoInput.setAgencyName(agencyInfoInput.getAgencyName().trim());
        if(agencyInfoMapper.countByAgencyName(agencyInfoInput.getAgencyName(),null) > 0){
            throw AgencyException.EXIST_AGENCY_NAME;
        }
        AgencyInfo record = new AgencyInfo();
        BeanUtils.copyProperties(agencyInfoInput,record);
        //获取最新的agencyId 并生成下一个agencyId
        String maxAgencyId = agencyInfoMapper.getMaxAgencyId();
        String last2Digit = maxAgencyId.substring(maxAgencyId.length() - 2, maxAgencyId.length());
        String newAgencyId = maxAgencyId.substring(0, maxAgencyId.length() - 2) + AgencyIdUtils.getNextNodeId(last2Digit);
        record.setAgencyId(newAgencyId);
        //运营公司信息
        OrgInfoDto orgInfoDto = this.queryOrgInfoByOrgId(agencyInfoInput.getOrgId());
        record.setOrgName(orgInfoDto != null ? orgInfoDto.getOrgName() : StringUtils.EMPTY);
        if(StringUtils.isBlank(record.getCreatedUser())){
            record.setCreatedUser(BussinessConstants.SYSTEM);
        }
        record.setUpdatedUser(record.getCreatedUser());
        if(StringUtils.isBlank(record.getCreatedTime())){
            record.setCreatedTime(ComUtil.getSystemDate(DateType.DATE_TYPE3));
        }
        if(record.getCooperateStatus() == null){
            record.setCooperateStatus(0);
        }
        record.setUpdatedTime(record.getCreatedTime());
        try {
            agencyInfoMapper.insertSelective(record);
            MmpOperationLog mmpOperationLog = new MmpOperationLog();
            mmpOperationLog.setAgencyId(newAgencyId);
            mmpOperationLog.setCreateOperName(record.getCreatedUser());
            mmpOperationLog.setUpdateOperName(record.getCreatedUser());
            mmpOperationLog.setOperationTime(new Date());
            mmpOperationLog.setOperationRemark("新增");
            mmpOperationLog.setCreateOperId(-1L);
            mmpOperationLog.setUpdateOperId(-1L);
            mmpOperationLog.setCreateTime(new Date());
            mmpOperationLog.setUpdateTime(new Date());
            mmpOperationLogMapper.insertSelective(mmpOperationLog);
        }catch (Exception e){
            logger.error(e.getMessage(),e);
            throw AgencyException.CREATE_AGENCY_FAILED;
        }
    }

    @Override
    public void createAgencyInfoAndBegin(AgencyInfoInput agencyInfoInput) throws AgencyException{
        agencyInfoInput.setCooperateStatus(1);
        this.createAgencyInfo(agencyInfoInput);
    }

    @Override
    public void modifyAgencyInfo(AgencyInfoInput agencyInfoInput) throws AgencyException{
        String agencyId = agencyInfoInput.getAgencyId();
        AgencyInfoDto agencyInfoDto = this.queryAgencyInfoByAgencyId(agencyId);
        if(agencyInfoDto == null){
           throw AgencyException.NOT_EXIST_AGENCY;
        }
        if(StringUtils.isNotBlank(agencyInfoInput.getAgencyName())){
            agencyInfoInput.setAgencyName(agencyInfoInput.getAgencyName().trim());
            if(agencyInfoMapper.countByAgencyName(agencyInfoInput.getAgencyName(),agencyId) > 0){
                throw AgencyException.EXIST_AGENCY_NAME;
            }
        }
        AgencyInfo record = new AgencyInfo();
        BeanUtils.copyProperties(agencyInfoInput,record);
        if(StringUtils.isNotBlank(agencyInfoInput.getOrgId())){
            OrgInfoDto orgInfoDto = this.queryOrgInfoByOrgId(agencyInfoInput.getOrgId());
            record.setOrgName(orgInfoDto != null ? orgInfoDto.getOrgName() : StringUtils.EMPTY);
        }
        if(StringUtils.isBlank(record.getUpdatedUser())){
            record.setUpdatedUser(BussinessConstants.SYSTEM);
        }
        if(StringUtils.isBlank(record.getUpdatedTime())){
            record.setUpdatedTime(ComUtil.getSystemDate(DateType.DATE_TYPE3));
        }
        try {
            int i = agencyInfoMapper.updateByPrimaryKeySelective(record);
            if(i == 0){
                throw AgencyException.UPDATE_AGENCY_FAILED;
            }
            MmpOperationLog mmpOperationLog = new MmpOperationLog();
            mmpOperationLog.setAgencyId(agencyInfoInput.getAgencyId());
            mmpOperationLog.setOperationUserName(record.getUpdatedUser());
            mmpOperationLog.setCreateOperName(record.getUpdatedUser());
            mmpOperationLog.setUpdateOperName(record.getUpdatedUser());
            mmpOperationLog.setOperationTime(new Date());
            mmpOperationLog.setCreateTime(new Date());
            mmpOperationLog.setUpdateTime(new Date());
            mmpOperationLog.setCreateOperId(-1L);
            mmpOperationLog.setUpdateOperId(-1L);
            String operationRemark = StringUtils.EMPTY;
            if(agencyInfoInput.getAgencyName() != null && !agencyInfoInput.getAgencyName().equals(agencyInfoDto.getAgencyName())){
                operationRemark = "机构名称由 "+ agencyInfoDto.getAgencyName() + " 改为 " + record.getAgencyName() + ";";
            }
            if(agencyInfoInput.getOrgId() != null && !agencyInfoInput.getOrgId().equals(agencyInfoDto.getOrgId())){
                operationRemark =  operationRemark + "运营机构由 "+ agencyInfoDto.getOrgName() + " 改为 " + record.getOrgName() + ";";
            }
            if(agencyInfoInput.getCooperateStatus() != null){
                if(agencyInfoInput.getCooperateStatus() == 2){
                    operationRemark = "禁用";
                }
                if(agencyInfoInput.getCooperateStatus() == 1){
                    operationRemark = "恢复";
                }
            }
            mmpOperationLog.setOperationRemark(operationRemark);
            mmpOperationLogMapper.insertSelective(mmpOperationLog);
        }catch (Exception e){
            logger.error(e.getMessage(),e);
            if(e instanceof AgencyException){
                throw e;
            }
            throw AgencyException.CREATE_AGENCY_FAILED;
        }

    }

    @Override
    public void modifyAgencyCooperationStatus(String agencyId,boolean isStop, String updatedUser, String updatedTime) throws AgencyException {
        AgencyInfoInput agencyInfoInput = new AgencyInfoInput();
        agencyInfoInput.setAgencyId(agencyId);
        agencyInfoInput.setCooperateStatus(isStop ? 2 : 1);
        agencyInfoInput.setUpdatedUser(updatedUser);
        agencyInfoInput.setUpdatedTime(updatedTime);
        this.modifyAgencyInfo(agencyInfoInput);
    }

    @Override
    public PageBeanDto<AgencyInfoDto> queryAgencyInfoPage(QueryAgencyInfoInput queryAgencyInfoInput) {
        String orgId = queryAgencyInfoInput.getOrgId();
        String agencyName = queryAgencyInfoInput.getAgencyName();
        Integer cooperateStatus = queryAgencyInfoInput.getCooperateStatus();
        Page page = queryAgencyInfoInput.getPage();
        if(page == null){
            page = new Page(1,20);
        }
        if(page.getCountFlag()){
            int num = agencyInfoMapper.countAgencyInfoNum(orgId, agencyName, cooperateStatus);
            page.setCount(num);
        }
        List<AgencyInfo> agencyInfoList = agencyInfoMapper.queryAgencyInfoByCondition(orgId, agencyName, cooperateStatus, page);
        List<AgencyInfoDto> list = new ArrayList<>(agencyInfoList.size());
        if(CollectionUtils.isNotEmpty(agencyInfoList)){
            for(AgencyInfo agencyInfo : agencyInfoList){
                AgencyInfoDto agencyInfoDto = new AgencyInfoDto();
                BeanUtils.copyProperties(agencyInfo,agencyInfoDto);
                list.add(agencyInfoDto);
            }
        }
        PageBeanDto<AgencyInfoDto> pageBeanDto = new PageBeanDto<>();
        pageBeanDto.setPage(page);
        pageBeanDto.setList(list);
        return pageBeanDto;
    }

    @Override
    public PageBeanDto<MmpOperationLogDTO> queryAgencyOperationLog(QueryAgencyOperationLogInput queryAgencyOperationLogInput) {
        String agencyId = queryAgencyOperationLogInput.getAgencyId();
        Page page = queryAgencyOperationLogInput.getPage();
        if(page == null){
            page = new Page(1,20);
        }
        if(page.getCountFlag()){
            int num = mmpOperationLogMapper.countAgencyOperationLogNum(agencyId);
            page.setCount(num);
        }
        List<MmpOperationLog> operationLogList = mmpOperationLogMapper.queryAgencyOperationLogPage(agencyId,page);
        List<MmpOperationLogDTO> list = new ArrayList<>(operationLogList.size());
        if(CollectionUtils.isNotEmpty(operationLogList)){
            for(MmpOperationLog operationLog : operationLogList){
                MmpOperationLogDTO operationLogDTO = new MmpOperationLogDTO();
                BeanUtils.copyProperties(operationLog,operationLogDTO);
                list.add(operationLogDTO);
            }
        }
        PageBeanDto<MmpOperationLogDTO> pageBeanDto = new PageBeanDto<>();
        pageBeanDto.setPage(page);
        pageBeanDto.setList(list);
        return pageBeanDto;
    }

    @Override
    public List<AgencyInfoDto> queryAgencyListByCondition(QueryAgencyListConditionInput queryAgencyListConditionInput) {
        List<AgencyInfo> agencyInfoList = agencyInfoMapper.queryAgencyListByCondition(queryAgencyListConditionInput);
        if(CollectionUtils.isNotEmpty(agencyInfoList)){
            List<AgencyInfoDto> agencyInfoDtoList = new ArrayList<>(agencyInfoList.size());
            for(AgencyInfo agencyInfo : agencyInfoList){
                AgencyInfoDto agencyInfoDto = new AgencyInfoDto();
                BeanUtils.copyProperties(agencyInfo,agencyInfoDto);
                agencyInfoDtoList.add(agencyInfoDto);
            }
            return agencyInfoDtoList;
        }
        return null;
    }

    @Override
    public AgencyInfoBlackDTO queryAgencyInfoBlackById(String agencyId) {
        if(StringUtils.isNotBlank(agencyId)){
            AgencyInfoBlacklist agencyInfoBlacklist = agencyInfoBlacklistMapper.selectByPrimaryKey(agencyId);
            if(agencyInfoBlacklist != null){
                AgencyInfoBlackDTO agencyInfoBlackDTO = new AgencyInfoBlackDTO();
                BeanUtils.copyProperties(agencyInfoBlacklist,agencyInfoBlackDTO);
                return agencyInfoBlackDTO;
            }
        }
        return null;
    }

    @Override
    public boolean deleteAgencyInfoBlackById(String agencyId) {
        if(StringUtils.isNotBlank(agencyId)){
            try {
                int i = agencyInfoBlacklistMapper.deleteByPrimaryKey(agencyId);
                return i > 0;
            }catch (Exception e){
                logger.error(e.getMessage(),e);
            }
        }
        return false;
    }

    @Override
    public boolean insertAgencyInfoBlack(AgencyInfoBlackDTO agencyInfoBlackDTO) {
        if(agencyInfoBlackDTO != null){
            try {
                AgencyInfoBlacklist bean = new AgencyInfoBlacklist();
                BeanUtils.copyProperties(agencyInfoBlackDTO,bean);
                int i = agencyInfoBlacklistMapper.insertSelective(bean);
                return i > 0;
            }catch (Exception e){
                logger.error(e.getMessage(),e);
            }
        }
        return false;
    }

    @Override
    public List<AgencyInfoBlackDTO> queryAgencyInfoBlackByCondition(QueryAgencyInfoBlackConditionInput queryAgencyInfoBlackConditionInput) {
        List<AgencyInfoBlacklist> agencyInfoBlacklists = agencyInfoBlacklistMapper.queryAgencyInfoBlackByCondition(queryAgencyInfoBlackConditionInput);
        if(CollectionUtils.isNotEmpty(agencyInfoBlacklists)){
            List<AgencyInfoBlackDTO> agencyInfoBlackDTOList = new ArrayList<>(agencyInfoBlacklists.size());
            for(AgencyInfoBlacklist blacklist:agencyInfoBlacklists){
                AgencyInfoBlackDTO agencyInfoBlackDTO = new AgencyInfoBlackDTO();
                BeanUtils.copyProperties(blacklist,agencyInfoBlackDTO);
                agencyInfoBlackDTOList.add(agencyInfoBlackDTO);
            }
            return agencyInfoBlackDTOList;
        }
        return null;
    }

    @Override
    public AppKeyManagerDTO queryAppKeyManagerByAppKey(String appKey) {
        AppKeyManager appKeyManager = appKeyManagerMapper.selectByPrimaryKey(appKey);
        if(appKeyManager != null){
            AppKeyManagerDTO appKeyManagerDTO = new AppKeyManagerDTO();
            BeanUtils.copyProperties(appKeyManager,appKeyManagerDTO);
            return appKeyManagerDTO;
        }
        return null;
    }
}
