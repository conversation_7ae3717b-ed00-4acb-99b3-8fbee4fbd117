<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.core.dao.MmpProvisionUserFeedbackMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.MmpProvisionUserFeedback" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="AUTH_ID" property="authId" jdbcType="VARCHAR" />
    <result column="provision_id" property="provisionId" jdbcType="BIGINT" />
    <result column="node_id" property="nodeId" jdbcType="VARCHAR" />
    <result column="content" property="content" jdbcType="VARCHAR" />
    <result column="misc_desc" property="miscDesc" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, AUTH_ID, provision_id, node_id, content, misc_desc, status, create_time, create_oper_id, 
    create_oper_name, update_time, update_oper_id, update_oper_name
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_provision_user_feedback
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from ${issSchema}.mmp_provision_user_feedback
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.membership.core.model.MmpProvisionUserFeedback" >
    insert into ${issSchema}.mmp_provision_user_feedback (id, AUTH_ID, provision_id, 
      node_id, content, misc_desc, 
      status, create_time, create_oper_id, 
      create_oper_name, update_time, update_oper_id, 
      update_oper_name)
    values (#{id,jdbcType=BIGINT}, #{authId,jdbcType=VARCHAR}, #{provisionId,jdbcType=BIGINT}, 
      #{nodeId,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, #{miscDesc,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, 
      #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, 
      #{updateOperName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.core.model.MmpProvisionUserFeedback" >
    insert into ${issSchema}.mmp_provision_user_feedback
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="authId != null" >
        AUTH_ID,
      </if>
      <if test="provisionId != null" >
        provision_id,
      </if>
      <if test="nodeId != null" >
        node_id,
      </if>
      <if test="content != null" >
        content,
      </if>
      <if test="miscDesc != null" >
        misc_desc,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateOperId != null" >
        update_oper_id,
      </if>
      <if test="updateOperName != null" >
        update_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="authId != null" >
        #{authId,jdbcType=VARCHAR},
      </if>
      <if test="provisionId != null" >
        #{provisionId,jdbcType=BIGINT},
      </if>
      <if test="nodeId != null" >
        #{nodeId,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="miscDesc != null" >
        #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        #{updateOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.core.model.MmpProvisionUserFeedback" >
    update ${issSchema}.mmp_provision_user_feedback
    <set >
      <if test="authId != null" >
        AUTH_ID = #{authId,jdbcType=VARCHAR},
      </if>
      <if test="provisionId != null" >
        provision_id = #{provisionId,jdbcType=BIGINT},
      </if>
      <if test="nodeId != null" >
        node_id = #{nodeId,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="miscDesc != null" >
        misc_desc = #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.core.model.MmpProvisionUserFeedback" >
    update ${issSchema}.mmp_provision_user_feedback
    set AUTH_ID = #{authId,jdbcType=VARCHAR},
      provision_id = #{provisionId,jdbcType=BIGINT},
      node_id = #{nodeId,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      misc_desc = #{miscDesc,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="queryPageByNodeId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_provision_user_feedback
    where node_id = #{nodeId,jdbcType=VARCHAR}
    and status = 0
    <if test="startDate != null and endDate != null " >
        and (create_time between #{startDate,jdbcType=TIMESTAMP} and #{endDate,jdbcType=TIMESTAMP})
    </if>
    order by create_time desc
    limit  #{page.offSet} , #{page.limitSet}
  </select>

  <select id="countPageByNodeId" resultType="java.lang.Integer">
    select count(1)
    from ${issSchema}.mmp_provision_user_feedback
    where node_id = #{nodeId,jdbcType=VARCHAR}
    and status = 0
    <if test="startDate != null and endDate != null " >
      and (create_time between #{startDate,jdbcType=TIMESTAMP} and BTWEEN #{endDate,jdbcType=TIMESTAMP})
    </if>
  </select>

  <select id="queryByNodeId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_provision_user_feedback
    where node_id = #{nodeId,jdbcType=VARCHAR}
    and status = 0
    <if test="startDate != null and endDate != null " >
      and (create_time between #{startDate,jdbcType=TIMESTAMP} and BTWEEN #{endDate,jdbcType=TIMESTAMP})
    </if>
    and j
    order by create_time desc
    limit 2000
  </select>

</mapper>