package com.extracme.evcard.membership.health.service;

import com.extracme.evcard.membership.health.AESUtil;
import com.extracme.evcard.membership.health.dto.GetMemberHealthResponse;
import com.extracme.evcard.membership.health.dto.HealthTypeEnum;
import com.extracme.evcard.rpc.messagepush.service.ISensorsdataService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 */
@Service(value = "memberShipHealthServ")
public class MemberShipInvitationServ implements IMemberShipHealthServ{

    private static Logger logger = LoggerFactory.getLogger(MemberShipInvitationServ.class);

    @Autowired
    ISensorsdataService sensorsdataService;

    @Value("${health.secret}")
    String secret;
    @Value("${health.iv}")
    String iv;
    @Value("${health.key}")
    String key;
    @Value("${health.url}")
    String url;
    @Override
    public GetMemberHealthResponse getMemberHealth(String name, String authId,String auth) {
        int code = -1;
        String message = "";
        HealthTypeEnum healthType = null;
        String coler = StringUtils.EMPTY;
        if (StringUtils.isBlank(name) || StringUtils.isBlank(authId)){
            return null;
        }
        try {
            String content  = name + "," + authId;
            String ewmidjmh = AESUtil.encode(secret, iv, content).replaceAll("\\s*", "");
            String postUrl = url +"?mw="+ URLEncoder.encode(ewmidjmh,"utf-8") +"&from=" + key;
//            String postUrl = "http://***********:10011/healthCode";
            URI uri = new URI(postUrl);
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<Map> result = restTemplate.exchange(uri, HttpMethod.POST, new HttpEntity<String>(new HttpHeaders()), Map.class);
            String responseCode = result.getBody().get("code").toString();
            if("0".equals(responseCode)){
                code = 0;
                    switch ((String) result.getBody().get("type")){
                        case "00":
                            healthType = HealthTypeEnum.GREEN;
                            coler = "绿色";
                            break;
                        case "01":
                            healthType = HealthTypeEnum.YELLOW;
                            coler = "黄色";
                            break;
                        case "10":
                            healthType = HealthTypeEnum.RED;
                            coler = "红色";
                    }
            }else{
                logger.error("查询健康码返回：{},name:{},id:{}",responseCode,name,authId);
                message = (String)result.getBody().get("message");
            }
        } catch (Exception e) {
            logger.error("查询随身码失败"+e);
            message = e.getMessage();
        }
        /**
         * 埋点
         */
        try {
            Map<String, Object> map = new HashMap<>();
            map.put("type", "健康码");
            map.put("supplier", "第三方");
            if (code == 0) {
                map.put("is_success", true);
                map.put("reason", StringUtils.EMPTY);
                map.put("desc", coler);
            } else {
                map.put("is_success", false);
                map.put("reason", message);
            }
            sensorsdataService.track(auth, true, "third_party_verify", map);
        } catch (Exception e) {
            logger.error("健康码埋点失败，authId=" + authId, e);
        }
        return new GetMemberHealthResponse(code, message, healthType);
    }
}
