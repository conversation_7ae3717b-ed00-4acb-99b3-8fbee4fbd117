<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.core.dao.MmpPersonalDiscountMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.MmpPersonalDiscount" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 09 13:53:04 CST 2018.
    -->
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="discount_rule_id" property="discountRuleId" jdbcType="BIGINT" />
    <result column="discount_type" property="discountType" jdbcType="INTEGER" />
    <result column="discount_rate" property="discountRate" jdbcType="DOUBLE" />
    <result column="discount_start_time" property="discountStartTime" jdbcType="INTEGER" />
    <result column="discount_end_time" property="discountEndTime" jdbcType="INTEGER" />
    <result column="misc_desc" property="miscDesc" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 09 13:53:04 CST 2018.
    -->
    id, discount_rule_id, discount_type, discount_rate, discount_start_time, discount_end_time, 
    misc_desc, status, create_time, create_oper_id, create_oper_name, update_time, update_oper_id, 
    update_oper_name
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 09 13:53:04 CST 2018.
    -->
    select 
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_personal_discount
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="findPersonalList" resultType="com.extracme.evcard.membership.core.dto.agency.PersonalDiscountDTO" parameterType="java.lang.Long" >
    select
      t.id id,
      t.discount_rule_id discountRuleId,
      t.discount_type discountType,
      t.discount_rate discountRate,
      t.discount_start_time discountStartTime,
      t.discount_end_time discountEndTime
    from ${issSchema}.mmp_personal_discount t
    where t.discount_rule_id = #{discount_rule_id,jdbcType=BIGINT}
  </select>
</mapper>