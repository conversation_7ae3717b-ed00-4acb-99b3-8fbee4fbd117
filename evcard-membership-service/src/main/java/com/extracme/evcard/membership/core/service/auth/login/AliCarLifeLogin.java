package com.extracme.evcard.membership.core.service.auth.login;

import com.alibaba.fastjson.JSON;
import com.baosight.iplat4j.security.util.Crypto;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.common.MidGenerator;
import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.dao.CityMapper;
import com.extracme.evcard.membership.core.dao.MemberIdentityDocumentMapper;
import com.extracme.evcard.membership.core.dao.SecondAppKeyManagerMapper;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.enums.IdentityAuthStatusEnum;
import com.extracme.evcard.membership.core.input.ThirdLoginContext;
import com.extracme.evcard.membership.core.input.ThirdLoginInput;
import com.extracme.evcard.membership.core.input.ThirdLoginOtherDto;
import com.extracme.evcard.membership.core.model.City;
import com.extracme.evcard.membership.core.model.MemberIdentityDocument;
import com.extracme.evcard.membership.core.model.SecondAppKeyManager;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs;
import com.extracme.evcard.redis.JedisLock;
import com.extracme.evcard.redis.JedisUtil;
import com.extracme.evcard.rpc.enums.StatusCode;
import com.extracme.evcard.rpc.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

import static com.extracme.evcard.membership.common.ComUtil.*;

/**
 * 阿里云车生活 登录注册
 */
@Slf4j
@Service
@Deprecated
public class AliCarLifeLogin extends ThirdLoginAbstract {

    @Autowired
    private IMemberShipService memberShipService;
    @Autowired
    private CityMapper cityMapper;
    @Resource
    private MidGenerator midGenerator;
    @Autowired
    private MembershipInfoMapper membershipInfoMapper;
    @Resource
    private MemberIdentityDocumentMapper memberIdentityDocumentMapper;
    @Resource
    private SecondAppKeyManagerMapper secondAppKeyManagerMapper;

    @Override
    public String getSecondAppKey() {
        return BussinessConstants.HELLO_SECOND_APP_KEY;
    }

    @Override
    public String getDescription() {
        return "支付宝车生活";
    }

    @Override
    public void paramCheck(ThirdLoginContext context) throws BusinessException {
        super.paramCheck(context);

        ThirdLoginInput input = context.getInput();
        String cityId = input.getCityId();
        String idCardNo = input.getIdCardNo();
        String userName = input.getUserName();
        /*if (StringUtils.isBlank(userName) || StringUtils.isBlank(idCardNo)) {
            throw new BusinessException(-1, "入参格式不正确");
        }*/

        try {
            String cityName = "";
            if (StringUtils.isNotEmpty(cityId)) {
                City city = cityMapper.getCityByCityId(cityId);
                if (city != null) {
                    cityName = city.getCity();
                }
            }
            if (StringUtils.isBlank(cityName)) {
                cityName = "上海市";
            }
            ThirdLoginOtherDto otherDto = context.getOtherDto();
            otherDto.setCityName(cityName);
        } catch (Exception e) {
            log.error("车生活，获取注册城市异常，input={}", JSON.toJSONString(input), e);
        }

    }

    @Override
    protected MembershipBasicInfo doRegisterMembershipInfo(ThirdLoginContext context) throws BusinessException {
        String cityName = context.getOtherDto().getCityName();
        ThirdLoginInput input = context.getInput();
        log.info("支付宝车生活，doRegisterMembershipInfo,input[{}]",JSON.toJSONString(input));
        String mobilePhone = input.getMobilePhone();
        String appKey = input.getAppKey();
        String idCardNo = input.getIdCardNo();
        String userName = input.getUserName();

        Jedis jedis = null;
        JedisLock jedisLock = null;
        try {
            //防止手机号并发注册
            jedis = JedisUtil.getJedis();
            jedisLock = new JedisLock(mobilePhone);
            if (!jedisLock.acquire(jedis)) {
                throw new BusinessException(StatusCode.SYSTEM_ERROR);
            }

            // 密码校验
            String password = mobilePhone.substring(1, 6).trim();
            //加密密码
            password = Crypto.encryptDES(password, ENCRYPT_KEY);
            //生成会员ID
            LocalDateTime localDateTime = LocalDateTime.now();
            String authId = mobilePhone + localDateTime.format(DateTimeFormatter.ofPattern(DATE_TYPE6));
            //插入记录
            MembershipInfoWithBLOBs member = new MembershipInfoWithBLOBs();
            member.setAuthId(authId);
            member.setName(userName);
            member.setPassword(password);
            member.setMobilePhone(mobilePhone);
            member.setCityOfOrigin(cityName);
            member.setMembershipType((short) 0);
            member.setIdCardNumber(idCardNo);
            member.setPassportNo(idCardNo);
            //审核状态
            member.setReviewStatus((short) -1);
            // TODO  注册来源车生活
            //member.setDataOrigin((short) registerDto.getRegisterOrigin());
            member.setCreatedTime(localDateTime.format(DateTimeFormatter.ofPattern(DATE_TYPE3)));
            member.setCreatedUser(userName);
            member.setUpdatedTime(localDateTime.format(DateTimeFormatter.ofPattern(DATE_TYPE3)));
            member.setUpdatedUser(userName);
            member.setRegTime(localDateTime.format(DateTimeFormatter.ofPattern(DATE_TYPE4)));
            //第三方appKey
            member.setAppKey(appKey);
            try {
                // 通过一级渠道查询二级渠道信息
                List<SecondAppKeyManager> appKeyManagers = secondAppKeyManagerMapper.selectListByFirstAppKey(appKey);
                if (CollectionUtils.isNotEmpty(appKeyManagers)) {
                    SecondAppKeyManager secondAppKeyManager = appKeyManagers.get(0);
                    member.setSecondAppKey(secondAppKeyManager.getSecondAppKey());
                    member.setPlatformId(secondAppKeyManager.getPlatformId());
                }
            } catch (Exception e) {
                log.error("支付宝车生活注册时，通过一级渠道查询二级渠道报错，appKey={}",appKey,e);
            }
            member.setMid(midGenerator.getMid());

            // 新增记录
            MemberIdentityDocument insertDocument = insertIdentityDocument(input, member.getMid());
            member.setIdentityId(insertDocument.getId());
            int insertSelective = membershipInfoMapper.insertSelective(member);

            if (insertSelective > 0) {
                log.info("车生活mobile=" + mobilePhone + "insertSelective成功");
                MembershipBasicInfo membership = memberShipService.getMembershipByPhone(mobilePhone, 0);
                context.setMembershipBasicInfo(membership);
                return membership;
            } else {
                throw new BusinessException(-1, "新增会员失败");
            }
        } catch (Exception e) {
            log.error("车生活注册失败,原因：创建会员记录失败，" + e.getMessage(), e);
            throw new BusinessException(StatusCode.SYSTEM_ERROR);
        } finally {
            if (jedisLock != null && jedis != null) {
                jedisLock.releaseLua(jedis);
                jedis.close();
                log.debug("车生活注册服务-释放redis锁和链接成功");
            }
        }

    }

    /**
     * 新增 MemberIdentityDocument记录
     *
     * @param input
     * @param mid
     * @return
     */
    private MemberIdentityDocument insertIdentityDocument(ThirdLoginInput input, String mid) throws BusinessException {
        MemberIdentityDocument insertDocument = new MemberIdentityDocument();
        insertDocument.setMid(mid);
        insertDocument.setIdentityNo(input.getIdCardNo());
        insertDocument.setName(input.getUserName());
        insertDocument.setCreateTime(new Date());
        insertDocument.setCreateOperName(input.getUserName());
        int i = memberIdentityDocumentMapper.insertSelective(insertDocument);
        if (i < 0) {
            throw new BusinessException(-1, "新增MemberIdentityDocument记录失败");
        }
        return insertDocument;
    }


    @Override
    public MembershipBasicInfo getExistedMembershipInfo(ThirdLoginContext context) throws BusinessException {
        try {
            MembershipBasicInfo basicInfo = context.getMembershipBasicInfo();
            ThirdLoginInput input = context.getInput();
            log.info("支付宝车生活，getExistedMembershipInfo,input[{}],basicInfo[{}]",JSON.toJSONString(input),JSON.toJSONString(basicInfo));
            //未认证时 需要去 补全用户信息
            Integer identityStatus = basicInfo.getAuthenticationStatusNew();//新认证字段
            Long oldIdentityId = basicInfo.getIdentityId();
            if (oldIdentityId == null) {
                // 新增记录
                MemberIdentityDocument insertDocument = insertIdentityDocument(input, basicInfo.getMid());
                // 更新dentityId值
                MembershipInfoWithBLOBs updateMember = new MembershipInfoWithBLOBs();
                updateMember.setPkId(basicInfo.getPkId());
                updateMember.setIdentityId(insertDocument.getId());
                updateMember.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE4));
                membershipInfoMapper.updateByPrimaryKeySelective(updateMember);

                //重新放回
                basicInfo = memberShipService.getMembershipByPhone(input.getMobilePhone(), 0);
                context.setMembershipBasicInfo(basicInfo);
            } else if (identityStatus == null || IdentityAuthStatusEnum.UN_SUBMIT.getValue().equals(identityStatus)) {
                MemberIdentityDocument updateIdentityDocument = new MemberIdentityDocument();
                updateIdentityDocument.setName(input.getUserName());
                updateIdentityDocument.setIdentityNo(input.getIdCardNo());
                updateIdentityDocument.setId(oldIdentityId);
                updateIdentityDocument.setUpdateTime(new Date());
                int i = memberIdentityDocumentMapper.updateByPrimaryKeySelective(updateIdentityDocument);
                if (i < 0) {
                    throw new BusinessException(-1, "更新MemberIdentityDocument记录失败");
                }
            }
        } catch (Exception e) {
            log.error("车生活getExistedMembershipInfo异常,context={}", JSON.toJSONString(context), e);
        }
        return super.getExistedMembershipInfo(context);
    }

    @Override
    public void afterGetMembershipInfo(ThirdLoginContext context) throws BusinessException{
        MembershipBasicInfo membershipInfo = context.getMembershipBasicInfo();
        //制卡，该方法已实现幂等
        if (StringUtils.isBlank(membershipInfo.getCardNo())) {
            log.info("车生活membershipInfo=[{}],开始制卡", JSON.toJSONString(membershipInfo));
            memberShipService.setVirtualCard(membershipInfo.getAuthId());
            log.info("车生活membershipInfo=[{}],制卡成功", JSON.toJSONString(membershipInfo));
        }
        super.afterGetMembershipInfo(context);
    }
}
