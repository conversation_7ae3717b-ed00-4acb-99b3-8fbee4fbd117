<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.core.dao.MmpShortLinkManagementMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.MmpShortLinkManagement" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="misc_desc" property="miscDesc" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
    <result column="short_link_name" property="shortLinkName" jdbcType="VARCHAR" />
    <result column="short_link_url" property="shortLinkUrl" jdbcType="VARCHAR" />
    <result column="random_code" property="randomCode" jdbcType="VARCHAR" />
    <result column="original_url" property="originalUrl" jdbcType="VARCHAR" />
    <result column="effective_time" property="effectiveTime" jdbcType="DATE" />
    <result column="failure_time" property="failureTime" jdbcType="DATE" />
    <result column="qrcode_path" property="qrcodePath" jdbcType="VARCHAR" />
    <result column="is_hidden" property="isHidden" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, misc_desc, status, create_time, create_oper_id, create_oper_name, update_time, 
    update_oper_id, update_oper_name, short_link_name, short_link_url, random_code, original_url, 
    effective_time, failure_time, qrcode_path
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from iss.mmp_short_link_management
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from iss.mmp_short_link_management
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.membership.core.model.MmpShortLinkManagement" keyProperty="id">
    insert into iss.mmp_short_link_management (id, misc_desc, status,
      create_time, create_oper_id, create_oper_name, 
      update_time, update_oper_id, update_oper_name, 
      short_link_name, short_link_url, random_code, 
      original_url, effective_time, failure_time,qrcode_path
      )
    values (#{id,jdbcType=BIGINT}, #{miscDesc,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, #{createOperName,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, #{updateOperName,jdbcType=VARCHAR}, 
      #{shortLinkName,jdbcType=VARCHAR}, #{shortLinkUrl,jdbcType=VARCHAR}, #{randomCode,jdbcType=VARCHAR}, 
      #{originalUrl,jdbcType=VARCHAR}, #{effectiveTime,jdbcType=DATE}, #{failureTime,jdbcType=DATE}, #{qrcodePath,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.core.model.MmpShortLinkManagement" useGeneratedKeys="true" keyProperty="id">
    insert into iss.mmp_short_link_management
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="miscDesc != null" >
        misc_desc,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateOperId != null" >
        update_oper_id,
      </if>
      <if test="updateOperName != null" >
        update_oper_name,
      </if>
      <if test="shortLinkName != null" >
        short_link_name,
      </if>
      <if test="shortLinkUrl != null" >
        short_link_url,
      </if>
      <if test="randomCode != null" >
        random_code,
      </if>
      <if test="originalUrl != null" >
        original_url,
      </if>
      <if test="effectiveTime != null" >
        effective_time,
      </if>
      <if test="failureTime != null" >
        failure_time,
      </if>
      <if test="qrcodePath != null" >
        qrcode_path,
      </if>
      <if test="isHidden != null" >
        is_hidden
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="miscDesc != null" >
        #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="shortLinkName != null" >
        #{shortLinkName,jdbcType=VARCHAR},
      </if>
      <if test="shortLinkUrl != null" >
        #{shortLinkUrl,jdbcType=VARCHAR},
      </if>
      <if test="randomCode != null" >
        #{randomCode,jdbcType=VARCHAR},
      </if>
      <if test="originalUrl != null" >
        #{originalUrl,jdbcType=VARCHAR},
      </if>
      <if test="effectiveTime != null" >
        #{effectiveTime,jdbcType=DATE},
      </if>
      <if test="failureTime != null" >
        #{failureTime,jdbcType=DATE},
      </if>
      <if test="qrcodePath != null" >
        #{qrcodePath,jdbcType=VARCHAR},
      </if>
      <if test="isHidden != null" >
        #{isHidden,jdbcType=INTEGER}
      </if>
    </trim>
  </insert>

  <update id="updateByDto" parameterType="com.extracme.evcard.membership.core.model.MmpShortLinkManagement" >
    update iss.mmp_short_link_management
    <set >
      <if test="miscDesc != null" >
        misc_desc = #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="shortLinkName != null" >
        short_link_name = #{shortLinkName,jdbcType=VARCHAR},
      </if>
      <if test="shortLinkUrl != null" >
        short_link_url = #{shortLinkUrl,jdbcType=VARCHAR},
      </if>
      <if test="randomCode != null" >
        random_code = #{randomCode,jdbcType=VARCHAR},
      </if>
      <if test="originalUrl != null" >
        original_url = #{originalUrl,jdbcType=VARCHAR},
      </if>
      <if test="effectiveTime != null" >
        effective_time = #{effectiveTime,jdbcType=DATE},
      </if>
      <if test="failureTime != null" >
        failure_time = #{failureTime,jdbcType=DATE},
      </if>
      <if test="qrcodePath != null" >
        qrcode_path = #{qrcodePath,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.core.model.MmpShortLinkManagement" >
    update iss.mmp_short_link_management
    set misc_desc = #{miscDesc,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      short_link_name = #{shortLinkName,jdbcType=VARCHAR},
      short_link_url = #{shortLinkUrl,jdbcType=VARCHAR},
      random_code = #{randomCode,jdbcType=VARCHAR},
      original_url = #{originalUrl,jdbcType=VARCHAR},
      effective_time = #{effectiveTime,jdbcType=DATE},
      failure_time = #{failureTime,jdbcType=DATE},
      qrcode_path = #{qrcodePath,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>





  <select id="selectByKeys" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from iss.mmp_short_link_management
    where 1=1
    <if test="shortLinkName != null" >
      and short_link_name like CONCAT('%',#{shortLinkName,jdbcType=VARCHAR},'%')
    </if>
    <if test="shortLinkUrl != null" >
      and short_link_url like CONCAT('%',#{shortLinkUrl,jdbcType=VARCHAR},'%')
    </if>
    <if test="originalUrl != null" >
      and original_url like CONCAT('%',#{originalUrl,jdbcType=VARCHAR},'%')
    </if>

    <if test="randomCode != null" >
      and random_code = #{randomCode,jdbcType=VARCHAR}
    </if>
    <if test="status != null" >
      and status = #{status,jdbcType=INTEGER}
    </if>
    <if test="failureTime != null" >
      AND failure_time <![CDATA[ < ]]> #{failureTime,jdbcType=DATE}
    </if>
    order by create_time desc
  </select>

</mapper>