package com.extracme.evcard.membership.inner.service;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.dao.UserDeviceInfoMapper;
import com.extracme.evcard.membership.core.model.UserDeviceInfo;
import com.extracme.evcard.membership.core.model.UserDeviceInfoExample;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class UserDeviceInfoService {

    @Autowired
    private UserDeviceInfoMapper userDeviceInfoMapper;

    public void bindUserDeviceInfo(UserDeviceInfo userDeviceInfo) {
        String deviceCode = userDeviceInfo.getDeviceCode();
        Integer deviceCodeType = userDeviceInfo.getDeviceCodeType();
        String mid = userDeviceInfo.getMid();
        Integer type = userDeviceInfo.getType();
        Integer osType = userDeviceInfo.getOsType();

        // 参数校验
        if (StringUtils.isBlank(deviceCode) || deviceCodeType == null || !BussinessConstants.DEVICE_CODE_TYPE.contains(deviceCodeType) || StringUtils.isBlank(mid)) {
            log.error("bindUserDeviceInfo 参数不符合，userDeviceInfo={}", JSON.toJSONString(userDeviceInfo));
            return;
        }

        UserDeviceInfoExample userDeviceInfoExample = new UserDeviceInfoExample();
        UserDeviceInfoExample.Criteria criteria = userDeviceInfoExample.createCriteria();
        criteria.andDeviceCodeEqualTo(deviceCode);
        criteria.andDeviceCodeTypeEqualTo(deviceCodeType);
        criteria.andMidEqualTo(mid);
        criteria.andTypeEqualTo(type);
        criteria.andOsTypeEqualTo(osType);

        List<UserDeviceInfo> userDeviceInfos = userDeviceInfoMapper.selectByExample(userDeviceInfoExample);
        // 记录已存在 不新增
        if (CollectionUtils.isEmpty(userDeviceInfos)) {
            Date now = new Date();
            userDeviceInfo.setCreateTime(now);
            userDeviceInfo.setUpdateTime(now);
            userDeviceInfoMapper.insertSelective(userDeviceInfo);
        }
    }

}
