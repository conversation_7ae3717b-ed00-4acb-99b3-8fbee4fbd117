package com.extracme.evcard.membership.ocr;

import com.extracme.evcard.membership.core.enums.CommonSideEnums;
import lombok.Data;

import java.io.Serializable;

/**
 * 统一的身份证识别入参
 *
 * <AUTHOR>
 * @date 2022/8/19
 */
@Data
public class CommonIdCardOcrReq implements Serializable {
    /**
     * 会员唯一标识
     */
    private String mid;

    /**
     * 图片信息
     * 与图片路径二选一
     */
    private byte[] image;

    /**
     * 图片完整URL, 与图片路径二选一，注意这个URL必须是线上的，不能是本地路径
     */
    private String url;

    /**
     * 正面或反面
     * -front：身份证含照片的一面
     * -back：身份证带国徽的一面
     */
    private CommonSideEnums commonSide;
}
