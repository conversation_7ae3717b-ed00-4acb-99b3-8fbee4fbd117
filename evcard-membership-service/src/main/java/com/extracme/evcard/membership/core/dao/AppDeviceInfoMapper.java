package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.model.AppDeviceInfo;
import org.apache.ibatis.annotations.Param;

public interface AppDeviceInfoMapper {

    int insert(AppDeviceInfo record);

    AppDeviceInfo selectByPrimaryKey(Long pkId);


    /**
     * 根据手机号和imei码获取登录信息
     * @param mobilePhone
     * @param imei
     * @return
     */
    int updateLoginCount(@Param("phone")String mobilePhone,@Param("imei") String imei);

    /**
     *
     * @param mobilePhone
     * @param imei
     * @return
     */
    int countInfo(@Param("phone")String mobilePhone,@Param("imei") String imei);

}