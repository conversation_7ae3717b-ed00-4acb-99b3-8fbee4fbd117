package com.extracme.evcard.membership.vipcredits.anyolife.entity;

import lombok.Data;

/**
 * 积分变动履历
 * <AUTHOR>
 * @Discription
 * @date 2020/8/12
 */
@Data
public class CreditHistoryRecord {
    /**
     * 变动原因
     */
    private String changeReason;
    /**
     * 变动积分数
     */
    private Integer credits;
    /**
     * 变动日期
     */
    private String updateDate;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 收单号
     */
    private String payOrderId;
    /**
     * 积分变动操作 1消费 2获取
     */
    private String creditsType;
    /**
     * 变动操作类别
     * 消费： 01商品购物
     * 获取： 0固定积分 1完成认证 2补全资料 3...
     */
    private String feeType;
    /**
     * if获取积分 有效期
     */
    private String expireDate;
    /**
     * 变动后剩余积分数
     */
    private Integer leftCredits;
}
