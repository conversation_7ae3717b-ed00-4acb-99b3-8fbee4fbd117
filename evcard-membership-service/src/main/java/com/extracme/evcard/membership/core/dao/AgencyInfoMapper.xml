<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.core.dao.AgencyInfoMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.AgencyInfo" >
    <id column="AGENCY_ID" property="agencyId" jdbcType="VARCHAR" />
    <result column="AGENCY_NAME" property="agencyName" jdbcType="VARCHAR" />
    <result column="DISCOUNT_INNER" property="discountInner" jdbcType="DOUBLE" />
    <result column="DISCOUNT_OUTER" property="discountOuter" jdbcType="DOUBLE" />
    <result column="CREATED_USER" property="createdUser" jdbcType="VARCHAR" />
    <result column="CREATED_TIME" property="createdTime" jdbcType="VARCHAR" />
    <result column="UPDATED_USER" property="updatedUser" jdbcType="VARCHAR" />
    <result column="UPDATED_TIME" property="updatedTime" jdbcType="VARCHAR" />
    <result column="STATUS" property="status" jdbcType="DOUBLE" />
    <result column="PICKUP_WEEKS" property="pickupWeeks" jdbcType="VARCHAR" />
    <result column="RETURN_WEEKS" property="returnWeeks" jdbcType="VARCHAR" />
    <result column="PICKUP_TIME" property="pickupTime" jdbcType="VARCHAR" />
    <result column="RETURN_TIME" property="returnTime" jdbcType="VARCHAR" />
    <result column="MAX_USER_HOUR" property="maxUserHour" jdbcType="VARCHAR" />
    <result column="EXEMPT_DEPOSIT" property="exemptDeposit" jdbcType="INTEGER" />
    <result column="CONTACT" property="contact" jdbcType="VARCHAR" />
    <result column="TEL" property="tel" jdbcType="VARCHAR" />
    <result column="MOBILE_PHONE" property="mobilePhone" jdbcType="VARCHAR" />
    <result column="MAIL" property="mail" jdbcType="VARCHAR" />
    <result column="LICENSE_NO" property="licenseNo" jdbcType="VARCHAR" />
    <result column="FAX" property="fax" jdbcType="VARCHAR" />
    <result column="LICENSE_NO_IMG_URL" property="licenseNoImgUrl" jdbcType="VARCHAR" />
    <result column="CONTRACT_IMG_URL" property="contractImgUrl" jdbcType="VARCHAR" />
    <result column="TAX_REGISTRATION_IMG_URL" property="taxRegistrationImgUrl" jdbcType="VARCHAR" />
    <result column="ORG_CODE_IMG_URL" property="orgCodeImgUrl" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="PAY_WAY" property="payWay" jdbcType="DOUBLE" />
    <result column="DEPOSIT" property="deposit" jdbcType="DOUBLE" />
    <result column="RENT_MINS" property="rentMins" jdbcType="DOUBLE" />
    <result column="CHECK_DATE" property="checkDate" jdbcType="VARCHAR" />
    <result column="CHECK_ALERT" property="checkAlert" jdbcType="INTEGER" />
    <result column="ORG_PROPERTY" property="orgProperty" jdbcType="TINYINT" />
    <result column="INSIDE_FLAG" property="insideFlag" jdbcType="INTEGER" />
    <result column="ADDRESS" property="address" jdbcType="VARCHAR" />
    <result column="LAST_REMIND_DATE" property="lastRemindDate" jdbcType="DATE" />
    <result column="DISCOUNT_RULE" property="discountRule" jdbcType="INTEGER" />
    <result column="org_id" property="orgId" jdbcType="VARCHAR" />
    <result column="org_name" property="orgName" jdbcType="VARCHAR" />
    <result column="max_unit_price" property="maxUnitPrice" jdbcType="DECIMAL" />
    <result column="APP_KEY" property="appKey" jdbcType="VARCHAR" />
    <result column="COOPERATE_START_TIME" property="cooperateStartTime" jdbcType="TIMESTAMP" />
    <result column="COOPERATE_END_TIME" property="cooperateEndTime" jdbcType="TIMESTAMP" />
    <result column="COOPERATE_STATUS" property="cooperateStatus" jdbcType="INTEGER" />
    <result column="VEHICLE_THRESHOLD" property="vehicleThreshold" jdbcType="DOUBLE" />
    <result column="DISCOUNT_ID" property="discountId" jdbcType="BIGINT" />
    <result column="DISCOUNT_PERSONAL_ID" property="discountPersonalId" jdbcType="BIGINT" />
    <result column="LINE_LIMIT_MONTHLY" property="lineLimitMonthly" jdbcType="DOUBLE" />
    <result column="VEHICLE_NO" jdbcType="VARCHAR" property="vehicleNo"/>

    <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime" />
    <result column="create_way" jdbcType="INTEGER" property="createWay" />
    <result column="long_rent_contract_id" jdbcType="VARCHAR" property="longRentContractId" />
    <result column="deposit_guarantor" jdbcType="INTEGER" property="depositGuarantor" />
    <result column="order_payer" jdbcType="INTEGER" property="orderPayer" />
    <result column="defaulting_party" jdbcType="INTEGER" property="defaultingParty" />
    <result column="business_source" jdbcType="INTEGER" property="businessSource" />
  </resultMap>
  <sql id="Base_Column_List" >
    AGENCY_ID, AGENCY_NAME, DISCOUNT_INNER, DISCOUNT_OUTER, CREATED_USER, CREATED_TIME, 
    UPDATED_USER, UPDATED_TIME, STATUS, PICKUP_WEEKS, RETURN_WEEKS, PICKUP_TIME, RETURN_TIME, 
    MAX_USER_HOUR, EXEMPT_DEPOSIT, CONTACT, TEL, MOBILE_PHONE, MAIL, LICENSE_NO, FAX, 
    LICENSE_NO_IMG_URL, CONTRACT_IMG_URL, TAX_REGISTRATION_IMG_URL, ORG_CODE_IMG_URL, 
    REMARK, PAY_WAY, DEPOSIT, RENT_MINS, CHECK_DATE, CHECK_ALERT, ORG_PROPERTY, INSIDE_FLAG, 
    ADDRESS, LAST_REMIND_DATE, DISCOUNT_RULE,org_id,org_name, max_unit_price, APP_KEY, COOPERATE_START_TIME,
    COOPERATE_END_TIME, COOPERATE_STATUS, VEHICLE_THRESHOLD, DISCOUNT_ID, DISCOUNT_PERSONAL_ID, 
    LINE_LIMIT_MONTHLY,VEHICLE_NO,
    expire_time,
    create_way,
    long_rent_contract_id,
    deposit_guarantor,
    order_payer,
    defaulting_party,
    business_source
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from ${siacSchema}.AGENCY_INFO
    where AGENCY_ID = #{agencyId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from ${siacSchema}.AGENCY_INFO
    where AGENCY_ID = #{agencyId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.membership.core.model.AgencyInfo" >
    insert into ${siacSchema}.AGENCY_INFO (AGENCY_ID, AGENCY_NAME, DISCOUNT_INNER,
      DISCOUNT_OUTER, CREATED_USER, CREATED_TIME, 
      UPDATED_USER, UPDATED_TIME, STATUS, 
      PICKUP_WEEKS, RETURN_WEEKS, PICKUP_TIME, 
      RETURN_TIME, MAX_USER_HOUR, EXEMPT_DEPOSIT, 
      CONTACT, TEL, MOBILE_PHONE, 
      MAIL, LICENSE_NO, FAX, 
      LICENSE_NO_IMG_URL, CONTRACT_IMG_URL, TAX_REGISTRATION_IMG_URL, 
      ORG_CODE_IMG_URL, REMARK, PAY_WAY, 
      DEPOSIT, RENT_MINS, CHECK_DATE, 
      CHECK_ALERT, ORG_PROPERTY, INSIDE_FLAG, 
      ADDRESS, LAST_REMIND_DATE, DISCOUNT_RULE, 
      org_name, max_unit_price, APP_KEY, 
      COOPERATE_START_TIME, COOPERATE_END_TIME, 
      COOPERATE_STATUS, VEHICLE_THRESHOLD, DISCOUNT_ID, 
      DISCOUNT_PERSONAL_ID, LINE_LIMIT_MONTHLY, VEHICLE_NO)
    values (#{agencyId,jdbcType=VARCHAR}, #{agencyName,jdbcType=VARCHAR}, #{discountInner,jdbcType=DOUBLE}, 
      #{discountOuter,jdbcType=DOUBLE}, #{createdUser,jdbcType=VARCHAR}, #{createdTime,jdbcType=VARCHAR}, 
      #{updatedUser,jdbcType=VARCHAR}, #{updatedTime,jdbcType=VARCHAR}, #{status,jdbcType=DOUBLE}, 
      #{pickupWeeks,jdbcType=VARCHAR}, #{returnWeeks,jdbcType=VARCHAR}, #{pickupTime,jdbcType=VARCHAR}, 
      #{returnTime,jdbcType=VARCHAR}, #{maxUserHour,jdbcType=VARCHAR}, #{exemptDeposit,jdbcType=INTEGER}, 
      #{contact,jdbcType=VARCHAR}, #{tel,jdbcType=VARCHAR}, #{mobilePhone,jdbcType=VARCHAR}, 
      #{mail,jdbcType=VARCHAR}, #{licenseNo,jdbcType=VARCHAR}, #{fax,jdbcType=VARCHAR}, 
      #{licenseNoImgUrl,jdbcType=VARCHAR}, #{contractImgUrl,jdbcType=VARCHAR}, #{taxRegistrationImgUrl,jdbcType=VARCHAR}, 
      #{orgCodeImgUrl,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{payWay,jdbcType=DOUBLE}, 
      #{deposit,jdbcType=DOUBLE}, #{rentMins,jdbcType=DOUBLE}, #{checkDate,jdbcType=VARCHAR}, 
      #{checkAlert,jdbcType=INTEGER}, #{orgProperty,jdbcType=TINYINT}, #{insideFlag,jdbcType=INTEGER}, 
      #{address,jdbcType=VARCHAR}, #{lastRemindDate,jdbcType=DATE}, #{discountRule,jdbcType=INTEGER}, 
      #{orgName,jdbcType=VARCHAR}, #{maxUnitPrice,jdbcType=DECIMAL}, #{appKey,jdbcType=VARCHAR}, 
      #{cooperateStartTime,jdbcType=TIMESTAMP}, #{cooperateEndTime,jdbcType=TIMESTAMP}, 
      #{cooperateStatus,jdbcType=INTEGER}, #{vehicleThreshold,jdbcType=DOUBLE}, #{discountId,jdbcType=BIGINT}, 
      #{discountPersonalId,jdbcType=BIGINT}, #{lineLimitMonthly,jdbcType=DOUBLE}, #{vehicleNo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.core.model.AgencyInfo" >
    insert into ${siacSchema}.AGENCY_INFO
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="agencyId != null" >
        AGENCY_ID,
      </if>
      <if test="agencyName != null" >
        AGENCY_NAME,
      </if>
      <if test="discountInner != null" >
        DISCOUNT_INNER,
      </if>
      <if test="discountOuter != null" >
        DISCOUNT_OUTER,
      </if>
      <if test="createdUser != null" >
        CREATED_USER,
      </if>
      <if test="createdTime != null" >
        CREATED_TIME,
      </if>
      <if test="updatedUser != null" >
        UPDATED_USER,
      </if>
      <if test="updatedTime != null" >
        UPDATED_TIME,
      </if>
      <if test="status != null" >
        STATUS,
      </if>
      <if test="pickupWeeks != null" >
        PICKUP_WEEKS,
      </if>
      <if test="returnWeeks != null" >
        RETURN_WEEKS,
      </if>
      <if test="pickupTime != null" >
        PICKUP_TIME,
      </if>
      <if test="returnTime != null" >
        RETURN_TIME,
      </if>
      <if test="maxUserHour != null" >
        MAX_USER_HOUR,
      </if>
      <if test="exemptDeposit != null" >
        EXEMPT_DEPOSIT,
      </if>
      <if test="contact != null" >
        CONTACT,
      </if>
      <if test="tel != null" >
        TEL,
      </if>
      <if test="mobilePhone != null" >
        MOBILE_PHONE,
      </if>
      <if test="mail != null" >
        MAIL,
      </if>
      <if test="licenseNo != null" >
        LICENSE_NO,
      </if>
      <if test="fax != null" >
        FAX,
      </if>
      <if test="licenseNoImgUrl != null" >
        LICENSE_NO_IMG_URL,
      </if>
      <if test="contractImgUrl != null" >
        CONTRACT_IMG_URL,
      </if>
      <if test="taxRegistrationImgUrl != null" >
        TAX_REGISTRATION_IMG_URL,
      </if>
      <if test="orgCodeImgUrl != null" >
        ORG_CODE_IMG_URL,
      </if>
      <if test="remark != null" >
        REMARK,
      </if>
      <if test="payWay != null" >
        PAY_WAY,
      </if>
      <if test="deposit != null" >
        DEPOSIT,
      </if>
      <if test="rentMins != null" >
        RENT_MINS,
      </if>
      <if test="checkDate != null" >
        CHECK_DATE,
      </if>
      <if test="checkAlert != null" >
        CHECK_ALERT,
      </if>
      <if test="orgProperty != null" >
        ORG_PROPERTY,
      </if>
      <if test="insideFlag != null" >
        INSIDE_FLAG,
      </if>
      <if test="address != null" >
        ADDRESS,
      </if>
      <if test="lastRemindDate != null" >
        LAST_REMIND_DATE,
      </if>
      <if test="discountRule != null" >
        DISCOUNT_RULE,
      </if>
      <if test="orgId != null" >
        org_id,
      </if>
      <if test="orgName != null" >
        org_name,
      </if>
      <if test="maxUnitPrice != null" >
        max_unit_price,
      </if>
      <if test="appKey != null" >
        APP_KEY,
      </if>
      <if test="cooperateStartTime != null" >
        COOPERATE_START_TIME,
      </if>
      <if test="cooperateEndTime != null" >
        COOPERATE_END_TIME,
      </if>
      <if test="cooperateStatus != null" >
        COOPERATE_STATUS,
      </if>
      <if test="vehicleThreshold != null" >
        VEHICLE_THRESHOLD,
      </if>
      <if test="discountId != null" >
        DISCOUNT_ID,
      </if>
      <if test="discountPersonalId != null" >
        DISCOUNT_PERSONAL_ID,
      </if>
      <if test="lineLimitMonthly != null" >
        LINE_LIMIT_MONTHLY,
      </if>
      <if test="vehicleNo != null" >
          VEHICLE_NO,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="agencyId != null" >
        #{agencyId,jdbcType=VARCHAR},
      </if>
      <if test="agencyName != null" >
        #{agencyName,jdbcType=VARCHAR},
      </if>
      <if test="discountInner != null" >
        #{discountInner,jdbcType=DOUBLE},
      </if>
      <if test="discountOuter != null" >
        #{discountOuter,jdbcType=DOUBLE},
      </if>
      <if test="createdUser != null" >
        #{createdUser,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=VARCHAR},
      </if>
      <if test="updatedUser != null" >
        #{updatedUser,jdbcType=VARCHAR},
      </if>
      <if test="updatedTime != null" >
        #{updatedTime,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=DOUBLE},
      </if>
      <if test="pickupWeeks != null" >
        #{pickupWeeks,jdbcType=VARCHAR},
      </if>
      <if test="returnWeeks != null" >
        #{returnWeeks,jdbcType=VARCHAR},
      </if>
      <if test="pickupTime != null" >
        #{pickupTime,jdbcType=VARCHAR},
      </if>
      <if test="returnTime != null" >
        #{returnTime,jdbcType=VARCHAR},
      </if>
      <if test="maxUserHour != null" >
        #{maxUserHour,jdbcType=VARCHAR},
      </if>
      <if test="exemptDeposit != null" >
        #{exemptDeposit,jdbcType=INTEGER},
      </if>
      <if test="contact != null" >
        #{contact,jdbcType=VARCHAR},
      </if>
      <if test="tel != null" >
        #{tel,jdbcType=VARCHAR},
      </if>
      <if test="mobilePhone != null" >
        #{mobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="mail != null" >
        #{mail,jdbcType=VARCHAR},
      </if>
      <if test="licenseNo != null" >
        #{licenseNo,jdbcType=VARCHAR},
      </if>
      <if test="fax != null" >
        #{fax,jdbcType=VARCHAR},
      </if>
      <if test="licenseNoImgUrl != null" >
        #{licenseNoImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="contractImgUrl != null" >
        #{contractImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="taxRegistrationImgUrl != null" >
        #{taxRegistrationImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="orgCodeImgUrl != null" >
        #{orgCodeImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="payWay != null" >
        #{payWay,jdbcType=DOUBLE},
      </if>
      <if test="deposit != null" >
        #{deposit,jdbcType=DOUBLE},
      </if>
      <if test="rentMins != null" >
        #{rentMins,jdbcType=DOUBLE},
      </if>
      <if test="checkDate != null" >
        #{checkDate,jdbcType=VARCHAR},
      </if>
      <if test="checkAlert != null" >
        #{checkAlert,jdbcType=INTEGER},
      </if>
      <if test="orgProperty != null" >
        #{orgProperty,jdbcType=TINYINT},
      </if>
      <if test="insideFlag != null" >
        #{insideFlag,jdbcType=INTEGER},
      </if>
      <if test="address != null" >
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="lastRemindDate != null" >
        #{lastRemindDate,jdbcType=DATE},
      </if>
      <if test="discountRule != null" >
        #{discountRule,jdbcType=INTEGER},
      </if>
      <if test="orgId != null" >
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null" >
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="maxUnitPrice != null" >
        #{maxUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="appKey != null" >
        #{appKey,jdbcType=VARCHAR},
      </if>
      <if test="cooperateStartTime != null" >
        #{cooperateStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cooperateEndTime != null" >
        #{cooperateEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cooperateStatus != null" >
        #{cooperateStatus,jdbcType=INTEGER},
      </if>
      <if test="vehicleThreshold != null" >
        #{vehicleThreshold,jdbcType=DOUBLE},
      </if>
      <if test="discountId != null" >
        #{discountId,jdbcType=BIGINT},
      </if>
      <if test="discountPersonalId != null" >
        #{discountPersonalId,jdbcType=BIGINT},
      </if>
      <if test="lineLimitMonthly != null" >
        #{lineLimitMonthly,jdbcType=DOUBLE},
      </if>
      <if test="vehicleNo != null" >
        #{vehicleNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.core.model.AgencyInfo" >
    update ${siacSchema}.AGENCY_INFO
    <set >
      <if test="agencyName != null" >
        AGENCY_NAME = #{agencyName,jdbcType=VARCHAR},
      </if>
      <if test="discountInner != null" >
        DISCOUNT_INNER = #{discountInner,jdbcType=DOUBLE},
      </if>
      <if test="discountOuter != null" >
        DISCOUNT_OUTER = #{discountOuter,jdbcType=DOUBLE},
      </if>
      <if test="createdUser != null" >
        CREATED_USER = #{createdUser,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        CREATED_TIME = #{createdTime,jdbcType=VARCHAR},
      </if>
      <if test="updatedUser != null" >
        UPDATED_USER = #{updatedUser,jdbcType=VARCHAR},
      </if>
      <if test="updatedTime != null" >
        UPDATED_TIME = #{updatedTime,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        STATUS = #{status,jdbcType=DOUBLE},
      </if>
      <if test="pickupWeeks != null" >
        PICKUP_WEEKS = #{pickupWeeks,jdbcType=VARCHAR},
      </if>
      <if test="returnWeeks != null" >
        RETURN_WEEKS = #{returnWeeks,jdbcType=VARCHAR},
      </if>
      <if test="pickupTime != null" >
        PICKUP_TIME = #{pickupTime,jdbcType=VARCHAR},
      </if>
      <if test="returnTime != null" >
        RETURN_TIME = #{returnTime,jdbcType=VARCHAR},
      </if>
      <if test="maxUserHour != null" >
        MAX_USER_HOUR = #{maxUserHour,jdbcType=VARCHAR},
      </if>
      <if test="exemptDeposit != null" >
        EXEMPT_DEPOSIT = #{exemptDeposit,jdbcType=INTEGER},
      </if>
      <if test="contact != null" >
        CONTACT = #{contact,jdbcType=VARCHAR},
      </if>
      <if test="tel != null" >
        TEL = #{tel,jdbcType=VARCHAR},
      </if>
      <if test="mobilePhone != null" >
        MOBILE_PHONE = #{mobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="mail != null" >
        MAIL = #{mail,jdbcType=VARCHAR},
      </if>
      <if test="licenseNo != null" >
        LICENSE_NO = #{licenseNo,jdbcType=VARCHAR},
      </if>
      <if test="fax != null" >
        FAX = #{fax,jdbcType=VARCHAR},
      </if>
      <if test="licenseNoImgUrl != null" >
        LICENSE_NO_IMG_URL = #{licenseNoImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="contractImgUrl != null" >
        CONTRACT_IMG_URL = #{contractImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="taxRegistrationImgUrl != null" >
        TAX_REGISTRATION_IMG_URL = #{taxRegistrationImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="orgCodeImgUrl != null" >
        ORG_CODE_IMG_URL = #{orgCodeImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="payWay != null" >
        PAY_WAY = #{payWay,jdbcType=DOUBLE},
      </if>
      <if test="deposit != null" >
        DEPOSIT = #{deposit,jdbcType=DOUBLE},
      </if>
      <if test="rentMins != null" >
        RENT_MINS = #{rentMins,jdbcType=DOUBLE},
      </if>
      <if test="checkDate != null" >
        CHECK_DATE = #{checkDate,jdbcType=VARCHAR},
      </if>
      <if test="checkAlert != null" >
        CHECK_ALERT = #{checkAlert,jdbcType=INTEGER},
      </if>
      <if test="orgProperty != null" >
        ORG_PROPERTY = #{orgProperty,jdbcType=TINYINT},
      </if>
      <if test="insideFlag != null" >
        INSIDE_FLAG = #{insideFlag,jdbcType=INTEGER},
      </if>
      <if test="address != null" >
        ADDRESS = #{address,jdbcType=VARCHAR},
      </if>
      <if test="lastRemindDate != null" >
        LAST_REMIND_DATE = #{lastRemindDate,jdbcType=DATE},
      </if>
      <if test="discountRule != null" >
        DISCOUNT_RULE = #{discountRule,jdbcType=INTEGER},
      </if>
      <if test="orgId != null" >
        org_id = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null" >
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="maxUnitPrice != null" >
        max_unit_price = #{maxUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="appKey != null" >
        APP_KEY = #{appKey,jdbcType=VARCHAR},
      </if>
      <if test="cooperateStartTime != null" >
        COOPERATE_START_TIME = #{cooperateStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cooperateEndTime != null" >
        COOPERATE_END_TIME = #{cooperateEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cooperateStatus != null" >
        COOPERATE_STATUS = #{cooperateStatus,jdbcType=INTEGER},
      </if>
      <if test="vehicleThreshold != null" >
        VEHICLE_THRESHOLD = #{vehicleThreshold,jdbcType=DOUBLE},
      </if>
      <if test="discountId != null" >
        DISCOUNT_ID = #{discountId,jdbcType=BIGINT},
      </if>
      <if test="discountPersonalId != null" >
        DISCOUNT_PERSONAL_ID = #{discountPersonalId,jdbcType=BIGINT},
      </if>
      <if test="lineLimitMonthly != null" >
        LINE_LIMIT_MONTHLY = #{lineLimitMonthly,jdbcType=DOUBLE},
      </if>
      <if test="vehicleNo != null" >
        VEHICLE_NO = #{vehicleNo,jdbcType=VARCHAR},
      </if>
    </set>
    where AGENCY_ID = #{agencyId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.core.model.AgencyInfo" >
    update ${siacSchema}.AGENCY_INFO
    set AGENCY_NAME = #{agencyName,jdbcType=VARCHAR},
      DISCOUNT_INNER = #{discountInner,jdbcType=DOUBLE},
      DISCOUNT_OUTER = #{discountOuter,jdbcType=DOUBLE},
      CREATED_USER = #{createdUser,jdbcType=VARCHAR},
      CREATED_TIME = #{createdTime,jdbcType=VARCHAR},
      UPDATED_USER = #{updatedUser,jdbcType=VARCHAR},
      UPDATED_TIME = #{updatedTime,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=DOUBLE},
      PICKUP_WEEKS = #{pickupWeeks,jdbcType=VARCHAR},
      RETURN_WEEKS = #{returnWeeks,jdbcType=VARCHAR},
      PICKUP_TIME = #{pickupTime,jdbcType=VARCHAR},
      RETURN_TIME = #{returnTime,jdbcType=VARCHAR},
      MAX_USER_HOUR = #{maxUserHour,jdbcType=VARCHAR},
      EXEMPT_DEPOSIT = #{exemptDeposit,jdbcType=INTEGER},
      CONTACT = #{contact,jdbcType=VARCHAR},
      TEL = #{tel,jdbcType=VARCHAR},
      MOBILE_PHONE = #{mobilePhone,jdbcType=VARCHAR},
      MAIL = #{mail,jdbcType=VARCHAR},
      LICENSE_NO = #{licenseNo,jdbcType=VARCHAR},
      FAX = #{fax,jdbcType=VARCHAR},
      LICENSE_NO_IMG_URL = #{licenseNoImgUrl,jdbcType=VARCHAR},
      CONTRACT_IMG_URL = #{contractImgUrl,jdbcType=VARCHAR},
      TAX_REGISTRATION_IMG_URL = #{taxRegistrationImgUrl,jdbcType=VARCHAR},
      ORG_CODE_IMG_URL = #{orgCodeImgUrl,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      PAY_WAY = #{payWay,jdbcType=DOUBLE},
      DEPOSIT = #{deposit,jdbcType=DOUBLE},
      RENT_MINS = #{rentMins,jdbcType=DOUBLE},
      CHECK_DATE = #{checkDate,jdbcType=VARCHAR},
      CHECK_ALERT = #{checkAlert,jdbcType=INTEGER},
      ORG_PROPERTY = #{orgProperty,jdbcType=TINYINT},
      INSIDE_FLAG = #{insideFlag,jdbcType=INTEGER},
      ADDRESS = #{address,jdbcType=VARCHAR},
      LAST_REMIND_DATE = #{lastRemindDate,jdbcType=DATE},
      DISCOUNT_RULE = #{discountRule,jdbcType=INTEGER},
      org_name = #{orgName,jdbcType=VARCHAR},
      max_unit_price = #{maxUnitPrice,jdbcType=DECIMAL},
      APP_KEY = #{appKey,jdbcType=VARCHAR},
      COOPERATE_START_TIME = #{cooperateStartTime,jdbcType=TIMESTAMP},
      COOPERATE_END_TIME = #{cooperateEndTime,jdbcType=TIMESTAMP},
      COOPERATE_STATUS = #{cooperateStatus,jdbcType=INTEGER},
      VEHICLE_THRESHOLD = #{vehicleThreshold,jdbcType=DOUBLE},
      DISCOUNT_ID = #{discountId,jdbcType=BIGINT},
      DISCOUNT_PERSONAL_ID = #{discountPersonalId,jdbcType=BIGINT},
      LINE_LIMIT_MONTHLY = #{lineLimitMonthly,jdbcType=DOUBLE},
      VEHICLE_NO = #{vehicleNo,jdbcType=VARCHAR}
    where AGENCY_ID = #{agencyId,jdbcType=VARCHAR}
  </update>

  <select id="getAgencyInfoByName" resultType="com.extracme.evcard.membership.core.model.EnterpriseAgencyInfo">
    SELECT B.USER_TYPE AS userType, A.AGENCY_NAME AS agencyName, A.STATUS AS status, A.AGENCY_ID AS agencyId
    FROM ${siacSchema}.AGENCY_INFO A,${iplatSchema}.T_ES_USER B
    WHERE A.AGENCY_ID = B.ORG_CODE AND B.NAME = #{name} AND B.USER_TYPE in (0, 1)
  </select>

  <select id="getFlagByAgencyId" resultType="int">
    SELECT INSIDE_FLAG
    FROM ${siacSchema}.AGENCY_INFO where agency_id = #{agencyId}
  </select>

  <select id="getAgencyMinsById" resultType="com.extracme.evcard.membership.core.dto.AgencyMinsDto">
    SELECT RENT_MINS AS rentMins,STATUS ,PAY_WAY AS payWay
    FROM ${siacSchema}.AGENCY_INFO
    WHERE AGENCY_ID = #{agencyId}
  </select>


  <select id="getZhiMaCreditFlagByOrgId" resultType="java.lang.Integer">
    select
    ZHI_MA_CREDIT_FLAG
    from ${isvSchema}.org_info
    WHERE ORG_ID=#{orgId}
  </select>

  <select id="canUseZhimaCitys" resultType="java.lang.String">
    select
    ORG_ID
    from ${isvSchema}.org_info
    where ZHI_MA_CREDIT_FLAG = 1
  </select>

  <select id="queryAgencyInfoByAgencyId" resultMap="BaseResultMap" parameterType="string">
    SELECT
    <include refid="Base_Column_List" />
    FROM ${siacSchema}.AGENCY_INFO where agency_id = #{agencyId}
  </select>

  <select id="queryAllAgencyInfo" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM ${siacSchema}.AGENCY_INFO
  </select>

  <select id="countAgencyInfoNum" resultType="int">
    SELECT
    count(*) as num
    FROM ${siacSchema}.AGENCY_INFO
    <where>
      <if test="agencyName != null and agencyName != ''">
        AGENCY_NAME like concat(#{agencyName},'%')
      </if>
      <if test="orgId != null and orgId != ''">
        and  org_Id like  concat(#{orgId},'%')
      </if>
      <if test="cooperateStatus != null">
        and  COOPERATE_STATUS = #{cooperateStatus}
      </if>
    </where>
  </select>
  <select id="queryAgencyInfoByCondition" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM ${siacSchema}.AGENCY_INFO
    <where>
      <if test="agencyName != null and agencyName != ''">
        AGENCY_NAME like concat(#{agencyName},'%')
      </if>
      <if test="orgId != null and orgId != ''">
        and  org_Id like  concat(#{orgId},'%')
      </if>
      <if test="cooperateStatus != null">
        and  COOPERATE_STATUS = #{cooperateStatus}
      </if>
    </where>
     limit #{page.offSet},#{page.limitSet}
  </select>
  <select id="getMaxAgencyId" resultType="string">
    SELECT MAX(AGENCY_ID) as agencyId
    FROM ${siacSchema}.agency_info
  </select>

  <select id="countByAgencyName" resultType="int">
    SELECT  count(*)
    FROM ${siacSchema}.agency_info
    where AGENCY_NAME = #{agencyName}
    <if test="agencyId != null">
      And AGENCY_ID != #{agencyId}
    </if>
  </select>

  <select id="queryAgencyListByCondition" resultMap="BaseResultMap" parameterType="com.extracme.evcard.membership.core.input.QueryAgencyListConditionInput">
    select
    <include refid="Base_Column_List"/>
    from ${siacSchema}.agency_info
    <where>
      <if test="agencyIds != null">
        <choose>
          <when test="isInQueryAgencyIds == false">
            and AGENCY_ID not in
          </when>
          <otherwise>
            and AGENCY_ID in
          </otherwise>
        </choose>
        <foreach item="item" collection="agencyIds" index="index" open="(" close=")" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="status != null">
        and `status` = #{status}
      </if>
      <if test="orgId != null ">
        <choose>
          <when test="isLikeQueryOrgId == 1">
            and ORG_ID like concat('%',#{orgId})
          </when>
          <when test="isLikeQueryOrgId == 2">
            and ORG_ID like concat(#{orgId},'%')
          </when>
          <when test="isLikeQueryOrgId == 3">
            and ORG_ID like concat('%',#{orgId},'%')
          </when>
          <otherwise>
            and ORG_ID = #{orgId}
          </otherwise>
        </choose>
      </if>
    </where>
  </select>

  <select id="selectByPrimaryKeyAndStatus" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from ${siacSchema}.AGENCY_INFO
    where AGENCY_ID = #{agencyId,jdbcType=VARCHAR}
    <if test="status != null">
      AND `status` = #{status}
    </if>
  </select>

  <!-- 通过登陆者信息查询关联企业 -->
  <select id="getAgencyId" parameterType="string" resultType="string">
        SELECT
        a.AGENCY_ID
        FROM
        ${iplatSchema}.t_es_user a
        WHERE a.NAME = #{name,jdbcType=VARCHAR}
    </select>


</mapper>