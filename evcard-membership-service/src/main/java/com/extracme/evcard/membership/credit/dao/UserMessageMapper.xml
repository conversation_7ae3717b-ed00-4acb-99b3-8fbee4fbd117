<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- UserMessageMapper，，对应表user_message -->
<mapper namespace="com.extracme.evcard.membership.credit.dao.UserMessageMapper">
    <!-- 返回结果集Map -->
    <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.credit.model.UserMessage">
        <id column="MESSAGE_SEQ" jdbcType="BIGINT" property="messageSeq" />
        <result column="MESSAGE_SEQ" jdbcType="BIGINT" property="messageSeq" />
        <result column="AUTH_ID" jdbcType="VARCHAR" property="authId" />
        <result column="MEMBERSHIP_TYPE" jdbcType="DECIMAL" property="membershipType" />
        <result column="MESSAGE_CONTENT" jdbcType="LONGVARCHAR" property="messageContent" />
        <result column="MESSAGE_TITLE" jdbcType="VARCHAR" property="messageTitle" />
        <result column="TYPE" jdbcType="DECIMAL" property="type" />
        <result column="SEND_TYPE" jdbcType="DECIMAL" property="sendType" />
        <result column="URL" jdbcType="LONGVARCHAR" property="url" />
        <result column="INFO_ORIGIN" jdbcType="VARCHAR" property="infoOrigin" />
        <result column="CREATED_USER" jdbcType="VARCHAR" property="createdUser" />
        <result column="CREATED_TIME" jdbcType="VARCHAR" property="createdTime" />
        <result column="UPDATED_USER" jdbcType="VARCHAR" property="updatedUser" />
        <result column="UPDATED_TIME" jdbcType="VARCHAR" property="updatedTime" />
    </resultMap>
    
    <!--数据列-->
    <sql id="Base_Column_List" >
            MESSAGE_SEQ,
            AUTH_ID,
            MEMBERSHIP_TYPE,
            MESSAGE_CONTENT,
            MESSAGE_TITLE,
            TYPE,
            SEND_TYPE,
            URL,
            INFO_ORIGIN,
            CREATED_USER,
            CREATED_TIME,
            UPDATED_USER,
            UPDATED_TIME
    </sql>
    
    <!-- 保存数据 -->
    <insert id="save" parameterType="com.extracme.evcard.membership.credit.model.UserMessage">
        insert into ${locationSchema}.user_message (
            MESSAGE_SEQ,
            AUTH_ID,
            MEMBERSHIP_TYPE,
            MESSAGE_CONTENT,
            MESSAGE_TITLE,
            TYPE,
            SEND_TYPE,
            URL,
            INFO_ORIGIN,
            CREATED_USER,
            CREATED_TIME,
            UPDATED_USER,
            UPDATED_TIME
        ) values (
            #{messageSeq,jdbcType=BIGINT},
            #{authId,jdbcType=VARCHAR},
            #{membershipType,jdbcType=DECIMAL},
            #{messageContent,jdbcType=LONGVARCHAR},
            #{messageTitle,jdbcType=VARCHAR},
            #{type,jdbcType=DECIMAL},
            #{sendType,jdbcType=DECIMAL},
            #{url,jdbcType=LONGVARCHAR},
            #{infoOrigin,jdbcType=VARCHAR},
            #{createdUser,jdbcType=VARCHAR},
            #{createdTime,jdbcType=VARCHAR},
            #{updatedUser,jdbcType=VARCHAR},
            #{updatedTime,jdbcType=VARCHAR}
        )
    </insert>
    
    <!-- 根据主键取得数据 -->
    <select id="selectById" parameterType="long" resultMap="BaseResultMap">
        select 
        <include refid="Base_Column_List" />
         from ${locationSchema}.user_message where MESSAGE_SEQ = #{messageSeq}
    </select>
    
    <!--分页获取获取所有数据，分页使用(实际项目中需要自己改造，自己需要几个条件则添加几个条件)-->
    <select id="selectAllPage" resultMap="BaseResultMap" parameterType="java.util.Map" >
        select 
        <include refid="Base_Column_List" />
        from ${locationSchema}.user_message where status = 1
     </select>
  
    <!-- 根据主键删除数据 -->
    <delete id="deleteById" parameterType="long">
        delete from ${locationSchema}.user_message where MESSAGE_SEQ = #{messageSeq,jdbcType=BIGINT}
    </delete>

    <!-- 更新数据 -->
    <update id="updateByIdSelective" parameterType="com.extracme.evcard.membership.credit.model.UserMessage">
        update ${locationSchema}.user_message
        <set>
                        <if test="entityMap.authId != null">
                                AUTH_ID = #{entityMap.authId,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.membershipType != null">
                                MEMBERSHIP_TYPE = #{entityMap.membershipType,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.messageContent != null">
                                MESSAGE_CONTENT = #{entityMap.messageContent,jdbcType=LONGVARCHAR},
                        </if>
                        <if test="entityMap.messageTitle != null">
                                MESSAGE_TITLE = #{entityMap.messageTitle,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.type != null">
                                TYPE = #{entityMap.type,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.sendType != null">
                                SEND_TYPE = #{entityMap.sendType,jdbcType=DECIMAL},
                        </if>
                        <if test="entityMap.url != null">
                                URL = #{entityMap.url,jdbcType=LONGVARCHAR},
                        </if>
                        <if test="entityMap.infoOrigin != null">
                                INFO_ORIGIN = #{entityMap.infoOrigin,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.createdUser != null">
                                CREATED_USER = #{entityMap.createdUser,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.createdTime != null">
                                CREATED_TIME = #{entityMap.createdTime,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.updatedUser != null">
                                UPDATED_USER = #{entityMap.updatedUser,jdbcType=VARCHAR},
                        </if>
                        <if test="entityMap.updatedTime != null">
                                UPDATED_TIME = #{entityMap.updatedTime,jdbcType=VARCHAR}
                        </if>
        </set>
        where MESSAGE_SEQ = #{messageSeq,jdbcType=BIGINT} and update_time = #{entityMap.indbUpdateTime,jdbcType=TIMESTAMP}
    </update>
    
    <!-- 批量插入 -->
     <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ${locationSchema}.user_message (
               MESSAGE_SEQ,
               AUTH_ID,
               MEMBERSHIP_TYPE,
               MESSAGE_CONTENT,
               MESSAGE_TITLE,
               TYPE,
               SEND_TYPE,
               URL,
               INFO_ORIGIN,
               CREATED_USER,
               CREATED_TIME,
               UPDATED_USER,
               UPDATED_TIME
        ) 
        <foreach collection="list" item="item" index="index" separator="UNION ALL">
            SELECT 
                  #{item.messageSeq,jdbcType=BIGINT},
                  #{item.authId,jdbcType=VARCHAR},
                  #{item.membershipType,jdbcType=DECIMAL},
                  #{item.messageContent,jdbcType=LONGVARCHAR},
                  #{item.messageTitle,jdbcType=VARCHAR},
                  #{item.type,jdbcType=DECIMAL},
                  #{item.sendType,jdbcType=DECIMAL},
                  #{item.url,jdbcType=LONGVARCHAR},
                  #{item.infoOrigin,jdbcType=VARCHAR},
                  #{item.createdUser,jdbcType=VARCHAR},
                  #{item.createdTime,jdbcType=VARCHAR},
                  #{item.updatedUser,jdbcType=VARCHAR},
                  #{item.updatedTime,jdbcType=VARCHAR}
            FROM DUAL
        </foreach>
    </insert>
    
     <!-- 单条逻辑删除 -->
     <update id="logicalSelectById">
        update ${locationSchema}.user_message set status = 1 , update_time = #{updateTime,jdbcType=TIMESTAMP} , update_oper_id = #{updateOperId,jdbcType=BIGINT} , update_oper_name = #{updateOperName,jdbcType=VARCHAR} where id = #{id,jdbcType=BIGINT} and status = 1
     </update>
     
     <!-- 批量逻辑删除 -->
     <update id="batchLogicalSelectById" parameterType="java.util.List">  
        update ${locationSchema}.user_message  set status = 1 , update_time = #{updateTime,jdbcType=TIMESTAMP} , update_oper_id = #{updateOperId,jdbcType=BIGINT} , update_oper_name = #{updateOperName,jdbcType=VARCHAR}  where id in  
         <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">  
            ${item}
         </foreach> 
         and status = 1
     </update>
     
     <!-- 保存数据 -->
    <insert id="addUserMessage" parameterType="com.extracme.evcard.membership.credit.dto.UserMessageDto"
            useGeneratedKeys="true" keyProperty="messageSeq" >

        insert into ${siacSchema}.user_message 
        <trim  prefix="(" suffix=")" suffixOverrides=",">
            <if test="authId!=null">AUTH_ID,</if>
            <if test="membershipType!=null">MEMBERSHIP_TYPE,</if>
            <if test="messageContent!=null">MESSAGE_CONTENT,</if>
            <if test="messageTitle!=null">MESSAGE_TITLE,</if>
            <if test="type!=null">TYPE,</if>
            <if test="sendType!=null">SEND_TYPE,</if>
            <if test="url!=null">URL,</if>
            <if test="infoOrigin!=null">INFO_ORIGIN,</if>
            <if test="createdUser!=null">CREATED_USER,</if>
            <if test="createdTime!=null">CREATED_TIME,</if>
            <if test="updatedUser!=null">UPDATED_USER,</if>
            <if test="updatedTime!=null">UPDATED_TIME</if>
        </trim>
        <trim  prefix="values (" suffix=")" suffixOverrides=",">
            <if test="authId!=null">#{authId,jdbcType=VARCHAR},</if>
            <if test="membershipType!=null">#{membershipType,jdbcType=DECIMAL},</if>
            <if test="messageContent!=null">#{messageContent,jdbcType=LONGVARCHAR},</if>
            <if test="messageTitle!=null">#{messageTitle,jdbcType=VARCHAR},</if>
            <if test="type!=null">#{type,jdbcType=DECIMAL},</if>
            <if test="sendType!=null">#{sendType,jdbcType=DECIMAL},</if>
            <if test="url!=null">#{url,jdbcType=LONGVARCHAR},</if>
            <if test="infoOrigin!=null">#{infoOrigin,jdbcType=VARCHAR},</if>
            <if test="createdUser!=null">#{createdUser,jdbcType=VARCHAR},</if>
            <if test="createdTime!=null">#{createdTime,jdbcType=VARCHAR},</if>
            <if test="updatedUser!=null">#{updatedUser,jdbcType=VARCHAR},</if>
            <if test="updatedTime!=null">#{updatedTime,jdbcType=VARCHAR}</if>
        </trim>
    </insert>
</mapper>