package com.extracme.evcard.membership.common;

import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.font.FontProvider;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.*;

@Slf4j
public class PdfUtil {
    public static void main(String[] args) throws IOException {
        long startTime = System.currentTimeMillis();

        String htmlFile = "C:\\Users\\<USER>\\Desktop\\work\\租车单\\20250319租车合同改造\\2.html";
        String pdfFile = "C:\\Users\\<USER>\\Desktop\\work\\租车单\\20250319租车合同改造\\2.pdf";
        InputStream inputStream = new FileInputStream(htmlFile);
        OutputStream outputStream = new FileOutputStream(pdfFile);
        String fontPath = "font/simsun.ttc,0";
        PdfUtil.convertToPdf(htmlFile, fontPath, outputStream);
        log.info("转换结束，耗时：{}ms", System.currentTimeMillis() - startTime);
    }

    public static void convertToPdf(String htmlPath, String fontPath, OutputStream outputStream) throws IOException {
        InputStream inputStream = new FileInputStream(htmlPath);
        try {
            PdfWriter pdfWriter = new PdfWriter(outputStream);
            PdfDocument pdfDocument = new PdfDocument(pdfWriter);
            //设置为A4大小
            pdfDocument.setDefaultPageSize(PageSize.A4);
            //添加中文字体支持
            ConverterProperties properties = new ConverterProperties();
            FontProvider fontProvider = new FontProvider();
            // 设置字体
            //添加自定义字体，例如微软雅黑
            if (StringUtils.isNotBlank(fontPath)) {
                PdfFont microsoft = PdfFontFactory.createFont(fontPath, PdfEncodings.IDENTITY_H, false);
                fontProvider.addFont(microsoft.getFontProgram(), PdfEncodings.IDENTITY_H);
            }
            properties.setFontProvider(fontProvider);
            // 生成pdf文档
            HtmlConverter.convertToPdf(inputStream,outputStream , properties);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            inputStream.close();
        }

    }

    /**
     * html 页面转pdf
     * @param htmlPath
     * @param fontPath
     * @return
     * @throws IOException
     */
    public static ByteArrayOutputStream convertToPdf2(String htmlPath, String fontPath) throws IOException {
        ByteArrayOutputStream pdfOutputStream = new ByteArrayOutputStream();
        if (StringUtils.isBlank(fontPath)) {
            fontPath = "font/simsun.ttc,0";
        }
        try {
            //添加中文字体支持
            ConverterProperties properties = new ConverterProperties();
            FontProvider fontProvider = new FontProvider();
            // 设置字体
            //添加自定义字体，例如微软雅黑
            if (StringUtils.isNotBlank(fontPath)) {
                PdfFont microsoft = PdfFontFactory.createFont(fontPath, PdfEncodings.IDENTITY_H, false);
                fontProvider.addFont(microsoft.getFontProgram(), PdfEncodings.IDENTITY_H);
            }
            properties.setFontProvider(fontProvider);
            // 生成pdf文档
            HtmlConverter.convertToPdf(htmlPath,pdfOutputStream , properties);
        } catch (Exception e) {
            log.error("html转 pdf 异常,htmlPath=[{}]",htmlPath,e);
        } finally {

        }
        return pdfOutputStream;
    }
}
