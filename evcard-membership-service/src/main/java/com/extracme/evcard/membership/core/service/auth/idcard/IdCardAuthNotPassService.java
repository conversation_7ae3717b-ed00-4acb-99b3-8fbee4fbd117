package com.extracme.evcard.membership.core.service.auth.idcard;

import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.canstant.OcrChannelConstants;
import com.extracme.evcard.membership.core.dto.AuditIdCardDTO;
import com.extracme.evcard.membership.core.dto.input.AuditIdCardInput;
import com.extracme.evcard.membership.core.enums.MemOperateTypeEnum;
import com.extracme.evcard.membership.core.input.UserOperationLogInput;
import com.extracme.evcard.membership.core.model.MemberIdentityDocument;
import com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs;
import com.extracme.evcard.rpc.enums.StatusCode;
import javafx.util.Pair;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class IdCardAuthNotPassService extends IdCardAuthAbstract {


    @Override
    protected void messagePush(AuditIdCardDTO auditIdCardDTO) {
        //1操作日志记录
        String reviewRemark = auditIdCardDTO.getAuditIdCardInput().getReviewRemark();
        String operatorContent;
        if (StringUtils.isNotBlank(reviewRemark)) {
            operatorContent ="人工认证不通过，不通过原因" + reviewRemark;
        }else{
            operatorContent ="人工认证不通过";
        }
        UserOperationLogInput logRecord = getLogRecord(auditIdCardDTO, operatorContent, MemOperateTypeEnum.IDCARD_MANUAL_AUDIT_NOT_PASS);
        memberShipService.saveUserOperationLog(logRecord);

        //2 mq
        pushOldMemberMq(auditIdCardDTO, 2);
        pushNewMemberMq(auditIdCardDTO, 0, 2, 5);

        //埋点
        identityCertService.memberAuditTrack(false,auditIdCardDTO.getMembershipBasicInfo().getAuthId(),0,operatorContent,"","");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    protected Pair<Integer, String> updateDbRecord(AuditIdCardDTO auditIdCardDTO) {
        AuditIdCardInput auditIdCardInput = auditIdCardDTO.getAuditIdCardInput();

        //membershipInfoMapper 更新
        MembershipInfoWithBLOBs membershipInfo = getUpdatedMembershipInfo(auditIdCardDTO, 2);
        //membershipInfoMapper.updateByPrimaryKey(membershipInfo);
        membershipInfoMapper.updateByPrimaryKeySelective(membershipInfo);

        //memberIdentityDocumentMapper 更新
        MemberIdentityDocument updateMemberIdentityDocument = getUpdatedMemberIdentityDocument(auditIdCardDTO, 5);
        updateMemberIdentityDocument.setReviewIds(auditIdCardInput.getReviewItemIds());
        updateMemberIdentityDocument.setReviewItemNames(auditIdCardInput.getReviewItemName());
        updateMemberIdentityDocument.setReviewRemark(auditIdCardInput.getReviewRemark());
        memberIdentityDocumentMapper.updateByPrimaryKeySelective(updateMemberIdentityDocument);

        // 更新账号申请进度
        applyProgress(auditIdCardDTO, "审核未通过，" + auditIdCardInput.getReviewItemName() + "有误");

        return new Pair<>(0, OcrChannelConstants.IDCARD_AUDIT_OK_MESSAGE);
    }

    @Override
    protected Pair<Integer, String> serviceCheck(AuditIdCardDTO auditIdCardDTO) {
        AuditIdCardInput idCardInput = auditIdCardDTO.getAuditIdCardInput();

        String reviewItems = idCardInput.getReviewItems();
        if (BussinessConstants.IDCARD_REVIEW_ITEMS_OK.equals(reviewItems)) {
            return new Pair<>(StatusCode.ILLEGAL_PARAM.getCode(), "ReviewItems"+StatusCode.ILLEGAL_PARAM.getMsg());
        }

        //会员管理系统这一项必输。
        if (idCardInput.getOperateSourceType() == 0) {
            if (StringUtils.isBlank(idCardInput.getReviewRemark())) {
                return new Pair<>(StatusCode.PARAM_EMPTY.getCode(), "ReviewRemark"+StatusCode.PARAM_EMPTY.getMsg());
            }
        }


        String reviewItemIds = idCardInput.getReviewItemIds();
        if (StringUtils.isBlank(reviewItemIds)) {
            reviewItemIds = getIdsFromReviewItems(reviewItems);
            idCardInput.setReviewItemIds(reviewItemIds);
        } else {
            if (!checkReviewIds(reviewItems, reviewItemIds)) {
                return new Pair<>(-1, "reviewIds  参数不正确");
            }
        }

        String reviewItemName = idCardInput.getReviewItemName();
        if (StringUtils.isBlank(reviewItemName)) {
            reviewItemName = buildReviewDetailStr(reviewItems);
            idCardInput.setReviewItemName(reviewItemName);
        }

        //TODO 本国籍 和 外国籍 校验规则

        return new Pair<>(0, OcrChannelConstants.IDCARD_AUDIT_OK_MESSAGE);
    }

    //证件编号、姓名 、过期日期、身份证正页(护照/通行证)、身份证副页、手持证件照、人脸照片
    private static final String[] REVIEW_ITEMS_NAMES = {"证件编号", "姓名", "过期日期", "身份证正页(护照/通行证)", "身份证副页", "手持证件照", "人脸照片"};

    private String buildReviewDetailStr(String reviewItems) {
        if (org.apache.commons.lang3.StringUtils.isBlank(reviewItems)) {
            return org.apache.commons.lang3.StringUtils.EMPTY;
        }
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < reviewItems.length() && i < REVIEW_ITEMS_NAMES.length; i++) {
            String flag = String.valueOf(reviewItems.charAt(i));
            if ("2".equals(flag)) {
                sb.append(REVIEW_ITEMS_NAMES[i]).append(";");
            }
        }
        String desc = sb.toString();
        if (desc.endsWith(";")) {
            desc = desc.substring(0, desc.length() - 1);
        }
        return desc;
    }

    //TODO reviewItems 和 reviewIds 匹配的格式 校验。
    public Boolean checkReviewIds(String reviewItems, String reviewIds) {

        return true;
    }

    /**
     * 通过reviewItems 计算 reviewItemIds
     *
     * @param reviewItems
     * @return
     */
    public String getIdsFromReviewItems(String reviewItems) {
        StringBuffer sb = new StringBuffer();
        char[] chars = reviewItems.toCharArray();
        int length = chars.length;
        for (int i = 0; i < chars.length; i++) {
            char now = chars[i];
            if (now == '2') {
                String result = String.valueOf(i + 1);
                if (i + 1 == 10) {
                    result = "A";
                } else if (i + 1 == 11) {
                    result = "B";
                } else if (i + 1 == 11) {
                    result = "C";
                }
                sb.append(result);
                if (i != length - 1) {
                    sb.append(";");
                }
            }
        }
        return sb.toString();
    }


}
