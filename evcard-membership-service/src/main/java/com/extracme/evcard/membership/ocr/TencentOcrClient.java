package com.extracme.evcard.membership.ocr;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.enums.CommonRiskTypeEnums;
import com.extracme.evcard.membership.core.enums.CommonSideEnums;
import com.extracme.evcard.membership.core.enums.OcrChannelEnums;
import com.extracme.evcard.membership.core.enums.OcrTypeEnums;
import com.extracme.evcard.membership.core.manager.MembershipOcrCallLogManager;
import com.extracme.evcard.membership.third.baidu.Base64Util;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.ocr.v20181119.OcrClient;
import com.tencentcloudapi.ocr.v20181119.models.DriverLicenseOCRRequest;
import com.tencentcloudapi.ocr.v20181119.models.DriverLicenseOCRResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 腾讯OCR
 *
 * <AUTHOR>
 * @date 2022/8/17
 */
@Slf4j
@Component
public class TencentOcrClient implements OcrService {

    @Value("${tencent.ocr.url}")
    private String baseUrl;

    @Value("${tencent.ocr.secretId}")
    private String secretId;

    @Value("${tencent.ocr.secretKey}")
    private String secretKey;

    @Value("${tencent.ocr.region}")
    private String region;

    @Resource
    private MembershipOcrCallLogManager membershipOcrCallLogManager;

    @Override
    public String getChannel() {
        return "tencent";
    }

    @Override
    public CommonIdCardOcrResp idCardOcr(CommonIdCardOcrReq req) {
        return null;
    }

    @Override
    public CommonDrivingLicenseOcrResp drivingLicenseOcr(CommonDrivingLicenseOcrReq req) {
        String reqParams = "";
        String respParams = "";
        try {
            //log.info("用户[{}]调用腾讯的驾驶证识别接口，参数：{}", req.getMid(), JSON.toJSONString(req));
            // 设置入参
            DriverLicenseOCRRequest request = new DriverLicenseOCRRequest();
            if (StringUtils.isNotBlank(req.getUrl())) {
                request.setImageUrl(req.getUrl());
            } else if (!ObjectUtils.isEmpty(req.getImage())) {
                request.setImageBase64(Base64Util.encode(req.getImage()));
            } else {
                throw new BusinessException("图片数据不能为空！");
            }
            String cardSide = req.getCommonSide() == null ? CommonSideEnums.FRONT.getSide().toUpperCase() : req.getCommonSide().getSide().toUpperCase();
            request.setCardSide(cardSide);
            reqParams = "cardSide=" + cardSide;

            OcrClient ocrClient = new OcrClient(new Credential(secretId, secretKey), region);
            DriverLicenseOCRResponse response = ocrClient.DriverLicenseOCR(request);
            log.info("用户[{}]调用腾讯的驾驶证识别接口，结果：{}", req.getMid(), JSON.toJSONString(response));
            respParams = JSON.toJSONString(response);

            if (response != null) {
                CommonDrivingLicenseOcrResp resp = new CommonDrivingLicenseOcrResp();
                resp.setLicenseNo(response.getCardCode());
                resp.setName(response.getName());
                resp.setGender(response.getSex());
                resp.setNationality(response.getNationality());
                resp.setAddress(response.getAddress());

                String birthday = null;
                if (StringUtils.isNotBlank(response.getDateOfBirth())) {
                    birthday = ComUtil.getFormatDate(response.getDateOfBirth(), ComUtil.DATE_TYPE5, ComUtil.DATE_TYPE2);
                }
                resp.setBirthday(birthday);

                String issueDate = null;
                if (StringUtils.isNotBlank(response.getDateOfFirstIssue())) {
                    issueDate = ComUtil.getFormatDate(response.getDateOfFirstIssue(), ComUtil.DATE_TYPE5, ComUtil.DATE_TYPE2);
                }
                resp.setIssueDate(issueDate);

                resp.setDriveType(response.getClass_());

                String startDate = null;
                if (StringUtils.isNotBlank(response.getStartDate())) {
                    startDate = ComUtil.getFormatDate(response.getStartDate(), ComUtil.DATE_TYPE5, ComUtil.DATE_TYPE2);
                }
                resp.setStartDate(startDate);

                String endDate = null;
                if (StringUtils.isNotBlank(response.getEndDate())) {
                    if ("长期".equals(response.getEndDate())) {
                        endDate = response.getEndDate();
                    } else if (response.getEndDate().contains("年") && startDate != null) {
                        int year = Integer.parseInt(response.getEndDate().substring(0, response.getEndDate().length() - 1));
                        endDate = ComUtil.addYear(startDate, ComUtil.DATE_TYPE2, year);
                    } else {
                        endDate = ComUtil.getFormatDate(response.getEndDate(), ComUtil.DATE_TYPE5, ComUtil.DATE_TYPE2);
                    }
                }
                resp.setEndDate(endDate);

                resp.setIssuedBy(response.getIssuingAuthority());
                resp.setFileNo(response.getArchivesCode());
                resp.setRecord(response.getRecord());

                CommonRiskTypeEnums riskType = CommonRiskTypeEnums.NORMAL;
                // 驾照OCR忽略以下告警：复印件、翻拍、PS。
                /*if (!ObjectUtils.isEmpty(response.getRecognizeWarnCode())) {
                    *//**
                     * Code 告警码列表和释义：
                     * -9102  复印件告警
                     * -9103  翻拍件告警
                     * -9106  ps告警
                     * 注：告警码可以同时存在多个，这里只做简单处理，从前往后判断，有一个就算它了
                     *//*
                    List<Long> codeList = Arrays.stream(response.getRecognizeWarnCode()).collect(Collectors.toList());
                    if (codeList.contains(-9102L)) {
                        riskType = CommonRiskTypeEnums.COPY;
                    } else if (codeList.contains(-9103L)) {
                        riskType = CommonRiskTypeEnums.SCREEN;
                    } else if (codeList.contains(-9106L)) {
                        riskType = CommonRiskTypeEnums.PS;
                    }
                }*/
                resp.setRiskType(riskType);
                return resp;
            }
        } catch (Exception e) {
            log.error("用户[{}]调用腾讯的驾驶证识别接口异常，req={}", req.getMid(), JSON.toJSONString(req), e);
            respParams = JSON.toJSONString(e);
        } finally {
            membershipOcrCallLogManager.addMembershipOcrCallLog(req.getMid(), OcrChannelEnums.TENCENT,
                    OcrTypeEnums.DRIVING_LICENSE, "https://ocr.tencentcloudapi.com", reqParams, respParams);
        }
        return null;
    }

}
