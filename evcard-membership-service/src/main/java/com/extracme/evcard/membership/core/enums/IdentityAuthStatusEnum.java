package com.extracme.evcard.membership.core.enums;

public enum IdentityAuthStatusEnum {
    //身份证件认证状态 1未认证(未上传) 2未刷脸 3待认证(待人工认证) 4已认证 5认证不通过 + 6即将过期  7已过期
    UN_SUBMIT(1, "未认证(资料未上传)"),
    TO_FACE_REC(2, "未刷脸"),
    TO_MANUAL_REVIEW(3, "待认证(待人工认证)"),
    AUTHENTICATED(4, "已认证"),
    AUTHENTICATE_FAILED(5, "认证不通过"),

    /**
     * 以下为附加状态-数据库字段中实际不存在
     */
    EXTRA_EXPIRING(6, "即将过期"),
    EXTRA_EXPIRED(7, "已过期");

    IdentityAuthStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }


    public static String getDesByCode(Integer v) {
        if (v != null) {
            for (IdentityAuthStatusEnum i : IdentityAuthStatusEnum.values()) {
                if (i.value == v) {
                    return i.getDesc();
                }
            }
        }
        return "";
    }

    /**
     * 状态值
     */
    private Integer value;

    /**
     * 描述
     */
    private String desc;

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public boolean eq(Integer value) {
        return this.value.equals(value);
    }

    public static final boolean unFaceAuthed(Integer value) {
        return TO_FACE_REC.eq(value) || UN_SUBMIT.eq(value);
    }

    /**
     * 2 4,6,7 状态履约 重新认证 高亮
     * 1 3 5履约  重新认证 不高亮
     *
     * @param value
     * @return
     */
    public static final boolean unAuthed(Integer value) {
        return TO_MANUAL_REVIEW.eq(value) || UN_SUBMIT.eq(value) || AUTHENTICATE_FAILED.eq(value) || (0==value);
    }
}
