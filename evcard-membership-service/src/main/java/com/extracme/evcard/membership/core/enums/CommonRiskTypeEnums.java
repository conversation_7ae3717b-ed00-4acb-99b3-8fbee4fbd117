package com.extracme.evcard.membership.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * 统一的证件风险类型枚举
 *
 * <AUTHOR>
 * @date 2022/8/16
 */
@AllArgsConstructor
@Getter
public enum CommonRiskTypeEnums {
    NORMAL("normal", "正常", 1),
    COPY("copy", "复印件", 3),
    TEMPORARY("temporary", "临时身份证（目前只有身份证识别可能会给出这个选项）", 1),
    SCREEN("screen", "翻拍", 4),
    PS("ps", "ps", 5),
    UNKNOWN("unknown", "其他未知情况", 6);

    private String type;
    private String desc;
    //识别结果 1为正常 其他
    private int state;

    public static CommonRiskTypeEnums getByType(String type) {
        for (CommonRiskTypeEnums item : CommonRiskTypeEnums.values()) {
            if (item.getType().equals(type)) {
                return item;
            }
        }
        return CommonRiskTypeEnums.NORMAL;
    }

    public static boolean isValid(String type) {
        return Arrays.asList(NORMAL.getType(), TEMPORARY.getType()).contains(type);
    }
}
