package com.extracme.evcard.membership.core.service.auth;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.OnExceptionContext;
import com.aliyun.openservices.ons.api.SendCallback;
import com.aliyun.openservices.ons.api.SendResult;
import com.aliyun.openservices.ons.api.bean.ProducerBean;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.common.ImgUtils;
import com.extracme.evcard.membership.common.ThreadPoolUtils;
import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.dao.*;
import com.extracme.evcard.membership.core.dto.*;
import com.extracme.evcard.membership.core.dto.input.AuditIdCardInput;
import com.extracme.evcard.membership.core.dto.input.MemberReAuditInput;
import com.extracme.evcard.membership.core.dto.input.OfcSubmitFacePicInput;
import com.extracme.evcard.membership.core.dto.input.SubmitFaceImgSepInput;
import com.extracme.evcard.membership.core.dto.md.SearchCityConfigurationResponse;
import com.extracme.evcard.membership.core.enums.*;
import com.extracme.evcard.membership.core.exception.AuthenticationException;
import com.extracme.evcard.membership.core.input.*;
import com.extracme.evcard.membership.core.model.ApplyProgress;
import com.extracme.evcard.membership.core.model.City;
import com.extracme.evcard.membership.core.model.MemberIdentityDocument;
import com.extracme.evcard.membership.core.model.MmpUserOperationLog;
import com.extracme.evcard.membership.core.service.IMemberCertificationService;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.membership.core.service.auth.idcard.IdCardAuthNotPassService;
import com.extracme.evcard.membership.core.service.auth.idcard.IdCardAuthPassService;
import com.extracme.evcard.membership.core.service.auth.idcard.IdCardReAuthService;
import com.extracme.evcard.membership.core.service.auth.inner.IdentityCertService;
import com.extracme.evcard.membership.core.service.auth.inner.LicenseCertService;
import com.extracme.evcard.membership.core.service.md.MdStoreService;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.ocr.*;
import com.extracme.evcard.mq.bean.EventEnum;
import com.extracme.evcard.mq.bean.event.MemberAudit;
import com.extracme.evcard.protobuf.ProtobufUtil;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.dto.Page;
import com.extracme.evcard.rpc.dto.PageBeanDto;
import com.extracme.evcard.rpc.entity.HidLog;
import com.extracme.evcard.rpc.entity.LogPoint;
import com.extracme.evcard.rpc.enums.StatusCode;
import com.extracme.evcard.rpc.exception.BusinessRuntimeException;
import com.extracme.evcard.rpc.messagepush.service.IMessagepushServ;
import com.extracme.evcard.rpc.messagepush.service.ISensorsdataService;
import com.extracme.evcard.rpc.util.BeanCopyUtils;
import com.extracme.evcard.rpc.util.DateUtil;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;


@Slf4j
@Service
public class MemberCertificationService implements IMemberCertificationService {
    @Autowired
    private OcrAdapter ocrAdapter;

    @Resource
    private MemberIdentityDocumentMapper memberIdentityDocumentMapper;

    @Resource
    private MembershipInfoMapper membershipInfoMapper;

    public static final int EXPIRE_DATE_DAY = 90;
    @Resource
    UserOperatorLogMapper userOperatorLogMapper;

    @Resource
    private IdCardAuthPassService idCardAuthPassService;

    @Resource
    private IdCardAuthNotPassService idCardAuthNotPassService;

    @Resource
    private IdCardReAuthService idCardReAuthService;

    @Resource
    private MmpUserOperationLogMapper mmpUserOperationLogMapper;

    @Resource
    protected IMemberShipService memberShipService;

    @Resource
    protected IdentityCertService identityCertService;

    @Resource
    protected LicenseCertService licenseCertService;

    @Resource
    IMessagepushServ messagepushServ;

    @Resource
    protected ApplyProgressMapper applyProgressMapper;
    @Resource
    private CityMapper cityMapper;

    @Resource
    private MdStoreService mdStoreService;

    @Resource(name = "producer")
    protected ProducerBean producer;
    @Value("${ons.raw.topic}")
    protected String evcardRawDataTopic;

    @Value("${smsMode}")
    private String smsMode;

    // 会员审核-驾驶证人工审核 不通过短信模板id
    @Value("${drivingLicenceAuditNoPassSmsTemplateId:150}")
    private String drivingLicenceAuditNoPassSmsTemplateId;

    // 会员审核-驾驶证人工审核 (非首次)通过短信模板id
    @Value("${drivingLicenceAuditPassSmsTemplateId:149}")
    private String drivingLicenceAuditPassSmsTemplateId;

    @Autowired
    ISensorsdataService sensorsdataService;

    @Override
    public IdCardOcrRespDto idCardOcr(IdCardOcrInput input) {
        HidLog.membership(LogPoint.MEMBER_ID_CARD_OCR, "身份证OCR-开始", input.getMid() + "->" + input.getImageType(), true);
        CommonIdCardOcrReq req = new CommonIdCardOcrReq();
        req.setMid(input.getMid());
        req.setImage(input.getImage());
        req.setCommonSide(CommonSideEnums.getBySide(input.getImageType()));
        if (StringUtils.isNotBlank(input.getUrl())) {
            //处理图片路径->转换为完整的可下载地址 getFileFullPath
            req.setUrl(ComUtil.getFileFullPath(input.getUrl()));
        }
        IdCardOcrRespDto ocrRespDto = new IdCardOcrRespDto();
        try {
            CommonIdCardOcrResp commonOcrResp = ocrAdapter.idCardOcr(req);
            log.info("身份证ocr：input={}, resp={}", JSON.toJSONString(req), JSON.toJSONString(commonOcrResp));
            if (!ObjectUtils.isEmpty(commonOcrResp)) {
                ocrRespDto = new IdCardOcrRespDto();
                BeanUtils.copyProperties(commonOcrResp, ocrRespDto);
                if (StringUtils.equals(BussinessConstants.MAX_CERT_EXPIRE_TIME_DESC, commonOcrResp.getExpirationDate())) {
                    ocrRespDto.setExpireType(2);
                    ocrRespDto.setExpirationDate(StringUtils.EMPTY);
                } else {
                    ocrRespDto.setExpireType(1);
                    String expirationDate = ComUtil.getFormatDate(commonOcrResp.getExpirationDate(),
                            ComUtil.DATE_TYPE2, ComUtil.DATE_TYPE5);
                    if (StringUtils.isNotBlank(expirationDate)) {
                        ocrRespDto.setExpirationDate(expirationDate);
                    } else {
                        ocrRespDto.setExpirationDate(StringUtils.EMPTY);
                    }
                }
                CommonRiskTypeEnums riskTypeEnum = commonOcrResp.getRiskType();
                ocrRespDto.setState(riskTypeEnum.getState());
                ocrRespDto.setRiskTypeDesc(riskTypeEnum.getDesc());
                ocrRespDto.setRiskType(riskTypeEnum.getType());
                boolean isValidPic = CommonRiskTypeEnums.isValid(ocrRespDto.getRiskType());
                if (!isValidPic) {
                    //ocrRespDto.setCode(-1);
                    idCardOcrTrack(false,"暂不支持" + ocrRespDto.getRiskTypeDesc(),input,commonOcrResp);
                    ocrRespDto.setMessage("暂不支持" + ocrRespDto.getRiskTypeDesc());
                }else{
                    idCardOcrTrack(true,"成功",input,commonOcrResp);
                }
                return ocrRespDto;
            }
        } catch (Exception ex) {
            log.error("身份证OCR提取异常，mid=" + input.getMid(), ex);
            HidLog.membership(LogPoint.MEMBER_ID_CARD_OCR, "身份证OCR异常", input.getMid() + ex.getMessage(), false);
        }
        ocrRespDto.setState(2);
        ocrRespDto.setCodeMessage(-1, "身份证OCR信息提取失败");

        idCardOcrTrack(false,"身份证OCR信息提取失败",input,new CommonIdCardOcrResp());
        /**
         * TODO 确认是否存在埋点需求
         * 写入身份证ocr渠道
         */
        HidLog.membership(LogPoint.MEMBER_ID_CARD_OCR, "身份证OCR-完成", input.getMid(), false);
        return ocrRespDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SubmitIdCardRespDto submitUserIdCard(SubmitIdCardInput input) throws AuthenticationException {
        log.info("提交身份证件：input={}.", JSON.toJSONString(input));
        return identityCertService.submitUserIdCert(input);
    }

    @Override
    public IdentityCertInfo getIdentityCertInfo(String mid) {
        /**
         * 历史非大陆用户，事先处理身份证件信息。不在接口层面做兼容
         */
        IdentityCertInfo identityCertInfo = new IdentityCertInfo();
        MemberIdentityDocument identityDocument = memberIdentityDocumentMapper.selectOneByMid(mid);
        if (identityDocument != null) {
            BeanUtils.copyProperties(identityDocument, identityCertInfo);
            identityCertInfo.setIdentityCardImgUrl(ComUtil.getFileFullPath(identityDocument.getIdentityCardImgUrl()));
            identityCertInfo.setReverseIdentityCardImgUrl(ComUtil.getFileFullPath(identityDocument.getReverseIdentityCardImgUrl()));
            identityCertInfo.setHoldCardImgUrl(ComUtil.getFileFullPath(identityDocument.getHoldIdcardPicUrl()));
            identityCertInfo.setFaceImgUrl(ComUtil.getFileFullPath(identityDocument.getFaceRecognitionImgUrl()));
            identityCertInfo.setState(identityCertInfo.getAuthenticationStatus());
            //兼容姓名
            if(StringUtils.isBlank(identityDocument.getName())) {
                MembershipBasicInfo member = membershipInfoMapper.getUserBasicInfoByMid(mid);
                if(member != null) {
                    identityCertInfo.setName(member.getName());
                }
            }
            //组织证件状态 1未认证(未上传) 2未刷脸 3待认证(待人工认证) 4已认证 5认证不通过 + 6即将过期  7已过期
            if (IdentityAuthStatusEnum.AUTHENTICATED.eq(identityDocument.getAuthenticationStatus())) {
                if (IdTypeEnum.isMainlandId(identityDocument.getIdentityType()) && identityDocument.getExpireType().equals(1)) {
                    LocalDate now = LocalDate.now();
                    DateTimeFormatter fmt = DateTimeFormatter.ofPattern(ComUtil.DATE_TYPE5);
                    //非长期处理临期与过期
                    LocalDate expirationDate = LocalDate.parse(identityDocument.getExpirationDate(), fmt);
                    if (now.isAfter(expirationDate)) {
                        identityCertInfo.setState(IdentityAuthStatusEnum.EXTRA_EXPIRED.getValue());
                        identityCertInfo.setAuditIsError("1121111");
                    } else if (now.plusDays(EXPIRE_DATE_DAY).isAfter(expirationDate)) {
                        identityCertInfo.setState(IdentityAuthStatusEnum.EXTRA_EXPIRING.getValue());
                        //证件编号、姓名 、过期日期、身份证正页(护照/通行证)、身份证副页、手持证件照、人脸照片
                        identityCertInfo.setAuditIsError("1111111");
                    }
                }
            } else if (IdentityAuthStatusEnum.AUTHENTICATE_FAILED.eq(identityDocument.getAuthenticationStatus())) {
                if (StringUtils.isNotBlank(identityDocument.getReviewItems())) {
                    //组织审核项：证件编号/姓名/过期日期/身份证正页(护照/通行证)/身份证副页/手持证件照/人脸照片
                    identityCertInfo.setAuditIsError(identityDocument.getReviewItems());
                } else {
                    identityCertInfo.setAuditIsError("2222222");
                }
            }
        } else {
            //未认证用户
            identityCertInfo.setState(1);
        }

        return identityCertInfo;
    }

    @Override
    public DriverLicenseCertInfo getDriverLicenseInfo(String mid) {
        DriverLicenseCertInfo licenseCertInfo = new DriverLicenseCertInfo();
        MembershipBasicInfo member = membershipInfoMapper.getUserBasicInfoByMid(mid);
        if (member != null) {
            BeanCopyUtils.copyProperties(member, licenseCertInfo);
            licenseCertInfo.setDriverLicenseImgUrl(ComUtil.getFileFullPath(member.getDrivingLicenseImgUrl()));
            licenseCertInfo.setReverseDriverLicenseImgUrl(ComUtil.getFileFullPath(member.getFileNoImgUrl()));
            licenseCertInfo.setLicenseType(member.getDrivingLicenseType());
            licenseCertInfo.setFirstObtainTime(member.getObtainDriverTimer());
            licenseCertInfo.setExpirationDate(member.getLicenseExpirationTime());
            licenseCertInfo.setExpireType(1);
            if (StringUtils.equals(member.getLicenseExpirationTime(), BussinessConstants.MAX_CERT_EXPIRE_TIME)) {
                licenseCertInfo.setExpireType(2);
                licenseCertInfo.setExpirationDate(StringUtils.EMPTY);
            }
            licenseCertInfo.setDriverLicenseType(member.getLicenseImgType());
            //组织证件状态 认证状态 1:未认证、2:待认证、3:已认证、4:认证失败、 5:即将过期（剩余90天）、6:已过期
            if (licenseCertInfo.getLicenseReviewStatus() == null) {
                licenseCertInfo.setLicenseReviewStatus(1);
            }
            licenseCertInfo.setState(licenseCertInfo.getLicenseReviewStatus());
            if (LicenseAuthStatusEnum.AUTHENTICATED.eq(member.getLicenseReviewStatus())) {
                if (licenseCertInfo.getExpireType() != 2) {
                    // 如果驾照过期日期为空，直接认为已过期
                    if (StringUtils.isBlank(licenseCertInfo.getExpirationDate())) {
                        licenseCertInfo.setState(LicenseAuthStatusEnum.EXTRA_EXPIRED.getValue());
                        //姓名、准驾车型、驾照编号 、初次领证日期、过期日期、档案编号、驾照照片(不区分正副页)
                        licenseCertInfo.setAuditIsError("1111211");
                    } else {
                        LocalDate now = LocalDate.now();
                        DateTimeFormatter fmt = DateTimeFormatter.ofPattern(ComUtil.DATE_TYPE5);
                        //非长期处理临期与过期
                        LocalDate expirationDate = LocalDate.parse(licenseCertInfo.getExpirationDate(), fmt);
                        if (now.isAfter(expirationDate)) {
                            licenseCertInfo.setState(LicenseAuthStatusEnum.EXTRA_EXPIRED.getValue());
                            //姓名、准驾车型、驾照编号 、初次领证日期、过期日期、档案编号、驾照照片(不区分正副页)
                            licenseCertInfo.setAuditIsError("1111211");
                        } else if (now.plusDays(EXPIRE_DATE_DAY).isAfter(expirationDate)) {
                            licenseCertInfo.setState(LicenseAuthStatusEnum.EXTRA_EXPIRING.getValue());
                            licenseCertInfo.setAuditIsError("1111111");
                        }
                    }
                }
            } else if (LicenseAuthStatusEnum.AUTHENTICATE_FAILED.eq(member.getLicenseReviewStatus())) {
                //姓名 、驾照号、驾驶证照片、准驾车型、初次领证日期、到期时间 、邮寄地址(废弃)、 身份证/护照 、手持身份证/护照、驾驶证档案编号、身份证件编号、人脸照片
                //组织审核项：0姓名、3准驾车型、1驾照编号 、4初次领证日期、5过期日期、9档案编号、2驾照正页面、2驾照副页
                licenseCertInfo.setAuditIsError(buildReviewItem(member.getReviewItems(), new int[]{0, 3, 1, 4, 5, 9, 2, 2}));
            }
        }
        return licenseCertInfo;
    }

    private String buildReviewItem(String reviewStatus, int[] indexs) {
        StringBuffer sb = new StringBuffer();
        for (int index : indexs) {
            if (index < reviewStatus.length()) {
                sb.append(reviewStatus.charAt(index));
            } else {
                sb.append("2");
            }
        }
        return sb.toString();
    }

    @Override
    public DriverLicenseOcrRespDto driverLicenseOcr(DriverLicenseOcrInput input) {
        HidLog.membership(LogPoint.MEMBER_DRIVER_LICENSE_OCR, "驾照OCR", input.getMid() + "->" + input.getImageType(), true);
        CommonDrivingLicenseOcrReq req = new CommonDrivingLicenseOcrReq();
        req.setMid(input.getMid());
        req.setImage(req.getImage());
        req.setCommonSide(CommonSideEnums.getBySide(input.getImageType()));
        if (StringUtils.isNotBlank(input.getUrl())) {
            //处理图片路径->转换为完整的可下载地址 getFileFullPath
            req.setUrl(ComUtil.getFileFullPath(input.getUrl()));
        }
        DriverLicenseOcrRespDto ocrRespDto = new DriverLicenseOcrRespDto();
        try {
            CommonDrivingLicenseOcrResp commonOcrResp = ocrAdapter.drivingLicenseOcr(req);
            log.info("驾驶证ocr：input={}, resp={}", JSON.toJSONString(req), JSON.toJSONString(commonOcrResp));
            if (!ObjectUtils.isEmpty(commonOcrResp)) {
                ocrRespDto = new DriverLicenseOcrRespDto();
                BeanUtils.copyProperties(commonOcrResp, ocrRespDto);
                ocrRespDto.setDriverCode(commonOcrResp.getLicenseNo());
                ocrRespDto.setFirstObtainTime(ComUtil.getFormatDate(commonOcrResp.getIssueDate(),
                        ComUtil.DATE_TYPE2, ComUtil.DATE_TYPE5));
                if (StringUtils.equals(BussinessConstants.MAX_CERT_EXPIRE_TIME_DESC, commonOcrResp.getEndDate())) {
                    ocrRespDto.setExpireType(2);
                    ocrRespDto.setExpirationDate(StringUtils.EMPTY);
                } else {
                    ocrRespDto.setExpireType(1);
                    String expirationDate = ComUtil.getFormatDate(commonOcrResp.getEndDate(),
                            ComUtil.DATE_TYPE2, ComUtil.DATE_TYPE5);
                    if (StringUtils.isNotBlank(expirationDate)) {
                        ocrRespDto.setExpirationDate(expirationDate);
                    }
                }
                CommonRiskTypeEnums riskTypeEnum = commonOcrResp.getRiskType();
                ocrRespDto.setState(riskTypeEnum.getState());
                ocrRespDto.setRiskTypeDesc(riskTypeEnum.getDesc());
                ocrRespDto.setRiskType(riskTypeEnum.getType());
                boolean isValidPic = CommonRiskTypeEnums.isValid(ocrRespDto.getRiskType());
                if (!isValidPic) {
                    //ocrRespDto.setCode(-1);
                    ocrRespDto.setMessage("暂不支持" + ocrRespDto.getRiskTypeDesc());
                    licenceOcrTrack(false,"暂不支持" + ocrRespDto.getRiskTypeDesc(),input,commonOcrResp);
                }else{
                    licenceOcrTrack(true,"成功",input,commonOcrResp);
                }
                return ocrRespDto;
            }
        } catch (Exception ex) {
            log.error("驾照OCR提取异常，mid=" + input.getMid(), ex);
            HidLog.membership(LogPoint.MEMBER_DRIVER_LICENSE_OCR, "驾照OCR异常", input.getMid() + ex.getMessage(), false);
        }
        ocrRespDto.setState(2);
        ocrRespDto.setCodeMessage(-1, "OCR信息提取失败");
        licenceOcrTrack(false,"OCR信息提取失败",input,new CommonDrivingLicenseOcrResp());
        /**
         * 写入驾照ocr渠道
         */
        HidLog.membership(LogPoint.MEMBER_ID_CARD_OCR, "驾照OCR-完成", input.getMid(), false);
        return ocrRespDto;
    }

    /**
     *驾照ocr 的埋点
     *
     */
    public void licenceOcrTrack(boolean success, String reason, DriverLicenseOcrInput input, CommonDrivingLicenseOcrResp commonOcrResp) {
        try {
            log.info("驾照ocr埋点时，入参success[{}],reason[{}],input[{}],commonOcrResp[{}]", success, reason, JSON.toJSON(input), JSON.toJSON(commonOcrResp));
            if (input == null || commonOcrResp == null) {
                log.info("驾照ocr埋点时，入参为空");
                return;
            }

            MembershipBasicInfo base = membershipInfoMapper.getUserBasicInfoByMid(input.getMid());
            if (base == null) {
                log.info("驾照ocr埋点时，未查询到用户mid[{}]", input.getMid());
                return;
            }

            Map<String, Object> map = new HashMap<>();
            String eventName = "";
            if (input.getImageType() == 1) {
                if (success) {
                    map.put("driver_name", commonOcrResp.getName());
                    map.put("type", commonOcrResp.getDriveType());
                    map.put("license_no", commonOcrResp.getLicenseNo());
                    String certification_date = "";
                    try {
                        String issueDate = commonOcrResp.getIssueDate();
                        if (StringUtils.isNotEmpty(issueDate)) {
                            DateFormat dateFormat1 = new SimpleDateFormat("yyyy-MM-dd");
                            DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
                            certification_date = dateFormat1.format(dateFormat.parse(issueDate));
                        }
                    } catch (Exception e) {
                        log.error("驾照ocr埋点时，certification_date时间转换异常：", e);
                    }
                    map.put("certification_date", certification_date);

                    if (input.getDriverLicenseType() == 1) {
                        map.put("desc", "电子驾照");
                    }else{
                        map.put("desc", "纸质驾照");
                    }

                    String expired_date = "";
                    try {
                        String endDate = commonOcrResp.getEndDate();
                        if (StringUtils.isNotEmpty(endDate)) {
                            DateFormat dateFormat1 = new SimpleDateFormat("yyyy-MM-dd");
                            DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
                            expired_date = dateFormat1.format(dateFormat.parse(endDate));
                        }
                    } catch (Exception e) {
                        log.error("驾照ocr埋点时，expired_date时间转换异常：", e);
                    }
                    map.put("expired_date", expired_date);

                }
                eventName = "recog_result_first";
            } else if (input.getImageType() == 2) {
                if (success) {
                    map.put("file_no", commonOcrResp.getFileNo());
                }
                eventName = "recog_result_second";
            } else {
                log.info("驾照ocr埋点时，入参ImageType异常[{}]", input.getImageType());
                return;
            }


            map.put("is_success", success);
            map.put("reason", reason);
            map.put("supplier", StringUtils.isNotEmpty(commonOcrResp.getChannel()) ? commonOcrResp.getChannel() : "");


            if ("1".equals(input.getOperationModel())) {
                map.put("operation_model", "门店");
            } else if ("0".equals(input.getOperationModel())) {
                map.put("operation_model", "大库");
            }
            sensorsdataService.track(base.getAuthId(), true, eventName, map);
        } catch (Exception e) {
            log.error("驾照ocr埋点时，异常[{}]", e);
        }
    }


    /**
     *身份证ocr 的埋点
     *
     */
    public void idCardOcrTrack(boolean success, String reason, IdCardOcrInput input, CommonIdCardOcrResp commonOcrResp) {
        try {
            log.info("身份证ocr埋点时，入参success[{}],reason[{}],input[{}],commonOcrResp[{}]", success, reason, JSON.toJSON(input), JSON.toJSON(commonOcrResp));
            if (input == null || commonOcrResp == null) {
                log.info("身份证ocr埋点时，入参为空");
                return;
            }

            MembershipBasicInfo base = membershipInfoMapper.getUserBasicInfoByMid(input.getMid());
            if (base == null) {
                log.info("身份证ocr埋点时，未查询到用户mid[{}]", input.getMid());
                return;
            }

            Map<String, Object> map = new HashMap<>();
            String eventName = "";
            if (input.getImageType() == 1) {
                if (success) {
                    map.put("driver_name", commonOcrResp.getName());
                    map.put("license_no", commonOcrResp.getIdCardNo());
                }
                eventName = "idcard_recog_result";
            } else if (input.getImageType() == 2) {
                if (success) {
                    String expired_date = "";
                    try {
                        String endDate = commonOcrResp.getExpirationDate();
                        if (StringUtils.isNotEmpty(endDate)) {
                            DateFormat dateFormat1 = new SimpleDateFormat("yyyy-MM-dd");
                            DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
                            expired_date = dateFormat1.format(dateFormat.parse(endDate));
                        }
                    } catch (Exception e) {
                        log.error("身份证ocr埋点时，expired_date时间转换异常：", e);
                    }
                    map.put("expired_date", expired_date);
                }
                eventName = "idcard_recog_result_second";
            } else {
                log.info("身份证ocr埋点时，入参ImageType异常[{}]", input.getImageType());
                return;
            }
            map.put("type", input.getIdentityType() == 2 ?"军人身份证":"居民身份证");
            map.put("is_success", success);
            map.put("reason", reason);
            map.put("supplier", StringUtils.isNotEmpty(commonOcrResp.getChannel()) ? commonOcrResp.getChannel() : "");

            if ("1".equals(input.getOperationModel())) {
                map.put("operation_model", "门店");
            } else if ("0".equals(input.getOperationModel())) {
                map.put("operation_model", "大库");
            }
            sensorsdataService.track(base.getAuthId(), true, eventName, map);
        } catch (Exception e) {
            log.error("身份证ocr埋点时，异常[{}]", e);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public SubmitDriverLicenseRespDto submitDriverLicense(SubmitDriverLicenseInput input) throws AuthenticationException {
        log.info("提交驾照：input={}.", JSON.toJSONString(input));
        return licenseCertService.submitDriverLicense(input);
    }

    /**
     * 驾驶证审核
     *
     * @param updateAuthIdItemsDTO
     * @return
     */
    @Override
    public BaseResponse updateDrivingLicenceReviewStatus(UpdateLicenceReviewDto updateAuthIdItemsDTO) {
        BaseResponse vo = new BaseResponse();
        String mid = updateAuthIdItemsDTO.getMid();
        if (StringUtils.isBlank(mid)) {
            vo.setCode(-1);
            vo.setMessage("会员ID不能为空");
            return vo;
        }
        //获取会员状态
        MembershipBasicInfo member = membershipInfoMapper.getUserBasicInfoByMid(mid);
        if (StringUtils.isBlank(member.getDrivingLicenseImgUrl())) {
            vo.setCode(-1);
            vo.setMessage("审核失败，请检查用户的驾照照片");
            return vo;
        }
        /*if (StringUtils.isBlank(member.getFileNoImgUrl()) && member.getLicenseImgType() == 2) {
            vo.setCode(-1);
            vo.setMessage("审核失败，请检查用户的驾副页照片");
            return vo;
        }*/

        String createUser = updateAuthIdItemsDTO.getOperatorUserName();
        String operatorId = updateAuthIdItemsDTO.getOperatorId();
        int auditStatus = updateAuthIdItemsDTO.getAuditStatus();
        String failReason = updateAuthIdItemsDTO.getFailReason();
        String reviewItems = updateAuthIdItemsDTO.getReviewItems();
        Map<String, Object> empMap = new HashMap<String, Object>();
        empMap.put("mid", mid);
        empMap.put("operatorUserName", createUser);
        empMap.put("reviewTime", ComUtil.getSystemDate(ComUtil.DATE_TYPE4));

        if (auditStatus == 1) {
            empMap.put("auditStatus", 3);
            empMap.put("authStatus", 1);
            // 此字段表示首次驾照审核通过时间，必须在原值为空的情况下，才能更新
            if (member.getLicenseFirstAuthTime() == null) {
                empMap.put("firstAuthTime", updateAuthIdItemsDTO.getOperationTime());
            }
        } else if (auditStatus == 2) {
            empMap.put("auditStatus", 4);
            empMap.put("authStatus", 2);
        } else if (auditStatus == 3) {
            empMap.put("auditStatus", 2);
            empMap.put("authStatus", 0);
        }
        if (org.apache.commons.lang.StringUtils.isNotBlank(updateAuthIdItemsDTO.getReviewItems())) {
            empMap.put("reviewItems", reviewItems);
        } else {
            empMap.put("reviewItems", "0000000");
        }
        if (org.apache.commons.lang.StringUtils.isNotBlank(updateAuthIdItemsDTO.getFailReason())) {
            empMap.put("failReason", failReason);
        } else {
            empMap.put("failReason", "");
        }
        empMap.put("updatedTime", ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
        empMap.put("operatorUserName", createUser);
        // 更新用户状态
        membershipInfoMapper.updateDrivingLicenceReviewStatus(empMap);

        //用户 制卡
        if (membershipInfoMapper.checkHavecard(member.getAuthId()) == 1) {// 判断用户是否是新用户
            try {
                memberShipService.setVirtualCard(member.getAuthId(),member.getMembershipType());
            } catch (AuthenticationException e) {
                log.error("身份证审核通过时，用户制卡异常<{}>", e);
                vo.setCode(e.getCode());
                vo.setMessage(e.getMessage());
                return vo;
            }
        } else {
            // 添加申请进度
            ApplyProgress applyProgress = new ApplyProgress();
            applyProgress.setAuthId(member.getAuthId());
            applyProgress.setProgressContent("审核通过");
            applyProgress.setCreatedUser(updateAuthIdItemsDTO.getOperatorUserName());
            applyProgress.setProgressTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE1));
            applyProgressMapper.addApplyProgress(applyProgress);
        }
        String authId = member.getAuthId();
        String mobilePhone = member.getMobilePhone();
        // 添加操作日志
        String operatorContent = "驾驶证审核";
        if (auditStatus == 1) {
            operatorContent = "驾驶证人工审核通过";
        } else if (auditStatus == 2) {
            operatorContent = "驾驶证人工审核不通过";
        } else if (auditStatus == 3) {
            operatorContent = "驾驶证人工重新审核";
        }
        String foreignKey = authId;
        ComUtil.insertOperatorLog(operatorContent, foreignKey, operatorId, createUser,
                userOperatorLogMapper);

        //1操作日志记录
        UserOperationLogInput operationLogInput = new UserOperationLogInput();
        operationLogInput.setUserId(member.getPkId());
        operationLogInput.setAuthId(member.getAuthId());
        operationLogInput.setMembershipType(0);
        //operationLogInput.setOperationType(MemOperateTypeEnum.IDCARD_AUDIT.getCode());
        // 用RefKey1 存 来源
        if (updateAuthIdItemsDTO.getOperateSourceType() == 0) {
            operationLogInput.setRefKey1(BussinessConstants.APP_KEY_EVCARD_MMP);
        } else if (updateAuthIdItemsDTO.getOperateSourceType() == 1) {
            operationLogInput.setRefKey1("mdzcrz");
        }

        if (auditStatus == 1) {
            operationLogInput.setOperationType(MemOperateTypeEnum.LICENSE_MANUAL_AUDIT_PASS.getCode());
            operatorContent = "驾驶证人工审核通过";
        } else if (auditStatus == 2) {
            operationLogInput.setOperationType(MemOperateTypeEnum.LICENSE_MANUAL_AUDIT_NOT_PASS.getCode());
            if (org.apache.commons.lang.StringUtils.isNotBlank(updateAuthIdItemsDTO.getFailReason())) {
                operatorContent = "驾驶证人工审核不通过原因:" + updateAuthIdItemsDTO.getFailReason();
            } else {
                operatorContent = "驾驶证人工审核不通过";
            }
        } else if (auditStatus == 3) {
            operationLogInput.setOperationType(MemOperateTypeEnum.LICENSE_MANUAL_REAUDIT.getCode());
            operatorContent = "驾驶证人工重新审核";
        }
        try {
            //埋点
            if (auditStatus == 1) {
                identityCertService.memberAuditTrack(true,member.getAuthId(),2,operatorContent,"","");
            }else if (auditStatus == 2) {
                identityCertService.memberAuditTrack(false,member.getAuthId(),2,operatorContent,"","");
            }
        } catch (Exception e) {
            log.error("驾照人工审核埋点,auditStatus[{}],mid[{}],异常[{}]",auditStatus,mid,e);
        }


        operationLogInput.setOperationContent(operatorContent);
        operationLogInput.setOperatorId(Long.valueOf(updateAuthIdItemsDTO.getOperatorId()));
        operationLogInput.setOperator(updateAuthIdItemsDTO.getOperatorUserName());
        operationLogInput.setOperationTime(updateAuthIdItemsDTO.getOperationTime());
        memberShipService.saveUserOperationLog(operationLogInput);

        // 保持之前的  旧tag的mq MEMBER_AUDIT
        MemberAudit audit = new MemberAudit();
        audit.setAuthId(member.getAuthId());
        audit.setMobilePhone(member.getMobilePhone());
        audit.setOptUser(updateAuthIdItemsDTO.getOperatorUserName());
        audit.setReviewStatus(String.valueOf(auditStatus));
        if (auditStatus == 2) {
            audit.setReviewRemark(updateAuthIdItemsDTO.getFailReason());
        }
        byte[] messageBody = ProtobufUtil.serializeProtobuf(audit);
        Message msg = new Message(evcardRawDataTopic, EventEnum.MEMBER_AUDIT_DRIVING_LICENCE.getTag(), messageBody);
        producer.sendAsync(msg, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                log.info(EventEnum.MEMBER_AUDIT_DRIVING_LICENCE.getTag() + "发送mq成功");
            }

            @Override
            public void onException(OnExceptionContext onExceptionContext) {
                log.error(EventEnum.MEMBER_AUDIT_DRIVING_LICENCE.getTag() + "发送mq失败，失败原因<{}>", onExceptionContext);
            }
        });
        //发送短信消息
        String optUser = updateAuthIdItemsDTO.getOperatorUserName();
        //向审核通过不通过的会员发送审核短信, 新老用户均发
        String appKey = membershipInfoMapper.queryAppKey(authId);
        if (!ComUtil.checkIsSgmAppKey(appKey)) {
            MembershipBasicInfo membership = memberShipService.getUserBasicInfo(authId, (short) 0);
            //1. 短信发送
            Map<String, String> param = new HashMap<>(1);
            if (auditStatus == 1) {
                String title = "会员审核-驾驶证人工审核通过";
                // 请求不是来自履约小程序，才发短信
                if (updateAuthIdItemsDTO.getOperateSourceType() != 1) {
                    notifyReviewResultBySms(authId, mobilePhone, String.valueOf(236), null, title, optUser);
                }
                messagepushServ.push(authId, 0, 236, 2, 1, null, "membershipPush");
            } else if (auditStatus == 2) {
                String title = "会员审核-驾驶证人工审核不通过";
                param.put("reviewRemark", membership.getReviewRemark());
                // 请求不是来自履约小程序，才发短信
                if (updateAuthIdItemsDTO.getOperateSourceType() != 1) {
                    notifyReviewResultBySms(authId, mobilePhone, String.valueOf(237), param, title, optUser);
                }
                messagepushServ.push(authId, 0, 237, 2, 1, param, "membershipPush");
            }

        }
        vo.setCode(0);
        vo.setMessage("提交成功");
        return vo;
    }

    public void notifyReviewResultBySms(String authId, String mobilePhone, String smsId,
                                        Map<String, String> param, String title, String optUser) {
        if (StringUtils.isBlank(mobilePhone)) {
            return;
        }
        try {
            if (StringUtils.isNotBlank(smsId) && StringUtils.equals(smsMode, "1")) {
                int templateId = Integer.valueOf(smsId).intValue();
                com.extracme.evcard.rpc.dto.BaseResponse baseResponse = messagepushServ.asyncSendSMSTemplate(mobilePhone,
                        templateId, param, optUser);
                if (baseResponse.getCode() == 0) {
                    log.info(title + "-短信成功通知，authId={}", authId);
                } else {
                    log.error(title + "-短信通知失败，authId={}, 原因={}", authId, baseResponse.getMessage());
                }
            }
        } catch (Exception ex) {
            log.error(title + "-短信通知失败，authId=" + authId, ex);
        }
    }

    @Override
    public DefaultServiceRespDTO passAuditIdentity(AuditIdCardInput input) {
        Pair<Integer, String> auth = idCardAuthPassService.auth(input);
        if (auth.getKey() == 0) {
            return DefaultServiceRespDTO.SUCCESS;
        } else {
            return new DefaultServiceRespDTO(auth.getKey(), auth.getValue());
        }
    }

    @Override
    public DefaultServiceRespDTO notPassAuditIdentity(AuditIdCardInput input) {
        Pair<Integer, String> auth = idCardAuthNotPassService.auth(input);
        if (auth.getKey() == 0) {
            return DefaultServiceRespDTO.SUCCESS;
        } else {
            return new DefaultServiceRespDTO(auth.getKey(), auth.getValue());
        }
    }

    @Override
    public DefaultServiceRespDTO reAuditIdentity(AuditIdCardInput input) {
        Pair<Integer, String> auth = idCardReAuthService.auth(input);
        if (auth.getKey() == 0) {
            return DefaultServiceRespDTO.SUCCESS;
        } else {
            return new DefaultServiceRespDTO(auth.getKey(), auth.getValue());
        }
    }

    @Override
    public DefaultServiceRespDTO reAuditMember(MemberReAuditInput input) {
        int type = input.getType();
        String mid = input.getMid();
        if (StringUtils.isBlank(mid) || (type != 1 && type != 2)) {
            return new DefaultServiceRespDTO(-1, "入参校验失败");
        }

        try {
            if (type == 1) {
                //身份重新认证
                return identityCertService.reAuditMember(input,true);
            } else if (type == 2) {
                //驾照重新认证
                return licenseCertService.reAuditMember(input,true);
            }
        } catch (Exception e) {
            return new DefaultServiceRespDTO(-1, "业务处理异常");
        }
        return new DefaultServiceRespDTO(-1, "系统异常");
    }


    @Override
    public PageBeanDto<MemberAuthLogDTO> queryAuditLogs(int type, Integer pageSize, Integer pageNumber, String mid) {
        PageBeanDto<MemberAuthLogDTO> pageBeanDto = new PageBeanDto<>();
        try {
            if (StringUtils.isBlank(mid) || pageSize == null || pageNumber == null) {
                pageBeanDto.setCode(StatusCode.PARAM_EMPTY.getCode());
                pageBeanDto.setMessage(StatusCode.PARAM_EMPTY.getMsg());
                return pageBeanDto;
            }

            MembershipBasicInfo base = membershipInfoMapper.getUserBasicInfoByMid(mid);
            List<Long> operationTypes;
            if (type == 0) {
                operationTypes = BussinessConstants.IDCARD_TYPES;
            } else if (type == 1) {
                operationTypes = BussinessConstants.LICENCE_TYPES;
            } else {
                pageBeanDto.setCode(StatusCode.ILLEGAL_PARAM.getCode());
                pageBeanDto.setMessage(StatusCode.ILLEGAL_PARAM.getMsg());
                return pageBeanDto;
            }

            PageHelper.startPage(pageNumber, pageSize, true);
            List<MmpUserOperationLog> logs = mmpUserOperationLogMapper.selectOperationLogsByType(base.getPkId(), operationTypes);
            PageInfo<MmpUserOperationLog> pageInfo = new PageInfo<>(logs);
            List<MemberAuthLogDTO> result = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(logs)) {
                logs.stream().forEach(l -> {
                    try {
                        MemberAuthLogDTO memberAuthLogDTO = new MemberAuthLogDTO();
                        memberAuthLogDTO.setLogType(type);
                        Long operationType = l.getOperationType();
                        memberAuthLogDTO.setOperateType(MemOperateTypeEnum.getOperateByCode(operationType));
                        memberAuthLogDTO.setSupplier(l.getRefKey1());
                        memberAuthLogDTO.setDetails(l.getOperationContent());
                        memberAuthLogDTO.setOperateTime(ComUtil.getFormatDate(l.getUpdateTime(),ComUtil.DATE_TYPE1));
                        memberAuthLogDTO.setOperatorName(l.getUpdateOperName());
                        memberAuthLogDTO.setOperatorId(l.getUpdateOperId());
                        result.add(memberAuthLogDTO);
                    } catch (Exception e) {

                    }
                });
            }

            Page page = new Page(pageInfo.getPageNum(), pageInfo.getPageSize());
            page.setCount((int) pageInfo.getTotal());
            pageBeanDto.setPage(page);
            pageBeanDto.setList(result);
            pageBeanDto.setCode(0);
        } catch (Exception e) {
            log.error("查询认证相关日志异常,mid<{}>,异常<{}>", mid, e);
            pageBeanDto.setCode(StatusCode.SYSTEM_ERROR.getCode());
            pageBeanDto.setMessage(StatusCode.SYSTEM_ERROR.getMsg());
        }
        return pageBeanDto;
    }

    @Override
    public JudgeIfCanTradeResp judgeIfCanTrade(String authId, Short membershipType,String cityName) {
        log.info("判断是否能支持交易，入参：authId[{}]membershipType[{}]cityName[{}]", authId, membershipType, cityName);
        JudgeIfCanTradeResp resp = new JudgeIfCanTradeResp();
        try {
            if (StringUtils.isBlank(authId) || membershipType == null) {
                resp.setCode(StatusCode.PARAM_EMPTY.getCode());
                resp.setMessage(StatusCode.PARAM_EMPTY.getMsg());
                return resp;
            }

            // 查询会员信息
            MembershipBasicInfo memberInfo = memberShipService.getUserBasicInfo(authId, membershipType);
            log.info("判断是否能支持交易，查询会员信息：authId[{}]membershipType[{}]memberInfo[{}]", authId, membershipType, JSON.toJSONString(memberInfo));
            if (memberInfo == null) {
                resp.setCode(StatusCode.MEMBER_NOT_EXIT.getCode());
                resp.setMessage(StatusCode.MEMBER_NOT_EXIT.getMsg());
                return resp;
            }

            // 查询会员是什么模式
            boolean storeMode = getStoreMode(cityName);

            int licenseReviewStatus = memberInfo.getLicenseReviewStatus(); // 新的驾驶证审核状态(1:未认证 2:待认证 3:已认证 4:认证不通过)
            int authenticationStatusNew = memberInfo.getAuthenticationStatusNew() == null ? 0 : memberInfo.getAuthenticationStatusNew(); // 新的身份认证状态 1未认证(未上传) 2未刷脸 3待认证(待人工认证) 4已认证 5认证不通过
            int expireType = memberInfo.getExpireType() == null ? 0 : memberInfo.getExpireType(); // 证件时间类型 1：非长期 2：长期
            String expirationDateStr = memberInfo.getExpirationDate(); // 证件有效期（yyyy-MM-dd)，如果expireType为2-长期，那么此字段为空
            LocalDate expirationDate = null;
            if (StringUtils.isNotBlank(expirationDateStr)) {
                expirationDate = LocalDate.parse(expirationDateStr, DateUtil.DATE_TYPE5);
            }
            String licenseExpirationTimeStr = memberInfo.getLicenseExpirationTime(); // 驾驶证有效期（yyyy-MM-dd)
            LocalDate licenseExpirationTime = null;
            // 沿用老逻辑，如果为空，认为驾驶证就是过期了
            if (StringUtils.isNotBlank(licenseExpirationTimeStr)) {
                licenseExpirationTime = LocalDate.parse(licenseExpirationTimeStr, DateUtil.DATE_TYPE5);
            }

            // 门店模式
            if (storeMode) {
                log.info("判断是否能支持交易，是门店模式：authenticationStatusNew[{}]expireType[{}]expirationDate[{}]", authenticationStatusNew, expireType, expirationDate);
                // 如果新的身份认证状态为 3或4 且身份证未过期 能交易
                if ((authenticationStatusNew == 3 || authenticationStatusNew == 4) && (expireType == 2 || (expirationDate != null && !LocalDate.now().isAfter(expirationDate)))) {
                    resp.setState(1);
                    resp.setCode(StatusCode.SYSTEM_SUCCESS.getCode());
                    resp.setMessage(StatusCode.SYSTEM_SUCCESS.getMsg());
                } else {
                    resp.setCode(StatusCode.AUTHENTICATION_ERROR_ONE.getCode());
                    resp.setMessage(StatusCode.AUTHENTICATION_ERROR_ONE.getMsg());
                }
            } else { // 大库模式
                log.info("判断是否能支持交易，是大库模式：authenticationStatusNew[{}]expireType[{}]expirationDate[{}]licenseReviewStatus[{}]appKey[{}]licenseExpirationTime[{}]",
                        authenticationStatusNew, expireType, expirationDate, licenseReviewStatus, memberInfo.getAppKey(), licenseExpirationTime);
                // 如果新的身份认证状态为 4 且身份证未过期 可继续
                if (authenticationStatusNew == 4 && (expireType == 2 || (expirationDate != null && !LocalDate.now().isAfter(expirationDate)))) {
                    // 如果新的驾驶证审核状态为 3 且驾驶证未过期（沿用老逻辑，如果origin.equals("alipay_carsharing")，就不判断驾驶证是否过期） 可继续
                    if (licenseReviewStatus == 3 && (BussinessConstants.APP_KEY_CAR_SHARING.equals(memberInfo.getAppKey()) || (licenseExpirationTime != null && !LocalDate.now().isAfter(licenseExpirationTime)))) {
                        resp.setState(1);
                        resp.setCode(StatusCode.SYSTEM_SUCCESS.getCode());
                        resp.setMessage(StatusCode.SYSTEM_SUCCESS.getMsg());
                    } else {
                        resp.setCode(StatusCode.USER_IN_AUDIT.getCode());
                        resp.setMessage(StatusCode.USER_IN_AUDIT.getMsg());
                    }
                } else {
                    resp.setCode(StatusCode.AUTHENTICATION_ERROR_ONE.getCode());
                    resp.setMessage(StatusCode.AUTHENTICATION_ERROR_ONE.getMsg());
                }
            }
            return resp;
        } catch (Exception e) {
            log.error("judgeIfCanTrade happen exception!", e);
            resp.setCode(StatusCode.SYSTEM_ERROR.getCode());
            resp.setMessage(StatusCode.SYSTEM_ERROR.getMsg());
            return resp;
        }
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse thirdSubmitFaceImgToReview(SubmitFaceImgSepInput input) throws AuthenticationException {
        HidLog.membership(LogPoint.SUBMIT_USER_FACE_INFO_NATIVE, "特别渠道-提交用户认证信息开始-" + input.getAppKey(), input.getMid(), true);
        log.info("单独提交人脸照片：input={}", JSON.toJSONString(input));
        if(StringUtils.isBlank(input.getMid())) {
            throw BusinessRuntimeException.PARAM_EXEPTION;
        }
        MembershipBasicInfo member = membershipInfoMapper.getUserBasicInfoByMid(input.getMid());
        if(member == null || member.getAccountStatus().equals(2)) {
            throw new AuthenticationException(StatusCode.MEMBER_NOT_EXIT);
        }
        if(input.isCheckFaceAuthStatus()) {
            //TODO 确认是否需要综合原刷脸状态
            boolean faceAuthed = identityCertService.getFaceRecFlag(member);
            if (faceAuthed && StringUtils.isNotBlank(input.getAppKey()) && !BussinessConstants.FACE_NO_CHECK_CHANNEL.contains(input.getAppKey())) {
                log.error("单独提交人脸照片：mid={}, appKey={}", input.getMid(), input.getAppKey());

                throw new AuthenticationException(StatusCode.SUBMIT_FAILURE);
            }
        }
        //提交审核
        BaseResponse resp = identityCertService.submitFaceImgToReview(input.getMid(), false, input.getFaceRecImg(),
                input.getIdCardNo(), input.getAppKey(), input.getRefKey2(), member);
        log.info("单独提交人脸照片：完成，input={}, resp={},", JSON.toJSONString(input), JSON.toJSONString(resp));
        HidLog.membership(LogPoint.SUBMIT_USER_FACE_INFO_NATIVE, "特别渠道-提交用户认证信息结束-" + input.getAppKey(), input.getMid(), true);
        return resp;
    }

    /**
     * 城市是否支持门店模式
     *
     * @param cityName
     * @return
     */
    private boolean getStoreMode(String cityName) {
        long cityId = 310100;
        if (StringUtils.isNotBlank(cityName)) {
            City city = cityMapper.getCityByName(cityName);
            if (city != null && city.getCityid() != null) {
                cityId = city.getCityid();
            }
        }
        log.info("查询城市配置，请求：cityId={}", cityId);
        SearchCityConfigurationResponse resp = mdStoreService.searchCityConfiguration(cityId);
        log.info("查询城市配置，应答：{}", JSON.toJSONString(resp));
        if (resp != null && resp.getData() != null && CollectionUtils.isNotEmpty(resp.getData().getCfg())) {
            return true;
        }
        return false;
    }

    /**
     *  履约提交人脸
     * @param input  基本入参
     * @return
     */
    @Override
    public BaseResponse ofcSubmitFacePic(OfcSubmitFacePicInput input){
        log.info("履约提交人脸识别图片，参数input=[{}] ",OfcSubmitFacePicInput.getToString(input));
        BaseResponse result = new BaseResponse(0,"success");
        if (input == null || StringUtils.isBlank(input.getImageSrc()) || StringUtils.isBlank(input.getMid())
                || (input.getImageType() != 1 && input.getImageType() != 0) || StringUtils.isBlank(input.getAppKey())
                || StringUtils.isBlank(input.getAppType())) {
            log.warn("履约提交人脸识别图片，入参格式检验不通过，参数input=[{}]", JSON.toJSONString(input));
            result.setCode(StatusCode.ILLEGAL_PARAM.getCode());
            result.setMessage(StatusCode.ILLEGAL_PARAM.getMsg());
            return result;
        }

        try {
            MembershipBasicInfo membershipInfo = memberShipService.getUserBasicInfo(input.getMid());
            if (membershipInfo == null) {
                throw new AuthenticationException(StatusCode.MEMBER_NOT_EXIT);
            }

            Integer authenticationStatusNew = membershipInfo.getAuthenticationStatusNew();
            if (!IdentityAuthStatusEnum.TO_FACE_REC.eq(authenticationStatusNew)) {
                log.error("履约更新人脸照片时，当前身份认证状态非未刷脸, 参数input=[{}] ",OfcSubmitFacePicInput.getToString(input));
                throw new AuthenticationException(StatusCode.SUBMIT_FAILURE);
            }

            String authId = membershipInfo.getAuthId();
            String targetPath = StringUtils.EMPTY;
            if (input.getImageType() == 0) {
                // 处理图片base格式
                String imageSrc = input.getImageSrc();
                if (imageSrc.startsWith(BussinessConstants.PREFIX_BASE64)) {
                    String newImageSrc = imageSrc.replace(BussinessConstants.PREFIX_BASE64, "");
                    input.setImageSrc(newImageSrc);
                }

                byte[] bytes = ImgUtils.decode(input.getImageSrc());
                UploadMemberImageDto imageDto = memberShipService.uploadMemberImage(4, bytes, authId);
                targetPath = imageDto.getDisplayUrl();
               /* // base64太长了，这里上传后改用 url的方式
                input.setImageSrc(targetPath);
                input.setImageType(1);*/
            } else {
                targetPath = input.getImageSrc();
            }

            // 强制成功时，不用去调用百度接口比较；
            if(input.isForceSuccess()){
                ofcSubmitFaceRecognitionPic(membershipInfo, null, 0, targetPath, input.getAppKey(),1);
            }else{
                ofcSubmitFaceRecognitionPic(membershipInfo, input.getImageSrc(), input.getImageType(), targetPath, input.getAppKey(),0);
            }
            log.info("履约提交人脸识别图片，提交成功，参数input=[{}] ", OfcSubmitFacePicInput.getToString(input));
        } catch (AuthenticationException e) {
            log.error("履约提交人脸识别图片，异常，参数input=[{}]", OfcSubmitFacePicInput.getToString(input),e);
            result.setCode(e.getCode());
            result.setMessage(e.getMessage());
        } catch (Exception e) {
            log.error("履约提交人脸识别图片，失败，参数input=[{}]", OfcSubmitFacePicInput.getToString(input),e);
            result.setCode(-1);
            result.setMessage("履约提交人脸失败");
        }
        return result;
    }

    /**
     * forceSuccess true 不调用百度人脸比较，提交人脸信息
     * forceSuccess false 调用百度人脸比较，提交人脸信息
     *
     * @param membershipInfo
     * @param imageSrc
     * @param imageType
     * @param faceRecognitionPic
     * @param appkey
     * @param forceSuccess
     * @throws AuthenticationException
     */
    public void ofcSubmitFaceRecognitionPic(MembershipBasicInfo membershipInfo, String imageSrc, int imageType, String faceRecognitionPic, String appkey,int forceSuccess) throws AuthenticationException{
        try {
            String authId = membershipInfo.getAuthId();
            String faceAuthenticationResult = StringUtils.EMPTY;
            String faceAuthenticationSimilarity = StringUtils.EMPTY;

            String idCardNum = StringUtils.isNotBlank(membershipInfo.getIdCardNumber()) ? membershipInfo.getIdCardNumber() : membershipInfo.getDriverCode();

            if (forceSuccess == 0) {
                // imageSrc 为url时，需要完整路径
                Map<String, String> faceResultMap = memberShipService.baiDuFaceAuthentication2(membershipInfo.getName(), idCardNum, imageSrc, imageType, authId, appkey);
                faceAuthenticationResult = faceResultMap.get("faceAuthenticationResult");
                faceAuthenticationSimilarity = faceResultMap.get("faceAuthenticationSimilarity");
            }

            SubmitFaceInfoInput faceInfoInput = new SubmitFaceInfoInput();
            faceInfoInput.setAuthId(authId);
            faceInfoInput.setFaceRecognitionPic(faceRecognitionPic);
            faceInfoInput.setAppKey(appkey);
            faceInfoInput.setRefKey2("baidu");
            faceInfoInput.setFaceAuthenticationResult(faceAuthenticationResult);
            faceInfoInput.setFaceAuthenticationSimilarity(faceAuthenticationSimilarity);
            faceInfoInput.setOrigin(1);
            faceInfoInput.setForceSuccess(forceSuccess);
            faceInfoInput.setMembershipType(membershipInfo.getMembershipType());
            memberShipService.ofcSubmitOnlyFaceRecognitionPic(faceInfoInput);
        } catch (AuthenticationException e) {
            log.error("履约提交人脸认证图片失败 参数membershipInfo=[{}]", JSON.toJSONString(membershipInfo), e);
            throw e;
        }
    }


    /**
     * 判断是否过期
     * @param expireType
     * @param expireDate
     * @return true 过期 false 未过期
     */
    public boolean isExpired(int expireType,String expireDate){
        try {
            //长期
            if (expireType == 2) {
                return false;
            }else{
                LocalDate now = LocalDate.now();
                DateTimeFormatter ymd = DateTimeFormatter.ofPattern(ComUtil.DATE_TYPE5);
                if (StringUtils.isNotBlank(expireDate)){
                    LocalDate expirationDate = LocalDate.parse(expireDate, ymd);
                    if (now.isAfter(expirationDate)){
                        // 过期
                        return true;
                    }
                }
            }
        } catch (Exception e) {
        }
        return false;
    }

    @Override
    public OcrAndSubmitIdentityRespDto ocrAndSubmitIdentity(OcrAndSubmitIdentityInput input) {
        log.info("Starting ocrAndSubmitIdentity,input={}", JSON.toJSONString(input));

        if (StringUtils.isBlank(input.getMid()) || StringUtils.isBlank(input.getIdentityCardImgUrl())|| StringUtils.isBlank(input.getReverseIdentityCardImgUrl()) || StringUtils.isBlank(input.getIdentityNo())) {
            return new OcrAndSubmitIdentityRespDto(-1, "参数异常");
        }

        MembershipBasicInfo member = memberShipService.getUserBasicInfo(input.getMid());
        if (member == null) {
            return new OcrAndSubmitIdentityRespDto(-1, "未查询到用户");
        }


        if (IdentityAuthStatusEnum.AUTHENTICATED.getValue().equals(member.getAuthenticationStatusNew()) && !isExpired(member.getExpireType(), member.getExpirationDate())) {
            OcrAndSubmitIdentityRespDto respDto = new OcrAndSubmitIdentityRespDto();
            respDto.setCode(0);
            respDto.setState(1);
            respDto.setMessage("已认证");
            return respDto;
        }

        try {
            log.info("Starting OCR and identity submission,input={}",JSON.toJSONString(input));

            IdCardOcrInput ocrInput = new IdCardOcrInput();
            ocrInput.setMid(input.getMid());
            ocrInput.setIdentityType(input.getIdentityType());
            ocrInput.setOperationModel(input.getOperationModel());
            ocrInput.setUrl(input.getIdentityCardImgUrl());
            ocrInput.setImageType(1);

            IdCardOcrInput ocrReverseInput = new IdCardOcrInput();
            BeanUtils.copyProperties(ocrInput, ocrReverseInput);
            ocrReverseInput.setImageType(2);
            ocrReverseInput.setUrl(input.getReverseIdentityCardImgUrl());
            // 1:证件正面 ocr
            CompletableFuture<IdCardOcrRespDto> future1 = CompletableFuture.supplyAsync(() -> idCardOcr(ocrInput), ThreadPoolUtils.EXECUTOR_OCR);
            // 2:证件反面 ocr
            CompletableFuture<IdCardOcrRespDto> future2 = CompletableFuture.supplyAsync(() -> idCardOcr(ocrReverseInput), ThreadPoolUtils.EXECUTOR_OCR);

            CompletableFuture<OcrAndSubmitIdentityRespDto> resultFuture = future1.thenCombine(future2, (ocrRespDto1, ocrRespDto2) -> {
                return getOcrAndSubmitIdentityRespDto(input, member, ocrRespDto1, ocrRespDto2);
            });

            OcrAndSubmitIdentityRespDto respDto = resultFuture.get();
             return respDto;
        } catch (Exception e) {
            log.error("ocrAndSubmitIdentity 异常 ,input={}",JSON.toJSONString(input) ,e);
            return new OcrAndSubmitIdentityRespDto(-1,StatusCode.SYSTEM_ERROR.getMsg());
        }
    }

    /**
     * 封装响应结果
     *
     * @param input
     * @param member
     * @param ocrRespDto1 ocr证件正面结果
     * @param ocrRespDto2 ocr证件反面结果
     * @return
     */
    private OcrAndSubmitIdentityRespDto getOcrAndSubmitIdentityRespDto(OcrAndSubmitIdentityInput input, MembershipBasicInfo member, IdCardOcrRespDto ocrRespDto1, IdCardOcrRespDto ocrRespDto2) {
        OcrAndSubmitIdentityRespDto respDto = new OcrAndSubmitIdentityRespDto();
        // 默认值
        SubmitIdCardInput submitIdCardInput = new SubmitIdCardInput();
        submitIdCardInput.setMid(input.getMid());
        submitIdCardInput.setOperationModel(input.getOperationModel());
        submitIdCardInput.setCertInputType(1);
        //submitIdCardInput.setIdcardPicUrl(input.getIdentityCardImgUrl());
        //submitIdCardInput.setIdcardPicBackUrl(input.getReverseIdentityCardImgUrl());
        submitIdCardInput.setIdcardPicUrl(ComUtil.splitPicUrl(input.getIdentityCardImgUrl()));
        submitIdCardInput.setIdcardPicBackUrl(ComUtil.splitPicUrl(input.getReverseIdentityCardImgUrl()));

        submitIdCardInput.setIdType(input.getIdentityType());

        // ocr 正面失败，给默认值
        if (ocrRespDto1.getCode() == 0 && ocrRespDto1.getState() == 1 && StringUtils.isNotBlank(ocrRespDto1.getIdCardNo())) {
            BeanUtils.copyProperties(ocrRespDto1, respDto);
            submitIdCardInput.setName(ocrRespDto1.getName());
            submitIdCardInput.setIdCardNumber(ocrRespDto1.getIdCardNo());
        } else {
            submitIdCardInput.setName(member.getName());
            submitIdCardInput.setIdCardNumber(input.getIdentityNo());
        }

        // ocr反面 失败，给默认值
        if (ocrRespDto2.getCode() == 0 && ocrRespDto2.getState() == 1 && StringUtils.isNotBlank(ocrRespDto2.getExpirationDate())) {
            submitIdCardInput.setExpireType(ocrRespDto2.getExpireType());
            submitIdCardInput.setExpirationDate(ocrRespDto2.getExpirationDate());
        } else {
            submitIdCardInput.setExpireType(2);
        }

        try {
            // 3:提交身份认证
            submitIdCardInput.setAppKey(BussinessConstants.APPKEY_QL);
            respDto.setExpirationDate(submitIdCardInput.getExpirationDate());
            respDto.setExpireType(submitIdCardInput.getExpireType());
            respDto.setName(submitIdCardInput.getName());
            respDto.setIdCardNo(submitIdCardInput.getIdCardNumber());

            SubmitIdCardRespDto submitIdCardRespDto = submitUserIdCard(submitIdCardInput);
            if (submitIdCardRespDto.getCode() == 0 && submitIdCardRespDto.getState() == 1) {
                // 返回值
                respDto.setState(submitIdCardRespDto.getState());
                respDto.setMsg(submitIdCardRespDto.getMsg());
                return respDto;
            }
        } catch (Exception e) {
            log.error("ocrAndSubmitIdentity submitUserIdCard业务异常 ,submitIdCardInput={}", JSON.toJSONString(submitIdCardInput), e);
        }

        /**
         *
         * 强制认证成功
         *
         */
        try {
            log.info("ocrAndSubmitIdentity 强制认证成功开始 ,submitIdCardInput={}", JSON.toJSONString(submitIdCardInput));
            MemberIdentityDocument memberIdentity = memberIdentityDocumentMapper.selectOneByMid(input.getMid());
            SubmitIdCardRespDto submitIdCardRespDto = identityCertService.submitForReview(submitIdCardInput, memberIdentity, member, true);
            respDto.setState(submitIdCardRespDto.getState());
            respDto.setMsg(submitIdCardRespDto.getMsg());
        } catch (Exception e) {
            respDto.setCode(-1);
            respDto.setMessage(StatusCode.SYSTEM_ERROR.getMsg());
            log.error("ocrAndSubmitIdentity 强制认证成功异常 ,submitIdCardInput={}", JSON.toJSONString(submitIdCardInput));
        }
        return respDto;
    }

    @Override
    public OcrAndSubmitLicenseRespDto ocrAndSubmitLicense(OcrAndSubmitLicenseInput input) {
        log.info("Starting  ocrAndSubmitLicense： input={}",JSON.toJSONString(input));

        if (StringUtils.isBlank(input.getMid()) || StringUtils.isBlank(input.getDriverLicenseImgUrl()) || StringUtils.isBlank(input.getLicenseNo())) {
            return new OcrAndSubmitLicenseRespDto(-1, "参数异常");
        }

        MembershipBasicInfo member = memberShipService.getUserBasicInfo(input.getMid());
        if (member == null) {
            return new OcrAndSubmitLicenseRespDto(-1, "未查询到用户");
        }

        if (LicenseAuthStatusEnum.AUTHENTICATED.getValue().equals(member.getLicenseReviewStatus())&& !isExpired(1, member.getLicenseExpirationTime())) {
            OcrAndSubmitLicenseRespDto respDto = new OcrAndSubmitLicenseRespDto();
            respDto.setCode(0);
            respDto.setState(1);
            respDto.setMessage("已认证");
            return respDto;
        }

        try {
            log.info("Starting ocr and license submission,input={}",JSON.toJSONString(input));

            DriverLicenseOcrInput ocrInput = new DriverLicenseOcrInput();
            ocrInput.setMid(input.getMid());
            ocrInput.setOperationModel(input.getOperationModel());
            ocrInput.setUrl(input.getDriverLicenseImgUrl());
            ocrInput.setImageType(1);
            ocrInput.setAppKey(input.getAppKey());

            DriverLicenseOcrInput ocrReverseInput = new DriverLicenseOcrInput();
            BeanUtils.copyProperties(ocrInput, ocrReverseInput);
            ocrReverseInput.setImageType(2);
            ocrReverseInput.setUrl(input.getReverseDriverLicenseImgUrl());
            // 1:驾照证件正面 ocr
            CompletableFuture<DriverLicenseOcrRespDto> future1 = CompletableFuture.supplyAsync(() -> driverLicenseOcr(ocrInput), ThreadPoolUtils.EXECUTOR_OCR);
            // 2:驾照证件反面 ocr
            CompletableFuture<DriverLicenseOcrRespDto> future2 = CompletableFuture.supplyAsync(() -> driverLicenseOcr(ocrReverseInput), ThreadPoolUtils.EXECUTOR_OCR);

            CompletableFuture<OcrAndSubmitLicenseRespDto> resultFuture = future1.thenCombine(future2, (ocrRespDto1, ocrRespDto2) -> {
                return getOcrAndSubmitLicenseRespDto(input, member, ocrRespDto1, ocrRespDto2);
            });
            OcrAndSubmitLicenseRespDto respDto = resultFuture.get();
             return respDto;
        } catch (Exception e) {
            log.error("ocrAndSubmitLicense 异常 ,input={}",JSON.toJSONString(input) ,e);
            return new OcrAndSubmitLicenseRespDto(-1,StatusCode.SYSTEM_ERROR.getMsg());
        }
    }

    /**
     * 封装响应结果
     *
     * @param input
     * @param member
     * @param ocrRespDto1 ocr证件正面结果
     * @param ocrRespDto2 ocr证件反面结果
     * @return
     */
    private OcrAndSubmitLicenseRespDto getOcrAndSubmitLicenseRespDto(OcrAndSubmitLicenseInput input, MembershipBasicInfo member, DriverLicenseOcrRespDto ocrRespDto1, DriverLicenseOcrRespDto ocrRespDto2) {

        OcrAndSubmitLicenseRespDto respDto = new OcrAndSubmitLicenseRespDto();
        SubmitDriverLicenseInput submitDriverLicenseInput = new SubmitDriverLicenseInput();
        submitDriverLicenseInput.setMid(input.getMid());
        submitDriverLicenseInput.setAppKey(BussinessConstants.APPKEY_QL);
        submitDriverLicenseInput.setOperationModel(input.getOperationModel());
        //submitDriverLicenseInput.setDriverLicenseImgUrl(input.getDriverLicenseImgUrl());
        //submitDriverLicenseInput.setReverseDriverLicenseImgUrl(input.getReverseDriverLicenseImgUrl());
        submitDriverLicenseInput.setDriverLicenseImgUrl(ComUtil.splitPicUrl(input.getDriverLicenseImgUrl()));
        submitDriverLicenseInput.setReverseDriverLicenseImgUrl(ComUtil.splitPicUrl(input.getReverseDriverLicenseImgUrl()));

        Integer driverLicenseType = input.getDriverLicenseType();
        submitDriverLicenseInput.setDriverLicenseType((driverLicenseType == null || driverLicenseType == 0) ? 1 : driverLicenseType);

        // ocr正面成功
        if (ocrRespDto1.getCode() == 0 && ocrRespDto1.getState() == 1 && StringUtils.isNotBlank(ocrRespDto1.getDriverCode())) {
            // 这里电子驾照和纸质驾照返回不一样
            BeanUtils.copyProperties(ocrRespDto1, submitDriverLicenseInput);
            submitDriverLicenseInput.setLicenseType(ocrRespDto1.getDriveType());
        } else {
            // ocr 正面失败 默认值
            submitDriverLicenseInput.setDriverCode(input.getLicenseNo());
            submitDriverLicenseInput.setName(member.getName());
            submitDriverLicenseInput.setExpireType(2);
            submitDriverLicenseInput.setLicenseType("C1");
        }

        // ocr反面 失败不处理
        if (ocrRespDto2.getCode() == 0 && ocrRespDto2.getState() == 1) {
            if (StringUtils.isNotBlank(ocrRespDto2.getFileNo())) {
                submitDriverLicenseInput.setFileNo(ocrRespDto2.getFileNo());
            }
        }

        try {
            // 3:提交驾照认证
            // 返回值
            respDto.setDriverCode(submitDriverLicenseInput.getDriverCode());
            respDto.setDriveType(submitDriverLicenseInput.getLicenseType());
            respDto.setFileNo(submitDriverLicenseInput.getFileNo());
            respDto.setExpirationDate(submitDriverLicenseInput.getExpirationDate());
            respDto.setExpireType(submitDriverLicenseInput.getExpireType());
            respDto.setName(submitDriverLicenseInput.getName());
            respDto.setFirstObtainTime(submitDriverLicenseInput.getFirstObtainTime());

            SubmitDriverLicenseRespDto submitDriverLicenseRespDto = submitDriverLicense(submitDriverLicenseInput);
            if (submitDriverLicenseRespDto.getCode() ==0 && submitDriverLicenseRespDto.getState() ==1) {
                respDto.setState(submitDriverLicenseRespDto.getState());
                respDto.setMsg(submitDriverLicenseRespDto.getMsg());
                return respDto;
            }
        } catch (Exception e) {
            log.error("ocrAndSubmitLicense 提交驾照认证业务异常 ,submitDriverLicense={}", JSON.toJSONString(input), e);
        }

        /**
         *
         * 强制认证成功
         *
         */
        try {
            log.info("ocrAndSubmitLicense 强制认证成功开始 ,submitDriverLicenseInput={}", JSON.toJSONString(submitDriverLicenseInput));
            SubmitDriverLicenseRespDto submitDriverLicenseRespDto = licenseCertService.submitForReview(submitDriverLicenseInput, member, true);
            respDto.setState(submitDriverLicenseRespDto.getState());
            respDto.setMsg(submitDriverLicenseRespDto.getMsg());
        } catch (Exception e) {
            respDto.setCode(-1);
            respDto.setMessage(StatusCode.SYSTEM_ERROR.getMsg());
            log.error("ocrAndSubmitLicense 强制认证成功异常 ,submitDriverLicenseInput={}", JSON.toJSONString(submitDriverLicenseInput));
        }

        return respDto;
    }
}
