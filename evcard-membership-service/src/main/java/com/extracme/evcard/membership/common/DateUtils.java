package com.extracme.evcard.membership.common;

import org.apache.commons.lang3.StringUtils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;


public class DateUtils {
	public static final String DATE_TYPE1 = "yyyy-MM-dd HH:mm:ss";

	public static final String DATE_TYPE2 = "yyyy-MM-dd HH:mm:ss.SSS";

	public static final String DATE_TYPE3 = "yyyyMMddHHmmssSSS";

	public static final String DATE_TYPE4 = "yyyyMMddHHmmss";

	public static final String DATE_TYPE5 = "yyyy-MM-dd";

	public static final String DATE_TYPE6 = "yy-MM-dd-HH-mm-ss";

	public static final String DATE_TYPE7 = "yyyy-MM-dd HH:mm";

	public static final String DATE_TYPE8 = "yyyyMMdd";

	public static final String DATE_TYPE9 = "yyyy-M-d H:m:s:S";

	public static final String DATE_TYPE10 = "yyyyMMddHHmm";

	public static final String DATE_TYPE11 = "yyyy-M-d H:m:s";

	public static final String DATE_TYPE12 = "yy-MM-dd HH:mm:ss";

	public static final String DATE_TYPE13 = "yyyy/MM/dd HH:mm:ss";

	public static final String DATE_TYPE14 = "MM-dd HH:mm:ss";

	public static final String DATE_TYPE15 = "yyyy年MM月dd日 HH:mm";

	public static final String DATE_TYPE16 = "MM月dd日 HH:mm";

	public static final String DATE_TYPE17 = "yyyyMM";

	public static final String DATE_TYPE18 = "HHmm";

	public  static final String DATE_TYPE19 = "yyyy/MM/dd";

	public static final String DATE_TYPE20 = "HH";

	public static final String DATE_TYPE21 = "HH:mm";
	public static final String DATE_TYPE22 = "HHmm00";

	private static TimeZone timeZoneChina = TimeZone.getTimeZone("Asia/Shanghai");

	public static ZoneId timeZoneChinaZoneId = ZoneId.of("Asia/Shanghai");


	/**
	 * 获取当前时间
	 * @param type  指定格式
	 * @return String
	 */
	public static String getSystemDate(String type) {

		// 指定格式
		DateFormat dateFormat = new SimpleDateFormat(type);
		dateFormat.setTimeZone(timeZoneChina);

		// 范围指定格式的字符串
		return dateFormat.format(new Date());
	}
	
	
	public static String getFormatDate(String dateStr, String fromType,String toType) {
		try {
			DateFormat dateFromFmt = new SimpleDateFormat(fromType);
			dateFromFmt.setTimeZone(timeZoneChina);
			DateFormat dateToFmt = new SimpleDateFormat(toType);
			dateToFmt.setTimeZone(timeZoneChina);

			// 非空检查
			if (StringUtils.isBlank(dateStr)) {
				return StringUtils.EMPTY;
			} else {
				Date tmpDate = dateFromFmt.parse(dateStr);

				if (dateFromFmt.format(tmpDate).equals(dateStr)) {
					return dateToFmt.format(tmpDate);
				} else {
					return StringUtils.EMPTY;
				}
			}
		} catch (Exception e) {
			return StringUtils.EMPTY;
		}
	}

	public static String getFormatDate(Date date, String toType) {

		try {
			DateFormat dateToFmt = new SimpleDateFormat(toType);
			dateToFmt.setTimeZone(timeZoneChina);
			// 非空检查
			if (date == null) {
				return StringUtils.EMPTY;
			} else {
				return dateToFmt.format(date);
			}
		} catch (Exception e) {
			return StringUtils.EMPTY;
		}
	}

	public static Date getDateFromStr(String dateStr, String fromType) {

		try {
			DateFormat dateFromFmt = new SimpleDateFormat(fromType);
			dateFromFmt.setTimeZone(timeZoneChina);
			
			return dateFromFmt.parse(dateStr);
		} catch (Exception e) {
			//e.printStackTrace();
			return null;
		}
	}

	public static int getDaysOfMonth(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
	}

	/**
	 * 获取指定月的天数
	 * @param dateStr
	 * @param fromType
	 * @return
	 */
	public static int getDaysOfMonth(String dateStr, String fromType) {
		Date date = getDateFromStr(dateStr, fromType);
		if(date != null) {
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(date);
			return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
		}
		return -1;
	}

	public static LocalDate dateToLocalDate(String date, String fromType) {
		DateTimeFormatter pattern = DateTimeFormatter.ofPattern(fromType);
		return LocalDate.parse(date, pattern);
	}

	public static LocalDate dateToLocalDate(Date date, String fromType){
		DateTimeFormatter pattern = DateTimeFormatter.ofPattern(fromType);
		String dateString =  dateToString(date,fromType);
		return LocalDate.parse(dateString, pattern);
	}

	public static String dateToString(LocalDate date, String fromType) {
		DateTimeFormatter pattern = DateTimeFormatter.ofPattern(fromType);
		return date.format(pattern);
	}

	public static String dateToString(LocalDateTime date, String fromType) {
		DateTimeFormatter pattern = DateTimeFormatter.ofPattern(fromType);
		return date.format(pattern);
	}

	public static String dateToString(Date date, String fromType) {
		DateTimeFormatter pattern = DateTimeFormatter.ofPattern(fromType);
		if (date == null) {
			return "";
		} else {
			LocalDateTime ldt = date.toInstant().atZone(timeZoneChinaZoneId).toLocalDateTime();
			return ldt.format(pattern);
		}
	}
	public static Date localDateToDate(LocalDate localDate) {
		LocalDateTime localDateTime = localDate.atStartOfDay();
		return localTimeToDate(localDateTime);
	}
	public static Date localTimeToDate(LocalDateTime date) {
		return Date.from(date.atZone(timeZoneChinaZoneId).toInstant());
	}
	/**
	 * 判断字符串格式是否符合要求
	 *
	 * @param str
	 * @param format
	 * @return
	 */
	public static boolean isValidDateString(String str, String format) {
		if (StringUtils.isAnyBlank(str, format)) {
			return false;
		}
		try {
			DateFormat dateFormat = new SimpleDateFormat(format);
			dateFormat.setTimeZone(timeZoneChina);
			dateFormat.setLenient(false); // 严格解析日期
			dateFormat.parse(str);
			return true;
		} catch (Exception e) {
			return false;
		}
	}

}
