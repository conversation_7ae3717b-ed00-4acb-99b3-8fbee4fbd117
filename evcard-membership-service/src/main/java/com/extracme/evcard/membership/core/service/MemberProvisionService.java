package com.extracme.evcard.membership.core.service;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.dao.*;
import com.extracme.evcard.membership.core.dto.*;
import com.extracme.evcard.membership.core.dto.input.MemberContractQueryInput;
import com.extracme.evcard.membership.core.dto.input.ProvisionUserFeedbackInput;
import com.extracme.evcard.membership.core.dto.input.ProvisionUserTagInput;
import com.extracme.evcard.membership.core.exception.MemberException;
import com.extracme.evcard.membership.core.model.*;
import com.extracme.evcard.redis.JedisUtil;
import com.extracme.evcard.rpc.dto.Page;
import com.extracme.evcard.rpc.dto.PageBeanDto;
import com.extracme.evcard.rpc.exception.BusinessRuntimeException;
import com.extracme.framework.core.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/9/23
 */
@Slf4j
@Service
public class MemberProvisionService implements IMemberProvisionService{

    @Autowired
    private MmpProvisionInfoMapper mmpProvisionInfoMapper;

    @Autowired
    private MmpProvisionGroupMapper mmpProvisionGroupMapper;

    @Autowired
    private MmpProvisionGroupNodeMapper mmpProvisionGroupNodeMapper;

    @Autowired
    private MmpProvisionUserTagMapper mmpProvisionUserTagMapper;

    @Autowired
    private MmpProvisionUserFeedbackMapper mmpProvisionUserFeedbackMapper;

    @Resource
    private UserContractMapper userContractMapper;

    @Value("${csms_address}")
    private String csmsAddress;

    @Value("${oss.web_url}")
    private String ossFilePath;


    private static final String KEY_PROVISION_GROUP = "provisionGroup:";
    private static final int EXPIRE_PROVISION_GROUP = 60 * 10;

    @Override
    public ProvisionGroupViewDto getProvisionGroups(int type) {
        ProvisionGroupViewDto groupViewDto = new ProvisionGroupViewDto();
        /**
         * 1. 当前条款版本信息
         */
        ProvisionViewDto provisionView = getActiveMmpProvision(type);
        if(provisionView != null) {
            groupViewDto.setProvisionId(provisionView.getProvisionId());
            groupViewDto.setVersion(provisionView.getVersion());
            groupViewDto.setProvisionUrl(provisionView.getProvisionUrl());
        }
        /**
         * 2. 条款分组信息
         */
        if(type == 3) {
            groupViewDto.setGroups(getProvisionGroupList(type));
        }
        return groupViewDto;
    }


    public List<ProvisionGroupDto> getProvisionGroupList(int type) {
        List<ProvisionGroupDto> provisionGroups = null;
        String redisKey = KEY_PROVISION_GROUP + type;
        String value = JedisUtil.get(redisKey);
        //1. 缓存中不存在，则从数据库中获取
        if (value == null) {
            synchronized (this) {
                String cacheValue = JedisUtil.get(redisKey);
                //redis中存在，则直接从redis中读取。
                if(cacheValue != null) {
                    provisionGroups = JSON.parseArray(cacheValue, ProvisionGroupDto.class);
                }else {
                    //redis中不存在，则读数据库，并更新缓存。
                    provisionGroups = new ArrayList<>();
                    List<MmpProvisionGroup> groups = mmpProvisionGroupMapper.queryGroups();
                    if(CollectionUtils.isNotEmpty(groups)) {
                        for(MmpProvisionGroup group : groups) {
                            ProvisionGroupDto groupDto = new ProvisionGroupDto();
                            BeanCopyUtils.copyProperties(group, groupDto);
                            provisionGroups.add(groupDto);
                        }
                    }
                    JedisUtil.setCover(redisKey, JSON.toJSONString(provisionGroups), EXPIRE_PROVISION_GROUP);
                }
                return provisionGroups;
            }
        }
        provisionGroups = JSON.parseArray(value, ProvisionGroupDto.class);
        return provisionGroups;
    }


    private static final String KEY_PROVISION_NODE = "provisionNodes";
    private static final int EXPIRE_PROVISION_NODE = 60 * 10;
    @Override
    public List<ProvisionNodeDto> getProvisionNodesOfGroup(Long groupId) {
        if(groupId == null) {
            throw BusinessRuntimeException.PARAM_EXEPTION;
        }

        List<ProvisionNodeDto> result = null;
        String redisKey = KEY_PROVISION_NODE;
        String value = JedisUtil.hget(redisKey, String.valueOf(groupId));
        //2. 缓存中不存在，则从数据库中获取
        if (value == null) {
            synchronized (this) {
                String cacheValue = JedisUtil.hget(redisKey, String.valueOf(groupId));
                //redis中存在，则直接从redis中读取。
                if(cacheValue != null) {
                    result = JSON.parseArray(cacheValue, ProvisionNodeDto.class);
                }else {
                    //redis中不存在，则读数据库，并更新缓存。
                    result = mmpProvisionGroupNodeMapper.selectNodesByGroupId(groupId);
                    JedisUtil.hset(redisKey, String.valueOf(groupId), JSON.toJSONString(result));
                    JedisUtil.expire(redisKey, EXPIRE_PROVISION_NODE);
                }
                return result;
            }
        }
        result = JSON.parseArray(value, ProvisionNodeDto.class);
        return result;
    }

    private String genAnonymousAuthId(){
        return StringUtils.abbreviate("anon_" + System.currentTimeMillis(), 20);
    }


    @Override
    public Long addNodeTag(String authId, ProvisionUserTagInput input) throws MemberException {
        //参数检查
        log.debug("addNodeTag，input={}", JSON.toJSONString(input));
        if(StringUtils.isBlank(input.getNodeId())) {
            throw BusinessRuntimeException.PARAM_EXEPTION;
        }
        if(StringUtils.isEmpty(authId)) {
            authId = genAnonymousAuthId();
        }
        if(input.getTag() < 0 && input.getTagId() == null) {
            throw BusinessRuntimeException.PARAM_EXEPTION;
        }
        try {
            if(input.getTag() < 0) {
                mmpProvisionUserTagMapper.removeTag(input.getTagId(), authId);
                return input.getTagId();
            }else {
                Long provisionId = 0L;
                MmpProvisionInfo provision = mmpProvisionInfoMapper.queryLatestMmpProvision(input.getType());
                if (provision != null) {
                    provisionId = provision.getId();
                }
                MmpProvisionUserTag record = new MmpProvisionUserTag();
                record.setAuthId(authId);
                record.setNodeId(input.getNodeId());
                record.setProvisionId(provisionId);
                record.setTagType(input.getTag());
                record.setStatus(0);
                record.setCreateOperId(1L);
                record.setCreateOperName(authId);
                record.setUpdateOperId(1L);
                record.setUpdateOperName(authId);
                mmpProvisionUserTagMapper.insertSelective(record);
                return record.getId();
            }
        }catch (Exception e) {
            log.error("addNodeTag 失败，input=" + JSON.toJSONString(input), e);
            throw new MemberException(-1, "提交失败");
        }
    }

    @Override
    public void submitNodeFeedback(String authId, ProvisionUserFeedbackInput input) {
        try {
            if(StringUtils.isBlank(input.getNodeId())) {
                throw BusinessRuntimeException.PARAM_EXEPTION;
            }
            if(StringUtils.isEmpty(authId)) {
                authId = genAnonymousAuthId();
            }
            if(StringUtils.isBlank(input.getContent()) || input.getContent().length() > 200) {
                throw BusinessRuntimeException.PARAM_EXEPTION;
            }
            MmpProvisionInfo provision = mmpProvisionInfoMapper.queryLatestMmpProvision(input.getType());
            if (provision != null) {
                MmpProvisionUserFeedback record = new MmpProvisionUserFeedback();
                record.setAuthId(authId);
                record.setNodeId(input.getNodeId());
                record.setProvisionId(provision.getId());
                record.setContent(input.getContent());
                record.setStatus(0);
                record.setCreateOperId(1L);
                record.setCreateOperName(authId);
                record.setUpdateOperId(1L);
                record.setUpdateOperName(authId);
                mmpProvisionUserFeedbackMapper.insertSelective(record);
            }
        }catch (Exception e) {
            log.error("submitNodeFeedback 失败，input=" + JSON.toJSONString(input), e);
            throw new MemberException(-1, "提交失败");
        }
    }

    @Override
    public ProvisionUserTagDto queryUserTags(Long provisionId, String nodeId){
        return mmpProvisionUserTagMapper.countTagsByNodeId(nodeId, 3);
    }

    @Override
    public PageBeanDto<ProvisionUserFeedbackDto> queryUserFeedbacks(ProvisionFeedbackQueryDto input, Page page){
        page = new Page(page.getPageNo(), page.getPageSize(), page.getCountFlag());
        if (page.getCountFlag()) {
            Integer count = mmpProvisionUserFeedbackMapper.countPageByNodeId(input.getNodeId(), 3,
                    input.getStartDate(), input.getEndDate());
            page.setCount(count);
        }
        List<MmpProvisionUserFeedback> list = mmpProvisionUserFeedbackMapper.queryPageByNodeId(input.getNodeId(), 3,
                input.getStartDate(), input.getEndDate(), page);
        List<ProvisionUserFeedbackDto> result = new ArrayList<>();
        for (MmpProvisionUserFeedback record : list) {
            ProvisionUserFeedbackDto dto = new ProvisionUserFeedbackDto();
            BeanCopyUtils.copyProperties(record, dto);
            result.add(dto);
        }
        PageBeanDto<ProvisionUserFeedbackDto> pageDtoPageBeanDto = new PageBeanDto<>();
        pageDtoPageBeanDto.setPage(page);
        pageDtoPageBeanDto.setList(result);
        return pageDtoPageBeanDto;
    }

//    public ProvisionInfoDto queryActiveMmpProvision(int type) {
//        MmpProvisionInfo provision = mmpProvisionInfoMapper.queryLatestMmpProvision(type);
//        if (provision == null) {
//            return null;
//        }
//        ProvisionInfoDto provisionDto = new ProvisionInfoDto();
//        BeanCopyUtils.copyProperties(provision, provisionDto);
//        switch (type) {
//            case 1:
//                provisionDto.setProvisionUrl(csmsAddress + BussinessConstants.MEMEBER_SZ);
//                break;
//            case 2:
//                provisionDto.setProvisionUrl(csmsAddress + BussinessConstants.MEMEBER_YS);
//                break;
//            case 3:
//                provisionDto.setProvisionUrl(ossFilePath + provision.getProvisionAddress());
//                break;
//        }
//        return provisionDto;
//    }

    private static final String KEY_PROVISION_VERSION = "typeVersionMap";
    public ProvisionViewDto getActiveMmpProvision(int type){
        ProvisionViewDto provisionView = new ProvisionViewDto();
        //会员条款
        String version = JedisUtil.hget(KEY_PROVISION_VERSION, String.valueOf(type));
        if (StringUtils.isBlank(version)) {
            MmpProvisionInfo provision = mmpProvisionInfoMapper.queryLatestMmpProvision(type);
            if(provision == null) {
                return null;
            }
            version = provision.getVersion();
            provisionView.setProvisionId(provision.getId());
            JedisUtil.hset(KEY_PROVISION_VERSION, String.valueOf(type), version);
        }
        provisionView.setVersion(version);
        switch (type) {
            case 1:
                provisionView.setProvisionUrl(csmsAddress + BussinessConstants.MEMEBER_SZ);
                break;
            case 2:
                provisionView.setProvisionUrl(csmsAddress + BussinessConstants.MEMEBER_YS);
                break;
            case 3:
                provisionView.setProvisionUrl(ossFilePath + String.format(BussinessConstants.MEMBER_WT_FMT, version));
                break;
        }
        return provisionView;
    }

    @Override
    public UserContractViewDto queryMemberContract(MemberContractQueryInput input) throws BusinessRuntimeException{
        if(StringUtils.isBlank(input.getAuthId())) {
            throw BusinessRuntimeException.PARAM_EXEPTION;
        }
        if(input.getDate() == null) {
            input.setDate(new Date());
        }
        if(input.getProvisionType() == null) {
            input.setProvisionType(1);
        }
        String prefix = StringUtils.EMPTY;
        if(input.getProvisionType() == 1) {
            prefix = "SZ";
        } else if(input.getProvisionType() != 2) {
            prefix = "YS";
        }
        String time = ComUtil.getFormatDate(input.getDate(), ComUtil.DATE_TYPE3);
        //查询用户最近签署的合同表
        UserContract contract = userContractMapper.selectMemberLastContract(input.getAuthId(), prefix, time);
        if(contract == null) {
            log.info("userContract：用户尚未签署协议, input={}", JSON.toJSONString(input));
            return null;
        }
        UserContractViewDto contractViewDto = new UserContractViewDto();
        BeanCopyUtils.copyProperties(contract, contractViewDto);
        //查询合同所属版本的会员条款
        MmpProvisionInfo provision = mmpProvisionInfoMapper.queryMmpProvisionByVersion(contract.getVersionId(), input.getProvisionType());
        if(provision != null) {
            ProvisionDetailDto provisionDetail = new ProvisionDetailDto();
            BeanCopyUtils.copyProperties(provision, provisionDetail);
            contractViewDto.setProvisionInfo(provisionDetail);
        }
        return contractViewDto;
    }


    @Override
    public UserContractInfo getUserContractArchiveUrl(String authId, String templateId) {
        UserContractInfo contract = userContractMapper.selectUserContractInfo(authId, templateId);
        if(contract == null) {
            log.error("查询用户协议文件失败，authId={}, templatId={}.", authId, templateId);
            return null;
        }
        if(StringUtils.isBlank(contract.getArchiveUrl())) {
            log.warn("用户协议文件归档地址不存在，authId={}, templatId={}, Id={}.", authId, templateId, contract.getId());
            contract.setArchiveUrl(contract.getViewpdfUrl());
        }
        contract.setArchiveUrl(ComUtil.getFileFullPath(contract.getArchiveUrl()));
        return contract;
    }

    @Override
    public UserContractViewDto getMemberCloseContract(MemberContractQueryInput input) throws BusinessRuntimeException{
        if(StringUtils.isBlank(input.getAuthId())) {
            throw BusinessRuntimeException.PARAM_EXEPTION;
        }
        if(input.getDate() == null) {
            input.setDate(new Date());
        }
        if(input.getProvisionType() == null) {
            input.setProvisionType(1);
        }
        String prefix = StringUtils.EMPTY;
        if(input.getProvisionType() == 1) {
            prefix = "SZ";
        } else if(input.getProvisionType() != 2) {
            prefix = "YS";
        }
        String time = ComUtil.getFormatDate(input.getDate(), ComUtil.DATE_TYPE3);
        //查询用户最近签署的合同表
        UserContract contract = userContractMapper.selectMemberCloseContract(input.getAuthId(), prefix, time, input.getVersionId());
        if(contract == null) {
            log.info("userContract：用户尚未签署协议, input={}", JSON.toJSONString(input));
            return null;
        }
        UserContractViewDto contractViewDto = new UserContractViewDto();
        BeanCopyUtils.copyProperties(contract, contractViewDto);
        //查询合同所属版本的会员条款
        MmpProvisionInfo provision = mmpProvisionInfoMapper.queryMmpProvisionByVersion(contract.getVersionId(), input.getProvisionType());
        if(provision != null) {
            ProvisionDetailDto provisionDetail = new ProvisionDetailDto();
            BeanCopyUtils.copyProperties(provision, provisionDetail);
            contractViewDto.setProvisionInfo(provisionDetail);
        }
        return contractViewDto;
    }
}
