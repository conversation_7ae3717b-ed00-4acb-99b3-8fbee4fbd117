package com.extracme.evcard.membership.core.service.md;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.config.MdRestApiConfig;
import com.extracme.evcard.membership.core.dto.md.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/8/30
 */
@Slf4j
@Component
public class MdDepositService {
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private MdRestApiConfig mdRestApiConfig;

    public GetMemberDepositInfosData getMemberDepositInfos(String mid) {
        try {
            String path = mdRestApiConfig.getBaseUrl() + mdRestApiConfig.getGetMemberDepositInfos();
            GetMemberDepositInfosRequest request = new GetMemberDepositInfosRequest();
            request.setMid(mid);
            GetMemberDepositInfosResponse memberDepositInfos = restTemplate.postForObject(path, request, GetMemberDepositInfosResponse.class);
            log.info("getMemberDepositInfos  getMemberDepositInfos，mid=[{}],memberDepositInfos={}", mid, JSON.toJSONString(memberDepositInfos));
            if (memberDepositInfos != null && memberDepositInfos.getCode() == 0) {
                return memberDepositInfos.getData();
            }
        } catch (Exception e) {
            log.error("getMemberDepositInfos  异常，dto=[{}]", mid, e);
        }
        return null;
    }
}
