<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.extracme.evcard.membership.core.dao.MmpPlatformInfoMapper">
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.MmpPlatformInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 22 15:40:39 CST 2019.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="platform_explain" jdbcType="VARCHAR" property="platformExplain" />
    <result column="misc_desc" jdbcType="VARCHAR" property="miscDesc" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_oper_id" jdbcType="BIGINT" property="createOperId" />
    <result column="create_oper_name" jdbcType="VARCHAR" property="createOperName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_oper_id" jdbcType="BIGINT" property="updateOperId" />
    <result column="update_oper_name" jdbcType="VARCHAR" property="updateOperName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 22 15:40:39 CST 2019.
    -->
    id, platform_name, platform_explain, misc_desc, status, create_time, create_oper_id, 
    create_oper_name, update_time, update_oper_id, update_oper_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 22 15:40:39 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_platform_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 22 15:40:39 CST 2019.
    -->
    delete from ${issSchema}.mmp_platform_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.membership.core.model.MmpPlatformInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 22 15:40:39 CST 2019.
    -->
    insert into ${issSchema}.mmp_platform_info (id, platform_name, platform_explain, 
      misc_desc, status, create_time, 
      create_oper_id, create_oper_name, update_time, 
      update_oper_id, update_oper_name)
    values (#{id,jdbcType=BIGINT}, #{platformName,jdbcType=VARCHAR}, #{platformExplain,jdbcType=VARCHAR}, 
      #{miscDesc,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{createOperId,jdbcType=BIGINT}, #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updateOperId,jdbcType=BIGINT}, #{updateOperName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.core.model.MmpPlatformInfo" useGeneratedKeys="true" keyProperty="id">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 22 15:40:39 CST 2019.
    -->
    insert into ${issSchema}.mmp_platform_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="platformName != null">
        platform_name,
      </if>
      <if test="platformExplain != null">
        platform_explain,
      </if>
      <if test="miscDesc != null">
        misc_desc,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createOperId != null">
        create_oper_id,
      </if>
      <if test="createOperName != null">
        create_oper_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateOperId != null">
        update_oper_id,
      </if>
      <if test="updateOperName != null">
        update_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="platformName != null">
        #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="platformExplain != null">
        #{platformExplain,jdbcType=VARCHAR},
      </if>
      <if test="miscDesc != null">
        #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        #{updateOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.core.model.MmpPlatformInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 22 15:40:39 CST 2019.
    -->
    update ${issSchema}.mmp_platform_info
    <set>
      <if test="platformName != null">
        platform_name = #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="platformExplain != null">
        platform_explain = #{platformExplain,jdbcType=VARCHAR},
      </if>
      <if test="miscDesc != null">
        misc_desc = #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.core.model.MmpPlatformInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Jan 22 15:40:39 CST 2019.
    -->
    update ${issSchema}.mmp_platform_info
    set platform_name = #{platformName,jdbcType=VARCHAR},
      platform_explain = #{platformExplain,jdbcType=VARCHAR},
      misc_desc = #{miscDesc,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <select id="queryAllPlatform" resultType="com.extracme.evcard.membership.core.dto.PlatformInfoDTO">
  	SELECT
		id AS id,
		platform_name AS platformName
	FROM
		${issSchema}.mmp_platform_info 
  </select>

  <select id="getAppKeysByPlatformId" resultType="java.lang.String">
    SELECT
    app_key
    FROM
    siac.app_key_manager
    WHERE
    platform_id = #{platformId,jdbcType=BIGINT}
    <if test="status != null">
      and status = #{status,jdbcType=INTEGER}
    </if>
  </select>

</mapper>