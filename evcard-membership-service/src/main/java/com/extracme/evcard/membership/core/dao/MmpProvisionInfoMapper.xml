<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.core.dao.MmpProvisionInfoMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.MmpProvisionInfo" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="provision_type" property="provisionType" jdbcType="INTEGER" />
    <result column="version" property="version" jdbcType="VARCHAR" />
    <result column="start_time" property="startTime" jdbcType="TIMESTAMP" />
    <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
    <result column="provision_status" property="provisionStatus" jdbcType="INTEGER" />
    <result column="publisher_id" property="publisherId" jdbcType="BIGINT" />
    <result column="provision_address" property="provisionAddress" jdbcType="VARCHAR" />
    <result column="misc_desc" property="miscDesc" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
    <result column="word_address" property="wordAddress" jdbcType="VARCHAR" />
    <result column="provision_pic" property="provisionPic" jdbcType="VARCHAR" />
    <result column="content" property="content" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, provision_type, version, start_time, end_time, provision_status, publisher_id, 
    provision_address, misc_desc, status, create_time, create_oper_id, create_oper_name, 
    update_time, update_oper_id, update_oper_name, word_address, provision_pic, content
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from mmp_provision_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from mmp_provision_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.membership.core.model.MmpProvisionInfo" >
    insert into mmp_provision_info (id, provision_type, version, 
      start_time, end_time, provision_status, 
      publisher_id, provision_address, misc_desc, 
      status, create_time, create_oper_id, 
      create_oper_name, update_time, update_oper_id, 
      update_oper_name, word_address, provision_pic, 
      content)
    values (#{id,jdbcType=BIGINT}, #{provisionType,jdbcType=INTEGER}, #{version,jdbcType=VARCHAR}, 
      #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{provisionStatus,jdbcType=INTEGER}, 
      #{publisherId,jdbcType=BIGINT}, #{provisionAddress,jdbcType=VARCHAR}, #{miscDesc,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, 
      #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, 
      #{updateOperName,jdbcType=VARCHAR}, #{wordAddress,jdbcType=VARCHAR}, #{provisionPic,jdbcType=VARCHAR}, 
      #{content,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.core.model.MmpProvisionInfo" >
    insert into mmp_provision_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="provisionType != null" >
        provision_type,
      </if>
      <if test="version != null" >
        version,
      </if>
      <if test="startTime != null" >
        start_time,
      </if>
      <if test="endTime != null" >
        end_time,
      </if>
      <if test="provisionStatus != null" >
        provision_status,
      </if>
      <if test="publisherId != null" >
        publisher_id,
      </if>
      <if test="provisionAddress != null" >
        provision_address,
      </if>
      <if test="miscDesc != null" >
        misc_desc,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateOperId != null" >
        update_oper_id,
      </if>
      <if test="updateOperName != null" >
        update_oper_name,
      </if>
      <if test="wordAddress != null" >
        word_address,
      </if>
      <if test="provisionPic != null" >
        provision_pic,
      </if>
      <if test="content != null" >
        content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="provisionType != null" >
        #{provisionType,jdbcType=INTEGER},
      </if>
      <if test="version != null" >
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null" >
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null" >
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="provisionStatus != null" >
        #{provisionStatus,jdbcType=INTEGER},
      </if>
      <if test="publisherId != null" >
        #{publisherId,jdbcType=BIGINT},
      </if>
      <if test="provisionAddress != null" >
        #{provisionAddress,jdbcType=VARCHAR},
      </if>
      <if test="miscDesc != null" >
        #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="wordAddress != null" >
        #{wordAddress,jdbcType=VARCHAR},
      </if>
      <if test="provisionPic != null" >
        #{provisionPic,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        #{content,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.core.model.MmpProvisionInfo" >
    update mmp_provision_info
    <set >
      <if test="provisionType != null" >
        provision_type = #{provisionType,jdbcType=INTEGER},
      </if>
      <if test="version != null" >
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null" >
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null" >
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="provisionStatus != null" >
        provision_status = #{provisionStatus,jdbcType=INTEGER},
      </if>
      <if test="publisherId != null" >
        publisher_id = #{publisherId,jdbcType=BIGINT},
      </if>
      <if test="provisionAddress != null" >
        provision_address = #{provisionAddress,jdbcType=VARCHAR},
      </if>
      <if test="miscDesc != null" >
        misc_desc = #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="wordAddress != null" >
        word_address = #{wordAddress,jdbcType=VARCHAR},
      </if>
      <if test="provisionPic != null" >
        provision_pic = #{provisionPic,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        content = #{content,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.core.model.MmpProvisionInfo" >
    update mmp_provision_info
    set provision_type = #{provisionType,jdbcType=INTEGER},
      version = #{version,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      provision_status = #{provisionStatus,jdbcType=INTEGER},
      publisher_id = #{publisherId,jdbcType=BIGINT},
      provision_address = #{provisionAddress,jdbcType=VARCHAR},
      misc_desc = #{miscDesc,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      word_address = #{wordAddress,jdbcType=VARCHAR},
      provision_pic = #{provisionPic,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryLatestMmpProvisionVersion" resultType="string" parameterType="integer">
       SELECT
       	version
       FROM
       	iss.mmp_provision_info
       WHERE
       	provision_status = 2
       AND provision_type = #{provisionType}
       LIMIT 1
  </select>

  <select id="queryLatestMmpProvision" resultMap="BaseResultMap" parameterType="integer">
       SELECT
    <include refid="Base_Column_List" />
       FROM
       	iss.mmp_provision_info
       WHERE
       	provision_status = 2
       AND provision_type = #{provisionType}
       LIMIT 1
  </select>


  <select id="queryMmpProvisionByVersion" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM
    iss.mmp_provision_info
    WHERE provision_type = #{provisionType} and version = #{version,jdbcType=VARCHAR}
    and `status` = 1
    LIMIT 1
  </select>

</mapper>