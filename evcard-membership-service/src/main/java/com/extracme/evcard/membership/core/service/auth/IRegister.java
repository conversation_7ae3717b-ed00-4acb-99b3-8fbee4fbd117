package com.extracme.evcard.membership.core.service.auth;

import com.extracme.evcard.membership.core.input.NotifyLoginDto;
import com.extracme.evcard.membership.core.input.NotifyRegisterDto;

public interface IRegister {

    /**
     * 注册成功后 通知
     * @param dto
     */
    void notifyRegisterSuccess(NotifyRegisterDto dto);

    /**
     * 注册成功后 通知
     * @param dto
     */
    void notifyLoginSuccess(NotifyLoginDto dto);
}
