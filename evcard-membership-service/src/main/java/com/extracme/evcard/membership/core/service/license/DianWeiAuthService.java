package com.extracme.evcard.membership.core.service.license;


import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.common.SeqGenerator;
import com.extracme.evcard.membership.config.DianWeiServiceConfig;
import com.extracme.evcard.membership.core.dto.DriverLicenseElementsAuthenticateLogDTO;
import com.extracme.evcard.membership.core.dto.DriverLicenseQueryResultDTO;
import com.extracme.evcard.membership.core.dto.DriverLicenseValidResultDto;
import com.extracme.evcard.membership.core.input.SaveDriverElementsAuthenticateLogInput;
import com.extracme.evcard.rpc.util.BeanCopyUtils;
import com.extracme.evcard.third.dianwei.api.IAbstractDianweiService;
import com.extracme.evcard.third.dianwei.entity.*;
import com.extracme.evcard.third.dianwei.util.DianWeiConstants;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 基于点微8320接口的
 * 三要素核查服务提供者
 */
@Slf4j
@Component
public class DianWeiAuthService extends IAbstractDianweiService implements ILicenseAuthenticateService {

    protected Integer getSupplier() {
        return 2;
    }

    @Autowired
    protected OkHttpClient okHttpClient;

    @Autowired
    protected DianWeiServiceConfig dianWeiServiceConfig;

    protected DianweiConfig dianweiConfig = null;

    @Autowired
    protected SeqGenerator seqGenerator;

    @Override
    public DianweiConfig getConfig() {
        if (Objects.isNull(this.dianweiConfig)){
            this.dianweiConfig = new DianweiConfig();
            this.dianweiConfig.setChannelId(dianWeiServiceConfig.getChannelId());
            this.dianweiConfig.setEnkey(dianWeiServiceConfig.getEnkey());
            this.dianweiConfig.setProductCode(dianWeiServiceConfig.getProductCodeVerify());
            this.dianweiConfig.setSalt(dianWeiServiceConfig.getSalt());
            this.dianweiConfig.setSubChannelName(dianWeiServiceConfig.getSubChannelName());
            this.dianweiConfig.setUrl(dianWeiServiceConfig.getUrl());
        }
        return this.dianweiConfig;
    }

    @Override
    public okhttp3.OkHttpClient getHttpClient() {
        return this.okHttpClient;
    }

    @Override
    public SaveDriverElementsAuthenticateLogInput authenticate(String driverCode, String name, String fileNo, Integer readTimeOut) {
        SaveDriverElementsAuthenticateLogInput logInput = new SaveDriverElementsAuthenticateLogInput();
        logInput.setName(name);
        logInput.setDriverCode(driverCode);
        logInput.setFileNo(fileNo);
        logInput.setLogType(0);
        logInput.setSupplier(getSupplier());
        logInput.setServiceName(getConfig().getUrl());
        LicenseAuthenticateResult resp = licenseAuthenticate(name, driverCode, fileNo, readTimeOut);
        /**
         * 返回结果
         */
        logInput.setRequestId(resp.getRequestId());
        logInput.setResult(resp.getResult());
        logInput.setResultCode(String.valueOf(resp.getErrCode()));
        logInput.setResultMsg(resp.getReason());
        logInput.setResponse(resp.getResponse());
        //三要素认证结果：驾照号/姓名/档案编号，0不一致，1一致，如010
        String itemCheckResult = StringUtils.join(resp.getCardNoCheckResult(), resp.getNameCheckResult(), resp.getArchviesNoCheckResult());
        logInput.setElementsReviewItems(itemCheckResult);
        logInput.setLicenseStatusMsg(resp.getMsg());
        return logInput;
    }

    @Override
    public SaveDriverElementsAuthenticateLogInput queryDriverDeduction(String driverCode, String name, String fileNo) {
        SaveDriverElementsAuthenticateLogInput logInput = new SaveDriverElementsAuthenticateLogInput();
        logInput.setName(name);
        logInput.setDriverCode(driverCode);
        logInput.setFileNo(fileNo);
        logInput.setLogType(1);
        logInput.setSupplier(getSupplier());
        logInput.setServiceName("dainwei-deduction-none");

        int resultCode = 0;
        String resultMsg = "success";
        String result = "{\"error_code\":0,\"reason\":\"success\",\"result\":\"0\"}";
        logInput.setResult(String.valueOf(resultCode));
        logInput.setResultCode(String.valueOf(resultCode));
        logInput.setResultMsg(resultMsg);
        logInput.setResponse(result);
        return logInput;
    }

    @Override
    @Deprecated
    public DriverLicenseQueryResultDTO queryDriverLicenseInfo(String driverCode, String name) {
        return null;
    }

    @Override
    public DriverLicenseValidResultDto checkDriverLicenseValid(String name, String cardNo, String archviesNo) {
        LicenseAuthenticateResult authenticateResult = licenseAuthenticate(name, cardNo, archviesNo, null);
        if(authenticateResult == null) {
            return null;
        }
        DriverLicenseValidResultDto driverLicenseValidResultDto = new DriverLicenseValidResultDto();
        BeanCopyUtils.copyProperties(authenticateResult, driverLicenseValidResultDto);
        return driverLicenseValidResultDto;
    }

    private static final String CHECK_OK = "1";
    private static final String CHECK_FAIL = "2";
    private static final String CHECK_RETRY = "3";
    private static final String CHECK_UNKNOWN = "4";

    public LicenseAuthenticateResult licenseAuthenticate(String name, String cardNo, String archviesNo, Integer readTimeOut) {
        /**
         * 1. 调用供应商三要素核验接口
         *    请求流水号：大写 & 唯一
         */
        DianweiResult<DianweiVerifyRes> resp;
        DianweiReq req = DianweiReq.builder().name(name).cid(cardNo.toUpperCase()).fileNum(archviesNo).driverStatus("正常").build();
        req.setReqNo(seqGenerator.genDianWeiReqSeq());
        try {
            //lambda与AtomicReference结合使用。
            AtomicReference<DianweiResult<DianweiVerifyRes>> result = new AtomicReference<>();
            super.verify(req, false, response -> {
                response.setReqNo(req.getReqNo());
                result.set(response);
            }, (request, e)-> {
                log.error("点微三要素核查：核查失败，input=" + JSON.toJSONString(req), e);
                result.set(new DianweiResult<>(req.getReqNo(), "-1", e.getMessage(), null));
            });
            resp = result.get();
            //直接使用okhttp封装方法
            //resp = super.verifySync(req);
            //使用HttpClientUtils，指定读取超时时间
            //String jsonResult = HttpClientUtils.httpPostRequest(getConfig().getUrl(), super.buildRequest(req), readTimeOut);
            //return JSON.parseObject(jsonResult, new TypeReference<DianweiResult<DianweiVerifyRes>>() {});
        }catch (Exception e) {
            log.error("点微三要素核查：核查异常，input=" + JSON.toJSONString(req), e);
            resp = new DianweiResult<>(req.getReqNo(), "-1", e.getMessage(), null);
        }

        /**
         * 2. 组织及转化核查结果
         */
        LicenseAuthenticateResult result = getLicenseCheckResult(resp);
        result.setRequestId(req.getReqNo());
        log.warn("点微三要素核查：核查完成，name={}, cardNo={}, fileNo={}, result={}", name, cardNo, archviesNo, JSON.toJSON(result));
        return result;
    }

    private LicenseAuthenticateResult getLicenseCheckResult(DianweiResult<DianweiVerifyRes> resp) {
        /**
         * 1. 组织及转化核验结果
         */
        String response = JSON.toJSONString(resp);
        if(StringUtils.equals(resp.getCode(), "-1")) {
            return LicenseAuthenticateResult.getException(response, StringUtils.abbreviate(resp.getMsg(), 50));
        }
        /**
         * 1.1 请求失败场景
         * 请求code 仅code= C0 或 C11表示有查询结果，做后续解析
         * 其他错误 跟产品配置、调用余额、白名单有关，不要做查无重试。
         */
        if(resp == null || !Arrays.asList(DianWeiConstants.CODE_STATUS_OK, DianWeiConstants.CODE_STATUS_INVALID_CID).contains(resp.getCode())) {
            return LicenseAuthenticateResult.getFail(response, resp.getMsg());
        }

        /**
         * 2.2 身份证查验不通过
         */
        if(DianWeiConstants.CODE_STATUS_INVALID_CID.equals(resp.getCode())) {
            return new LicenseAuthenticateResult(CHECK_FAIL, true, false, true,  response, StringUtils.EMPTY, resp.getMsg());
        }

        /**
         * 2.3 姓名/驾照匹配，驾照档案编号匹配
         */
        LicenseAuthenticateResult result = new LicenseAuthenticateResult();
        DianweiVerifyRes verifyRes = resp.getData();
        String verifyDesc = StringUtils.join('[', verifyRes.getRespCode(), ']', verifyRes.getRespDesc());
        //驾照
        if(DianWeiConstants.CODE_PASS.equals(verifyRes.getRespCode())) {
            if(DianWeiConstants.ITEM_CHECK_PASS.equals(verifyRes.getDetail().getFile_no())) {
                //1. 三要素核查全部通过
                String devierStatus = (DianWeiConstants.ITEM_CHECK_ERR.equals(verifyRes.getDetail().getStatus()) ? "异常" : StringUtils.EMPTY);
                result = LicenseAuthenticateResult.getSuccess(response, devierStatus);
            }else {
                //2. 姓名身份证核查通过，档案编号不通过
                result.set(CHECK_FAIL, true, true, false,  StringUtils.EMPTY);
            }
        } else if(DianWeiConstants.CODE_FAIL.equals(verifyRes.getRespCode())) {
            result.set(CHECK_FAIL, false, false, true, StringUtils.EMPTY);
        }else if(DianWeiConstants.CODE_VALID_AUTH.equals(verifyRes.getRespCode())) {
            //身份证未命中
            result.set(CHECK_FAIL, true, false, true, StringUtils.EMPTY);
        }else if(DianWeiConstants.CODE_EXCEPTION.equals(verifyRes.getRespCode())) {
            result.set(CHECK_RETRY, true, true, true, StringUtils.EMPTY);
        } else {
            return LicenseAuthenticateResult.getFail(response, verifyDesc);
        }
        result.setResponse(response);
        //result.setReason(StringUtils.join(resp.getMsg(), ':', result.getMsg()));
        result.setReason(StringUtils.abbreviate(verifyDesc, 50));
        return result;
    }

    @Override
    public String buildAuthResultDetail(DriverLicenseElementsAuthenticateLogDTO logDTO) {
        String msg = StringUtils.EMPTY;
        try {
            String result = logDTO.getResult().trim();
            String itemCheckResult = logDTO.getElementsReviewItems();
            switch (result) {
                case CHECK_OK:
                    if(StringUtils.isBlank(logDTO.getLicenseStatusMsg())) {
                        logDTO.setLicenseStatusMsg("正常");
                    }
                    return "认证成功";
                case CHECK_FAIL:
                    StringBuffer sb = new StringBuffer();
                    if (itemCheckResult == null || itemCheckResult.length() != 3) {
                        return "认证不一致";
                    }
                    sb.append("【身份证号码核查结果】：").append(LicenseAuthUtils.itemToDesc(itemCheckResult, 0)).append(";   ");
                    sb.append("【姓名核查结果】：").append(LicenseAuthUtils.itemToDesc(itemCheckResult, 1)).append(";   ");
                    sb.append("【档案编号核查结果】：").append(LicenseAuthUtils.itemToDesc(itemCheckResult, 2)).append(";   ");
                    return sb.toString();
                case CHECK_RETRY:
                    msg = "查无记录或无法核查 ";
                    if (StringUtils.isNotBlank(logDTO.getResultMsg())) {
                        msg += "，" + logDTO.getResultMsg();
                    }
                    break;
                case CHECK_UNKNOWN:
                    msg = "查询异常 ";
                    if (StringUtils.isNotBlank(logDTO.getResultMsg())) {
                        msg += logDTO.getResultMsg();
                    }
                    break;
            }
        }catch (Exception ex) {
            log.error("组织三要素核查结果异常", ex);
            msg = "查询失败 ";
        }
        return msg;
    }

}
