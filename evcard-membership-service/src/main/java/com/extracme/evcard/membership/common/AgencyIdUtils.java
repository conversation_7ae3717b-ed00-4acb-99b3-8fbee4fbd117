package com.extracme.evcard.membership.common;

/**
 * Created by lx920 on 2018/5/22.
 */
public class AgencyIdUtils {

    public static String getNextNodeId(String nodeId) {
        String nextNodeId = "";
        String a = stringToAscii(nodeId.substring(nodeId.length() - 2, nodeId.length()));
        String[] b = a.split(",");
        String bit = b[1];
        String ten = b[0];
        int asci = Integer.parseInt(bit) + 1;
        int ascii = Integer.parseInt(ten);

        // 如果nodeId为ZZ则return
        if (asci == 90 && ascii == 90) {
            System.out.println("当前节点下已达到最大值");
            return "";
        }

        // 个位+1如果大于57则转到A的asc码
        if (asci > 57 && asci < 65) {
            asci = 65;
        }

        // 如果大于90(Z)则转到0的asc码，并且十位asc码+1
        if (asci > 90) {
            asci = 48;
            ascii++;
            if (ascii > 57 && ascii < 65) {
                ascii = 65;
            }
        }
        nextNodeId = asciiToString(ascii + "," + asci);
        return nextNodeId;
    }

    /**
     * stringToAscii 将传入的字符串转换为Ascii码,0~9对应的码是48~57 A~Z对应的码是65~90
     *
     * @param value 传入的字符串
     * @return sbu Ascii码
     */
    public static String stringToAscii(String value) {
        StringBuffer sbu = new StringBuffer();
        char[] chars = value.toCharArray();
        for (int i = 0; i < chars.length; i++) {
            if (i != chars.length - 1) {
                sbu.append((int) chars[i]).append(",");
            } else {
                sbu.append((int) chars[i]);
            }
        }
        return sbu.toString();
    }

    public static String asciiToString(String value) {
        StringBuffer sbu = new StringBuffer();
        String[] chars = value.split(",");
        for (int i = 0; i < chars.length; i++) {
            sbu.append((char) Integer.parseInt(chars[i]));
        }
        return sbu.toString();
    }
}
