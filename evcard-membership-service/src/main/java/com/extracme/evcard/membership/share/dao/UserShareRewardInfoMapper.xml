<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.extracme.evcard.membership.share.dao.UserShareRewardInfoMapper">
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.share.model.UserShareRewardInfo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jan 20 20:50:20 CST 2018.
    -->
    <id column="USER_SHARE_REWARD_INFO_SEQ" jdbcType="BIGINT" property="userShareRewardInfoSeq" />
    <result column="AUTH_ID" jdbcType="VARCHAR" property="authId" />
    <result column="REWARD_TYPE" jdbcType="VARCHAR" property="rewardType" />
    <result column="REWARD_CONTENT" jdbcType="VARCHAR" property="rewardContent" />
    <result column="EAMOUNT" jdbcType="DECIMAL" property="eamount" />
    <result column="ORGIN_USERNAME" jdbcType="VARCHAR" property="orginUsername" />
    <result column="ORGIN_MOBILE_PHONE" jdbcType="VARCHAR" property="orginMobilePhone" />
    <result column="ORGIN_AUTH_ID" jdbcType="VARCHAR" property="orginAuthId" />
    <result column="CREATED_TIME" jdbcType="VARCHAR" property="createdTime" />
    <result column="CREATED_USER" jdbcType="VARCHAR" property="createdUser" />
    <result column="UPDATED_TIME" jdbcType="VARCHAR" property="updatedTime" />
    <result column="UPDATED_USER" jdbcType="VARCHAR" property="updatedUser" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jan 20 20:50:20 CST 2018.
    -->
    USER_SHARE_REWARD_INFO_SEQ, AUTH_ID, REWARD_TYPE, REWARD_CONTENT, EAMOUNT, ORGIN_USERNAME, 
    ORGIN_MOBILE_PHONE, ORGIN_AUTH_ID, CREATED_TIME, CREATED_USER, UPDATED_TIME, UPDATED_USER
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jan 20 20:50:20 CST 2018.
    -->
    select 
    <include refid="Base_Column_List" />
    from user_share_reward_info
    where USER_SHARE_REWARD_INFO_SEQ = #{userShareRewardInfoSeq,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jan 20 20:50:20 CST 2018.
    -->
    delete from user_share_reward_info
    where USER_SHARE_REWARD_INFO_SEQ = #{userShareRewardInfoSeq,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.membership.share.model.UserShareRewardInfo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jan 20 20:50:20 CST 2018.
    -->
    insert into user_share_reward_info (USER_SHARE_REWARD_INFO_SEQ, AUTH_ID, REWARD_TYPE, 
      REWARD_CONTENT, EAMOUNT, ORGIN_USERNAME, 
      ORGIN_MOBILE_PHONE, ORGIN_AUTH_ID, CREATED_TIME, 
      CREATED_USER, UPDATED_TIME, UPDATED_USER
      )
    values (#{userShareRewardInfoSeq,jdbcType=BIGINT}, #{authId,jdbcType=VARCHAR}, #{rewardType,jdbcType=VARCHAR}, 
      #{rewardContent,jdbcType=VARCHAR}, #{eamount,jdbcType=DECIMAL}, #{orginUsername,jdbcType=VARCHAR}, 
      #{orginMobilePhone,jdbcType=VARCHAR}, #{orginAuthId,jdbcType=VARCHAR}, #{createdTime,jdbcType=VARCHAR}, 
      #{createdUser,jdbcType=VARCHAR}, #{updatedTime,jdbcType=VARCHAR}, #{updatedUser,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.share.model.UserShareRewardInfo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jan 20 20:50:20 CST 2018.
    -->
    insert into user_share_reward_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userShareRewardInfoSeq != null">
        USER_SHARE_REWARD_INFO_SEQ,
      </if>
      <if test="authId != null">
        AUTH_ID,
      </if>
      <if test="rewardType != null">
        REWARD_TYPE,
      </if>
      <if test="rewardContent != null">
        REWARD_CONTENT,
      </if>
      <if test="eamount != null">
        EAMOUNT,
      </if>
      <if test="orginUsername != null">
        ORGIN_USERNAME,
      </if>
      <if test="orginMobilePhone != null">
        ORGIN_MOBILE_PHONE,
      </if>
      <if test="orginAuthId != null">
        ORGIN_AUTH_ID,
      </if>
      <if test="createdTime != null">
        CREATED_TIME,
      </if>
      <if test="createdUser != null">
        CREATED_USER,
      </if>
      <if test="updatedTime != null">
        UPDATED_TIME,
      </if>
      <if test="updatedUser != null">
        UPDATED_USER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userShareRewardInfoSeq != null">
        #{userShareRewardInfoSeq,jdbcType=BIGINT},
      </if>
      <if test="authId != null">
        #{authId,jdbcType=VARCHAR},
      </if>
      <if test="rewardType != null">
        #{rewardType,jdbcType=VARCHAR},
      </if>
      <if test="rewardContent != null">
        #{rewardContent,jdbcType=VARCHAR},
      </if>
      <if test="eamount != null">
        #{eamount,jdbcType=DECIMAL},
      </if>
      <if test="orginUsername != null">
        #{orginUsername,jdbcType=VARCHAR},
      </if>
      <if test="orginMobilePhone != null">
        #{orginMobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="orginAuthId != null">
        #{orginAuthId,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=VARCHAR},
      </if>
      <if test="createdUser != null">
        #{createdUser,jdbcType=VARCHAR},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=VARCHAR},
      </if>
      <if test="updatedUser != null">
        #{updatedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.share.model.UserShareRewardInfo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jan 20 20:50:20 CST 2018.
    -->
    update user_share_reward_info
    <set>
      <if test="authId != null">
        AUTH_ID = #{authId,jdbcType=VARCHAR},
      </if>
      <if test="rewardType != null">
        REWARD_TYPE = #{rewardType,jdbcType=VARCHAR},
      </if>
      <if test="rewardContent != null">
        REWARD_CONTENT = #{rewardContent,jdbcType=VARCHAR},
      </if>
      <if test="eamount != null">
        EAMOUNT = #{eamount,jdbcType=DECIMAL},
      </if>
      <if test="orginUsername != null">
        ORGIN_USERNAME = #{orginUsername,jdbcType=VARCHAR},
      </if>
      <if test="orginMobilePhone != null">
        ORGIN_MOBILE_PHONE = #{orginMobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="orginAuthId != null">
        ORGIN_AUTH_ID = #{orginAuthId,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null">
        CREATED_TIME = #{createdTime,jdbcType=VARCHAR},
      </if>
      <if test="createdUser != null">
        CREATED_USER = #{createdUser,jdbcType=VARCHAR},
      </if>
      <if test="updatedTime != null">
        UPDATED_TIME = #{updatedTime,jdbcType=VARCHAR},
      </if>
      <if test="updatedUser != null">
        UPDATED_USER = #{updatedUser,jdbcType=VARCHAR},
      </if>
    </set>
    where USER_SHARE_REWARD_INFO_SEQ = #{userShareRewardInfoSeq,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.share.model.UserShareRewardInfo">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Jan 20 20:50:20 CST 2018.
    -->
    update user_share_reward_info
    set AUTH_ID = #{authId,jdbcType=VARCHAR},
      REWARD_TYPE = #{rewardType,jdbcType=VARCHAR},
      REWARD_CONTENT = #{rewardContent,jdbcType=VARCHAR},
      EAMOUNT = #{eamount,jdbcType=DECIMAL},
      ORGIN_USERNAME = #{orginUsername,jdbcType=VARCHAR},
      ORGIN_MOBILE_PHONE = #{orginMobilePhone,jdbcType=VARCHAR},
      ORGIN_AUTH_ID = #{orginAuthId,jdbcType=VARCHAR},
      CREATED_TIME = #{createdTime,jdbcType=VARCHAR},
      CREATED_USER = #{createdUser,jdbcType=VARCHAR},
      UPDATED_TIME = #{updatedTime,jdbcType=VARCHAR},
      UPDATED_USER = #{updatedUser,jdbcType=VARCHAR}
    where USER_SHARE_REWARD_INFO_SEQ = #{userShareRewardInfoSeq,jdbcType=BIGINT}
  </update>
  
  <!-- 邀请好友，奖励次数 -->
  <select id="shareRewardTimes" parameterType="java.lang.String" resultType="int">
  		select 
  			count(1) 
  		from 
			siac.USER_SHARE_REWARD_INFO 
 		where  
 			REWARD_TYPE='好友邀请奖励' 
 			AND auth_id = #{authId}
  </select>
</mapper>