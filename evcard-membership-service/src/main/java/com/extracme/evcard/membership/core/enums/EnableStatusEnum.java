package com.extracme.evcard.membership.core.enums;

public enum EnableStatusEnum {
    /**
     * 启用状态:1= 启用 2=禁用
     */

    ENABLE(1, "启用"),

    DISABLE(2, "禁用");

    EnableStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    /** 异常描述信息 */
    private Integer code;

    /** 描述 */
    private String msg;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
