<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.credit.dao.MmpUserAnalysisMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.credit.model.MmpUserAnalysis" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 11 10:49:35 CST 2017.
    -->
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="year" property="year" jdbcType="INTEGER" />
    <result column="month" property="month" jdbcType="INTEGER" />
    <result column="org_id" property="orgId" jdbcType="VARCHAR" />
    <result column="user_level" property="userLevel" jdbcType="INTEGER" />
    <result column="member_cnt" property="memberCnt" jdbcType="VARCHAR" />
    <result column="member_rate" property="memberRate" jdbcType="VARCHAR" />
    <result column="order_cnt" property="orderCnt" jdbcType="VARCHAR" />
    <result column="order_rate" property="orderRate" jdbcType="VARCHAR" />
    <result column="real_pay" property="realPay" jdbcType="VARCHAR" />
    <result column="real_pay_rate" property="realPayRate" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 11 10:49:35 CST 2017.
    -->
    id, year, month, org_id, user_level, member_cnt, member_rate, order_cnt, order_rate, 
    real_pay, real_pay_rate, remark, create_time, create_oper_id, create_oper_name, update_time, 
    update_oper_id, update_oper_name
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 11 10:49:35 CST 2017.
    -->
    select 
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_user_analysis
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 11 10:49:35 CST 2017.
    -->
    delete from ${issSchema}.mmp_user_analysis
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.membership.credit.model.MmpUserAnalysis" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 11 10:49:35 CST 2017.
    -->
    insert into ${issSchema}.mmp_user_analysis (id, year, month,
      org_id, user_level, member_cnt, 
      member_rate, order_cnt, order_rate, 
      real_pay, real_pay_rate, remark, 
      create_time, create_oper_id, create_oper_name, 
      update_time, update_oper_id, update_oper_name
      )
    values (#{id,jdbcType=BIGINT}, #{year,jdbcType=INTEGER}, #{month,jdbcType=INTEGER}, 
      #{orgId,jdbcType=VARCHAR}, #{userLevel,jdbcType=INTEGER}, #{memberCnt,jdbcType=VARCHAR}, 
      #{memberRate,jdbcType=VARCHAR}, #{orderCnt,jdbcType=VARCHAR}, #{orderRate,jdbcType=VARCHAR}, 
      #{realPay,jdbcType=VARCHAR}, #{realPayRate,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, #{createOperName,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, #{updateOperName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.credit.model.MmpUserAnalysis"
      useGeneratedKeys="true" keyProperty="id">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 11 10:49:35 CST 2017.
    -->
    insert into ${issSchema}.mmp_user_analysis
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="year != null" >
        year,
      </if>
      <if test="month != null" >
        month,
      </if>
      <if test="orgId != null" >
        org_id,
      </if>
      <if test="userLevel != null" >
        user_level,
      </if>
      <if test="memberCnt != null" >
        member_cnt,
      </if>
      <if test="memberRate != null" >
        member_rate,
      </if>
      <if test="orderCnt != null" >
        order_cnt,
      </if>
      <if test="orderRate != null" >
        order_rate,
      </if>
      <if test="realPay != null" >
        real_pay,
      </if>
      <if test="realPayRate != null" >
        real_pay_rate,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateOperId != null" >
        update_oper_id,
      </if>
      <if test="updateOperName != null" >
        update_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="year != null" >
        #{year,jdbcType=INTEGER},
      </if>
      <if test="month != null" >
        #{month,jdbcType=INTEGER},
      </if>
      <if test="orgId != null" >
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="userLevel != null" >
        #{userLevel,jdbcType=INTEGER},
      </if>
      <if test="memberCnt != null" >
        #{memberCnt,jdbcType=VARCHAR},
      </if>
      <if test="memberRate != null" >
        #{memberRate,jdbcType=VARCHAR},
      </if>
      <if test="orderCnt != null" >
        #{orderCnt,jdbcType=VARCHAR},
      </if>
      <if test="orderRate != null" >
        #{orderRate,jdbcType=VARCHAR},
      </if>
      <if test="realPay != null" >
        #{realPay,jdbcType=VARCHAR},
      </if>
      <if test="realPayRate != null" >
        #{realPayRate,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        #{updateOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.credit.model.MmpUserAnalysis" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 11 10:49:35 CST 2017.
    -->
    update ${issSchema}.mmp_user_analysis
    <set >
      <if test="year != null" >
        year = #{year,jdbcType=INTEGER},
      </if>
      <if test="month != null" >
        month = #{month,jdbcType=INTEGER},
      </if>
      <if test="orgId != null" >
        org_id = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="userLevel != null" >
        user_level = #{userLevel,jdbcType=INTEGER},
      </if>
      <if test="memberCnt != null" >
        member_cnt = #{memberCnt,jdbcType=VARCHAR},
      </if>
      <if test="memberRate != null" >
        member_rate = #{memberRate,jdbcType=VARCHAR},
      </if>
      <if test="orderCnt != null" >
        order_cnt = #{orderCnt,jdbcType=VARCHAR},
      </if>
      <if test="orderRate != null" >
        order_rate = #{orderRate,jdbcType=VARCHAR},
      </if>
      <if test="realPay != null" >
        real_pay = #{realPay,jdbcType=VARCHAR},
      </if>
      <if test="realPayRate != null" >
        real_pay_rate = #{realPayRate,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.credit.model.MmpUserAnalysis" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Dec 11 10:49:35 CST 2017.
    -->
    update ${issSchema}.mmp_user_analysis
    set year = #{year,jdbcType=INTEGER},
      month = #{month,jdbcType=INTEGER},
      org_id = #{orgId,jdbcType=VARCHAR},
      user_level = #{userLevel,jdbcType=INTEGER},
      member_cnt = #{memberCnt,jdbcType=VARCHAR},
      member_rate = #{memberRate,jdbcType=VARCHAR},
      order_cnt = #{orderCnt,jdbcType=VARCHAR},
      order_rate = #{orderRate,jdbcType=VARCHAR},
      real_pay = #{realPay,jdbcType=VARCHAR},
      real_pay_rate = #{realPayRate,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectIsExistUserAnalysis" resultType="java.lang.Integer">
    SELECT COUNT(1)
    FROM ${issSchema}.mmp_user_analysis
    WHERE year = #{paramsDto.year,jdbcType=INTEGER}
     AND month = #{paramsDto.month,jdbcType=INTEGER}
     AND org_id = #{paramsDto.orgId,jdbcType=VARCHAR}
     AND user_level = #{paramsDto.userLevel,jdbcType=INTEGER}
  </select>

  <update id="updateExistUserAnalysis">
     UPDATE ${issSchema}.mmp_user_analysis
     SET  user_level = #{paramsDto.userLevel,jdbcType=INTEGER},
      member_cnt = #{paramsDto.memberCnt,jdbcType=VARCHAR},
      member_rate = #{paramsDto.memberRate,jdbcType=VARCHAR},
      order_cnt = #{paramsDto.orderCnt,jdbcType=VARCHAR},
      order_rate = #{paramsDto.orderRate,jdbcType=VARCHAR},
      real_pay = #{paramsDto.realPay,jdbcType=VARCHAR},
      real_pay_rate = #{paramsDto.realPayRate,jdbcType=VARCHAR}
    WHERE year = #{paramsDto.year,jdbcType=INTEGER}
     AND month = #{paramsDto.month,jdbcType=INTEGER}
     AND org_id = #{paramsDto.orgId,jdbcType=VARCHAR}
     AND user_level = #{paramsDto.userLevel,jdbcType=INTEGER}
  </update>
</mapper>