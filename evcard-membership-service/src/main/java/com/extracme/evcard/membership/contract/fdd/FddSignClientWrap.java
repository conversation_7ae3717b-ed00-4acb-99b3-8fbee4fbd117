package com.extracme.evcard.membership.contract.fdd;

import com.extracme.evcard.membership.common.HttpClientUtils;
import com.fadada.sdk.client.FddClientBase;
import com.fadada.sdk.util.crypt.FddEncryptTool;
import com.fadada.sdk.util.http.HttpsUtil;
import com.fadada.sdk.util.http.SSLClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 法大大客户端扩展
 * <AUTHOR>
 * @Discription
 * @date 2021/2/1
 */
public class FddSignClientWrap extends FddClientBase {

    public FddSignClientWrap(String appId, String secret, String version, String url) {
        super(appId, secret, version, url);
    }

    public String getURLOfBatchDownloadContract() {
        return this.getUrl() + "batch_download_contract" + ".api";
    }

    public InputStream batchDownloadContracts(Map<String, String> contractMap) {
        Map<String, Object> params = new HashMap<>();
        if(CollectionUtils.isEmpty(contractMap.entrySet())) {
            return null;
        }
        String contractIds = StringUtils.join(contractMap.keySet(), ',');

        try {
            String timeStamp = HttpsUtil.getTimeStamp();
            String sha1 = FddEncryptTool.sha1(super.getAppId() + FddEncryptTool.md5Digest(timeStamp)
                    + FddEncryptTool.sha1(super.getSecret() + contractIds));
            String msgDigest = new String(FddEncryptTool.Base64Encode(sha1.getBytes()));
            params.put("contract_ids", contractIds);
            params.put("app_id", super.getAppId());
            params.put("timestamp", timeStamp);
            params.put("v", super.getVersion());
            params.put("msg_digest", msgDigest);
        } catch (Exception var12) {
            var12.printStackTrace();
            throw new RuntimeException(var12);
        }

        return doGetZip(this.getURLOfBatchDownloadContract(), params, 2000, 50000);
    }

    public static InputStream doGetZip(String url, Map<String, Object> params, int connect_time, int timeout) {
        HttpClient httpClient = null;
        HttpGet httpGet = null;

        //try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
        try {
            httpClient = new SSLClient();
            RequestConfig timeoutConfig = RequestConfig.custom()
                    .setConnectTimeout(connect_time).setConnectionRequestTimeout(connect_time)
                    .setSocketTimeout(timeout).build();
            httpGet = HttpClientUtils.buildhttpGet(url, null, params);
            httpGet.setConfig(timeoutConfig);

            HttpResponse response = httpClient.execute(httpGet);
            if (response == null) {
                throw new RuntimeException("HttpResponse is null.");
            }
            if (response.getStatusLine().getStatusCode() != 200) {
                throw new RuntimeException("connect fail. http_status:" + response.getStatusLine().getStatusCode());
            }

            HttpEntity entity = response.getEntity();
            if (null == entity) {
                throw new RuntimeException("HttpEntity is null.");
            }

            InputStream input = entity.getContent();
//            File dest = new File("E:/aaa.zip");
//            OutputStream output = new FileOutputStream(dest);
//            int len = 0;
//            byte[] ch = new byte[1024];
//            while ((len = input.read(ch)) != -1) {
//                output.write(ch, 0, len);
//            }
            return input;

        } catch (Exception var17) {
            var17.printStackTrace();
            throw new RuntimeException(var17);
        } finally {
//            try {
//                httpClient.getConnectionManager().shutdown();
//            } catch (Exception var16) {
//                var16.printStackTrace();
//            }

        }
    }
}
