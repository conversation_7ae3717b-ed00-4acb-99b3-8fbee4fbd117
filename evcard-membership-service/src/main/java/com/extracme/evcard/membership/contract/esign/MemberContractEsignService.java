package com.extracme.evcard.membership.contract.esign;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.contract.service.*;
import com.extracme.evcard.membership.core.dto.ContractVersionDto;
import com.extracme.evcard.membership.core.dto.UserContractInfo;
import com.extracme.evcard.membership.core.dto.UserContractKeyDto;
import com.extracme.evcard.membership.core.exception.MemberException;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.model.MembershipInfo;
import com.timevale.esign.sdk.tech.bean.AccountProfile;
import com.timevale.esign.sdk.tech.bean.result.FileDigestSignResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * e签宝合同签署服务提供者
 *
 * <AUTHOR>
 * @Discription
 * @date 2021/2/10
 */
@Slf4j
@Component("esignContractService")
public class MemberContractEsignService implements IMemberContractSignProvider {
    @Resource
    private MembershipInfoMapper membershipInfoMapper;

   /* @Resource
    EsignRestClient esignRestClient;*/

    private ContractSupplier supplier = ContractSupplier.ESIGN;

    @Value("${esign.posX}")
    private Integer posX;
    @Value("${esign.posY}")
    private Integer posY;

    @Override
   /* public UpdateTemplateResponse uploadTemplate(String templateKey, String docUrl) {
        UpdateTemplateResponse response = new UpdateTemplateResponse();
        response.setSupplier(supplier);
        String fileName = templateKey + ".pdf";
        BaseResult<UploadTemplateResponse> result = esignRestClient.uploadTemplate(docUrl, fileName, true);
        if(result != null && result.getData() != null) {
            UploadTemplateResponse data = result.getData();
            if(StringUtils.isNotBlank(data.getFileId())) {
                response.setTemplateId(data.getFileId());
                return response;
            }
        }
        //log.error("上传模板失败，");
        response.setCode(-1);
        response.setMessage("上传模板失败");
        return response;
    }*/
    /**
     * e签宝修改SDK，不在需要上传签署文件，所以这里不做任何实现对接
     */
    public UpdateTemplateResponse uploadTemplate(String templateKey, String docUrl) {
        log.info("e签宝修改成SDK，上传文件不做对接，{}，{}", templateKey, docUrl);
        UpdateTemplateResponse response = new UpdateTemplateResponse();
        response.setSupplier(supplier);
        response.setTemplateId("");
        response.setCode(0);
        return response;
    }

    private static final String ACCOUNT_EXISIT = "********";
  /*  @Override
    public ContractSignOutput autoSignContract(ContractSignInput input) throws MemberException {
        log.info("e签宝esign：合同自动签署开始，input={}", JSON.toJSONString(input));
        String authId = input.getAuthId();
        String accountId = StringUtils.EMPTY;

        ContractSignOutput output = new ContractSignOutput();
        List<ContractSignResult> results = new ArrayList<>();
        List<ContractVersionDto> contractVersionDtoList = input.getContractVersionDtoList();
        if (CollectionUtils.isEmpty(contractVersionDtoList)) {
            return output;
        }
        *//**
     * 件一步签署(签署&归档)
     *//*
        for (ContractVersionDto p : contractVersionDtoList) {
            //模板文件id
            String templateId = p.getTemplateId();
            String fileId = p.getTemplateFileId();
            //合同标题
            String docTitle = p.getDocTitle();
            if (StringUtils.isBlank(authId) || StringUtils.isBlank(fileId) || StringUtils.isBlank(docTitle)) {
                log.error("e签宝esign：合同自动签署失败，原因authId/fileId/docTitle不完整，input={}", JSON.toJSONString(input));
                throw new MemberException(-1, "请检查参数完整性");
            }
            //检查当前版本是否已经签署过
            if (membershipInfoMapper.countContractByTemplateId(authId, templateId) == 0) {
                try {
                    if(StringUtils.isBlank(accountId)) {
                        *//**
     * 1. 若尚无账户，则先获取账户信息
     *//*
                        accountId = getMemberAccount(input.getPkId(), input.getName(), input.getCertNo());
                        if(StringUtils.isBlank(accountId)) {
                            log.error("e签宝esign：合同自动签署失败，原因获取个人签署账号失败，input={}", JSON.toJSONString(input));
                            throw new MemberException(-1, "获取个人签署账号失败");
                        }
                    }
                    */

    /**
     * 2. 发起一步签署
     *//*
                    log.warn("e签宝esign：开始签署本合同，authId={}, templatId={}，customerId={}", authId, templateId, accountId);
                    //调用一步签署接口并自动归档
                    BaseResult<CreateFlowOneStepResponse> psnSignResult = esignRestClient.personAutoOneStepSign(accountId, fileId, docTitle, posX, posY);
                    if(psnSignResult.getData() == null || StringUtils.isBlank(psnSignResult.getData().getFlowId())) {
                        log.error("e签宝esign：发起一步签署失败，authId={}, accountId={}, fileId={}, result={}",
                                authId, accountId, fileId, JSON.toJSONString(psnSignResult));
                        throw new MemberException(-1, "签署协议失败");
                    }

                    String flowId = psnSignResult.getData().getFlowId();
//                    BaseResult<GetSignFileResponse> signFileResult = esignRestClient.getFileResponse(flowId);
//                    if(signFileResult.getData() == null || CollectionUtils.isEmpty(signFileResult.getData().getDocs())) {
//                        log.error("esign：未能成功获取签署好的文件，authId={}, flowId={}, result={}",
//                                authId, flowId, JSON.toJSONString(signFileResult));
//                        throw new MemberException(-1, "读取签署结果失败");
//                    }
//                    //读取文件路径
//                    GetSignFileResponse signFile = signFileResult.getData();
//                    if(CollectionUtils.isEmpty(signFile.getDocs())){
//                        log.error("esign：未能成功获取签署好的文件，authId={}, flowId={}, result={}",
//                                authId, flowId, JSON.toJSONString(signFileResult));
//                        throw new MemberException(-1, "获取签署文件路径失败");
//                    }
//                    Doc doc = signFile.getDocs().get(0);
                    ContractSignResult signResult = new ContractSignResult();
                    signResult.setCustomerId(accountId);
                    signResult.setTemplateId(templateId);
                    signResult.setVersionId(p.getVersionId());
                    signResult.setViewUrl(StringUtils.EMPTY);
                    signResult.setDownloadUrl(StringUtils.EMPTY);
                    signResult.setSigned(1);
                    signResult.setContractId(StringUtils.EMPTY);
                    signResult.setTransactionId(flowId);
                    results.add(signResult);
                    log.warn("e签宝esign：authid=" + authId + " 签署合同成功");
                } catch (Exception e) {
                    log.error("e签宝esign：自动签署合同失败，authId=" + authId, e);
                    throw new MemberException(-1, "签署合同失败");
                }
            }else {
                log.warn("e签宝esign：此版本合同无需再次签署，authId={}, templatId={}，customerId={}", authId, templateId, accountId);
            }
        }
        output.setSignResults(results);
        return output;
    }*/
    @Override
    public ContractSignOutput autoSignContract(ContractSignInput input) throws MemberException {
        log.info("e签宝esign：合同自动签署开始，input={}", JSON.toJSONString(input));
        String authId = input.getAuthId();
        String accountId = StringUtils.EMPTY;

        ContractSignOutput output = new ContractSignOutput();
        List<ContractSignResult> results = new ArrayList<>();
        List<ContractVersionDto> contractVersionDtoList = input.getContractVersionDtoList();
        if (CollectionUtils.isEmpty(contractVersionDtoList)) {
            return output;
        }
        /**
         * 件一步签署(签署&归档)
         */
        for (ContractVersionDto p : contractVersionDtoList) {
            //模板文件id
            String templateId = p.getTemplateId();
            String fileId = p.getTemplateFileId();
            //合同标题
            String docTitle = p.getDocTitle();
            if (StringUtils.isBlank(authId) || StringUtils.isBlank(docTitle)) {
                log.error("e签宝esign：合同自动签署失败，原因authId/docTitle不完整，input={}", JSON.toJSONString(input));
                throw new MemberException(-1, "请检查参数完整性");
            }
            //检查当前版本是否已经签署过
            if (membershipInfoMapper.countContractByTemplateId(authId, templateId) == 0) {
                try {
                    if (StringUtils.isBlank(accountId)) {
                        // 1. 若尚无账户，则先获取或者创建账户信息
                        accountId = getMemberAccount(input.getPkId(), input.getName(), input.getCertNo());
                    }
                    String personSeal = EsignService.addPersonSeal(accountId);
                    if (StringUtils.isBlank(p.getOssPdfUrl())) {
                        // 查询协议数据库
                        Integer provisionType = (StringUtils.startsWith(p.getTemplateId(), "SZ")) ? 1 : 2;
                        p.setOssPdfUrl(ComUtil.getFileFullPath(membershipInfoMapper.getOssUrlByVersion(p.getVersionId(), provisionType)));
                    }

                    log.warn("e签宝esign：开始签署本合同，authId={}, templatId={}，customerId={}", authId, templateId, accountId);
                    FileDigestSignResult fileDigestSignResult = EsignService.signPdfByPerson(p.getOssPdfUrl(), accountId, personSeal,
                            docTitle + ".pdf", posX, posY);
                    ContractSignResult signResult = new ContractSignResult();
                    signResult.setCustomerId(accountId);
                    signResult.setTemplateId(templateId);
                    signResult.setVersionId(p.getVersionId());
                    signResult.setViewUrl(StringUtils.EMPTY);
                    signResult.setDownloadUrl(StringUtils.EMPTY);
                    signResult.setSigned(1);
                    signResult.setContractId(StringUtils.EMPTY);
                    signResult.setTransactionId(fileDigestSignResult.getSignServiceId());//老的对接是保存的flowid
                    signResult.setStream(fileDigestSignResult.getStream());//老的对接是保存的flowid
                    results.add(signResult);
                    log.warn("e签宝esign：authid=" + authId + " 签署合同成功");
                } catch (Exception e) {
                    log.error("e签宝esign：自动签署合同失败，authId=" + authId, e);
                    throw new MemberException(-1, "签署合同失败");
                }
            } else {
                log.warn("e签宝esign：此版本合同无需再次签署，authId={}, templatId={}，customerId={}", authId, templateId, accountId);
            }
        }
        output.setSignResults(results);
        return output;
    }

    @Value("${env}")
    private String env;

    public String getMemberAccount(Long pkId, String name, String driverCode) {
        /**
         * TODO 暂不持久化
         */
        // String thirdId = env + "_" + pkId;
        String accountId = StringUtils.EMPTY;
        //获取签署账号id
        MembershipInfo identityByPkId = membershipInfoMapper.getIdentityByPkId(pkId);
        if (identityByPkId == null || StringUtils.isBlank(identityByPkId.getPassportNo())) {
            log.error("未查到对应的账户证件信息，无法签署协议，pkid={}", pkId);
            throw new MemberException(-1, "查询证件失败");
        }
        AccountProfile personAccount = EsignService.getPersonAccount(identityByPkId.getPassportNo(), identityByPkId.getIdType());
        if (personAccount != null && StringUtils.isNotBlank(personAccount.getAccountUid())) {
            accountId = personAccount.getAccountUid();
            //名称已经变更，则需更新账号姓名
            if (!StringUtils.equals(personAccount.getName(), name)) {
                log.info("e签宝esign：修改个人签署账号：, pkId={}, {}->{}", pkId, personAccount.getName(), name);
                EsignService.updatePersonAccount(personAccount.getAccountUid(), name);
            }
        }
        //尚未绑定账号则新创建账号，并绑定
        if (StringUtils.isBlank(accountId)) {
            // 创建账户
            accountId = EsignService.addPersonAccount(identityByPkId.getPassportNo(),
                    name, identityByPkId.getIdType());
        }
        return accountId;
    }

    /* public String getMemberAccount(Long pkId, String name, String driverCode){
     */

    /**
     * TODO 暂不持久化
     *//*
        String thirdId = env + "_" + pkId;
        String accountId = StringUtils.EMPTY;
        //获取签署账号id
        BaseResult<QueryAccountResponse> result = esignRestClient.queryAccount(thirdId);
        if(result != null && result.getData() != null) {
            QueryAccountResponse account = result.getData();
            if(StringUtils.isNotBlank(account.getAccountId())) {
                //名称已经变更，则需更新账号姓名
                if(!StringUtils.equals(account.getName(), name)) {
                    BaseResult<QueryAccountResponse> updateNameResp = esignRestClient.updateAccountName(account.getAccountId(), name);
                    if(updateNameResp.getData() == null || !updateNameResp.getCode().equals("0")) {
                        log.error("e签宝esign：修改个人签署账号：修改姓名失败, pkId={}, {}->{}", pkId, account.getName(), name);
                        throw new MemberException(-1, "更新个人签署账号失败");
                    }
                }
                accountId = account.getAccountId();
            }
        }
        //尚未绑定账号则新创建账号，并绑定
        if(StringUtils.isBlank(accountId)) {
            CreateAccountRequest request = new CreateAccountRequest();
            request.setThirdPartyUserId(thirdId);
            request.setIdType("CRED_PSN_UNKNOWN");
            request.setIdNumber(driverCode);
            request.setName(name);
            BaseResult<CreateAccountResponse> createResult = esignRestClient.CreateAccount(request);
            CreateAccountResponse accountCreate = createResult.getData();
            if(accountCreate == null || StringUtils.isBlank(accountCreate.getAccountId())) {
                log.error("e签宝esign：授予个人签署账号静默签署权限失败, pkId={}", pkId);
                throw new MemberException(-1, "创建个人签署账号失败");
            }
            accountId = accountCreate.getAccountId();
            //暂时不保存账号id 保存customId
            //membershipInfoMapper.saveCustomerId(input.getAuthId(), customerId);
        }
        if(StringUtils.isBlank(accountId)) {
            throw new MemberException(-1, "获取个人签署账号失败");
        }
        //授予静默签署权限
        BaseResult<Boolean> authResponse = esignRestClient.setSilentSignAuth(accountId, null);
        if(authResponse.getData() == null || !authResponse.getData()) {
            log.error("e签宝esign：授予个人签署账号静默签署权限失败, pkId={}", pkId);
            throw new MemberException(-1, "授予个人签署账号静默签署权限失败");
        }
        return accountId;
    }*/

   /* @Override
    public UserContractInfo getContractUrl(UserContractInfo contract) {
        if(StringUtils.isNotBlank(contract.getArchiveUrl())) {
            return contract;
        }
        String flowId = contract.getTransactionId();
        if(StringUtils.isBlank(flowId)) {
            return contract;
        }
        BaseResult<GetSignFileResponse> signFileResult = esignRestClient.getFileResponse(flowId);
        if(signFileResult.getData() == null || CollectionUtils.isEmpty(signFileResult.getData().getDocs())) {
            log.error("e签宝esign：未能成功获取签署好的文件，authId={}, flowId={}, result={}",
                    contract.getAuthId(), flowId, JSON.toJSONString(signFileResult));
            return contract;
        }
        //读取文件路径
        GetSignFileResponse signFile = signFileResult.getData();
        Doc doc = signFile.getDocs().get(0);
        contract.setContractId(doc.getFileId());
        contract.setArchiveUrl(doc.getFileUrl());
        contract.setViewpdfUrl(doc.getFileUrl());
        contract.setDownloadUrl(doc.getFileUrl());
        return contract;
    }*/
    @Override
    public UserContractInfo getContractUrl(UserContractInfo contract) {
        if (StringUtils.isNotBlank(contract.getArchiveUrl())) {
            return contract;
        }
        String flowId = contract.getTransactionId();
        if (StringUtils.isBlank(flowId)) {
            return contract;
        }
       /* BaseResult<GetSignFileResponse> signFileResult = esignRestClient.getFileResponse(flowId);
        if(signFileResult.getData() == null || CollectionUtils.isEmpty(signFileResult.getData().getDocs())) {
            log.error("e签宝esign：未能成功获取签署好的文件，authId={}, flowId={}, result={}",
                    contract.getAuthId(), flowId, JSON.toJSONString(signFileResult));
            return contract;
        }
        //读取文件路径
        GetSignFileResponse signFile = signFileResult.getData();
        Doc doc = signFile.getDocs().get(0);
        contract.setContractId(doc.getFileId());
        contract.setArchiveUrl(doc.getFileUrl());
        contract.setViewpdfUrl(doc.getFileUrl());
        contract.setDownloadUrl(doc.getFileUrl());*/

        // contract.setArchiveUrl(""); // todo
        return contract;
    }

    @Override
    public Map<String, String> batchDownloadContract(Map<String, UserContractKeyDto> contractMap) {
        return null;
    }
}
