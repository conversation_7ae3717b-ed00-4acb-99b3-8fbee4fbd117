<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.core.dao.AppDeviceInfoMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.AppDeviceInfo" >
    <id column="pk_id" property="pkId" jdbcType="BIGINT" />
    <result column="MOBILE_PHONE" property="mobilePhone" jdbcType="VARCHAR" />
    <result column="IMEI_NO" property="imeiNo" jdbcType="VARCHAR" />
    <result column="LOGIN_COUNT" property="loginCount" jdbcType="DECIMAL" />
    <result column="UPDATED_TIME" property="updatedTime" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    pk_id, MOBILE_PHONE, IMEI_NO, LOGIN_COUNT, UPDATED_TIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from ${siacSchema}.app_device_info
    where pk_id = #{pkId,jdbcType=BIGINT}
  </select>

  <insert id="insert" parameterType="com.extracme.evcard.membership.core.model.AppDeviceInfo" >
    insert into ${siacSchema}.app_device_info ( MOBILE_PHONE, IMEI_NO, UPDATED_TIME)
    values (#{mobilePhone,jdbcType=VARCHAR}, #{imeiNo,jdbcType=VARCHAR}, date_format(sysdate(), '%Y%m%d%H%i%s'))
  </insert>
  
  <!--  -->
  <update id="updateLoginCount" >
    update ${siacSchema}.APP_DEVICE_INFO
    set UPDATED_TIME = date_format(sysdate(), '%Y%m%d%H%i%s'), LOGIN_COUNT = LOGIN_COUNT + 1
    WHERE MOBILE_PHONE = #{phone} AND IMEI_NO = #{imei}
  </update>

  <select id="countInfo" resultType="int">
    SELECT count(*) as num FROM ${siacSchema}.APP_DEVICE_INFO
    where MOBILE_PHONE = #{phone} AND IMEI_NO = #{imei}
  </select>

</mapper>