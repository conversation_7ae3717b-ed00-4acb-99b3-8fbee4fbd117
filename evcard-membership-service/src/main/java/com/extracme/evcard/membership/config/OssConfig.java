package com.extracme.evcard.membership.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @Discription
 * @date 2021/12/10
 * @remark 若采用ConfigurationProperties则需要做apollo的动态刷新
 */

@Configuration
@Data
public class OssConfig {
    @Value("${env}")
    private String ENV;

    @Value("${oss.bucket}")
    private String OSS_BUCKET;

    @Value("${oss.bucket.sensitive}")
    private String OSS_SENSITIVE_BUCKET;

    @Value("${oss.endPoint}")
    private String OSS_ENDPOINT;

    @Value("${oss.roleArn}")
    private String ROLE_ARN;

    @Value("${oss.roleSessionName}")
    private String ROLE_SESSION_NAME;

    @Value("${oss.accessKey}")
    private String ALI_ACCESS_ID;

    @Value("${oss.secretKey}")
    private String ALI_ACCESS_KEY;
}
