package com.extracme.evcard.membership.core.manager;

import com.extracme.evcard.membership.core.dao.MembershipOcrCallLogMapper;
import com.extracme.evcard.membership.core.enums.OcrChannelEnums;
import com.extracme.evcard.membership.core.enums.OcrTypeEnums;
import com.extracme.evcard.membership.core.model.MembershipOcrCallLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/8/23
 */
@Slf4j
@Component
public class MembershipOcrCallLogManager {

    @Resource
    private MembershipOcrCallLogMapper membershipOcrCallLogMapper;

    public int addMembershipOcrCallLog(String mid, OcrChannelEnums channel, OcrTypeEnums ocrType,
                                       String reqUrl, String reqParams, String respParams) {
        try {
            MembershipOcrCallLog record = new MembershipOcrCallLog();
            record.setMid(mid);
            record.setChannel(channel.getChannel());
            record.setOcrType(ocrType.getOcrType());
            record.setReqUrl(reqUrl);
            record.setReqParams(StringUtils.abbreviate(reqParams, 1000));
            record.setRespParams(StringUtils.abbreviate(respParams, 2000));
            return membershipOcrCallLogMapper.insertSelective(record);
        } catch (Exception e) {
            log.error("新增membership_ocr_call_log表记录异常！", e);
            return 0;
        }
    }
}
