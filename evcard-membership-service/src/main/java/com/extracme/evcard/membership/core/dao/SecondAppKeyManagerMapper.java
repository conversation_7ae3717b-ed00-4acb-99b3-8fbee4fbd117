package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.dto.SecondChannelListInfoDTO;
import com.extracme.evcard.membership.core.dto.agency.SecondAppKeyDetailInfoDTO;
import com.extracme.evcard.membership.core.model.SecondAppKeyManager;
import com.extracme.evcard.membership.core.model.SecondAppKeyManagerExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SecondAppKeyManagerMapper {
    int countByExample(SecondAppKeyManagerExample example);

    int deleteByPrimaryKey(Long id);

    int insert(SecondAppKeyManager record);

    int insertSelective(SecondAppKeyManager record);

    List<SecondAppKeyManager> selectByExample(SecondAppKeyManagerExample example);

    SecondAppKeyManager selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SecondAppKeyManager record, @Param("example") SecondAppKeyManagerExample example);

    int updateByExample(@Param("record") SecondAppKeyManager record, @Param("example") SecondAppKeyManagerExample example);

    int updateByPrimaryKeySelective(SecondAppKeyManager record);

    int updateByPrimaryKey(SecondAppKeyManager record);


    // 渠道信息一览
    List<SecondChannelListInfoDTO> getChannelManagement(@Param("secondAppKeyName") String secondAppKeyName, @Param("secondAppKey") String secondAppKey, @Param("firstAppKey") String firstAppKey, @Param("createdUser") String createdUser,
                                                        @Param("orgId") String orgId, @Param("startTime") String startTime, @Param("endTime") String endTime, @Param("platformId") Long platformId, @Param("status") Integer status);

    // 渠道信息总件数
    int getChannelManagementNum(@Param("secondAppKeyName") String secondAppKeyName, @Param("secondAppKey") String secondAppKey, @Param("firstAppKey") String firstAppKey, @Param("createdUser") String createdUser, @Param("orgId") String orgId,
                                @Param("startTime") String startTime, @Param("endTime") String endTime, @Param("platformId") Long platformId, @Param("status") Integer status);

    // 检验渠道名称是否存在
    int checkPlatNameExist(@Param("secondAppKeyName") String secondAppKeyName, @Param("secondAppKey") String secondAppKey);

    // 检验APP_KEY是否存在
    int checkAppKeyExist(@Param("secondAppKey") String secondAppKey);

    // 用secondAppKey 查询记录
    SecondAppKeyManager selectBySecondAppKey(@Param("secondAppKey") String secondAppKey);

    int disableAppKey(@Param("secondAppKey") String secondAppKey);

    int enableAppKey(@Param("secondAppKey") String secondAppKey);

    List<SecondAppKeyManager> selectListByFirstAppKey(@Param("firstAppKey") String firstAppKey);

    // 用secondAppKey 查询记录
    SecondAppKeyDetailInfoDTO selectBySecondAppKeyDetailInfo(@Param("secondAppKey") String secondAppKey);
}