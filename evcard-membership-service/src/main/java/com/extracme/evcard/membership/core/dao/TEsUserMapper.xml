<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.core.dao.TEsUserMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.TEsUser" >
    <id column="NAME" property="name" jdbcType="VARCHAR" />
    <result column="DISPLAY_NAME" property="displayName" jdbcType="VARCHAR" />
    <result column="CODED_PASSWORD" property="codedPassword" jdbcType="VARCHAR" />
    <result column="DESC_INFO" property="descInfo" jdbcType="VARCHAR" />
    <result column="LAST_NAME" property="lastName" jdbcType="VARCHAR" />
    <result column="FIRST_NAME" property="firstName" jdbcType="VARCHAR" />
    <result column="MIDDLE_NAME" property="middleName" jdbcType="VARCHAR" />
    <result column="NICK_NAME" property="nickName" jdbcType="VARCHAR" />
    <result column="TITLE" property="title" jdbcType="VARCHAR" />
    <result column="INITIALS" property="initials" jdbcType="VARCHAR" />
    <result column="FULL_NAME" property="fullName" jdbcType="VARCHAR" />
    <result column="PROF" property="prof" jdbcType="VARCHAR" />
    <result column="POSITION" property="position" jdbcType="VARCHAR" />
    <result column="DEPT" property="dept" jdbcType="VARCHAR" />
    <result column="ACCOUNT" property="account" jdbcType="VARCHAR" />
    <result column="ID_NUMBER" property="idNumber" jdbcType="VARCHAR" />
    <result column="WORK_NUMBER" property="workNumber" jdbcType="VARCHAR" />
    <result column="GENDER" property="gender" jdbcType="VARCHAR" />
    <result column="BIRTHDAY" property="birthday" jdbcType="VARCHAR" />
    <result column="COUNTRY" property="country" jdbcType="VARCHAR" />
    <result column="PROVINCE" property="province" jdbcType="VARCHAR" />
    <result column="CITY" property="city" jdbcType="VARCHAR" />
    <result column="STREET" property="street" jdbcType="VARCHAR" />
    <result column="ADDR" property="addr" jdbcType="VARCHAR" />
    <result column="HOME_ADDR" property="homeAddr" jdbcType="VARCHAR" />
    <result column="OFFICE_ADDR" property="officeAddr" jdbcType="VARCHAR" />
    <result column="BUSINESS_ADDR" property="businessAddr" jdbcType="VARCHAR" />
    <result column="OTHER_ADDR" property="otherAddr" jdbcType="VARCHAR" />
    <result column="POSTAL_CODE" property="postalCode" jdbcType="VARCHAR" />
    <result column="POST_BOX" property="postBox" jdbcType="VARCHAR" />
    <result column="WEB_SITE" property="webSite" jdbcType="VARCHAR" />
    <result column="EMAIL" property="email" jdbcType="VARCHAR" />
    <result column="IM" property="im" jdbcType="VARCHAR" />
    <result column="DOMAIN" property="domain" jdbcType="VARCHAR" />
    <result column="MAIN_PHONE" property="mainPhone" jdbcType="VARCHAR" />
    <result column="HOME_PHONE" property="homePhone" jdbcType="VARCHAR" />
    <result column="MOBILE_PHONE" property="mobilePhone" jdbcType="VARCHAR" />
    <result column="OFFICE_PHONE" property="officePhone" jdbcType="VARCHAR" />
    <result column="BUSINESS_PHONE" property="businessPhone" jdbcType="VARCHAR" />
    <result column="OTHER_PHONE" property="otherPhone" jdbcType="VARCHAR" />
    <result column="FAX" property="fax" jdbcType="VARCHAR" />
    <result column="USER_TYPE" property="userType" jdbcType="VARCHAR" />
    <result column="USER_CODE" property="userCode" jdbcType="VARCHAR" />
    <result column="ORG_CODE" property="orgCode" jdbcType="VARCHAR" />
    <result column="CREATE_DATE" property="createDate" jdbcType="VARCHAR" />
    <result column="EXPIRE_DATE" property="expireDate" jdbcType="VARCHAR" />
    <result column="PWD_EXPIRE_DATE" property="pwdExpireDate" jdbcType="VARCHAR" />
    <result column="PROPERTY_UPDATE_DATE" property="propertyUpdateDate" jdbcType="VARCHAR" />
    <result column="PWD_UPDATE_DATE" property="pwdUpdateDate" jdbcType="VARCHAR" />
    <result column="LAST_LOGIN_DATE" property="lastLoginDate" jdbcType="VARCHAR" />
    <result column="ERROR_LOGIN_DATE" property="errorLoginDate" jdbcType="VARCHAR" />
    <result column="VALID" property="valid" jdbcType="DECIMAL" />
    <result column="SORT_INDEX" property="sortIndex" jdbcType="DECIMAL" />
    <result column="ATTRIBUTE" property="attribute" jdbcType="VARCHAR" />
    <result column="PROPERTY" property="property" jdbcType="VARCHAR" />
    <result column="EXT1" property="ext1" jdbcType="VARCHAR" />
    <result column="EXT2" property="ext2" jdbcType="VARCHAR" />
    <result column="EXT3" property="ext3" jdbcType="VARCHAR" />
    <result column="AGENCY_ID" property="agencyId" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    NAME, DISPLAY_NAME, CODED_PASSWORD, DESC_INFO, LAST_NAME, FIRST_NAME, MIDDLE_NAME, 
    NICK_NAME, TITLE, INITIALS, FULL_NAME, PROF, POSITION, DEPT, ACCOUNT, ID_NUMBER, 
    WORK_NUMBER, GENDER, BIRTHDAY, COUNTRY, PROVINCE, CITY, STREET, ADDR, HOME_ADDR, 
    OFFICE_ADDR, BUSINESS_ADDR, OTHER_ADDR, POSTAL_CODE, POST_BOX, WEB_SITE, EMAIL, IM, 
    DOMAIN, MAIN_PHONE, HOME_PHONE, MOBILE_PHONE, OFFICE_PHONE, BUSINESS_PHONE, OTHER_PHONE, 
    FAX, USER_TYPE, USER_CODE, ORG_CODE, CREATE_DATE, EXPIRE_DATE, PWD_EXPIRE_DATE, PROPERTY_UPDATE_DATE, 
    PWD_UPDATE_DATE, LAST_LOGIN_DATE, ERROR_LOGIN_DATE, VALID, SORT_INDEX, ATTRIBUTE, 
    PROPERTY, EXT1, EXT2, EXT3, AGENCY_ID
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from t_es_user
    where NAME = #{name,jdbcType=VARCHAR}
  </select>

  <insert id="insert" parameterType="com.extracme.evcard.membership.core.model.TEsUser" >
    insert into t_es_user (NAME, DISPLAY_NAME, CODED_PASSWORD, 
      DESC_INFO, LAST_NAME, FIRST_NAME, 
      MIDDLE_NAME, NICK_NAME, TITLE, 
      INITIALS, FULL_NAME, PROF, 
      POSITION, DEPT, ACCOUNT, 
      ID_NUMBER, WORK_NUMBER, GENDER, 
      BIRTHDAY, COUNTRY, PROVINCE, 
      CITY, STREET, ADDR, 
      HOME_ADDR, OFFICE_ADDR, BUSINESS_ADDR, 
      OTHER_ADDR, POSTAL_CODE, POST_BOX, 
      WEB_SITE, EMAIL, IM, 
      DOMAIN, MAIN_PHONE, HOME_PHONE, 
      MOBILE_PHONE, OFFICE_PHONE, BUSINESS_PHONE, 
      OTHER_PHONE, FAX, USER_TYPE, 
      USER_CODE, ORG_CODE, CREATE_DATE, 
      EXPIRE_DATE, PWD_EXPIRE_DATE, PROPERTY_UPDATE_DATE, 
      PWD_UPDATE_DATE, LAST_LOGIN_DATE, ERROR_LOGIN_DATE, 
      VALID, SORT_INDEX, ATTRIBUTE, 
      PROPERTY, EXT1, EXT2, 
      EXT3, AGENCY_ID)
    values (#{name,jdbcType=VARCHAR}, #{displayName,jdbcType=VARCHAR}, #{codedPassword,jdbcType=VARCHAR}, 
      #{descInfo,jdbcType=VARCHAR}, #{lastName,jdbcType=VARCHAR}, #{firstName,jdbcType=VARCHAR}, 
      #{middleName,jdbcType=VARCHAR}, #{nickName,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, 
      #{initials,jdbcType=VARCHAR}, #{fullName,jdbcType=VARCHAR}, #{prof,jdbcType=VARCHAR}, 
      #{position,jdbcType=VARCHAR}, #{dept,jdbcType=VARCHAR}, #{account,jdbcType=VARCHAR}, 
      #{idNumber,jdbcType=VARCHAR}, #{workNumber,jdbcType=VARCHAR}, #{gender,jdbcType=VARCHAR}, 
      #{birthday,jdbcType=VARCHAR}, #{country,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, 
      #{city,jdbcType=VARCHAR}, #{street,jdbcType=VARCHAR}, #{addr,jdbcType=VARCHAR}, 
      #{homeAddr,jdbcType=VARCHAR}, #{officeAddr,jdbcType=VARCHAR}, #{businessAddr,jdbcType=VARCHAR}, 
      #{otherAddr,jdbcType=VARCHAR}, #{postalCode,jdbcType=VARCHAR}, #{postBox,jdbcType=VARCHAR}, 
      #{webSite,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, #{im,jdbcType=VARCHAR}, 
      #{domain,jdbcType=VARCHAR}, #{mainPhone,jdbcType=VARCHAR}, #{homePhone,jdbcType=VARCHAR}, 
      #{mobilePhone,jdbcType=VARCHAR}, #{officePhone,jdbcType=VARCHAR}, #{businessPhone,jdbcType=VARCHAR}, 
      #{otherPhone,jdbcType=VARCHAR}, #{fax,jdbcType=VARCHAR}, #{userType,jdbcType=VARCHAR}, 
      #{userCode,jdbcType=VARCHAR}, #{orgCode,jdbcType=VARCHAR}, #{createDate,jdbcType=VARCHAR}, 
      #{expireDate,jdbcType=VARCHAR}, #{pwdExpireDate,jdbcType=VARCHAR}, #{propertyUpdateDate,jdbcType=VARCHAR}, 
      #{pwdUpdateDate,jdbcType=VARCHAR}, #{lastLoginDate,jdbcType=VARCHAR}, #{errorLoginDate,jdbcType=VARCHAR}, 
      #{valid,jdbcType=DECIMAL}, #{sortIndex,jdbcType=DECIMAL}, #{attribute,jdbcType=VARCHAR}, 
      #{property,jdbcType=VARCHAR}, #{ext1,jdbcType=VARCHAR}, #{ext2,jdbcType=VARCHAR}, 
      #{ext3,jdbcType=VARCHAR}, #{agencyId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.core.model.TEsUser" >
    insert into t_es_user
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="name != null" >
        NAME,
      </if>
      <if test="displayName != null" >
        DISPLAY_NAME,
      </if>
      <if test="codedPassword != null" >
        CODED_PASSWORD,
      </if>
      <if test="descInfo != null" >
        DESC_INFO,
      </if>
      <if test="lastName != null" >
        LAST_NAME,
      </if>
      <if test="firstName != null" >
        FIRST_NAME,
      </if>
      <if test="middleName != null" >
        MIDDLE_NAME,
      </if>
      <if test="nickName != null" >
        NICK_NAME,
      </if>
      <if test="title != null" >
        TITLE,
      </if>
      <if test="initials != null" >
        INITIALS,
      </if>
      <if test="fullName != null" >
        FULL_NAME,
      </if>
      <if test="prof != null" >
        PROF,
      </if>
      <if test="position != null" >
        POSITION,
      </if>
      <if test="dept != null" >
        DEPT,
      </if>
      <if test="account != null" >
        ACCOUNT,
      </if>
      <if test="idNumber != null" >
        ID_NUMBER,
      </if>
      <if test="workNumber != null" >
        WORK_NUMBER,
      </if>
      <if test="gender != null" >
        GENDER,
      </if>
      <if test="birthday != null" >
        BIRTHDAY,
      </if>
      <if test="country != null" >
        COUNTRY,
      </if>
      <if test="province != null" >
        PROVINCE,
      </if>
      <if test="city != null" >
        CITY,
      </if>
      <if test="street != null" >
        STREET,
      </if>
      <if test="addr != null" >
        ADDR,
      </if>
      <if test="homeAddr != null" >
        HOME_ADDR,
      </if>
      <if test="officeAddr != null" >
        OFFICE_ADDR,
      </if>
      <if test="businessAddr != null" >
        BUSINESS_ADDR,
      </if>
      <if test="otherAddr != null" >
        OTHER_ADDR,
      </if>
      <if test="postalCode != null" >
        POSTAL_CODE,
      </if>
      <if test="postBox != null" >
        POST_BOX,
      </if>
      <if test="webSite != null" >
        WEB_SITE,
      </if>
      <if test="email != null" >
        EMAIL,
      </if>
      <if test="im != null" >
        IM,
      </if>
      <if test="domain != null" >
        DOMAIN,
      </if>
      <if test="mainPhone != null" >
        MAIN_PHONE,
      </if>
      <if test="homePhone != null" >
        HOME_PHONE,
      </if>
      <if test="mobilePhone != null" >
        MOBILE_PHONE,
      </if>
      <if test="officePhone != null" >
        OFFICE_PHONE,
      </if>
      <if test="businessPhone != null" >
        BUSINESS_PHONE,
      </if>
      <if test="otherPhone != null" >
        OTHER_PHONE,
      </if>
      <if test="fax != null" >
        FAX,
      </if>
      <if test="userType != null" >
        USER_TYPE,
      </if>
      <if test="userCode != null" >
        USER_CODE,
      </if>
      <if test="orgCode != null" >
        ORG_CODE,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="expireDate != null" >
        EXPIRE_DATE,
      </if>
      <if test="pwdExpireDate != null" >
        PWD_EXPIRE_DATE,
      </if>
      <if test="propertyUpdateDate != null" >
        PROPERTY_UPDATE_DATE,
      </if>
      <if test="pwdUpdateDate != null" >
        PWD_UPDATE_DATE,
      </if>
      <if test="lastLoginDate != null" >
        LAST_LOGIN_DATE,
      </if>
      <if test="errorLoginDate != null" >
        ERROR_LOGIN_DATE,
      </if>
      <if test="valid != null" >
        VALID,
      </if>
      <if test="sortIndex != null" >
        SORT_INDEX,
      </if>
      <if test="attribute != null" >
        ATTRIBUTE,
      </if>
      <if test="property != null" >
        PROPERTY,
      </if>
      <if test="ext1 != null" >
        EXT1,
      </if>
      <if test="ext2 != null" >
        EXT2,
      </if>
      <if test="ext3 != null" >
        EXT3,
      </if>
      <if test="agencyId != null" >
        AGENCY_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="displayName != null" >
        #{displayName,jdbcType=VARCHAR},
      </if>
      <if test="codedPassword != null" >
        #{codedPassword,jdbcType=VARCHAR},
      </if>
      <if test="descInfo != null" >
        #{descInfo,jdbcType=VARCHAR},
      </if>
      <if test="lastName != null" >
        #{lastName,jdbcType=VARCHAR},
      </if>
      <if test="firstName != null" >
        #{firstName,jdbcType=VARCHAR},
      </if>
      <if test="middleName != null" >
        #{middleName,jdbcType=VARCHAR},
      </if>
      <if test="nickName != null" >
        #{nickName,jdbcType=VARCHAR},
      </if>
      <if test="title != null" >
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="initials != null" >
        #{initials,jdbcType=VARCHAR},
      </if>
      <if test="fullName != null" >
        #{fullName,jdbcType=VARCHAR},
      </if>
      <if test="prof != null" >
        #{prof,jdbcType=VARCHAR},
      </if>
      <if test="position != null" >
        #{position,jdbcType=VARCHAR},
      </if>
      <if test="dept != null" >
        #{dept,jdbcType=VARCHAR},
      </if>
      <if test="account != null" >
        #{account,jdbcType=VARCHAR},
      </if>
      <if test="idNumber != null" >
        #{idNumber,jdbcType=VARCHAR},
      </if>
      <if test="workNumber != null" >
        #{workNumber,jdbcType=VARCHAR},
      </if>
      <if test="gender != null" >
        #{gender,jdbcType=VARCHAR},
      </if>
      <if test="birthday != null" >
        #{birthday,jdbcType=VARCHAR},
      </if>
      <if test="country != null" >
        #{country,jdbcType=VARCHAR},
      </if>
      <if test="province != null" >
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="street != null" >
        #{street,jdbcType=VARCHAR},
      </if>
      <if test="addr != null" >
        #{addr,jdbcType=VARCHAR},
      </if>
      <if test="homeAddr != null" >
        #{homeAddr,jdbcType=VARCHAR},
      </if>
      <if test="officeAddr != null" >
        #{officeAddr,jdbcType=VARCHAR},
      </if>
      <if test="businessAddr != null" >
        #{businessAddr,jdbcType=VARCHAR},
      </if>
      <if test="otherAddr != null" >
        #{otherAddr,jdbcType=VARCHAR},
      </if>
      <if test="postalCode != null" >
        #{postalCode,jdbcType=VARCHAR},
      </if>
      <if test="postBox != null" >
        #{postBox,jdbcType=VARCHAR},
      </if>
      <if test="webSite != null" >
        #{webSite,jdbcType=VARCHAR},
      </if>
      <if test="email != null" >
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="im != null" >
        #{im,jdbcType=VARCHAR},
      </if>
      <if test="domain != null" >
        #{domain,jdbcType=VARCHAR},
      </if>
      <if test="mainPhone != null" >
        #{mainPhone,jdbcType=VARCHAR},
      </if>
      <if test="homePhone != null" >
        #{homePhone,jdbcType=VARCHAR},
      </if>
      <if test="mobilePhone != null" >
        #{mobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="officePhone != null" >
        #{officePhone,jdbcType=VARCHAR},
      </if>
      <if test="businessPhone != null" >
        #{businessPhone,jdbcType=VARCHAR},
      </if>
      <if test="otherPhone != null" >
        #{otherPhone,jdbcType=VARCHAR},
      </if>
      <if test="fax != null" >
        #{fax,jdbcType=VARCHAR},
      </if>
      <if test="userType != null" >
        #{userType,jdbcType=VARCHAR},
      </if>
      <if test="userCode != null" >
        #{userCode,jdbcType=VARCHAR},
      </if>
      <if test="orgCode != null" >
        #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=VARCHAR},
      </if>
      <if test="expireDate != null" >
        #{expireDate,jdbcType=VARCHAR},
      </if>
      <if test="pwdExpireDate != null" >
        #{pwdExpireDate,jdbcType=VARCHAR},
      </if>
      <if test="propertyUpdateDate != null" >
        #{propertyUpdateDate,jdbcType=VARCHAR},
      </if>
      <if test="pwdUpdateDate != null" >
        #{pwdUpdateDate,jdbcType=VARCHAR},
      </if>
      <if test="lastLoginDate != null" >
        #{lastLoginDate,jdbcType=VARCHAR},
      </if>
      <if test="errorLoginDate != null" >
        #{errorLoginDate,jdbcType=VARCHAR},
      </if>
      <if test="valid != null" >
        #{valid,jdbcType=DECIMAL},
      </if>
      <if test="sortIndex != null" >
        #{sortIndex,jdbcType=DECIMAL},
      </if>
      <if test="attribute != null" >
        #{attribute,jdbcType=VARCHAR},
      </if>
      <if test="property != null" >
        #{property,jdbcType=VARCHAR},
      </if>
      <if test="ext1 != null" >
        #{ext1,jdbcType=VARCHAR},
      </if>
      <if test="ext2 != null" >
        #{ext2,jdbcType=VARCHAR},
      </if>
      <if test="ext3 != null" >
        #{ext3,jdbcType=VARCHAR},
      </if>
      <if test="agencyId != null" >
        #{agencyId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.core.model.TEsUser" >
    update t_es_user
    <set >
      <if test="displayName != null" >
        DISPLAY_NAME = #{displayName,jdbcType=VARCHAR},
      </if>
      <if test="codedPassword != null" >
        CODED_PASSWORD = #{codedPassword,jdbcType=VARCHAR},
      </if>
      <if test="descInfo != null" >
        DESC_INFO = #{descInfo,jdbcType=VARCHAR},
      </if>
      <if test="lastName != null" >
        LAST_NAME = #{lastName,jdbcType=VARCHAR},
      </if>
      <if test="firstName != null" >
        FIRST_NAME = #{firstName,jdbcType=VARCHAR},
      </if>
      <if test="middleName != null" >
        MIDDLE_NAME = #{middleName,jdbcType=VARCHAR},
      </if>
      <if test="nickName != null" >
        NICK_NAME = #{nickName,jdbcType=VARCHAR},
      </if>
      <if test="title != null" >
        TITLE = #{title,jdbcType=VARCHAR},
      </if>
      <if test="initials != null" >
        INITIALS = #{initials,jdbcType=VARCHAR},
      </if>
      <if test="fullName != null" >
        FULL_NAME = #{fullName,jdbcType=VARCHAR},
      </if>
      <if test="prof != null" >
        PROF = #{prof,jdbcType=VARCHAR},
      </if>
      <if test="position != null" >
        POSITION = #{position,jdbcType=VARCHAR},
      </if>
      <if test="dept != null" >
        DEPT = #{dept,jdbcType=VARCHAR},
      </if>
      <if test="account != null" >
        ACCOUNT = #{account,jdbcType=VARCHAR},
      </if>
      <if test="idNumber != null" >
        ID_NUMBER = #{idNumber,jdbcType=VARCHAR},
      </if>
      <if test="workNumber != null" >
        WORK_NUMBER = #{workNumber,jdbcType=VARCHAR},
      </if>
      <if test="gender != null" >
        GENDER = #{gender,jdbcType=VARCHAR},
      </if>
      <if test="birthday != null" >
        BIRTHDAY = #{birthday,jdbcType=VARCHAR},
      </if>
      <if test="country != null" >
        COUNTRY = #{country,jdbcType=VARCHAR},
      </if>
      <if test="province != null" >
        PROVINCE = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        CITY = #{city,jdbcType=VARCHAR},
      </if>
      <if test="street != null" >
        STREET = #{street,jdbcType=VARCHAR},
      </if>
      <if test="addr != null" >
        ADDR = #{addr,jdbcType=VARCHAR},
      </if>
      <if test="homeAddr != null" >
        HOME_ADDR = #{homeAddr,jdbcType=VARCHAR},
      </if>
      <if test="officeAddr != null" >
        OFFICE_ADDR = #{officeAddr,jdbcType=VARCHAR},
      </if>
      <if test="businessAddr != null" >
        BUSINESS_ADDR = #{businessAddr,jdbcType=VARCHAR},
      </if>
      <if test="otherAddr != null" >
        OTHER_ADDR = #{otherAddr,jdbcType=VARCHAR},
      </if>
      <if test="postalCode != null" >
        POSTAL_CODE = #{postalCode,jdbcType=VARCHAR},
      </if>
      <if test="postBox != null" >
        POST_BOX = #{postBox,jdbcType=VARCHAR},
      </if>
      <if test="webSite != null" >
        WEB_SITE = #{webSite,jdbcType=VARCHAR},
      </if>
      <if test="email != null" >
        EMAIL = #{email,jdbcType=VARCHAR},
      </if>
      <if test="im != null" >
        IM = #{im,jdbcType=VARCHAR},
      </if>
      <if test="domain != null" >
        DOMAIN = #{domain,jdbcType=VARCHAR},
      </if>
      <if test="mainPhone != null" >
        MAIN_PHONE = #{mainPhone,jdbcType=VARCHAR},
      </if>
      <if test="homePhone != null" >
        HOME_PHONE = #{homePhone,jdbcType=VARCHAR},
      </if>
      <if test="mobilePhone != null" >
        MOBILE_PHONE = #{mobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="officePhone != null" >
        OFFICE_PHONE = #{officePhone,jdbcType=VARCHAR},
      </if>
      <if test="businessPhone != null" >
        BUSINESS_PHONE = #{businessPhone,jdbcType=VARCHAR},
      </if>
      <if test="otherPhone != null" >
        OTHER_PHONE = #{otherPhone,jdbcType=VARCHAR},
      </if>
      <if test="fax != null" >
        FAX = #{fax,jdbcType=VARCHAR},
      </if>
      <if test="userType != null" >
        USER_TYPE = #{userType,jdbcType=VARCHAR},
      </if>
      <if test="userCode != null" >
        USER_CODE = #{userCode,jdbcType=VARCHAR},
      </if>
      <if test="orgCode != null" >
        ORG_CODE = #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        CREATE_DATE = #{createDate,jdbcType=VARCHAR},
      </if>
      <if test="expireDate != null" >
        EXPIRE_DATE = #{expireDate,jdbcType=VARCHAR},
      </if>
      <if test="pwdExpireDate != null" >
        PWD_EXPIRE_DATE = #{pwdExpireDate,jdbcType=VARCHAR},
      </if>
      <if test="propertyUpdateDate != null" >
        PROPERTY_UPDATE_DATE = #{propertyUpdateDate,jdbcType=VARCHAR},
      </if>
      <if test="pwdUpdateDate != null" >
        PWD_UPDATE_DATE = #{pwdUpdateDate,jdbcType=VARCHAR},
      </if>
      <if test="lastLoginDate != null" >
        LAST_LOGIN_DATE = #{lastLoginDate,jdbcType=VARCHAR},
      </if>
      <if test="errorLoginDate != null" >
        ERROR_LOGIN_DATE = #{errorLoginDate,jdbcType=VARCHAR},
      </if>
      <if test="valid != null" >
        VALID = #{valid,jdbcType=DECIMAL},
      </if>
      <if test="sortIndex != null" >
        SORT_INDEX = #{sortIndex,jdbcType=DECIMAL},
      </if>
      <if test="attribute != null" >
        ATTRIBUTE = #{attribute,jdbcType=VARCHAR},
      </if>
      <if test="property != null" >
        PROPERTY = #{property,jdbcType=VARCHAR},
      </if>
      <if test="ext1 != null" >
        EXT1 = #{ext1,jdbcType=VARCHAR},
      </if>
      <if test="ext2 != null" >
        EXT2 = #{ext2,jdbcType=VARCHAR},
      </if>
      <if test="ext3 != null" >
        EXT3 = #{ext3,jdbcType=VARCHAR},
      </if>
      <if test="agencyId != null" >
        AGENCY_ID = #{agencyId,jdbcType=VARCHAR},
      </if>
    </set>
    where NAME = #{name,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.core.model.TEsUser" >
    update t_es_user
    set DISPLAY_NAME = #{displayName,jdbcType=VARCHAR},
      CODED_PASSWORD = #{codedPassword,jdbcType=VARCHAR},
      DESC_INFO = #{descInfo,jdbcType=VARCHAR},
      LAST_NAME = #{lastName,jdbcType=VARCHAR},
      FIRST_NAME = #{firstName,jdbcType=VARCHAR},
      MIDDLE_NAME = #{middleName,jdbcType=VARCHAR},
      NICK_NAME = #{nickName,jdbcType=VARCHAR},
      TITLE = #{title,jdbcType=VARCHAR},
      INITIALS = #{initials,jdbcType=VARCHAR},
      FULL_NAME = #{fullName,jdbcType=VARCHAR},
      PROF = #{prof,jdbcType=VARCHAR},
      POSITION = #{position,jdbcType=VARCHAR},
      DEPT = #{dept,jdbcType=VARCHAR},
      ACCOUNT = #{account,jdbcType=VARCHAR},
      ID_NUMBER = #{idNumber,jdbcType=VARCHAR},
      WORK_NUMBER = #{workNumber,jdbcType=VARCHAR},
      GENDER = #{gender,jdbcType=VARCHAR},
      BIRTHDAY = #{birthday,jdbcType=VARCHAR},
      COUNTRY = #{country,jdbcType=VARCHAR},
      PROVINCE = #{province,jdbcType=VARCHAR},
      CITY = #{city,jdbcType=VARCHAR},
      STREET = #{street,jdbcType=VARCHAR},
      ADDR = #{addr,jdbcType=VARCHAR},
      HOME_ADDR = #{homeAddr,jdbcType=VARCHAR},
      OFFICE_ADDR = #{officeAddr,jdbcType=VARCHAR},
      BUSINESS_ADDR = #{businessAddr,jdbcType=VARCHAR},
      OTHER_ADDR = #{otherAddr,jdbcType=VARCHAR},
      POSTAL_CODE = #{postalCode,jdbcType=VARCHAR},
      POST_BOX = #{postBox,jdbcType=VARCHAR},
      WEB_SITE = #{webSite,jdbcType=VARCHAR},
      EMAIL = #{email,jdbcType=VARCHAR},
      IM = #{im,jdbcType=VARCHAR},
      DOMAIN = #{domain,jdbcType=VARCHAR},
      MAIN_PHONE = #{mainPhone,jdbcType=VARCHAR},
      HOME_PHONE = #{homePhone,jdbcType=VARCHAR},
      MOBILE_PHONE = #{mobilePhone,jdbcType=VARCHAR},
      OFFICE_PHONE = #{officePhone,jdbcType=VARCHAR},
      BUSINESS_PHONE = #{businessPhone,jdbcType=VARCHAR},
      OTHER_PHONE = #{otherPhone,jdbcType=VARCHAR},
      FAX = #{fax,jdbcType=VARCHAR},
      USER_TYPE = #{userType,jdbcType=VARCHAR},
      USER_CODE = #{userCode,jdbcType=VARCHAR},
      ORG_CODE = #{orgCode,jdbcType=VARCHAR},
      CREATE_DATE = #{createDate,jdbcType=VARCHAR},
      EXPIRE_DATE = #{expireDate,jdbcType=VARCHAR},
      PWD_EXPIRE_DATE = #{pwdExpireDate,jdbcType=VARCHAR},
      PROPERTY_UPDATE_DATE = #{propertyUpdateDate,jdbcType=VARCHAR},
      PWD_UPDATE_DATE = #{pwdUpdateDate,jdbcType=VARCHAR},
      LAST_LOGIN_DATE = #{lastLoginDate,jdbcType=VARCHAR},
      ERROR_LOGIN_DATE = #{errorLoginDate,jdbcType=VARCHAR},
      VALID = #{valid,jdbcType=DECIMAL},
      SORT_INDEX = #{sortIndex,jdbcType=DECIMAL},
      ATTRIBUTE = #{attribute,jdbcType=VARCHAR},
      PROPERTY = #{property,jdbcType=VARCHAR},
      EXT1 = #{ext1,jdbcType=VARCHAR},
      EXT2 = #{ext2,jdbcType=VARCHAR},
      EXT3 = #{ext3,jdbcType=VARCHAR},
      AGENCY_ID = #{agencyId,jdbcType=VARCHAR}
    where NAME = #{name,jdbcType=VARCHAR}
  </update>

  <select id="getByNameAndPass" resultType="com.extracme.evcard.membership.core.dto.OrgUserInfoDto">
    SELECT pk_id AS pkId,DISPLAY_NAME AS displayName,ORG_CODE AS orgCode, AGENCY_ID AS agencyId
    FROM ${iplatSchema}.t_es_user
    WHERE NAME = #{name} and CODED_PASSWORD = #{password}
  </select>

  <select id="getOrgPhoneByName" resultType="String">
    SELECT MOBILE_PHONE
    FROM ${iplatSchema}.t_es_user
    where NAME = #{name} AND USER_TYPE in(0, 1)
  </select>
  
  <!-- 通过姓名修改密码 -->
  <update id="updateCodedPasswordByName">
	UPDATE iplat.t_es_user
	SET `CODED_PASSWORD` = #{password}
	WHERE
	NAME = #{name}
</update>
</mapper>