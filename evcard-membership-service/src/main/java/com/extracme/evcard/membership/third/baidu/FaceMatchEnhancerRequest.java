package com.extracme.evcard.membership.third.baidu;

import lombok.Data;

@Data
public class FaceMatchEnhancerRequest {

    /**
     * 非必填： 默认选择APP场景
     * APP ： APP场景，与采集SDK一起使用
     * H5：H5场景，在增强级的实名认证场景进行使用
     */
    private String scene_type;

    /**
     * 必填, 采集发生在人脸比对时。
     * APP场景： 采集SDK4.1版本上传的加密图片Base64信息
     * H5场景： 可以直接使用拍摄的照片
     */
    private String image;

    /**
     * 必填
     * APP: 图片类型 BASE64:图片的base64值，base64编码后的图片数据，编码后的图片大小不超过2M；
     */
    private String image_type;

    /**
     * scene_type 等于 APP 时必填。
     */
    private String app;

    /**
     * 必填， 这个是底图。
     * 图片信息(总数据大小应小于10M)，图片上传方式根据image_type来判断。本图片特指客户服务器上传图片，非加密图片Base64值
     */
    private String register_image;

    /**
     * 必填：
     * 图片类型
     * BASE64:图片的base64值，base64编码后的图片数据，编码后的图片大小不超过2M；
     * URL:图片的 URL地址( 可能由于网络等原因导致下载图片时间过长)；
     * FACE_TOKEN: 人脸图片的唯一标识，调用人脸检测接口时，会为每个人脸图片赋予一个唯一的FACE_TOKEN，同一张图片多次检测得到的FACE_TOKEN是同一个。
     */
    private String register_image_type;


    /**
     * 	活体检测控制
     * NONE: 不进行控制
     * LOW:较低的活体要求(高通过率 低攻击拒绝率)
     * NORMAL: 一般的活体要求(平衡的攻击拒绝率, 通过率)
     * HIGH: 较高的活体要求(高攻击拒绝率 低通过率)
     * 默认 NONE 若活体检测结果不满足要求，则返回结果中会提示活体检测失败
     */
    private String liveness_control;
}
