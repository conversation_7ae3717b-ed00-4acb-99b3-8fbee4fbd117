package com.extracme.evcard.membership.core.dto.md;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/8/30
 */
@Data
public class CityConfigurationDto {
    private long id; // id
    private long cityId; // 运营城市ID
    private String orgCode; // 运营公司CODE
    private String cityName; // 运营城市名
    private String orgName; // 运营公司名
    private int storeBusinessStatus; // 门店业务状态 2=无效 1=有效
    private int crossRegionReturnCar; // 跨区还车 2=无效 1=有效
    private int crossStoreReturnCar; // 跨店还车 2=无效 1=有效
}
