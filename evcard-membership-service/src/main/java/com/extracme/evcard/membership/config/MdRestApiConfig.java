package com.extracme.evcard.membership.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "md.inner.api")
public class MdRestApiConfig {
    private String baseUrl;

    private String searchContractById;

    private String queryOrderPayInfo;

    private String getOrderCountByStatusUrl;

    private String searchCityConfiguration;

    private String pageFulfillmentOrder;

    private String getMemberDepositInfos;

    private String saveBlackListLogUrl;
}
