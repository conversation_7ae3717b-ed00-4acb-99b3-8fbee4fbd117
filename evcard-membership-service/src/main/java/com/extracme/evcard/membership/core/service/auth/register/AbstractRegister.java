package com.extracme.evcard.membership.core.service.auth.register;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.bvm.bo.ServiceResult;
import com.extracme.evcard.bvm.service.IAgencyMemberService;
import com.extracme.evcard.bvm.to.InitialAgencyMembershipTO;
import com.extracme.evcard.membership.core.input.NotifyRegisterDto;
import com.extracme.evcard.membership.core.service.auth.IDescription;
import com.extracme.evcard.membership.core.service.auth.IRegister;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.model.MembershipBaseInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

@Slf4j
public abstract class AbstractRegister implements IRegister, IDescription {

    @Resource(name = "agencyMemberService")
    private IAgencyMemberService agencyMemberService;
    @Autowired
    private MembershipInfoMapper membershipInfoMapper;

    /**
     * 待绑定的企业id
     */
    private String bindAgencyId;
    /**
     * 待绑定的企业 规则id
     */
    private String bindAgencyRoleId;

    public String getBindAgencyId() {
        return bindAgencyId;
    }

    public String getBindAgencyRoleId() {
        return bindAgencyRoleId;
    }

    @Override
    public String getDescription() {
        return null;
    }


    /**
     * 关联企业
     *
     * @param notifyRegisterDto
     */
    public void bindAgency(NotifyRegisterDto notifyRegisterDto) {
        try {
            String agencyId = getBindAgencyId();
            String agencyRoleId = getBindAgencyRoleId();
            //需要绑定 特殊的企业
            if (StringUtils.isNotBlank(agencyId) && StringUtils.isNotBlank(agencyRoleId)) {
                Long pkId = notifyRegisterDto.getUserId();
                if (pkId == null || pkId == 0L) {
                    MembershipBaseInfo membershipBaseInfo = membershipInfoMapper.selectBaseByPhone2(notifyRegisterDto.getMobilePhone(), 0);
                    if (membershipBaseInfo != null) {
                        pkId = membershipBaseInfo.getPkId();
                    }
                }

                if (pkId != null && pkId != 0L) {
                    InitialAgencyMembershipTO dto = new InitialAgencyMembershipTO();
                    dto.setAgencyId(agencyId);
                    dto.setAgencyRoleId(Long.valueOf(agencyRoleId));
                    dto.setMemberShipId(pkId);
                    dto.setOperatorId(-1L);
                    dto.setOperatorName("evcard-membership");
                    ServiceResult serviceResult = agencyMemberService.initialAgencyMembership(dto);
                    if (serviceResult != null && serviceResult.isSuccess()) {
                        log.info("{} initialAgencyMembership  关联企业成功，dto={}，serviceResult={}", getDescription(), JSON.toJSONString(dto), JSON.toJSONString(serviceResult));
                    } else {
                        log.error("{} initialAgencyMembership  失败，dto={}，serviceResult={}", getDescription(), JSON.toJSONString(dto), JSON.toJSONString(serviceResult));
                    }
                }
            }
        } catch (Exception e) {
            log.error("{} bindAgency  异常，notifyRegisterDto={}", getDescription(), JSON.toJSONString(notifyRegisterDto));
        }
    }

}
