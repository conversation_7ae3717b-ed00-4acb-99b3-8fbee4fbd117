package com.extracme.evcard.membership.core.service.agency;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.bvm.bo.ServiceResult;
import com.extracme.evcard.bvm.dto.GetAgencyRoleInfoDTO;
import com.extracme.evcard.bvm.service.IAgencyMemberService;
import com.extracme.evcard.bvm.service.IAgencyPriceService;
import com.extracme.evcard.bvm.to.InitialAgencyMembershipTO;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.dao.AgencySecondAppKeyRelationMapper;
import com.extracme.evcard.membership.core.dao.SecondAppKeyManagerMapper;
import com.extracme.evcard.membership.core.dto.AgencyInfoDto;
import com.extracme.evcard.membership.core.dto.CommBaseResponse;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.dto.OperatorDto;
import com.extracme.evcard.membership.core.dto.agency.*;
import com.extracme.evcard.membership.core.exception.ExceptionEnum;
import com.extracme.evcard.membership.core.manager.MembershipAgencyManager;
import com.extracme.evcard.membership.core.model.AgencySecondAppKeyRelation;
import com.extracme.evcard.membership.core.model.AgencySecondAppKeyRelationExample;
import com.extracme.evcard.membership.core.model.SecondAppKeyManager;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.membership.core.service.IMembershipWrapService;
import com.extracme.evcard.membership.core.service.IShortlinkManagementService;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.dto.CommonAddRespDto;
import com.extracme.evcard.rpc.enums.StatusCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

/**
 * 企业会员 信息
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MembershipAgencyService implements IMembershipAgencyService {

    @Autowired
    private IMemberShipService membershipService;

    @Autowired
    private IMembershipWrapService membershipWrapService;

    @Autowired
    private SecondAppKeyManagerMapper secondAppKeyManagerMapper;

    @Autowired
    private AgencySecondAppKeyRelationMapper relationMapper;

    @Autowired
    private IShortlinkManagementService shortlinkService;
    @Autowired
    private IAgencyDiscountService agencyDiscountService;

    @Autowired
    private MembershipAgencyManager membershipAgencyManager;
    @Resource(name = "agencyMemberService")
    private IAgencyMemberService agencyMemberService;

    @Resource(name = "agencyPriceService")
    private IAgencyPriceService agencyPriceService;

    @Override
    public CommonAddRespDto addAgencySecondAppKeyRelation(AddAgencySecondAppKeyRelationDTO dto) {
        // 1. 入参校验
        if (dto == null || dto.getAgencyId() == null || dto.getSecondAppKey() == null || (dto.getAgencyRoleId() == null && dto.getAgencyRoleId() > 0)) {
            return new CommonAddRespDto(-1, "参数不能为空");
        }

        String agencyId = dto.getAgencyId();
        String secondAppKey = dto.getSecondAppKey();
        Long agencyRoleId = dto.getAgencyRoleId();

        // 2. 校验企业是否存在
        AgencyInfoDto agencyInfoDto = membershipService.queryAgencyInfoByAgencyId(agencyId);
        if (agencyInfoDto == null) {
            return new CommonAddRespDto(-1, "企业不存在");
        }
        if (agencyInfoDto.getStatus() != 1) {
            return new CommonAddRespDto(-1, "企业状态非合作中");
        }

        if (agencyInfoDto.getOrderPayer() != 2) {
            return new CommonAddRespDto(-1, "该企业未开启个人支付");
        }

        // 3. 校验二级渠道是否存在
        SecondAppKeyManager secondAppKeyManager = secondAppKeyManagerMapper.selectBySecondAppKey(secondAppKey);
        if (secondAppKeyManager == null) {
            return new CommonAddRespDto(-1, StatusCode.APPKEY_INVALID.getMsg());
        }
        if (secondAppKeyManager.getStatus() == 1) {
            return new CommonAddRespDto(-1, "渠道无效");
        }

        //  校验角色是否存在
        ServiceResult<GetAgencyRoleInfoDTO> agencyRoleInfo = agencyMemberService.getAgencyRoleInfo(agencyRoleId);
        if (!agencyRoleInfo.isSuccess()) {
            return new CommonAddRespDto(-1, "用车规则不存在");
        }
        // 校验用车规则是否禁用
        GetAgencyRoleInfoDTO getAgencyRoleInfoDTO = agencyRoleInfo.getResult();
        if (getAgencyRoleInfoDTO == null || getAgencyRoleInfoDTO.getDisabled() == 0) {
            return new CommonAddRespDto(-1, "用车规则已经禁用");
        }

        if (getAgencyRoleInfoDTO.getPersonalPayFlag() == 0) {
            return new CommonAddRespDto(-1, "该用车规则未开启个人支付");
        }
        //TODO 企业id和角色id是否匹配

        // 4. 校验绑定关系是否已存在
        AgencySecondAppKeyRelationExample example = new AgencySecondAppKeyRelationExample();
        example.createCriteria().andAgencyIdEqualTo(agencyId).andSecondAppKeyEqualTo(secondAppKey).andIsDeletedEqualTo(0);
        List<AgencySecondAppKeyRelation> agencySecondAppKeyRelations = relationMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(agencySecondAppKeyRelations)) {
            AgencySecondAppKeyRelation agencySecondAppKeyRelation = agencySecondAppKeyRelations.get(0);
            log.error("该企业与二级渠道已存在绑定关系，agencyId={},secondAppKey={},agencySecondAppKeyRelation={}", agencyId, secondAppKey, JSON.toJSONString(agencySecondAppKeyRelation));
            return new CommonAddRespDto(-1, "该企业与二级渠道已存在绑定关系");
        }

        OperatorDto operator = dto.getOperator();
        if (operator == null) {
            operator = OperatorDto.SYSTEM;
        }

        // 保存数据
        try {
            Long agencySecondAppKeyRelationId = membershipAgencyManager.createAgencySecondAppKeyRelationWithQrCode(agencyId, secondAppKey, agencyRoleId, operator);
            return new CommonAddRespDto(0, "添加成功", agencySecondAppKeyRelationId);
        } catch (Exception e) {
            log.error("新增企业绑定关系失败，dto={}", JSON.toJSONString(dto), e);
            return new CommonAddRespDto(-1, e.getMessage());
        }
    }

    @Override
    public BaseResponse deleteAgencySecondAppKeyRelation(String agencyId, OperatorDto operator) {
        // 1. 入参校验
        if (StringUtils.isBlank(agencyId)) {
            return new BaseResponse(StatusCode.ILLEGAL_PARAM.getCode(), "企业ID不能为空");
        }
        if (operator == null) {
            operator = OperatorDto.SYSTEM;
        }

        // 2. 校验企业是否存在
        AgencyInfoDto agencyInfoDto = membershipService.queryAgencyInfoByAgencyId(agencyId);
        if (agencyInfoDto == null) {
            return new BaseResponse(-1, "企业不存在");
        }

        // 3. 查询当前企业的绑定关系
        int i = relationMapper.deleteByAgencyId(agencyId, operator.getOperatorId(), operator.getOperatorName());
        if (i <= 0) {
            log.error("删除企业与二级渠道绑定关系数目为0，agencyId={}", agencyId);
        }

        return new BaseResponse(0, "删除成功");
    }

    @Override
    public CommBaseResponse<AgencySecondAppKeyRelationDTO> queryAgencySecondAppKeyRelation(String agencyId) {
        // 1. 入参校验
        if (StringUtils.isBlank(agencyId)) {
            log.warn("企业ID为空");
            return CommBaseResponse.getFailVO("参数不能为空");
        }

        // 2. 查询绑定关系
        List<AgencySecondAppKeyRelation> relations = relationMapper.selectRelationByAgencyId(agencyId);
        if (CollectionUtils.isEmpty(relations)) {
            log.info("未找到企业与二级渠道的绑定关系，agencyId={}", agencyId);
            return CommBaseResponse.getFailVO("未找到企业与二级渠道的绑定关系");
        }

        // 假设一个企业只绑定一个二级渠道（根据你的业务决定是否需要支持多个）
        AgencySecondAppKeyRelation relation = relations.get(0);

        // 3. 查询渠道信息
        String secondAppKey = relation.getSecondAppKey();
        SecondAppKeyDetailInfoDTO secondAppKeyDetailInfoDTO = secondAppKeyManagerMapper.selectBySecondAppKeyDetailInfo(secondAppKey);
        if (secondAppKeyDetailInfoDTO == null) {
            log.warn("未找到对应的二级渠道信息，secondAppKey={}", secondAppKey);
            return CommBaseResponse.getFailVO("未找到二级渠道信息");
        }

        // 4. 查询规则信息（假设存在一个规则服务或DAO）
        // AgencyRule agencyRule = agencyRuleService.queryById(relation.getAgencyRoleId());
        Long agencyRoleId = relation.getAgencyRoleId();
        //  校验角色是否存在
        ServiceResult<GetAgencyRoleInfoDTO> agencyRoleInfo = agencyMemberService.getAgencyRoleInfo(agencyRoleId);
        if (!agencyRoleInfo.isSuccess()) {
            return CommBaseResponse.getFailVO(-1, "用车规则不存在");
        }
        // 校验用车规则是否禁用
        GetAgencyRoleInfoDTO getAgencyRoleInfoDTO = agencyRoleInfo.getResult();
        if (getAgencyRoleInfoDTO == null) {
            return CommBaseResponse.getFailVO(-1, "用车规则不存在");
        }

        // 5. 构建 DTO 并返回
        AgencySecondAppKeyRelationDTO dto = new AgencySecondAppKeyRelationDTO();
        dto.setId(relation.getId());
        dto.setAgencyId(relation.getAgencyId());
        dto.setSecondAppKey(secondAppKey);
        dto.setSecondAppKeyName(secondAppKeyDetailInfoDTO.getSecondAppKeyName()); // 渠道名称
        dto.setAppKey(secondAppKeyDetailInfoDTO.getFirstAppKey());
        dto.setAppKeyName(secondAppKeyDetailInfoDTO.getFirstAppKeyName());
        dto.setPlatformId(secondAppKeyDetailInfoDTO.getPlatformId());
        dto.setPlatformName(secondAppKeyDetailInfoDTO.getPlatName());

        // 企业用车平台-角色信息
        dto.setAgencyRoleId(agencyRoleId);
        dto.setAgencyRoleName(getAgencyRoleInfoDTO.getAgencyRoleName()); // 规则名称
        BigDecimal exemptionAmount = getAgencyRoleInfoDTO.getExemptionAmount();
        dto.setExemptionAmount(exemptionAmount != null ? exemptionAmount.toPlainString() : "0.00");
        Integer personalPayFlag = getAgencyRoleInfoDTO.getPersonalPayFlag();
        Integer businessPayFlag = getAgencyRoleInfoDTO.getBusinessPayFlag();
        if (personalPayFlag != null && personalPayFlag == 1) {
            dto.setAgencyPayType(0);
        } else if (businessPayFlag != null && businessPayFlag == 1) {
            dto.setAgencyPayType(1);
        } else {
            dto.setAgencyPayType(0);
        }

        dto.setWechatQrPicUrl(relation.getWechatQrPicUrl());
        dto.setCreateTime(relation.getCreateTime());
        dto.setUpdateTime(relation.getUpdateTime());
        dto.setCreateOperName(relation.getCreateOperName());
        dto.setUpdateOperName(relation.getUpdateOperName());

        return CommBaseResponse.getSuccessVO(dto);
    }

    @Override
    public CommBaseResponse<BindAgencyBySecondAppKeyRespDto> bindAgencyBySecondAppKey(BindAgencyBySecondAppKeyRelationDTO input) {
        log.info("通过二级渠道绑定企业，input={}", JSON.toJSONString(input));
        // 1. 入参校验
        if (input == null || StringUtils.isBlank(input.getMid()) || input.getRelationId() == null || input.getRelationId() <= 0l) {
            return CommBaseResponse.getFailVO("参数不能为空");
        }

        String mid = input.getMid();
        Long relationId = input.getRelationId();
        BindAgencyBySecondAppKeyRespDto output = new BindAgencyBySecondAppKeyRespDto();

        MembershipBasicInfo member = membershipWrapService.getMemberByMid(mid);
        if (member == null) {
            return CommBaseResponse.getFailVO("用户不存在");
        }

        // 2. 根据 二级渠道查询企业信息
        AgencySecondAppKeyRelation agencySecondAppKeyRelation = relationMapper.selectByPrimaryKey(relationId);
        if (agencySecondAppKeyRelation != null) {
            //  校验角色是否存在
            ServiceResult<GetAgencyRoleInfoDTO> agencyRoleInfo = agencyMemberService.getAgencyRoleInfo(agencySecondAppKeyRelation.getAgencyRoleId());
            if (!agencyRoleInfo.isSuccess()) {
                return CommBaseResponse.getFailVO("用车规则不存在");
            }
            // 校验用车规则是否禁用
            GetAgencyRoleInfoDTO getAgencyRoleInfoDTO = agencyRoleInfo.getResult();
            if (getAgencyRoleInfoDTO == null || getAgencyRoleInfoDTO.getDisabled() == 0) {
                return CommBaseResponse.getFailVO("用车规则已经禁用");
            }

            if (getAgencyRoleInfoDTO.getPersonalPayFlag() == 0) {
                return CommBaseResponse.getFailVO("该用车规则未开启个人支付");
            }
        } else {
            return CommBaseResponse.getFailVO("无法找到二级渠道和企业绑定关系");
        }

        String newAgencyId = agencySecondAppKeyRelation.getAgencyId();
        Long newAgencyRoleId = agencySecondAppKeyRelation.getAgencyRoleId();

        // 3. 查询用户当前绑定的企业ID
        String userCurrentAgencyId = member.getAgencyId();
        output.setBindType(1);

        // 新企业信息
        AgencyDiscountDTO newAgencyDiscountDetail = agencyDiscountService.getAgencyDiscountDetail(newAgencyId, null, false);
        if (newAgencyDiscountDetail == null) {
            return CommBaseResponse.getFailVO("渠道下单码对应的企业不存在");
        }

        output.setNewAgencyInfo(newAgencyDiscountDetail);
        // 4. 用户已经有绑定的企业ID
        if (StringUtils.isNotBlank(userCurrentAgencyId) && !("00".equals(userCurrentAgencyId))) {
            // 判断 老企业个人折扣 是否生效
            boolean oldAgencyPersonDiscountIsValid = judgeAgencyDiscountIsValid(member.getAuthId(), userCurrentAgencyId, 1);

            ServiceResult<Void> newAgencyDiscountIsValidResult = agencyMemberService.judgeJoinAgency(newAgencyRoleId, newAgencyId);
            //判断新企业是否满足条件
            boolean newAgencyDiscountIsValid = newAgencyDiscountIsValidResult.isSuccess();

            log.info("用户已经绑定企业,oldAgencyPersonDiscountIsValid={},newAgencyDiscountIsValid={},input={},agencySecondAppKeyRelation={}", oldAgencyPersonDiscountIsValid, newAgencyDiscountIsValid, JSON.toJSONString(input), JSON.toJSONString(agencySecondAppKeyRelation));
            //老企业生效
            if (oldAgencyPersonDiscountIsValid) {
                // 4.1 判断是否与传入的 newAgencyId 相同
                if (newAgencyId.equals(userCurrentAgencyId)) {
                    log.info("用户已经绑定企业，企业且相同，input={}", JSON.toJSONString(input));
                    // 相同：直接返回当前企业信息
                    output.setBindResultFlag(4);
                    output.setBindType(3);
                    output.setOldAgencyInfo(newAgencyDiscountDetail);
                    return CommBaseResponse.getSuccessVO(output);
                } else {
                    AgencyDiscountDTO oldAgencyDiscountDetail = agencyDiscountService.getAgencyDiscountDetail(userCurrentAgencyId, null, true);
                    output.setBindType(2);
                    output.setOldAgencyInfo(oldAgencyDiscountDetail);
                    if (newAgencyDiscountIsValid) {
                        log.info("用户已经绑定企业，企业不相同，新企业折扣有效，input={}", JSON.toJSONString(input));
                        // 新企业 满足条件,让用户自己选择
                        output.setBindResultFlag(3);
                        return CommBaseResponse.getSuccessVO(output);
                    } else {
                        log.info("用户已经绑定企业，企业不相同，新企业折扣无效，input={}", JSON.toJSONString(input));
                        // 新企业 不满足条件,返回报错信息，需要返回老企业信息
                        BaseResponse response = handleBindAgency(() -> newAgencyDiscountIsValidResult, true, newAgencyDiscountDetail.getStatus(), output);
                        return new CommBaseResponse<>(response.getCode(), response.getMessage(), output);
                    }
                }
            } else {
                //老企业无效 -- 尝试切换到新企业
                log.info("用户已经绑定企业，老企业个人折扣无效，input={}", JSON.toJSONString(input));
                // 有企业，,但原企业已经不满足条件
                output.setBindType(4);
                // 需要调用切换企业方法
                BaseResponse response = handleBindAgency(() -> doSwitchAgency(member.getPkId(), agencySecondAppKeyRelation.getAgencyId(), agencySecondAppKeyRelation.getAgencyRoleId()), false, newAgencyDiscountDetail.getStatus(), output);
                return new CommBaseResponse<>(response.getCode(), response.getMessage(), output);
            }
        }

        // 5. 用户未绑定任何企业，执行绑定操作
        BaseResponse response = handleBindAgency(() -> doBindAgency(member.getPkId(), agencySecondAppKeyRelation.getAgencyId(), agencySecondAppKeyRelation.getAgencyRoleId()), false, newAgencyDiscountDetail.getStatus(), output);
        return new CommBaseResponse<>(response.getCode(), response.getMessage(), output);
    }


    /**
     * 判断企业 折扣 是否 生效
     *
     * @param authId
     * @param agencyId
     * @param type     1：个人折扣 2：企业折扣
     * @return
     */
    private boolean judgeAgencyDiscountIsValid(String authId, String agencyId, int type) {
        log.info("判断企业折扣是否生效,auth={},agencyId={},type={}", authId, agencyId, type);
        AgencyDiscountDTO agencyDiscountDTO = agencyDiscountService.getAgencyDiscountDetail(agencyId, null, true);
        if (agencyDiscountDTO == null) {
            log.error("渠道下单码对应的企业不存在,agencyId={}", agencyId);
            return false;
        }
        //门店限制：替换企业用车平台新接口(join网点限制&门店限制)
        ServiceResult<GetAgencyRoleInfoDTO> agencyRole = agencyPriceService.getAgencyRoleInfo(authId);
        GetAgencyRoleInfoDTO getAgencyRoleInfoDTO = agencyRole.getResult();
        if (getAgencyRoleInfoDTO == null) {
            log.error("无法找到用户对应的企业用车平台角色，authId={}", authId);
            return false;
        }
        if (type == 1) {
            if (getAgencyRoleInfoDTO.getPersonalPayFlag().equals(1)) {
                DiscountRuleDTO personDiscountRule = agencyDiscountDTO.getPersonDiscountRule();
                if (personDiscountRule != null) {
                    return true;
                } else {
                    log.error("用户对应的企业不支持个人折扣，authId={},agencyDiscountDTO={}", authId, JSON.toJSONString(agencyDiscountDTO));
                }
            } else {
                log.error("用户对应的企业用车平台角色不支持个人折扣，authId={},getAgencyRoleInfoDTO={}", authId, JSON.toJSONString(getAgencyRoleInfoDTO));
            }
        } else {
            if (getAgencyRoleInfoDTO.getBusinessPayFlag().equals(1)) {
                DiscountRuleDTO agencyDiscountRule = agencyDiscountDTO.getAgencyDiscountRule();
                if (agencyDiscountRule != null) {
                    return true;
                }

            }
        }
        return false;
    }

    @Override
    public CommBaseResponse<BindAgencyBySecondAppKeyRespDto> bindAgencyByShortLinkRandomCode(BindAgencyBySecondAppKeyRelationDTO input) {
        log.info("通过二级渠道绑定企业-start，input={}", JSON.toJSONString(input));
        String randomCode = input.getRandomCode();
        if (StringUtils.isBlank(randomCode)) {
            return CommBaseResponse.getFailVO("参数不能为空");
        }
        AgencySecondAppKeyRelationShortLinkParamsDTO secondAppKeyByRandomCode = getSecondAppKeyByRandomCode(randomCode);
        if (secondAppKeyByRandomCode == null) {
            log.error("无法找到二维码中的渠道企业关联关系，input={}", JSON.toJSONString(input));
            return CommBaseResponse.getFailVO("无法找到二维码中的渠道企业关联关系");
        }

        input.setRelationId(secondAppKeyByRandomCode.getRelationId());
        CommBaseResponse<BindAgencyBySecondAppKeyRespDto> bindAgencyBySecondAppKeyRespDtoCommBaseResponse = bindAgencyBySecondAppKey(input);
        log.info("通过二维码绑定企业-end，input={},result={}", JSON.toJSONString(input), JSON.toJSONString(bindAgencyBySecondAppKeyRespDtoCommBaseResponse));
        return bindAgencyBySecondAppKeyRespDtoCommBaseResponse;
    }

    /**
     * @param randomCode
     * @return
     */
    private AgencySecondAppKeyRelationShortLinkParamsDTO getSecondAppKeyByRandomCode(String randomCode) {
        // 通过短链
        String longUrl = shortlinkService.getLongUrl(randomCode);
        if (StringUtils.isNotBlank(longUrl)) {
            // 从字符串 ?orderPlatform=secondAppKey&relationId=123 中封装
            Map<String, String> stringStringMap = ComUtil.extractParams(longUrl);
            if (stringStringMap != null) {
                String secondAppKey = stringStringMap.get("orderPlatform");
                String relationId = stringStringMap.get("relationId");
                if (StringUtils.isNotBlank(relationId)) {
                    AgencySecondAppKeyRelationShortLinkParamsDTO result = new AgencySecondAppKeyRelationShortLinkParamsDTO();
                    if (StringUtils.isNotBlank(secondAppKey)) {
                        result.setSecondAppKey(secondAppKey);
                    }
                    result.setRelationId(Long.valueOf(relationId));
                    return result;
                }
            }
        }
        return null;
    }


    @Override
    public CommBaseResponse<BindAgencyBySecondAppKeyRespDto> switchAgencyByShortLinkRandomCode(SwitchAgencyDTO input) {
        log.info("通过二级渠道切换企业-start，input={}", JSON.toJSONString(input));
        String randomCode = input.getRandomCode();
        if (StringUtils.isBlank(randomCode)) {
            return CommBaseResponse.getFailVO("参数不能为空");
        }
        AgencySecondAppKeyRelationShortLinkParamsDTO secondAppKeyByRandomCode = getSecondAppKeyByRandomCode(randomCode);
        if (secondAppKeyByRandomCode == null) {
            log.error("无法找到二维码中的渠道企业关联关系，input={}", JSON.toJSONString(input));
            return CommBaseResponse.getFailVO("无法找到二维码中的渠道企业关联关系");
        }
        input.setRelationId(secondAppKeyByRandomCode.getRelationId());

        // 查询企业信息
        AgencySecondAppKeyRelation agencySecondAppKeyRelation = relationMapper.selectByPrimaryKey(input.getRelationId());
        if (agencySecondAppKeyRelation != null) {
            String dbAgencyId = agencySecondAppKeyRelation.getAgencyId();
            if (StringUtils.isNotBlank(dbAgencyId)) {
                input.setNewAgencyId(dbAgencyId);
            }
        } else {
            return CommBaseResponse.getFailVO("无法找到二维码中的企业信息");
        }
        CommBaseResponse<BindAgencyBySecondAppKeyRespDto> result = switchAgency(input);
        log.info("通过二维码切换企业-end，input={},result={}", JSON.toJSONString(input), JSON.toJSONString(result));
        return result;
    }

    /**
     * 处理绑定企业逻辑
     *
     * @param serviceResultSupplier 执行动作
     * @param existedAgency
     * @param agencyStatus
     * @param output
     * @return
     */
    public BaseResponse handleBindAgency(Supplier<ServiceResult> serviceResultSupplier, boolean existedAgency, int agencyStatus, BindAgencyBySecondAppKeyRespDto output) {
        output.setBindResultFlag(1);
        // 企业状态非合作中
        if (agencyStatus != 1) {
            output.setBindResultMsg(ExceptionEnum.AGENCY_STATUS_STOP.getErrMsg());
            return new BaseResponse(ExceptionEnum.AGENCY_STATUS_STOP.getErrCode(), ExceptionEnum.AGENCY_STATUS_STOP.getErrMsg());
        }

        ServiceResult serviceResult = serviceResultSupplier.get();
        if (serviceResult != null && serviceResult.isSuccess()) {
            output.setBindResultFlag(2);
            return BussinessConstants.BASERESPONSE_SUCCESS;
        } else {
            String code = serviceResult.getCode();
            //超过企业折扣人数限制
            if (BussinessConstants.ENTERPRISE_DISCOUNT_EXCEEDS_QUOTA_LIST.contains(code)) {
                if (existedAgency) {
                    output.setBindResultMsg(ExceptionEnum.NEW_AGENCY_DISCOUNT_EXCEEDS_QUOTA.getErrMsg());
                    return new BaseResponse(ExceptionEnum.NEW_AGENCY_DISCOUNT_EXCEEDS_QUOTA.getErrCode(), ExceptionEnum.NEW_AGENCY_DISCOUNT_EXCEEDS_QUOTA.getErrMsg());
                } else {
                    output.setBindResultMsg(ExceptionEnum.AGENCY_DISCOUNT_EXCEEDS_QUOTA.getErrMsg());
                    return new BaseResponse(ExceptionEnum.AGENCY_DISCOUNT_EXCEEDS_QUOTA.getErrCode(), ExceptionEnum.AGENCY_DISCOUNT_EXCEEDS_QUOTA.getErrMsg());
                }
            }

            //不满足企业折扣有效期限制
            if (BussinessConstants.ENTERPRISE_DISCOUNT_EXPIRED_LIST.contains(code)) {
                if (existedAgency) {
                    output.setBindResultMsg(ExceptionEnum.NEW_AGENCY_DISCOUNT_EXPIRED.getErrMsg());
                    return new BaseResponse(ExceptionEnum.NEW_AGENCY_DISCOUNT_EXPIRED.getErrCode(), ExceptionEnum.NEW_AGENCY_DISCOUNT_EXPIRED.getErrMsg());
                } else {
                    output.setBindResultMsg(ExceptionEnum.AGENCY_DISCOUNT_EXPIRED.getErrMsg());
                    return new BaseResponse(ExceptionEnum.AGENCY_DISCOUNT_EXPIRED.getErrCode(), ExceptionEnum.AGENCY_DISCOUNT_EXPIRED.getErrMsg());
                }
            }
            return new BaseResponse(ExceptionEnum.BIND_AGENCY_FAIL.getErrCode(), ExceptionEnum.BIND_AGENCY_FAIL.getErrMsg());
        }
    }


    /**
     * 执行绑定企业逻辑
     *
     * @param pkId
     * @param agencyId
     * @param agencyRoleId
     * @return
     */
    public ServiceResult doBindAgency(Long pkId, String agencyId, Long agencyRoleId) {
        InitialAgencyMembershipTO dto = new InitialAgencyMembershipTO();
        dto.setAgencyId(agencyId);
        dto.setAgencyRoleId(agencyRoleId);
        dto.setMemberShipId(pkId);
        dto.setOperatorId(pkId);
        dto.setOperatorName("evcard-membership");
        dto.setOnlyValidPersonalDiscount(true);
        ServiceResult serviceResult = agencyMemberService.initialAgencyMembership(dto);
        if (serviceResult != null && serviceResult.isSuccess()) {
            log.info("doBindAgency  关联企业成功，dto={}，serviceResult={}", JSON.toJSONString(dto), JSON.toJSONString(serviceResult));
        } else {
            log.error("doBindAgency  失败，dto={}，serviceResult={}", JSON.toJSONString(dto), JSON.toJSONString(serviceResult));
        }
        return serviceResult;
    }

    @Override
    public CommBaseResponse<BindAgencyBySecondAppKeyRespDto> switchAgency(SwitchAgencyDTO switchAgencyDTO) {
        log.info("开始switchAgency  input={}", JSON.toJSONString(switchAgencyDTO));
        String newAgencyId = switchAgencyDTO.getNewAgencyId();
        String oldAgencyId = switchAgencyDTO.getOldAgencyId();
        String mid = switchAgencyDTO.getMid();
        Long relationId = switchAgencyDTO.getRelationId();
        BindAgencyBySecondAppKeyRespDto output = new BindAgencyBySecondAppKeyRespDto();

        if (StringUtils.isBlank(mid) || StringUtils.isBlank(oldAgencyId) || StringUtils.isBlank(newAgencyId) || relationId == null || relationId <= 0l) {
            return CommBaseResponse.getFailVO("参数不能为空");
        }

        MembershipBasicInfo member = membershipWrapService.getMemberByMid(mid);
        if (member == null) {
            return CommBaseResponse.getFailVO("用户不存在");
        }

        // 1. 老企业ID校验
        String agencyId = member.getAgencyId();
        if (!StringUtils.equals(oldAgencyId, agencyId)) {
            log.error("用户绑定企业失败，老企业不一致，agencyId={}, oldAgencyId={},switchAgencyDTO={}", agencyId, oldAgencyId, JSON.toJSONString(switchAgencyDTO));
            return CommBaseResponse.getFailVO("用户未绑定企业");
        }

        // 2. 新企业ID校验
        AgencySecondAppKeyRelation agencySecondAppKeyRelation = relationMapper.selectByPrimaryKey(relationId);
        if (agencySecondAppKeyRelation != null) {
            String dbAgencyId = agencySecondAppKeyRelation.getAgencyId();
            if (!StringUtils.equals(dbAgencyId, newAgencyId)) {
                log.error("用户绑定企业失败，新企业不一致，newAgencyId={}, dbAgencyId={},switchAgencyDTO={}", newAgencyId, dbAgencyId, JSON.toJSONString(switchAgencyDTO));
                return CommBaseResponse.getFailVO("用户未绑定企业");
            }
        } else {
            return CommBaseResponse.getFailVO("无法找到二级渠道对应的企业");
        }

        // 新企业信息
        AgencyDiscountDTO newAgencyDiscountDetail = agencyDiscountService.getAgencyDiscountDetail(newAgencyId, null, false);
        if (newAgencyDiscountDetail == null) {
            return CommBaseResponse.getFailVO("渠道下单码对应的企业不存在");
        }
        // 切换企业
        BaseResponse response = handleBindAgency(() -> doSwitchAgency(member.getPkId(), newAgencyId, agencySecondAppKeyRelation.getAgencyRoleId()), true, newAgencyDiscountDetail.getStatus(), output);
        output.setNewAgencyInfo(newAgencyDiscountDetail);
        output.setBindType(2);
        return new CommBaseResponse<>(response.getCode(), response.getMessage(), output);
    }

    /**
     * 执行绑定企业逻辑
     *
     * @param pkId
     * @param agencyId
     * @param agencyRoleId
     * @return
     */
    public ServiceResult doSwitchAgency(Long pkId, String agencyId, Long agencyRoleId) {
        InitialAgencyMembershipTO dto = new InitialAgencyMembershipTO();
        dto.setMemberShipId(pkId);
        dto.setAgencyId(agencyId);
        dto.setAgencyRoleId(agencyRoleId);
        dto.setOperatorId(pkId);
        dto.setOperatorName("用户");
        dto.setOnlyValidPersonalDiscount(true);
        ServiceResult serviceResult = agencyMemberService.switchMemberAgency(dto);
        if (serviceResult != null && serviceResult.isSuccess()) {
            log.info("doSwitchAgency-切换企业成功，dto={}，serviceResult={}", JSON.toJSONString(dto), JSON.toJSONString(serviceResult));
        } else {
            log.error("doSwitchAgency-切换企业失败，dto={}，serviceResult={}", JSON.toJSONString(dto), JSON.toJSONString(serviceResult));
        }
        return serviceResult;
    }

}
