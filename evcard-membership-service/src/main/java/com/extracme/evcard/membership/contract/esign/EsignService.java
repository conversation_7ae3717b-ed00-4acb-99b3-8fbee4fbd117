package com.extracme.evcard.membership.contract.esign;

import com.extracme.evcard.membership.config.CommConfigUtil;
import com.extracme.evcard.membership.core.enums.IdTypeEnums;
import com.extracme.evcard.membership.core.exception.MemberException;
import com.extracme.evcard.rpc.enums.StatusCode;
import com.timevale.esign.sdk.tech.bean.*;
import com.timevale.esign.sdk.tech.bean.result.*;
import com.timevale.esign.sdk.tech.bean.seal.PersonTemplateType;
import com.timevale.esign.sdk.tech.bean.seal.SealColor;
import com.timevale.esign.sdk.tech.impl.constants.LegalAreaType;
import com.timevale.esign.sdk.tech.impl.constants.LicenseQueryType;
import com.timevale.esign.sdk.tech.impl.constants.SignType;
import com.timevale.esign.sdk.tech.service.AccountService;
import com.timevale.esign.sdk.tech.service.SealService;
import com.timevale.esign.sdk.tech.service.SignService;
import com.timevale.esign.sdk.tech.service.UserSignService;
import com.timevale.esign.sdk.tech.v3.client.ServiceClient;
import com.timevale.esign.sdk.tech.v3.client.ServiceClientManager;
import com.timevale.tech.sdk.bean.HttpConnectionConfig;
import com.timevale.tech.sdk.bean.ProjectConfig;
import com.timevale.tech.sdk.bean.SignatureConfig;
import com.timevale.tech.sdk.constants.AlgorithmType;
import com.timevale.tech.sdk.constants.HttpType;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;

@Slf4j
@Data
public class EsignService {



   /* private static String esignprojectId = "7438843037";
    private static String secret = "61104a734e062f2371ef82c2dc199ab7";
    private static String eBaseUrl = "http://smlitsm.tsign.cn:8080";*/


    public static String appId;

    public static String appKey;

    public static String apiBaseUrl;

    static {
            appId = CommConfigUtil.getEsignAppId();
            appKey = CommConfigUtil.getEsignAppKey();
            apiBaseUrl = CommConfigUtil.getEsignBaseUrl();
    }
   /* private static String appId ;
    private static String appKey ;
    private static String apiBaseUrl ;
    static {
        appId = CommConfigUtil.getEsignAppId();
        appKey = CommConfigUtil.getEsignAppKey();
        apiBaseUrl = CommConfigUtil.getEsignBaseUrl();
    }
*/

    static Result rst = ServiceClientManager.registClient(getProjectCfg(), getHttpConCfg(), getSignatureCfg());
    private static ServiceClient serviceClient = ServiceClientManager.get(appId);

    /*public static void main(String[] args) throws IOException {
        String idNo = "510623199208106516";
        String name = "站6";
        int idType = 1;
        String url = "http://evcard.oss-cn-shanghai.aliyuncs.com/test/member_provision/SZ0099.pdf";

        AccountProfile personAccount = getPersonAccount(idNo, idType);
        String accountUid = personAccount.getAccountUid();
        String accountId = addPersonAccount(idNo, name, idType);
        // updatePersonAccount(accountId,"站6");
        String sealData = addPersonSeal(accountId);
        FileDigestSignResult fileDigestSignResult = signPdfByPerson(url, accountId, sealData, "会员守则.pdf", 450, 710);
        System.out.println(fileDigestSignResult);
    }*/

    private static byte[] getStreamByOssUrl(String url) throws IOException {
        byte[] byteArray;
        InputStream stream = null;
        HttpURLConnection con = null;
        try {
            URL ul = new URL(url);
            con = (HttpURLConnection) ul.openConnection();
            stream = con.getInputStream();
            byteArray = IOUtils.toByteArray(stream);
        } catch (IOException e) {
            log.error("读取oss文件异常：{}", e.getMessage());
            throw new MemberException(StatusCode.UPDATE_FAILED);
        } finally {
            if (stream != null) {
                stream.close();
            }
            if (con != null) {
                IOUtils.close(con);
            }
        }
        return byteArray;
    }


    //
    public static FileDigestSignResult signPdfByPerson(String pdfOssUrl, String accountId, String sealData,
                                                       String fileName, float x, float y) throws IOException {
        log.info("个人={}签署签署合同",accountId);
        SignPDFStreamBean streamBean = new SignPDFStreamBean();
        streamBean.setStream(getStreamByOssUrl(pdfOssUrl));
        streamBean.setFileName(fileName);// 文档名称，e签宝签署日志对应的文档名，若为空则取文档路径中的名称
        PosBean signPos = new PosBean();
        signPos.setPosType(0);// 定位类型，0-坐标定位，1-关键字定位，默认0，SignType为关键字签署的时候，为1，否则为0。用户可以不作处理。此处只是为了兼容旧版本而保留
        signPos.setPosPage("1");// 签署页码，若为多页签章，支持页码格式“1-3,5,8“，若为坐标定位时，不可空
        signPos.setPosX(x);// 签署位置X坐标，若为关键字定位，相对于关键字的X坐标偏移量，默认0
        signPos.setPosY(y);// 签署位置Y坐标，若为关键字定位，相对于关键字的Y坐标偏移量，默认0
        // signPos.setKey("杭州天谷");// 关键字，仅限关键字签章时有效，若为关键字定位时，不可空
        // signPos.setCacellingSign(true);//是否是作废签签署，默认为false;如果签署作废章的话，建议线下也签署一份作废协议，这样法律效力较高
        //signPos.setAddSignTime(true);// 是否显示本地签署时间，需要width设置92以上才可以看到时间
        FileDigestSignResult ret = null;
        try {
            UserSignService signService = serviceClient.userSignService();
            ret = signService.localSignPDF(accountId, sealData, streamBean, signPos, SignType.Single);
            if (ret.getErrCode() != 0 || ret.getStream() == null) {
                throw new MemberException(StatusCode.UPDATE_FAILED);
            }
            verifyPdf(ret.getStream()); // 有点慢，影响效果 后续可以优化
            /*
            // 本地测试 查看签署的pdf文件
            try (FileOutputStream fos = new FileOutputStream("D:\\test.pdf")) {
                fos.write(ret.getStream());
                fos.close();
                System.out.println("文件写入成功，文件路径：");
            } catch (IOException e) {
                System.out.println("文件写入失败：" + e.getMessage());
            }*/
        } catch (Exception e) {
            log.error("E签宝个人签署pdf文件失败，{}", JSONObject.fromObject(ret));
            throw new MemberException(StatusCode.UPDATE_FAILED);
        }
        return ret;
    }

    public static FileDigestSignResult signPdfByPerson(byte[] pdfStream, String accountId, String sealData,
                                                       String fileName, float x, float y) throws IOException {
        log.info("个人={}签署签署合同",accountId);
        SignPDFStreamBean streamBean = new SignPDFStreamBean();
        streamBean.setStream(pdfStream);
        streamBean.setFileName(fileName);// 文档名称，e签宝签署日志对应的文档名，若为空则取文档路径中的名称
        PosBean signPos = new PosBean();
        signPos.setPosType(0);// 定位类型，0-坐标定位，1-关键字定位，默认0，SignType为关键字签署的时候，为1，否则为0。用户可以不作处理。此处只是为了兼容旧版本而保留
        signPos.setPosPage("1");// 签署页码，若为多页签章，支持页码格式“1-3,5,8“，若为坐标定位时，不可空
        signPos.setPosX(x);// 签署位置X坐标，若为关键字定位，相对于关键字的X坐标偏移量，默认0
        signPos.setPosY(y);// 签署位置Y坐标，若为关键字定位，相对于关键字的Y坐标偏移量，默认0
        // signPos.setKey("杭州天谷");// 关键字，仅限关键字签章时有效，若为关键字定位时，不可空
        // signPos.setCacellingSign(true);//是否是作废签签署，默认为false;如果签署作废章的话，建议线下也签署一份作废协议，这样法律效力较高
        //signPos.setAddSignTime(true);// 是否显示本地签署时间，需要width设置92以上才可以看到时间
        FileDigestSignResult ret = null;
        try {
            UserSignService signService = serviceClient.userSignService();
            ret = signService.localSignPDF(accountId, sealData, streamBean, signPos, SignType.Single);
            if (ret.getErrCode() != 0 || ret.getStream() == null) {
                throw new MemberException(StatusCode.UPDATE_FAILED);
            }
            verifyPdf(ret.getStream()); // 有点慢，影响效果 后续可以优化
            /*
            // 本地测试 查看签署的pdf文件
            try (FileOutputStream fos = new FileOutputStream("D:\\test.pdf")) {
                fos.write(ret.getStream());
                fos.close();
                System.out.println("文件写入成功，文件路径：");
            } catch (IOException e) {
                System.out.println("文件写入失败：" + e.getMessage());
            }*/
        } catch (Exception e) {
            log.error("E签宝个人签署pdf文件失败，{}", JSONObject.fromObject(ret));
            throw new MemberException(StatusCode.UPDATE_FAILED);
        }
        log.info("个人={}签署签署合同结束",accountId);
        return ret;
    }

    private static void verifyPdf(byte[] stream) {
        VerifyPdfResult verifyPdfResult = null;
        try {
            SignService signService = serviceClient.signService();
            verifyPdfResult = signService.localVerifyPdf(stream);
            if (verifyPdfResult.getErrCode() != 0) {
                throw new MemberException(StatusCode.UPDATE_FAILED);
            }
        } catch (Exception e) {
            log.error("E签宝验证pdf文件失败，{}", JSONObject.fromObject(verifyPdfResult));
            throw new MemberException(StatusCode.UPDATE_FAILED);
        }
    }

    // 跟新账户（证件号和账户）
    public static void updatePersonAccount(String accountId, String name) {
        Result ret = null;
        try {
            AccountService accountService = serviceClient.accountService();
            ret = accountService.updateAccount(accountId, updatePersonBean(name), new ArrayList<>());
            if (ret.getErrCode() != 0) {
                throw new MemberException(StatusCode.UPDATE_FAILED);
            }
        } catch (Exception e) {
            log.error("E签宝更新个人签署账户异常，{}", JSONObject.fromObject(ret));
            throw new MemberException(StatusCode.UPDATE_FAILED);
        }
    }

    // 创建个人签章
    public static String addPersonSeal(String accountId) {
        AddSealResult addSealResult = null;
        try {
            SealService sealService = serviceClient.sealService();
            addSealResult = sealService.addTemplateSeal(accountId, PersonTemplateType.RECTANGLE, SealColor.RED);
            if (addSealResult.getErrCode() != 0) {
                throw new MemberException(StatusCode.UPDATE_FAILED);
            }
        } catch (Exception e) {
            log.error("E签宝创建个人签章异常，{}", JSONObject.fromObject(addSealResult));
            throw new MemberException(StatusCode.UPDATE_FAILED);
        }
        return addSealResult.getSealData();
    }

    public static String addPersonAccount(String idTypeNo, String name, Integer idType) {
        AddAccountResult ret = null;
        AccountService accountService = null;
        try {
            accountService = serviceClient.accountService();
            ret = accountService.addAccount(getAccountBean(idTypeNo, name, idType));
            if (ret.getErrCode() != 0 || StringUtils.isBlank(ret.getAccountId())) {
                log.error("E签宝初次创建个人签署账户异常，idTypeNo={},name={},idType={},ret={}", idTypeNo,name,idType,JSONObject.fromObject(ret));
                throw new MemberException(StatusCode.UPDATE_FAILED);
            }
        } catch (Exception e) {
            if((IdTypeEnums.GA.getType()==idType || IdTypeEnums.TAIWAI.getType()==idType)
            && (ret!= null && 150020 == ret.getErrCode())) { // 港澳或者台湾类型兜底
                try {
                    ret = accountService.addAccount(getAccountBean(idTypeNo, name, null));
                    if (ret.getErrCode() != 0 || StringUtils.isBlank(ret.getAccountId())) {
                        log.error("E签宝兜底创建个人签署账户异常，idTypeNo={},name={},idType={},ret={}", idTypeNo,name,idType,JSONObject.fromObject(ret));
                        throw new MemberException(StatusCode.UPDATE_FAILED);
                    }
                }catch (Exception e1) {
                    log.error("E签宝兜底创建个人签署账户异常，idTypeNo={},name={},idType={},ret={}" +
                            ",e1={}", idTypeNo,name,idType,JSONObject.fromObject(ret),JSONObject.fromObject(e1));
                    throw new MemberException(StatusCode.UPDATE_FAILED);
                }
            }else {
                log.error("E签宝初次创建个人签署账户异常，idTypeNo={},name={},idType={},ret={}，e={}", idTypeNo,name,idType,JSONObject.fromObject(ret),JSONObject.fromObject(e));
                throw new MemberException(StatusCode.UPDATE_FAILED);
            }

        }
        return ret.getAccountId();
    }

    public static AccountProfile getPersonAccount(String idTypeNo, int idType) {
        // 查询账户
        GetAccountProfileResult ret = null;
        // LicenseQueryType.MAINLAND
        try {
            log.info("serviceClient={}",JSONObject.fromObject(serviceClient));
            AccountService accountService = serviceClient.accountService();
            ret = accountService.getAccountInfoByIdNo(idTypeNo, transferToQueryEsignType(idType));
            if (ret.getErrCode() != 0 || ret.getAccountInfo() == null) {
                log.info("E签宝查询个人签署账户失败：{}",ret);
                return null;// 说明未查到数据，这里不能抛异常
            }
        } catch (Exception e) {
            log.error("E签宝查询个人签署账户异常，{}", JSONObject.fromObject(e.getMessage()));
            throw new MemberException(StatusCode.UPDATE_FAILED);
        }
        return ret.getAccountInfo();
    }

    private static int transferToQueryEsignType(Integer myType) {
        if (myType == null) {
            return LicenseQueryType.OTHER.getType();
        } else if (IdTypeEnums.ID.getType() == myType) {
            return LicenseQueryType.MAINLAND.getType();
        } else if (IdTypeEnums.FOREIGN.getType() == myType) {
            return LicenseQueryType.FOREIGN.getType();
        } else if (IdTypeEnums.GA.getType() == myType) {
            return LicenseQueryType.HONGKONG.getType();// 香港和台湾阈值都是17
        } else if (IdTypeEnums.TAIWAI.getType() == myType) {
            return LicenseQueryType.TAIWAN.getType();
        } else if (IdTypeEnums.SOLDIER.getType() == myType) {
            return LicenseQueryType.SOLDIER_IDNO.getType();
        }
        return LicenseQueryType.OTHER.getType();
    }

    private static LegalAreaType transferToAddEsignType(Integer myType) {
        if (myType == null) {
            return LegalAreaType.OTHER;
        } else if (IdTypeEnums.ID.getType() == myType) {
            return LegalAreaType.MAINLAND;
        } else if (IdTypeEnums.FOREIGN.getType() == myType) {
            return LegalAreaType.FOREIGN;
        } else if (IdTypeEnums.GA.getType() == myType) {
            return LegalAreaType.HONGKONG;// 香港和台湾阈值都是17
        } else if (IdTypeEnums.TAIWAI.getType() == myType) {
            return LegalAreaType.TAIWAN;
        } else if (IdTypeEnums.SOLDIER.getType() == myType) {
            return LegalAreaType.SOLDIER_IDNO;
        }
        return LegalAreaType.OTHER;
    }


    private static UpdatePersonBean updatePersonBean(String name) {
        UpdatePersonBean updatePersonBean = new UpdatePersonBean();
        updatePersonBean.setName(name);
        return updatePersonBean;

    }

    /**
     * description 个人账号信息
     */
    private static PersonBean getAccountBean(String idTypeNo, String name, Integer idType) {
        PersonBean personBean = new PersonBean();
        personBean.setName(name);// 姓名，不可空
        personBean.setIdNo(idTypeNo);// 证件号码，不可空
        personBean.setPersonArea(transferToAddEsignType(idType));// 个人身份证件类型，不可空
        return personBean;
    }

    /**
     * description 进行项目配置，如果是测试环境，请联系E签宝交付顾问获取
     */
    private static ProjectConfig getProjectCfg() {
        ProjectConfig proCfg = new ProjectConfig();
        // 项目ID（应用ID）
        proCfg.setProjectId(appId);
        // 项目Secret(应用Secret)
        proCfg.setProjectSecret(appKey);
        // 开放平台地址
        // 需要添加ip白名单，而且添加之后需要等五分钟之后才能生效，否则会报错：接口调用方尚未配置ip白名单，请联系e签宝管理员配置
        // 正式环境请求地址：http://openapi.tsign.cn:8080/tgmonitor/rest/app!getAPIInfo2
        // 测试环境请求地址：http://smlitsm.tsign.cn:8080/tgmonitor/rest/app!getAPIInfo2
        proCfg.setItsmApiUrl(apiBaseUrl + "/tgmonitor/rest/app!getAPIInfo2");
        log.info("获取e签宝参数{}->{}->{}",appId,appKey,apiBaseUrl);
        return proCfg;
    }

    /**
     * description http配置
     */
    private static HttpConnectionConfig getHttpConCfg() {
        HttpConnectionConfig httpConCfg = new HttpConnectionConfig();
        // 代理服务IP配置
        httpConCfg.setProxyIp(null);
        // 代理服务端口
        httpConCfg.setProxyPort(null);
        // 协议类型，默认Https
        httpConCfg.setHttpType(HttpType.HTTPS);
        // 请求失败重试次数，默认5次
        httpConCfg.setRetry(null);
        //连接超时时间配置，最大不能超过30秒
        httpConCfg.setTimeoutConnect(30);
        // 请求超时时间，最大不能超过30
        httpConCfg.setTimeoutRequest(30);
        // 代理服务器登录用户名
        httpConCfg.setUsername(null);
        // 代理服务器登录密码
        httpConCfg.setPassword(null);
        return httpConCfg;
    }

    /**
     * description 签名配置
     */
    private static SignatureConfig getSignatureCfg() {
        SignatureConfig signCfg = new SignatureConfig();
        signCfg.setAlgorithm(AlgorithmType.HMACSHA256);

        // 若算法类型是RSA，需要设置e签宝公钥和平台私钥
		/*signCfg.setEsignPublicKey(null);
		signCfg.setPrivateKey(null);*/

        return signCfg;
    }

}
