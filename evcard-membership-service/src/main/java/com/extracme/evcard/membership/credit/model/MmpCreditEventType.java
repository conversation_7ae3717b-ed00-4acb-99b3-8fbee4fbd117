package com.extracme.evcard.membership.credit.model;

import java.util.Date;

public class MmpCreditEventType {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_type.id
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_type.event_name
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    private String eventName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_type.event_nature
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    private String eventNature;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_type.amount
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    private Integer amount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_type.event_desc
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    private String eventDesc;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_type.event_way
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    private Integer eventWay;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_type.black_list
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    private Boolean blackList;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_type.misc_desc
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    private String miscDesc;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_type.status
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_type.create_time
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_type.create_oper_id
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    private Long createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_type.create_oper_name
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    private String createOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_type.update_time
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_type.update_oper_id
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    private Long updateOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_type.update_oper_name
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    private String updateOperName;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_type.id
     *
     * @return the value of mmp_credit_event_type.id
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_type.id
     *
     * @param id the value for mmp_credit_event_type.id
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_type.event_name
     *
     * @return the value of mmp_credit_event_type.event_name
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    public String getEventName() {
        return eventName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_type.event_name
     *
     * @param eventName the value for mmp_credit_event_type.event_name
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_type.event_nature
     *
     * @return the value of mmp_credit_event_type.event_nature
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    public String getEventNature() {
        return eventNature;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_type.event_nature
     *
     * @param eventNature the value for mmp_credit_event_type.event_nature
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    public void setEventNature(String eventNature) {
        this.eventNature = eventNature;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_type.amount
     *
     * @return the value of mmp_credit_event_type.amount
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    public Integer getAmount() {
        return amount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_type.amount
     *
     * @param amount the value for mmp_credit_event_type.amount
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_type.event_desc
     *
     * @return the value of mmp_credit_event_type.event_desc
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    public String getEventDesc() {
        return eventDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_type.event_desc
     *
     * @param eventDesc the value for mmp_credit_event_type.event_desc
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    public void setEventDesc(String eventDesc) {
        this.eventDesc = eventDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_type.event_way
     *
     * @return the value of mmp_credit_event_type.event_way
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    public Integer getEventWay() {
        return eventWay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_type.event_way
     *
     * @param eventWay the value for mmp_credit_event_type.event_way
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    public void setEventWay(Integer eventWay) {
        this.eventWay = eventWay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_type.black_list
     *
     * @return the value of mmp_credit_event_type.black_list
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    public Boolean getBlackList() {
        return blackList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_type.black_list
     *
     * @param blackList the value for mmp_credit_event_type.black_list
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    public void setBlackList(Boolean blackList) {
        this.blackList = blackList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_type.misc_desc
     *
     * @return the value of mmp_credit_event_type.misc_desc
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    public String getMiscDesc() {
        return miscDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_type.misc_desc
     *
     * @param miscDesc the value for mmp_credit_event_type.misc_desc
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_type.status
     *
     * @return the value of mmp_credit_event_type.status
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_type.status
     *
     * @param status the value for mmp_credit_event_type.status
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_type.create_time
     *
     * @return the value of mmp_credit_event_type.create_time
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_type.create_time
     *
     * @param createTime the value for mmp_credit_event_type.create_time
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_type.create_oper_id
     *
     * @return the value of mmp_credit_event_type.create_oper_id
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    public Long getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_type.create_oper_id
     *
     * @param createOperId the value for mmp_credit_event_type.create_oper_id
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_type.create_oper_name
     *
     * @return the value of mmp_credit_event_type.create_oper_name
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_type.create_oper_name
     *
     * @param createOperName the value for mmp_credit_event_type.create_oper_name
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_type.update_time
     *
     * @return the value of mmp_credit_event_type.update_time
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_type.update_time
     *
     * @param updateTime the value for mmp_credit_event_type.update_time
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_type.update_oper_id
     *
     * @return the value of mmp_credit_event_type.update_oper_id
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    public Long getUpdateOperId() {
        return updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_type.update_oper_id
     *
     * @param updateOperId the value for mmp_credit_event_type.update_oper_id
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_type.update_oper_name
     *
     * @return the value of mmp_credit_event_type.update_oper_name
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    public String getUpdateOperName() {
        return updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_type.update_oper_name
     *
     * @param updateOperName the value for mmp_credit_event_type.update_oper_name
     *
     * @mbggenerated Wed Nov 22 10:46:53 CST 2017
     */
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }
}