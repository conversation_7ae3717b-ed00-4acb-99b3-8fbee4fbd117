package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.model.MmpShortLinkLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MmpShortLinkLogMapper {

    int deleteByPrimaryKey(Long id);

    int insert(MmpShortLinkLog record);

    int insertSelective(MmpShortLinkLog record);


    MmpShortLinkLog selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MmpShortLinkLog record);

    int updateByPrimaryKey(MmpShortLinkLog record);

    List<MmpShortLinkLog> selectByLinkedId(@Param("linkedId")Long linkedId);
}