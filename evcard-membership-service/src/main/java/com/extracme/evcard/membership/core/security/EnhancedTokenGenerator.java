package com.extracme.evcard.membership.core.security;

import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.dto.JwtTokenDto;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.dto.TokenValidationResult;
import com.extracme.evcard.membership.core.enums.DeviceTypeEnum;
import com.extracme.evcard.membership.core.exception.BusinessException;
import com.extracme.evcard.membership.core.service.ITokenService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.UUID;

/**
 * 增强的Token生成器
 * 整合JWT和传统token生成逻辑，提供向后兼容性
 * 
 * <AUTHOR>
 * @since 2025-08-20
 */
@Slf4j
@Component
public class EnhancedTokenGenerator {
    
    @Autowired
    private ITokenService tokenService;
    
    @Autowired
    private JwtTokenUtil jwtTokenUtil;
    
    /**
     * 生成JWT令牌对（推荐使用）
     * 
     * @param membership 用户基本信息
     * @param appKey 应用标识
     * @param request HTTP请求对象（用于获取设备信息）
     * @return JWT令牌对
     * @throws BusinessException 业务异常
     */
    public JwtTokenDto generateJwtTokenPair(MembershipBasicInfo membership, String appKey, HttpServletRequest request) 
            throws BusinessException {
        
        if (membership == null || StringUtils.isBlank(appKey)) {
            throw new BusinessException(-1, "用户信息或应用标识不能为空");
        }
        
        // 提取设备信息
        String deviceId = extractDeviceId(request);
        String deviceType = extractDeviceType(request);
        String deviceInfo = extractDeviceInfo(request);
        String loginIp = extractClientIp(request);
        String loginLocation = ""; // 可以根据IP获取地理位置
        
        return tokenService.generateTokenPair(
            membership.getPkId(),
            membership.getMid(),
            appKey,
            deviceId,
            deviceType,
            deviceInfo,
            loginIp,
            loginLocation
        );
    }
    
    /**
     * 生成传统格式的token（向后兼容）
     * 
     * @param mid 用户MID
     * @return 传统格式的token字符串
     */
    public String generateLegacyToken(String mid) {
        String sysDate = ComUtil.getSystemDate(ComUtil.DATE_TYPE4);
        StringBuilder sb = new StringBuilder();
        sb.append("evcardToken");
        sb.append(":");
        sb.append(sysDate);
        sb.append("-");
        sb.append(UUID.randomUUID());
        return sb.toString();
    }
    
    /**
     * 验证JWT访问令牌
     * 
     * @param accessToken 访问令牌
     * @return 验证结果
     */
    public TokenValidationResult validateJwtToken(String accessToken) {
        return tokenService.validateAccessToken(accessToken);
    }
    
    /**
     * 刷新JWT访问令牌
     * 
     * @param refreshToken 刷新令牌
     * @return 新的JWT令牌对
     * @throws BusinessException 业务异常
     */
    public JwtTokenDto refreshJwtToken(String refreshToken) throws BusinessException {
        return tokenService.refreshAccessToken(refreshToken);
    }
    
    /**
     * 撤销JWT令牌
     * 
     * @param token 要撤销的令牌
     * @param reason 撤销原因
     * @throws BusinessException 业务异常
     */
    public void revokeJwtToken(String token, String reason) throws BusinessException {
        tokenService.revokeToken(token, reason);
    }
    
    /**
     * 用户登出
     * 
     * @param accessToken 访问令牌
     * @param reason 登出原因
     * @throws BusinessException 业务异常
     */
    public void logout(String accessToken, String reason) throws BusinessException {
        tokenService.logout(accessToken, reason);
    }
    
    /**
     * 检查令牌是否即将过期
     * 
     * @param accessToken 访问令牌
     * @param minutesBeforeExpiry 过期前多少分钟
     * @return true表示即将过期
     */
    public boolean isTokenExpiringSoon(String accessToken, int minutesBeforeExpiry) {
        return tokenService.isTokenExpiringSoon(accessToken, minutesBeforeExpiry);
    }
    
    /**
     * 从HTTP请求中提取设备ID
     */
    private String extractDeviceId(HttpServletRequest request) {
        if (request == null) {
            return "unknown_device_" + System.currentTimeMillis();
        }
        
        // 优先从请求头中获取设备ID
        String deviceId = request.getHeader("X-Device-ID");
        if (StringUtils.isNotBlank(deviceId)) {
            return deviceId;
        }
        
        // 从User-Agent和IP生成设备ID
        String userAgent = request.getHeader("User-Agent");
        String clientIp = extractClientIp(request);
        
        if (StringUtils.isNotBlank(userAgent) && StringUtils.isNotBlank(clientIp)) {
            return "device_" + Math.abs((userAgent + clientIp).hashCode());
        }
        
        return "unknown_device_" + System.currentTimeMillis();
    }
    
    /**
     * 从HTTP请求中提取设备类型
     */
    private String extractDeviceType(HttpServletRequest request) {
        if (request == null) {
            return DeviceTypeEnum.OTHER.getCode();
        }
        
        // 从请求头中获取设备类型
        String deviceType = request.getHeader("X-Device-Type");
        if (StringUtils.isNotBlank(deviceType)) {
            return DeviceTypeEnum.fromCode(deviceType.toUpperCase()).getCode();
        }
        
        // 从User-Agent判断设备类型
        String userAgent = request.getHeader("User-Agent");
        if (StringUtils.isNotBlank(userAgent)) {
            userAgent = userAgent.toLowerCase();
            
            if (userAgent.contains("mobile") || userAgent.contains("android") || userAgent.contains("iphone")) {
                return DeviceTypeEnum.MOBILE.getCode();
            } else if (userAgent.contains("micromessenger")) {
                return DeviceTypeEnum.MINI_PROGRAM.getCode();
            } else if (userAgent.contains("mozilla") || userAgent.contains("chrome") || userAgent.contains("safari")) {
                return DeviceTypeEnum.WEB.getCode();
            }
        }
        
        // 检查是否为API调用
        String contentType = request.getContentType();
        if (StringUtils.isNotBlank(contentType) && contentType.contains("application/json")) {
            return DeviceTypeEnum.API.getCode();
        }
        
        return DeviceTypeEnum.OTHER.getCode();
    }
    
    /**
     * 从HTTP请求中提取设备信息
     */
    private String extractDeviceInfo(HttpServletRequest request) {
        if (request == null) {
            return "Unknown Device";
        }
        
        StringBuilder deviceInfo = new StringBuilder();
        
        String userAgent = request.getHeader("User-Agent");
        if (StringUtils.isNotBlank(userAgent)) {
            deviceInfo.append("UA: ").append(userAgent);
        }
        
        String acceptLanguage = request.getHeader("Accept-Language");
        if (StringUtils.isNotBlank(acceptLanguage)) {
            deviceInfo.append("; Lang: ").append(acceptLanguage);
        }
        
        String platform = request.getHeader("X-Platform");
        if (StringUtils.isNotBlank(platform)) {
            deviceInfo.append("; Platform: ").append(platform);
        }
        
        String appVersion = request.getHeader("X-App-Version");
        if (StringUtils.isNotBlank(appVersion)) {
            deviceInfo.append("; Version: ").append(appVersion);
        }
        
        return deviceInfo.length() > 0 ? deviceInfo.toString() : "Unknown Device";
    }
    
    /**
     * 从HTTP请求中提取客户端IP地址
     */
    private String extractClientIp(HttpServletRequest request) {
        if (request == null) {
            return "unknown";
        }
        
        String ip = request.getHeader("X-Forwarded-For");
        if (StringUtils.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
            // 多级代理的情况下，第一个IP为客户端真实IP
            int index = ip.indexOf(',');
            if (index != -1) {
                ip = ip.substring(0, index);
            }
            return ip.trim();
        }
        
        ip = request.getHeader("X-Real-IP");
        if (StringUtils.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }
        
        ip = request.getHeader("Proxy-Client-IP");
        if (StringUtils.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }
        
        ip = request.getHeader("WL-Proxy-Client-IP");
        if (StringUtils.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }
        
        ip = request.getHeader("HTTP_CLIENT_IP");
        if (StringUtils.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }
        
        ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        if (StringUtils.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }
        
        return request.getRemoteAddr();
    }
}
