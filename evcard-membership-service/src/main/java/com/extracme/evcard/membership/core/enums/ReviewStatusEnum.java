package com.extracme.evcard.membership.core.enums;

public enum ReviewStatusEnum {


    /**
     * 去认证
     */
    ONE("去认证"),
    /**
     * 去上传护照
     */
    TWO("去上传护照"),
    /**
     * 去刷脸
     */
    THREE("去刷脸"),
    /**
     * 去实名认证
     */
    FOUR("去实名认证"),
    /**
     * 认证审核中
     */
    FIVE("认证审核中"),
    /**
     * 认证失败
     */
    SIX("认证失败"),
    /**
     * 已认证
     */
    SEVEN("已认证"),
    /**
     * 驾照已过期
     */
    EIGHT("驾照已过期,请重新认证"),
    /**
     * 请重新认证
     */
    NINE("请重新认证"),
    /**
     * 驾照即将过期,请更新
     */
    TEN("驾照即将过期,请更新"),
    /**
     * 新驾照审核中
     */
    ELEVEN("新驾照审核中"),
    /**
     * 新驾照审核失败
     */
    TWELVE("新驾照审核失败");
    ReviewStatusEnum(String errMsg) {
        this.errMsg = errMsg;
    }

    /** 异常描述信息 */
    private String errMsg;

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }
}
