package com.extracme.evcard.membership.share.service;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.extracme.evcard.membership.share.dao.UserShareRewardInfoMapper;
@Service("membershipShareService")
public class MembershipShareServiceImpl implements IMembershipShareService {
	
	@Resource
	UserShareRewardInfoMapper userShareRewardInfoMapper;

	@Override
	public int shareRewardTimes(String authId) {
		return userShareRewardInfoMapper.shareRewardTimes(authId);
	}

}
