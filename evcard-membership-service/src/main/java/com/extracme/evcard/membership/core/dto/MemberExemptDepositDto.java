package com.extracme.evcard.membership.core.dto;

public class MemberExemptDepositDto {

    /** 是否免押金 0：不免押金 1：免押金 */
    private int exemptDeposit;
    /** 会员来源(0：网点注册 1：网站注册 2：管理平台注册 3:手机APP 4:第三方 5:CRM） */
    private int dataOrigin ;
    /** 机构免押 */
    private int agencyExemptDeposit;
    /** 用户状态 */
    private int userStatus;
    /** 卡号状态 */
    private int cardStatus;

    public int getExemptDeposit() {
        return exemptDeposit;
    }

    public void setExemptDeposit(int exemptDeposit) {
        this.exemptDeposit = exemptDeposit;
    }

    public int getDataOrigin() {
        return dataOrigin;
    }

    public void setDataOrigin(int dataOrigin) {
        this.dataOrigin = dataOrigin;
    }

    public int getAgencyExemptDeposit() {
        return agencyExemptDeposit;
    }

    public void setAgencyExemptDeposit(int agencyExemptDeposit) {
        this.agencyExemptDeposit = agencyExemptDeposit;
    }

    public int getUserStatus() {
        return userStatus;
    }

    public void setUserStatus(int userStatus) {
        this.userStatus = userStatus;
    }

    public int getCardStatus() {
        return cardStatus;
    }

    public void setCardStatus(int cardStatus) {
        this.cardStatus = cardStatus;
    }
}
