package com.extracme.evcard.membership.core.service.auth.login;

import com.extracme.evcard.membership.core.input.ThirdLoginContext;
import com.extracme.evcard.membership.core.input.ThirdLoginInput;
import com.extracme.evcard.membership.core.input.ThirdLoginOtherDto;
import com.extracme.evcard.rpc.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 订单用车人 登录注册
 */
@Slf4j
@Service
public class OrderUseCarMemberLogin extends CommonUserLogin {


    @Override
    public String getSecondAppKey() {
        return "second_user_platform_evcard";
    }

    @Override
    public String getDescription() {
        return "订单用车人登录";
    }

    @Override
    public void paramCheck(ThirdLoginContext context) throws BusinessException {
        super.paramCheck(context);

        ThirdLoginInput input = context.getInput();
        String idCardNo = input.getIdCardNo();

        if (StringUtils.isBlank(idCardNo)) {
            throw new BusinessException(-1, "入参不能为空");
        }

        ThirdLoginOtherDto otherDto = context.getOtherDto();
        otherDto.setNeedIdentitySupplement(true);
    }


}
