package com.extracme.evcard.membership.core.model;

import com.extracme.framework.core.model.Model;

/**
 * 模型类，对应mmp_personal_discount
 */
public class MmpPersonalDiscount extends Model{
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_personal_discount.discount_rule_id
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    private Long discountRuleId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_personal_discount.discount_type
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    private Integer discountType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_personal_discount.discount_rate
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    private Double discountRate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_personal_discount.discount_start_time
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    private Integer discountStartTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_personal_discount.discount_end_time
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    private Integer discountEndTime;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_personal_discount.discount_rule_id
     *
     * @return the value of mmp_personal_discount.discount_rule_id
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    public Long getDiscountRuleId() {
        return discountRuleId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_personal_discount.discount_rule_id
     *
     * @param discountRuleId the value for mmp_personal_discount.discount_rule_id
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    public void setDiscountRuleId(Long discountRuleId) {
        this.discountRuleId = discountRuleId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_personal_discount.discount_type
     *
     * @return the value of mmp_personal_discount.discount_type
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    public Integer getDiscountType() {
        return discountType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_personal_discount.discount_type
     *
     * @param discountType the value for mmp_personal_discount.discount_type
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    public void setDiscountType(Integer discountType) {
        this.discountType = discountType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_personal_discount.discount_rate
     *
     * @return the value of mmp_personal_discount.discount_rate
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    public Double getDiscountRate() {
        return discountRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_personal_discount.discount_rate
     *
     * @param discountRate the value for mmp_personal_discount.discount_rate
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    public void setDiscountRate(Double discountRate) {
        this.discountRate = discountRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_personal_discount.discount_start_time
     *
     * @return the value of mmp_personal_discount.discount_start_time
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    public Integer getDiscountStartTime() {
        return discountStartTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_personal_discount.discount_start_time
     *
     * @param discountStartTime the value for mmp_personal_discount.discount_start_time
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    public void setDiscountStartTime(Integer discountStartTime) {
        this.discountStartTime = discountStartTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_personal_discount.discount_end_time
     *
     * @return the value of mmp_personal_discount.discount_end_time
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    public Integer getDiscountEndTime() {
        return discountEndTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_personal_discount.discount_end_time
     *
     * @param discountEndTime the value for mmp_personal_discount.discount_end_time
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    public void setDiscountEndTime(Integer discountEndTime) {
        this.discountEndTime = discountEndTime;
    }
}