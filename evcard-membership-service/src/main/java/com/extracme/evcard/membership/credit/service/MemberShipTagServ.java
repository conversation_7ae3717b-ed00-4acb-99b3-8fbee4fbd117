package com.extracme.evcard.membership.credit.service;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;

import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.dao.ChannelBlacklistMapper;
import com.extracme.evcard.membership.core.dao.MemberIdentityDocumentMapper;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.dto.blacklist.UpdateChannelBlacklistDto;
import com.extracme.evcard.membership.core.enums.EnableStatusEnum;
import com.extracme.evcard.membership.core.enums.IdTypeEnum;
import com.extracme.evcard.membership.core.exception.AuthenticationException;
import com.extracme.evcard.membership.core.model.ChannelBlacklist;
import com.extracme.evcard.membership.core.model.MemberIdentityDocument;
import com.extracme.evcard.membership.core.service.IChannelBlacklistService;
import com.extracme.evcard.membership.credit.model.*;
import com.extracme.evcard.mq.bean.event.MemberAudit;
import com.extracme.evcard.rpc.enums.StatusCode;
import com.extracme.evcard.rpc.messagepush.service.IMessagepushServ;
import com.extracme.evcard.rpc.order.dto.UserAccountDetailDto;
import com.extracme.evcard.rpc.order.service.IOrderService;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.dao.MmpCreditEventAppealRecordMapper;
import com.extracme.evcard.membership.credit.dao.MmpCreditEventRecordMapper;
import com.extracme.evcard.membership.credit.dao.MmpCreditEventTypeMapper;
import com.extracme.evcard.membership.credit.dao.MmpCreditEventTypeReportMapper;
import com.extracme.evcard.membership.credit.dao.MmpUserTagMapper;
import com.extracme.evcard.membership.credit.dto.AddCreditEventAppealDto;
import com.extracme.evcard.membership.credit.dto.AddCreditEventTypeReportDto;
import com.extracme.evcard.membership.credit.dto.AuthCreditEventRecordPageDto;
import com.extracme.evcard.membership.credit.dto.CreditEventAppealHandleDto;
import com.extracme.evcard.membership.credit.dto.CreditEventAppealRecordDetailDto;
import com.extracme.evcard.membership.credit.dto.CreditEventAppealRecordPageDto;
import com.extracme.evcard.membership.credit.dto.CreditEventAppealRecordParamsDto;
import com.extracme.evcard.membership.credit.dto.CreditEventRecordDetailDto;
import com.extracme.evcard.membership.credit.dto.CreditEventRecordFullDto;
import com.extracme.evcard.membership.credit.dto.CreditEventRecordPageDto;
import com.extracme.evcard.membership.credit.dto.CreditEventRecordParamsDto;
import com.extracme.evcard.membership.credit.dto.CreditEventTypeDto;
import com.extracme.evcard.membership.credit.dto.CreditEventTypeFullDto;
import com.extracme.evcard.membership.credit.dto.CreditEventTypePageDto;
import com.extracme.evcard.membership.credit.dto.CreditEventTypeParamsDto;
import com.extracme.evcard.membership.credit.dto.CreditEventTypeReportPageDto;
import com.extracme.evcard.membership.credit.dto.CreditEventTypeReportParamsDto;
import com.extracme.evcard.membership.credit.dto.MemberShipRecordTagDto;
import com.extracme.evcard.membership.share.service.IMembershipShareService;
import com.extracme.evcard.redis.JedisUtil;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.dto.CommonAddRespDto;
import com.extracme.evcard.rpc.dto.Page;
import com.extracme.evcard.rpc.dto.PageBeanDto;
import javax.annotation.Resource;

/**
 * Created by Elin on 2017/11/22.
 */
@Service
public class MemberShipTagServ implements IMemberShipTagServ {

    @Autowired
    private MmpCreditEventTypeMapper mmpCreditEventTypeMapper;

    @Autowired
    private MmpCreditEventRecordMapper mmpCreditEventRecordMapper;

    @Autowired
    private MmpCreditEventAppealRecordMapper mmpCreditEventAppealRecordMapper;

    @Autowired
    private MmpCreditEventTypeReportMapper mmpCreditEventTypeReportMapper;

    @Autowired
    private MmpUserTagMapper mmpUserTagMapper;

    @Autowired
    private MembershipInfoMapper membershipInfoMapper;
    
    @Autowired
    IMembershipShareService membershipShareService;
    @Autowired
    IChannelBlacklistService iChannelBlacklistService;
    @Autowired
    private ChannelBlacklistMapper channelBlacklistMapper;
    @Autowired
    private MemberIdentityDocumentMapper memberIdentityDocumentMapper;
    @Resource
    IOrderService orderService;

    private com.extracme.evcard.membership.common.ComUtil ComUtil;


    @Override
    public List<CreditEventTypeDto> getCreditEventTypes() {
        return mmpCreditEventTypeMapper.getCreditEventTypes();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonAddRespDto addCreditEventType(CreditEventTypeFullDto creditEventTypeFullDto) {
        MmpCreditEventType mmpCreditEventType = new MmpCreditEventType();
        if (creditEventTypeFullDto == null) {
            return new CommonAddRespDto(-1, "传入的参数为空");
        }
        if (!(creditEventTypeFullDto.getAmount() instanceof Integer)) {
            return new CommonAddRespDto(-2, "分值不是数值");
        }
        if (StringUtils.isBlank(creditEventTypeFullDto.getEventName())) {
            return new CommonAddRespDto(-3, "事件类型名称不能为空");
        }
        if (StringUtils.isNotBlank(creditEventTypeFullDto.getEventName()) &&
                creditEventTypeFullDto.getEventName().length() > 20) {
            return new CommonAddRespDto(-3, "事件类型名称太长，最多20个字符");
        }
        if (!"1".equals(creditEventTypeFullDto.getEventNature()) &&
                !"0".equals(creditEventTypeFullDto.getEventNature())) {
            return new CommonAddRespDto(-4, "事件性质必须为1、0");
        }
        if (creditEventTypeFullDto.getCreateOperId() == null ||
                creditEventTypeFullDto.getCreateOperId() == -1) {
            return new CommonAddRespDto(-5, "操作人id不能为空");
        }
        if (StringUtils.isBlank(creditEventTypeFullDto.getCreateOperName())) {
            return new CommonAddRespDto(-5, "操作人姓名不能为空");
        }
        creditEventTypeFullDto.setCreateTime(new Date());
        creditEventTypeFullDto.setUpdateOperId(creditEventTypeFullDto.getCreateOperId());
        creditEventTypeFullDto.setUpdateOperName(creditEventTypeFullDto.getCreateOperName());
        creditEventTypeFullDto.setUpdateTime(creditEventTypeFullDto.getCreateTime());
        creditEventTypeFullDto.setEventWay(1);//手动
        creditEventTypeFullDto.setBlackList(false);//否
        BeanUtils.copyProperties(creditEventTypeFullDto, mmpCreditEventType);
        mmpCreditEventTypeMapper.insertSelective(mmpCreditEventType);
        saveCreditTypeToRedis(mmpCreditEventType, 1);
        return new CommonAddRespDto(0, "", mmpCreditEventType.getId());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonAddRespDto updateCreditEventType(CreditEventTypeFullDto creditEventTypeFullDto) {
        MmpCreditEventType mmpCreditEventType = new MmpCreditEventType();
        if (creditEventTypeFullDto == null) {
            return new CommonAddRespDto(-1, "传入的参数为空");
        }
        if (!(creditEventTypeFullDto.getAmount() instanceof Integer)) {
            return new CommonAddRespDto(-2, "分值不是数值");
        }
        if (creditEventTypeFullDto.getUpdateOperId() == null || creditEventTypeFullDto.getUpdateOperId() == -1) {
            return new CommonAddRespDto(-5, "修改人id不能为空");
        }
        if (StringUtils.isBlank(creditEventTypeFullDto.getUpdateOperName())) {
            return new CommonAddRespDto(-5, "修改人姓名不能为空");
        }
        creditEventTypeFullDto.setUpdateTime(new Date());
        BeanUtils.copyProperties(creditEventTypeFullDto, mmpCreditEventType);
        int result = mmpCreditEventTypeMapper.updateByPrimaryKeySelective(mmpCreditEventType);
        if (result == 0) {
            return new CommonAddRespDto(-6, "该事件类型不存在");
        }
        saveCreditTypeToRedis(mmpCreditEventType, 2);
        return new CommonAddRespDto(0, "", mmpCreditEventType.getId());
    }

    @Override
    public PageBeanDto<CreditEventTypePageDto> getCreditEventTypeListPage(CreditEventTypeParamsDto paramsDto,
                                                                          Page page) {
        page = new Page(page.getPageNo(), page.getPageSize(), page.getCountFlag());
        if (page.getCountFlag()) {
            Integer count = mmpCreditEventTypeMapper.getCreditEventTypeCount(paramsDto);
            page.setCount(count);
        }
        List<CreditEventTypePageDto> pageDtos = mmpCreditEventTypeMapper.getCreditEventTypePages(paramsDto, page);
        PageBeanDto<CreditEventTypePageDto> pageDtoPageBeanDto = new PageBeanDto<>();
        pageDtoPageBeanDto.setPage(page);
        pageDtoPageBeanDto.setList(pageDtos);
        return pageDtoPageBeanDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonAddRespDto saveCreditEventRecord(CreditEventRecordFullDto creditEventRecordFullDto) {
        MmpCreditEventRecord mmpCreditEventRecord = new MmpCreditEventRecord();
        if (creditEventRecordFullDto == null) {
            return new CommonAddRespDto(-1, "传入的参数为空");
        }
        if (StringUtils.isBlank(creditEventRecordFullDto.getAuthId())) {
            return new CommonAddRespDto(-2, "会员id不能为空");
        }
        if (StringUtils.isBlank(creditEventRecordFullDto.getEventDesc())) {
            return new CommonAddRespDto(-3, "事件记录详情不能为空");
        }
        if ((StringUtils.isBlank(creditEventRecordFullDto.getEventImagePath()) ||
                (StringUtils.isNotBlank(creditEventRecordFullDto.getEventImagePath())
                        && creditEventRecordFullDto.getEventImagePath().split(",").length == 0)) &&
                (StringUtils.isBlank(creditEventRecordFullDto.getEventFilePath()) ||
                        (StringUtils.isNotBlank(creditEventRecordFullDto.getEventFilePath())
                                && creditEventRecordFullDto.getEventFilePath().split(",").length == 0))) {
            return new CommonAddRespDto(-4, "事件凭证图片和文件不能同时为空");
        }
        if (StringUtils.isNotBlank(creditEventRecordFullDto.getEventDesc()) &&
                creditEventRecordFullDto.getEventDesc().length() > 500) {
            return new CommonAddRespDto(-5, "事件记录详情太长，最多500个字符");
        }
        if (StringUtils.isNotBlank(creditEventRecordFullDto.getEventImagePath()) &&
                creditEventRecordFullDto.getEventImagePath().split(",").length > 10) {
            return new CommonAddRespDto(-6, "事件图片凭证最多10张");
        }
        if (!(creditEventRecordFullDto.getAmount() instanceof Integer)) {
            return new CommonAddRespDto(-7, "分值不是数值");
        }
        if (StringUtils.isBlank(creditEventRecordFullDto.getEventTypeId() + "")) {
            return new CommonAddRespDto(-8, "事件类型id不能为空");
        }
        if (StringUtils.isBlank(creditEventRecordFullDto.getEventName())) {
            return new CommonAddRespDto(-9, "事件类型名称不能为空");
        }
        if (StringUtils.isNotBlank(creditEventRecordFullDto.getEventName()) &&
                creditEventRecordFullDto.getEventName().length() > 20) {
            return new CommonAddRespDto(-9, "事件类型名称太长，最多20个字符");
        }
        if (!"1".equals(creditEventRecordFullDto.getEventNature()) &&
                !"0".equals(creditEventRecordFullDto.getEventNature())) {
            return new CommonAddRespDto(-10, "事件性质必须为1、0");
        }
        if (creditEventRecordFullDto.getBlackList() == null) {
            return new CommonAddRespDto(-11, "是否加入黑名单不能为空");
        }
        if (StringUtils.isBlank(creditEventRecordFullDto.getEventSource())) {
            creditEventRecordFullDto.setEventSource(StringUtils.EMPTY);
        }
        if (creditEventRecordFullDto.getCreateOperId() == null ||
                creditEventRecordFullDto.getCreateOperId() == -1) {
            return new CommonAddRespDto(-12, "操作人id不能为空");
        }
        if (StringUtils.isBlank(creditEventRecordFullDto.getCreateOperName())) {
            return new CommonAddRespDto(-12, "操作人姓名不能为空");
        }
        creditEventRecordFullDto.setCreateTime(new Date());
        creditEventRecordFullDto.setUpdateOperId(creditEventRecordFullDto.getCreateOperId());
        creditEventRecordFullDto.setUpdateOperName(creditEventRecordFullDto.getCreateOperName());
        creditEventRecordFullDto.setUpdateTime(creditEventRecordFullDto.getCreateTime());
        creditEventRecordFullDto.setStatus(1);
        BeanUtils.copyProperties(creditEventRecordFullDto, mmpCreditEventRecord);
        mmpCreditEventRecordMapper.insertSelective(mmpCreditEventRecord);
        //增减信用分 如果是 非法用车 和 行业黑名单的信用，则将会员信用分直接扣至0
        if (mmpCreditEventRecord.getEventTypeId() == 1L || mmpCreditEventRecord.getEventTypeId() == 2L) {
            mmpUserTagMapper.updateCreditZeroAmountByAuthId(mmpCreditEventRecord.getAuthId());
        } else {
            mmpUserTagMapper.updateCreditAmountByAuthId(mmpCreditEventRecord.getAuthId(),
                    creditEventRecordFullDto.getAmount());
        }
        //加入黑名单
        if (mmpCreditEventRecord.getBlackList()) {
            membershipInfoMapper.updateMemberShipStatusByAuthId(mmpCreditEventRecord.getAuthId());

            //同步到黑名单表
            //MembershipInfoWithBLOBs membershipInfo = membershipInfoMapper.selectByAuthId2(mmpCreditEventRecord.getAuthId());
            //新方法
            MembershipInfoWithBLOBs membershipInfo = membershipInfoMapper.selectByAuthId(mmpCreditEventRecord.getAuthId(), 0);
            if (membershipInfo == null){
                return new CommonAddRespDto(-12,"用户不存在，同步到黑名单失败");
            }
            //证件类型 1：身份证 2:护照 3：香港澳门通行证 4 台湾通行证 5:军人身份证
            MembershipInfo userInfo = membershipInfoMapper.getIdentityByPkId(membershipInfo.getPkId());
            String certificateNum = userInfo.getPassportNo();
            if (StringUtils.isNotBlank(certificateNum)){
               List<ChannelBlacklist> channelBlacklists = channelBlacklistMapper.queryByMobileAndCertificateNum(membershipInfo.getMobilePhone(), certificateNum,null);
                if (CollectionUtils.isNotEmpty(channelBlacklists) && channelBlacklists.size() > 0){
                    //黑名单已经存在数据，则更新状态为启用
                    channelBlacklists.forEach(channelBlacklist->{
                        UpdateChannelBlacklistDto updateChannelBlacklistDto = new UpdateChannelBlacklistDto();
                        BeanUtils.copyProperties(channelBlacklist, updateChannelBlacklistDto);
                        updateChannelBlacklistDto.setId(channelBlacklist.getId().intValue());
                        updateChannelBlacklistDto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                        updateChannelBlacklistDto.setOperateUser("个人会员数据同步");
                        iChannelBlacklistService.updateById(updateChannelBlacklistDto);
                    });
                }else {
                    //黑名单不存在，则做新增
                    UpdateChannelBlacklistDto updateChannelBlacklistDto = new UpdateChannelBlacklistDto();
                    updateChannelBlacklistDto.setUserName(membershipInfo.getName());
                    updateChannelBlacklistDto.setMobilePhone(membershipInfo.getMobilePhone());
                    updateChannelBlacklistDto.setBlacklistType(BussinessConstants.BLACKLIST_TYPE_BLACK);
                    updateChannelBlacklistDto.setRemark(creditEventRecordFullDto.getEventDesc());
                    updateChannelBlacklistDto.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
                    updateChannelBlacklistDto.setOperateUser("个人会员数据同步");
                    updateChannelBlacklistDto.setBlacklistSource(2);
                    updateChannelBlacklistDto.setCertificateType(membershipInfo.getIdType() == IdTypeEnum.ID_CARD.getValue() ? 1 : 2);
                    updateChannelBlacklistDto.setCertificateNum(certificateNum);
                    iChannelBlacklistService.insert(updateChannelBlacklistDto);
                }
            }
        }
        return new CommonAddRespDto(0, "", mmpCreditEventRecord.getId());
    }

    @Override
    public PageBeanDto<AuthCreditEventRecordPageDto> getAuthCreditEventRecordListPage(String authId, Page page) {
        page = new Page(page.getPageNo(), page.getPageSize(), page.getCountFlag());
        if (page.getCountFlag()) {
            Integer count = mmpCreditEventRecordMapper.getCreditEventCountByAuthId(authId);
            page.setCount(count);
        }
        List<AuthCreditEventRecordPageDto> pageDtos =
                mmpCreditEventRecordMapper.getCreditEventPagesByAuthId(authId, page);
        PageBeanDto<AuthCreditEventRecordPageDto> pageDtoPageBeanDto = new PageBeanDto<>();
        pageDtoPageBeanDto.setPage(page);
        pageDtoPageBeanDto.setList(pageDtos);
        return pageDtoPageBeanDto;
    }

    @Override
    public CreditEventRecordDetailDto getCreditEventRecordDetail(String authId, Long eventId) {
        //查询事件记录
        CreditEventRecordDetailDto eventRecordDetailDto =
                mmpCreditEventRecordMapper.getCreditEventDetailByAuthIdAndEventId(authId, eventId);
        if (null != eventRecordDetailDto) {
            //查询申诉记录
            CreditEventAppealRecordDetailDto eventAppealRecord =
                    mmpCreditEventAppealRecordMapper.getCreditEventAppealDetailRecordByAuthIdAndEventId(authId, eventId);
            if (null != eventAppealRecord) {
                BeanUtils.copyProperties(eventAppealRecord, eventRecordDetailDto);
            }
        } else {
            return new CreditEventRecordDetailDto(-1, "无记录");
        }
        return eventRecordDetailDto;
    }

    @Override
    public PageBeanDto<CreditEventAppealRecordPageDto> getCreditEventAppealRecordPage(
            CreditEventAppealRecordParamsDto paramsDto, Page page) {
        page = new Page(page.getPageNo(), page.getPageSize(), page.getCountFlag());
        String endTime = paramsDto.getAppealEndTime();
        if (StringUtils.isNotBlank(endTime)) {
            try {
                Date date = DateFormatUtils.ISO_DATE_FORMAT.parse(endTime);
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(date);
                calendar.add(Calendar.DAY_OF_MONTH, 1);
                date = calendar.getTime();
                endTime = DateFormatUtils.ISO_DATE_FORMAT.format(date);
                paramsDto.setAppealEndTime(endTime);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        if (page.getCountFlag()) {
            Integer count = mmpCreditEventAppealRecordMapper.getCreditEventAppealRecordCount(paramsDto);
            page.setCount(count);
        }
        List<CreditEventAppealRecordPageDto> pageDtos =
                mmpCreditEventAppealRecordMapper.getCreditEventAppealRecordPages(paramsDto, page);
        PageBeanDto<CreditEventAppealRecordPageDto> pageDtoPageBeanDto = new PageBeanDto<>();
        pageDtoPageBeanDto.setPage(page);
        pageDtoPageBeanDto.setList(pageDtos);
        return pageDtoPageBeanDto;
    }

    @Override
    public PageBeanDto<CreditEventRecordPageDto> getCreditEventRecordPage(CreditEventRecordParamsDto paramsDto, Page page) {
        page = new Page(page.getPageNo(), page.getPageSize(), page.getCountFlag());
        String endTime = paramsDto.getEventEndTime();
        if (StringUtils.isNotBlank(endTime)) {
            try {
                Date date = DateFormatUtils.ISO_DATE_FORMAT.parse(endTime);
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(date);
                calendar.add(Calendar.DAY_OF_MONTH, 1);
                date = calendar.getTime();
                endTime = DateFormatUtils.ISO_DATE_FORMAT.format(date);
                paramsDto.setEventEndTime(endTime);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        if (page.getCountFlag()) {
            Integer count = mmpCreditEventRecordMapper.getCreditEventRecordCount(paramsDto);
            page.setCount(count);
        }
        List<CreditEventRecordPageDto> pageDtos =
                mmpCreditEventRecordMapper.getCreditEventRecordPages(paramsDto, page);
        PageBeanDto<CreditEventRecordPageDto> pageDtoPageBeanDto = new PageBeanDto<>();
        pageDtoPageBeanDto.setPage(page);
        pageDtoPageBeanDto.setList(pageDtos);
        return pageDtoPageBeanDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse handleAppealEventStatus(CreditEventAppealHandleDto creditEventAppealHandleDto) {
        if (creditEventAppealHandleDto.getHandleResult() == null ||
                (1 != creditEventAppealHandleDto.getHandleResult())
                        && 2 != creditEventAppealHandleDto.getHandleResult()) {
            return new BaseResponse(-1, "处理状态错误（1-同意 2-拒绝）");
        }
        if (StringUtils.isBlank(creditEventAppealHandleDto.getHandleUserId()) &&
                StringUtils.isBlank(creditEventAppealHandleDto.getHandleUser())) {
            return new BaseResponse(-2, "处理人不能为空");
        }
        if (2 == creditEventAppealHandleDto.getHandleResult() &&
                StringUtils.isBlank(creditEventAppealHandleDto.getHandleRemark())) {
            return new BaseResponse(-3, "请填写通知话术");
        }
        if (creditEventAppealHandleDto.getUpdateOperId() == null ||
                creditEventAppealHandleDto.getUpdateOperId() == -1 ||
                StringUtils.isBlank(creditEventAppealHandleDto.getUpdateOperName())) {
            return new BaseResponse(-4, "操作人不能为空");
        }
        MmpCreditEventAppealRecord appealRecord = mmpCreditEventAppealRecordMapper.
                getCreditEventAppealRecordById(creditEventAppealHandleDto.getAppealId(), creditEventAppealHandleDto.getAuthId());
        if (null == appealRecord) {
            return new BaseResponse(-5, "申诉事件不存在！");
        }
        int result = mmpCreditEventAppealRecordMapper.updateAppealEventHandleStatus(creditEventAppealHandleDto);
        if (result != 1) {
            return new BaseResponse(-6, "该申诉事件处理性质已改变");
        }
        //同意则撤销事件
        if (1 == creditEventAppealHandleDto.getHandleResult()) {
            mmpCreditEventRecordMapper.updateCreditEventRecordStatusByEventIdAndAuthId(
                    creditEventAppealHandleDto.getEventId(), creditEventAppealHandleDto.getAuthId(),
                    creditEventAppealHandleDto.getUpdateOperId(), creditEventAppealHandleDto.getUpdateOperName());
        }
        return new BaseResponse(0, "");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonAddRespDto saveAppealEvent(AddCreditEventAppealDto addCreditEventAppealDto) {
        if (addCreditEventAppealDto == null) {
            return new CommonAddRespDto(-1, "传入的参数为空");
        }
        if (StringUtils.isBlank(addCreditEventAppealDto.getAuthId())) {
            return new CommonAddRespDto(-2, "会员id不能为空");
        }
        if (addCreditEventAppealDto.getEventId() == null || addCreditEventAppealDto.getEventId() == -1) {
            return new CommonAddRespDto(-3, "事件编号不能为空");
        }
        if (StringUtils.isBlank(addCreditEventAppealDto.getAppealDesc()) &&
                (StringUtils.isBlank(addCreditEventAppealDto.getAppealImagePath()) ||
                        (StringUtils.isNotBlank(addCreditEventAppealDto.getAppealImagePath())
                                && addCreditEventAppealDto.getAppealImagePath().split(",").length == 0)) &&
                (StringUtils.isBlank(addCreditEventAppealDto.getAppealFilePath()) ||
                        (StringUtils.isNotBlank(addCreditEventAppealDto.getAppealFilePath())
                                && addCreditEventAppealDto.getAppealFilePath().split(",").length == 0))) {
            return new CommonAddRespDto(-4, "申诉描述和申诉凭证不能同时为空");
        }
        if (StringUtils.isNotBlank(addCreditEventAppealDto.getAppealDesc()) &&
                addCreditEventAppealDto.getAppealDesc().length() > 500) {
            return new CommonAddRespDto(-5, "申诉描述太长，最多500个字符");
        }
        if (StringUtils.isNotBlank(addCreditEventAppealDto.getAppealImagePath()) &&
                addCreditEventAppealDto.getAppealImagePath().split(",").length > 10) {
            return new CommonAddRespDto(-6, "申诉图片凭证最多10张");
        }
        //查询事件是否存在
        MmpCreditEventRecord mmpCreditEventRecord = mmpCreditEventRecordMapper.getCreditEventByAuthIdAndEventId(
                addCreditEventAppealDto.getAuthId(), addCreditEventAppealDto.getEventId());
        if (null == mmpCreditEventRecord) {
            return new CommonAddRespDto(-7, "事件不存在");
        }
        //查询是否已有申诉记录 且状态为未处理的记录
        MmpCreditEventAppealRecord mmpCreditEventAppealRecord =
                mmpCreditEventAppealRecordMapper.getCreditEventAppealRecordByAuthIdAndEventId(
                        addCreditEventAppealDto.getAuthId(), addCreditEventAppealDto.getEventId());
        if (null != mmpCreditEventAppealRecord) {
            return new CommonAddRespDto(-8, "存在未处理的申诉记录，不可重复提交");
        }
        MmpCreditEventAppealRecord appealRecord = new MmpCreditEventAppealRecord();
        BeanUtils.copyProperties(addCreditEventAppealDto, appealRecord);
        mmpCreditEventAppealRecordMapper.saveNewCreditEventAppealRecord(appealRecord);
        return new CommonAddRespDto(0, "", appealRecord.getId());
    }

    @Override
    public PageBeanDto<CreditEventTypeReportPageDto> getCreditEventTypeReportPage(CreditEventTypeReportParamsDto paramsDto, Page page) {
        page = new Page(page.getPageNo(), page.getPageSize(), page.getCountFlag());
        if (page.getCountFlag()) {
            Integer count = mmpCreditEventTypeReportMapper.getCreditEventTypeReportCount(paramsDto);
            page.setCount(count);
        }
        //查询所有类型
        List<Integer> eventTypeIds = mmpCreditEventTypeReportMapper.getCreditEventTypeIdReportPages(paramsDto, page);
        PageBeanDto<CreditEventTypeReportPageDto> pageDtoPageBeanDto = new PageBeanDto<>();
        List<CreditEventTypeReportPageDto> pageDtos = new ArrayList<>();
        if (eventTypeIds != null && eventTypeIds.size() > 0) {
            pageDtos =
                    mmpCreditEventTypeReportMapper.getCreditEventTypeReportPages(paramsDto, eventTypeIds);
        }
        pageDtoPageBeanDto.setPage(page);
        pageDtoPageBeanDto.setList(pageDtos);
        return pageDtoPageBeanDto;
    }

    @Override
    public PageBeanDto<CreditEventTypeReportPageDto> getCreditEventTypeReportExport(CreditEventTypeReportParamsDto paramsDto) {
        //查询所有类型
        List<Integer> eventTypeIds = mmpCreditEventTypeReportMapper.getCreditEventTypeIdReportExport(paramsDto);
        PageBeanDto<CreditEventTypeReportPageDto> pageDtoPageBeanDto = new PageBeanDto<>();
        List<CreditEventTypeReportPageDto> pageDtos = new ArrayList<>();
        if (eventTypeIds != null && eventTypeIds.size() > 0) {
            pageDtos =
                    mmpCreditEventTypeReportMapper.getCreditEventTypeReportPages(paramsDto, eventTypeIds);
        }
        pageDtoPageBeanDto.setList(pageDtos);
        return pageDtoPageBeanDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonAddRespDto saveCreditEventTypeReport(AddCreditEventTypeReportDto addCreditEventTypeReportDto) {
        if (addCreditEventTypeReportDto.getTotal() == null) {
            addCreditEventTypeReportDto.setTotal(0);
        }
        MmpCreditEventTypeReport mmpCreditEventTypeReport = new MmpCreditEventTypeReport();
        BeanUtils.copyProperties(addCreditEventTypeReportDto, mmpCreditEventTypeReport);
        mmpCreditEventTypeReportMapper.insertSelective(mmpCreditEventTypeReport);
        return new CommonAddRespDto(0, "", mmpCreditEventTypeReport.getId());
    }

    /**
     * redis保存
     *
     * @param mmpCreditEventType
     */
    private void saveCreditTypeToRedis(MmpCreditEventType mmpCreditEventType, Integer optType) {
        Map<String, String> resultMap = new HashMap<String, String>();
        //更新时只更新amount
        if ("1".equals(optType)) {
            resultMap.put("id", String.valueOf(mmpCreditEventType.getId()));
            resultMap.put("eventName", mmpCreditEventType.getEventName());
            resultMap.put("eventNature", mmpCreditEventType.getEventNature());
        }
        resultMap.put("amount", String.valueOf(mmpCreditEventType.getAmount()));
        JedisUtil.hmset("credit_event_type_" + mmpCreditEventType.getId(), resultMap);
        JedisUtil.expire("credit_event_type_" + mmpCreditEventType.getId(), 3600);
    }

    /**
     * 现阶段通过统计支付记录表，后期应该调整为查看user_tag表.<br>
     */
    @Override
    @Deprecated
    public BigDecimal consumAmount(String authId) {
        return this.memberShipRecordTag(authId).getConsumAmount();
    }

    @Override
    public MemberShipRecordTagDto memberShipRecordTag(String authId) {
        UserAccountDetailDto orderStatistics = orderService.queryUserOrderStatistics(authId, Arrays.asList(0), null);
        MemberShipRecordTagDto recordTag = new MemberShipRecordTagDto();
        if(orderStatistics != null) {
            recordTag.setConsumLength(orderStatistics.getCostTime());
            recordTag.setConsumTimes(orderStatistics.getOrderCount());
            //现金总额+充值e币总额
            BigDecimal totalAmount = new BigDecimal(orderStatistics.getRealAmount()).add(orderStatistics.getBillTimeRecharge());
            recordTag.setConsumAmount(totalAmount);
        }
        if (recordTag.getConsumAmount() == null) {
            recordTag.setConsumAmount(BigDecimal.ZERO);
        }
        recordTag.setInvitAwardsTimes(membershipShareService.shareRewardTimes(authId));
        return recordTag;
    }

}
