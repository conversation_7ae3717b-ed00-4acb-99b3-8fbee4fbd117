<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.core.dao.CityMapper">


    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.City">
        <id column="ID" property="id" />
        <result column="CITYID" property="cityid" />
        <result column="CITY" property="city" />
        <result column="FATHERID" property="fatherid" />
        <result column="LON" property="lon" />
        <result column="LAT" property="lat" />
        <result column="STATUS" property="status" />
        <result column="org_id" property="orgId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, CITYID, CITY, FATHERID, LON, LAT, STATUS, org_id
    </sql>


    <select id="queryCityInfoByShopSeq" resultType="com.extracme.evcard.membership.core.model.City">
        SELECT
        c.CITY AS city,
        c.org_id AS orgId
        FROM
        ${siacSchema}.shop_info a
        LEFT JOIN ${siacSchema}.area b ON a.AREA_CODE = b.AREAID
        LEFT JOIN ${siacSchema}.city c ON b.FATHERID = c.CITYID
        WHERE
        a.SHOP_SEQ = #{shopSeq}
    </select>

    <select id="selectCity" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM ${siacSchema}.city
        WHERE CITY like #{cityName}
    </select>

    <select id="getCityByName" resultType="com.extracme.evcard.membership.core.model.City">
        SELECT
        <include refid="Base_Column_List" />
        FROM ${siacSchema}.city
        WHERE CITY = #{cityName} limit 1
    </select>

    <select id="getCityByCityId" resultType="com.extracme.evcard.membership.core.model.City">
        SELECT
        <include refid="Base_Column_List" />
        FROM ${siacSchema}.city
        WHERE CITYID = #{cityId} limit 1
    </select>


    <select id="queryOrgNameByCity" resultType="string">
        SELECT
            b.ORG_NAME
        FROM
            siac.city a
            JOIN isv.org_info b ON a.org_id = b.ORG_ID
        WHERE    a.CITY LIKE concat(#{cityName},'%')
    </select>

    <!-- 根据城市获取分公司 -->
    <select id="queryCityOrg" parameterType="string"
            resultType="String">
        SELECT
            org_id
        FROM
            ${siacSchema}.CITY WHERE city = #{city,jdbcType=VARCHAR}
    </select>
</mapper>
