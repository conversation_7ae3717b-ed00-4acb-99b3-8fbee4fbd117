package com.extracme.evcard.membership.core.dto;

import com.extracme.evcard.membership.core.dto.input.AuditIdCardInput;
import com.extracme.evcard.membership.core.model.MemberIdentityDocument;
import lombok.Data;

import java.util.Date;

@Data
public class AuditIdCardDTO {

    private AuditIdCardInput auditIdCardInput;

    private MembershipBasicInfo membershipBasicInfo;

    private MemberIdentityDocument memberIdentityDocument;

    //是否是新用户
    Boolean newUser;

    //审核时间，方便统一
    Date operaterDate;

}
