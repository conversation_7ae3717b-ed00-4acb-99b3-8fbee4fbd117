package com.extracme.evcard.membership.mq;

import com.aliyun.openservices.ons.api.MessageListener;
import com.aliyun.openservices.ons.api.PropertyKeyConst;
import com.aliyun.openservices.ons.api.bean.ConsumerBean;
import com.aliyun.openservices.ons.api.bean.Subscription;
import com.extracme.evcard.membership.config.AliyunConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;


@Configuration
public class OnsConsumer {

    @Autowired
    private AliyunConfig aliyunConfig;

    @Autowired
    private RawListener rawListener;

    @Autowired
    private MemberCreditsListener memberCreditsListener;

    @Autowired
    private AnalysisListener analysisListener;

    @Autowired
    private MdEventListener mdEventListener;


    @Bean(initMethod = "start", destroyMethod = "shutdown")
    public ConsumerBean rawDataConsumer() {
        ConsumerBean consumerBean = new ConsumerBean();

        Properties properties = aliyunConfig.getOnsPropertie();
        properties.setProperty(PropertyKeyConst.GROUP_ID, aliyunConfig.getOnsGid());
        properties.setProperty(PropertyKeyConst.ConsumeThreadNums, "2");

        Map<Subscription, MessageListener> subscriptionTable = new HashMap<>();
        /**
         * rowData topic
         */
        Subscription subscription = new Subscription();
        subscription.setTopic(aliyunConfig.getOnsRowDataTopic());
        String tags = "RETURN_VEHICLE||ACCIDENT||ILLEGAL||ILLEGAL_NOT_HANDLED_IN_TIME||ORDER_PAY_NEW||MEMBER_REGISTER||MEMBER_AUDIT"
                + "||CHARGE_DEPOSIT||MEMBER_RECHARGE_ECOIN||ORDER_SUBMIT_ACCESS||REPORT_VEHICLE_CONDITION||MEMBER_DAILY_SIGN"
                + "||MEMBER_INVITATION_COMPLETE||MEMBER_ORDER_ACHIEVEMENT||MEMBER_AUDIT_IDCARD||MEMBER_TO_AUDIT_IDCARD||MEMBER_UNREGISTER||MEMBER_LOGIN||MEMBER_REGISTER_COUPON";
        subscription.setExpression(tags);
        //subscription.setExpression("*");
        subscriptionTable.put(subscription, rawListener);

        /**
         * credits topic
         */
        subscription = new Subscription();
        subscription.setTopic(aliyunConfig.getOnsCreditsTopic());
        subscription.setExpression("MEMBER_POINTS_PUSH||MEMBER_TITLE_REWARD_POINTS");
        subscriptionTable.put(subscription, memberCreditsListener);

        consumerBean.setSubscriptionTable(subscriptionTable);
        consumerBean.setProperties(properties);

        /**
         * 增加门店订单事件监听
         * 尽管门店订单消息为分区顺序消息，但本服务不需要做顺序消费，暂使用普通消费
         */
        subscription = new Subscription();
        subscription.setTopic(aliyunConfig.getOnsMdContractTopic());
        subscription.setExpression("ORDER_PAY||RETURN_VEHICLE||ORDER_ASSESS||VEHICLE_ORDER||PICKUP_VEHICLE");
        subscriptionTable.put(subscription, mdEventListener);

        consumerBean.setSubscriptionTable(subscriptionTable);
        consumerBean.setProperties(properties);


        return consumerBean;
    }

    @Bean(initMethod = "start", destroyMethod = "shutdown")
    public ConsumerBean analysisConsumer() {
        ConsumerBean consumerBean = new ConsumerBean();

        Properties properties = aliyunConfig.getOnsPublicPropertie();
        properties.setProperty(PropertyKeyConst.GROUP_ID, aliyunConfig.getOnsGidPublic());
        properties.setProperty(PropertyKeyConst.ConsumeThreadNums, "5");

        Map<Subscription, MessageListener> subscriptionTable = new HashMap<>();
        Subscription subscription = new Subscription();
        subscription.setTopic(aliyunConfig.getOnsAnalysisTopic());
        subscription.setExpression("MEMBER_CREDIT_ANNUAL_AWARD||MEMBER_CREDIT_MEMBER_COUNT||MEMBER_CREDIT_EVENT_COUNT"
                        + "||MEMBER_LEVEL_ASSESS||MEMBER_LEVEL_COUNT");
        subscriptionTable.put(subscription, analysisListener);

        consumerBean.setSubscriptionTable(subscriptionTable);
        consumerBean.setProperties(properties);
        return consumerBean;
    }
}
