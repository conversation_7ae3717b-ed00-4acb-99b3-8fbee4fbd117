package com.extracme.evcard.membership.core.service.auth.idcard;

import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.canstant.OcrChannelConstants;
import com.extracme.evcard.membership.core.dto.AuditIdCardDTO;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.dto.input.AuditIdCardInput;
import com.extracme.evcard.membership.core.enums.MemOperateTypeEnum;
import com.extracme.evcard.membership.core.input.UserOperationLogInput;
import com.extracme.evcard.membership.core.model.MemberIdentityDocument;
import com.extracme.evcard.membership.core.service.md.MdOrderService;
import com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs;
import com.extracme.evcard.rpc.order.service.IOrderService;
import javafx.util.Pair;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 会员系统重新认证
 */
@Service
public class IdCardReAuthService extends IdCardAuthAbstract {

    @Resource
    private IOrderService orderService;
    @Resource
    MdOrderService mdOrderService;


    public Pair<Integer, String> auth(Object input) {
        try {
            AuditIdCardInput idCardInput = (AuditIdCardInput) input;
            AuditIdCardDTO auditIdCardDTO = new AuditIdCardDTO();
            auditIdCardDTO.setAuditIdCardInput(idCardInput);

            Pair<Integer, String> result = reAurhParamCheck(auditIdCardDTO);
            if (result != null && result.getKey() != 0) {
                return result;
            }

            //操作db
            result = updateDbRecord(auditIdCardDTO);
            if (result != null && result.getKey() != 0) {
                return result;
            }

            //1操作日志记录
            UserOperationLogInput logRecord = getLogRecord(auditIdCardDTO, "身份证人工审核重新审核", MemOperateTypeEnum.IDCARD_MANUAL_REAUDIT);
            memberShipService.saveUserOperationLog(logRecord);
        } catch (Exception e) {
            return new Pair<>(-1, "请稍后重试");
        }

        return new Pair<>(0, OcrChannelConstants.IDCARD_AUDIT_OK_MESSAGE);
    }

    private Pair<Integer, String> reAurhParamCheck(AuditIdCardDTO auditIdCardDTO) {
        AuditIdCardInput idCardInput = auditIdCardDTO.getAuditIdCardInput();
        if (idCardInput == null) {
            return new Pair<>(-1, "入参  不能为空");
        }
        String mid = idCardInput.getMid();
        if (StringUtils.isBlank(mid)) {
            return new Pair<>(-1, "mid  不能为空");
        }

        if (idCardInput.getOperateSourceType() != 0 && idCardInput.getOperateSourceType() != 1) {
            return new Pair<>(-1, "operateSourceType  非法参数");
        }

        String operatorId = idCardInput.getOperatorId();
        if (StringUtils.isBlank(operatorId)) {
            return new Pair<>(-1, "operatorId  不能为空");
        }
        String operatorUserName = idCardInput.getOperatorUserName();
        if (StringUtils.isBlank(operatorUserName)) {
            return new Pair<>(-1, "operatorUserName  不能为空");
        }

        // 这里给 ReviewItems 设置为 0000000
        idCardInput.setReviewItems(BussinessConstants.IDCARD_REVIEW_ITEMS_INIT);
        // 查询membershipInfo
        MembershipBasicInfo membershipInfo = membershipInfoMapper.getUserBasicInfoByMid(mid);
        if (membershipInfo == null) {
            return new Pair<>(-1, "membershipInfoWithBLOBs  为空");
        }
        auditIdCardDTO.setMembershipBasicInfo(membershipInfo);

        if (0 != membershipInfo.getAccountStatus()) {
            return new Pair<>(-1, "会员状态已变更，请刷新后重试");
        }

        Long identityId = membershipInfo.getIdentityId();
        MemberIdentityDocument memberIdentityDocument = memberIdentityDocumentMapper.selectByPrimaryKey(identityId);
        if (memberIdentityDocument == null) {
            return new Pair<>(-1, "memberIdentityDocument 为空");
        }
        auditIdCardDTO.setMemberIdentityDocument(memberIdentityDocument);
        // 判断用户是否是新用户
        if (membershipInfoMapper.checkHavecard(membershipInfo.getAuthId()) == 1) {
            auditIdCardDTO.setNewUser(true);
        }
        auditIdCardDTO.setOperaterDate(new Date());
        return new Pair<>(0, OcrChannelConstants.IDCARD_AUDIT_OK_MESSAGE);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    protected Pair<Integer, String> updateDbRecord(AuditIdCardDTO auditIdCardDTO) {
        //TODO 重新审核对 有效订单的处理逻辑 是否保留
       /* // 判断用户有没有有效的订单(0:已取消 1:预订中 2：已预约 3：已取车 4：还车中 5：已还车 6：已支付) not in (0,1)
        String orderSeq = orderService.selectOneUserOrder(mid, 0, null, null, Arrays.asList(0, 1));
        //查询门店订单是否有有效订单
        int mdOrderCount = 0;
        String mid = membershipInfo.getMid();
        //合同状态 1:预订中(备车中) 2：已预约(待取车) 3：已取车 4：还车中(收车中) 5：已还车(待支付) 6：已支付 7:已取消
        GetOrderCountByStatusRequest mdOrderReq = new GetOrderCountByStatusRequest(mid,Arrays.asList(2,3,4,5,6));
        GetOrderCountByStatusResponse res = mdOrderService.getOrderCountByStatus(mdOrderReq);
        if (res.getCode() == 0){
            mdOrderCount = res.getData().getResult();
        }*/

        MembershipInfoWithBLOBs updatedMembershipInfo = getUpdatedMembershipInfo(auditIdCardDTO, -1);
       /* if(org.apache.commons.lang3.StringUtils.isNotBlank(orderSeq) && mdOrderCount > 0) {
            String reviewItems = membershipInfo.getReviewItems();
            if (!"".equals(reviewItems)) {
                reviewItems = reviewItems.substring(0, 3) + "0";
            }
            updatedMembershipInfo.setReviewItems(reviewItems);
        } else {

            updatedMembershipInfo.setReviewItems("0000");
            updatedMembershipInfo.setReviewStatus((short)0);

        }*/
        //membershipInfoMapper.updateByPrimaryKey(updatedMembershipInfo);
        membershipInfoMapper.updateByPrimaryKeySelective(updatedMembershipInfo);

        //memberIdentityDocumentMapper 更新
        MemberIdentityDocument updateMemberIdentityDocument = getUpdatedMemberIdentityDocument(auditIdCardDTO, 3);
        memberIdentityDocumentMapper.updateByPrimaryKeySelective(updateMemberIdentityDocument);
        return new Pair<>(0, OcrChannelConstants.IDCARD_AUDIT_OK_MESSAGE);
    }

    @Override
    protected Pair<Integer, String> serviceCheck(AuditIdCardDTO auditIdCardDTO) {
        return null;
    }


    @Override
    protected void messagePush(AuditIdCardDTO auditIdCardDTO) {

    }
}
