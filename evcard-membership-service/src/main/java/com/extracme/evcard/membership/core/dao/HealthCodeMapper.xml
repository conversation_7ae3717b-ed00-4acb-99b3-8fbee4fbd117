<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.extracme.evcard.membership.core.dao.HealthCodeMapper">
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.HealthCode">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="auth_id" jdbcType="VARCHAR" property="authId" />
    <result column="color" jdbcType="INTEGER" property="color" />
    <result column="STATUS" jdbcType="DECIMAL" property="status" />
    <result column="CREATED_TIME" jdbcType="VARCHAR" property="createdTime" />
    <result column="CREATED_USER" jdbcType="VARCHAR" property="createdUser" />
    <result column="UPDATED_TIME" jdbcType="VARCHAR" property="updatedTime" />
    <result column="UPDATED_USER" jdbcType="VARCHAR" property="updatedUser" />
  </resultMap>
  <sql id="Base_Column_List">
    id, auth_id, color, STATUS, CREATED_TIME, CREATED_USER, UPDATED_TIME, UPDATED_USER
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from health_code
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByAuthId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${siacSchema}.health_code
    where auth_id = #{authId} limit 1
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from ${siacSchema}.health_code
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.membership.core.model.HealthCode">
    insert into ${siacSchema}.health_code (id, auth_id, color,
      STATUS, CREATED_TIME, CREATED_USER,
      UPDATED_TIME, UPDATED_USER)
    values (#{id,jdbcType=INTEGER}, #{authId,jdbcType=VARCHAR}, #{color,jdbcType=INTEGER},
      #{status,jdbcType=DECIMAL}, #{createdTime,jdbcType=VARCHAR}, #{createdUser,jdbcType=VARCHAR},
      #{updatedTime,jdbcType=VARCHAR}, #{updatedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.core.model.HealthCode">
    insert into ${siacSchema}.health_code
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="authId != null">
        auth_id,
      </if>
      <if test="color != null">
        color,
      </if>
      <if test="status != null">
        STATUS,
      </if>
      <if test="createdTime != null">
        CREATED_TIME,
      </if>
      <if test="createdUser != null">
        CREATED_USER,
      </if>
      <if test="updatedTime != null">
        UPDATED_TIME,
      </if>
      <if test="updatedUser != null">
        UPDATED_USER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="authId != null">
        #{authId,jdbcType=VARCHAR},
      </if>
      <if test="color != null">
        #{color,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=DECIMAL},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=VARCHAR},
      </if>
      <if test="createdUser != null">
        #{createdUser,jdbcType=VARCHAR},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=VARCHAR},
      </if>
      <if test="updatedUser != null">
        #{updatedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.core.model.HealthCode">
    update health_code
    <set>
      <if test="authId != null">
        auth_id = #{authId,jdbcType=VARCHAR},
      </if>
      <if test="color != null">
        color = #{color,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        STATUS = #{status,jdbcType=DECIMAL},
      </if>
      <if test="createdTime != null">
        CREATED_TIME = #{createdTime,jdbcType=VARCHAR},
      </if>
      <if test="createdUser != null">
        CREATED_USER = #{createdUser,jdbcType=VARCHAR},
      </if>
      <if test="updatedTime != null">
        UPDATED_TIME = #{updatedTime,jdbcType=VARCHAR},
      </if>
      <if test="updatedUser != null">
        UPDATED_USER = #{updatedUser,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.core.model.HealthCode">
    update health_code
    set auth_id = #{authId,jdbcType=VARCHAR},
      color = #{color,jdbcType=INTEGER},
      STATUS = #{status,jdbcType=DECIMAL},
      CREATED_TIME = #{createdTime,jdbcType=VARCHAR},
      CREATED_USER = #{createdUser,jdbcType=VARCHAR},
      UPDATED_TIME = #{updatedTime,jdbcType=VARCHAR},
      UPDATED_USER = #{updatedUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByAuthId">
    update ${siacSchema}.health_code
    set
      color = #{color,jdbcType=INTEGER},
      STATUS = #{status,jdbcType=DECIMAL}
    where auth_id = #{authId,jdbcType=VARCHAR}
  </update>
</mapper>