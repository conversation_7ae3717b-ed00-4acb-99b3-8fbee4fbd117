package com.extracme.evcard.membership.core.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class MemberIdentityDocumentExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public MemberIdentityDocumentExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andMidIsNull() {
            addCriterion("mid is null");
            return (Criteria) this;
        }

        public Criteria andMidIsNotNull() {
            addCriterion("mid is not null");
            return (Criteria) this;
        }

        public Criteria andMidEqualTo(String value) {
            addCriterion("mid =", value, "mid");
            return (Criteria) this;
        }

        public Criteria andMidNotEqualTo(String value) {
            addCriterion("mid <>", value, "mid");
            return (Criteria) this;
        }

        public Criteria andMidGreaterThan(String value) {
            addCriterion("mid >", value, "mid");
            return (Criteria) this;
        }

        public Criteria andMidGreaterThanOrEqualTo(String value) {
            addCriterion("mid >=", value, "mid");
            return (Criteria) this;
        }

        public Criteria andMidLessThan(String value) {
            addCriterion("mid <", value, "mid");
            return (Criteria) this;
        }

        public Criteria andMidLessThanOrEqualTo(String value) {
            addCriterion("mid <=", value, "mid");
            return (Criteria) this;
        }

        public Criteria andMidLike(String value) {
            addCriterion("mid like", value, "mid");
            return (Criteria) this;
        }

        public Criteria andMidNotLike(String value) {
            addCriterion("mid not like", value, "mid");
            return (Criteria) this;
        }

        public Criteria andMidIn(List<String> values) {
            addCriterion("mid in", values, "mid");
            return (Criteria) this;
        }

        public Criteria andMidNotIn(List<String> values) {
            addCriterion("mid not in", values, "mid");
            return (Criteria) this;
        }

        public Criteria andMidBetween(String value1, String value2) {
            addCriterion("mid between", value1, value2, "mid");
            return (Criteria) this;
        }

        public Criteria andMidNotBetween(String value1, String value2) {
            addCriterion("mid not between", value1, value2, "mid");
            return (Criteria) this;
        }

        public Criteria andIdentityNoIsNull() {
            addCriterion("identity_no is null");
            return (Criteria) this;
        }

        public Criteria andIdentityNoIsNotNull() {
            addCriterion("identity_no is not null");
            return (Criteria) this;
        }

        public Criteria andIdentityNoEqualTo(String value) {
            addCriterion("identity_no =", value, "identityNo");
            return (Criteria) this;
        }

        public Criteria andIdentityNoNotEqualTo(String value) {
            addCriterion("identity_no <>", value, "identityNo");
            return (Criteria) this;
        }

        public Criteria andIdentityNoGreaterThan(String value) {
            addCriterion("identity_no >", value, "identityNo");
            return (Criteria) this;
        }

        public Criteria andIdentityNoGreaterThanOrEqualTo(String value) {
            addCriterion("identity_no >=", value, "identityNo");
            return (Criteria) this;
        }

        public Criteria andIdentityNoLessThan(String value) {
            addCriterion("identity_no <", value, "identityNo");
            return (Criteria) this;
        }

        public Criteria andIdentityNoLessThanOrEqualTo(String value) {
            addCriterion("identity_no <=", value, "identityNo");
            return (Criteria) this;
        }

        public Criteria andIdentityNoLike(String value) {
            addCriterion("identity_no like", value, "identityNo");
            return (Criteria) this;
        }

        public Criteria andIdentityNoNotLike(String value) {
            addCriterion("identity_no not like", value, "identityNo");
            return (Criteria) this;
        }

        public Criteria andIdentityNoIn(List<String> values) {
            addCriterion("identity_no in", values, "identityNo");
            return (Criteria) this;
        }

        public Criteria andIdentityNoNotIn(List<String> values) {
            addCriterion("identity_no not in", values, "identityNo");
            return (Criteria) this;
        }

        public Criteria andIdentityNoBetween(String value1, String value2) {
            addCriterion("identity_no between", value1, value2, "identityNo");
            return (Criteria) this;
        }

        public Criteria andIdentityNoNotBetween(String value1, String value2) {
            addCriterion("identity_no not between", value1, value2, "identityNo");
            return (Criteria) this;
        }

        public Criteria andIdentityTypeIsNull() {
            addCriterion("identity_type is null");
            return (Criteria) this;
        }

        public Criteria andIdentityTypeIsNotNull() {
            addCriterion("identity_type is not null");
            return (Criteria) this;
        }

        public Criteria andIdentityTypeEqualTo(Integer value) {
            addCriterion("identity_type =", value, "identityType");
            return (Criteria) this;
        }

        public Criteria andIdentityTypeNotEqualTo(Integer value) {
            addCriterion("identity_type <>", value, "identityType");
            return (Criteria) this;
        }

        public Criteria andIdentityTypeGreaterThan(Integer value) {
            addCriterion("identity_type >", value, "identityType");
            return (Criteria) this;
        }

        public Criteria andIdentityTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("identity_type >=", value, "identityType");
            return (Criteria) this;
        }

        public Criteria andIdentityTypeLessThan(Integer value) {
            addCriterion("identity_type <", value, "identityType");
            return (Criteria) this;
        }

        public Criteria andIdentityTypeLessThanOrEqualTo(Integer value) {
            addCriterion("identity_type <=", value, "identityType");
            return (Criteria) this;
        }

        public Criteria andIdentityTypeIn(List<Integer> values) {
            addCriterion("identity_type in", values, "identityType");
            return (Criteria) this;
        }

        public Criteria andIdentityTypeNotIn(List<Integer> values) {
            addCriterion("identity_type not in", values, "identityType");
            return (Criteria) this;
        }

        public Criteria andIdentityTypeBetween(Integer value1, Integer value2) {
            addCriterion("identity_type between", value1, value2, "identityType");
            return (Criteria) this;
        }

        public Criteria andIdentityTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("identity_type not between", value1, value2, "identityType");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andExpireTypeIsNull() {
            addCriterion("expire_type is null");
            return (Criteria) this;
        }

        public Criteria andExpireTypeIsNotNull() {
            addCriterion("expire_type is not null");
            return (Criteria) this;
        }

        public Criteria andExpireTypeEqualTo(Integer value) {
            addCriterion("expire_type =", value, "expireType");
            return (Criteria) this;
        }

        public Criteria andExpireTypeNotEqualTo(Integer value) {
            addCriterion("expire_type <>", value, "expireType");
            return (Criteria) this;
        }

        public Criteria andExpireTypeGreaterThan(Integer value) {
            addCriterion("expire_type >", value, "expireType");
            return (Criteria) this;
        }

        public Criteria andExpireTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("expire_type >=", value, "expireType");
            return (Criteria) this;
        }

        public Criteria andExpireTypeLessThan(Integer value) {
            addCriterion("expire_type <", value, "expireType");
            return (Criteria) this;
        }

        public Criteria andExpireTypeLessThanOrEqualTo(Integer value) {
            addCriterion("expire_type <=", value, "expireType");
            return (Criteria) this;
        }

        public Criteria andExpireTypeIn(List<Integer> values) {
            addCriterion("expire_type in", values, "expireType");
            return (Criteria) this;
        }

        public Criteria andExpireTypeNotIn(List<Integer> values) {
            addCriterion("expire_type not in", values, "expireType");
            return (Criteria) this;
        }

        public Criteria andExpireTypeBetween(Integer value1, Integer value2) {
            addCriterion("expire_type between", value1, value2, "expireType");
            return (Criteria) this;
        }

        public Criteria andExpireTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("expire_type not between", value1, value2, "expireType");
            return (Criteria) this;
        }

        public Criteria andExpirationDateIsNull() {
            addCriterion("expiration_date is null");
            return (Criteria) this;
        }

        public Criteria andExpirationDateIsNotNull() {
            addCriterion("expiration_date is not null");
            return (Criteria) this;
        }

        public Criteria andExpirationDateEqualTo(String value) {
            addCriterion("expiration_date =", value, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andExpirationDateNotEqualTo(String value) {
            addCriterion("expiration_date <>", value, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andExpirationDateGreaterThan(String value) {
            addCriterion("expiration_date >", value, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andExpirationDateGreaterThanOrEqualTo(String value) {
            addCriterion("expiration_date >=", value, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andExpirationDateLessThan(String value) {
            addCriterion("expiration_date <", value, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andExpirationDateLessThanOrEqualTo(String value) {
            addCriterion("expiration_date <=", value, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andExpirationDateLike(String value) {
            addCriterion("expiration_date like", value, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andExpirationDateNotLike(String value) {
            addCriterion("expiration_date not like", value, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andExpirationDateIn(List<String> values) {
            addCriterion("expiration_date in", values, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andExpirationDateNotIn(List<String> values) {
            addCriterion("expiration_date not in", values, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andExpirationDateBetween(String value1, String value2) {
            addCriterion("expiration_date between", value1, value2, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andExpirationDateNotBetween(String value1, String value2) {
            addCriterion("expiration_date not between", value1, value2, "expirationDate");
            return (Criteria) this;
        }

        public Criteria andIdentityCardImgUrlIsNull() {
            addCriterion("identity_card_img_url is null");
            return (Criteria) this;
        }

        public Criteria andIdentityCardImgUrlIsNotNull() {
            addCriterion("identity_card_img_url is not null");
            return (Criteria) this;
        }

        public Criteria andIdentityCardImgUrlEqualTo(String value) {
            addCriterion("identity_card_img_url =", value, "identityCardImgUrl");
            return (Criteria) this;
        }

        public Criteria andIdentityCardImgUrlNotEqualTo(String value) {
            addCriterion("identity_card_img_url <>", value, "identityCardImgUrl");
            return (Criteria) this;
        }

        public Criteria andIdentityCardImgUrlGreaterThan(String value) {
            addCriterion("identity_card_img_url >", value, "identityCardImgUrl");
            return (Criteria) this;
        }

        public Criteria andIdentityCardImgUrlGreaterThanOrEqualTo(String value) {
            addCriterion("identity_card_img_url >=", value, "identityCardImgUrl");
            return (Criteria) this;
        }

        public Criteria andIdentityCardImgUrlLessThan(String value) {
            addCriterion("identity_card_img_url <", value, "identityCardImgUrl");
            return (Criteria) this;
        }

        public Criteria andIdentityCardImgUrlLessThanOrEqualTo(String value) {
            addCriterion("identity_card_img_url <=", value, "identityCardImgUrl");
            return (Criteria) this;
        }

        public Criteria andIdentityCardImgUrlLike(String value) {
            addCriterion("identity_card_img_url like", value, "identityCardImgUrl");
            return (Criteria) this;
        }

        public Criteria andIdentityCardImgUrlNotLike(String value) {
            addCriterion("identity_card_img_url not like", value, "identityCardImgUrl");
            return (Criteria) this;
        }

        public Criteria andIdentityCardImgUrlIn(List<String> values) {
            addCriterion("identity_card_img_url in", values, "identityCardImgUrl");
            return (Criteria) this;
        }

        public Criteria andIdentityCardImgUrlNotIn(List<String> values) {
            addCriterion("identity_card_img_url not in", values, "identityCardImgUrl");
            return (Criteria) this;
        }

        public Criteria andIdentityCardImgUrlBetween(String value1, String value2) {
            addCriterion("identity_card_img_url between", value1, value2, "identityCardImgUrl");
            return (Criteria) this;
        }

        public Criteria andIdentityCardImgUrlNotBetween(String value1, String value2) {
            addCriterion("identity_card_img_url not between", value1, value2, "identityCardImgUrl");
            return (Criteria) this;
        }

        public Criteria andReverseIdentityCardImgUrlIsNull() {
            addCriterion("reverse_identity_card_img_url is null");
            return (Criteria) this;
        }

        public Criteria andReverseIdentityCardImgUrlIsNotNull() {
            addCriterion("reverse_identity_card_img_url is not null");
            return (Criteria) this;
        }

        public Criteria andReverseIdentityCardImgUrlEqualTo(String value) {
            addCriterion("reverse_identity_card_img_url =", value, "reverseIdentityCardImgUrl");
            return (Criteria) this;
        }

        public Criteria andReverseIdentityCardImgUrlNotEqualTo(String value) {
            addCriterion("reverse_identity_card_img_url <>", value, "reverseIdentityCardImgUrl");
            return (Criteria) this;
        }

        public Criteria andReverseIdentityCardImgUrlGreaterThan(String value) {
            addCriterion("reverse_identity_card_img_url >", value, "reverseIdentityCardImgUrl");
            return (Criteria) this;
        }

        public Criteria andReverseIdentityCardImgUrlGreaterThanOrEqualTo(String value) {
            addCriterion("reverse_identity_card_img_url >=", value, "reverseIdentityCardImgUrl");
            return (Criteria) this;
        }

        public Criteria andReverseIdentityCardImgUrlLessThan(String value) {
            addCriterion("reverse_identity_card_img_url <", value, "reverseIdentityCardImgUrl");
            return (Criteria) this;
        }

        public Criteria andReverseIdentityCardImgUrlLessThanOrEqualTo(String value) {
            addCriterion("reverse_identity_card_img_url <=", value, "reverseIdentityCardImgUrl");
            return (Criteria) this;
        }

        public Criteria andReverseIdentityCardImgUrlLike(String value) {
            addCriterion("reverse_identity_card_img_url like", value, "reverseIdentityCardImgUrl");
            return (Criteria) this;
        }

        public Criteria andReverseIdentityCardImgUrlNotLike(String value) {
            addCriterion("reverse_identity_card_img_url not like", value, "reverseIdentityCardImgUrl");
            return (Criteria) this;
        }

        public Criteria andReverseIdentityCardImgUrlIn(List<String> values) {
            addCriterion("reverse_identity_card_img_url in", values, "reverseIdentityCardImgUrl");
            return (Criteria) this;
        }

        public Criteria andReverseIdentityCardImgUrlNotIn(List<String> values) {
            addCriterion("reverse_identity_card_img_url not in", values, "reverseIdentityCardImgUrl");
            return (Criteria) this;
        }

        public Criteria andReverseIdentityCardImgUrlBetween(String value1, String value2) {
            addCriterion("reverse_identity_card_img_url between", value1, value2, "reverseIdentityCardImgUrl");
            return (Criteria) this;
        }

        public Criteria andReverseIdentityCardImgUrlNotBetween(String value1, String value2) {
            addCriterion("reverse_identity_card_img_url not between", value1, value2, "reverseIdentityCardImgUrl");
            return (Criteria) this;
        }

        public Criteria andHoldIdcardPicUrlIsNull() {
            addCriterion("hold_idcard_pic_url is null");
            return (Criteria) this;
        }

        public Criteria andHoldIdcardPicUrlIsNotNull() {
            addCriterion("hold_idcard_pic_url is not null");
            return (Criteria) this;
        }

        public Criteria andHoldIdcardPicUrlEqualTo(String value) {
            addCriterion("hold_idcard_pic_url =", value, "holdIdcardPicUrl");
            return (Criteria) this;
        }

        public Criteria andHoldIdcardPicUrlNotEqualTo(String value) {
            addCriterion("hold_idcard_pic_url <>", value, "holdIdcardPicUrl");
            return (Criteria) this;
        }

        public Criteria andHoldIdcardPicUrlGreaterThan(String value) {
            addCriterion("hold_idcard_pic_url >", value, "holdIdcardPicUrl");
            return (Criteria) this;
        }

        public Criteria andHoldIdcardPicUrlGreaterThanOrEqualTo(String value) {
            addCriterion("hold_idcard_pic_url >=", value, "holdIdcardPicUrl");
            return (Criteria) this;
        }

        public Criteria andHoldIdcardPicUrlLessThan(String value) {
            addCriterion("hold_idcard_pic_url <", value, "holdIdcardPicUrl");
            return (Criteria) this;
        }

        public Criteria andHoldIdcardPicUrlLessThanOrEqualTo(String value) {
            addCriterion("hold_idcard_pic_url <=", value, "holdIdcardPicUrl");
            return (Criteria) this;
        }

        public Criteria andHoldIdcardPicUrlLike(String value) {
            addCriterion("hold_idcard_pic_url like", value, "holdIdcardPicUrl");
            return (Criteria) this;
        }

        public Criteria andHoldIdcardPicUrlNotLike(String value) {
            addCriterion("hold_idcard_pic_url not like", value, "holdIdcardPicUrl");
            return (Criteria) this;
        }

        public Criteria andHoldIdcardPicUrlIn(List<String> values) {
            addCriterion("hold_idcard_pic_url in", values, "holdIdcardPicUrl");
            return (Criteria) this;
        }

        public Criteria andHoldIdcardPicUrlNotIn(List<String> values) {
            addCriterion("hold_idcard_pic_url not in", values, "holdIdcardPicUrl");
            return (Criteria) this;
        }

        public Criteria andHoldIdcardPicUrlBetween(String value1, String value2) {
            addCriterion("hold_idcard_pic_url between", value1, value2, "holdIdcardPicUrl");
            return (Criteria) this;
        }

        public Criteria andHoldIdcardPicUrlNotBetween(String value1, String value2) {
            addCriterion("hold_idcard_pic_url not between", value1, value2, "holdIdcardPicUrl");
            return (Criteria) this;
        }

        public Criteria andFaceRecognitionImgUrlIsNull() {
            addCriterion("face_recognition_img_url is null");
            return (Criteria) this;
        }

        public Criteria andFaceRecognitionImgUrlIsNotNull() {
            addCriterion("face_recognition_img_url is not null");
            return (Criteria) this;
        }

        public Criteria andFaceRecognitionImgUrlEqualTo(String value) {
            addCriterion("face_recognition_img_url =", value, "faceRecognitionImgUrl");
            return (Criteria) this;
        }

        public Criteria andFaceRecognitionImgUrlNotEqualTo(String value) {
            addCriterion("face_recognition_img_url <>", value, "faceRecognitionImgUrl");
            return (Criteria) this;
        }

        public Criteria andFaceRecognitionImgUrlGreaterThan(String value) {
            addCriterion("face_recognition_img_url >", value, "faceRecognitionImgUrl");
            return (Criteria) this;
        }

        public Criteria andFaceRecognitionImgUrlGreaterThanOrEqualTo(String value) {
            addCriterion("face_recognition_img_url >=", value, "faceRecognitionImgUrl");
            return (Criteria) this;
        }

        public Criteria andFaceRecognitionImgUrlLessThan(String value) {
            addCriterion("face_recognition_img_url <", value, "faceRecognitionImgUrl");
            return (Criteria) this;
        }

        public Criteria andFaceRecognitionImgUrlLessThanOrEqualTo(String value) {
            addCriterion("face_recognition_img_url <=", value, "faceRecognitionImgUrl");
            return (Criteria) this;
        }

        public Criteria andFaceRecognitionImgUrlLike(String value) {
            addCriterion("face_recognition_img_url like", value, "faceRecognitionImgUrl");
            return (Criteria) this;
        }

        public Criteria andFaceRecognitionImgUrlNotLike(String value) {
            addCriterion("face_recognition_img_url not like", value, "faceRecognitionImgUrl");
            return (Criteria) this;
        }

        public Criteria andFaceRecognitionImgUrlIn(List<String> values) {
            addCriterion("face_recognition_img_url in", values, "faceRecognitionImgUrl");
            return (Criteria) this;
        }

        public Criteria andFaceRecognitionImgUrlNotIn(List<String> values) {
            addCriterion("face_recognition_img_url not in", values, "faceRecognitionImgUrl");
            return (Criteria) this;
        }

        public Criteria andFaceRecognitionImgUrlBetween(String value1, String value2) {
            addCriterion("face_recognition_img_url between", value1, value2, "faceRecognitionImgUrl");
            return (Criteria) this;
        }

        public Criteria andFaceRecognitionImgUrlNotBetween(String value1, String value2) {
            addCriterion("face_recognition_img_url not between", value1, value2, "faceRecognitionImgUrl");
            return (Criteria) this;
        }

        public Criteria andCertInputTypeIsNull() {
            addCriterion("cert_input_type is null");
            return (Criteria) this;
        }

        public Criteria andCertInputTypeIsNotNull() {
            addCriterion("cert_input_type is not null");
            return (Criteria) this;
        }

        public Criteria andCertInputTypeEqualTo(Integer value) {
            addCriterion("cert_input_type =", value, "certInputType");
            return (Criteria) this;
        }

        public Criteria andCertInputTypeNotEqualTo(Integer value) {
            addCriterion("cert_input_type <>", value, "certInputType");
            return (Criteria) this;
        }

        public Criteria andCertInputTypeGreaterThan(Integer value) {
            addCriterion("cert_input_type >", value, "certInputType");
            return (Criteria) this;
        }

        public Criteria andCertInputTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("cert_input_type >=", value, "certInputType");
            return (Criteria) this;
        }

        public Criteria andCertInputTypeLessThan(Integer value) {
            addCriterion("cert_input_type <", value, "certInputType");
            return (Criteria) this;
        }

        public Criteria andCertInputTypeLessThanOrEqualTo(Integer value) {
            addCriterion("cert_input_type <=", value, "certInputType");
            return (Criteria) this;
        }

        public Criteria andCertInputTypeIn(List<Integer> values) {
            addCriterion("cert_input_type in", values, "certInputType");
            return (Criteria) this;
        }

        public Criteria andCertInputTypeNotIn(List<Integer> values) {
            addCriterion("cert_input_type not in", values, "certInputType");
            return (Criteria) this;
        }

        public Criteria andCertInputTypeBetween(Integer value1, Integer value2) {
            addCriterion("cert_input_type between", value1, value2, "certInputType");
            return (Criteria) this;
        }

        public Criteria andCertInputTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("cert_input_type not between", value1, value2, "certInputType");
            return (Criteria) this;
        }

        public Criteria andAuthenticationStatusIsNull() {
            addCriterion("authentication_status is null");
            return (Criteria) this;
        }

        public Criteria andAuthenticationStatusIsNotNull() {
            addCriterion("authentication_status is not null");
            return (Criteria) this;
        }

        public Criteria andAuthenticationStatusEqualTo(Integer value) {
            addCriterion("authentication_status =", value, "authenticationStatus");
            return (Criteria) this;
        }

        public Criteria andAuthenticationStatusNotEqualTo(Integer value) {
            addCriterion("authentication_status <>", value, "authenticationStatus");
            return (Criteria) this;
        }

        public Criteria andAuthenticationStatusGreaterThan(Integer value) {
            addCriterion("authentication_status >", value, "authenticationStatus");
            return (Criteria) this;
        }

        public Criteria andAuthenticationStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("authentication_status >=", value, "authenticationStatus");
            return (Criteria) this;
        }

        public Criteria andAuthenticationStatusLessThan(Integer value) {
            addCriterion("authentication_status <", value, "authenticationStatus");
            return (Criteria) this;
        }

        public Criteria andAuthenticationStatusLessThanOrEqualTo(Integer value) {
            addCriterion("authentication_status <=", value, "authenticationStatus");
            return (Criteria) this;
        }

        public Criteria andAuthenticationStatusIn(List<Integer> values) {
            addCriterion("authentication_status in", values, "authenticationStatus");
            return (Criteria) this;
        }

        public Criteria andAuthenticationStatusNotIn(List<Integer> values) {
            addCriterion("authentication_status not in", values, "authenticationStatus");
            return (Criteria) this;
        }

        public Criteria andAuthenticationStatusBetween(Integer value1, Integer value2) {
            addCriterion("authentication_status between", value1, value2, "authenticationStatus");
            return (Criteria) this;
        }

        public Criteria andAuthenticationStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("authentication_status not between", value1, value2, "authenticationStatus");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeIsNull() {
            addCriterion("submit_time is null");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeIsNotNull() {
            addCriterion("submit_time is not null");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeEqualTo(Date value) {
            addCriterionForJDBCDate("submit_time =", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeNotEqualTo(Date value) {
            addCriterionForJDBCDate("submit_time <>", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeGreaterThan(Date value) {
            addCriterionForJDBCDate("submit_time >", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("submit_time >=", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeLessThan(Date value) {
            addCriterionForJDBCDate("submit_time <", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("submit_time <=", value, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeIn(List<Date> values) {
            addCriterionForJDBCDate("submit_time in", values, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeNotIn(List<Date> values) {
            addCriterionForJDBCDate("submit_time not in", values, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("submit_time between", value1, value2, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitTimeNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("submit_time not between", value1, value2, "submitTime");
            return (Criteria) this;
        }

        public Criteria andSubmitAppkeyIsNull() {
            addCriterion("submit_appkey is null");
            return (Criteria) this;
        }

        public Criteria andSubmitAppkeyIsNotNull() {
            addCriterion("submit_appkey is not null");
            return (Criteria) this;
        }

        public Criteria andSubmitAppkeyEqualTo(String value) {
            addCriterion("submit_appkey =", value, "submitAppkey");
            return (Criteria) this;
        }

        public Criteria andSubmitAppkeyNotEqualTo(String value) {
            addCriterion("submit_appkey <>", value, "submitAppkey");
            return (Criteria) this;
        }

        public Criteria andSubmitAppkeyGreaterThan(String value) {
            addCriterion("submit_appkey >", value, "submitAppkey");
            return (Criteria) this;
        }

        public Criteria andSubmitAppkeyGreaterThanOrEqualTo(String value) {
            addCriterion("submit_appkey >=", value, "submitAppkey");
            return (Criteria) this;
        }

        public Criteria andSubmitAppkeyLessThan(String value) {
            addCriterion("submit_appkey <", value, "submitAppkey");
            return (Criteria) this;
        }

        public Criteria andSubmitAppkeyLessThanOrEqualTo(String value) {
            addCriterion("submit_appkey <=", value, "submitAppkey");
            return (Criteria) this;
        }

        public Criteria andSubmitAppkeyLike(String value) {
            addCriterion("submit_appkey like", value, "submitAppkey");
            return (Criteria) this;
        }

        public Criteria andSubmitAppkeyNotLike(String value) {
            addCriterion("submit_appkey not like", value, "submitAppkey");
            return (Criteria) this;
        }

        public Criteria andSubmitAppkeyIn(List<String> values) {
            addCriterion("submit_appkey in", values, "submitAppkey");
            return (Criteria) this;
        }

        public Criteria andSubmitAppkeyNotIn(List<String> values) {
            addCriterion("submit_appkey not in", values, "submitAppkey");
            return (Criteria) this;
        }

        public Criteria andSubmitAppkeyBetween(String value1, String value2) {
            addCriterion("submit_appkey between", value1, value2, "submitAppkey");
            return (Criteria) this;
        }

        public Criteria andSubmitAppkeyNotBetween(String value1, String value2) {
            addCriterion("submit_appkey not between", value1, value2, "submitAppkey");
            return (Criteria) this;
        }

        public Criteria andReviewUserIsNull() {
            addCriterion("review_user is null");
            return (Criteria) this;
        }

        public Criteria andReviewUserIsNotNull() {
            addCriterion("review_user is not null");
            return (Criteria) this;
        }

        public Criteria andReviewUserEqualTo(String value) {
            addCriterion("review_user =", value, "reviewUser");
            return (Criteria) this;
        }

        public Criteria andReviewUserNotEqualTo(String value) {
            addCriterion("review_user <>", value, "reviewUser");
            return (Criteria) this;
        }

        public Criteria andReviewUserGreaterThan(String value) {
            addCriterion("review_user >", value, "reviewUser");
            return (Criteria) this;
        }

        public Criteria andReviewUserGreaterThanOrEqualTo(String value) {
            addCriterion("review_user >=", value, "reviewUser");
            return (Criteria) this;
        }

        public Criteria andReviewUserLessThan(String value) {
            addCriterion("review_user <", value, "reviewUser");
            return (Criteria) this;
        }

        public Criteria andReviewUserLessThanOrEqualTo(String value) {
            addCriterion("review_user <=", value, "reviewUser");
            return (Criteria) this;
        }

        public Criteria andReviewUserLike(String value) {
            addCriterion("review_user like", value, "reviewUser");
            return (Criteria) this;
        }

        public Criteria andReviewUserNotLike(String value) {
            addCriterion("review_user not like", value, "reviewUser");
            return (Criteria) this;
        }

        public Criteria andReviewUserIn(List<String> values) {
            addCriterion("review_user in", values, "reviewUser");
            return (Criteria) this;
        }

        public Criteria andReviewUserNotIn(List<String> values) {
            addCriterion("review_user not in", values, "reviewUser");
            return (Criteria) this;
        }

        public Criteria andReviewUserBetween(String value1, String value2) {
            addCriterion("review_user between", value1, value2, "reviewUser");
            return (Criteria) this;
        }

        public Criteria andReviewUserNotBetween(String value1, String value2) {
            addCriterion("review_user not between", value1, value2, "reviewUser");
            return (Criteria) this;
        }

        public Criteria andReviewTimeIsNull() {
            addCriterion("review_time is null");
            return (Criteria) this;
        }

        public Criteria andReviewTimeIsNotNull() {
            addCriterion("review_time is not null");
            return (Criteria) this;
        }

        public Criteria andReviewTimeEqualTo(Date value) {
            addCriterionForJDBCDate("review_time =", value, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeNotEqualTo(Date value) {
            addCriterionForJDBCDate("review_time <>", value, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeGreaterThan(Date value) {
            addCriterionForJDBCDate("review_time >", value, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("review_time >=", value, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeLessThan(Date value) {
            addCriterionForJDBCDate("review_time <", value, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("review_time <=", value, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeIn(List<Date> values) {
            addCriterionForJDBCDate("review_time in", values, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeNotIn(List<Date> values) {
            addCriterionForJDBCDate("review_time not in", values, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("review_time between", value1, value2, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewTimeNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("review_time not between", value1, value2, "reviewTime");
            return (Criteria) this;
        }

        public Criteria andReviewItemsIsNull() {
            addCriterion("review_items is null");
            return (Criteria) this;
        }

        public Criteria andReviewItemsIsNotNull() {
            addCriterion("review_items is not null");
            return (Criteria) this;
        }

        public Criteria andReviewItemsEqualTo(String value) {
            addCriterion("review_items =", value, "reviewItems");
            return (Criteria) this;
        }

        public Criteria andReviewItemsNotEqualTo(String value) {
            addCriterion("review_items <>", value, "reviewItems");
            return (Criteria) this;
        }

        public Criteria andReviewItemsGreaterThan(String value) {
            addCriterion("review_items >", value, "reviewItems");
            return (Criteria) this;
        }

        public Criteria andReviewItemsGreaterThanOrEqualTo(String value) {
            addCriterion("review_items >=", value, "reviewItems");
            return (Criteria) this;
        }

        public Criteria andReviewItemsLessThan(String value) {
            addCriterion("review_items <", value, "reviewItems");
            return (Criteria) this;
        }

        public Criteria andReviewItemsLessThanOrEqualTo(String value) {
            addCriterion("review_items <=", value, "reviewItems");
            return (Criteria) this;
        }

        public Criteria andReviewItemsLike(String value) {
            addCriterion("review_items like", value, "reviewItems");
            return (Criteria) this;
        }

        public Criteria andReviewItemsNotLike(String value) {
            addCriterion("review_items not like", value, "reviewItems");
            return (Criteria) this;
        }

        public Criteria andReviewItemsIn(List<String> values) {
            addCriterion("review_items in", values, "reviewItems");
            return (Criteria) this;
        }

        public Criteria andReviewItemsNotIn(List<String> values) {
            addCriterion("review_items not in", values, "reviewItems");
            return (Criteria) this;
        }

        public Criteria andReviewItemsBetween(String value1, String value2) {
            addCriterion("review_items between", value1, value2, "reviewItems");
            return (Criteria) this;
        }

        public Criteria andReviewItemsNotBetween(String value1, String value2) {
            addCriterion("review_items not between", value1, value2, "reviewItems");
            return (Criteria) this;
        }

        public Criteria andReviewIdsIsNull() {
            addCriterion("review_ids is null");
            return (Criteria) this;
        }

        public Criteria andReviewIdsIsNotNull() {
            addCriterion("review_ids is not null");
            return (Criteria) this;
        }

        public Criteria andReviewIdsEqualTo(String value) {
            addCriterion("review_ids =", value, "reviewIds");
            return (Criteria) this;
        }

        public Criteria andReviewIdsNotEqualTo(String value) {
            addCriterion("review_ids <>", value, "reviewIds");
            return (Criteria) this;
        }

        public Criteria andReviewIdsGreaterThan(String value) {
            addCriterion("review_ids >", value, "reviewIds");
            return (Criteria) this;
        }

        public Criteria andReviewIdsGreaterThanOrEqualTo(String value) {
            addCriterion("review_ids >=", value, "reviewIds");
            return (Criteria) this;
        }

        public Criteria andReviewIdsLessThan(String value) {
            addCriterion("review_ids <", value, "reviewIds");
            return (Criteria) this;
        }

        public Criteria andReviewIdsLessThanOrEqualTo(String value) {
            addCriterion("review_ids <=", value, "reviewIds");
            return (Criteria) this;
        }

        public Criteria andReviewIdsLike(String value) {
            addCriterion("review_ids like", value, "reviewIds");
            return (Criteria) this;
        }

        public Criteria andReviewIdsNotLike(String value) {
            addCriterion("review_ids not like", value, "reviewIds");
            return (Criteria) this;
        }

        public Criteria andReviewIdsIn(List<String> values) {
            addCriterion("review_ids in", values, "reviewIds");
            return (Criteria) this;
        }

        public Criteria andReviewIdsNotIn(List<String> values) {
            addCriterion("review_ids not in", values, "reviewIds");
            return (Criteria) this;
        }

        public Criteria andReviewIdsBetween(String value1, String value2) {
            addCriterion("review_ids between", value1, value2, "reviewIds");
            return (Criteria) this;
        }

        public Criteria andReviewIdsNotBetween(String value1, String value2) {
            addCriterion("review_ids not between", value1, value2, "reviewIds");
            return (Criteria) this;
        }

        public Criteria andReviewRemarkIsNull() {
            addCriterion("review_remark is null");
            return (Criteria) this;
        }

        public Criteria andReviewRemarkIsNotNull() {
            addCriterion("review_remark is not null");
            return (Criteria) this;
        }

        public Criteria andReviewRemarkEqualTo(String value) {
            addCriterion("review_remark =", value, "reviewRemark");
            return (Criteria) this;
        }

        public Criteria andReviewRemarkNotEqualTo(String value) {
            addCriterion("review_remark <>", value, "reviewRemark");
            return (Criteria) this;
        }

        public Criteria andReviewRemarkGreaterThan(String value) {
            addCriterion("review_remark >", value, "reviewRemark");
            return (Criteria) this;
        }

        public Criteria andReviewRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("review_remark >=", value, "reviewRemark");
            return (Criteria) this;
        }

        public Criteria andReviewRemarkLessThan(String value) {
            addCriterion("review_remark <", value, "reviewRemark");
            return (Criteria) this;
        }

        public Criteria andReviewRemarkLessThanOrEqualTo(String value) {
            addCriterion("review_remark <=", value, "reviewRemark");
            return (Criteria) this;
        }

        public Criteria andReviewRemarkLike(String value) {
            addCriterion("review_remark like", value, "reviewRemark");
            return (Criteria) this;
        }

        public Criteria andReviewRemarkNotLike(String value) {
            addCriterion("review_remark not like", value, "reviewRemark");
            return (Criteria) this;
        }

        public Criteria andReviewRemarkIn(List<String> values) {
            addCriterion("review_remark in", values, "reviewRemark");
            return (Criteria) this;
        }

        public Criteria andReviewRemarkNotIn(List<String> values) {
            addCriterion("review_remark not in", values, "reviewRemark");
            return (Criteria) this;
        }

        public Criteria andReviewRemarkBetween(String value1, String value2) {
            addCriterion("review_remark between", value1, value2, "reviewRemark");
            return (Criteria) this;
        }

        public Criteria andReviewRemarkNotBetween(String value1, String value2) {
            addCriterion("review_remark not between", value1, value2, "reviewRemark");
            return (Criteria) this;
        }

        public Criteria andReviewItemNamesIsNull() {
            addCriterion("review_item_names is null");
            return (Criteria) this;
        }

        public Criteria andReviewItemNamesIsNotNull() {
            addCriterion("review_item_names is not null");
            return (Criteria) this;
        }

        public Criteria andReviewItemNamesEqualTo(String value) {
            addCriterion("review_item_names =", value, "reviewItemNames");
            return (Criteria) this;
        }

        public Criteria andReviewItemNamesNotEqualTo(String value) {
            addCriterion("review_item_names <>", value, "reviewItemNames");
            return (Criteria) this;
        }

        public Criteria andReviewItemNamesGreaterThan(String value) {
            addCriterion("review_item_names >", value, "reviewItemNames");
            return (Criteria) this;
        }

        public Criteria andReviewItemNamesGreaterThanOrEqualTo(String value) {
            addCriterion("review_item_names >=", value, "reviewItemNames");
            return (Criteria) this;
        }

        public Criteria andReviewItemNamesLessThan(String value) {
            addCriterion("review_item_names <", value, "reviewItemNames");
            return (Criteria) this;
        }

        public Criteria andReviewItemNamesLessThanOrEqualTo(String value) {
            addCriterion("review_item_names <=", value, "reviewItemNames");
            return (Criteria) this;
        }

        public Criteria andReviewItemNamesLike(String value) {
            addCriterion("review_item_names like", value, "reviewItemNames");
            return (Criteria) this;
        }

        public Criteria andReviewItemNamesNotLike(String value) {
            addCriterion("review_item_names not like", value, "reviewItemNames");
            return (Criteria) this;
        }

        public Criteria andReviewItemNamesIn(List<String> values) {
            addCriterion("review_item_names in", values, "reviewItemNames");
            return (Criteria) this;
        }

        public Criteria andReviewItemNamesNotIn(List<String> values) {
            addCriterion("review_item_names not in", values, "reviewItemNames");
            return (Criteria) this;
        }

        public Criteria andReviewItemNamesBetween(String value1, String value2) {
            addCriterion("review_item_names between", value1, value2, "reviewItemNames");
            return (Criteria) this;
        }

        public Criteria andReviewItemNamesNotBetween(String value1, String value2) {
            addCriterion("review_item_names not between", value1, value2, "reviewItemNames");
            return (Criteria) this;
        }

        public Criteria andReviewModeIsNull() {
            addCriterion("review_mode is null");
            return (Criteria) this;
        }

        public Criteria andReviewModeIsNotNull() {
            addCriterion("review_mode is not null");
            return (Criteria) this;
        }

        public Criteria andReviewModeEqualTo(Integer value) {
            addCriterion("review_mode =", value, "reviewMode");
            return (Criteria) this;
        }

        public Criteria andReviewModeNotEqualTo(Integer value) {
            addCriterion("review_mode <>", value, "reviewMode");
            return (Criteria) this;
        }

        public Criteria andReviewModeGreaterThan(Integer value) {
            addCriterion("review_mode >", value, "reviewMode");
            return (Criteria) this;
        }

        public Criteria andReviewModeGreaterThanOrEqualTo(Integer value) {
            addCriterion("review_mode >=", value, "reviewMode");
            return (Criteria) this;
        }

        public Criteria andReviewModeLessThan(Integer value) {
            addCriterion("review_mode <", value, "reviewMode");
            return (Criteria) this;
        }

        public Criteria andReviewModeLessThanOrEqualTo(Integer value) {
            addCriterion("review_mode <=", value, "reviewMode");
            return (Criteria) this;
        }

        public Criteria andReviewModeIn(List<Integer> values) {
            addCriterion("review_mode in", values, "reviewMode");
            return (Criteria) this;
        }

        public Criteria andReviewModeNotIn(List<Integer> values) {
            addCriterion("review_mode not in", values, "reviewMode");
            return (Criteria) this;
        }

        public Criteria andReviewModeBetween(Integer value1, Integer value2) {
            addCriterion("review_mode between", value1, value2, "reviewMode");
            return (Criteria) this;
        }

        public Criteria andReviewModeNotBetween(Integer value1, Integer value2) {
            addCriterion("review_mode not between", value1, value2, "reviewMode");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdIsNull() {
            addCriterion("create_oper_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdIsNotNull() {
            addCriterion("create_oper_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdEqualTo(Long value) {
            addCriterion("create_oper_id =", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdNotEqualTo(Long value) {
            addCriterion("create_oper_id <>", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdGreaterThan(Long value) {
            addCriterion("create_oper_id >", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdGreaterThanOrEqualTo(Long value) {
            addCriterion("create_oper_id >=", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdLessThan(Long value) {
            addCriterion("create_oper_id <", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdLessThanOrEqualTo(Long value) {
            addCriterion("create_oper_id <=", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdIn(List<Long> values) {
            addCriterion("create_oper_id in", values, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdNotIn(List<Long> values) {
            addCriterion("create_oper_id not in", values, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdBetween(Long value1, Long value2) {
            addCriterion("create_oper_id between", value1, value2, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdNotBetween(Long value1, Long value2) {
            addCriterion("create_oper_id not between", value1, value2, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameIsNull() {
            addCriterion("create_oper_name is null");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameIsNotNull() {
            addCriterion("create_oper_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameEqualTo(String value) {
            addCriterion("create_oper_name =", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotEqualTo(String value) {
            addCriterion("create_oper_name <>", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameGreaterThan(String value) {
            addCriterion("create_oper_name >", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameGreaterThanOrEqualTo(String value) {
            addCriterion("create_oper_name >=", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameLessThan(String value) {
            addCriterion("create_oper_name <", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameLessThanOrEqualTo(String value) {
            addCriterion("create_oper_name <=", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameLike(String value) {
            addCriterion("create_oper_name like", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotLike(String value) {
            addCriterion("create_oper_name not like", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameIn(List<String> values) {
            addCriterion("create_oper_name in", values, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotIn(List<String> values) {
            addCriterion("create_oper_name not in", values, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameBetween(String value1, String value2) {
            addCriterion("create_oper_name between", value1, value2, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotBetween(String value1, String value2) {
            addCriterion("create_oper_name not between", value1, value2, "createOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdIsNull() {
            addCriterion("update_oper_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdIsNotNull() {
            addCriterion("update_oper_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdEqualTo(Long value) {
            addCriterion("update_oper_id =", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdNotEqualTo(Long value) {
            addCriterion("update_oper_id <>", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdGreaterThan(Long value) {
            addCriterion("update_oper_id >", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdGreaterThanOrEqualTo(Long value) {
            addCriterion("update_oper_id >=", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdLessThan(Long value) {
            addCriterion("update_oper_id <", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdLessThanOrEqualTo(Long value) {
            addCriterion("update_oper_id <=", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdIn(List<Long> values) {
            addCriterion("update_oper_id in", values, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdNotIn(List<Long> values) {
            addCriterion("update_oper_id not in", values, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdBetween(Long value1, Long value2) {
            addCriterion("update_oper_id between", value1, value2, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdNotBetween(Long value1, Long value2) {
            addCriterion("update_oper_id not between", value1, value2, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameIsNull() {
            addCriterion("update_oper_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameIsNotNull() {
            addCriterion("update_oper_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameEqualTo(String value) {
            addCriterion("update_oper_name =", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotEqualTo(String value) {
            addCriterion("update_oper_name <>", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameGreaterThan(String value) {
            addCriterion("update_oper_name >", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameGreaterThanOrEqualTo(String value) {
            addCriterion("update_oper_name >=", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameLessThan(String value) {
            addCriterion("update_oper_name <", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameLessThanOrEqualTo(String value) {
            addCriterion("update_oper_name <=", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameLike(String value) {
            addCriterion("update_oper_name like", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotLike(String value) {
            addCriterion("update_oper_name not like", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameIn(List<String> values) {
            addCriterion("update_oper_name in", values, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotIn(List<String> values) {
            addCriterion("update_oper_name not in", values, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameBetween(String value1, String value2) {
            addCriterion("update_oper_name between", value1, value2, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotBetween(String value1, String value2) {
            addCriterion("update_oper_name not between", value1, value2, "updateOperName");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}