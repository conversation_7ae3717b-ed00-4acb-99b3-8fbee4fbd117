package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.model.UserDeviceInfo;
import com.extracme.evcard.membership.core.model.UserDeviceInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface UserDeviceInfoMapper {
    int countByExample(UserDeviceInfoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(UserDeviceInfo record);

    int insertSelective(UserDeviceInfo record);

    List<UserDeviceInfo> selectByExample(UserDeviceInfoExample example);

    UserDeviceInfo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") UserDeviceInfo record, @Param("example") UserDeviceInfoExample example);

    int updateByExample(@Param("record") UserDeviceInfo record, @Param("example") UserDeviceInfoExample example);

    int updateByPrimaryKeySelective(UserDeviceInfo record);

    int updateByPrimaryKey(UserDeviceInfo record);
}