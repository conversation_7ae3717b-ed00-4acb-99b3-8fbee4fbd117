package com.extracme.evcard.membership.job;


import com.alibaba.fastjson.JSON;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.extracme.evcard.elasticjob.ElasticSimpleJob;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.dao.MemberIdentityDocumentMapper;
import com.extracme.evcard.membership.core.enums.IdentityAuthStatusEnum;
import com.extracme.evcard.membership.core.model.MemberIdentityDocument;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.model.MembershipInfo;
import com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs;
import com.extracme.evcard.membership.share.service.MembershipShareServiceImpl;
import com.extracme.evcard.saic.service.ISaicMemberService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.BinaryDecoder;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;


/**
 * 审核通过已认证
 * 非大陆用户证件信息初始化
 */
@Slf4j
@Component
@ElasticSimpleJob(jobName = "evcard-membership-unNativeIdentityInitJob",
        cron = "0 00 16 20 7 ? 2032", description = "非大陆认证会员身份证件信息初始化JOB", overwrite = true)
public class UnNativeIdentityInitJob implements SimpleJob {

    @Resource
    private MemberIdentityDocumentMapper memberIdentityDocumentMapper;

    @Resource
    private MembershipInfoMapper membershipInfoMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void execute(ShardingContext arg0) {
        log.info("====================非本籍用户的证件信息同步，开始");
        String params = arg0.getJobParameter();
        if(StringUtils.isBlank(params)) {
            return;
        }
        String pkIdStr = params;
        String[] strs = StringUtils.split(params, ",");
        Set<Long> list = new HashSet<>();
        for(String str : strs) {
            list.add(Long.valueOf(str));
        }
        dowork(list);
        log.info("====================非本籍用户的证件信息同步，结束");
    }

    public void dowork(Set<Long> pkIds) {
        if(CollectionUtils.isEmpty(pkIds)) {
            return;
        }
        List<MembershipInfoWithBLOBs> members = membershipInfoMapper.selectByPkIds(pkIds);
        for(MembershipInfoWithBLOBs member : members) {
            MemberIdentityDocument identity = new MemberIdentityDocument();
            identity.setMid(member.getMid());
            identity.setIdentityNo(member.getPassportNo());
            identity.setIdentityType(member.getIdType());
            identity.setName(member.getName());
            identity.setExpireType(2);
            identity.setIdentityCardImgUrl(member.getIdcardPicUrl());
            identity.setHoldIdcardPicUrl(member.getHoldIdcardPicUrl());
            identity.setFaceRecognitionImgUrl(member.getFaceRecognitionImgUrl());
            identity.setCertInputType(2);
            identity.setAuthenticationStatus(IdentityAuthStatusEnum.AUTHENTICATED.getValue());
            identity.setSubmitTime(ComUtil.getDateFromStr(member.getReviewTime(), ComUtil.DATE_TYPE4));
            identity.setSubmitAppkey("evcard-app");
            identity.setReviewUser(member.getReviewUser());
            identity.setReviewTime(identity.getSubmitTime());
            identity.setReviewMode(2);
            identity.setCreateTime(new Date());
            identity.setCreateOperId(1L);
            identity.setCreateOperName("user"); //membership-rpc
            try{
                MemberIdentityDocument identityDocument = memberIdentityDocumentMapper.selectOneByMid(identity.getMid());
                if(identityDocument != null) {
                    if(!identityDocument.getCreateOperName().equals(identity.getCreateOperName())) {
                        log.info("新增非本籍用户的证件信息，已存在用户新提交数据，跳过，mid={}, recoreId={}", member.getMid(), identityDocument.getIdentityNo());
                        continue;
                    }
                    identity.setId(identityDocument.getId());
                    memberIdentityDocumentMapper.updateByPrimaryKeySelective(identity);
                    log.info("新增非本籍用户的证件信息，已更新，mid={}, recoreId={}", member.getMid(), identityDocument.getIdentityNo());
                } else {
                    memberIdentityDocumentMapper.insertSelective(identity);
                    log.info("新增非本籍用户的证件信息，已新增，mid={}", member.getMid());
                }
                MembershipInfoWithBLOBs record = new MembershipInfoWithBLOBs();
                record.setPkId(member.getPkId());
                record.setIdentityId(identity.getId());
                record.setIdentityFirstAuthTime(identity.getReviewTime());
                membershipInfoMapper.updateByPrimaryKeySelective(record);
            }catch (Exception e) {
                log.error("新增非本籍用户的证件信息失败，mid=" + member.getMid() + "mid=" + JSON.toJSONString(identity), e);
            }
        }
        log.info("====================非本籍用户的证件信息同步完成, size={}", members.size());
    }
}
