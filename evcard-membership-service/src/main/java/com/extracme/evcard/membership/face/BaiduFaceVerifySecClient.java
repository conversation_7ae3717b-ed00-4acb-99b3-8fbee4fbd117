package com.extracme.evcard.membership.face;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.extracme.evcard.membership.third.baidu.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 实名认证增强版
 */
@Slf4j
@Component
public class BaiduFaceVerifySecClient {

    @Value("j5rgtVrKpk5y9aETFQwzK9pf")
    private String ak;

    @Value("61BLGR2CUS31xoDU4irSRwKl761wOXvK")
    private String sk;

    @Value("https://aip.baidubce.com/")
    private String baseUrl;

    @Autowired
    private BaiduAccessTokenClient accessTokenClient;

    /**
     * 人脸比对
     */
    public BaseResult<PersonVerifySecResult> personVerify(PersonVerifySecRequest request) {
        // 请求url
        String url = baseUrl + "rest/2.0/face/v3/person/verifySec";
        try {
            String param = JSON.toJSONString(request);
            String accessToken = getAuthToken();
            if(StringUtils.isBlank(accessToken)) {
                log.error("获取baidubce accessToken失败");
                return null;
            }
            //请求人脸身份认证接口
            String result = HttpUtil.post(url, accessToken, "application/json", param);
            BaseResult<PersonVerifySecResult> resp = JSON.parseObject(result, new TypeReference<BaseResult<PersonVerifySecResult>>() {});
            log.warn("baidu:person/verifySec input={},accessToken={}, result={}， {}",
                    JSON.toJSONString(request.toLogRequest()), accessToken, resp.getError_code(), resp.getError_msg());
            return resp;
        } catch (Exception e) {
            log.error("personVerify faild " + JSON.toJSONString(request.toLogRequest()), e);
        }
        return null;
    }

    public String getAuthToken(){
        return accessTokenClient.getAuthToken(ak, sk);
    }
}
