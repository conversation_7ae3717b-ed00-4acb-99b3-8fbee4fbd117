<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.core.dao.VehicleControlUserMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.VehicleControlUser" >
    <id column="USERID" property="userid" jdbcType="VARCHAR" />
  </resultMap>
  <select id="getUser" resultType="com.extracme.evcard.membership.core.model.VehicleControlUser">
    SELECT USERID AS userid
    FROM ${siacSchema}.VEHICLE_CONTROL_USER
    WHERE USERID =#{userId}
  </select>
</mapper>