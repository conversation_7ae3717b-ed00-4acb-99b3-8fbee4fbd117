package com.extracme.evcard.membership.face;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.extracme.evcard.membership.third.baidu.BaseResult;
import com.extracme.evcard.membership.third.baidu.HttpUtil;
import com.extracme.evcard.membership.third.baidu.PersonVerifyRequest;
import com.extracme.evcard.membership.third.baidu.PersonVerifyResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Discription
 * @date 2021/3/17
 */
@Slf4j
@Component
public class BaiduFaceVerifyClient {

    //@Value("${baidu.face.ak}")
    @Value("j5rgtVrKpk5y9aETFQwzK9pf")
    private String ak;

    //@Value("${baidu.face.sk}")
    @Value("61BLGR2CUS31xoDU4irSRwKl761wOXvK")
    private String sk;

    //@Value("${baidu.face.api}")
    @Value("${new.baidu.face.api:https://aip.baidubce.com/}")
    private String baseUrl;


    public String getAuthToken(){
        /**
         * TODO 添加缓存
         * 暂时每次都重新获取
         */
        return getAuthToken(ak, sk);
    }

    public BaseResult<PersonVerifyResult> personVerify(PersonVerifyRequest request) {
        // 请求url
        String url = baseUrl + "rest/2.0/face/v3/person/verify";
        //String url = baseUrl + "rest/2.0/face/v3/person/verifySec";
        try {
//            Map<String, Object> map = new HashMap<>();
//            map.put("image", "sfasq35sadvsvqwr5q...");
//            map.put("liveness_control", "HIGH");
//            map.put("name", "张三");
//            map.put("id_card_number", "110...");
//            map.put("image_type", "BASE64");
//            map.put("quality_control", "LOW");

            String param = JSON.toJSONString(request);
            // 注意这里仅为了简化编码每一次请求都去获取access_token，线上环境access_token有过期时间， 客户端可自行缓存，过期后重新获取。
            String accessToken = getAuthToken();
            if(StringUtils.isBlank(accessToken)) {
                log.error("获取baidubce accessToken失败");
                return null;
            }
            //请求人脸身份认证接口
            String result = HttpUtil.post(url, accessToken, "application/json", param);
            BaseResult<PersonVerifyResult> resp = JSON.parseObject(result, new TypeReference<BaseResult<PersonVerifyResult>>() {});
            log.warn("baidu:drivingLicenseVerify ,url={},input={}, result={}", url,param, result);
            return resp;
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }

    public String getAuthToken(String ak, String sk) {
        // 获取token地址
        String authHost = baseUrl + "oauth/2.0/token?";
        String getAccessTokenUrl = authHost
                // 1. grant_type为固定参数
                + "grant_type=client_credentials"
                // 2. 官网获取的 API Key
                + "&client_id=" + ak
                // 3. 官网获取的 Secret Key
                + "&client_secret=" + sk;
        try {
            URL realUrl = new URL(getAccessTokenUrl);
            // 打开和URL之间的连接
            HttpURLConnection connection = (HttpURLConnection) realUrl.openConnection();
            connection.setRequestMethod("GET");
            connection.connect();
            // 获取所有响应头字段
            Map<String, List<String>> map = connection.getHeaderFields();
            // 遍历所有的响应头字段
            for (String key : map.keySet()) {
                log.info("baidu getAuthToken key:  "+ key + "--->" + map.get(key));
            }
            // 定义 BufferedReader输入流来读取URL的响应
            BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String result = "";
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
            /**
             * 返回结果示例
             */
            log.info("baidu getAuthToken result:" + result);
            JSONObject jsonObject = JSONObject.parseObject(result);
            String access_token = jsonObject.getString("access_token");
            return access_token;
        } catch (Exception e) {
            log.error("获取token失败", e);
        }
        return null;
    }
}
