package com.extracme.evcard.membership.core.service;

import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.dao.ChannelBlacklistLogMapper;
import com.extracme.evcard.membership.core.dao.ChannelBlacklistMapper;
import com.extracme.evcard.membership.core.dao.UserOperatorLogMapper;
import com.extracme.evcard.membership.core.dto.blacklist.*;
import com.extracme.evcard.membership.core.dto.md.SaveBlackListLogRequest;
import com.extracme.evcard.membership.core.enums.EnableStatusEnum;
import com.extracme.evcard.membership.core.enums.IdTypeEnum;
import com.extracme.evcard.membership.core.model.ChannelBlacklist;
import com.extracme.evcard.membership.core.model.ChannelBlacklistLog;
import com.extracme.evcard.membership.core.service.md.MdOrderService;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.model.MembershipInfo;
import com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service("channelBlacklistService")
@Slf4j
public class ChannelBlacklistServiceImpl implements IChannelBlacklistService{

    @Resource
    private ChannelBlacklistMapper channelBlacklistMapper;

    @Resource
    private ChannelBlacklistLogMapper channelBlacklistLogMapper;
    @Resource
    private MembershipInfoMapper membershipInfoMapper;
    @Autowired
    private UserOperatorLogMapper userOperatorLogMapper;

    @Autowired
    private MdOrderService mdOrderService;

    @Override
    public ListChannelBlacklistResponse listChannelBlacklist(ListChannelBlacklistDto dto) {
        if (dto.getPageSize() != null && dto.getPageNum() != null) {
            dto.setOffset((dto.getPageNum() - 1) * dto.getPageSize());
        }
        ListChannelBlacklistResponse response = new ListChannelBlacklistResponse();
        List<ChannelBlacklistDto> channelBlacklists = channelBlacklistMapper.listChannelBlacklist(dto);
        Integer countChannelBlacklist = channelBlacklistMapper.countChannelBlacklist(dto);
        response.setList(channelBlacklists);
        response.setTotal(countChannelBlacklist);
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer insert(UpdateChannelBlacklistDto dto) {
        if (StringUtils.isAnyBlank(dto.getUserName(), dto.getCertificateNum(), dto.getRemark())
                || dto.getCertificateType() == null || dto.getBlacklistType() == null) {
            throw new RuntimeException("请将信息填写完整！");
        }
        //校验身份证号
        if (dto.getCertificateType() == 1) {
            if (!ComUtil.checkIDCard(dto.getCertificateNum())) {
                throw new RuntimeException("请输入正确的身份证号");
            }
        }
        ChannelBlacklist channelBlacklist = new ChannelBlacklist();
        Date now = new Date();
        if (checkRepetitiveMobileAndCertificateNum(dto.getMobilePhone(), dto.getCertificateNum(),null)) {
            throw new RuntimeException("证件号+手机号重复，不可重复添加");
        }
        BeanUtils.copyProperties(dto, channelBlacklist);
        channelBlacklist.setEnableStatus(EnableStatusEnum.ENABLE.getCode());
        channelBlacklist.setBlacklistType(BussinessConstants.BLACKLIST_TYPE_BLACK);
        channelBlacklist.setCreateBy(dto.getOperateUser());
        channelBlacklist.setCreateTime(now);
        channelBlacklist.setUpdateBy(dto.getOperateUser());
        channelBlacklist.setUpdateTime(now);
        channelBlacklist.setBlacklistSource(dto.getBlacklistSource() == null ? 1 : dto.getBlacklistSource());
        Integer result = channelBlacklistMapper.insert(channelBlacklist);
        if (result < 1) {
            throw new RuntimeException("新增黑名单用户失败");
        }
        ChannelBlacklistLog channelBlacklistLog = new ChannelBlacklistLog();
        channelBlacklistLog.setChannelBlacklistId(channelBlacklist.getId());
        channelBlacklistLog.setContent("创建成功");
        channelBlacklistLog.setCreateBy(dto.getOperateUser());
        channelBlacklistLog.setCreateTime(now);
        channelBlacklistLog.setUpdateBy(dto.getOperateUser());
        channelBlacklistLog.setUpdateTime(now);
        result = channelBlacklistLogMapper.insert(channelBlacklistLog);
        if (result < 1) {
            throw new RuntimeException("新增黑名单用户失败");
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateById(UpdateChannelBlacklistDto dto) {
        if (dto.getId() == null) {
            throw new RuntimeException("请选择要修改的黑名单用户");
        }
        if (StringUtils.isAnyBlank(dto.getRemark())
                || dto.getEnableStatus() == null) {
            throw new RuntimeException("请将信息填写完整！");
        }
        ChannelBlacklist channelBlacklist = channelBlacklistMapper.getById(dto.getId());
        if (channelBlacklist == null) {
            throw new RuntimeException("该黑名单用户不存在");
        }
        if (checkRepetitiveMobileAndCertificateNum(dto.getMobilePhone(), dto.getCertificateNum(),dto.getId())) {
            throw new RuntimeException("证件号+手机号重复，不可重复添加");
        }

        String originRemark = channelBlacklist.getRemark();
        Integer originEnableStatus = channelBlacklist.getEnableStatus();
        String originMobilePhone = channelBlacklist.getMobilePhone();
        String originCertificateNum = channelBlacklist.getCertificateNum();
        String updateRemark = dto.getRemark();
        Integer updateEnableStatus = dto.getEnableStatus();
        String updateMobilePhone = StringUtils.isBlank(dto.getMobilePhone()) ? null : dto.getMobilePhone();
        String updateCertificateNum = dto.getCertificateNum();
        Date now = new Date();
        channelBlacklist.setMobilePhone(updateMobilePhone);
        channelBlacklist.setEnableStatus(dto.getEnableStatus());
        channelBlacklist.setRemark(dto.getRemark());
        channelBlacklist.setCertificateNum(updateCertificateNum);
        channelBlacklist.setUpdateBy(dto.getOperateUser());
        channelBlacklist.setUpdateTime(now);
        Integer result = channelBlacklistMapper.updateStatusById(channelBlacklist);
        if (result < 1) {
            throw new RuntimeException("修改黑名单用户失败");
        }
        //启用 和 禁用，同步对个人会员进行操作 ----暂不启用
//        List<MembershipInfo> membershipInfos = membershipInfoMapper.getInfoByPassportNoAndPhone(channelBlacklist.getCertificateNum(), StringUtils.isBlank(updateMobilePhone)? null :updateMobilePhone);
//        if (CollectionUtils.isNotEmpty(membershipInfos) && !Objects.equals(originEnableStatus, updateEnableStatus)){
//            for (MembershipInfo membershipInfo : membershipInfos) {
//                MembershipInfoWithBLOBs updateMember = new MembershipInfoWithBLOBs();
//                updateMember.setUpdatedUser("黑名单数据同步");
//                updateMember.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
//                //黑名单 1 启用， 对应信息表1；黑名单2 禁用，对应信息表0
//                updateMember.setStatus(dto.getEnableStatus() == 1 ? (short) 1 : (short) 0);
//                updateMember.setPkId(membershipInfo.getPkId());
//                Integer num = membershipInfoMapper.updateByPrimaryKeySelective(updateMember);
//                if (num > 0) {
//                    String operatorContent = dto.getEnableStatus() == 1 ? "加入黑名单" : "移出黑名单";
//                    ComUtil.insertOperatorLog(operatorContent, membershipInfo.getAuthId(), dto.getOperateUserId()+ StringUtils.EMPTY, dto.getOperateUser(),
//                            userOperatorLogMapper);
//                }
//            }
//        }

        String content = generateContent(originRemark, updateRemark, originEnableStatus, updateEnableStatus,originMobilePhone,updateMobilePhone,originCertificateNum,updateCertificateNum);
        ChannelBlacklistLog channelBlacklistLog = new ChannelBlacklistLog();
        channelBlacklistLog.setChannelBlacklistId(channelBlacklist.getId());
        channelBlacklistLog.setContent(content);
        channelBlacklistLog.setCreateBy(dto.getOperateUser());
        channelBlacklistLog.setCreateTime(now);
        channelBlacklistLog.setUpdateBy(dto.getOperateUser());
        channelBlacklistLog.setUpdateTime(now);
        result = channelBlacklistLogMapper.insert(channelBlacklistLog);
        if (result < 1) {
            throw new RuntimeException("修改黑名单用户失败");
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateByCertificateNumAndPhone(UpdateChannelBlacklistDto dto) {
        String mobilePhone = dto.getMobilePhone();
        String certificateNum = dto.getCertificateNum();
        List<ChannelBlacklist> channelBlacklists = channelBlacklistMapper.queryByMobileAndCertificateNum(mobilePhone, certificateNum,null);
        Integer updateNum = 0;
        if (CollectionUtils.isNotEmpty(channelBlacklists)){
            for (ChannelBlacklist channelBlacklist : channelBlacklists) {
                Integer originEnableStatus = channelBlacklist.getEnableStatus();
                Integer updateEnableStatus = dto.getEnableStatus();
                Date now = new Date();
                channelBlacklist.setEnableStatus(dto.getEnableStatus());
                channelBlacklist.setUpdateBy(dto.getOperateUser());
                channelBlacklist.setUpdateTime(now);
                Integer result = channelBlacklistMapper.updateStatusById(channelBlacklist);
                //记录日志
                String content = generateContent(null, null, originEnableStatus, updateEnableStatus,null,null,null,null);
                ChannelBlacklistLog channelBlacklistLog = new ChannelBlacklistLog();
                channelBlacklistLog.setChannelBlacklistId(channelBlacklist.getId());
                channelBlacklistLog.setContent(content);
                channelBlacklistLog.setCreateBy(dto.getOperateUser());
                channelBlacklistLog.setCreateTime(now);
                channelBlacklistLog.setUpdateBy(dto.getOperateUser());
                channelBlacklistLog.setUpdateTime(now);
                result = channelBlacklistLogMapper.insert(channelBlacklistLog);
                updateNum += result;
            }
        }
        return updateNum;
    }

    private String generateContent(String originRemark, String updateRemark, Integer originEnableStatus, Integer updateEnableStatus,
                                   String originMobilePhone, String updateMobilePhone, String originCertificateNum, String updateCertificateNum) {
        if (Objects.equals(originRemark, updateRemark) && Objects.equals(originEnableStatus, updateEnableStatus) && Objects.equals(originMobilePhone, updateMobilePhone)
        && Objects.equals(originCertificateNum, updateCertificateNum)) {
            return "";
        }
        String originContent = "编辑【";
        String updateContent = "【";
        boolean flag = false;
        boolean statusFlag = false;
        boolean phoneFlag = false;
        if (!Objects.equals(originRemark, updateRemark)) {
            originContent += "备注：" + originRemark;
            updateContent += "备注：" + updateRemark;
            flag = true;
        }
        if (!Objects.equals(originEnableStatus, updateEnableStatus)) {
            originContent += (flag ? "、" : "") + "状态：" + getEnableStatusByCode(originEnableStatus);
            updateContent += (flag ? "、" : "") + "状态：" + getEnableStatusByCode(updateEnableStatus);
            statusFlag =true;
        }
        if (!Objects.equals(originMobilePhone, updateMobilePhone)) {
            originContent += (statusFlag || flag ? "、" : "") + "手机号：" + originMobilePhone;
            updateContent += (statusFlag || flag ? "、" : "") + "手机号：" + updateMobilePhone;
            phoneFlag = true;
        }
        if (!Objects.equals(originCertificateNum, updateCertificateNum)) {
            originContent += (phoneFlag || statusFlag || flag ? "、" : "") + "证件号：" + originCertificateNum;
            updateContent += (phoneFlag || statusFlag || flag ? "、" : "") + "证件号：" + updateCertificateNum;
        }
        originContent += "】";
        updateContent += "】";

        return originContent + "改为" + updateContent;
    }

    @Override
    public ChannelBlacklistDto getById(Integer id) {
        ChannelBlacklist channelBlacklist = channelBlacklistMapper.getById(id);
        if (channelBlacklist == null) {
            throw new RuntimeException("该黑名单用户不存在");
        }
        ChannelBlacklistDto channelBlacklistDto = new ChannelBlacklistDto();
        BeanUtils.copyProperties(channelBlacklist, channelBlacklistDto);
        return channelBlacklistDto;
    }

    @Override
    public Boolean judgeBlacklist(String mobilePhone, String certificateNum, String contractId, int type, String userName) {
        if (StringUtils.isEmpty(mobilePhone) && StringUtils.isEmpty(certificateNum)) {
            return Boolean.FALSE;
        }
        List<ChannelBlacklist> channelBlacklists = channelBlacklistMapper.queryByMobileOrCertificateNum(mobilePhone, certificateNum);
        if (CollectionUtils.isNotEmpty(channelBlacklists)) {
            try {
                SaveBlackListLogRequest saveBlackListLogRequest = new SaveBlackListLogRequest();
                saveBlackListLogRequest.setMobilePhone(mobilePhone);
                saveBlackListLogRequest.setIdNumber(certificateNum);
                saveBlackListLogRequest.setContractId(contractId);
                saveBlackListLogRequest.setType(type);
                saveBlackListLogRequest.setUserName(userName);
                mdOrderService.saveBlackListLog(saveBlackListLogRequest);
            } catch (Exception e) {
                log.error("调用mdOrderService.saveBlackListLog异常");
            }
            return true;
        }
        else {
            return false;
        }
    }

    @Override
    public ListChannelBlacklistLogResponse listChannelBlacklistLog(ListChannelBlacklistLogDto dto) {
        if (dto.getPageNum() != null && dto.getPageSize() != null) {
            dto.setOffset((dto.getPageNum()-1) * dto.getPageSize());
        }
        ListChannelBlacklistLogResponse response = new ListChannelBlacklistLogResponse();
        List<ChannelBlacklistLog> channelBlacklistLogs = channelBlacklistLogMapper.listChannelBlacklistLog(dto);
        List<ChannelBlacklistLogDto> channelBlacklistLogDtoList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (ChannelBlacklistLog channelBlacklistLog : channelBlacklistLogs) {
            ChannelBlacklistLogDto channelBlacklistLogDto = new ChannelBlacklistLogDto();
            BeanUtils.copyProperties(channelBlacklistLog, channelBlacklistLogDto);
            channelBlacklistLogDto.setOperateTime(sdf.format(channelBlacklistLog.getCreateTime()));
            channelBlacklistLogDtoList.add(channelBlacklistLogDto);
        }
        Integer total = channelBlacklistLogMapper.countChannelBlacklistLog(dto);
        response.setList(channelBlacklistLogDtoList);
        response.setTotal(total);
        return response;
    }


    private Boolean checkRepetitiveMobileAndCertificateNum(String mobilePhone, String certificateNum,Integer id) {
        List<ChannelBlacklist> channelBlacklists = channelBlacklistMapper.queryByMobileAndCertificateNum(mobilePhone, certificateNum,id);
        return CollectionUtils.isNotEmpty(channelBlacklists);
    }

    private String getEnableStatusByCode(Integer id) {
        if (id == 1) {
            return "启用";
        }
        else if (id == 2) {
            return "禁用";
        }
        else {
            return "";
        }
    }


    public Integer insertOrUpdate(UpdateChannelBlacklistDto dto) {
        List<ChannelBlacklist> channelBlacklists = channelBlacklistMapper.queryByMobileAndCertificateNum(dto.getMobilePhone(), dto.getCertificateNum(),null);
        if (CollectionUtils.isNotEmpty(channelBlacklists)){
            //黑名单已经存在数据，则更新状态
            channelBlacklists.forEach(channelBlacklist->{
                UpdateChannelBlacklistDto updateChannelBlacklistDto = new UpdateChannelBlacklistDto();
                BeanUtils.copyProperties(channelBlacklist, updateChannelBlacklistDto);
                updateChannelBlacklistDto.setOperateUser(dto.getOperateUser());
                updateChannelBlacklistDto.setOperateUserId(dto.getOperateUserId());
                updateChannelBlacklistDto.setId(channelBlacklist.getId().intValue());
                updateChannelBlacklistDto.setEnableStatus(dto.getEnableStatus());
                this.updateById(updateChannelBlacklistDto);
            });
            return channelBlacklists.size();
        }else {
            //黑名单不存在且启用，则做新增
            if (dto.getEnableStatus() == 1) {
                return this.insert(dto);
            }
            else {
                return 1;
            }
        }
    }
}
