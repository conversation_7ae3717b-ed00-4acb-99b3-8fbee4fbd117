package com.extracme.evcard.membership.job;

import com.alibaba.fastjson.JSON;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.extracme.evcard.elasticjob.ElasticSimpleJob;
import com.extracme.evcard.membership.credit.dao.MmpUserTagMapper;
import com.extracme.evcard.membership.credit.model.MmpUserTag;
import com.extracme.evcard.rpc.pay.service.IMemAccountService;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;


/**
 * 解约 老芝麻（只刷一次）
 *
 * <AUTHOR>
 * @date 2023/9/2
 */
@Slf4j
//@Component
//@ElasticSimpleJob(jobName = "evcard-membership-cancleoldzm", cron = "0 00 16 20 7 ? 2099", description = "解约老芝麻产品任务", overwrite = true)
public class CancleOldZhiMaJob implements SimpleJob {

    @Autowired
    private MmpUserTagMapper mmpUserTagMapper;
    @Resource
    private IMemAccountService memAccountService;

    @Value("${cancle.oldzhima.enable:1}")
    private String cancleOldzhimaEnable;

    @Value("${cancle.oldzhima.start.id:0}")
    private String startId;

    @Value("${cancle.oldzhima.limit.size:1000}")
    private String limtSize;

    @Override
    public void execute(ShardingContext shardingContext) {
        log.info("CancleOldZhiMaJob 新解约老芝麻产品任务 start！shardingContext={}", JSON.toJSONString(shardingContext));
        long id = Long.valueOf(startId);
        while ("1".equals(cancleOldzhimaEnable)) {
            log.info("CancleOldZhiMaJob 新解约老芝麻产品任务 批量处理开始！id={}", id);
            int limit = Integer.valueOf(limtSize);
            List<MmpUserTag> spare4NotEmptyList = mmpUserTagMapper.getSpare4NotEmptyList(id, limit);
            if (CollectionUtils.isNotEmpty(spare4NotEmptyList)) {
                spare4NotEmptyList.stream().forEach(mmpUserTag -> {
                    try {
                        String authId = mmpUserTag.getAuthId();
                        String agreementNo = mmpUserTag.getSpare4();
                        if (StringUtils.isNotBlank(agreementNo) && StringUtils.isNotBlank(authId)) {
                            DefaultServiceRespDTO dto = memAccountService.cancelWithHold(authId, agreementNo, true);
                            if (dto != null && dto.getCode() == 0) {
                                log.info("cancleOldZhiMa解约老芝麻成功，auhtId=[{}],agreementNo=[{}]", authId, agreementNo);
                            } else {
                                log.error("cancleOldZhiMa解约老芝麻失败，auhtId=[{}],agreementNo=[{}],dto=[{}]", authId, agreementNo, JSON.toJSONString(dto));
                            }
                        }
                    } catch (Exception e) {
                        log.error("cancleOldZhiMa解约老芝麻异常，mmpUserTag=[{}]", JSON.toJSONString(mmpUserTag));
                    }
                });
                // 取最后一条记录id
                id = spare4NotEmptyList.get(spare4NotEmptyList.size() - 1).getId();
            } else {
                break;
            }
            log.info("CancleOldZhiMaJob 新解约老芝麻产品任务 批量处理结束！id={}", id);
        }
        log.info("CancleOldZhiMaJob 新解约老芝麻产品任务 end！shardingContext={}", JSON.toJSONString(shardingContext));

    }

}
