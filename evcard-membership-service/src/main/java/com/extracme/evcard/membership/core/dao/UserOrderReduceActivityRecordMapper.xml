<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.core.dao.UserOrderReduceActivityRecordMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.UserOrderReduceActivityRecord" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 28 10:10:42 CST 2021.
    -->
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="auth_id" property="authId" jdbcType="VARCHAR" />
    <result column="order_seq" property="orderSeq" jdbcType="VARCHAR" />
    <result column="reduce_amount_activity_id" property="reduceAmountActivityId" jdbcType="BIGINT" />
    <result column="order_reduce_amount" property="orderReduceAmount" jdbcType="DECIMAL" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="misc_desc" property="miscDesc" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 28 10:10:42 CST 2021.
    -->
    id, auth_id, order_seq, reduce_amount_activity_id, order_reduce_amount, status, misc_desc, 
    create_time, create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 28 10:10:42 CST 2021.
    -->
    select 
    <include refid="Base_Column_List" />
    from ${siacSchema}.user_order_reduce_activity_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.core.model.UserOrderReduceActivityRecord" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 28 10:10:42 CST 2021.
    -->
    insert into ${siacSchema}.user_order_reduce_activity_record
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="authId != null" >
        auth_id,
      </if>
      <if test="orderSeq != null" >
        order_seq,
      </if>
      <if test="reduceAmountActivityId != null" >
        reduce_amount_activity_id,
      </if>
      <if test="orderReduceAmount != null" >
        order_reduce_amount,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="miscDesc != null" >
        misc_desc,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateOperId != null" >
        update_oper_id,
      </if>
      <if test="updateOperName != null" >
        update_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="authId != null" >
        #{authId,jdbcType=VARCHAR},
      </if>
      <if test="orderSeq != null" >
        #{orderSeq,jdbcType=VARCHAR},
      </if>
      <if test="reduceAmountActivityId != null" >
        #{reduceAmountActivityId,jdbcType=BIGINT},
      </if>
      <if test="orderReduceAmount != null" >
        #{orderReduceAmount,jdbcType=DECIMAL},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="miscDesc != null" >
        #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        #{updateOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.core.model.UserOrderReduceActivityRecord" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 28 10:10:42 CST 2021.
    -->
    update ${siacSchema}.user_order_reduce_activity_record
    <set >
      <if test="authId != null" >
        auth_id = #{authId,jdbcType=VARCHAR},
      </if>
      <if test="orderSeq != null" >
        order_seq = #{orderSeq,jdbcType=VARCHAR},
      </if>
      <if test="reduceAmountActivityId != null" >
        reduce_amount_activity_id = #{reduceAmountActivityId,jdbcType=BIGINT},
      </if>
      <if test="orderReduceAmount != null" >
        order_reduce_amount = #{orderReduceAmount,jdbcType=DECIMAL},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="miscDesc != null" >
        misc_desc = #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByCondition" resultMap="BaseResultMap" parameterType="com.extracme.evcard.membership.core.model.UserOrderReduceActivityRecord" >
    select
        <include refid="Base_Column_List" />
    from ${siacSchema}.user_order_reduce_activity_record
    where `status` = 1
    <if test="authId != null" >
      and auth_id = #{authId,jdbcType=VARCHAR}
    </if>
    <if test="orderSeq != null" >
      and order_seq = #{orderSeq,jdbcType=VARCHAR}
    </if>
    <if test="reduceAmountActivityId != null" >
      and reduce_amount_activity_id = #{reduceAmountActivityId,jdbcType=BIGINT}
    </if>
  </select>

</mapper>