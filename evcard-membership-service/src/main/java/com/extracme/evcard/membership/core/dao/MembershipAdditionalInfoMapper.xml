<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.core.dao.MembershipAdditionalInfoMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.MembershipAdditionalInfo" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="auth_id" property="authId" jdbcType="VARCHAR" />
    <result column="auth_kind" property="authKind" jdbcType="VARCHAR" />
    <result column="id_card_number" property="idCardNumber" jdbcType="VARCHAR" />
    <result column="certificate_validity" property="certificateValidity" jdbcType="VARCHAR" />
    <result column="certificate_address" property="certificateAddress" jdbcType="VARCHAR" />
    <result column="driving_license" property="drivingLicense" jdbcType="VARCHAR" />
    <result column="driving_license_type" property="drivingLicenseType" jdbcType="VARCHAR" />
    <result column="national" property="national" jdbcType="VARCHAR" />
    <result column="obtain_driver_timer" property="obtainDriverTimer" jdbcType="VARCHAR" />
    <result column="license_expiration_time" property="licenseExpirationTime" jdbcType="VARCHAR" />
    <result column="driving_license_img_url" property="drivingLicenseImgUrl" jdbcType="VARCHAR" />
    <result column="idcard_pic_url" property="idcardPicUrl" jdbcType="VARCHAR" />
    <result column="hold_idcard_pic_url" property="holdIdcardPicUrl" jdbcType="VARCHAR" />
    <result column="file_no" property="fileNo" jdbcType="VARCHAR" />
    <result column="file_no_img_url" property="fileNoImgUrl" jdbcType="VARCHAR" />
    <result column="review_time" property="reviewTime" jdbcType="VARCHAR" />
    <result column="review_user" property="reviewUser" jdbcType="VARCHAR" />
    <result column="app_review_time" property="appReviewTime" jdbcType="VARCHAR" />
    <result column="review_status" property="reviewStatus" jdbcType="INTEGER" />
    <result column="review_items" property="reviewItems" jdbcType="VARCHAR" />
    <result column="review_remark" property="reviewRemark" jdbcType="VARCHAR" />
    <result column="review_item_ids" property="reviewItemIds" jdbcType="VARCHAR" />
    <result column="review_item_name" property="reviewItemName" jdbcType="VARCHAR" />
    <result column="review_mode" property="reviewMode" jdbcType="INTEGER" />
    <result column="personal_address" property="personalAddress" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, auth_id, auth_kind, id_card_number, certificate_validity, certificate_address, 
    driving_license, driving_license_type, national, obtain_driver_timer, license_expiration_time, 
    driving_license_img_url, idcard_pic_url, hold_idcard_pic_url, file_no,file_no_img_url, review_time, review_user,
    app_review_time, review_status, review_items, review_remark, review_item_ids, review_item_name, 
    review_mode,personal_address, remark, status, create_time, create_oper_id, create_oper_name, update_time,
    update_oper_id, update_oper_name
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from ${siacSchema}.membership_additional_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from ${siacSchema}.membership_additional_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.membership.core.model.MembershipAdditionalInfo" >
    insert into ${siacSchema}.membership_additional_info (id, auth_id, auth_kind,
      id_card_number, certificate_validity, certificate_address, 
      driving_license, driving_license_type, national, 
      obtain_driver_timer, license_expiration_time, 
      driving_license_img_url, idcard_pic_url, hold_idcard_pic_url,
      file_no, file_no_img_url,
      review_time, review_user, app_review_time, 
      review_status, review_items, review_remark, 
      review_item_ids, review_item_name, review_mode, 
      remark, status, create_time, 
      create_oper_id, create_oper_name, update_time, 
      update_oper_id, update_oper_name,personal_address)
    values (#{id,jdbcType=BIGINT}, #{authId,jdbcType=VARCHAR}, #{authKind,jdbcType=VARCHAR}, 
      #{idCardNumber,jdbcType=VARCHAR}, #{certificateValidity,jdbcType=VARCHAR}, #{certificateAddress,jdbcType=VARCHAR}, 
      #{drivingLicense,jdbcType=VARCHAR}, #{drivingLicenseType,jdbcType=VARCHAR}, #{national,jdbcType=VARCHAR}, 
      #{obtainDriverTimer,jdbcType=VARCHAR}, #{licenseExpirationTime,jdbcType=VARCHAR}, 
      #{drivingLicenseImgUrl,jdbcType=VARCHAR}, #{idcardPicUrl,jdbcType=VARCHAR}, #{holdIdcardPicUrl,jdbcType=VARCHAR},
      #{fileNo,jdbcType=VARCHAR}, #{fileNoImgUrl,jdbcType=VARCHAR},
      #{reviewTime,jdbcType=VARCHAR}, #{reviewUser,jdbcType=VARCHAR}, #{appReviewTime,jdbcType=VARCHAR}, 
      #{reviewStatus,jdbcType=INTEGER}, #{reviewItems,jdbcType=VARCHAR}, #{reviewRemark,jdbcType=VARCHAR}, 
      #{reviewItemIds,jdbcType=VARCHAR}, #{reviewItemName,jdbcType=VARCHAR}, #{reviewMode,jdbcType=INTEGER}, 
      #{remark,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{createOperId,jdbcType=BIGINT}, #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updateOperId,jdbcType=BIGINT}, #{updateOperName,jdbcType=VARCHAR}, #{personalAddress,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.core.model.MembershipAdditionalInfo" >
    insert into ${siacSchema}.membership_additional_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="authId != null" >
        auth_id,
      </if>
      <if test="authKind != null" >
        auth_kind,
      </if>
      <if test="idCardNumber != null" >
        id_card_number,
      </if>
      <if test="certificateValidity != null" >
        certificate_validity,
      </if>
      <if test="certificateAddress != null" >
        certificate_address,
      </if>
      <if test="drivingLicense != null" >
        driving_license,
      </if>
      <if test="drivingLicenseType != null" >
        driving_license_type,
      </if>
      <if test="national != null" >
        national,
      </if>
      <if test="obtainDriverTimer != null" >
        obtain_driver_timer,
      </if>
      <if test="licenseExpirationTime != null" >
        license_expiration_time,
      </if>
      <if test="drivingLicenseImgUrl != null" >
        driving_license_img_url,
      </if>
      <if test="idcardPicUrl != null" >
        idcard_pic_url,
      </if>
      <if test="holdIdcardPicUrl != null" >
        hold_idcard_pic_url,
      </if>
      <if test="fileNo != null" >
        file_no,
      </if>
      <if test="fileNoImgUrl != null" >
        file_no_img_url,
      </if>
      <if test="reviewTime != null" >
        review_time,
      </if>
      <if test="reviewUser != null" >
        review_user,
      </if>
      <if test="appReviewTime != null" >
        app_review_time,
      </if>
      <if test="reviewStatus != null" >
        review_status,
      </if>
      <if test="reviewItems != null" >
        review_items,
      </if>
      <if test="reviewRemark != null" >
        review_remark,
      </if>
      <if test="reviewItemIds != null" >
        review_item_ids,
      </if>
      <if test="reviewItemName != null" >
        review_item_name,
      </if>
      <if test="reviewMode != null" >
        review_mode,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateOperId != null" >
        update_oper_id,
      </if>
      <if test="updateOperName != null" >
        update_oper_name,
      </if>
      <if test="personalAddress != null" >
        personal_address,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="authId != null" >
        #{authId,jdbcType=VARCHAR},
      </if>
      <if test="authKind != null" >
        #{authKind,jdbcType=VARCHAR},
      </if>
      <if test="idCardNumber != null" >
        #{idCardNumber,jdbcType=VARCHAR},
      </if>
      <if test="certificateValidity != null" >
        #{certificateValidity,jdbcType=VARCHAR},
      </if>
      <if test="certificateAddress != null" >
        #{certificateAddress,jdbcType=VARCHAR},
      </if>
      <if test="drivingLicense != null" >
        #{drivingLicense,jdbcType=VARCHAR},
      </if>
      <if test="drivingLicenseType != null" >
        #{drivingLicenseType,jdbcType=VARCHAR},
      </if>
      <if test="national != null" >
        #{national,jdbcType=VARCHAR},
      </if>
      <if test="obtainDriverTimer != null" >
        #{obtainDriverTimer,jdbcType=VARCHAR},
      </if>
      <if test="licenseExpirationTime != null" >
        #{licenseExpirationTime,jdbcType=VARCHAR},
      </if>
      <if test="drivingLicenseImgUrl != null" >
        #{drivingLicenseImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="idcardPicUrl != null" >
        #{idcardPicUrl,jdbcType=VARCHAR},
      </if>
      <if test="holdIdcardPicUrl != null" >
        #{holdIdcardPicUrl,jdbcType=VARCHAR},
      </if>
      <if test="fileNo != null" >
        #{fileNo,jdbcType=VARCHAR},
      </if>
      <if test="fileNoImgUrl != null" >
        #{fileNoImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="reviewTime != null" >
        #{reviewTime,jdbcType=VARCHAR},
      </if>
      <if test="reviewUser != null" >
        #{reviewUser,jdbcType=VARCHAR},
      </if>
      <if test="appReviewTime != null" >
        #{appReviewTime,jdbcType=VARCHAR},
      </if>
      <if test="reviewStatus != null" >
        #{reviewStatus,jdbcType=INTEGER},
      </if>
      <if test="reviewItems != null" >
        #{reviewItems,jdbcType=VARCHAR},
      </if>
      <if test="reviewRemark != null" >
        #{reviewRemark,jdbcType=VARCHAR},
      </if>
      <if test="reviewItemIds != null" >
        #{reviewItemIds,jdbcType=VARCHAR},
      </if>
      <if test="reviewItemName != null" >
        #{reviewItemName,jdbcType=VARCHAR},
      </if>
      <if test="reviewMode != null" >
        #{reviewMode,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="personalAddress != null" >
        #{personalAddress,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.core.model.MembershipAdditionalInfo" >
    update ${siacSchema}.membership_additional_info
    <set >
      <if test="authId != null" >
        auth_id = #{authId,jdbcType=VARCHAR},
      </if>
      <if test="authKind != null" >
        auth_kind = #{authKind,jdbcType=VARCHAR},
      </if>
      <if test="idCardNumber != null" >
        id_card_number = #{idCardNumber,jdbcType=VARCHAR},
      </if>
      <if test="certificateValidity != null" >
        certificate_validity = #{certificateValidity,jdbcType=VARCHAR},
      </if>
      <if test="certificateAddress != null" >
        certificate_address = #{certificateAddress,jdbcType=VARCHAR},
      </if>
      <if test="drivingLicense != null" >
        driving_license = #{drivingLicense,jdbcType=VARCHAR},
      </if>
      <if test="drivingLicenseType != null" >
        driving_license_type = #{drivingLicenseType,jdbcType=VARCHAR},
      </if>
      <if test="national != null" >
        national = #{national,jdbcType=VARCHAR},
      </if>
      <if test="obtainDriverTimer != null" >
        obtain_driver_timer = #{obtainDriverTimer,jdbcType=VARCHAR},
      </if>
      <if test="licenseExpirationTime != null" >
        license_expiration_time = #{licenseExpirationTime,jdbcType=VARCHAR},
      </if>
      <if test="drivingLicenseImgUrl != null" >
        driving_license_img_url = #{drivingLicenseImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="idcardPicUrl != null" >
        idcard_pic_url = #{idcardPicUrl,jdbcType=VARCHAR},
      </if>
      <if test="holdIdcardPicUrl != null" >
        hold_idcard_pic_url = #{holdIdcardPicUrl,jdbcType=VARCHAR},
      </if>
      <if test="fileNo != null" >
        file_no = #{fileNo,jdbcType=VARCHAR},
      </if>
      <if test="fileNoImgUrl != null" >
        file_no_img_url = #{fileNoImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="reviewTime != null" >
        review_time = #{reviewTime,jdbcType=VARCHAR},
      </if>
      <if test="reviewUser != null" >
        review_user = #{reviewUser,jdbcType=VARCHAR},
      </if>
      <if test="appReviewTime != null" >
        app_review_time = #{appReviewTime,jdbcType=VARCHAR},
      </if>
      <if test="reviewStatus != null" >
        review_status = #{reviewStatus,jdbcType=INTEGER},
      </if>
      <if test="reviewItems != null" >
        review_items = #{reviewItems,jdbcType=VARCHAR},
      </if>
      <if test="reviewRemark != null" >
        review_remark = #{reviewRemark,jdbcType=VARCHAR},
      </if>
      <if test="reviewItemIds != null" >
        review_item_ids = #{reviewItemIds,jdbcType=VARCHAR},
      </if>
      <if test="reviewItemName != null" >
        review_item_name = #{reviewItemName,jdbcType=VARCHAR},
      </if>
      <if test="reviewMode != null" >
        review_mode = #{reviewMode,jdbcType=INTEGER},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
      <if test="personalAddress != null" >
        personal_address = #{personalAddress,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.core.model.MembershipAdditionalInfo" >
    update ${siacSchema}.membership_additional_info
    set auth_id = #{authId,jdbcType=VARCHAR},
      auth_kind = #{authKind,jdbcType=VARCHAR},
      id_card_number = #{idCardNumber,jdbcType=VARCHAR},
      certificate_validity = #{certificateValidity,jdbcType=VARCHAR},
      certificate_address = #{certificateAddress,jdbcType=VARCHAR},
      driving_license = #{drivingLicense,jdbcType=VARCHAR},
      driving_license_type = #{drivingLicenseType,jdbcType=VARCHAR},
      national = #{national,jdbcType=VARCHAR},
      obtain_driver_timer = #{obtainDriverTimer,jdbcType=VARCHAR},
      license_expiration_time = #{licenseExpirationTime,jdbcType=VARCHAR},
      driving_license_img_url = #{drivingLicenseImgUrl,jdbcType=VARCHAR},
      idcard_pic_url = #{idcardPicUrl,jdbcType=VARCHAR},
      hold_idcard_pic_url = #{holdIdcardPicUrl,jdbcType=VARCHAR},
      file_no = #{fileNo,jdbcType=VARCHAR},
      file_no_img_url = #{fileNoImgUrl,jdbcType=VARCHAR},
      review_time = #{reviewTime,jdbcType=VARCHAR},
      review_user = #{reviewUser,jdbcType=VARCHAR},
      app_review_time = #{appReviewTime,jdbcType=VARCHAR},
      review_status = #{reviewStatus,jdbcType=INTEGER},
      review_items = #{reviewItems,jdbcType=VARCHAR},
      review_remark = #{reviewRemark,jdbcType=VARCHAR},
      review_item_ids = #{reviewItemIds,jdbcType=VARCHAR},
      review_item_name = #{reviewItemName,jdbcType=VARCHAR},
      review_mode = #{reviewMode,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      personal_address = #{personalAddress,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectReviewStatus" resultType="int">
    SELECT review_status
    FROM ${siacSchema}.membership_additional_info
    WHERE auth_id = #{authId} AND status = #{status}
  </select>

  <select id="selectByAuthId" resultMap="BaseResultMap">
    select
      <include refid="Base_Column_List"/>
    FROM ${siacSchema}.membership_additional_info
    WHERE auth_id = #{authId} AND status = #{status}
  </select>

  <select id="selectByAuthIdAndReviewStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    FROM ${siacSchema}.membership_additional_info
    WHERE auth_id = #{authId} AND status = #{status}
    and review_status = #{reviewStatus}
  </select>

  <update id="reviewAdditionalInfoByAuthId">
        UPDATE ${siacSchema}.membership_additional_info
          SET
          review_items = #{reviewItems,jdbcType=VARCHAR},
          review_status = #{reviewStatus,jdbcType=INTEGER},
          update_time = now(),
          update_oper_id = #{updateOperId,jdbcType=VARCHAR},
          update_oper_name = #{updateOperName,jdbcType=VARCHAR},
          review_time = #{reviewTime,jdbcType=VARCHAR},
          review_user = #{reviewUser,jdbcType=VARCHAR},
          `status` = #{status,jdbcType=INTEGER}
          WHERE auth_id = #{authId,jdbcType=VARCHAR}
          AND review_status = '0' AND `status` = '0'
  </update>


</mapper>