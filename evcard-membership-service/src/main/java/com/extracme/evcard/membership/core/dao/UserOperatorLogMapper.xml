<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- UserOperatorLogMapper，，对应表user_operator_log -->
<mapper namespace="com.extracme.evcard.membership.core.dao.UserOperatorLogMapper">
	<!-- 返回结果集Map -->
	<resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.UserOperatorLog">
		<id column="LOG_SEQ" jdbcType="BIGINT" property="logSeq"/>
		<result column="LOG_SEQ" jdbcType="BIGINT" property="logSeq"/>
		<result column="FOREIGN_KEY" jdbcType="VARCHAR" property="foreignKey"/>
		<result column="OPERATOR_CONTENT" jdbcType="LONGVARCHAR" property="operatorContent"/>
		<result column="FOREIGN_KEY2" jdbcType="VARCHAR" property="foreignKey2"/>
		<result column="CREATED_TIME" jdbcType="VARCHAR" property="createdTime"/>
		<result column="CREATED_USER" jdbcType="VARCHAR" property="createdUser"/>
	</resultMap>

	<!--数据列-->
	<sql id="Base_Column_List">
            LOG_SEQ,
            FOREIGN_KEY,
            OPERATOR_CONTENT,
            FOREIGN_KEY2,
            CREATED_TIME,
            CREATED_USER
    </sql>

	<!-- 保存数据 -->
	<insert id="saveSelective" parameterType="com.extracme.evcard.membership.core.model.UserOperatorLog">
		insert into ${siacSchema}.user_operator_log
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="foreignKey">
				FOREIGN_KEY,
			</if>
			<if test="operatorContent">
				OPERATOR_CONTENT,
			</if>
			<if test="foreignKey2">
				FOREIGN_KEY2,
			</if>
			<if test="createdTime">
				CREATED_TIME,
			</if>
			<if test="createdUser">
				CREATED_USER
			</if>
		</trim>
		<trim prefix="values(" suffix=")" suffixOverrides=",">
			<if test="foreignKey">
				#{foreignKey,jdbcType=VARCHAR},
			</if>
			<if test="operatorContent">
				#{operatorContent,jdbcType=LONGVARCHAR},
			</if>
			<if test="foreignKey2">
				#{foreignKey2,jdbcType=VARCHAR},
			</if>
			<if test="createdTime">
				#{createdTime,jdbcType=VARCHAR},
			</if>
			<if test="createdUser">
				#{createdUser,jdbcType=VARCHAR},
			</if>
		</trim>
	</insert>

	<!--由会员操作日志中统计指定操作的执行次数-->
	<select id="countMemChangeOperatorLog" resultType="java.lang.Integer">
		SELECT count(1)
		FROM siac.USER_OPERATOR_LOG force index(Idx_user_operator_log_1)
		WHERE FOREIGN_KEY=#{authId}
		<if test="startTime != ''">
			and CREATED_TIME &gt; #{startTime,jdbcType=VARCHAR}
		</if>
		AND (
		<foreach collection="keywords" item="keyword" index="index" separator="OR">
			<if test="keyword.type == 0">
				OPERATOR_CONTENT = #{keyword.key,jdbcType=VARCHAR}
			</if>
			<if test="keyword.type == 1">
				OPERATOR_CONTENT LIKE CONCAT(#{keyword.key},'%')
			</if>
			<if test="keyword.type == 2">
				OPERATOR_CONTENT LIKE CONCAT('%',#{keyword.key},'%')
			</if>
		</foreach>
		)
	</select>

	<!--查询会员操作日志列表（分页）-->
	<select id="queryUserOperateLog" resultType="com.extracme.evcard.membership.core.dto.UserOperatorLogDTO">
        SELECT
			OPERATOR_CONTENT AS operatorContent,
			CREATED_TIME AS createdTime,
			CREATED_USER AS createdUser,
			FOREIGN_KEY AS foreignKey,
			FOREIGN_KEY2 AS foreignKey2
		FROM
			siac.USER_OPERATOR_LOG
		WHERE
			FOREIGN_KEY = #{foreignKey,jdbcType=VARCHAR}
			<if test="foreignKey2 != null">
				and FOREIGN_KEY2 = #{foreignKey2}
			</if>
		ORDER BY
			CREATED_TIME DESC
		 <if test="page != null">
			 LIMIT #{page.offSet},#{page.limitSet}
		 </if>
    </select>
</mapper>
