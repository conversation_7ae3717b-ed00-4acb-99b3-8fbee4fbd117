package com.extracme.evcard.membership.core.service.license;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.extracme.evcard.membership.common.HttpClientUtils;
import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.dto.DriverLicenseElementsAuthenticateLogDTO;
import com.extracme.evcard.membership.core.dto.DriverLicenseQueryResultDTO;
import com.extracme.evcard.membership.core.dto.DriverLicenseValidResultDto;
import com.extracme.evcard.membership.core.input.SaveDriverElementsAuthenticateLogInput;
import com.extracme.framework.core.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.Map;

/**
 * 聚合驾照识别服务
 * <AUTHOR> @Discription
 * @date 2019/12/16
 */
@Deprecated
@Slf4j
@Component
public class JuHeAuthService implements ILicenseAuthenticateService {

    @Value("${elements_authenticate_ju_he_key}")
    private String DRIVER_LICENSE_ELEMENTS_AUTHENTICATE_JU_HE_KEY;
    @Value("${elements_authenticate_ju_he_url}")
    private String DRIVER_LICENSE_ELEMENTS_AUTHENTICATE_JU_HE_URL;

    @Value("${driver_license_deduction_ju_he_key}")
    private String DRIVER_LICENSE_DEDUCTION_JU_HE_KEY;
    @Value("${driver_license_deduction_ju_he_url}")
    private String DRIVER_LICENSE_DEDUCTION_JU_HE_URL;


    private final static String[] PARAM_ERR_ITEMS = {"驾驶证号格式错误", "姓名格式错误", "档案编号格式错误"};
    @Override
    public SaveDriverElementsAuthenticateLogInput authenticate(String driverCode, String name, String fileNo, Integer readTimeOut) {
        SaveDriverElementsAuthenticateLogInput logInput = new SaveDriverElementsAuthenticateLogInput();
        logInput.setName(name);
        logInput.setDriverCode(driverCode);
        logInput.setFileNo(fileNo);
        logInput.setLogType(0);
        logInput.setSupplier(0);
        logInput.setServiceName(DRIVER_LICENSE_ELEMENTS_AUTHENTICATE_JU_HE_URL);
        LicenseAuthenticateResult checkResult = licenseAuthenticate(name, driverCode, fileNo, readTimeOut);
        //三要素结果(1认证一致 2不一致 3查无记录/无法核查 4异常情况)
        if(checkResult != null) {
            String result;
            String checkItem = "";
            if (checkResult.getErrCode() == 0) {
                if(StringUtils.isNotBlank(checkResult.getMsg()) && !"证件参数错误".equals(checkResult.getMsg())) {
                    //表明三要素验证已经通过, 更新驾照状态描述
                    /** 驾照参数有误，正常/超分/转出/暂扣/撤销/吊销/注销/违法未处理/事故未处理/停止使用/
                     * 协查/扣押/锁定/逾期未换证/延期换证/延期体检/逾期未体检/延期审验/逾期未审验/扣留/其他 **/
                    result = "1";
                    logInput.setLicenseStatusMsg(checkResult.getMsg());
                }
                else if (BussinessConstants.AUTHENTICATE_RESULT_1.equals(checkResult.getNameCheckResult())
                        && BussinessConstants.AUTHENTICATE_RESULT_1.equals(checkResult.getCardNoCheckResult())
                        && BussinessConstants.AUTHENTICATE_RESULT_1.equals(checkResult.getArchviesNoCheckResult())) {
                    result = "1";
                } else if (BussinessConstants.AUTHENTICATE_RESULT_2.equals(checkResult.getNameCheckResult())
                        || BussinessConstants.AUTHENTICATE_RESULT_2.equals(checkResult.getCardNoCheckResult())
                        || BussinessConstants.AUTHENTICATE_RESULT_2.equals(checkResult.getArchviesNoCheckResult())) {
                    result = "2";
                    checkItem = getCheckItemResult(checkResult);
                } else {
                    //三要素均不为全部一致， 且不包含不一致，->无法核查，则认为查无记录，需要重新查验。
                    result = "3";
                }
            } else if(ERROR_REQUEST_PARAMS.equals(String.valueOf(checkResult.getErrCode()))) {
                //此类错误等同于三要素认证不通过
                result = "2";
                checkItem = getCheckItemResult(checkResult.getReason());
            }else {
                result = "4";
            }
            logInput.setResult(result);
            logInput.setResultCode(String.valueOf(checkResult.getErrCode()));
            logInput.setResultMsg(checkResult.getReason());
            logInput.setResponse(checkResult.getResponse());
            logInput.setElementsReviewItems(checkItem);
        }
        return logInput;
    }

    /**
     * 聚合-查询驾驶证扣分情况
     * @param driverCode
     * @param name
     * @param fileNo
     * @return
     */
    @Override
    public SaveDriverElementsAuthenticateLogInput queryDriverDeduction(String driverCode, String name, String fileNo) {
        long st = System.currentTimeMillis();
        SaveDriverElementsAuthenticateLogInput logInput = new SaveDriverElementsAuthenticateLogInput();
        logInput.setName(name);
        logInput.setDriverCode(driverCode);
        logInput.setFileNo(fileNo);
        logInput.setLogType(1);
        logInput.setSupplier(0);
        logInput.setServiceName(DRIVER_LICENSE_DEDUCTION_JU_HE_URL);
        Map<String, Object> params = new HashMap<>();
        params.put("jszh", driverCode);
        params.put("dabh", fileNo);
        params.put("key", DRIVER_LICENSE_DEDUCTION_JU_HE_KEY);
        String jsonResult = StringUtils.EMPTY;
        JSONObject jsonObject = null;
        try {
            jsonResult = HttpClientUtils.httpGetRequest(DRIVER_LICENSE_DEDUCTION_JU_HE_URL, params);
            jsonObject = JSON.parseObject(jsonResult);
            log.warn("聚合查分：name={}, result={}, resultObject={}, cost={}",
                    jsonResult, jsonObject, System.currentTimeMillis() - st);
            int resultCode = jsonObject.getIntValue("error_code");
            String resultMsg = jsonObject.getString("reason");
            String result = jsonObject.getString("result");
            logInput.setResult(result);
            logInput.setResultCode(String.valueOf(resultCode));
            logInput.setResultMsg(resultMsg);
            logInput.setResponse(jsonResult);
        } catch (URISyntaxException e) {
            log.error("聚合查询驾照扣分情况失败", e);
        } catch (Exception e) {
            log.error("聚合查询驾照扣分情况失败, name=" + name, e);
        }
        return logInput;
    }

    @Override
    public DriverLicenseQueryResultDTO queryDriverLicenseInfo(String driverCode, String name) {
        return null;
    }

    private String ERROR_NETWORK_TIMEOUT = "228000";
    private String ERROR_REQUEST_PARAMS = "228001";
    @Override
    public String buildAuthResultDetail(DriverLicenseElementsAuthenticateLogDTO logDTO){
        try {
            JSONObject jsonObject = JSON.parseObject(logDTO.getResponse());
            JSONObject result = null;
            if (jsonObject != null && jsonObject.get("result") != null) {
                result = JSON.parseObject(jsonObject.get("result").toString());
            }
            switch (logDTO.getResult().trim()) {
                case "1":
                    return "认证成功";
                case "2":
                    StringBuffer sb = new StringBuffer();
                    if (result == null) {
                        return "认证不一致";
                    }
                    sb.append("【档案编号核查结果】：").append(result.get("archviesNoCheckResult")).append(";   ");
                    sb.append("【身份证号码核查结果】：").append(result.get("cardNoCheckResult")).append(";  ");
                    sb.append("【姓名核查结果】：").append(result.get("nameCheckResult")).append(";   ");
                    return sb.toString();
                case "3":
                    String msg = "查无记录或无法核查 ";
                    if (jsonObject != null && StringUtils.isNotBlank(jsonObject.getString("reason"))) {
                        msg = StringUtils.join("查无记录或无法核查 ", jsonObject.get("reason"));
                    }
                    return msg;
                case "4":
                    //异常情况/驾照扣分
                    String resultCode = logDTO.getResultCode().trim();
                    if (StringUtils.equals(ERROR_NETWORK_TIMEOUT, resultCode)) {
                        return "网络超时";
                    }
//                if (StringUtils.equals(ERROR_REQUEST_PARAMS, resultCode)) {
//                    return jsonObject.get("reason").toString();
//                }
                    return "系统错误";
            }
        }catch (Exception ex) {
        }
        return StringUtils.EMPTY;
    }

	@Override
	public DriverLicenseValidResultDto checkDriverLicenseValid(String name, String cardNo, String archviesNo) {
        LicenseAuthenticateResult authenticateResult = licenseAuthenticate(name, cardNo, archviesNo, null);
        if(authenticateResult == null) {
            return null;
        }
        DriverLicenseValidResultDto driverLicenseValidResultDto = new DriverLicenseValidResultDto();
        BeanCopyUtils.copyProperties(authenticateResult, driverLicenseValidResultDto);
        return driverLicenseValidResultDto;
	}

    public LicenseAuthenticateResult licenseAuthenticate(String name, String cardNo, String archviesNo, Integer readTimeOut) {
        LicenseAuthenticateResult licenseAuthenticateResult = new LicenseAuthenticateResult();
        Map<String, Object> params = new HashMap<>();
        params.put("name", name);
        params.put("cardNo", cardNo);
        params.put("archviesNo", archviesNo);
        params.put("key", DRIVER_LICENSE_ELEMENTS_AUTHENTICATE_JU_HE_KEY);
        String jsonResult = StringUtils.EMPTY;
        try {
            jsonResult = HttpClientUtils.httpGetRequest(DRIVER_LICENSE_ELEMENTS_AUTHENTICATE_JU_HE_URL, params, readTimeOut);
            JSONObject jsonObject = JSON.parseObject(jsonResult);
            int resultCode = jsonObject.getIntValue("error_code");
            String resultMsg = jsonObject.getString("reason");
            JSONObject resultObg = jsonObject.getJSONObject("result");
            String nameCheckResult = StringUtils.EMPTY;
            String cardNoCheckResult = StringUtils.EMPTY;
            String archviesNoCheckResult = StringUtils.EMPTY;
            String msg = StringUtils.EMPTY;
            if (resultObg != null) {
                nameCheckResult = resultObg.getString("nameCheckResult");
                cardNoCheckResult = resultObg.getString("cardNoCheckResult");
                archviesNoCheckResult = resultObg.getString("archviesNoCheckResult");
                msg = resultObg.getString("msg");
            }
            licenseAuthenticateResult.setNameCheckResult(nameCheckResult);
            licenseAuthenticateResult.setCardNoCheckResult(cardNoCheckResult);
            licenseAuthenticateResult.setArchviesNoCheckResult(archviesNoCheckResult);
            licenseAuthenticateResult.setMsg(msg);
            licenseAuthenticateResult.setErrCode(resultCode);
            licenseAuthenticateResult.setReason(resultMsg);
            licenseAuthenticateResult.setResponse(jsonResult);
            return licenseAuthenticateResult;
        } catch (URISyntaxException e) {
            log.error("聚合驾照三要素认证异常， driverCode=" + cardNo + ", result=" + jsonResult, e);
        } catch (Exception e) {
            log.error("聚合驾照三要素认证异常， driverCode=" + cardNo + ", result=" + jsonResult, e);
        }
        return null;
    }

    private String getCheckItemResult(LicenseAuthenticateResult checkResult){
        StringBuffer checkItem = new StringBuffer();
        boolean resp = BussinessConstants.AUTHENTICATE_RESULT_2.equals(checkResult.getCardNoCheckResult());
        checkItem.append(resp ? "0" : "1");
        resp = BussinessConstants.AUTHENTICATE_RESULT_2.equals(checkResult.getNameCheckResult());
        checkItem.append(resp ? "0" : "1");
        resp = BussinessConstants.AUTHENTICATE_RESULT_2.equals(checkResult.getArchviesNoCheckResult());
        checkItem.append(resp ? "0" : "1");
        return checkItem.toString();
    }

    private String getCheckItemResult(String reason){
        StringBuffer checkItem = new StringBuffer();
        for (String itemMsg : PARAM_ERR_ITEMS) {
            boolean resp = StringUtils.contains(reason, itemMsg);
            checkItem.append(resp ? "0" : "1");
        }
        //三者都不包含，则为其他错误，统一返回三项全部不一致。
        if(StringUtils.equals(checkItem.toString(), "111")) {
            return "000";
        }
        return checkItem.toString();
    }
}
