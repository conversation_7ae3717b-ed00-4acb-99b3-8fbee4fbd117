package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.model.MembershipAdditionalInfo;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

public interface MembershipAdditionalInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MembershipAdditionalInfo record);

    int insertSelective(MembershipAdditionalInfo record);

    MembershipAdditionalInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MembershipAdditionalInfo record);

    int updateByPrimaryKey(MembershipAdditionalInfo record);

    Integer selectReviewStatus(@Param("authId") String authId,@Param("status") Integer status);

    /**
     * 查询用户补充信息
     * @param authId
     * @param status
     * @return
     */
    MembershipAdditionalInfo selectByAuthId(@Param("authId") String authId, @Param("status") Integer status);

    MembershipAdditionalInfo selectByAuthIdAndReviewStatus(@Param("authId") String authId,
                                                           @Param("status") Integer status,
                                                           @Param("reviewStatus") Integer reviewStatus);

    int reviewAdditionalInfoByAuthId(MembershipAdditionalInfo additionalInfo);
}