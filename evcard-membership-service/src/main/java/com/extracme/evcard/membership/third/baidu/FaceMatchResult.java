package com.extracme.evcard.membership.third.baidu;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class FaceMatchResult {


    @JSONField(name = "score") private Double score;
    @JSONField(name = "face_list") private List<FaceListDTO> faceList;

    @NoArgsConstructor
    @Data
    public static class FaceListDTO {
        @JSONField(name = "face_token") private String faceToken;
    }
}
