package com.extracme.evcard.membership.common;

import com.extracme.evcard.redis.JedisUtil;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Random;
import java.util.UUID;


@Component
public class SeqGenerator {
    private static final char[] r = new char[]{'J', 'G', 'H', 'S', 'Z', '7', 'V', 'W', 'T', 'K', 'Q', 'X', 'R', 'C', '8', 'F', 'I', '0', 'B', 'D', '6', 'M', 'N', '9', 'U', '4', 'P', '5', '2', 'Y', '3', 'E'};
    private static final char b = 'A';
    private static final int binLen = r.length;
    private static final int s = 7;
    public static final String UUID_SPITE = "-";

    //奖励领取流水号前缀
    private static final String PREFIX_DIANWEI = "DW";
    private static final String REDIS_KEY_CODE_NUM_PREFIX = "seq_gen_";

    public String genDianWeiReqSeq() {
        return generalSeq(StringUtils.EMPTY, PREFIX_DIANWEI).toUpperCase();
    }

    public String generalSeq(String groupId, String prefix) {
        if(StringUtils.isBlank(groupId)) {
            groupId = StringUtils.EMPTY;
        }
        StringBuffer seq = new StringBuffer();
        String sysDate = ComUtil.getSystemDate(ComUtil.DATE_TYPE3);
        seq.append(prefix).append(sysDate).append(groupId);
        String numTickKey = REDIS_KEY_CODE_NUM_PREFIX + seq.toString();
        long newNum  = JedisUtil.incr(numTickKey);
        if (newNum == 1L) {
            JedisUtil.expire(numTickKey, 10 * 60);
        }
        seq.append(encode(newNum));
        return seq.toString();
    }

    public static String UUID() {
        UUID uuid = UUID.randomUUID();
        //return compressedUUID(uuid);
        return toHexString(uuid);
    }

    /**
     * 压缩uuid的长度
     * @param uuid
     * @return
     */
    private static String compressedUUID(UUID uuid) {
        byte[] byUuid = new byte[16];
        long least = uuid.getLeastSignificantBits();
        long most = uuid.getMostSignificantBits();
        long2bytes(most, byUuid, 0);
        long2bytes(least, byUuid, 8);
        String compressUUID = Base64.encodeBase64URLSafeString(byUuid);
        return compressUUID;
    }

    private static void long2bytes(long value, byte[] bytes, int offset) {
        for (int i = 7; i > -1; i--) {
            bytes[offset++] = (byte) ((value >> 8 * i) & 0xFF);
        }
    }


    private static final long hexMod = 15L;
    private static final char[] digits = new char[] { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f' };

    public static String toHexString(UUID uuid) {
        char[] array = new char[32];
        long leastBits = uuid.getLeastSignificantBits();
        for (int i = 31; i >= 16; i--) {
            array[i] = digits[(int) (leastBits & hexMod)];
            leastBits >>>= 4;
        }
        long mostBits = uuid.getMostSignificantBits();
        for (int i = 15; i >= 0; i--) {
            array[i] = digits[(int) (mostBits & hexMod)];
            mostBits >>>= 4;
        }
        return new String(array);
    }

    public static String randomUUID() {
        return toHexString(UUID.randomUUID());
    }


    private static String encode(long id) {
        char[] buf = new char[32];

        int charPos;
        for(charPos = 32; id / (long)binLen > 0L; id /= (long)binLen) {
            int ind = (int)(id % (long)binLen);
            --charPos;
            buf[charPos] = r[ind];
        }

        --charPos;
        buf[charPos] = r[(int)(id % (long)binLen)];
        String str = new String(buf, charPos, 32 - charPos);
        if (str.length() < 7) {
            StringBuilder sb = new StringBuilder();
            sb.append(str);
            sb.append('A');
            Random rnd = new Random();

            for(int i = 1; i < 7 - str.length(); ++i) {
                sb.append(r[rnd.nextInt(binLen)]);
            }

            str = sb.toString();
        }
        return str;
    }
}
