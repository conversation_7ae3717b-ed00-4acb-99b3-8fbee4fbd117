package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.model.City;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CityMapper {


    /**
     * 根据网点查询城市信息
     * @param shopSeq
     * @return
     */
    City queryCityInfoByShopSeq(@Param("shopSeq") int shopSeq);

    List<City> selectCity(@Param("cityName")String cityName);

    City getCityByName(@Param("cityName")String cityName);

    String queryOrgNameByCity(String cityName);

    /**
     * 根据城市查询会员所属公司
     *
     * @param city
     * @return
     */
    List<String> queryCityOrg(@Param("city") String city);

    City getCityByCityId(@Param("cityId")String cityId);
}
