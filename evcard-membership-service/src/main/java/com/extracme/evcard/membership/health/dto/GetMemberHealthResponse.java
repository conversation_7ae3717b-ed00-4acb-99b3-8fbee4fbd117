package com.extracme.evcard.membership.health.dto;

import com.extracme.evcard.rpc.dto.BaseResponse;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 */
@Data
public class GetMemberHealthResponse extends BaseResponse {

    /**
     * 健康状态
     */
    private HealthTypeEnum healthType;

    public GetMemberHealthResponse(int code, String message, HealthTypeEnum healthType){
        super(code, message);
        this.healthType = healthType;
    }
}
