package com.extracme.evcard.membership.core.service.license;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.extracme.evcard.membership.common.HttpClientUtils;
import com.extracme.evcard.membership.core.dto.DriverLicenseElementsAuthenticateLogDTO;
import com.extracme.evcard.membership.core.dto.DriverLicenseQueryResultDTO;
import com.extracme.evcard.membership.core.dto.DriverLicenseValidResultDto;
import com.extracme.evcard.membership.core.input.SaveDriverElementsAuthenticateLogInput;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;

/**
 * 小视驾照识别服务
 * <AUTHOR> @Discription
 * @date 2019/12/16
 */
@Slf4j
@Component
public class MiniVisionAuthService implements ILicenseAuthenticateService {

    @Value("${elements_authenticate_mini_url}")
    private String ELEMENTS_AUTHENTICATE_API_URL;

    @Value("${elements_authenticate_mini_service_name}")
    private String SERVICE_ELEMENTS_AUTHENTICATE;
    @Value("${elements_authenticate_mini_login_name}")
    private String ELEMENTS_AUTHENTICATE_MINI_LOGIN_NAME;
    @Value("${elements_authenticate_mini_pwd}")
    private String ELEMENTS_AUTHENTICATE_MINI_PWD;

    @Value("${driver_license_query_mini_service_name}")
    private String SERVICE_DRIVER_LICENSE_QUERY;


    public static final int MIN_VISION_SUPPLIER = 1;
    @Override
    public SaveDriverElementsAuthenticateLogInput authenticate(String driverCode, String name, String fileNo, Integer readTimeOut) {
        String serviceName = SERVICE_ELEMENTS_AUTHENTICATE;
        SaveDriverElementsAuthenticateLogInput logInput = new SaveDriverElementsAuthenticateLogInput();
        logInput.setName(name);
        logInput.setDriverCode(driverCode);
        logInput.setFileNo(fileNo);
        logInput.setLogType(0);
        logInput.setSupplier(MIN_VISION_SUPPLIER);
        logInput.setServiceName(ELEMENTS_AUTHENTICATE_API_URL + serviceName);

        Map<String, Object> params = new HashMap<>();
        params.put("name", name);
        params.put("idCard", driverCode);
        params.put("fileNumber", fileNo);
        try {
            String jsonResult = doPost(serviceName, params);
            JSONObject jsonObject = JSON.parseObject(jsonResult);
            String resultCode = jsonObject.getString("RESULT");
            String resultMsg = jsonObject.getString("MESSAGE");
            String result;
            if ("1".equals(resultCode)) {
                result = "1";
            } else if ("2".equals(resultCode)) {
                result = "2";
            } else if ("3".equals(resultCode)) {
                result = "3";
            } else {
                result = "4";
            }
            logInput.setResult(result);
            logInput.setResultCode(String.valueOf(resultCode));
            logInput.setResultMsg(resultMsg);
            logInput.setResponse(jsonResult);
            //小视无具体不一致项，默认全部不一致
            logInput.setElementsReviewItems("000");
        } catch (Exception e) {
            log.error("小视驾照三要素认证失败", e);
        }
        return logInput;
    }

    @Override
    public SaveDriverElementsAuthenticateLogInput queryDriverDeduction(String driverCode, String name, String fileNo) {
        return null;
    }

    public static final String CODE_QUERY_LIC_OK = "1001";

    @Override
    public DriverLicenseQueryResultDTO queryDriverLicenseInfo(String driverCode, String name) {
        String serviceName = SERVICE_DRIVER_LICENSE_QUERY;

        DriverLicenseQueryResultDTO licenseQueryResultDTO = new DriverLicenseQueryResultDTO();
        licenseQueryResultDTO.setName(name);
        licenseQueryResultDTO.setDriverCode(driverCode);
        //驾照详情获取
        licenseQueryResultDTO.setLogType(2);
        licenseQueryResultDTO.setSupplier(MIN_VISION_SUPPLIER);
        licenseQueryResultDTO.setServiceName(ELEMENTS_AUTHENTICATE_API_URL + serviceName);

        //params
        Map<String, Object> params = new HashMap<>();
        params.put("name", name);
        params.put("idCard", driverCode);
        try {
            String jsonResult = doPost(serviceName, params);
            JSONObject jsonObject = JSON.parseObject(jsonResult);
            /**
             * 一级状态码：
             * 1. 查询成功   1001 查询成功有数据
             * 2. 查询成功   2001 查询成功无数据
             *             2002 限制查询驾驶证
             * 3. 无记录    3001 查无记录
             *             3002 参数错误
             * -1 异常情况   -1XXX 服务平台已知异常
             *             9999 服务异常
             */
            StringBuffer sb = new StringBuffer();
            String resultCode = jsonObject.getString("RESULT");
            sb.append(jsonObject.getString("MESSAGE"));
            String detail = jsonObject.getString("detail");
            MinVisionResponse detailResp = JSON.parseObject(detail, MinVisionResponse.class);
            if(detailResp != null) {
                if("1".equals(resultCode) && CODE_QUERY_LIC_OK.equals(detailResp.getResultCode())) {
                    MinVisionDriverLicenseInfo licenseInfo = detailResp.getResultInfo();
                    if(licenseInfo != null) {
                        licenseQueryResultDTO.setLicenseInfo(licenseInfo.buildDriverLicenseDetail());
                    }
                }
                sb.append(":【").append(detailResp.getResultCode()).append("】").append(detailResp.getResultMsg());
            }
            licenseQueryResultDTO.setResult(resultCode);
            licenseQueryResultDTO.setResultCode(resultCode);
            licenseQueryResultDTO.setResultMsg(sb.toString());
            licenseQueryResultDTO.setResponse(jsonResult);
        } catch (Exception e) {
            log.error("小视驾照信息获取失败，driverCode=" + driverCode + ", name=" + name, e);
        }
        return licenseQueryResultDTO;
    }

    private String doPost(String serviceName, Map<String, Object> params)
            throws UnsupportedEncodingException {
        //params
        Map<String, Object> map = new HashMap<>();
        map.put("loginName", ELEMENTS_AUTHENTICATE_MINI_LOGIN_NAME);
        map.put("pwd", ELEMENTS_AUTHENTICATE_MINI_PWD);
        map.put("serviceName", serviceName);
        map.put("param", params);
        return HttpClientUtils.httpPostRequest(ELEMENTS_AUTHENTICATE_API_URL + serviceName, JSON.toJSONString(map));
    }

    @Override
    public String buildAuthResultDetail(DriverLicenseElementsAuthenticateLogDTO logDTO){
        switch (logDTO.getResultCode().trim()){
            case "1":
                return "认证一致";
            case "2":
                return "认证不一致";
            case "3":
                return "查无记录";
            case "-1":
                return "服务异常";
        }
        return StringUtils.EMPTY;
    }

	@Override
	public DriverLicenseValidResultDto checkDriverLicenseValid(String name, String cardNo, String archviesNo) {
		return null;
	}
}
