package com.extracme.evcard.membership.core.model;

/**
 * 模型类，，对应表card_info_histroy.
 */
public class CardInfoHistroy{
    private String cardNo;
    private String authId;
    private Double membershipType;
    private Double cardStatus;
    private String remark;
    private String createdTime;
    private String createdUser;
    private String updatedTime;
    private String updatedUser;
    public String getCardNo(){ 
        return cardNo;
    } 
    public void setCardNo(String cardNo){ 
        this.cardNo = cardNo;
    }
    public String getAuthId(){ 
        return authId;
    } 
    public void setAuthId(String authId){ 
        this.authId = authId;
    }
    public Double getMembershipType(){ 
        return membershipType;
    } 
    public void setMembershipType(Double membershipType){ 
        this.membershipType = membershipType;
    }
    public Double getCardStatus(){ 
        return cardStatus;
    } 
    public void setCardStatus(Double cardStatus){ 
        this.cardStatus = cardStatus;
    }
    public String getRemark(){ 
        return remark;
    } 
    public void setRemark(String remark){ 
        this.remark = remark;
    }
    public String getCreatedTime(){ 
        return createdTime;
    } 
    public void setCreatedTime(String createdTime){ 
        this.createdTime = createdTime;
    }
    public String getCreatedUser(){ 
        return createdUser;
    } 
    public void setCreatedUser(String createdUser){ 
        this.createdUser = createdUser;
    }
    public String getUpdatedTime(){ 
        return updatedTime;
    } 
    public void setUpdatedTime(String updatedTime){ 
        this.updatedTime = updatedTime;
    }
    public String getUpdatedUser(){ 
        return updatedUser;
    } 
    public void setUpdatedUser(String updatedUser){ 
        this.updatedUser = updatedUser;
    }

}