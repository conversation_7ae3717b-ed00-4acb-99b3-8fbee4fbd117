package com.extracme.evcard.membership.contract.service.html;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.contract.service.AddRentCarContractRequest;
import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.dto.CompanySeal;
import com.extracme.evcard.membership.core.dto.CompanySeals;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.enums.CompanySealEnum;
import com.extracme.evcard.membership.core.enums.IdTypeEnum;
import com.extracme.evcard.rpc.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class RentCarContractForm implements IRentCarContractService {

    @Override
    public String description() {
        return "普通租车合同";
    }

    @Override
    public String renderContractHtml(RenderHtmlContext context) throws BusinessException {
        String initHtml = context.getInitHtml();
        String signImgHtml = context.getSignImgHtml();
        MembershipBasicInfo membershipBasicInfo = context.getMembershipBasicInfo();
        AddRentCarContractRequest addRequest = context.getAddRequest();
        String operOrgName = addRequest.getOperOrgName();
        String operOrgCode = addRequest.getOperOrgCode();
        String name = membershipBasicInfo.getName();

        String idCardNumber = membershipBasicInfo.getPassportNo();
        // 外籍用驾照号
        if (!IdTypeEnum.isMainlandId(membershipBasicInfo.getIdType())) {
            idCardNumber = membershipBasicInfo.getDriverCode();
        }

        // 获取甲的公司用章
        String jiaSeal = getSealPicByOrgCode(operOrgCode);
        if (StringUtils.isBlank(jiaSeal)) {
            log.error("获取甲公司用章失败，入参request=[{}]", JSON.toJSONString(context));
            throw new BusinessException("获取公司用章失败");
        }
        String jiaSealHtml = "<img src=\"" + jiaSeal + "\" style=\"margin-bottom: -110px;width: 136px;height: 136px;margin-left: 32px;\">";

        String date = ComUtil.getSystemDate(ComUtil.DATE_TYPE22);
        String resultHtml = initHtml.replace(BussinessConstants.HTML_YINAME, name)
                .replace(BussinessConstants.HTML_JIANAME, operOrgName) // 获取甲的公司名称
                .replace(BussinessConstants.HTML_DATE, date)
                .replace(BussinessConstants.HTML_SIGN, signImgHtml)
                .replace(BussinessConstants.HTML_IDCARD, idCardNumber)
                .replace(BussinessConstants.HTML_JIA_SEAL, jiaSealHtml);
        return resultHtml;
    }


    // 可配置的印章图片json
    /**
     * {
     * "companySeals": [{
     * "orgCode": "00",
     * "orgName": "环球车享汽车租赁有限公司",
     * "sealPicUrl": "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/00.png"
     * },
     * {
     * "orgCode": "009W",
     * "orgName": "上海环行汽车租赁有限公司乌鲁木齐分公司",
     * "sealPicUrl": "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/009W.png"
     * }]
     * }
     */
    @ApolloJsonValue("${config.contract.seal.json:{}}")
    public CompanySeals companySeals;

    public String getSealPicByOrgCode(String operOrgCode) {
        String sealPicUrl = CompanySealEnum.getSealPicUrlByOrgCode(operOrgCode);
        if (StringUtils.isNotBlank(sealPicUrl)) {
            return sealPicUrl;
        }

        if (companySeals != null && CollectionUtils.isNotEmpty(companySeals.getCompanySeals())) {
            for (CompanySeal companySeal : companySeals.getCompanySeals()) {
                if (StringUtils.equals(operOrgCode, companySeal.getOrgCode())) {
                    sealPicUrl = companySeal.getSealPicUrl();
                    return sealPicUrl;
                }
            }
        }
        return null;
    }
}
