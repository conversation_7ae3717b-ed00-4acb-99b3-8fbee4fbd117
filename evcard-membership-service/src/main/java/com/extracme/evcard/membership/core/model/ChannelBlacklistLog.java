package com.extracme.evcard.membership.core.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *  渠道黑名单日志表
 */
@Data
public class ChannelBlacklistLog implements Serializable {

    /**
     * 主键
     *
     */
    private Long id;

    /**
     * 渠道黑名单id
     */
    private Long channelBlacklistId;

    /**
     * 日志内容
     */
    private String content;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateBy;
}
