package com.extracme.evcard.membership.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.extracme.evcard.elasticjob.ElasticSimpleJob;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.model.MembershipInfo;
import com.extracme.evcard.saic.service.ISaicMemberService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


/**
 * 会员注销定时任务
 * 每天0点
 */
@Slf4j
@Component
@ElasticSimpleJob(jobName = "evcard-membership-saicCancel",
		cron = "0 0 0 * * ?", description = "saic账号注销任务", overwrite = true)
public class MemberRegisterRecoverJob implements SimpleJob{

	@Autowired
	private MembershipInfoMapper membershipInfoMapper;

	@Resource
	private ISaicMemberService saicMemberService;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void execute(ShardingContext arg0) {
		String params = arg0.getJobParameter();
		if(StringUtils.isBlank(params)) {
			return;
		}
		log.warn("处理未注销会员：开始， authIds={}", params);
		Set<String> set = new HashSet<>();
		String[] pkIds = StringUtils.split(params, ",");
		for (String authId : pkIds) {
			if(StringUtils.isNotBlank(authId)) {
				set.add(authId);
			}
		}
		if(CollectionUtils.isEmpty(set)) {
			return;
		}
		List<MembershipInfo> members = membershipInfoMapper.selectByAuthIds(set);
		if(CollectionUtils.isNotEmpty(members)) {
			for (MembershipInfo member : members) {
				if(!member.getAccountStatus().equals(2)) {
					log.warn("处理未注销会员：非注销完成不做处理，authId={}", member.getAuthId(), member.getMobilePhone());
					return;
				}
				log.warn("处理未注销会员：authId={}", member.getAuthId(), member.getMobilePhone());
				try {
					saicMemberService.cancelAccount(member.getAuthId(), null, member.getMobilePhone(), member.getMobilePhone());
				}catch (Exception e) {
					log.warn("处理未注销会员：authId=" + member.getAuthId() + ", mobile=" + member.getMobilePhone(), e);
				}
			}
		}
		log.warn("处理未注销会员：完成， authIds={}", params);
	}

}
