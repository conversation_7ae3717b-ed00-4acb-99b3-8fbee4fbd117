package com.extracme.evcard.membership.core.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/4/30
 */
@Data
public class UpdateReviewStatusDto {
    /**
     * 变更来源： 1自动审核通过  2人工审核通过 3人工审核不通过 4自动审核不通过-登录校准 5自动审核不通过-后台查无校准
     */
    private Integer operateType;

    /**
     * 会员id, 必须
     */
    private String authId;

    /**
     * 操作日志
     */
    private String operatorContent;

    /**
     * 目标值：审核状态(-1:资料不全 0:待审核 1：审核通过 2: 审核不通过 3：用户无效 4:重新审核）
     */
    private Integer reviewStatus;

    /**
     * 目标值：认证状态 0待认证 1认证通过 2认证不通过 3查证中(待重新认证)
     */
    private Integer authenticateStatus;

    /**
     * 目标值：驾照状态 0待认证 1可用 2不可用（驾照有效期/准驾车型/驾照状态不符合用车条件）
     */
    private Integer licenseStatus;

    /**
     * 目标值：驾照状态描述: "超分","暂扣","撤销","吊销","注销","停止使用"等
     */
    private String licenseStatusMsg;

    /**
     * 目标值：认证状态（0 未认证 1 未刷脸 2 已认证）
     **/
    private Integer authenticationStatus;

    /**
     * 目标值：用户当前驾照录入方式（1 ocr提取  2 手动输入）
     **/
    private Integer driverLicenseInputType;

    /**
     * 目标值：审核不通过项
     * 审核不通过时，必须
     * 勾选的审核项： 原9位 现12位(20190910起)
     * 从左右至右: 姓名、驾驶证号、驾驶证照片、准驾车型、初次领证日期、
     * 驾照到期日期、邮寄地址（废弃）、身份证件照片、手持身份证件照片、
     * 驾驶证档案编号、身份证件编号、人脸照片
     * 初始值为全0. 00000000000
     **/
    private String reviewItems;

    /**
     * 目标值：审核不通过原因
     * 审核不通过时，必须
     * 审核不通过原因详细描述，分号分隔(发短信使用)
     **/
    private String reviewRemark;

    /**
     * 目标值：审核不通过项
     * 审核不通过时，必须
     * 审核不通过概要编号，分号分隔(1.姓名不正确 2.驾驶证号不正确 3.驾驶证照片不正确 4. 准驾车型不正确
     * 5.初次领证日期不正确 6.驾照到期日期不正确 7.邮寄地址不正确 8.身份证件照片不正确 9.手持身份证件照片不正确
     * A.驾驶证档案编号不正确 B.身份证件编号不正确 C.人脸照片不正确 0.用户意愿))
     **/
    private String reviewItemIds;

    /**
     * 审核不通过时，必须
     * 审核不通过原因概要，分号分隔
     **/
    private String reviewItemName;

    private String appReviewTime;;
}
