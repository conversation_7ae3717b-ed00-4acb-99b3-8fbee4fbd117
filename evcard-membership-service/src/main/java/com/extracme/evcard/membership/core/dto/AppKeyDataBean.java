package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;

public class AppKeyDataBean implements Serializable {
    private static final long serialVersionUID = 3948447691227781876L;

    // evcard分配给其他平台的APP KEY
    private String appKey;

    //EVCARD分配给其他平台的APP_SECRET
    private String appSceret;

    //访问其他平台的APP key
    private String requestAppKey;

    //VCARD分配给其他平台的秘钥
    private String requestAppSceret;

    //平台名称
    private String platName;

    //URL
    private String postUrl;

    private String className;

    //0:不自动注册 1：自动注册
    private int autoRegist;
    //登录限制,0:不限制 1：限制
    private int loginRestrict;
    //	享受优惠，0：享受 1：不享受
    private int enjoyBenefit;
    //是否自动支付，0：手动支付 1：自动支付
    private int autoPay;

    //0:不回传订单 1：自动回传订单
    private int uploadOrder;

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getAppSceret() {
        return appSceret;
    }

    public void setAppSceret(String appSceret) {
        this.appSceret = appSceret;
    }

    public String getRequestAppKey() {
        return requestAppKey;
    }

    public void setRequestAppKey(String requestAppKey) {
        this.requestAppKey = requestAppKey;
    }

    public String getRequestAppSceret() {
        return requestAppSceret;
    }

    public void setRequestAppSceret(String requestAppSceret) {
        this.requestAppSceret = requestAppSceret;
    }

    public String getPlatName() {
        return platName;
    }

    public void setPlatName(String platName) {
        this.platName = platName;
    }

    public String getPostUrl() {
        return postUrl;
    }

    public void setPostUrl(String postUrl) {
        this.postUrl = postUrl;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public int getAutoRegist() {
        return autoRegist;
    }

    public void setAutoRegist(int autoRegist) {
        this.autoRegist = autoRegist;
    }

    public int getLoginRestrict() {
        return loginRestrict;
    }

    public void setLoginRestrict(int loginRestrict) {
        this.loginRestrict = loginRestrict;
    }

    public int getEnjoyBenefit() {
        return enjoyBenefit;
    }

    public void setEnjoyBenefit(int enjoyBenefit) {
        this.enjoyBenefit = enjoyBenefit;
    }

    public int getAutoPay() {
        return autoPay;
    }

    public void setAutoPay(int autoPay) {
        this.autoPay = autoPay;
    }

    public int getUploadOrder() {
        return uploadOrder;
    }

    public void setUploadOrder(int uploadOrder) {
        this.uploadOrder = uploadOrder;
    }
}
