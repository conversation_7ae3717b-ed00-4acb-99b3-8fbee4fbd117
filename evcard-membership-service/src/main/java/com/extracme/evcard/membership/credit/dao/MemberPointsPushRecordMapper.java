package com.extracme.evcard.membership.credit.dao;

import com.extracme.evcard.membership.credit.model.MemberPointsPushRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface MemberPointsPushRecordMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MemberPointsPushRecord record);

    int insertSelective(MemberPointsPushRecord record);

    MemberPointsPushRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MemberPointsPushRecord record);

    int updateByPrimaryKey(MemberPointsPushRecord record);

    MemberPointsPushRecord selectOneByRefKey(@Param("authId") String authId,
                                          @Param("eventType") Integer eventType,  @Param("eventRefSeq") String eventRefSeq);

    List<MemberPointsPushRecord> queryByIds(@Param("list") Set<Long> recordIds);

    List<MemberPointsPushRecord> queryByIdRefKeys(@Param("list") Set<String> refKeys);

    int batchSaveTitleReward(@Param("list") List<MemberPointsPushRecord> list);
}