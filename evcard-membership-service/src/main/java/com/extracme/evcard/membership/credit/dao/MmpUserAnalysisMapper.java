package com.extracme.evcard.membership.credit.dao;

import com.extracme.evcard.membership.credit.model.MmpUserAnalysis;
import org.apache.ibatis.annotations.Param;

public interface MmpUserAnalysisMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_user_analysis
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_user_analysis
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    int insert(MmpUserAnalysis record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_user_analysis
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    int insertSelective(MmpUserAnalysis record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_user_analysis
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    MmpUserAnalysis selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_user_analysis
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    int updateByPrimaryKeySelective(MmpUserAnalysis record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_user_analysis
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    int updateByPrimaryKey(MmpUserAnalysis record);

    Integer selectIsExistUserAnalysis(@Param("paramsDto") MmpUserAnalysis analysis);

    int updateExistUserAnalysis(@Param("paramsDto") MmpUserAnalysis analysis);
}