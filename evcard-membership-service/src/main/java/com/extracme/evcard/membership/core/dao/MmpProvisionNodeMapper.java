package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.model.MmpProvisionNode;

public interface MmpProvisionNodeMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MmpProvisionNode record);

    int insertSelective(MmpProvisionNode record);

    MmpProvisionNode selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MmpProvisionNode record);

    int updateByPrimaryKey(MmpProvisionNode record);
}