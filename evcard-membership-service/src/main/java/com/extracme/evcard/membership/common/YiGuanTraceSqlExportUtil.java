package com.extracme.evcard.membership.common;

import com.extracme.evcard.membership.config.CommConfigUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @Date 2020/6/23 15:08
 */
public class YiGuanTraceSqlExportUtil {

    private static Logger logger = LoggerFactory.getLogger(YiGuanTraceSqlExportUtil.class);

    public static void sqlExport(String dirUrl){
        try {
            String url;
            String APP_KEY;
            String token;
            String env = CommConfigUtil.getENV();
            if("prod".equals(env)) {
                APP_KEY = "20456cc3aeaefd7f";
                token = "943d6d062926b315fef7da99ff356710";
                url = "http://hl.evcard.vip:4005/uba/api/sql/export";
            } else {
                APP_KEY = "9c03f8b2095d71a2";
                token = "45a82b06c7dfeb9cad261dace5ab5762";
                url = "http://hl.evcard.vip:4005/uba/api/sql/export";
            }
            String UTF = "UTF-8";
            CloseableHttpClient client = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(url);
            //Header 参数
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("appKey", APP_KEY);
            httpPost.setHeader("token", token);
            // post body params
            Map<String, Object> params = new HashMap<>();

            params.put("format", "json");
            params.put("sql", "select xwho from profile_vd where length(xwho) < 30 and " +
                    "(user_id is null or register_time_modified is null or register_city is null or register_platform is null or register_source is null)");
            ObjectMapper objectMapper = new ObjectMapper();
            StringEntity stringEntity = new StringEntity(objectMapper.writeValueAsString(params), UTF);
            stringEntity.setContentEncoding(UTF);
            httpPost.setEntity(stringEntity);
            //set timeout
            RequestConfig reqConfig = RequestConfig.custom()
                    .setSocketTimeout(10 * 60 * 1000).setConnectTimeout(60 * 1000).build();
            httpPost.setConfig(reqConfig);
            File dir = new File(dirUrl);
            if (!dir.exists()) {
                dir.mkdir();
            }
            File file = new File(dirUrl, "output.csv");
            /*if (!file.exists()) {
                file.createNewFile();
            }*/
            try (CloseableHttpResponse response1 = client.execute(httpPost);
                 FileOutputStream fos = new FileOutputStream(file)) {
                final HttpEntity entity = response1.getEntity();
                if (entity != null) {
                    try (InputStream inputStream = entity.getContent()) {
                        try (BufferedReader br = new BufferedReader(new InputStreamReader(inputStream))) {
                            String line;
                            while ((line = br.readLine()) != null) {
                                fos.write((line + "\n").getBytes(UTF));
                            }
                        }
                    }
                    EntityUtils.consume(entity);
                }
            } catch (Exception e) {
                logger.error("易观埋点，获取易观缺失用户信息失败", e);
            }
        } catch (Exception e) {
            logger.error("易观埋点，获取易观缺失用户信息失败", e);
        }
    }

}
