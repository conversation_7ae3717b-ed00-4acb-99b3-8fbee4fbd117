package com.extracme.evcard.membership.contract.service;

import com.extracme.evcard.rpc.dto.BaseResponse;
import lombok.Data;

/**
 * <AUTHOR>
 * @Discription
 * @date 2021/3/7
 */
@Data
public class ContractSignResult {

    private String viewUrl;

    private String downloadUrl;

    //0不符合签署条件 1签署成功 2已签署本次不再签署
    private Integer signed;

    //个人签署账户id
    private String customerId;

    //模板id
    private String templateId;

    private String transactionId;

    private String contractId;

    private String versionId;

    private String archiveUrl;

    private byte[] stream;

}
