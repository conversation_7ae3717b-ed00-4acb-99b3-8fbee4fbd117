package com.extracme.evcard.membership.job;

import com.baosight.iplat4j.util.util.DateUtil;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.extracme.evcard.elasticjob.ElasticSimpleJob;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.dto.*;
import com.extracme.evcard.membership.core.dto.input.QueryDepositInfoInputDTO;
import com.extracme.evcard.membership.core.service.IMemberCardService;
import com.extracme.evcard.membership.core.service.IMemberDepositService;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.membership.core.service.MemberShipServiceImpl;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.rpc.base.dto.OrgInfo;
import com.extracme.evcard.rpc.base.service.IOrgService;
import com.extracme.evcard.rpc.messagepush.service.ISensorsdataService;
import com.extracme.evcard.rpc.util.DateType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 易观 会员公共信息 埋点
 *  每日更新增量数据
 *
 * <AUTHOR>
 *
 */
@Slf4j
@Component
@ElasticSimpleJob(jobName = "evcard-membership-yiguanMemberTrackJob",
		cron = "0 0 1 * * ?", description = "会员信息易观埋点", overwrite = true)
public class YiguanMemberTrackJob implements SimpleJob{

	@Autowired
	private MembershipInfoMapper membershipInfoMapper;

	@Autowired
	private MemberShipServiceImpl memberShipService;

	@Override
	public void execute(ShardingContext arg0) {

		//查询更新时间在昨天的会员数据
		Date currentDate = new Date();
		String endTimeStr = DateFormatUtils.format(currentDate, DateType.DATE_TYPE8);
		Date startDate = DateUtil.addDay(currentDate, -1);
		String startTimeStr = DateFormatUtils.format(startDate, DateType.DATE_TYPE8);

		List<MembershipBasicInfo> membershipInfoList = membershipInfoMapper.selectMemberInfoByUpdateTime(startTimeStr, endTimeStr);
		if(CollectionUtils.isNotEmpty(membershipInfoList)){
			for(MembershipBasicInfo p : membershipInfoList){
				memberShipService.traceMember(p);
			}

		}
	}

}
