package com.extracme.evcard.membership.vipcredits.anyolife;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.common.RestTemplateUtil;
import com.extracme.evcard.membership.vipcredits.anyolife.entity.*;
import com.extracme.evcard.rpc.util.DateType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 积分商城api访问工具类
 * 供应商：安悦e商城anyolife
 * TODO 细节待供应商确定。
 * <AUTHOR>
 * @Description
 */
@Slf4j
@Component
public class AnyolifeRestClient {

//    @Resource
//    private RestTemplate restTemplate;

    @Value("${anyolife.appId}")
    private String appId;
    @Value("${anyolife.appKey}")
    private String appKey;

    @Value("${anyolife.vipcredits.api}")
    private String apiBaseUrl;

    @Value("${anyolife.vipcredits.gainPoints}")
    private String apiUrlGainPoints;
    /**
     * 用户积分发放(积分事件推送)
     * @param input
     * @return
     */
    public GainCreditsResponse gainCredits(GainCreditsRequest input) {
        String path = apiUrlGainPoints;
        String inputStr = JSON.toJSONString(input);
        String result = StringUtils.EMPTY;
        log.debug("anyolife.vipcredits: 推送积分事件开始, input={}", inputStr);
        try {
            result = postMethodComplex(path, null, inputStr);
            if(StringUtils.isNotBlank(result)) {
                GainCreditsResponse resp = JSON.parseObject(result, GainCreditsResponse.class);
                if (resp != null) {
                    log.debug("anyolife.vipcredits: 推送积分事件完成, input={}, resp={}", inputStr, JSON.toJSONString(resp));
                    return resp;
                }
            }
            log.error("anyolife.vipcredits: 推送积分事件失败，input={}, result={}", inputStr, result);
        } catch (Exception e) {
            log.error("anyolife.vipcredits: 获取积分事件推送接口结果异常，input=" + inputStr + ", result=" + result, e);
        }
        return null;
    }


    @Value("${anyolife.vipcredits.memberPoints}")
    private String apiUrlMemberPoints;
    /**
     * 获取个人积分账户信息
     * @param input
     * @return
     */
    public GetUserCreditsResponse getUserCreditsAccount(BaseCreditsRequest input) {
        String path = apiUrlMemberPoints;
        String inputJsonStr = JSON.toJSONString(input);
        String result = StringUtils.EMPTY;
        log.debug("anyolife.vipcredits: 获取积分账户信息开始... input={}", inputJsonStr);
        try {
            result = postMethod(path, null, inputJsonStr);
            if(StringUtils.isNotBlank(result)) {
                GetUserCreditsResponse resp = JSON.parseObject(result, GetUserCreditsResponse.class);
                if (resp != null) {
                    return resp;
                }
            }
            log.error("anyolife.vipcredits: 获取积分账户信息失败，input={}, result={}", inputJsonStr, result);
        } catch (Exception e) {
            log.error("anyolife.vipcredits: 获取积分账户信息结果异常，input=" + inputJsonStr + ", result=" + result, e);
        }
        return null;
    }


    @Value("${anyolife.vipcredits.pointsHistory}")
    private String apiUrlPointsHistory;
    public QueryCreditsHistoryResponse queryUserCreditsHistory(QueryCreditsHistoryRequest input) {
        String path = apiUrlPointsHistory;
        String inputJsonStr = JSON.toJSONString(input);
        String result = StringUtils.EMPTY;
        log.debug("anyolife.vipcredits: 查询积分账户变更履历开始... input={}", inputJsonStr);
        try {
            result = postMethod(path, null, inputJsonStr);
            if(StringUtils.isNotBlank(result)) {
                QueryCreditsHistoryResponse resp = JSON.parseObject(result, QueryCreditsHistoryResponse.class);
                if (resp != null) {
                    return resp;
                }
            }
            log.error("anyolife.vipcredits: 查询积分账户变更履历失败，input={}, result={}", inputJsonStr, result);
        } catch (Exception e) {
            log.error("anyolife.vipcredits: 查询积分账户变更履历结果异常，input=" + inputJsonStr + ", result=" + result, e);
        }
        return null;
    }

    @Value("${anyolife.vipcredits.queryGainCredits}")
    private String apiUrlQueryGainCredits;

    /**
     * 获取积分操作信息
     * @param input
     * @return
     */
    public QueryCreditsBeforeResponse queryGainCredits(QueryCreditsBeforeRequest input) {
        String path = apiUrlQueryGainCredits;
        String inputJsonStr = JSON.toJSONString(input);
        String result = StringUtils.EMPTY;
        log.debug("anyolife.vipcredits: 获取积分数值信息开始... input={}", inputJsonStr);
        try {
            result = postMethod(path, null, inputJsonStr);
            if(StringUtils.isNotBlank(result)) {
                QueryCreditsBeforeResponse resp = JSON.parseObject(result, QueryCreditsBeforeResponse.class);
                if (resp != null) {
                    return resp;
                }
            }
            log.error("anyolife.vipcredits: 获取积分数值信息失败，input={}, result={}", inputJsonStr, result);
        } catch (Exception e) {
            log.error("anyolife.vipcredits: 获取积分数值信息结果异常，input=" + inputJsonStr + ", result=" + result, e);
        }
        return null;
    }


    @Value("${anyolife.vipcredits.queryCarbonCredits}")
    private String apiUrlQueryCarbonReduceTitleCredits;
    public TitlePointsQueryResponse queryCarbonReduceTitlePoints(UserTitlePointsQueryRequest input) {
        String path = apiUrlQueryCarbonReduceTitleCredits;
        String inputJsonStr = JSON.toJSONString(input);
        String result = StringUtils.EMPTY;
        log.debug("anyolife.vipcredits: 获取节碳称号积分数值信息开始... input={}", inputJsonStr);
        try {
            result = postMethod(path, null, inputJsonStr);
            if(StringUtils.isNotBlank(result)) {
                TitlePointsQueryResponse resp = JSON.parseObject(result, TitlePointsQueryResponse.class);
                if (resp != null) {
                    return resp;
                }
            }
            log.error("anyolife.vipcredits: 获取节碳称号积分数值信息失败，input={}, result={}", inputJsonStr, result);
        } catch (Exception e) {
            log.error("anyolife.vipcredits: 获取节碳称号积分数值信息结果异常，input=" + inputJsonStr + ", result=" + result, e);
        }
        return null;
    }

    @Value("${anyolife.vipcredits.gainCarbonCredits}")
    private String apiUrlGainCarbonReduceTitleCredits;
    public TitlePointsOfferResponse offerCarbonReduceTitlePoints(UserTitlePointsOfferRequest input) {
        String path = apiUrlGainCarbonReduceTitleCredits;
        String inputStr = JSON.toJSONString(input);
        String result = StringUtils.EMPTY;
        log.debug("anyolife.vipcredits: 推送节碳称号事件开始, input={}", inputStr);
        try {
            result = postMethod(path, null, inputStr);
            if(StringUtils.isNotBlank(result)) {
                TitlePointsOfferResponse resp = JSON.parseObject(result, TitlePointsOfferResponse.class);
                if (resp != null) {
                    log.debug("anyolife.vipcredits: 推送节碳称号事件完成, input={}, resp={}", inputStr, JSON.toJSONString(resp));
                    return resp;
                }
            }
            log.error("anyolife.vipcredits: 推送节碳称号事件失败，input={}, result={}", inputStr, result);
        } catch (Exception e) {
            log.error("anyolife.vipcredits: 获取推送节碳称号事件接口结果异常，input=" + inputStr + ", result=" + result, e);
        }
        return null;
    }

    public static final RestTemplate restTemplateBasic = RestTemplateUtil.getInstance(500, 1000);
    public static final RestTemplate restTemplateComplex = RestTemplateUtil.getInstance(500, 5000);

    protected String postMethodComplex(String path, Map<String, String> headers, String bodyJsonStr, Object... uriVariables) {
        long tm = System.currentTimeMillis();
        String result = postMethod(restTemplateComplex, path, headers, bodyJsonStr, uriVariables);
        log.error("gainCreditsCost: cost={}ms.", System.currentTimeMillis() - tm);
        return result;
    }

    protected String postMethod(String path, Map<String, String> headers, String bodyJsonStr, Object... uriVariables) {
        return postMethod(restTemplateBasic, path, headers, bodyJsonStr, uriVariables);
    }

    protected String postMethod(RestTemplate template, String path, Map<String, String> headers, String bodyJsonStr, Object... uriVariables) {
        String url = apiBaseUrl + path;
        String timestamp = DateFormatUtils.format(System.currentTimeMillis(), DateType.DATE_TYPE4);
        String sign = generateSign(bodyJsonStr, timestamp, appKey);
        //签名
        HttpHeaders header = new HttpHeaders();
        MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
        header.setContentType(type);
        header.set("X-Timestamp", timestamp);
        header.set("X-Sign", sign);
        header.set("X-SignAlgorithm", "1");
        header.set("X-AppId", appId);
        if(!CollectionUtils.isEmpty(headers)) {
            for(String key : header.keySet()) {
                header.set(key, headers.get(key));
            }
        }
        try {
            //发起请求
            HttpEntity<String> httpEntity = new HttpEntity<>(bodyJsonStr, header);
            ResponseEntity response = template.exchange(url, HttpMethod.POST, httpEntity, String.class, uriVariables);
            log.info("anyolife.vipCredit->url={}, bodyVariables={}, result={}", url, bodyJsonStr, JSON.toJSONString(response));
            if (response.getStatusCodeValue() == 200) {
                //返回正常，取得相应的数据
                String resultJson = response.getBody().toString();
                return resultJson;
            }else {
                log.error("anyolife.vipCredit->请求失败，url={}, bodyVariables={}, result={}", url,
                        bodyJsonStr, JSON.toJSONString(response));
            }
        } catch (Exception e) {
            log.error("anyolife.vipCredit->请求失败，url=" + url + ", bodyVariables=" + bodyJsonStr, e);
        }
        return null;
    }


    public static String generateSign(String content, String timestamp, String appSecret) {
        return SHA1TranferUtils.encode(content + timestamp + appSecret);
    }
}
