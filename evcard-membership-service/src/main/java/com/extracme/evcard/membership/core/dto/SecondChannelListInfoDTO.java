package com.extracme.evcard.membership.core.dto;

import lombok.Data;

/**
 * 二级渠道 dto
 */
@Data
public class SecondChannelListInfoDTO {

    private Long id;

    /**
     *二级渠道appkey
     */
    private String secondAppKey;

    /**
     * 二级渠道APP_SECRET
     */
    private String secondAppSecret;

    /**
     * 二级渠道名称
     */
    private String secondAppKeyName;

    /**
     * 第三方id
     */
    private Integer thirdid;

    /**
     * 一级渠道appkey
     */
    private String firstAppKey;

    /**
     * 平台ID
     */
    private Long platformId;

    /**
     * '渠道用途'
     */
    private String channelPurpose;
    /**
     * 渠道用途，描述以逗号分隔
     */
    private String channelPurposeDesc = "";

    /**
     * 税务主体公司 组织id
     */
    private String taxMainCompany;

    /**
     * 税务主体公司 名称
     */
    private String taxMainCompanyName;

    /**
     * 申请机构 运营公司
     */
    private String orgId;

    /**
     * 备注
     */
    private String remark;

    /**
     * '0:有效 1：无效'
     */
    private Integer status;


    /** 组织机构名称 */
    private String orgName;
    /** 创建时间 */
    private String createTime;
    /** 创建人 */
    private String createUser;
    /** 创建人id */
    private String createUserId;
    /** 平台名称 */
    private String platName;

    /** 一级渠道名称 */
    private String firstAppKeyName;
}