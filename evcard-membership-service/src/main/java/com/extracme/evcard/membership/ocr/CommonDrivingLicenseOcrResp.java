package com.extracme.evcard.membership.ocr;

import com.extracme.evcard.membership.core.enums.CommonRiskTypeEnums;
import lombok.Data;

import java.io.Serializable;

/**
 * 统一的身份证识别出参
 *
 * <AUTHOR>
 * @date 2022/8/19
 */
@Data
public class CommonDrivingLicenseOcrResp implements Serializable {

    /**
     * 证件号
     */
    private String licenseNo;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别：男、女
     */
    private String gender;

    /**
     * 国籍
     */
    private String nationality;

    /**
     * 地址
     */
    private String address;

    /**
     * 出生日期，格式：yyyyMMdd
     */
    private String birthday;

    /**
     * 初次领证日期，格式：yyyyMMdd
     */
    private String issueDate;

    /**
     * 准驾车型
     */
    private String driveType;

    /**
     * 有效期开始日期，格式yyyyMMdd
     */
    private String startDate;

    /**
     * 有效期结束日期，格式yyyyMMdd，或 “长期”
     */
    private String endDate;

    /**
     * 发证机关
     */
    private String issuedBy;

    /**
     * 档案编号（副页）
     */
    private String fileNo;

    /**
     * 记录（副页）
     */
    private String record;

    /**
     * 驾驶证证风险类型:
     * normal-正常驾驶证；
     * copy-复印件；
     * screen-翻拍；
     */
    private CommonRiskTypeEnums riskType;

    /**
     * 供应商
     *
     */
    private String channel;
}
