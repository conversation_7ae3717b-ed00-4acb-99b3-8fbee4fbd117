package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.model.MmpAgencyDiscountLog;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

public interface MmpAgencyDiscountLogMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_agency_discount_log
     *
     * @mbggenerated Wed Apr 01 16:59:50 CST 2020
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_agency_discount_log
     *
     * @mbggenerated Wed Apr 01 16:59:50 CST 2020
     */
    int insert(MmpAgencyDiscountLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_agency_discount_log
     *
     * @mbggenerated Wed Apr 01 16:59:50 CST 2020
     */
    int insertSelective(MmpAgencyDiscountLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_agency_discount_log
     *
     * @mbggenerated Wed Apr 01 16:59:50 CST 2020
     */
    MmpAgencyDiscountLog selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_agency_discount_log
     *
     * @mbggenerated Wed Apr 01 16:59:50 CST 2020
     */
    int updateByPrimaryKeySelective(MmpAgencyDiscountLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_agency_discount_log
     *
     * @mbggenerated Wed Apr 01 16:59:50 CST 2020
     */
    int updateByPrimaryKey(MmpAgencyDiscountLog record);

    /**
     * 根据时间查询当时指定机构的折扣相关配置
     * @param agencyId
     * @param time
     * @return
     */
    MmpAgencyDiscountLog selectLatestByTime(@Param("agencyId") String agencyId, @Param("time") Date time);
}