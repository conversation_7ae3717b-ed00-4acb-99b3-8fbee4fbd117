package com.extracme.evcard.membership.common;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.dubbo.common.extension.Activate;
import com.alibaba.dubbo.rpc.Filter;
import com.alibaba.dubbo.rpc.Invocation;
import com.alibaba.dubbo.rpc.Invoker;
import com.alibaba.dubbo.rpc.Result;
import com.alibaba.dubbo.rpc.RpcContext;
import com.alibaba.dubbo.rpc.RpcException;
import com.extracme.evcard.rpc.entity.HidLog;

@Activate(group = {"consumer", "provider"}, order = -9999)
public class GlobalTraceFilter implements Filter {

	Logger logger = LoggerFactory.getLogger(this.getClass());
	
	@Override
	public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
		if(RpcContext.getContext().isConsumerSide()) {
			//RpcContext.getContext().setAttachments(HidLog.PARAM_LOCAL.get());
			RpcContext.getContext().setAttachment(HidLog.REQ_ID, HidLog.getTraceParam(HidLog.REQ_ID));
			RpcContext.getContext().setAttachment(HidLog.CHANNEL, HidLog.getTraceParam(HidLog.CHANNEL));
			RpcContext.getContext().setAttachment(HidLog.APP_VERSION, HidLog.getTraceParam(HidLog.APP_VERSION));
			//logger.debug("拦截器consumerSide#" + HidLog.PARAM_LOCAL == null || HidLog.PARAM_LOCAL.get() == null? null: HidLog.PARAM_LOCAL.get().get(HidLog.CHANNEL));
		}
		if(RpcContext.getContext().isProviderSide()) {
			//logger.debug("拦截器ProviderSide#" + HidLog.PARAM_LOCAL == null || HidLog.PARAM_LOCAL.get() == null? null: HidLog.PARAM_LOCAL.get().get(HidLog.CHANNEL));
			String reqId = RpcContext.getContext().getAttachment(HidLog.REQ_ID);
			if(StringUtils.isNotBlank(reqId)) {
				HidLog.putTraceParam(HidLog.REQ_ID, reqId);
				HidLog.putTraceParam(HidLog.CHANNEL, RpcContext.getContext().getAttachment(HidLog.CHANNEL));
				HidLog.putTraceParam(HidLog.APP_VERSION, RpcContext.getContext().getAttachment(HidLog.APP_VERSION));
			} else {
				if(HidLog.PARAM_LOCAL.get() != null) {
					HidLog.PARAM_LOCAL.remove();
				}
			}
		}

		return invoker.invoke(invocation);
	}

}
