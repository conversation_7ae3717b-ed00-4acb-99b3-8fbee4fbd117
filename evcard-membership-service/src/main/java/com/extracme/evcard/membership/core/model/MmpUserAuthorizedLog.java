package com.extracme.evcard.membership.core.model;

import java.util.Date;

public class MmpUserAuthorizedLog {
    private Long id;

    private String authId;

    private Integer authorizedType;

    private Integer authorizedResult;

    private String authorizedReason;

    private Date authorizedDateTime;

    private Integer authorizedFraction;

    private Integer status;

    private String miscDesc;

    private Date createTime;

    private Long createOperId;

    private String createOperName;

    private Date updateTime;

    private Long updateOperId;

    private String updateOperName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public Integer getAuthorizedType() {
        return authorizedType;
    }

    public void setAuthorizedType(Integer authorizedType) {
        this.authorizedType = authorizedType;
    }

    public Integer getAuthorizedResult() {
        return authorizedResult;
    }

    public void setAuthorizedResult(Integer authorizedResult) {
        this.authorizedResult = authorizedResult;
    }

    public String getAuthorizedReason() {
        return authorizedReason;
    }

    public void setAuthorizedReason(String authorizedReason) {
        this.authorizedReason = authorizedReason;
    }

    public Date getAuthorizedDateTime() {
        return authorizedDateTime;
    }

    public void setAuthorizedDateTime(Date authorizedDateTime) {
        this.authorizedDateTime = authorizedDateTime;
    }

    public Integer getAuthorizedFraction() {
        return authorizedFraction;
    }

    public void setAuthorizedFraction(Integer authorizedFraction) {
        this.authorizedFraction = authorizedFraction;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMiscDesc() {
        return miscDesc;
    }

    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateOperId() {
        return createOperId;
    }

    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    public String getCreateOperName() {
        return createOperName;
    }

    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdateOperId() {
        return updateOperId;
    }

    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    public String getUpdateOperName() {
        return updateOperName;
    }

    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }
}