package com.extracme.evcard.membership.core.dto;

import com.extracme.evcard.membership.common.ComUtil;

import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/5/13
 */
public class LicenseReAuthListQueryDto {

    private Date minLatestAuthenticateTime;

    private Integer maxRetryTimes;

    private Date createStartTime;

    private Date createEndTime;

    private List<RetryParams> retryConditions;


    private static class RetryParams implements Serializable {

        private int retryTimes;

        private Date startTime;

        private Date endTime;

        public RetryParams() {
        }

        public RetryParams(Calendar calendar, int times, int retryMinutesSpan) {
            this.retryTimes = times;
            calendar.add(Calendar.MINUTE, 0 - retryMinutesSpan);
            this.endTime = calendar.getTime();
            calendar.add(Calendar.MINUTE, -5);
            this.startTime = calendar.getTime();
        }

        public int getRetryTimes() {
            return retryTimes;
        }

        public void setRetryTimes(int retryTimes) {
            this.retryTimes = retryTimes;
        }

        public Date getStartTime() {
            return startTime;
        }

        public void setStartTime(Date startTime) {
            this.startTime = startTime;
        }

        public Date getEndTime() {
            return endTime;
        }

        public void setEndTime(Date endTime) {
            this.endTime = endTime;
        }
    }

    public Date getMinLatestAuthenticateTime() {
        return minLatestAuthenticateTime;
    }

    public void setMinLatestAuthenticateTime(Date minLatestAuthenticateTime) {
        this.minLatestAuthenticateTime = minLatestAuthenticateTime;
    }

    public Integer getMaxRetryTimes() {
        return maxRetryTimes;
    }

    public void setMaxRetryTimes(Integer maxRetryTimes) {
        this.maxRetryTimes = maxRetryTimes;
    }

    public Date getCreateStartTime() {
        return createStartTime;
    }

    public void setCreateStartTime(Date createStartTime) {
        this.createStartTime = createStartTime;
    }

    public Date getCreateEndTime() {
        return createEndTime;
    }

    public void setCreateEndTime(Date createEndTime) {
        this.createEndTime = createEndTime;
    }

    public List<RetryParams> getRetryConditions() {
        return retryConditions;
    }

    public void setRetryConditions(List<RetryParams> retryConditions) {
        this.retryConditions = retryConditions;
    }

    public void buildRetryConditions(int[] retryMinutesSpans){
        List<RetryParams> retryConditions = new ArrayList<>();
        Calendar calendar = Calendar.getInstance(ComUtil.timeZoneChina);
        for (int times = 0; times < retryMinutesSpans.length; times ++) {
            int retryMinutesSpan = retryMinutesSpans[times];
            RetryParams retryParams = new RetryParams(calendar, times, retryMinutesSpan);
            retryConditions.add(retryParams);
        }
        this.retryConditions = retryConditions;
    }
}


