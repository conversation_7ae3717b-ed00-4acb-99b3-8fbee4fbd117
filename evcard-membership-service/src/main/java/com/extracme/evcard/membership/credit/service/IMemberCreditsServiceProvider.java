package com.extracme.evcard.membership.credit.service;

import com.extracme.evcard.membership.core.dto.UserTitlePointsOfferSingleDto;
import com.extracme.evcard.membership.core.dto.UserTitlePointsQueryDto;
import com.extracme.evcard.membership.core.dto.UserTitleUpgradePointsDto;
import com.extracme.evcard.membership.core.exception.MemberException;
import com.extracme.evcard.membership.credit.dto.*;
import com.extracme.evcard.membership.vipcredits.anyolife.entity.TitlePointsOfferResponse;
import com.extracme.evcard.membership.vipcredits.anyolife.entity.TitlePointsQueryResponse;
import com.extracme.evcard.membership.vipcredits.anyolife.entity.UserTitlePointsOfferRequest;
import com.extracme.evcard.membership.vipcredits.anyolife.entity.UserTitlePointsQueryRequest;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.dto.PageBeanDto;

import java.util.List;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/8/12
 */
public interface IMemberCreditsServiceProvider {
    /**
     * 会员积分发放
     * @param offerDto
     * @return
     */
    MemberPointsOfferResp offerPoints(MemberPointsOfferDto offerDto) throws MemberException;

    /**
     * 会员积分变更履历
     * @param queryDto
     * @return
     */
    PageBeanDto<MemberPointsHistoryDto> queryUserHistoryPage(MemberPointsHistoryQueryDto queryDto);


    /**
     * 会员个人积分账户详情
     * @param queryDto
     * @return
     */
    MemberPointsAccountDto queryUserAccount(MemberPointsAccountQueryDto queryDto);


    /**
     * 查询用户完成指定操作将获得的积分数
     * @param queryDto
     * @return
     */
    MemberPointsGainQueryResp queryGainCredits(MemberPointsGainQueryDto queryDto);


    /**
     * 根据升级记录查询节碳称号奖励积分
     * @param queryDto
     * @return
     */
    List<UserTitleUpgradePointsDto> queryCarbonReduceTitlePoints(UserTitlePointsQueryDto queryDto);

    /**
     * 发放节碳称号奖励积分，单次发放
     * @param offerDto
     * @return
     */
    MemberPointsOfferResp offerCarbonReduceTitlePoints(UserTitlePointsOfferSingleDto offerDto);
}
