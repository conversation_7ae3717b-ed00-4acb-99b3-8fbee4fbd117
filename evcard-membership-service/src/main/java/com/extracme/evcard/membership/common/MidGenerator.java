package com.extracme.evcard.membership.common;

import com.extracme.evcard.membership.core.exception.RegisterException;
import com.extracme.evcard.redis.JedisUtil;
import com.extracme.evcard.rpc.enums.StatusCode;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;


@Component
public class MidGenerator {

    private static final String PREFIX_MEM_INNER = "ME";
    private static final String PREFIX_MEM = "M";

    private static final String MID_REDIS_KEY = "MID";
    private static final int MID_EXPIRE_TIME = 60 * 60 + 20;

    /**
     * 生成会员ID
     * @return
     * @remark 暂不区分内部外部会员
     */
    public String getMid() {
        return generateId(StringUtils.EMPTY, MID_REDIS_KEY);
    }

    private static String generateId(String prefix, String redisPrefix) {
        StringBuffer seq = new StringBuffer();
        String sysDate = ComUtil.getSystemDate(ComUtil.DATE_TYPE9);
        seq.append(prefix).append(sysDate);
        String redisKey = redisPrefix + ":" + seq.toString();
        Long serialNumber = JedisUtil.incr(redisKey);
        if (serialNumber == 1L) {
            JedisUtil.expire(redisKey, MID_EXPIRE_TIME);
        }
        if (serialNumber > 99999L) {
            throw new RegisterException(StatusCode.SYSTEM_ERROR);
        }
        seq.append(String.format("%05d", serialNumber));
        return seq.toString();
    }
}
