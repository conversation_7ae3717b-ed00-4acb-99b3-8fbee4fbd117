<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.core.dao.UserCouponListMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.UserCouponList" >
    <id column="USER_COUPON_SEQ" property="userCouponSeq" jdbcType="BIGINT" />
    <result column="AUTH_ID" property="authId" jdbcType="VARCHAR" />
    <result column="COUPON_SEQ" property="couponSeq" jdbcType="DECIMAL" />
    <result column="START_DATE" property="startDate" jdbcType="VARCHAR" />
    <result column="EXPIRES_DATE" property="expiresDate" jdbcType="VARCHAR" />
    <result column="STATUS" property="status" jdbcType="DECIMAL" />
    <result column="CREATED_TIME" property="createdTime" jdbcType="VARCHAR" />
    <result column="CREATED_USER" property="createdUser" jdbcType="VARCHAR" />
    <result column="UPDATED_TIME" property="updatedTime" jdbcType="VARCHAR" />
    <result column="UPDATED_USER" property="updatedUser" jdbcType="VARCHAR" />
    <result column="COUPON_ORIGIN" property="couponOrigin" jdbcType="VARCHAR" />
    <result column="coupon_code" property="couponCode" jdbcType="VARCHAR" />
    <result column="CRM_USER_COUPON_SEQ" property="crmUserCouponSeq" jdbcType="INTEGER" />
    <result column="exchangeTime" property="exchangetime" jdbcType="TIMESTAMP" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="OFFER_TYPE" property="offerType" jdbcType="INTEGER" />
    <result column="ACTION_ID" property="actionId" jdbcType="VARCHAR" />
    <result column="ORG_SEQ" property="orgSeq" jdbcType="BIGINT" />
    <result column="ORDER_ORG_SEQ" property="orderOrgSeq" jdbcType="BIGINT" />
    <result column="ORDER_SEQ" property="orderSeq" jdbcType="VARCHAR" />
    <result column="DISCOUNT" property="discount" jdbcType="DOUBLE" />
    <result column="ORIGIN_REF_SEQ" property="originRefSeq" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    USER_COUPON_SEQ, AUTH_ID, COUPON_SEQ, START_DATE, EXPIRES_DATE, STATUS, CREATED_TIME, 
    CREATED_USER, UPDATED_TIME, UPDATED_USER, COUPON_ORIGIN, coupon_code, CRM_USER_COUPON_SEQ, 
    exchangeTime, remark, OFFER_TYPE, ACTION_ID, ORG_SEQ, ORDER_ORG_SEQ, ORDER_SEQ, DISCOUNT, 
    ORIGIN_REF_SEQ
  </sql>

  <select id="selectCount" resultType="int">
    SELECT COUNT(*)
    FROM siac.USER_COUPON_LIST
    WHERE AUTH_ID = #{authId} AND COUPON_ORIGIN = #{origin}
  </select>

  <update id="batchInvalidatedCouponByUserCouponSeq">
    UPDATE ${siacSchema}.user_coupon_list
    SET  `STATUS` = 2 , UPDATED_USER = #{optUser}, UPDATED_TIME = #{updatedTime}
    WHERE  AUTH_ID = #{authId}
    AND  STATUS = 0
    AND ACTION_ID =  #{actionId}
    AND OFFER_TYPE = #{offerType}
    AND CREATED_TIME > #{createdTime}
  </update>

</mapper>