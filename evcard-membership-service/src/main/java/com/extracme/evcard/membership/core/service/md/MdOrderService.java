package com.extracme.evcard.membership.core.service.md;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.config.MdRestApiConfig;
import com.extracme.evcard.membership.core.dto.md.*;
import com.extracme.evcard.rpc.util.BeanCopyUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

@Component
public class MdOrderService {
    private static final Logger log = LoggerFactory.getLogger(MdOrderService.class);
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private MdRestApiConfig mdRestApiConfig;

    public MdRentalContract getMdContract(String contractId) {
        GetMdContractResponse response = searchMdContract(contractId);
        if (response != null && response.getCode() != 0) {
            return null;
        }
        MdRentalContract contract = new MdRentalContract();
        BeanCopyUtils.copyProperties(response, contract);
        return contract;
    }

    public GetMdContractResponse searchMdContract(String contractId) {
        String path = mdRestApiConfig.getBaseUrl() + mdRestApiConfig.getSearchContractById();
        GetMdContractRequest req = new GetMdContractRequest();
        req.setContractId(contractId);
        GetMdContractResponse response = restTemplate.postForObject(path, req, GetMdContractResponse.class);
        return response;
    }

    public QueryOrderPayInfoRes queryOrderPayInfo(String contractId) {
        String path = mdRestApiConfig.getBaseUrl() + mdRestApiConfig.getQueryOrderPayInfo();
        HttpHeaders requestHeaders = new HttpHeaders();
        requestHeaders.add("Content-Type", "application/json");
        HttpEntity<String> requestEntity = new HttpEntity<String>(contractId, requestHeaders);
        QueryOrderPayInfoRes response = restTemplate.postForObject(path, requestEntity, QueryOrderPayInfoRes.class);
        return response;
    }

    public GetOrderCountByStatusResponse getOrderCountByStatus(GetOrderCountByStatusRequest request) {
        String path = mdRestApiConfig.getBaseUrl() + mdRestApiConfig.getGetOrderCountByStatusUrl();
        GetOrderCountByStatusResponse response = restTemplate.postForObject(path, request, GetOrderCountByStatusResponse.class);
        return response;
    }

    public SaveBlackListLogResponse saveBlackListLog(SaveBlackListLogRequest request) {
        String path = mdRestApiConfig.getBaseUrl() + mdRestApiConfig.getSaveBlackListLogUrl();
        log.info("mdOrderService.saveBlackListLog request:{}", JSON.toJSONString(request));
        SaveBlackListLogResponse response = restTemplate.postForObject(path, request, SaveBlackListLogResponse.class);
        log.info("mdOrderService.saveBlackListLog response:{}", JSON.toJSONString(response));
        return response;
    }

}
