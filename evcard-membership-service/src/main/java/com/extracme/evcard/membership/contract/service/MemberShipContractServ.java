package com.extracme.evcard.membership.contract.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.model.OSSObject;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.common.PdfUtil;
import com.extracme.evcard.membership.common.ThreadPoolUtils;
import com.extracme.evcard.membership.common.UploadImgUtil;
import com.extracme.evcard.membership.config.CommConfig;
import com.extracme.evcard.membership.config.OssConfigUtil;
import com.extracme.evcard.membership.contract.esign.EsignService;
import com.extracme.evcard.membership.contract.esign.MemberContractEsignService;
import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.dao.UserContractMapper;
import com.extracme.evcard.membership.core.dto.*;
import com.extracme.evcard.membership.core.enums.CompanySealEnum;
import com.extracme.evcard.membership.core.enums.IdTypeEnum;
import com.extracme.evcard.membership.core.exception.MemberException;
import com.extracme.evcard.membership.core.model.UserContract;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs;
import com.extracme.evcard.membership.login.util.FreemarkerUtil;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.util.BeanCopyUtils;
import com.fadada.sdk.client.FddClientBase;
import com.timevale.esign.sdk.tech.bean.result.FileDigestSignResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: Elin
 * \* Date: 2018/1/11
 * \* Time: 16:42
 * \* To change this template use File | Settings | File Templates.
 * \* Description:合同
 * \
 */
@Service
@Slf4j
public class MemberShipContractServ implements IMemberShipContractServ {

    @Autowired
    private CommConfig commConfig;

    @Resource
    private MembershipInfoMapper membershipInfoMapper;

    @Autowired
    private UserContractMapper userContractMapper;

    @Autowired
    private Map<String, IMemberContractSignProvider> signProviderMap = new ConcurrentHashMap<>(5);

    @Resource(name = "fddContractService")
    private IMemberContractSignProvider memberContractSignProvider;
    @Resource(name = "esignContractService")
    private MemberContractEsignService memberContractEsignService;

    @Value("${contract.supplier}")
    private String currentSupplier;

    @Value("${esign.posX}")
    private Integer posX;
    @Value("${esign.posY}")
    private Integer posY;

    public IMemberContractSignProvider getProvider(String supplier) {
        if (StringUtils.isBlank(supplier)) {
            supplier = currentSupplier;
        }
        ContractSupplier contractSupplier = ContractSupplier.getInstance(NumberUtils.toInt(supplier));
        IMemberContractSignProvider provider = signProviderMap.get(contractSupplier.getKey() + "ContractService");
        if (provider == null) {
            log.error("ContractSignProvider [" + contractSupplier.getKey() + "] undefined.");
        }
        return provider;
    }

    @Override
    public String invokeSyncPersonAuto(String authName, String email, String idCard, String iDentType, String mobile) {
        String customerId = "";
        FddClientBase clientbase = new FddClientBase(commConfig.getFadadaAppId(), commConfig.getFadadaAppSecret(),
                commConfig.getFadadaVersion(), commConfig.getFadadaApiUrl());
        log.debug("authName：" + authName + "，email：" + email + "，idCard：" + idCard + "，iDentType：" + iDentType + "，mobile：" + mobile);
        String response = clientbase.invokeSyncPersonAuto(authName, email, idCard, iDentType, mobile);
        log.debug("调用法大大接口invokeSyncPersonAuto返回：" + response);
        Map<String, Object> resultMap = (Map<String, Object>) JSONObject.parse(response);
        if (null != resultMap && resultMap.size() > 0) {
            String code = (String) resultMap.get("code");
            String msg = (String) resultMap.get("msg");
            if (!"1000".equals(code)) {
                log.error("----调用法大大invokeSyncPersonAuto接口失败----" + msg + "，idCard：" + idCard);
            }
            //得到 customer_id
            customerId = (String) resultMap.get("customer_id");
            log.debug("idCard" + idCard + "，法大大返回的customer_id为：" + customerId);
        }
        return customerId;
    }

    @Deprecated
    @Override
    public BaseResponse invokeUploadTemplate(String templateId, String docUrl) {
        FddClientBase clientbase = new FddClientBase(commConfig.getFadadaAppId(), commConfig.getFadadaAppSecret(),
                commConfig.getFadadaVersion(), commConfig.getFadadaApiUrl());
        String response = clientbase.invokeUploadTemplate(templateId, null, docUrl);
        log.debug("调用法大大接口invokeUploadTemplate返回：" + response);
        Map<String, Object> resultMap = (Map<String, Object>) JSONObject.parse(response);
        if (null != resultMap && resultMap.size() > 0) {
            String code = (String) resultMap.get("code");
            String msg = (String) resultMap.get("msg");
            if (!"1".equals(code)) {
                log.error("----调用法大大invokeUploadTemplate接口失败----" + msg + "，templateId：" + templateId);
                return new BaseResponse(-1, msg);
            }
        }
        return new BaseResponse(0, "");
    }

    @Override
    public BaseResponse autoSignContract(SignContractDto signContractDto) throws MemberException {
        log.info("合同自动签署开始，input={}", JSON.toJSONString(signContractDto));
        //用户ID
        String authId = signContractDto.getAuthId();
        //查询会员
        MembershipInfoWithBLOBs member = membershipInfoMapper.selectByAuthId(authId, 0);
        if (StringUtils.isBlank(member.getName()) || StringUtils.isBlank(member.getMobilePhone())) {
            throw new MemberException(-1, "请先完成身份认证");
        }
        /**
         * 调用指定供应商的服务，执行签署操作
         */
        ContractSupplier cs = ContractSupplier.getInstance(NumberUtils.toInt(currentSupplier));
        ContractSignInput input = new ContractSignInput();
        input.setAuthId(member.getAuthId());
        input.setCertNo(member.getDriverCode());
        input.setMobilePhone(member.getMobilePhone());
        input.setPkId(member.getPkId());
        input.setName(member.getName());
        input.setCustomerId(member.getCustomerId());
        input.setContractVersionDtoList(signContractDto.getContractVersionDtoList());
        List<ContractVersionDto> contractVersionDtoList = signContractDto.getContractVersionDtoList();
        if (CollectionUtils.isEmpty(contractVersionDtoList)) {
            return new BaseResponse();
        }

        int provisionSupplier = 0;
        for (ContractVersionDto p : contractVersionDtoList) {
            Integer provisionType = (StringUtils.startsWith(p.getTemplateId(), "SZ")) ? 1 : 2;
            ProvisionInfoDto provision = userContractMapper.selectProvisionInfo(provisionType, p.getVersionId());
            if (provision == null) {
                return new BaseResponse();
            }
            provisionSupplier = provision.getSupplier();
            p.setSupplier(provision.getSupplier());
            p.setTemplateFileId(provision.getTemplateId());
        }
        //获取模板对应的供应商
        IMemberContractSignProvider contractSignProvider = getProvider(String.valueOf(provisionSupplier));
        /**
         * 执行签署操作
         */
        ContractSignOutput output = contractSignProvider.autoSignContract(input);
        /**
         * 签署成功，保存签署记录
         */
        if (CollectionUtils.isNotEmpty(output.getSignResults())) {
            //3.1 保存签署记录
            List<UserContractDto> contracts = new ArrayList<>();
            for (ContractSignResult p : output.getSignResults()) {
                //保存合同签署记录记录
                UserContractDto userContract = new UserContractDto();
                userContract.setSupplier(provisionSupplier);
                userContract.setAuthId(authId);
                userContract.setContractId(p.getContractId());
                userContract.setCustomerId(p.getCustomerId());
                userContract.setTransactionId(p.getTransactionId());
                userContract.setTemplateId(p.getTemplateId()); // sz+版本号
                userContract.setVersionId(p.getVersionId());
                userContract.setDownloadUrl(p.getDownloadUrl());
                userContract.setViewpdfUrl(p.getViewUrl());
                userContract.setArchiveUrl(p.getArchiveUrl());
                userContract.setStream(p.getStream());
                userContractMapper.insertSelective(userContract);
                contracts.add(userContract);
            }
            //3.2 异步归档签署结果文档，并更新
            AsyncArchiveSignFiles(contracts);
        }
        return new BaseResponse();
    }

    // private static  final String localPdf=
    public void archiveSignFiles(UserContractInfo contract) {
        log.warn("归档用户合同, authId={}, templateId={}", contract.getAuthId(), contract.getTemplateId());
        UserContract record=new UserContract();
        if (contract.getSupplier() == 1) { // e签宝
            //更新协议文件信息
            record.setId(contract.getId());
            if(contract.getStream()==null) {// 定时job处理未归档，stream为空，从本地获取
                // todo 暂时不做处理
                return;
            }
            String ossUrl = uploadEsignPdfToOss(contract.getStream(),contract.getAuthId(),
                    contract.getSupplier(),contract.getTemplateId());
            if (StringUtils.isBlank(ossUrl)) {
                log.warn("e签宝归档用户合同失败, authId={}, templateId={}", contract.getAuthId(), contract.getTemplateId());
                return;
            }
            //record.setContractId(contract.getContractId());//
            record.setArchiveUrl(ossUrl);
            record.setDownloadUrl(ossUrl);
            record.setViewpdfUrl(ossUrl);
        }else if(contract.getSupplier() == 0){
            IMemberContractSignProvider contractSignProvider = getProvider(String.valueOf(contract.getSupplier()));
            UserContractInfo userContract = contractSignProvider.getContractUrl(contract);
            if (StringUtils.isBlank(userContract.getContractId()) ||
                    StringUtils.isBlank(userContract.getDownloadUrl())) {
                log.warn("归档用户合同失败, authId={}, templateId={}", contract.getAuthId(), contract.getTemplateId());
                return;
            }
            //更新协议文件信息
            record.setId(contract.getId());
            log.warn("归档用户合同uploadUserContractFile, fileUrl={}", userContract.getDownloadUrl());
            String archiveUrl = uploadUserContractFile(userContract.getAuthId(), userContract.getSupplier(),
                    userContract.getContractId(), userContract.getDownloadUrl());
            record.setArchiveUrl(archiveUrl);
        }
        userContractMapper.updateByPrimaryKeySelective(record);
        log.warn("归档用户合同, 完成，authId={}, templateId={}", contract.getAuthId(), contract.getTemplateId());
    }


    private String uploadEsignPdfToOss(byte[] stream,String authId, Integer supplier, String templateId) {
        String fileName = authId + "/" + supplier + "_" + templateId;
        String objectName = "sensitiveBucket/userContract/" + fileName + ".pdf";
        try {
            UploadImgUtil.uploadByteArray(stream,objectName,UploadImgUtil.OSS_SENSITIVE_BUCKET);
        } catch (Exception e) {
            log.error("e签宝签署文件推送oss异常",JSON.toJSONString(e));
            // 降级保存到本地， 后续可以补偿机制处理
            // todo 暂时不做处理
        }
        return objectName;
    }

    public void AsyncArchiveSignFiles(List<UserContractDto> userContracts) {
        if (CollectionUtils.isEmpty(userContracts)) {
            return;
        }
        String authId = userContracts.get(0).getAuthId();
        /**
         * 异步归档用户协议到自身文件服务器
         */
        ThreadPoolUtils.SCHEDULED_EXECUTOR.schedule(() -> {
            try {
                log.warn("归档用户合同start..., authId={}", authId);
                for (UserContractDto contract : userContracts) {
                    UserContractInfo contractInfo = new UserContractInfo();
                    BeanCopyUtils.copyProperties(contract, contractInfo);
                    contractInfo.setStream(contract.getStream());
                    archiveSignFiles(contractInfo);
                }
                log.warn("归档用户合同 end..., authId=" + authId);
            } catch (Exception e) {
                log.warn("归档用户合同 filed..., authId=" + authId, e);
            }
        }, 10, TimeUnit.SECONDS);
    }

   /* public void AsyncArchiveSignFiles(List<UserContract> userContracts) {
        if (CollectionUtils.isEmpty(userContracts)) {
            return;
        }
        String authId = userContracts.get(0).getAuthId();
        *//**
         * 异步归档用户协议到自身文件服务器
         *//*
        ThreadPoolUtils.SCHEDULED_EXECUTOR.schedule(() -> {
            try {
                log.warn("归档用户合同start..., authId={}", authId);
                for (UserContract contract : userContracts) {
                    UserContractInfo contractInfo = new UserContractInfo();
                    BeanCopyUtils.copyProperties(contract, contractInfo);
                    archiveSignFiles(contractInfo);
                }
                log.warn("归档用户合同 end..., authId=" + authId);
            } catch (Exception e) {
                log.warn("归档用户合同 filed..., authId=" + authId, e);
            }
        }, 10, TimeUnit.SECONDS);
    }*/

    @Override
    public UpdateTemplateResponse uploadTemplate(String templateId, String docUrl) {
        IMemberContractSignProvider contractSignProvider = getProvider(currentSupplier);
        log.info("MemberContractService.uploadTemplate, templateId={}, docUrl={}", templateId, docUrl);
        return contractSignProvider.uploadTemplate(templateId, docUrl);
    }

    @Override
    public UserContractInfo gainContractUrl(String authId, String templateId) {
        UserContractInfo contract = userContractMapper.selectUserContractInfo(authId, templateId);
        if (contract == null) {
            log.error("查询用户协议文件失败，authId={}, templatId={}.", authId, templateId);
            return null;
        }
        //未归档成功，则从供应商服务上获得文件
        IMemberContractSignProvider contractSignProvider = getProvider(String.valueOf(contract.getSupplier()));
        UserContractInfo contractInfo = contractSignProvider.getContractUrl(contract);
        contract.setArchiveUrl(contractInfo.getArchiveUrl());
        return contract;
    }

    @Override
    public ContractSupplier getCurrentContractSupplier() {
        Integer type = NumberUtils.toInt(currentSupplier);
        return ContractSupplier.getInstance(type);
    }

    /**
     * 上传协议文件
     *
     * @param authId
     * @param supplier
     * @param contractId
     * @param fileUrl
     * @return
     * @throws BusinessException
     */
    public String uploadUserContractFile(String authId, Integer supplier, String contractId, String fileUrl) throws MemberException {
        String fileName = authId + "/" + supplier + "_" + contractId;
        String objectName = "/sensitiveBucket/userContract/" + fileName + ".pdf";
        UploadImgUtil.uploadSynByFileUrl(fileUrl, objectName, UploadImgUtil.OSS_SENSITIVE_BUCKET);
        return objectName;
    }

    @Resource
    private IMemberShipService memberShipService;

    @Override
    public AddRentCarContractResponse addRentCarContract(AddRentCarContractRequest request) {
        AddRentCarContractResponse response = new AddRentCarContractResponse();
        String noSignPdfUrl = request.getPdfUrl();
        // 特殊处理
        if (noSignPdfUrl.contains(OssConfigUtil.getOssUrlPre())) {
            noSignPdfUrl = noSignPdfUrl.replace(OssConfigUtil.getOssUrlPre(),"");
        }
        String mid = request.getMid();
        String userSignPicUrl = request.getUserSignPicUrl();
        // 1 租车合同 5：结算单 6：租车单
        int operateType = request.getOperateType();
        String operOrgCode = request.getOperOrgCode();
        String operOrgName = request.getOperOrgName();

        if (operateType == 1 && (StringUtils.isBlank(operOrgName) || StringUtils.isBlank(operOrgCode))) {
            response.setCode(-1);
            response.setMessage("入参必填项未传入");
            return response;
        }

        // 前端zjd 不好实现，后端兼容
        String getPDFSignImgHtml = "<img src=\"" + userSignPicUrl + "\"alt=\"\" style=\"margin-left: 23px;margin-bottom: -108px;width: 156px;transform: rotate(-90deg);height: 200px;\">";

        log.info("用户签订租车合同开始，入参request=[{}]", JSON.toJSONString(request));
        MembershipBasicInfo membershipBasicInfo = memberShipService.getUserBasicInfo(mid);
        if (membershipBasicInfo == null) {
            response.setCode(-1);
            response.setMessage("用户不存在");
            return response;
        }

        ByteArrayOutputStream outputStream = null;
        InputStream objectContent = null;
        try {
            //1:从阿里云下载 html模板
            OSSObject ossObject = UploadImgUtil.downloadStream(noSignPdfUrl);
            objectContent = ossObject.getObjectContent();
            String html = IOUtils.toString(objectContent, StandardCharsets.UTF_8);
            log.info("成功从阿里云下载模板");

            // 2：修改 html模板
            String newHtml = StringUtils.EMPTY;
            if (operateType == 1) {
                String name = membershipBasicInfo.getName();

                String idCardNumber = membershipBasicInfo.getPassportNo();
                // 外籍用驾照号
                if (!IdTypeEnum.isMainlandId(membershipBasicInfo.getIdType())) {
                    idCardNumber = membershipBasicInfo.getDriverCode();
                }

                // 获取甲的公司用章
                String jiaSeal = getSealPicByOrgCode(operOrgCode);
                if (StringUtils.isBlank(jiaSeal)) {
                    log.error("获取甲公司用章失败，入参request=[{}]", JSON.toJSONString(request));
                    response.setCode(-1);
                    response.setMessage("获取公司用章失败");
                    return response;
                }
                String jiaSealHtml = "<img src=\"" + jiaSeal + "\" style=\"margin-bottom: -110px;width: 136px;height: 136px;margin-left: 32px;\">";
                String storeManagerMobile = request.getStoreManagerMobile();
                String storeMobile = request.getStoreMobile();

                String date = ComUtil.getSystemDate(ComUtil.DATE_TYPE22);
                newHtml = html.replace(BussinessConstants.HTML_YINAME, name)
                        .replace(BussinessConstants.HTML_JIANAME, operOrgName) // 获取甲的公司名称
                        .replace(BussinessConstants.HTML_DATE, date)
                        .replace(BussinessConstants.HTML_SIGN, getPDFSignImgHtml)
                        .replace(BussinessConstants.HTML_IDCARD, idCardNumber)
                        .replace(BussinessConstants.HTML_JIA_SEAL, jiaSealHtml);

                if(StringUtils.isNotEmpty(storeManagerMobile)){
                    newHtml = newHtml.replace(BussinessConstants.SOTRE_MANAGER_MOBILE, storeManagerMobile);
                }

                if(StringUtils.isNotEmpty(storeMobile)){
                    newHtml = newHtml .replace(BussinessConstants.SOTRE_MOBILE,  storeMobile);
                }
            } else {
                // 租车单 、结算单 只需要替换签字区域
                newHtml = html.replace(BussinessConstants.HTML_SIGN2, getPDFSignImgHtml);
            }
            log.info("html替换完成");
            // 3：html模板转化成pdf
            outputStream = PdfUtil.convertToPdf2(newHtml, null);
            byte[] byteArray = outputStream.toByteArray();
            log.info("html模板转化成pdf成功");
            String targetPath = "rentcarcontract/" + operateType + "/" + membershipBasicInfo.getAuthId() + "/" + request.getContractId() + "/" + System.currentTimeMillis() + ".pdf";
            if (operateType == 1) {
                String fileName = "ZCHT_"+request.getContractId() +"_"+System.currentTimeMillis()+ ".pdf";
                targetPath = "rentcarcontract/" + operateType + "/" + membershipBasicInfo.getAuthId() + "/" + request.getContractId() + "/" +fileName;
                String accountId = memberContractEsignService.getMemberAccount(membershipBasicInfo.getPkId(), membershipBasicInfo.getName(), null);
                String personSeal = EsignService.addPersonSeal(accountId);
                log.info("e签宝签订租车合同：开始签署本合同，authId={}, customerId={}，contranctId={}", membershipBasicInfo.getAuthId(), accountId,request.getContractId());
                FileDigestSignResult fileDigestSignResult = EsignService.signPdfByPerson(outputStream.toByteArray(), accountId, personSeal,
                        fileName, posX, posY);
                log.info("e签宝签订租车合同成功： contranctId={}", request.getContractId());
                byteArray = fileDigestSignResult.getStream();
            }
             // 4：pdf 上传到 阿里云上
            UploadImgUtil.uploadByteArray(byteArray, targetPath, UploadImgUtil.OSS_BUCKET);
            log.info("pdf上传到阿里云上成功");

            String imageUrl = OssConfigUtil.getFileBaseUrl() + "/" + targetPath;
            response.setPdfUrl(imageUrl);
        } catch (Exception e) {
            log.error("用户签租车合同异常，request={}", JSON.toJSONString(request), e);
            response.setCode(-1);
            response.setMessage("用户签订合同失败");
        } finally {
            try {
                objectContent.close();
                outputStream.close();
            } catch (Exception e) {
                log.error("用户签租车合同异常，request={}", JSON.toJSONString(request), e);
            }
        }
        log.info("用户签订租车合同结束，入参request=[{}],response=[{}]", JSON.toJSONString(request), JSON.toJSONString(response));
        return response;
    }

    // 可配置的印章图片json
    /**
     *
     *
     * {
     * 	"companySeals": [{
     * 		"orgCode": "00",
     * 		"orgName": "环球车享汽车租赁有限公司",
     * 		"sealPicUrl": "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/00.png"
     *        },
     *    {
     * 		"orgCode": "009W",
     * 		"orgName": "上海环行汽车租赁有限公司乌鲁木齐分公司",
     * 		"sealPicUrl": "https://evcard.oss-cn-shanghai.aliyuncs.com/rentcar/seal/009W.png"
     *    }]
     * }
     */
    @ApolloJsonValue("${config.contract.seal.json:{}}")
    public CompanySeals companySeals;

    public String getSealPicByOrgCode(String operOrgCode) {
        String sealPicUrl = CompanySealEnum.getSealPicUrlByOrgCode(operOrgCode);
        if (StringUtils.isNotBlank(sealPicUrl)) {
            return sealPicUrl;
        }

        if (companySeals != null && CollectionUtils.isNotEmpty(companySeals.getCompanySeals())) {
            for (CompanySeal companySeal : companySeals.getCompanySeals()) {
                if (StringUtils.equals(operOrgCode, companySeal.getOrgCode())) {
                    sealPicUrl = companySeal.getSealPicUrl();
                    return sealPicUrl;
                }
            }
        }
        return null;
    }


    @Override
    public OfcQueryContractResponse ofcQueryContract(OfcQueryContractRequest request) {
        OfcQueryContractResponse response = new OfcQueryContractResponse();
        if (!ofcQueryContractParamsCheck(request)) {
            response.setCode(-1);
            response.setMessage("参数校验失败");
            return response;
        }

        String mid = request.getMid();
        MembershipBasicInfo userBasicInfo = memberShipService.getUserBasicInfo(mid);
        if (userBasicInfo == null) {
            response.setCode(-1);
            response.setMessage("用户不存在");
            return response;
        }

        ByteArrayOutputStream template = new ByteArrayOutputStream();
        try {
            // 1：拿到渲染后的html
            OfcQueryContractBo bo = request.toOfcQueryContractBo();
            bo.setName(userBasicInfo.getName());
            Integer idType = userBasicInfo.getIdType();
            bo.setIdCardType(idType);

            if (IdTypeEnum.isMainlandId(idType)) {
                bo.setIdCardNo(userBasicInfo.getPassportNo());
            }else{
                bo.setIdCardNo(userBasicInfo.getDriverCode());
            }
            bo.setMobile(userBasicInfo.getMobilePhone());
            Map<String, Object> dataModel = new HashMap<>();
            dataModel.put("bo", bo);

            template = FreemarkerUtil.getTemplate(dataModel, "rental.ftl");
            if (template == null) {
                response.setCode(-1);
                response.setMessage("模板渲染失败");
                return response;
            }
            log.info("FreeMarker渲染html页面成功");

            // 2：上传到 阿里云
            String targetPath = "rentCar/" + request.getType() + "/" + userBasicInfo.getAuthId() + "/" + bo.getOrderBaseInfo().getOrderNo() + "/" + System.currentTimeMillis() + ".html";
            UploadImgUtil.uploadByteArray(template.toByteArray(), targetPath, UploadImgUtil.OSS_BUCKET);
            log.info("html上传到阿里云上成功");
            String htmlUrl = OssConfigUtil.getFileBaseUrl() + "/" + targetPath;
            response.setHtmlUrl(htmlUrl);
        } catch (BusinessException e) {
            log.error("用户查看租车单、结算单业务异常，request={}", JSON.toJSONString(request), e);
            response.setCode(-1);
            response.setMessage("用户查看租车单、结算单失败");
        } catch (Exception e) {
            log.error("用户查看租车单、结算单异常，request={}", JSON.toJSONString(request), e);
            response.setCode(-1);
            response.setMessage("用户查看租车单、结算单失败");
        } finally {
            try {
                template.close();
            } catch (Exception e) {
                log.error("用户签租车单关闭IO异常，request={}", JSON.toJSONString(request), e);
            }
        }

        return response;
    }

    /**
     * 参数校验
     *
     * @param request
     * @return
     */
    private boolean ofcQueryContractParamsCheck(OfcQueryContractRequest request) {
        return true;
    }
}