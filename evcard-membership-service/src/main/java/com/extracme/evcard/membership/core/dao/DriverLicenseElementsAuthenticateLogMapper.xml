<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.core.dao.DriverLicenseElementsAuthenticateLogMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.DriverLicenseElementsAuthenticateLog" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="record_id" property="recordId" jdbcType="BIGINT" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="driver_code" property="driverCode" jdbcType="VARCHAR" />
    <result column="file_no" property="fileNo" jdbcType="VARCHAR" />
    <result column="log_type" property="logType" jdbcType="INTEGER" />
    <result column="supplier" property="supplier" jdbcType="INTEGER" />
    <result column="service_name" property="serviceName" jdbcType="VARCHAR" />
    <result column="result" property="result" jdbcType="VARCHAR" />
    <result column="result_code" property="resultCode" jdbcType="VARCHAR" />
    <result column="result_msg" property="resultMsg" jdbcType="VARCHAR" />
    <result column="license_status_msg" property="licenseStatusMsg" jdbcType="VARCHAR" />
    <result column="elements_review_items" property="elementsReviewItems" jdbcType="VARCHAR" />
    <result column="response" property="response" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="misc_desc" property="miscDesc" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, record_id, name, driver_code, file_no, log_type, supplier, service_name,
    result, result_code, result_msg, response, status, misc_desc, create_time, create_oper_id, 
    create_oper_name, update_time, update_oper_id, update_oper_name, license_status_msg, elements_review_items
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from ${siacSchema}.driver_license_elements_authenticate_log
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByRecordId" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from ${siacSchema}.driver_license_elements_authenticate_log
    where status = 1
    and record_id = #{recordId,jdbcType=BIGINT}
    and log_type = #{logType,jdbcType=BIGINT}
    order by id desc limit 1
  </select>

  <select id="selectByRecordIds" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from ${siacSchema}.driver_license_elements_authenticate_log
    where status = 1
    and record_id in
    <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
      ${item}
    </foreach>
    <if test="logType != null">
      and `log_type` = #{logType,jdbcType=BIGINT}
    </if>
    order by id desc
    <if test="limit != null">
      limit #{limit}
    </if>
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from ${siacSchema}.driver_license_elements_authenticate_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.membership.core.model.DriverLicenseElementsAuthenticateLog" >
    insert into ${siacSchema}.driver_license_elements_authenticate_log (id, record_id, name,
      driver_code, file_no,
      log_type, supplier, service_name, 
      result, result_code, result_msg, license_status_msg, elements_review_items,
      response, status, misc_desc, 
      create_time, create_oper_id, create_oper_name, 
      update_time, update_oper_id, update_oper_name
      )
    values (#{id,jdbcType=BIGINT}, #{recordId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, 
      #{driverCode,jdbcType=VARCHAR}, #{fileNo,jdbcType=VARCHAR},
      #{logType,jdbcType=INTEGER}, #{supplier,jdbcType=INTEGER}, #{serviceName,jdbcType=VARCHAR}, 
      #{result,jdbcType=VARCHAR}, #{resultCode,jdbcType=VARCHAR}, #{resultMsg,jdbcType=VARCHAR}, #{licenseStatusMsg,jdbcType=VARCHAR},
      #{response,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{miscDesc,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, #{createOperName,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, #{updateOperName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.core.model.DriverLicenseElementsAuthenticateLog" >
    insert into ${siacSchema}.driver_license_elements_authenticate_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="recordId != null" >
        record_id,
      </if>
      <if test="name != null" >
        name,
      </if>
      <if test="driverCode != null" >
        driver_code,
      </if>
      <if test="fileNo != null" >
        file_no,
      </if>
      <if test="logType != null" >
        log_type,
      </if>
      <if test="supplier != null" >
        supplier,
      </if>
      <if test="serviceName != null" >
        service_name,
      </if>
      <if test="result != null" >
        result,
      </if>
      <if test="resultCode != null" >
        result_code,
      </if>
      <if test="resultMsg != null" >
        result_msg,
      </if>
      <if test="licenseStatusMsg != null" >
        license_status_msg,
      </if>
      <if test="elementsReviewItems != null" >
        elements_review_items,
      </if>
      <if test="response != null" >
        response,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="miscDesc != null" >
        misc_desc,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateOperId != null" >
        update_oper_id,
      </if>
      <if test="updateOperName != null" >
        update_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="recordId != null" >
        #{recordId,jdbcType=BIGINT},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="driverCode != null" >
        #{driverCode,jdbcType=VARCHAR},
      </if>
      <if test="fileNo != null" >
        #{fileNo,jdbcType=VARCHAR},
      </if>
      <if test="logType != null" >
        #{logType,jdbcType=INTEGER},
      </if>
      <if test="supplier != null" >
        #{supplier,jdbcType=INTEGER},
      </if>
      <if test="serviceName != null" >
        #{serviceName,jdbcType=VARCHAR},
      </if>
      <if test="result != null" >
        #{result,jdbcType=VARCHAR},
      </if>
      <if test="resultCode != null" >
        #{resultCode,jdbcType=VARCHAR},
      </if>
      <if test="resultMsg != null" >
        #{resultMsg,jdbcType=VARCHAR},
      </if>
      <if test="licenseStatusMsg != null" >
        #{licenseStatusMsg,jdbcType=VARCHAR},
      </if>
      <if test="elementsReviewItems != null" >
        #{elementsReviewItems,jdbcType=VARCHAR},
      </if>
      <if test="response != null" >
        #{response,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="miscDesc != null" >
        #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        #{updateOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.core.model.DriverLicenseElementsAuthenticateLog" >
    update ${siacSchema}.driver_license_elements_authenticate_log
    <set >
      <if test="recordId != null" >
        record_id = #{recordId,jdbcType=BIGINT},
      </if>
      <if test="name != null" >
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="driverCode != null" >
        driver_code = #{driverCode,jdbcType=VARCHAR},
      </if>
      <if test="fileNo != null" >
        file_no = #{fileNo,jdbcType=VARCHAR},
      </if>
      <if test="logType != null" >
        log_type = #{logType,jdbcType=INTEGER},
      </if>
      <if test="supplier != null" >
        supplier = #{supplier,jdbcType=INTEGER},
      </if>
      <if test="serviceName != null" >
        service_name = #{serviceName,jdbcType=VARCHAR},
      </if>
      <if test="result != null" >
        result = #{result,jdbcType=VARCHAR},
      </if>
      <if test="resultCode != null" >
        result_code = #{resultCode,jdbcType=VARCHAR},
      </if>
      <if test="resultMsg != null" >
        result_msg = #{resultMsg,jdbcType=VARCHAR},
      </if>
      <if test="licenseStatusMsg != null" >
        license_status_msg = #{licenseStatusMsg,jdbcType=VARCHAR},
      </if>
      <if test="elementsReviewItems != null" >
        elements_review_items = #{elementsReviewItems,jdbcType=VARCHAR},
      </if>
      <if test="response != null" >
        response = #{response,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="miscDesc != null" >
        misc_desc = #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.core.model.DriverLicenseElementsAuthenticateLog" >
    update ${siacSchema}.driver_license_elements_authenticate_log
    set record_id = #{recordId,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      driver_code = #{driverCode,jdbcType=VARCHAR},
      file_no = #{fileNo,jdbcType=VARCHAR},
      log_type = #{logType,jdbcType=INTEGER},
      supplier = #{supplier,jdbcType=INTEGER},
      service_name = #{serviceName,jdbcType=VARCHAR},
      result = #{result,jdbcType=VARCHAR},
      result_code = #{resultCode,jdbcType=VARCHAR},
      result_msg = #{licenseStatusMsg,jdbcType=VARCHAR},
      license_status_msg = #{resultMsg,jdbcType=VARCHAR},
      elementsReviewItems = #{elements_review_items,jdbcType=VARCHAR},
      response = #{response,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      misc_desc = #{miscDesc,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>