package com.extracme.evcard.membership.core.model;

import java.util.Date;

public class MmpPlatformInfo {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_platform_info.id
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_platform_info.platform_name
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    private String platformName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_platform_info.platform_explain
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    private String platformExplain;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_platform_info.misc_desc
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    private String miscDesc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_platform_info.status
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    private Integer status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_platform_info.create_time
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_platform_info.create_oper_id
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    private Long createOperId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_platform_info.create_oper_name
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    private String createOperName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_platform_info.update_time
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_platform_info.update_oper_id
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    private Long updateOperId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_platform_info.update_oper_name
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    private String updateOperName;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_platform_info.id
     *
     * @return the value of mmp_platform_info.id
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_platform_info.id
     *
     * @param id the value for mmp_platform_info.id
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_platform_info.platform_name
     *
     * @return the value of mmp_platform_info.platform_name
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    public String getPlatformName() {
        return platformName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_platform_info.platform_name
     *
     * @param platformName the value for mmp_platform_info.platform_name
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_platform_info.platform_explain
     *
     * @return the value of mmp_platform_info.platform_explain
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    public String getPlatformExplain() {
        return platformExplain;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_platform_info.platform_explain
     *
     * @param platformExplain the value for mmp_platform_info.platform_explain
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    public void setPlatformExplain(String platformExplain) {
        this.platformExplain = platformExplain;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_platform_info.misc_desc
     *
     * @return the value of mmp_platform_info.misc_desc
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    public String getMiscDesc() {
        return miscDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_platform_info.misc_desc
     *
     * @param miscDesc the value for mmp_platform_info.misc_desc
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_platform_info.status
     *
     * @return the value of mmp_platform_info.status
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_platform_info.status
     *
     * @param status the value for mmp_platform_info.status
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_platform_info.create_time
     *
     * @return the value of mmp_platform_info.create_time
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_platform_info.create_time
     *
     * @param createTime the value for mmp_platform_info.create_time
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_platform_info.create_oper_id
     *
     * @return the value of mmp_platform_info.create_oper_id
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    public Long getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_platform_info.create_oper_id
     *
     * @param createOperId the value for mmp_platform_info.create_oper_id
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_platform_info.create_oper_name
     *
     * @return the value of mmp_platform_info.create_oper_name
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_platform_info.create_oper_name
     *
     * @param createOperName the value for mmp_platform_info.create_oper_name
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_platform_info.update_time
     *
     * @return the value of mmp_platform_info.update_time
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_platform_info.update_time
     *
     * @param updateTime the value for mmp_platform_info.update_time
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_platform_info.update_oper_id
     *
     * @return the value of mmp_platform_info.update_oper_id
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    public Long getUpdateOperId() {
        return updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_platform_info.update_oper_id
     *
     * @param updateOperId the value for mmp_platform_info.update_oper_id
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_platform_info.update_oper_name
     *
     * @return the value of mmp_platform_info.update_oper_name
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    public String getUpdateOperName() {
        return updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_platform_info.update_oper_name
     *
     * @param updateOperName the value for mmp_platform_info.update_oper_name
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }
}