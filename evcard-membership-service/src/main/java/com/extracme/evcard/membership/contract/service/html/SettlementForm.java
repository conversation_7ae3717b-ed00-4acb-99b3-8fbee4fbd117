package com.extracme.evcard.membership.contract.service.html;

import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.rpc.exception.BusinessException;
import org.springframework.stereotype.Service;

@Service
public class SettlementForm implements IRentCarContractService {

    @Override
    public String description() {
        return "结算单";
    }

    @Override
    public String renderContractHtml(RenderHtmlContext context) throws BusinessException {
        String initHtml = context.getInitHtml();
        String signImgHtml = context.getSignImgHtml();

        // 租车单 、结算单 只需要替换签字区域
        return initHtml.replace(BussinessConstants.HTML_SIGN2, signImgHtml);
    }
}
