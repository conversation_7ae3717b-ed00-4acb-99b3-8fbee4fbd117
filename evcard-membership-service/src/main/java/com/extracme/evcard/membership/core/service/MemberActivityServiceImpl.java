package com.extracme.evcard.membership.core.service;

import com.extracme.evcard.activity.dubboService.IRegisterRewardService;
import com.extracme.evcard.membership.core.dao.UserOrderReduceActivityRecordMapper;
import com.extracme.evcard.membership.core.dto.IdentityCertInfo;
import com.extracme.evcard.membership.core.dto.UserInstantActivityRecordDto;
import com.extracme.evcard.membership.core.model.UserOrderReduceActivityRecord;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.mq.RawListener;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> chennian.
 * @Date ：Created in 10:45 2021/2/1
 */
@Service("memberActivityService")
public class MemberActivityServiceImpl implements IMemberActivityService {

    private static Logger logger = LoggerFactory.getLogger(MemberCardServiceImpl.class);

    @Autowired
    private UserOrderReduceActivityRecordMapper userOrderReduceActivityRecordMapper;

    @Resource
    private RawListener rawListener;

    @Autowired
    private MembershipInfoMapper membershipInfoMapper;

    @Resource
    private IRegisterRewardService registerRewardService;

    @Autowired
    private IMemberCertificationService memberCertificationService;

    @Override
    public List<UserInstantActivityRecordDto> getInstantActivityRecord(String authId, Long activityId) {
        List<UserInstantActivityRecordDto> resultList = new ArrayList<>();
        UserOrderReduceActivityRecord condition = new UserOrderReduceActivityRecord();
        condition.setAuthId(authId);
        condition.setReduceAmountActivityId(activityId);
        List<UserOrderReduceActivityRecord> recordList = userOrderReduceActivityRecordMapper.selectByCondition(condition);
        if (CollectionUtils.isNotEmpty(recordList)) {
            for (UserOrderReduceActivityRecord record : recordList) {
                UserInstantActivityRecordDto dto = new UserInstantActivityRecordDto();
                BeanUtils.copyProperties(record, dto);
                resultList.add(dto);
            }
        }
        return resultList;
    }

    @Override
    public boolean checkFirstAuditRewards(String authId, String mid) {
        IdentityCertInfo identityCertInfo = memberCertificationService.getIdentityCertInfo(mid);
        if (identityCertInfo.getState() != 1) {
            return false;
        }
        BigDecimal totalAmount = rawListener.getFirstAuditRewards(authId, mid);
        return (BigDecimal.ZERO.compareTo(totalAmount) < 0);
    }
}
