package com.extracme.evcard.membership.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.csvreader.CsvReader;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.extracme.evcard.elasticjob.ElasticSimpleJob;
import com.extracme.evcard.membership.common.YiGuanTraceSqlExportUtil;
import com.extracme.evcard.membership.core.dto.*;
import com.extracme.evcard.membership.core.input.QueryMemberListInputDTO;
import com.extracme.evcard.membership.core.service.MemberShipServiceImpl;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;

/**
 * 易观 会员公共信息 埋点
 *  每日补全易观缺失的用户信息
 *
 * <AUTHOR>
 *
 */
@Slf4j
@Component
@ElasticSimpleJob(jobName = "evcard-membership-addMemberTrackJob",
		cron = "0 0 2 * * ?", description = "易观埋点补全会员信息", overwrite = true)
public class YiguanMemberTrackAddJob implements SimpleJob{

	@Autowired
	private MembershipInfoMapper membershipInfoMapper;

	@Autowired
	private MemberShipServiceImpl memberShipService;

	@Override
	public void execute(ShardingContext arg0) {

		int pageNum = 2000;
		//查询易观中缺失的会员
		String dir = "/evcard/evcard-membership-rpc/tmpUser";
		YiGuanTraceSqlExportUtil.sqlExport(dir);
		List<String> list = new ArrayList<>();
		int tempStart = 0;
		int tempEnd = pageNum;
		do {
			list = readCSV(dir + "/output.csv", tempStart, tempEnd);
			if (CollectionUtils.isEmpty(list)) {
				break;
			}
			tempStart = tempStart + pageNum;
			tempEnd = tempEnd + pageNum;
			QueryMemberListInputDTO queryMemberListInputDTO = new QueryMemberListInputDTO();
			queryMemberListInputDTO.setMemberType(0);
			queryMemberListInputDTO.setAuthIdList(list);
			List<MembershipBasicInfo> membershipInfoList = membershipInfoMapper.queryMemberListByCondition(queryMemberListInputDTO);
			if(CollectionUtils.isNotEmpty(membershipInfoList)){
				for(MembershipBasicInfo p : membershipInfoList){
					memberShipService.traceMember(p);
				}
			}
		} while (list.size() == pageNum);


	}

	/**
	 * 读取CSV文件
	 * @param filePath:全路径名
	 */
	public static List<String> readCSV(String filePath, int startRow, int endLine) {
		CsvReader reader = null;
		List<String> dataList = new ArrayList<>();
		try {
			//如果生产文件乱码，windows下用gbk，linux用UTF-8
			reader = new CsvReader(filePath, ',', Charset.forName("GBK"));

			// 逐条读取记录，直至读完
			reader.getIndex("{\"xwho\":\"13800003042094851422\"}");
			while (reader.readRecord()) {
				long rowNum = reader.getCurrentRecord();
				if (startRow <= rowNum && rowNum <= endLine - 1) {
					String text = reader.get(0);
					JSONObject jsonObject = JSON.parseObject(text);
					String authId = jsonObject.getString("xwho");
					dataList.add(authId);
				}
			}
		} catch (Exception e) {
			log.error("读取csv易观埋点用户信息失败", e);
		} finally {
			if (null != reader) {
				reader.close();
			}
		}
		return dataList;
	}

}
