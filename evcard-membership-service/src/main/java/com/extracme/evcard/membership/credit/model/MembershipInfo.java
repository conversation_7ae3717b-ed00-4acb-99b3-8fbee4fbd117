package com.extracme.evcard.membership.credit.model;

import java.math.BigDecimal;
import java.util.Date;

public class MembershipInfo {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.pk_id
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private Long pkId;

    /**
     * 用户唯一标识mid
     */
    private String mid;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.AUTH_ID
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String authId;

    /**
     * 享道统一用户ID
     */
    private String uid;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.DRIVER_CODE
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String driverCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.OPEN_ID
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String openId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.NAME
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String name;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.PASSWORD
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String password;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.GENDER
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String gender;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.MOBILE_PHONE
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String mobilePhone;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.MAIL
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String mail;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.BIRTH_DATE
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String birthDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.REG_TIME
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String regTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.ZIP
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String zip;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.ADDRESS
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String address;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.PROVINCE
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String province;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.CITY
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String city;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.AREA
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String area;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.MEMBERSHIP_TYPE
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private Short membershipType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.USER_LEVEL
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private Short userLevel;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.ORG_ID
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String orgId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.AGENCY_ID
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String agencyId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.AUTH_KIND
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String authKind;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.CERTIFICATE_VALIDITY
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String certificateValidity;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.CERTIFICATE_ADDRESS
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String certificateAddress;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.DRIVING_LICENSE
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String drivingLicense;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.OBTAIN_DRIVER_TIMER
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String obtainDriverTimer;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.LICENSE_EXPIRATION_TIME
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String licenseExpirationTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.EMERGENCY_CONTACT
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String emergencyContact;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.EMERGENCY_MOBIL
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String emergencyMobil;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.GUARANTEE_NAME
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String guaranteeName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.GUARANTEE_IC_CARD_KIND
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private Short guaranteeIcCardKind;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.GUARANTEE_IC_CARD
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String guaranteeIcCard;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.GUARANTEE_MOBIL
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String guaranteeMobil;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.REVIEW_REASON
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String reviewReason;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.REVIEW_STATUS
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private Short reviewStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.CARD_NO
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String cardNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.CREDIT_NO
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String creditNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.DATA_ORIGIN
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private Short dataOrigin;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.ID
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private BigDecimal id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.STATUS
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private Short status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.BLACKLIST_REASON
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String blacklistReason;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.REVIEW_USER
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String reviewUser;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.REVIEW_ITEMS
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String reviewItems;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.ILLEGAL_METHOD
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private Short illegalMethod;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.DEPOSIT
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private BigDecimal deposit;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.RESERVE_AMOUNT
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private BigDecimal reserveAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.RENT_MINS
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private BigDecimal rentMins;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.EXEMPT_DEPOSIT
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private Short exemptDeposit;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.EMPNO
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String empno;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.INFO_ORIGIN
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String infoOrigin;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.CREATED_TIME
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String createdTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.CREATED_USER
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String createdUser;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.UPDATED_TIME
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String updatedTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.UPDATED_USER
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String updatedUser;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.PERSONNEL_STATE
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private Short personnelState;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.SHARE_UID
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String shareUid;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.EZBIKE
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private Short ezbike;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.APPLY_STATUS
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private Short applyStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.SERVICE_VER
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String serviceVer;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.SERVICE_VER_TIME
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String serviceVerTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.APP_KEY
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String appKey;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.TYPE_FLAG
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private Short typeFlag;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.INVOICED_AMOUNT
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private BigDecimal invoicedAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.CHANNEL_ID
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String channelId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.REGIONID
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private Long regionid;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.deposit_vehicle
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private BigDecimal depositVehicle;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.province_of_origin
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String provinceOfOrigin;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.city_of_origin
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String cityOfOrigin;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.area_of_origin
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String areaOfOrigin;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.idcard_pic_url
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String idcardPicUrl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.hold_idcard_pic_url
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String holdIdcardPicUrl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.point
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private Integer point;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.REVIEW_TIME
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String reviewTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.APP_REVIEW_TIME
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String appReviewTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.REVIEW_REMARK
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String reviewRemark;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.REVIEW_ITEM_IDS
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String reviewItemIds;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.REVIEW_ITEM_NAME
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String reviewItemName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.REVIEW_MODE
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private Integer reviewMode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.FACE_RECOGNITION_IMG_URL
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String faceRecognitionImgUrl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.customer_id
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String customerId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column membership_info.id_card_number
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    private String idCardNumber;


    /**
     * 认证状态 0 未认证 1 未刷脸 2 已认证
     */
    private Integer authenticationStatus;

    /**
     * 是否需要刷脸 1-不需要刷脸  2-需要刷脸
     * */
    private Integer needFace;

    /**
     * 国籍
     */
    private String national;

    /**
     * 驾照类型
     */
    private String drivingLicenseType;

    /** 0  无  1 驾照ocr识别 2 手动输入 */
    private Integer driverLicenseInputType;

    /** 注销时间 */
    private String unRegisterTime;

    /** 护照号 */
    private String passportNo;

     /** 档案编号 */
    private String fileNo;

     /** 驾照附页照片地址 */
    private String fileNoImgUrl;

    /** 1：身份证 2:护照 3：香港澳门通行证 4 台湾通行证 */
    private Integer idType;

    /** 驾照认证状态 0待认证 1认证通过 2认证不通过 */
    private Integer licenseAuthStatus;
    /** 驾照三要素认证状态：0待认证 1认证通过 2认证不通过 3待重新认证(查证中) */
    private Integer licenseElementsAuthStatus;
    /** 驾照状态信息：吊销、撤销、注销、停止使用、锁定等异常状态 */
    private String licenseStatusMsg;
    /**
     * elementsReviewItems: 驾照号/姓名/档案编号
     * 0不一致 1一致
     */
    private String elementsReviewItems;

    private Integer accountStatus;

    /**
     *
     * 身份证 认证表 id
     */
    private Long identityId;
    //初次身份认证通过时间
    private Date identityFirstAuthTime;
    //驾照认证状态(1:未认证 2:待认证 3:已认证 4:认证不通过)
    private Integer licenseReviewStatus;
    //初次驾照认证通过时间
    private Date licenseFirstAuthTime;
    //驾照提交渠道
    private String licenseSubmitAppkey;
    //驾照文件类别 1电子驾照  2纸质驾照
    private Integer licenseImgType;

    // app5.8 增加冻结押金余额字段
    private BigDecimal freezeDeposit;

    // app2.0擎路  二级渠道appkey
    private String secondAppKey;
    // 平台id
    private Long platformId ;

    // 手机厂商
    private String manufacturer;

    private int isFictional;

    public int getIsFictional() {
        return isFictional;
    }

    public void setIsFictional(int isFictional) {
        this.isFictional = isFictional;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getSecondAppKey() {
        return secondAppKey;
    }

    public void setSecondAppKey(String secondAppKey) {
        this.secondAppKey = secondAppKey;
    }

    public Long getPlatformId() {
        return platformId;
    }

    public void setPlatformId(Long platformId) {
        this.platformId = platformId;
    }

    public BigDecimal getFreezeDeposit() {
        return freezeDeposit;
    }

    public void setFreezeDeposit(BigDecimal freezeDeposit) {
        this.freezeDeposit = freezeDeposit;
    }

    public String getLicenseSubmitAppkey() {
        return licenseSubmitAppkey;
    }

    public void setLicenseSubmitAppkey(String licenseSubmitAppkey) {
        this.licenseSubmitAppkey = licenseSubmitAppkey;
    }

    public Integer getLicenseImgType() {
        return licenseImgType;
    }

    public void setLicenseImgType(Integer licenseImgType) {
        this.licenseImgType = licenseImgType;
    }

    public Date getIdentityFirstAuthTime() {
        return identityFirstAuthTime;
    }

    public void setIdentityFirstAuthTime(Date identityFirstAuthTime) {
        this.identityFirstAuthTime = identityFirstAuthTime;
    }

    public Integer getLicenseReviewStatus() {
        return licenseReviewStatus;
    }

    public void setLicenseReviewStatus(Integer licenseReviewStatus) {
        this.licenseReviewStatus = licenseReviewStatus;
    }

    public Date getLicenseFirstAuthTime() {
        return licenseFirstAuthTime;
    }

    public void setLicenseFirstAuthTime(Date licenseFirstAuthTime) {
        this.licenseFirstAuthTime = licenseFirstAuthTime;
    }

    public Long getIdentityId() {
        return identityId;
    }

    public void setIdentityId(Long identityId) {
        this.identityId = identityId;
    }

    public String getPassportNo() {
        return passportNo;
    }

    public void setPassportNo(String passportNo) {
        this.passportNo = passportNo;
    }

    public String getFileNo() {
        return fileNo;
    }

    public void setFileNo(String fileNo) {
        this.fileNo = fileNo;
    }

    public String getFileNoImgUrl() {
        return fileNoImgUrl;
    }

    public void setFileNoImgUrl(String fileNoImgUrl) {
        this.fileNoImgUrl = fileNoImgUrl;
    }

    public Integer getIdType() {
        return idType;
    }

    public void setIdType(Integer idType) {
        this.idType = idType;
    }

    public String getUnRegisterTime() {
        return unRegisterTime;
    }

    public void setUnRegisterTime(String unRegisterTime) {
        this.unRegisterTime = unRegisterTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.pk_id
     *
     * @return the value of membership_info.pk_id
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public Long getPkId() {
        return pkId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.pk_id
     *
     * @param pkId the value for membership_info.pk_id
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setPkId(Long pkId) {
        this.pkId = pkId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.AUTH_ID
     *
     * @return the value of membership_info.AUTH_ID
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getAuthId() {
        return authId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.AUTH_ID
     *
     * @param authId the value for membership_info.AUTH_ID
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setAuthId(String authId) {
        this.authId = authId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.DRIVER_CODE
     *
     * @return the value of membership_info.DRIVER_CODE
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getDriverCode() {
        return driverCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.DRIVER_CODE
     *
     * @param driverCode the value for membership_info.DRIVER_CODE
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setDriverCode(String driverCode) {
        this.driverCode = driverCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.OPEN_ID
     *
     * @return the value of membership_info.OPEN_ID
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getOpenId() {
        return openId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.OPEN_ID
     *
     * @param openId the value for membership_info.OPEN_ID
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setOpenId(String openId) {
        this.openId = openId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.NAME
     *
     * @return the value of membership_info.NAME
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.NAME
     *
     * @param name the value for membership_info.NAME
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.PASSWORD
     *
     * @return the value of membership_info.PASSWORD
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getPassword() {
        return password;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.PASSWORD
     *
     * @param password the value for membership_info.PASSWORD
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setPassword(String password) {
        this.password = password;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.GENDER
     *
     * @return the value of membership_info.GENDER
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getGender() {
        return gender;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.GENDER
     *
     * @param gender the value for membership_info.GENDER
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setGender(String gender) {
        this.gender = gender;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.MOBILE_PHONE
     *
     * @return the value of membership_info.MOBILE_PHONE
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getMobilePhone() {
        return mobilePhone;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.MOBILE_PHONE
     *
     * @param mobilePhone the value for membership_info.MOBILE_PHONE
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.MAIL
     *
     * @return the value of membership_info.MAIL
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getMail() {
        return mail;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.MAIL
     *
     * @param mail the value for membership_info.MAIL
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setMail(String mail) {
        this.mail = mail;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.BIRTH_DATE
     *
     * @return the value of membership_info.BIRTH_DATE
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getBirthDate() {
        return birthDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.BIRTH_DATE
     *
     * @param birthDate the value for membership_info.BIRTH_DATE
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setBirthDate(String birthDate) {
        this.birthDate = birthDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.REG_TIME
     *
     * @return the value of membership_info.REG_TIME
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getRegTime() {
        return regTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.REG_TIME
     *
     * @param regTime the value for membership_info.REG_TIME
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setRegTime(String regTime) {
        this.regTime = regTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.ZIP
     *
     * @return the value of membership_info.ZIP
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getZip() {
        return zip;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.ZIP
     *
     * @param zip the value for membership_info.ZIP
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setZip(String zip) {
        this.zip = zip;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.ADDRESS
     *
     * @return the value of membership_info.ADDRESS
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getAddress() {
        return address;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.ADDRESS
     *
     * @param address the value for membership_info.ADDRESS
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setAddress(String address) {
        this.address = address;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.PROVINCE
     *
     * @return the value of membership_info.PROVINCE
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getProvince() {
        return province;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.PROVINCE
     *
     * @param province the value for membership_info.PROVINCE
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setProvince(String province) {
        this.province = province;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.CITY
     *
     * @return the value of membership_info.CITY
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getCity() {
        return city;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.CITY
     *
     * @param city the value for membership_info.CITY
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setCity(String city) {
        this.city = city;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.AREA
     *
     * @return the value of membership_info.AREA
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getArea() {
        return area;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.AREA
     *
     * @param area the value for membership_info.AREA
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setArea(String area) {
        this.area = area;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.MEMBERSHIP_TYPE
     *
     * @return the value of membership_info.MEMBERSHIP_TYPE
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public Short getMembershipType() {
        return membershipType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.MEMBERSHIP_TYPE
     *
     * @param membershipType the value for membership_info.MEMBERSHIP_TYPE
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setMembershipType(Short membershipType) {
        this.membershipType = membershipType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.USER_LEVEL
     *
     * @return the value of membership_info.USER_LEVEL
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public Short getUserLevel() {
        return userLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.USER_LEVEL
     *
     * @param userLevel the value for membership_info.USER_LEVEL
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setUserLevel(Short userLevel) {
        this.userLevel = userLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.ORG_ID
     *
     * @return the value of membership_info.ORG_ID
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getOrgId() {
        return orgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.ORG_ID
     *
     * @param orgId the value for membership_info.ORG_ID
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.AGENCY_ID
     *
     * @return the value of membership_info.AGENCY_ID
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getAgencyId() {
        return agencyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.AGENCY_ID
     *
     * @param agencyId the value for membership_info.AGENCY_ID
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setAgencyId(String agencyId) {
        this.agencyId = agencyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.AUTH_KIND
     *
     * @return the value of membership_info.AUTH_KIND
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getAuthKind() {
        return authKind;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.AUTH_KIND
     *
     * @param authKind the value for membership_info.AUTH_KIND
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setAuthKind(String authKind) {
        this.authKind = authKind;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.CERTIFICATE_VALIDITY
     *
     * @return the value of membership_info.CERTIFICATE_VALIDITY
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getCertificateValidity() {
        return certificateValidity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.CERTIFICATE_VALIDITY
     *
     * @param certificateValidity the value for membership_info.CERTIFICATE_VALIDITY
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setCertificateValidity(String certificateValidity) {
        this.certificateValidity = certificateValidity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.CERTIFICATE_ADDRESS
     *
     * @return the value of membership_info.CERTIFICATE_ADDRESS
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getCertificateAddress() {
        return certificateAddress;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.CERTIFICATE_ADDRESS
     *
     * @param certificateAddress the value for membership_info.CERTIFICATE_ADDRESS
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setCertificateAddress(String certificateAddress) {
        this.certificateAddress = certificateAddress;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.DRIVING_LICENSE
     *
     * @return the value of membership_info.DRIVING_LICENSE
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getDrivingLicense() {
        return drivingLicense;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.DRIVING_LICENSE
     *
     * @param drivingLicense the value for membership_info.DRIVING_LICENSE
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setDrivingLicense(String drivingLicense) {
        this.drivingLicense = drivingLicense;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.OBTAIN_DRIVER_TIMER
     *
     * @return the value of membership_info.OBTAIN_DRIVER_TIMER
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getObtainDriverTimer() {
        return obtainDriverTimer;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.OBTAIN_DRIVER_TIMER
     *
     * @param obtainDriverTimer the value for membership_info.OBTAIN_DRIVER_TIMER
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setObtainDriverTimer(String obtainDriverTimer) {
        this.obtainDriverTimer = obtainDriverTimer;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.LICENSE_EXPIRATION_TIME
     *
     * @return the value of membership_info.LICENSE_EXPIRATION_TIME
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getLicenseExpirationTime() {
        return licenseExpirationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.LICENSE_EXPIRATION_TIME
     *
     * @param licenseExpirationTime the value for membership_info.LICENSE_EXPIRATION_TIME
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setLicenseExpirationTime(String licenseExpirationTime) {
        this.licenseExpirationTime = licenseExpirationTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.EMERGENCY_CONTACT
     *
     * @return the value of membership_info.EMERGENCY_CONTACT
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getEmergencyContact() {
        return emergencyContact;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.EMERGENCY_CONTACT
     *
     * @param emergencyContact the value for membership_info.EMERGENCY_CONTACT
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setEmergencyContact(String emergencyContact) {
        this.emergencyContact = emergencyContact;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.EMERGENCY_MOBIL
     *
     * @return the value of membership_info.EMERGENCY_MOBIL
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getEmergencyMobil() {
        return emergencyMobil;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.EMERGENCY_MOBIL
     *
     * @param emergencyMobil the value for membership_info.EMERGENCY_MOBIL
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setEmergencyMobil(String emergencyMobil) {
        this.emergencyMobil = emergencyMobil;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.GUARANTEE_NAME
     *
     * @return the value of membership_info.GUARANTEE_NAME
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getGuaranteeName() {
        return guaranteeName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.GUARANTEE_NAME
     *
     * @param guaranteeName the value for membership_info.GUARANTEE_NAME
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setGuaranteeName(String guaranteeName) {
        this.guaranteeName = guaranteeName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.GUARANTEE_IC_CARD_KIND
     *
     * @return the value of membership_info.GUARANTEE_IC_CARD_KIND
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public Short getGuaranteeIcCardKind() {
        return guaranteeIcCardKind;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.GUARANTEE_IC_CARD_KIND
     *
     * @param guaranteeIcCardKind the value for membership_info.GUARANTEE_IC_CARD_KIND
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setGuaranteeIcCardKind(Short guaranteeIcCardKind) {
        this.guaranteeIcCardKind = guaranteeIcCardKind;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.GUARANTEE_IC_CARD
     *
     * @return the value of membership_info.GUARANTEE_IC_CARD
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getGuaranteeIcCard() {
        return guaranteeIcCard;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.GUARANTEE_IC_CARD
     *
     * @param guaranteeIcCard the value for membership_info.GUARANTEE_IC_CARD
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setGuaranteeIcCard(String guaranteeIcCard) {
        this.guaranteeIcCard = guaranteeIcCard;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.GUARANTEE_MOBIL
     *
     * @return the value of membership_info.GUARANTEE_MOBIL
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getGuaranteeMobil() {
        return guaranteeMobil;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.GUARANTEE_MOBIL
     *
     * @param guaranteeMobil the value for membership_info.GUARANTEE_MOBIL
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setGuaranteeMobil(String guaranteeMobil) {
        this.guaranteeMobil = guaranteeMobil;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.REVIEW_REASON
     *
     * @return the value of membership_info.REVIEW_REASON
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getReviewReason() {
        return reviewReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.REVIEW_REASON
     *
     * @param reviewReason the value for membership_info.REVIEW_REASON
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setReviewReason(String reviewReason) {
        this.reviewReason = reviewReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.REVIEW_STATUS
     *
     * @return the value of membership_info.REVIEW_STATUS
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public Short getReviewStatus() {
        return reviewStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.REVIEW_STATUS
     *
     * @param reviewStatus the value for membership_info.REVIEW_STATUS
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setReviewStatus(Short reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.CARD_NO
     *
     * @return the value of membership_info.CARD_NO
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getCardNo() {
        return cardNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.CARD_NO
     *
     * @param cardNo the value for membership_info.CARD_NO
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.CREDIT_NO
     *
     * @return the value of membership_info.CREDIT_NO
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getCreditNo() {
        return creditNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.CREDIT_NO
     *
     * @param creditNo the value for membership_info.CREDIT_NO
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setCreditNo(String creditNo) {
        this.creditNo = creditNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.DATA_ORIGIN
     *
     * @return the value of membership_info.DATA_ORIGIN
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public Short getDataOrigin() {
        return dataOrigin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.DATA_ORIGIN
     *
     * @param dataOrigin the value for membership_info.DATA_ORIGIN
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setDataOrigin(Short dataOrigin) {
        this.dataOrigin = dataOrigin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.ID
     *
     * @return the value of membership_info.ID
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public BigDecimal getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.ID
     *
     * @param id the value for membership_info.ID
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setId(BigDecimal id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.STATUS
     *
     * @return the value of membership_info.STATUS
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public Short getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.STATUS
     *
     * @param status the value for membership_info.STATUS
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setStatus(Short status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.BLACKLIST_REASON
     *
     * @return the value of membership_info.BLACKLIST_REASON
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getBlacklistReason() {
        return blacklistReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.BLACKLIST_REASON
     *
     * @param blacklistReason the value for membership_info.BLACKLIST_REASON
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setBlacklistReason(String blacklistReason) {
        this.blacklistReason = blacklistReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.REVIEW_USER
     *
     * @return the value of membership_info.REVIEW_USER
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getReviewUser() {
        return reviewUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.REVIEW_USER
     *
     * @param reviewUser the value for membership_info.REVIEW_USER
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setReviewUser(String reviewUser) {
        this.reviewUser = reviewUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.REVIEW_ITEMS
     *
     * @return the value of membership_info.REVIEW_ITEMS
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getReviewItems() {
        return reviewItems;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.REVIEW_ITEMS
     *
     * @param reviewItems the value for membership_info.REVIEW_ITEMS
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setReviewItems(String reviewItems) {
        this.reviewItems = reviewItems;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.ILLEGAL_METHOD
     *
     * @return the value of membership_info.ILLEGAL_METHOD
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public Short getIllegalMethod() {
        return illegalMethod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.ILLEGAL_METHOD
     *
     * @param illegalMethod the value for membership_info.ILLEGAL_METHOD
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setIllegalMethod(Short illegalMethod) {
        this.illegalMethod = illegalMethod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.DEPOSIT
     *
     * @return the value of membership_info.DEPOSIT
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public BigDecimal getDeposit() {
        return deposit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.DEPOSIT
     *
     * @param deposit the value for membership_info.DEPOSIT
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setDeposit(BigDecimal deposit) {
        this.deposit = deposit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.RESERVE_AMOUNT
     *
     * @return the value of membership_info.RESERVE_AMOUNT
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public BigDecimal getReserveAmount() {
        return reserveAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.RESERVE_AMOUNT
     *
     * @param reserveAmount the value for membership_info.RESERVE_AMOUNT
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setReserveAmount(BigDecimal reserveAmount) {
        this.reserveAmount = reserveAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.RENT_MINS
     *
     * @return the value of membership_info.RENT_MINS
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public BigDecimal getRentMins() {
        return rentMins;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.RENT_MINS
     *
     * @param rentMins the value for membership_info.RENT_MINS
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setRentMins(BigDecimal rentMins) {
        this.rentMins = rentMins;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.EXEMPT_DEPOSIT
     *
     * @return the value of membership_info.EXEMPT_DEPOSIT
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public Short getExemptDeposit() {
        return exemptDeposit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.EXEMPT_DEPOSIT
     *
     * @param exemptDeposit the value for membership_info.EXEMPT_DEPOSIT
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setExemptDeposit(Short exemptDeposit) {
        this.exemptDeposit = exemptDeposit;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.EMPNO
     *
     * @return the value of membership_info.EMPNO
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getEmpno() {
        return empno;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.EMPNO
     *
     * @param empno the value for membership_info.EMPNO
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setEmpno(String empno) {
        this.empno = empno;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.INFO_ORIGIN
     *
     * @return the value of membership_info.INFO_ORIGIN
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getInfoOrigin() {
        return infoOrigin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.INFO_ORIGIN
     *
     * @param infoOrigin the value for membership_info.INFO_ORIGIN
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setInfoOrigin(String infoOrigin) {
        this.infoOrigin = infoOrigin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.CREATED_TIME
     *
     * @return the value of membership_info.CREATED_TIME
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getCreatedTime() {
        return createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.CREATED_TIME
     *
     * @param createdTime the value for membership_info.CREATED_TIME
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.CREATED_USER
     *
     * @return the value of membership_info.CREATED_USER
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getCreatedUser() {
        return createdUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.CREATED_USER
     *
     * @param createdUser the value for membership_info.CREATED_USER
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setCreatedUser(String createdUser) {
        this.createdUser = createdUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.UPDATED_TIME
     *
     * @return the value of membership_info.UPDATED_TIME
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getUpdatedTime() {
        return updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.UPDATED_TIME
     *
     * @param updatedTime the value for membership_info.UPDATED_TIME
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setUpdatedTime(String updatedTime) {
        this.updatedTime = updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.UPDATED_USER
     *
     * @return the value of membership_info.UPDATED_USER
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getUpdatedUser() {
        return updatedUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.UPDATED_USER
     *
     * @param updatedUser the value for membership_info.UPDATED_USER
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setUpdatedUser(String updatedUser) {
        this.updatedUser = updatedUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.PERSONNEL_STATE
     *
     * @return the value of membership_info.PERSONNEL_STATE
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public Short getPersonnelState() {
        return personnelState;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.PERSONNEL_STATE
     *
     * @param personnelState the value for membership_info.PERSONNEL_STATE
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setPersonnelState(Short personnelState) {
        this.personnelState = personnelState;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.SHARE_UID
     *
     * @return the value of membership_info.SHARE_UID
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getShareUid() {
        return shareUid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.SHARE_UID
     *
     * @param shareUid the value for membership_info.SHARE_UID
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setShareUid(String shareUid) {
        this.shareUid = shareUid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.EZBIKE
     *
     * @return the value of membership_info.EZBIKE
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public Short getEzbike() {
        return ezbike;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.EZBIKE
     *
     * @param ezbike the value for membership_info.EZBIKE
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setEzbike(Short ezbike) {
        this.ezbike = ezbike;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.APPLY_STATUS
     *
     * @return the value of membership_info.APPLY_STATUS
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public Short getApplyStatus() {
        return applyStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.APPLY_STATUS
     *
     * @param applyStatus the value for membership_info.APPLY_STATUS
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setApplyStatus(Short applyStatus) {
        this.applyStatus = applyStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.SERVICE_VER
     *
     * @return the value of membership_info.SERVICE_VER
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getServiceVer() {
        return serviceVer;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.SERVICE_VER
     *
     * @param serviceVer the value for membership_info.SERVICE_VER
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setServiceVer(String serviceVer) {
        this.serviceVer = serviceVer;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.SERVICE_VER_TIME
     *
     * @return the value of membership_info.SERVICE_VER_TIME
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getServiceVerTime() {
        return serviceVerTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.SERVICE_VER_TIME
     *
     * @param serviceVerTime the value for membership_info.SERVICE_VER_TIME
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setServiceVerTime(String serviceVerTime) {
        this.serviceVerTime = serviceVerTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.APP_KEY
     *
     * @return the value of membership_info.APP_KEY
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getAppKey() {
        return appKey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.APP_KEY
     *
     * @param appKey the value for membership_info.APP_KEY
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.TYPE_FLAG
     *
     * @return the value of membership_info.TYPE_FLAG
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public Short getTypeFlag() {
        return typeFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.TYPE_FLAG
     *
     * @param typeFlag the value for membership_info.TYPE_FLAG
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setTypeFlag(Short typeFlag) {
        this.typeFlag = typeFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.INVOICED_AMOUNT
     *
     * @return the value of membership_info.INVOICED_AMOUNT
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public BigDecimal getInvoicedAmount() {
        return invoicedAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.INVOICED_AMOUNT
     *
     * @param invoicedAmount the value for membership_info.INVOICED_AMOUNT
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setInvoicedAmount(BigDecimal invoicedAmount) {
        this.invoicedAmount = invoicedAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.CHANNEL_ID
     *
     * @return the value of membership_info.CHANNEL_ID
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getChannelId() {
        return channelId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.CHANNEL_ID
     *
     * @param channelId the value for membership_info.CHANNEL_ID
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.REGIONID
     *
     * @return the value of membership_info.REGIONID
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public Long getRegionid() {
        return regionid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.REGIONID
     *
     * @param regionid the value for membership_info.REGIONID
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setRegionid(Long regionid) {
        this.regionid = regionid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.deposit_vehicle
     *
     * @return the value of membership_info.deposit_vehicle
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public BigDecimal getDepositVehicle() {
        return depositVehicle;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.deposit_vehicle
     *
     * @param depositVehicle the value for membership_info.deposit_vehicle
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setDepositVehicle(BigDecimal depositVehicle) {
        this.depositVehicle = depositVehicle;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.province_of_origin
     *
     * @return the value of membership_info.province_of_origin
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getProvinceOfOrigin() {
        return provinceOfOrigin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.province_of_origin
     *
     * @param provinceOfOrigin the value for membership_info.province_of_origin
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setProvinceOfOrigin(String provinceOfOrigin) {
        this.provinceOfOrigin = provinceOfOrigin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.city_of_origin
     *
     * @return the value of membership_info.city_of_origin
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getCityOfOrigin() {
        return cityOfOrigin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.city_of_origin
     *
     * @param cityOfOrigin the value for membership_info.city_of_origin
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setCityOfOrigin(String cityOfOrigin) {
        this.cityOfOrigin = cityOfOrigin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.area_of_origin
     *
     * @return the value of membership_info.area_of_origin
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getAreaOfOrigin() {
        return areaOfOrigin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.area_of_origin
     *
     * @param areaOfOrigin the value for membership_info.area_of_origin
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setAreaOfOrigin(String areaOfOrigin) {
        this.areaOfOrigin = areaOfOrigin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.idcard_pic_url
     *
     * @return the value of membership_info.idcard_pic_url
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getIdcardPicUrl() {
        return idcardPicUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.idcard_pic_url
     *
     * @param idcardPicUrl the value for membership_info.idcard_pic_url
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setIdcardPicUrl(String idcardPicUrl) {
        this.idcardPicUrl = idcardPicUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.hold_idcard_pic_url
     *
     * @return the value of membership_info.hold_idcard_pic_url
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getHoldIdcardPicUrl() {
        return holdIdcardPicUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.hold_idcard_pic_url
     *
     * @param holdIdcardPicUrl the value for membership_info.hold_idcard_pic_url
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setHoldIdcardPicUrl(String holdIdcardPicUrl) {
        this.holdIdcardPicUrl = holdIdcardPicUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.point
     *
     * @return the value of membership_info.point
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public Integer getPoint() {
        return point;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.point
     *
     * @param point the value for membership_info.point
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setPoint(Integer point) {
        this.point = point;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.REVIEW_TIME
     *
     * @return the value of membership_info.REVIEW_TIME
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getReviewTime() {
        return reviewTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.REVIEW_TIME
     *
     * @param reviewTime the value for membership_info.REVIEW_TIME
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setReviewTime(String reviewTime) {
        this.reviewTime = reviewTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.APP_REVIEW_TIME
     *
     * @return the value of membership_info.APP_REVIEW_TIME
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getAppReviewTime() {
        return appReviewTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.APP_REVIEW_TIME
     *
     * @param appReviewTime the value for membership_info.APP_REVIEW_TIME
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setAppReviewTime(String appReviewTime) {
        this.appReviewTime = appReviewTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.REVIEW_REMARK
     *
     * @return the value of membership_info.REVIEW_REMARK
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getReviewRemark() {
        return reviewRemark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.REVIEW_REMARK
     *
     * @param reviewRemark the value for membership_info.REVIEW_REMARK
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setReviewRemark(String reviewRemark) {
        this.reviewRemark = reviewRemark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.REVIEW_ITEM_IDS
     *
     * @return the value of membership_info.REVIEW_ITEM_IDS
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getReviewItemIds() {
        return reviewItemIds;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.REVIEW_ITEM_IDS
     *
     * @param reviewItemIds the value for membership_info.REVIEW_ITEM_IDS
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setReviewItemIds(String reviewItemIds) {
        this.reviewItemIds = reviewItemIds;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.REVIEW_ITEM_NAME
     *
     * @return the value of membership_info.REVIEW_ITEM_NAME
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getReviewItemName() {
        return reviewItemName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.REVIEW_ITEM_NAME
     *
     * @param reviewItemName the value for membership_info.REVIEW_ITEM_NAME
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setReviewItemName(String reviewItemName) {
        this.reviewItemName = reviewItemName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.REVIEW_MODE
     *
     * @return the value of membership_info.REVIEW_MODE
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public Integer getReviewMode() {
        return reviewMode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.REVIEW_MODE
     *
     * @param reviewMode the value for membership_info.REVIEW_MODE
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setReviewMode(Integer reviewMode) {
        this.reviewMode = reviewMode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.FACE_RECOGNITION_IMG_URL
     *
     * @return the value of membership_info.FACE_RECOGNITION_IMG_URL
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getFaceRecognitionImgUrl() {
        return faceRecognitionImgUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.FACE_RECOGNITION_IMG_URL
     *
     * @param faceRecognitionImgUrl the value for membership_info.FACE_RECOGNITION_IMG_URL
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setFaceRecognitionImgUrl(String faceRecognitionImgUrl) {
        this.faceRecognitionImgUrl = faceRecognitionImgUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column membership_info.customer_id
     *
     * @return the value of membership_info.customer_id
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getCustomerId() {
        return customerId;
    }
    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.customer_id
     *
     * @param customerId the value for membership_info.customer_id
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.id_card_number
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public String getIdCardNumber() {
        return idCardNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column membership_info.id_card_number
     *
     * @param idCardNumber the value for membership_info.id_card_number
     *
     * @mbggenerated Mon Dec 04 15:27:31 CST 2017
     */
    public void setIdCardNumber(String idCardNumber) {
        this.idCardNumber = idCardNumber;
    }

    public Integer getAuthenticationStatus() {
        return authenticationStatus;
    }

    public void setAuthenticationStatus(Integer authenticationStatus) {
        this.authenticationStatus = authenticationStatus;
    }

    public String getNational() {
        return national;
    }

    public void setNational(String national) {
        this.national = national;
    }

    public String getDrivingLicenseType() {
        return drivingLicenseType;
    }

    public void setDrivingLicenseType(String drivingLicenseType) {
        this.drivingLicenseType = drivingLicenseType;
    }

    public Integer getDriverLicenseInputType() {
        return driverLicenseInputType;
    }

    public void setDriverLicenseInputType(Integer driverLicenseInputType) {
        this.driverLicenseInputType = driverLicenseInputType;
    }

    public Integer getLicenseAuthStatus() {
        return licenseAuthStatus;
    }

    public void setLicenseAuthStatus(Integer licenseAuthStatus) {
        this.licenseAuthStatus = licenseAuthStatus;
    }

    public Integer getLicenseElementsAuthStatus() {
        return licenseElementsAuthStatus;
    }

    public void setLicenseElementsAuthStatus(Integer licenseElementsAuthStatus) {
        this.licenseElementsAuthStatus = licenseElementsAuthStatus;
    }

    public String getLicenseStatusMsg() {
        return licenseStatusMsg;
    }

    public void setLicenseStatusMsg(String licenseStatusMsg) {
        this.licenseStatusMsg = licenseStatusMsg;
    }

    public String getElementsReviewItems() {
        return elementsReviewItems;
    }

    public void setElementsReviewItems(String elementsReviewItems) {
        this.elementsReviewItems = elementsReviewItems;
    }

    public Integer getAccountStatus() {
        return accountStatus;
    }

    public void setAccountStatus(Integer accountStatus) {
        this.accountStatus = accountStatus;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public Integer getNeedFace() {
        return needFace;
    }

    public void setNeedFace(Integer needFace) {
        this.needFace = needFace;
    }
}