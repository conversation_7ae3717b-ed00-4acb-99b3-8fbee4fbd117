<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- CardInfoMapper，，对应表card_info -->
<mapper namespace="com.extracme.evcard.membership.core.dao.CardInfoMapper">
    <!-- 返回结果集Map -->
    <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.CardInfo">
        <id column="CARD_NO" jdbcType="VARCHAR" property="cardNo" />
        <result column="CARD_NO" jdbcType="VARCHAR" property="cardNo" />
        <result column="INTERNAL_NO" jdbcType="VARCHAR" property="internalNo" />
        <result column="VALIDITY_TIME" jdbcType="VARCHAR" property="validityTime" />
        <result column="RW_KEYT_AA" jdbcType="VARCHAR" property="rwKeytAa" />
        <result column="RW_KEYT_BB" jdbcType="VARCHAR" property="rwKeytBb" />
        <result column="CARD_TYPE" jdbcType="DOUBLE" property="cardType" />
        <result column="ACTIVATE_STATUS" jdbcType="DOUBLE" property="activateStatus" />
        <result column="ACTIVATE_TIME" jdbcType="VARCHAR" property="activateTime" />
        <result column="CARD_NO_TYPE" jdbcType="INTEGER" property="cardNoType" />
        <result column="STATUS" jdbcType="VARCHAR" property="status" />
        <result column="CREATED_TIME" jdbcType="VARCHAR" property="createdTime" />
        <result column="CREATED_USER" jdbcType="VARCHAR" property="createdUser" />
        <result column="UPDATED_TIME" jdbcType="VARCHAR" property="updatedTime" />
        <result column="UPDATED_USER" jdbcType="VARCHAR" property="updatedUser" />
        <result column="AUTH_ID" jdbcType="VARCHAR" property="authId" />
    </resultMap>
    
    <!--数据列-->
    <sql id="Base_Column_List" >
            CARD_NO,
            INTERNAL_NO,
            VALIDITY_TIME,
            RW_KEYT_AA,
            RW_KEYT_BB,
            CARD_TYPE,
            ACTIVATE_STATUS,
            ACTIVATE_TIME,
            CARD_NO_TYPE,
            STATUS,
            CREATED_TIME,
            CREATED_USER,
            UPDATED_TIME,
            UPDATED_USER,
            AUTH_ID
    </sql>

    <insert id="addCardInfo" parameterType="com.extracme.evcard.membership.core.model.CardInfo">
        insert into ${siacSchema}.card_info (
            CARD_NO,
            INTERNAL_NO,
            VALIDITY_TIME,
            RW_KEYT_AA,
            RW_KEYT_BB,
            CARD_TYPE,
            ACTIVATE_STATUS,
            CARD_NO_TYPE,
            STATUS,
            CREATED_TIME,
            CREATED_USER,
            UPDATED_TIME,
            AUTH_ID
        ) values (
            #{cardNo,jdbcType=VARCHAR},
            #{internalNo,jdbcType=VARCHAR},
            #{validityTime,jdbcType=VARCHAR},
            #{rwKeytAa,jdbcType=VARCHAR},
            #{rwKeytBb,jdbcType=VARCHAR},
            #{cardType,jdbcType=DOUBLE},
            #{activateStatus,jdbcType=DOUBLE},
            #{cardNoType,jdbcType=INTEGER},
            #{status,jdbcType=VARCHAR},
            #{createdTime,jdbcType=VARCHAR},
            #{createdUser,jdbcType=VARCHAR},
            #{updatedTime,jdbcType=VARCHAR},
            #{authId,jdbcType=VARCHAR}
        )
    </insert>

    <select id="getCardInfo" parameterType="string" resultType="com.extracme.evcard.membership.core.model.CardInfo">
        SELECT
        CARD_NO as "cardNo",  <!-- 会员卡号 -->
        INTERNAL_NO as "internalNo",  <!-- 卡内部编号 -->
        AUTH_ID as "authId",  <!-- 会员卡号 -->
        VALIDITY_TIME   as "validityTime",  <!-- 有效期(yyyymmdd) -->
        RW_KEYT_AA  as "rwKeytAa",  <!-- 读写密钥AA 16进制转文本 -->
        RW_KEYT_BB  as "rwKeytBb",  <!-- 读写密钥BB 16进制转文本 -->
        CARD_TYPE   as "cardType",  <!-- 会员卡物理类型 -->
        ACTIVATE_STATUS as "activateStatus",  <!-- 卡激活状态  0:未激活 1:已激活 2:失效 3:注销 -->
        ACTIVATE_TIME   as "activateTime",  <!-- 卡激活时间 -->
        STATUS  as "status",  <!-- 状态(0：有效  1：无效) -->
        CREATED_TIME    as "createdTime",  <!-- 创建时间 -->
        CREATED_USER    as "createdUser",  <!-- 创建用户 -->
        UPDATED_TIME    as "updatedTime",  <!-- 更新时间 -->
        UPDATED_USER    as "updatedUser" <!-- 更新用户 -->
        FROM ${siacSchema}.CARD_INFO WHERE 1=1 and CARD_NO=#{cardNo}
    </select>

    <select id="queryInternalNo" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT IFNULL(SEQ, 0)  FROM ${siacSchema}.CARD_SEQ WHERE USERTYPE = #{condition.userType}
    </select>

    <update id="updateCardSeq" parameterType="java.util.Map">
        UPDATE ${siacSchema}.CARD_SEQ
        SET
        SEQ = #{condition.newSeq}
        WHERE
        USERTYPE = #{condition.userType}
    </update>
    
    <update id="updateCardStatusByCardNo" parameterType="com.extracme.evcard.membership.core.model.CardInfo">
		UPDATE ${siacSchema}.CARD_INFO
		<set>
			<if test="status != null">
				STATUS = #{status,jdbcType=VARCHAR},
			</if>
			<if test="updatedTime != null">
				UPDATED_TIME = #{updatedTime,jdbcType=VARCHAR},
			</if>
			<if test="updatedUser != null">
				UPDATED_USER = #{updatedUser,jdbcType=VARCHAR},
			</if>
		</set>
		WHERE CARD_NO = #{cardNo,jdbcType=VARCHAR} 
		AND AUTH_ID = #{authId,jdbcType=VARCHAR}
	</update>
    <update id="updateCardNo">
      UPDATE ${siacSchema}.CARD_INFO
      set
      STATUS = #{status},
      ACTIVATE_STATUS = #{activateStatus},
      AUTH_ID =#{authId},
      UPDATED_TIME =#{updatedTime},
      UPDATED_USER = #{updatedUser}
      where CARD_NO = #{cardNo}
    </update>

    <select id="getOrgCardStatusById" resultType="int">
        SELECT COUNT(1)
        FROM ${siacSchema}.ORG_CARD_MANAGE a,${siacSchema}.CARD_INFO b
        WHERE a.CARD_NO = b.CARD_NO and a.ORG_ID = #{orgId} and b.ACTIVATE_STATUS = 1 and b.STATUS = 0 and a.CARD_NO = #{cardNo}
    </select>

    <select id="queryCardInfoByAuthId" parameterType="string" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List"/>
        FROM ${siacSchema}.CARD_INFO
        WHERE AUTH_ID = #{authId,jdbcType=VARCHAR}
        order by CREATED_TIME desc
    </select>

    <select id="queryCardInfoHistoryList" resultType="com.extracme.evcard.membership.core.dto.CardInfoHistoryDTO">
        SELECT
        A.CARD_NO  as "cardNo",
        A.AUTH_ID  as "authId",
        T.INTERNAL_NO AS "internalNo",
        A.CARD_STATUS  as "cardStatus",
        T.CARD_NO_TYPE as "cardNoType",
        A.REMARK  as "remark",
        A.CREATED_TIME  as "createdTime",
        A.CREATED_USER  as "createdUser",
        A.UPDATED_TIME  as "updatedTime",
        A.UPDATED_USER  as "updatedUser"
        FROM ${siacSchema}.CARD_INFO_HISTROY A
        left join ${siacSchema}.CARD_INFO T on(A.CARD_NO = T.CARD_NO AND A.AUTH_ID = T.AUTH_ID)
        <where>
            <if test="authId != null">
                and  A.AUTH_ID = #{authId}
            </if>
            <if test="memberType != null">
                and  A.MEMBERSHIP_TYPE = #{memberType}
            </if>
            <if test="orderCondition != null">
                <foreach item="item" collection="orderCondition" index="index" open="order by "  separator=",">
                    #{item.columnName}
                    <if test="item.desc == true ">
                        desc
                    </if>
                </foreach>
            </if>
        </where>
        <if test="page != null">
            LIMIT #{page.offSet},#{page.limitSet}
        </if>
    </select>

    <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.core.model.CardInfo">
        update ${siacSchema}.card_info
        <set >
            <if test="internalNo != null" >
                INTERNAL_NO = #{internalNo,jdbcType=VARCHAR},
            </if>
            <if test="validityTime != null" >
                VALIDITY_TIME = #{validityTime,jdbcType=VARCHAR},
            </if>
            <if test="rwKeytAa != null" >
                RW_KEYT_AA = #{rwKeytAa,jdbcType=VARCHAR},
            </if>
            <if test="rwKeytBb != null" >
                RW_KEYT_BB = #{rwKeytBb,jdbcType=VARCHAR},
            </if>
            <if test="cardType != null" >
                CARD_TYPE = #{cardType,jdbcType=DOUBLE},
            </if>
            <if test="activateStatus != null" >
                ACTIVATE_STATUS = #{activateStatus,jdbcType=DOUBLE},
            </if>
            <if test="activateTime != null" >
                ACTIVATE_TIME = #{activateTime,jdbcType=VARCHAR},
            </if>
            <if test="cardNoType != null" >
                CARD_NO_TYPE = #{cardNoType,jdbcType=INTEGER},
            </if>
            <if test="status != null" >
                STATUS = #{status,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null" >
                CREATED_TIME = #{createdTime,jdbcType=VARCHAR},
            </if>
            <if test="createdUser != null" >
                CREATED_USER = #{createdUser,jdbcType=VARCHAR},
            </if>
            <if test="updatedTime != null" >
                UPDATED_TIME = #{updatedTime,jdbcType=VARCHAR},
            </if>
            <if test="updatedUser != null" >
                UPDATED_USER = #{updatedUser,jdbcType=VARCHAR},
            </if>
            <if test="authId != null" >
                AUTH_ID = #{authId,jdbcType=VARCHAR},
            </if>
        </set>
        where CARD_NO = #{cardNo,jdbcType=VARCHAR}
    </update>

    <insert id="insertSelective" parameterType="com.extracme.evcard.membership.core.model.CardInfo" >
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed Oct 18 10:48:41 CST 2017.
        -->
        insert into ${siacSchema}.card_info
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="cardNo != null" >
                CARD_NO,
            </if>
            <if test="internalNo != null" >
                INTERNAL_NO,
            </if>
            <if test="validityTime != null" >
                VALIDITY_TIME,
            </if>
            <if test="rwKeytAa != null" >
                RW_KEYT_AA,
            </if>
            <if test="rwKeytBb != null" >
                RW_KEYT_BB,
            </if>
            <if test="cardType != null" >
                CARD_TYPE,
            </if>
            <if test="activateStatus != null" >
                ACTIVATE_STATUS,
            </if>
            <if test="activateTime != null" >
                ACTIVATE_TIME,
            </if>
            <if test="cardNoType != null" >
                CARD_NO_TYPE,
            </if>
            <if test="status != null" >
                STATUS,
            </if>
            <if test="createdTime != null" >
                CREATED_TIME,
            </if>
            <if test="createdUser != null" >
                CREATED_USER,
            </if>
            <if test="updatedTime != null" >
                UPDATED_TIME,
            </if>
            <if test="updatedUser != null" >
                UPDATED_USER,
            </if>
            <if test="authId != null" >
                AUTH_ID,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="cardNo != null" >
                #{cardNo,jdbcType=VARCHAR},
            </if>
            <if test="internalNo != null" >
                #{internalNo,jdbcType=VARCHAR},
            </if>
            <if test="validityTime != null" >
                #{validityTime,jdbcType=VARCHAR},
            </if>
            <if test="rwKeytAa != null" >
                #{rwKeytAa,jdbcType=VARCHAR},
            </if>
            <if test="rwKeytBb != null" >
                #{rwKeytBb,jdbcType=VARCHAR},
            </if>
            <if test="cardType != null" >
                #{cardType,jdbcType=DOUBLE},
            </if>
            <if test="activateStatus != null" >
                #{activateStatus,jdbcType=DOUBLE},
            </if>
            <if test="activateTime != null" >
                #{activateTime,jdbcType=VARCHAR},
            </if>
            <if test="cardNoType != null" >
                #{cardNoType,jdbcType=INTEGER},
            </if>
            <if test="status != null" >
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null" >
                #{createdTime,jdbcType=VARCHAR},
            </if>
            <if test="createdUser != null" >
                #{createdUser,jdbcType=VARCHAR},
            </if>
            <if test="updatedTime != null" >
                #{updatedTime,jdbcType=VARCHAR},
            </if>
            <if test="updatedUser != null" >
                #{updatedUser,jdbcType=VARCHAR},
            </if>
            <if test="authId != null" >
                #{authId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="queryCardInfoByCondition" resultMap="BaseResultMap" parameterType="com.extracme.evcard.membership.core.input.QueryCardInfoConditionInput">
       select
       <include refid="Base_Column_List"/>
       from ${siacSchema}.card_info
       <where>
           <if test="cardNo != null">
               and CARD_NO = #{cardNo}
           </if>
           <if test="internalNo != null">
               and INTERNAL_NO = #{internalNo}
           </if>
       </where>
    </select>
    <select id="batchQueryInternalNo" resultType="map" parameterType="list">
        select
          CARD_NO as cardNo,
          INTERNAL_NO as internalNo
        from ${siacSchema}.card_info
        where CARD_NO in
        <foreach item="item" collection="list" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="countCardInfoHistoryList" resultType="integer">
        SELECT
         count(*)
        FROM ${siacSchema}.CARD_INFO_HISTROY A
        left join ${siacSchema}.CARD_INFO T on(A.CARD_NO = T.CARD_NO AND A.AUTH_ID = T.AUTH_ID)
        <where>
            <if test="authId != null">
                and  A.AUTH_ID = #{authId}
            </if>
            <if test="memberType != null">
                and  A.MEMBERSHIP_TYPE = #{memberType}
            </if>
        </where>
    </select>

    <select id="queryOrgCardList" resultType="com.extracme.evcard.membership.core.dto.QueryOrgCardInfoDto">
        SELECT
            A.CARD_NO AS cardNo,
            A.ORG_ID AS orgId,
            B.INTERNAL_NO AS internalNo,
            ${siacSchema}.GET_ORGCARDSTATUS (A.ORG_ID, A.CARD_NO) AS cardStatus
        FROM
            ${siacSchema}.ORG_CARD_MANAGE A,
            ${siacSchema}.CARD_INFO B
        WHERE
            A.CARD_NO = B.CARD_NO
        AND B.ACTIVATE_STATUS = 1
        AND B. STATUS = 0
        AND A.ORG_ID = #{orgId}
    </select>
</mapper>