package com.extracme.evcard.membership.core.service.auth;

import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.bean.ProducerBean;
import com.extracme.evcard.mq.bean.EventEnum;
import com.extracme.evcard.mq.bean.event.MemberUnRegister;
import com.extracme.evcard.protobuf.ProtobufUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.UUID;

@Slf4j
@Service
public class MembershipLifeCycle {


    @Value("${ons.raw.topic}")
    private String evcardRawDataTopic;
    @Resource(name = "producer")
    private ProducerBean producer;

    /**
     * 推送会员注销事件
     */
    public void unRegisterMemberMqPush(String mid) {
        try {
            MemberUnRegister memberRegister = new MemberUnRegister();
            memberRegister.setMid(mid);
            byte[] messageBody = ProtobufUtil.serializeProtobuf(memberRegister);
            String messageKey = "membership#" + UUID.randomUUID().toString().replace("-", "") + mid;
            Message msg = new Message(evcardRawDataTopic, EventEnum.MEMBER_UNREGISTER.getTag(), messageBody);
            msg.setKey(messageKey);
            producer.send(msg);
        } catch (Exception e) {
            log.error("推送会员注销事件失败，authId=" + mid, e);
        }
    }


}
