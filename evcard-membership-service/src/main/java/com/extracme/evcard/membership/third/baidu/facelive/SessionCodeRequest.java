package com.extracme.evcard.membership.third.baidu.facelive;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 当传参为1-1-3时，代表随机生成1-3个动作进行核验；
 * 当传参为1-1-1时，代表随机生成1个动作进行核验；
 * 当传参为1-2-2时，代表随机生成2个动作进行核验；
 * 当传参为1-3-3时，代表随机生成3个动作进行核验。
 */
@Data
public class SessionCodeRequest {

    /**
     * 0为语音验证和唇语验证步骤， 1为视频动作活体 默认0
     */
    private Integer type;

    /**
     * 当type=0时，语音和唇语生成的验证码最小长度：最大6 最小3 默认3
     * 当type=1时，视频动作活体 的验证码最小长度：最大3 最小1 默认1
     */
    @JSONField(name = "min_code_length")
    private Integer minCodeLength;

    /**
     * 当type=0时，语音和唇语生成的验证码最大长度：最大6 最小3 默认6
     * 当type=1时，视频动作活体 的验证码最大长度：最大3 最小1 默认3
     */
    @JSONField(name = "max_code_length")
    private Integer maxCodeLength;
}
