package com.extracme.evcard.membership.share.dao;

import com.extracme.evcard.membership.share.model.UserShareRewardInfo;

public interface UserShareRewardInfoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_share_reward_info
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    int deleteByPrimaryKey(Long userShareRewardInfoSeq);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_share_reward_info
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    int insert(UserShareRewardInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_share_reward_info
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    int insertSelective(UserShareRewardInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_share_reward_info
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    UserShareRewardInfo selectByPrimaryKey(Long userShareRewardInfoSeq);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_share_reward_info
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    int updateByPrimaryKeySelective(UserShareRewardInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_share_reward_info
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    int updateByPrimaryKey(UserShareRewardInfo record);
    
    /**
     * 邀请好友 被奖励次数.<br>
     * @param authId
     * @return
     */
    int shareRewardTimes(String authId);
}