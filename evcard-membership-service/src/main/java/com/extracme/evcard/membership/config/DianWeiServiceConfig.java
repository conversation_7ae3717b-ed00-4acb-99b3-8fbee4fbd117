package com.extracme.evcard.membership.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Data
public class DianWeiServiceConfig {
    @Value("${elements.authenticate.dianwei.product.verify}")
    private String productCodeVerify;

    @Value("${elements.authenticate.dianwei.product.get}")
    private String productCodeGet;

    @Value("${elements.authenticate.dianwei.subChannelName}")
    private String subChannelName; //环球车享
    @Value("${elements.authenticate.dianwei.enkey}")
    private String enkey;
    @Value("${elements.authenticate.dianwei.url}")
    private String url;
    @Value("${elements.authenticate.dianwei.channelId}")
    private String channelId;
    @Value("${elements.authenticate.dianwei.salt}")
    private String salt;
}
