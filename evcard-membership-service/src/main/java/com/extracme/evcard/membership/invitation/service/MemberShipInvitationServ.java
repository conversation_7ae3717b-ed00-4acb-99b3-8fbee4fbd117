
package com.extracme.evcard.membership.invitation.service;


import java.math.BigDecimal;
import java.util.*;

import javax.annotation.Resource;

import com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.OnExceptionContext;
import com.aliyun.openservices.ons.api.SendCallback;
import com.aliyun.openservices.ons.api.SendResult;
import com.aliyun.openservices.ons.api.bean.ProducerBean;
import com.aliyun.openservices.ons.api.exception.ONSClientException;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.dto.MemberInviteInfoDto;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.dao.UserMessageMapper;
import com.extracme.evcard.membership.credit.dto.UserMessageDto;
import com.extracme.evcard.mq.bean.EventEnum;
import com.extracme.evcard.mq.bean.event.MemberInvitation;
import com.extracme.evcard.protobuf.ProtobufUtil;
import com.extracme.evcard.rpc.coupon.dto.CommonAddRespDto;
import com.extracme.evcard.rpc.coupon.dto.OfferCouponDto;
import com.extracme.evcard.rpc.coupon.service.ICouponServ;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.messagepush.service.IMessagepushServ;

/**
 * Created by Elin on 2018/1/9.
 * 好友邀请
 */
@Service
public class MemberShipInvitationServ implements IMemberShipInvitationServ {
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    public static final String APP_KEY = "sgm_car_sharing";

    @Resource
    MembershipInfoMapper membershipInfoMapper;

    @Resource
    UserMessageMapper userMessageMapper;

    @Autowired
    ICouponServ couponServ;

    @Resource
    IMessagepushServ messagepushServ;
    @Resource(name = "producer")
    private ProducerBean producer;

    @Value("${ons.raw.topic}")
    private String evcardRawDataTopic;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Deprecated
    public BaseResponse giveAnewGiftBag(String authId, String mobilePhone, String optOrgId, String createUser) {
        log.debug("----------giveAnewGiftBag------" + authId + " , " + mobilePhone + "," + optOrgId + "," + createUser);
        if (StringUtils.isBlank(authId)) {
            return new BaseResponse(-1, "会员id不能为空");
        }
        if (StringUtils.isBlank(mobilePhone)) {
            return new BaseResponse(-1, "会员手机号不能为空");
        }
        /*if (StringUtils.isBlank(optOrgId)) {
            return new BaseResponse(-1, "操作人所属机构不能为空");
        }*/
        if (StringUtils.isBlank(createUser)) {
            return new BaseResponse(-1, "操作人不能为空");
        }
        String appKey = membershipInfoMapper.queryAppKey(authId);
        MembershipInfoWithBLOBs  shipInfo = membershipInfoMapper.selectByAuthId(authId,0);
        // 检测用户是否发过新手礼包
        //查询card_no为null的会员，并且没有发放新手礼包的会员，则再次发放新手礼包
        Integer num = couponServ.getUserCouponByAuthIdAndCouponName(authId, "新手礼包");
        if (num < 1) {
            int couponCnt = 0;// 优惠券数量
            /**
             * reachnow上线送券
             * 活动领券截止日期：2018-10-30
             */
            boolean bool = ComUtil.isActiveEnd("2018-10-30");
            if (!bool) {
                // 用户所属为成都
                Integer countCD = membershipInfoMapper.checkMemberShipAddressCD(authId);
                if (countCD > 0) {
                    /*
                     * 满720减360*2，券名称：ReachNow包日体验券
                     * 50元无门槛*1，券名称： ReachNow新用户体验券
                     */
                    offerReachnowCoupon(authId, createUser);
                    couponCnt = couponCnt + 3;
                }
            }
            /**
             * ERX5_huanqiu 上线送券
             * 活动日期：2017年12月15日-2018年2月28日
             */
            boolean boolERX5End = ComUtil.isActiveEnd("2018-02-28");
            boolean boolERX5Start = ComUtil.isActiveEnd("2017-12-07");
            if (!boolERX5End && boolERX5Start) {
                /*
                 * ERX5上线体验券
                 */
                offerERX5Coupon(authId, createUser);
                couponCnt = couponCnt + 1;

            }

            /**
             * 增加故宫活动新春优惠券 add by elin date 20180115
             * 到账之日起60天
             * 第一类：gugong_huanqiu 50*4
             * 第二类：gugong_jiaxing1、gugong_jiaxing2、gugong_jiaxing3、
             *        gugong_jiaxing4、gugong_jiaxing5、gugong_jiaxing6   50*6 、20*2
             * 第三类：gugong_suzhou1、gugong_suzhou2  50*6
             * 第四类：gugong_haikou(1-3)、gugong_kunming(1-4)、gugong_guiyang(1-3)、zhengzhou(1-5)、
             *        gugong_yancheng1、gugong_nanjing(1-4)、gugong_chengdu(1-4)、gugong_zhenjiang(1-5)、
             *        gugong_hefei(1-8)、gugong_qingdao(1-8)、gugong_changzhou1、gugong_guangzhou(1-3)、
             *        gugong_jiangshan（1-2)、gugong_huangshan(1-2)、gugong_chongqing1    50*4 、20*2
             */
            //updated 20180419 新手礼包已不发放，目前存在成都richnow优惠券 ，需判断是否有优惠券数量，不满足则不发放
            if(couponCnt != 0) {
                UserMessageDto userMessage = new UserMessageDto();
                userMessage.setAuthId(authId);
                userMessage.setMembershipType(BigDecimal.ZERO);
                userMessage.setMessageContent("您收到了" + couponCnt + "张优惠券，快到账户钱包中看看吧！");
                userMessage.setMessageTitle("优惠券到账通知");
                userMessage.setType(new BigDecimal(3));
                userMessage.setCreatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
                userMessage.setCreatedUser(createUser);
                userMessage.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
                userMessageMapper.addUserMessage(userMessage);

                String title = "优惠券发放提醒";
                Map<String, String> param = new HashMap<>(1);
                param.put("name", shipInfo.getName());
                param.put("couponNum", String.valueOf(couponCnt));
                if (!StringUtils.equals(appKey, APP_KEY)) {
                    messagepushServ.asyncSendSMSTemplate(mobilePhone,162,param,"member-rpc");
                    messagepushServ.push(authId, 0, 162, 2, 1, param, "member-rpc");
                }
            }
            log.debug("新手礼包发放成功" + authId + "--" + mobilePhone);
        }
        return new BaseResponse(0, "");
    }


    /**
     * 修改邀请好友事件推送，
     * @param authId 会员id
     * @param mobilePhone 会员手机号
     * @param optOrgId 操作人机构id
     * @param createUser 操作人
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse giveFriendInvitationGiftBag(String authId, String mobilePhone, String optOrgId, String createUser) {
        log.debug("----------giveFriendInvitationGiftBag------" + authId + " , " + mobilePhone + "," + optOrgId + "," + createUser);
        if (StringUtils.isBlank(authId)) {
            return new BaseResponse(-1, "会员id不能为空");
        }
        if (StringUtils.isBlank(mobilePhone)) {
            return new BaseResponse(-1, "会员手机号不能为空");
        }
        if (StringUtils.isBlank(optOrgId)) {
            return new BaseResponse(-1, "操作人所属机构不能为空");
        }
        if (StringUtils.isBlank(createUser)) {
            return new BaseResponse(-1, "操作人不能为空");
        }

        //1. 不通过邀请好友注册，无需推送好友邀请消息
        MemberInviteInfoDto memberInviteInfoDto = getMemberInviteInfo(authId, Short.valueOf("0"));
        if(memberInviteInfoDto == null) {
            return new BaseResponse();
        }
        //2. 推送邀请事件服务
        sendMemberInviteMessage(authId, mobilePhone, optOrgId, createUser);
        return new BaseResponse();
    }

    /**
     * 推送好友邀请消息
     */
    private void sendMemberInviteMessage(String authId, String mobilePhone, String optOrgId, String createUser) {
        try {
            MemberInvitation memberInvitation = new MemberInvitation();
            memberInvitation.setAuthId(authId);
            memberInvitation.setMobilePhone(mobilePhone);
            memberInvitation.setOptOrgId(optOrgId);
            memberInvitation.setCreateUser(createUser);
            memberInvitation.setCreatedTime(new Date());
            byte[] messageBody = ProtobufUtil.serializeProtobuf(memberInvitation);
            String messageKey = "membership#" + UUID.randomUUID().toString().replace("-", "") + mobilePhone;
            Message message = new Message(evcardRawDataTopic, EventEnum.MEMBER_INVITATION.getTag(), messageBody);
            message.setKey(messageKey);
            //消息推送
            producer.sendAsync(message, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    log.info("推送好友邀请事件成功, authId={}", authId);
                }
                @Override
                public void onException(OnExceptionContext onExceptionContext) {
                    ONSClientException exception = onExceptionContext.getException();
                    log.error("推送好友邀请事件失败，authId=" + authId, exception);
                    //尝试再试一次
                    SendResult sendResult = producer.send(message);
                    if(sendResult != null) {
                        log.info("再次推送好友邀请事件成功, authId={}", authId);
                    }
                }
            });
        } catch (Exception e) {
            log.info("推送好友邀请事件失败，authId={}", authId);
        }
    }

    @Override
    public MemberInviteInfoDto getMemberInviteInfo(String authId, Short membershipType) {
        return membershipInfoMapper.getUserInviteInfo(authId, membershipType);
    }

    /**
     * 添加ERX5上线体验券
     */
    private void offerERX5Coupon(String authId, String createUser) {
        // 插入ERX5上线体验券信息
        OfferCouponDto couponDto = new OfferCouponDto();
        // 模板id 40元优惠券满80使用 限制ERX5车型
        couponDto.setCouponSeq(10055);
        // 会员id
        couponDto.setAuthId(authId);
        // 被操作会员的组织ID
        couponDto.setOrgId("00");
        // 优惠券名称
        couponDto.setCouponName("ERX5上线体验券");
        // 开始时间
        couponDto.setStartDate(ComUtil.getSystemDate(ComUtil.DATE_TYPE5));
        // 过期时间
        couponDto.setExpiresDate(ComUtil.AddMonth(ComUtil.getSystemDate(ComUtil.DATE_TYPE5), ComUtil.DATE_TYPE5, 1));
        // 登陆系统的组织ID
        couponDto.setOptOrgId("00");
        // 操作人机构
        couponDto.setOptUser(createUser);
        CommonAddRespDto commonAddRespDto1 = couponServ.offerCoupon(couponDto);
        log.debug("10055:" + commonAddRespDto1.getCode() + "||" + commonAddRespDto1.getMessage());
    }


    /**
     * 添加reachnow专用优惠券
     */
    private void offerReachnowCoupon(String authId, String createUser) {
        // 插入reachnow优惠券信息
        OfferCouponDto couponDto = new OfferCouponDto();
        // 会员id
        couponDto.setAuthId(authId);
        // 被操作会员的组织ID
        couponDto.setOrgId("00");
        //优惠券有效期：发放日起30个自然日
        Calendar calendar = Calendar.getInstance();
        // 开始时间
        calendar.setTime(new Date());
        couponDto.setStartDate(DateFormatUtils.ISO_DATE_FORMAT.format(calendar));
        calendar.add(Calendar.DAY_OF_MONTH, 30);
        // 过期时间
        couponDto.setExpiresDate(DateFormatUtils.ISO_DATE_FORMAT.format(calendar));
        // 登陆系统的组织ID
        couponDto.setOptOrgId("00");
        // 操作人机构
        couponDto.setOptUser(createUser);
        //ReachNow体验券*1：50元无门槛，仅限ReachNow车型 模板id:231265
        couponDto.setCouponSeq(231265);
        // 优惠券名称
        couponDto.setCouponName("ReachNow体验券");
        CommonAddRespDto commonAddRespDto1 = couponServ.offerCoupon(couponDto);
        log.debug("231265:" + commonAddRespDto1.getCode() + "||" + commonAddRespDto1.getMessage());

        //ReachNow包日体验券*2：360元满减券，满720减360，仅限ReachNow车型 模板id：20603
        //update by elin 2018/03/28 ReachNow包日体验券*2：5折优惠券 最高可抵扣360元满减券，仅限ReachNow车型
        couponDto.setCouponSeq(10063);
        couponDto.setCouponName("ReachNow包日体验券");
        CommonAddRespDto commonAddRespDto3 = couponServ.offerCoupon(couponDto);
        log.debug("10063:" + commonAddRespDto3.getCode() + "||" + commonAddRespDto3.getMessage());
        commonAddRespDto3 = couponServ.offerCoupon(couponDto);
        log.debug("10063:" + commonAddRespDto3.getCode() + "||" + commonAddRespDto3.getMessage());
    }

}
