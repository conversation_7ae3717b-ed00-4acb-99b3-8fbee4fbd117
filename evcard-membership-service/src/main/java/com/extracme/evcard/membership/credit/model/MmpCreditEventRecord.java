package com.extracme.evcard.membership.credit.model;

import java.util.Date;

public class MmpCreditEventRecord {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_record.id
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_record.auth_id
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    private String authId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_record.order_seq
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    private String orderSeq;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_record.event_type_id
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    private Long eventTypeId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_record.event_desc
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    private String eventDesc;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_record.event_source
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    private String eventSource;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_record.event_name
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    private String eventName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_record.event_image_path
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    private String eventImagePath;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_record.event_file_path
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    private String eventFilePath;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_record.event_nature
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    private String eventNature;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_record.amount
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    private Integer amount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_record.black_list
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    private Boolean blackList;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_record.misc_desc
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    private String miscDesc;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_record.status
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_record.create_time
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_record.create_oper_id
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    private Long createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_record.create_oper_name
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    private String createOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_record.update_time
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_record.update_oper_id
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    private Long updateOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_credit_event_record.update_oper_name
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    private String updateOperName;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_record.id
     *
     * @return the value of mmp_credit_event_record.id
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_record.id
     *
     * @param id the value for mmp_credit_event_record.id
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_record.auth_id
     *
     * @return the value of mmp_credit_event_record.auth_id
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public String getAuthId() {
        return authId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_record.auth_id
     *
     * @param authId the value for mmp_credit_event_record.auth_id
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public void setAuthId(String authId) {
        this.authId = authId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_record.order_seq
     *
     * @return the value of mmp_credit_event_record.order_seq
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public String getOrderSeq() {
        return orderSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_record.order_seq
     *
     * @param orderSeq the value for mmp_credit_event_record.order_seq
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public void setOrderSeq(String orderSeq) {
        this.orderSeq = orderSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_record.event_type_id
     *
     * @return the value of mmp_credit_event_record.event_type_id
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public Long getEventTypeId() {
        return eventTypeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_record.event_type_id
     *
     * @param eventTypeId the value for mmp_credit_event_record.event_type_id
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public void setEventTypeId(Long eventTypeId) {
        this.eventTypeId = eventTypeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_record.event_desc
     *
     * @return the value of mmp_credit_event_record.event_desc
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public String getEventDesc() {
        return eventDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_record.event_desc
     *
     * @param eventDesc the value for mmp_credit_event_record.event_desc
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public void setEventDesc(String eventDesc) {
        this.eventDesc = eventDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_record.event_source
     *
     * @return the value of mmp_credit_event_record.event_source
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public String getEventSource() {
        return eventSource;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_record.event_source
     *
     * @param eventSource the value for mmp_credit_event_record.event_source
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public void setEventSource(String eventSource) {
        this.eventSource = eventSource;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_record.event_name
     *
     * @return the value of mmp_credit_event_record.event_name
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public String getEventName() {
        return eventName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_record.event_name
     *
     * @param eventName the value for mmp_credit_event_record.event_name
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_record.event_image_path
     *
     * @return the value of mmp_credit_event_record.event_image_path
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public String getEventImagePath() {
        return eventImagePath;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_record.event_image_path
     *
     * @param eventImagePath the value for mmp_credit_event_record.event_image_path
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public void setEventImagePath(String eventImagePath) {
        this.eventImagePath = eventImagePath;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_record.event_file_path
     *
     * @return the value of mmp_credit_event_record.event_file_path
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public String getEventFilePath() {
        return eventFilePath;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_record.event_file_path
     *
     * @param eventFilePath the value for mmp_credit_event_record.event_file_path
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public void setEventFilePath(String eventFilePath) {
        this.eventFilePath = eventFilePath;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_record.event_nature
     *
     * @return the value of mmp_credit_event_record.event_nature
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public String getEventNature() {
        return eventNature;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_record.event_nature
     *
     * @param eventNature the value for mmp_credit_event_record.event_nature
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public void setEventNature(String eventNature) {
        this.eventNature = eventNature;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_record.amount
     *
     * @return the value of mmp_credit_event_record.amount
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public Integer getAmount() {
        return amount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_record.amount
     *
     * @param amount the value for mmp_credit_event_record.amount
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_record.black_list
     *
     * @return the value of mmp_credit_event_record.black_list
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public Boolean getBlackList() {
        return blackList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_record.black_list
     *
     * @param blackList the value for mmp_credit_event_record.black_list
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public void setBlackList(Boolean blackList) {
        this.blackList = blackList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_record.misc_desc
     *
     * @return the value of mmp_credit_event_record.misc_desc
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public String getMiscDesc() {
        return miscDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_record.misc_desc
     *
     * @param miscDesc the value for mmp_credit_event_record.misc_desc
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_record.status
     *
     * @return the value of mmp_credit_event_record.status
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_record.status
     *
     * @param status the value for mmp_credit_event_record.status
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_record.create_time
     *
     * @return the value of mmp_credit_event_record.create_time
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_record.create_time
     *
     * @param createTime the value for mmp_credit_event_record.create_time
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_record.create_oper_id
     *
     * @return the value of mmp_credit_event_record.create_oper_id
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public Long getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_record.create_oper_id
     *
     * @param createOperId the value for mmp_credit_event_record.create_oper_id
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_record.create_oper_name
     *
     * @return the value of mmp_credit_event_record.create_oper_name
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_record.create_oper_name
     *
     * @param createOperName the value for mmp_credit_event_record.create_oper_name
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_record.update_time
     *
     * @return the value of mmp_credit_event_record.update_time
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_record.update_time
     *
     * @param updateTime the value for mmp_credit_event_record.update_time
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_record.update_oper_id
     *
     * @return the value of mmp_credit_event_record.update_oper_id
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public Long getUpdateOperId() {
        return updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_record.update_oper_id
     *
     * @param updateOperId the value for mmp_credit_event_record.update_oper_id
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_credit_event_record.update_oper_name
     *
     * @return the value of mmp_credit_event_record.update_oper_name
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public String getUpdateOperName() {
        return updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_credit_event_record.update_oper_name
     *
     * @param updateOperName the value for mmp_credit_event_record.update_oper_name
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }
}