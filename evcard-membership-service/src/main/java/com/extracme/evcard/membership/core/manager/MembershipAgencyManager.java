package com.extracme.evcard.membership.core.manager;

import com.extracme.evcard.membership.core.dao.AgencySecondAppKeyRelationMapper;
import com.extracme.evcard.membership.core.dto.OperatorDto;
import com.extracme.evcard.membership.core.model.AgencySecondAppKeyRelation;
import com.extracme.evcard.membership.core.service.IShortlinkManagementService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Slf4j
@Component
public class MembershipAgencyManager {

    @Value("${oss.web_url}")
    private String ossFilePath;
    @Value("${agency.qr.page:pages/storeOrder/index}")
    private String agencyQrPage;
    @Autowired
    private AgencySecondAppKeyRelationMapper relationMapper;

    @Autowired
    private IShortlinkManagementService shortlinkService;

    /**
     * 新增企业与二级渠道绑定关系，并生成对应的微信二维码
     *
     * @param agencyId     企业ID
     * @param secondAppKey 二级渠道Key
     * @param agencyRoleId 角色ID
     * @param operator     操作人信息
     * @return 绑定关系ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long createAgencySecondAppKeyRelationWithQrCode(String agencyId, String secondAppKey,
                                                           Long agencyRoleId, OperatorDto operator) {
        // 1. 构建实体并入库
        AgencySecondAppKeyRelation entity = new AgencySecondAppKeyRelation();
        entity.setAgencyId(agencyId);
        entity.setSecondAppKey(secondAppKey);
        entity.setAgencyRoleId(agencyRoleId);
        entity.setWechatQrPicUrl(""); // 先设置空
        entity.setIsDeleted(0); // 默认正常状态
        entity.setCreateOperId(operator.getOperatorId());
        entity.setCreateOperName(operator.getOperatorName());
        entity.setUpdateOperId(operator.getOperatorId());
        entity.setUpdateOperName(operator.getOperatorName());

        relationMapper.insert(entity);
        Long agencySecondAppKeyRelationId = entity.getId();

        // 2. 创建微信太阳码
        String dir = "/agency/wechat/order/" + agencyId + "/";
        String originalUrl = "?orderPlatform=" + secondAppKey + "&relationId=" + agencySecondAppKeyRelationId;
        String shortLinkName = agencyId + "-下单码";
        String wechatQrPicUrl = shortlinkService.createWxQrCodeRetry(shortLinkName, originalUrl, agencyQrPage, 1, dir, operator, 3);

        if (StringUtils.isBlank(wechatQrPicUrl)) {
            log.error("创建微信太阳码失败，agencyId={}, secondAppKey={}", agencyId, secondAppKey);
            throw new RuntimeException("创建微信太阳码失败");
        }

        wechatQrPicUrl = ossFilePath + wechatQrPicUrl;

        // 3. 更新微信太阳码URL
        AgencySecondAppKeyRelation updateEntity = new AgencySecondAppKeyRelation();
        updateEntity.setId(agencySecondAppKeyRelationId);
        updateEntity.setWechatQrPicUrl(wechatQrPicUrl);
        updateEntity.setUpdateTime(LocalDateTime.now());
        relationMapper.updateByPrimaryKeySelective(updateEntity);

        return agencySecondAppKeyRelationId;
    }
}
