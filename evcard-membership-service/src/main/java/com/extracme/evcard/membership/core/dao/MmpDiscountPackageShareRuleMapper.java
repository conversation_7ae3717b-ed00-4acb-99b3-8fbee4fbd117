package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.model.MmpDiscountPackageShareRule;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface MmpDiscountPackageShareRuleMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MmpDiscountPackageShareRule record);

    int insertSelective(MmpDiscountPackageShareRule record);

    MmpDiscountPackageShareRule selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MmpDiscountPackageShareRule record);

    int updateByPrimaryKey(MmpDiscountPackageShareRule record);

    /**
     * 根据时间查询当时生效的套餐不同享乐规则
     * @param agencyId
     * @param time
     * @return
     */
    MmpDiscountPackageShareRule selectLatestByTime(@Param("agencyId") String agencyId, @Param("time") Date time);

    int updateByLatestTimeLapse(Long id);
}