package com.extracme.evcard.membership.share.model;

import java.math.BigDecimal;

public class UserShareRewardInfo {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_share_reward_info.USER_SHARE_REWARD_INFO_SEQ
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    private Long userShareRewardInfoSeq;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_share_reward_info.AUTH_ID
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    private String authId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_share_reward_info.REWARD_TYPE
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    private String rewardType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_share_reward_info.REWARD_CONTENT
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    private String rewardContent;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_share_reward_info.EAMOUNT
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    private BigDecimal eamount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_share_reward_info.ORGIN_USERNAME
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    private String orginUsername;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_share_reward_info.ORGIN_MOBILE_PHONE
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    private String orginMobilePhone;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_share_reward_info.ORGIN_AUTH_ID
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    private String orginAuthId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_share_reward_info.CREATED_TIME
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    private String createdTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_share_reward_info.CREATED_USER
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    private String createdUser;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_share_reward_info.UPDATED_TIME
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    private String updatedTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_share_reward_info.UPDATED_USER
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    private String updatedUser;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_share_reward_info.USER_SHARE_REWARD_INFO_SEQ
     *
     * @return the value of user_share_reward_info.USER_SHARE_REWARD_INFO_SEQ
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    public Long getUserShareRewardInfoSeq() {
        return userShareRewardInfoSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_share_reward_info.USER_SHARE_REWARD_INFO_SEQ
     *
     * @param userShareRewardInfoSeq the value for user_share_reward_info.USER_SHARE_REWARD_INFO_SEQ
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    public void setUserShareRewardInfoSeq(Long userShareRewardInfoSeq) {
        this.userShareRewardInfoSeq = userShareRewardInfoSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_share_reward_info.AUTH_ID
     *
     * @return the value of user_share_reward_info.AUTH_ID
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    public String getAuthId() {
        return authId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_share_reward_info.AUTH_ID
     *
     * @param authId the value for user_share_reward_info.AUTH_ID
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    public void setAuthId(String authId) {
        this.authId = authId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_share_reward_info.REWARD_TYPE
     *
     * @return the value of user_share_reward_info.REWARD_TYPE
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    public String getRewardType() {
        return rewardType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_share_reward_info.REWARD_TYPE
     *
     * @param rewardType the value for user_share_reward_info.REWARD_TYPE
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    public void setRewardType(String rewardType) {
        this.rewardType = rewardType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_share_reward_info.REWARD_CONTENT
     *
     * @return the value of user_share_reward_info.REWARD_CONTENT
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    public String getRewardContent() {
        return rewardContent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_share_reward_info.REWARD_CONTENT
     *
     * @param rewardContent the value for user_share_reward_info.REWARD_CONTENT
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    public void setRewardContent(String rewardContent) {
        this.rewardContent = rewardContent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_share_reward_info.EAMOUNT
     *
     * @return the value of user_share_reward_info.EAMOUNT
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    public BigDecimal getEamount() {
        return eamount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_share_reward_info.EAMOUNT
     *
     * @param eamount the value for user_share_reward_info.EAMOUNT
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    public void setEamount(BigDecimal eamount) {
        this.eamount = eamount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_share_reward_info.ORGIN_USERNAME
     *
     * @return the value of user_share_reward_info.ORGIN_USERNAME
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    public String getOrginUsername() {
        return orginUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_share_reward_info.ORGIN_USERNAME
     *
     * @param orginUsername the value for user_share_reward_info.ORGIN_USERNAME
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    public void setOrginUsername(String orginUsername) {
        this.orginUsername = orginUsername;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_share_reward_info.ORGIN_MOBILE_PHONE
     *
     * @return the value of user_share_reward_info.ORGIN_MOBILE_PHONE
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    public String getOrginMobilePhone() {
        return orginMobilePhone;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_share_reward_info.ORGIN_MOBILE_PHONE
     *
     * @param orginMobilePhone the value for user_share_reward_info.ORGIN_MOBILE_PHONE
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    public void setOrginMobilePhone(String orginMobilePhone) {
        this.orginMobilePhone = orginMobilePhone;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_share_reward_info.ORGIN_AUTH_ID
     *
     * @return the value of user_share_reward_info.ORGIN_AUTH_ID
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    public String getOrginAuthId() {
        return orginAuthId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_share_reward_info.ORGIN_AUTH_ID
     *
     * @param orginAuthId the value for user_share_reward_info.ORGIN_AUTH_ID
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    public void setOrginAuthId(String orginAuthId) {
        this.orginAuthId = orginAuthId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_share_reward_info.CREATED_TIME
     *
     * @return the value of user_share_reward_info.CREATED_TIME
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    public String getCreatedTime() {
        return createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_share_reward_info.CREATED_TIME
     *
     * @param createdTime the value for user_share_reward_info.CREATED_TIME
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_share_reward_info.CREATED_USER
     *
     * @return the value of user_share_reward_info.CREATED_USER
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    public String getCreatedUser() {
        return createdUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_share_reward_info.CREATED_USER
     *
     * @param createdUser the value for user_share_reward_info.CREATED_USER
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    public void setCreatedUser(String createdUser) {
        this.createdUser = createdUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_share_reward_info.UPDATED_TIME
     *
     * @return the value of user_share_reward_info.UPDATED_TIME
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    public String getUpdatedTime() {
        return updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_share_reward_info.UPDATED_TIME
     *
     * @param updatedTime the value for user_share_reward_info.UPDATED_TIME
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    public void setUpdatedTime(String updatedTime) {
        this.updatedTime = updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_share_reward_info.UPDATED_USER
     *
     * @return the value of user_share_reward_info.UPDATED_USER
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    public String getUpdatedUser() {
        return updatedUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_share_reward_info.UPDATED_USER
     *
     * @param updatedUser the value for user_share_reward_info.UPDATED_USER
     *
     * @mbggenerated Sat Jan 20 20:50:20 CST 2018
     */
    public void setUpdatedUser(String updatedUser) {
        this.updatedUser = updatedUser;
    }
}