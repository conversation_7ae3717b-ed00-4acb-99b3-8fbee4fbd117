package com.extracme.evcard.membership.third.baidu.facelive;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class SessionCodeResult {

    /**
     * 随机校验码会话id，有效期5分钟，请提示用户在五分钟内完成全部操作
     * 验证码使用过即失效，每次使用视频活体前请重新拉取验证码
     */
    @JSONField(name = "session_id")
    private String sessionId;

    /**
     *  随机验证码，数字形式，1~6位数字；
     *  若为动作活体时，返回数字表示的动作对应关系为：0:眨眼 2:右转 3:左转 4:抬头 5:低头
     */
    private String code;
}
