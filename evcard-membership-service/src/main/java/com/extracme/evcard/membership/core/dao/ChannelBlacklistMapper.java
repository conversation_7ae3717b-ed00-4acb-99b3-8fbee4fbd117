package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.dto.blacklist.*;
import com.extracme.evcard.membership.core.model.ChannelBlacklist;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 渠道黑名单表
 */
public interface ChannelBlacklistMapper {

    List<ChannelBlacklistDto> listChannelBlacklist(ListChannelBlacklistDto dto);

    Integer countChannelBlacklist(ListChannelBlacklistDto dto);

    Integer insert(ChannelBlacklist channelBlacklist);

    Integer updateStatusById(ChannelBlacklist channelBlacklist);

    ChannelBlacklist getById(Integer id);

    List<ChannelBlacklist> queryByMobileOrCertificateNum(@Param("mobilePhone") String mobilePhone,
                                                         @Param("certificateNum") String certificateNum);

    List<ChannelBlacklist> queryByMobileAndCertificateNum(@Param("mobilePhone") String mobilePhone,
                                                         @Param("certificateNum") String certificateNum,
                                                          @Param("id") Integer id);
}
