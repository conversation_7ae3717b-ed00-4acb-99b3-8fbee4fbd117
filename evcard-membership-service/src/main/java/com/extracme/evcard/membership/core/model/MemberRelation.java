package com.extracme.evcard.membership.core.model;

import lombok.Data;

import java.util.Date;

@Data
public class MemberRelation {
    private Long id;

    //type1时 保存外部会员pkid
    private Long pkId1;
    //type1时 保存虚拟会员pkid
    private Long pkId2;

    //关联类型 1：虚拟用户和外部用户关联关系
    private Integer type;

    private Integer isDeleted;

    private Date createTime;

    private Long createOperId;

    private String createOperName;

    private Date updateTime;

    private Long updateOperId;

    private String updateOperName;

}