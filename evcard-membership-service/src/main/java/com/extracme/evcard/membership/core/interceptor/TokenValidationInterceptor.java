package com.extracme.evcard.membership.core.interceptor;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.core.dto.TokenValidationResult;
import com.extracme.evcard.membership.core.security.EnhancedTokenGenerator;
import com.extracme.evcard.membership.core.service.ITokenService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Map;

/**
 * Token验证拦截器
 * 用于验证JWT访问令牌
 * 
 * <AUTHOR>
 * @since 2025-08-20
 */
@Slf4j
@Component
public class TokenValidationInterceptor implements HandlerInterceptor {
    
    @Autowired
    private EnhancedTokenGenerator tokenGenerator;
    
    @Autowired
    private ITokenService tokenService;
    
    /**
     * Token在请求头中的键名
     */
    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String TOKEN_PREFIX = "Bearer ";
    
    /**
     * 用户信息在请求属性中的键名
     */
    public static final String USER_ID_ATTRIBUTE = "userId";
    public static final String USER_MID_ATTRIBUTE = "userMid";
    public static final String APP_KEY_ATTRIBUTE = "appKey";
    public static final String DEVICE_ID_ATTRIBUTE = "deviceId";
    public static final String DEVICE_TYPE_ATTRIBUTE = "deviceType";
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        
        // 跳过OPTIONS请求
        if ("OPTIONS".equalsIgnoreCase(request.getMethod())) {
            return true;
        }
        
        // 从请求头中获取token
        String token = extractTokenFromRequest(request);
        
        if (StringUtils.isBlank(token)) {
            return handleTokenError(response, "TOKEN_MISSING", "访问令牌缺失", 401);
        }
        
        // 验证token
        TokenValidationResult validationResult = tokenGenerator.validateJwtToken(token);
        
        if (!validationResult.isValid()) {
            return handleTokenError(response, validationResult.getErrorCode(), 
                                  validationResult.getErrorMessage(), 401);
        }
        
        // 检查token是否即将过期（在30分钟内过期）
        if (tokenGenerator.isTokenExpiringSoon(token, 30)) {
            // 在响应头中添加提示信息
            response.setHeader("X-Token-Expiring-Soon", "true");
            log.info("访问令牌即将过期，用户ID: {}", validationResult.getUserId());
        }
        
        // 将用户信息存储到请求属性中，供后续处理使用
        request.setAttribute(USER_ID_ATTRIBUTE, validationResult.getUserId());
        request.setAttribute(USER_MID_ATTRIBUTE, validationResult.getMid());
        request.setAttribute(APP_KEY_ATTRIBUTE, validationResult.getAppKey());
        request.setAttribute(DEVICE_ID_ATTRIBUTE, validationResult.getDeviceId());
        request.setAttribute(DEVICE_TYPE_ATTRIBUTE, validationResult.getDeviceType());
        
        // 异步更新最后活跃时间
        updateLastActiveTimeAsync(token);
        
        return true;
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 清理请求属性
        request.removeAttribute(USER_ID_ATTRIBUTE);
        request.removeAttribute(USER_MID_ATTRIBUTE);
        request.removeAttribute(APP_KEY_ATTRIBUTE);
        request.removeAttribute(DEVICE_ID_ATTRIBUTE);
        request.removeAttribute(DEVICE_TYPE_ATTRIBUTE);
    }
    
    /**
     * 从请求中提取token
     */
    private String extractTokenFromRequest(HttpServletRequest request) {
        // 优先从Authorization头中获取
        String authHeader = request.getHeader(AUTHORIZATION_HEADER);
        if (StringUtils.isNotBlank(authHeader) && authHeader.startsWith(TOKEN_PREFIX)) {
            return authHeader.substring(TOKEN_PREFIX.length());
        }
        
        // 从X-Access-Token头中获取
        String accessToken = request.getHeader("X-Access-Token");
        if (StringUtils.isNotBlank(accessToken)) {
            return accessToken;
        }
        
        // 从请求参数中获取（不推荐，但提供兼容性）
        String tokenParam = request.getParameter("access_token");
        if (StringUtils.isNotBlank(tokenParam)) {
            return tokenParam;
        }
        
        return null;
    }
    
    /**
     * 处理token错误
     */
    private boolean handleTokenError(HttpServletResponse response, String errorCode, String errorMessage, int httpStatus) {
        response.setStatus(httpStatus);
        response.setContentType("application/json;charset=UTF-8");
        
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("code", -1);
        errorResponse.put("message", errorMessage);
        errorResponse.put("errorCode", errorCode);
        errorResponse.put("timestamp", System.currentTimeMillis());
        
        try (PrintWriter writer = response.getWriter()) {
            writer.write(JSON.toJSONString(errorResponse));
            writer.flush();
        } catch (IOException e) {
            log.error("写入错误响应失败: {}", e.getMessage(), e);
        }
        
        log.warn("Token验证失败: {} - {}", errorCode, errorMessage);
        return false;
    }
    
    /**
     * 异步更新最后活跃时间
     */
    private void updateLastActiveTimeAsync(String token) {
        // 使用异步方式更新，避免影响主要业务流程
        new Thread(() -> {
            try {
                tokenService.updateLastActiveTime(token);
            } catch (Exception e) {
                log.warn("更新最后活跃时间失败: {}", e.getMessage());
            }
        }).start();
    }
    
    /**
     * 从请求中获取用户ID
     */
    public static Long getUserIdFromRequest(HttpServletRequest request) {
        Object userId = request.getAttribute(USER_ID_ATTRIBUTE);
        return userId instanceof Long ? (Long) userId : null;
    }
    
    /**
     * 从请求中获取用户MID
     */
    public static String getUserMidFromRequest(HttpServletRequest request) {
        Object userMid = request.getAttribute(USER_MID_ATTRIBUTE);
        return userMid instanceof String ? (String) userMid : null;
    }
    
    /**
     * 从请求中获取应用标识
     */
    public static String getAppKeyFromRequest(HttpServletRequest request) {
        Object appKey = request.getAttribute(APP_KEY_ATTRIBUTE);
        return appKey instanceof String ? (String) appKey : null;
    }
    
    /**
     * 从请求中获取设备ID
     */
    public static String getDeviceIdFromRequest(HttpServletRequest request) {
        Object deviceId = request.getAttribute(DEVICE_ID_ATTRIBUTE);
        return deviceId instanceof String ? (String) deviceId : null;
    }
    
    /**
     * 从请求中获取设备类型
     */
    public static String getDeviceTypeFromRequest(HttpServletRequest request) {
        Object deviceType = request.getAttribute(DEVICE_TYPE_ATTRIBUTE);
        return deviceType instanceof String ? (String) deviceType : null;
    }
}
