package com.extracme.evcard.membership.config;

import com.aliyun.openservices.ons.api.PropertyKeyConst;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

/**
 * <AUTHOR>
 * @Discription
 * @date 2021/12/10
 */
@Configuration
@Data
public class AliyunConfig {
    @Value("${regionId}")
    private String regionId;

    @Value("${ali.AccessKey}")
    private String accessKeyId;

    @Value("${ali.SecretKey}")
    private String accessKeySecret;

    @Value("${ons.addr}")
    private String onsAddr;

    @Value("${ons.gid}")
    private String onsGid;

    @Value("${ons.raw.topic}")
    private String onsRowDataTopic;

    @Value("${ons.credits.topic}")
    private String onsCreditsTopic;
    /**
     * 公网-ons地址
     */
    @Value("${ons.addr.public}")
    private String onsAddrPublic;

    /**
     * 公网-ons地址
     */
    @Value("${ons.gid.public}")
    private String onsGidPublic;

    /**
     * 公网-统计topic
     * TODO 确认统计相关已经不再推送
     */
    @Value("${ons.analysis.topic}")
    private String onsAnalysisTopic;


    @Value("${ons.md.contract.topic}")
    private String onsMdContractTopic;

    public Properties getOnsPropertie() {
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.AccessKey, this.accessKeyId);
        properties.setProperty(PropertyKeyConst.SecretKey, this.accessKeySecret);
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, this.onsAddr);
        return properties;
    }

    public Properties getOnsPublicPropertie() {
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.AccessKey, this.accessKeyId);
        properties.setProperty(PropertyKeyConst.SecretKey, this.accessKeySecret);
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, this.onsAddrPublic);
        return properties;
    }

    @Value("${afs.appCode}")
    private String afsAppCode;

    @Value("${afs.nameServer}")
    private String afsNameServer;


    @Value("${green.nameServer}")
    private String greenNameServer;

    public String getGreenEndpoints(){
        if("local".equals(regionId)){
            return "api.green.alibaba.com";
        }
        return String.format(greenNameServer, regionId);
    }

//    public static String getDomain(String regionId){
//        if("cn-shanghai".equals(regionId)){
//            return "green.cn-shanghai.aliyuncs.com";
//        }
//
//        if("cn-hangzhou".equals(regionId)){
//            return "green.cn-hangzhou.aliyuncs.com";
//        }
//
//        if("local".equals(regionId)){
//            return "api.green.alibaba.com";
//        }
//
//        return "green.cn-shanghai.aliyuncs.com";
//    }
}
