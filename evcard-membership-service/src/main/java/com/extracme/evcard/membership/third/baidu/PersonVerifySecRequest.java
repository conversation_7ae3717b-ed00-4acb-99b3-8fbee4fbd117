package com.extracme.evcard.membership.third.baidu;

import lombok.Data;

/**
 * 实名认证加强版
 */
@Data
public class PersonVerifySecRequest extends PersonVerifyRequest {

    /**
     * 采集SDK类型：
     * ios
     * android
     */
    private String app;

    public PersonVerifySecRequest toLogRequest() {
        PersonVerifySecRequest res = new PersonVerifySecRequest();
        res.setApp(this.getApp());
        res.setId_card_number(this.getId_card_number());
        res.setImage_type(this.getImage_type());
        res.setLiveness_control(this.getLiveness_control());
        res.setName(this.getName());
        res.setQuality_control(this.getQuality_control());
        res.setSpoofing_control(this.getSpoofing_control());
        return res;
    }

}
