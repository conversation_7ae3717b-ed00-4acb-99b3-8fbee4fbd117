package com.extracme.evcard.membership.core.model;

/**
 * 模型类，，对应表user_operator_log.
 */
public class UserOperatorLog {
    /** 日志SEQ (SEQ:LOG_SEQ) */
    private Long logSeq;
    /** 外键 */
    private String foreignKey;
    /** 操作内容 */
    private String operatorContent;
    /** 外键2 */
    private String foreignKey2;
    /** 创建时间 */
    private String createdTime;
    /** 创建用户 */
    private String createdUser;
    /** 日志SEQ (SEQ:LOG_SEQ) */
    public Long getLogSeq(){ 
        return logSeq;
    } 
    /** 日志SEQ (SEQ:LOG_SEQ) */
    public void setLogSeq(Long logSeq){ 
        this.logSeq = logSeq;
    }
    /** 外键 */
    public String getForeignKey(){ 
        return foreignKey;
    } 
    /** 外键 */
    public void setForeignKey(String foreignKey){ 
        this.foreignKey = foreignKey;
    }
    /** 操作内容 */
    public String getOperatorContent(){ 
        return operatorContent;
    } 
    /** 操作内容 */
    public void setOperatorContent(String operatorContent){ 
        this.operatorContent = operatorContent;
    }
    /** 外键2 */
    public String getForeignKey2(){ 
        return foreignKey2;
    } 
    /** 外键2 */
    public void setForeignKey2(String foreignKey2){ 
        this.foreignKey2 = foreignKey2;
    }
    /** 创建时间 */
    public String getCreatedTime(){ 
        return createdTime;
    } 
    /** 创建时间 */
    public void setCreatedTime(String createdTime){ 
        this.createdTime = createdTime;
    }
    /** 创建用户 */
    public String getCreatedUser(){ 
        return createdUser;
    } 
    /** 创建用户 */
    public void setCreatedUser(String createdUser){ 
        this.createdUser = createdUser;
    }

}