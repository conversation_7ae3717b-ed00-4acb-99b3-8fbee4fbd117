package com.extracme.evcard.membership.config;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * oss配置参数
 * <AUTHOR> @Discription
 * @date 2021/12/17
 * @remark 静态参数读取，后续调整oss工具类后可移除
 */
@Configuration
public class OssConfigUtil {
    private static String ENV;

    private static String OSS_BUCKET;

    private static String OSS_SENSITIVE_BUCKET;

    private static String OSS_ENDPOINT;

    private static String ROLE_ARN;

    private static String ROLE_SESSION_NAME;

    private static String ALI_ACCESS_ID;

    private static String ALI_ACCESS_KEY;

    private static String FILE_BASE_URL;

    private static String SENSITIVE_FILE_URL;

    private static String OSS_URL_PRE;

    @Value("${env}")
    public void setENV(String value) {
        ENV = value;
    }

    @Value("${oss.bucket}")
    public void setOSS_BUCKET(String value) {
        OSS_BUCKET = value;
    }

    @Value("${oss.bucket.sensitive}")
    public void setOSS_SENSITIVE_BUCKET(String value) {
        OSS_SENSITIVE_BUCKET = value;
    }

    @Value("${oss.endPoint}")
    public void setOSS_ENDPOINT(String value) {
        OSS_ENDPOINT = value;
    }

    @Value("${oss.roleArn}")
    public void setROLE_ARN(String value) {
        ROLE_ARN = value;
    }

    @Value("${oss.roleSessionName}")
    public void setROLE_SESSION_NAME(String value) {
        ROLE_SESSION_NAME = value;
    }

    @Value("${oss.accessKey}")
    public void setALI_ACCESS_ID(String value) {
        ALI_ACCESS_ID = value;
    }

    @Value("${oss.secretKey}")
    public void setALI_ACCESS_KEY(String value) {
        ALI_ACCESS_KEY = value;
    }

    @Value("${oss.web_url}")
    public void setFileBaseUrl(String value) {
        FILE_BASE_URL = value;
    }

    @Value("${oss.sensitive_web_url}")
    public void setSensitiveFileUrl(String value) {
        SENSITIVE_FILE_URL = value;
    }

    @Value("${oss.url.pre}")
    public void setOSS_URL_PRE(String value) {
        OSS_URL_PRE = value;
    }

    public static String getENV() {
        return ENV;
    }

    public static String getOssBucket() {
        return OSS_BUCKET;
    }

    public static String getOssSensitiveBucket() {
        return OSS_SENSITIVE_BUCKET;
    }

    public static String getOssUrlPre() {
        return OSS_URL_PRE;
    }

    public static String getOssEndpoint() {
        return OSS_ENDPOINT;
    }

    public static String getRoleArn() {
        return ROLE_ARN;
    }

    public static String getRoleSessionName() {
        return ROLE_SESSION_NAME;
    }

    public static String getAliAccessId() {
        return ALI_ACCESS_ID;
    }

    public static String getAliAccessKey() {
        return ALI_ACCESS_KEY;
    }

    public static String getFileBaseUrl() {
        return FILE_BASE_URL;
    }

    public static String getSensitiveFileUrl() {
        return SENSITIVE_FILE_URL;
    }
}
