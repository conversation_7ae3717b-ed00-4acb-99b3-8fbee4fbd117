package com.extracme.evcard.membership.job;

import com.alibaba.fastjson.JSON;
import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.extracme.evcard.elasticjob.ElasticSimpleJob;
import com.extracme.evcard.membership.core.dao.MemberRelationMapper;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.enums.MemberRelationTypeEnums;
import com.extracme.evcard.membership.core.model.MemberRelation;
import com.extracme.evcard.membership.core.service.auth.MemberRelationService;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.model.MembershipInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 建立渠道会员 和 外部会员 关联关系
 *
 * <AUTHOR>
 * @date 2023/9/2
 */
@Slf4j
@Component
@ElasticSimpleJob(jobName = "evcard-membership-buildChannelMemberRelation", cron = "0 28 19 19 6 ? 2099", description = "建立渠道会员关联关系任务", overwrite = true)
public class BuildChannelMemberRelationJob implements SimpleJob {

    @Autowired
    private MembershipInfoMapper membershipInfoMapper;
    @Resource
    private MemberRelationMapper memberRelationMapper;
    @Autowired
    private MemberRelationService memberRelationService;


    @Value("${build.relation.enable:1}")
    private String buildRelationEnable;

    @Value("${build.relation.start.id:0}")
    private String startId;

    @Value("${build.relation.limit.size:500}")
    private String limtSize;

    @Override
    public void execute(ShardingContext shardingContext) {
        log.info("BuildChannelMemberRelationJob 建立渠道会员绑定关系任务 start！shardingContext={}", JSON.toJSONString(shardingContext));
        long id = Long.valueOf(startId);
        List<MemberRelation> list = new ArrayList<>();
        Date now = new Date();
        while ("1".equals(buildRelationEnable)) {
            log.info("BuildChannelMemberRelationJob 建立渠道会员绑定关系任务 批量处理开始！id={}", id);
            int limit = Integer.valueOf(limtSize);

            List<MembershipInfo> channelMembers = membershipInfoMapper.getChannelMember(id, limit);
            if (CollectionUtils.isNotEmpty(channelMembers)) {
                for (MembershipInfo channelMember : channelMembers) {
                    try {
                        Long pkId2 = channelMember.getPkId();
                        String mobilePhone = channelMember.getMobilePhone();
                        MembershipBasicInfo membership = membershipInfoMapper.getMembershipByPhone(mobilePhone, 0);
                        if (membership != null) {
                            //判断是否 需要建立绑定关系
                            Long pkId1 = membership.getPkId();
                            if (needAdd(pkId1, pkId2)) {
                                MemberRelation memberRelationRecord = new MemberRelation();
                                memberRelationRecord.setPkId1(pkId1);
                                memberRelationRecord.setPkId2(pkId2);
                                memberRelationRecord.setType(MemberRelationTypeEnums.CHANNEL_AND_INNER_MEMBER_RELATION.getType());
                                memberRelationRecord.setCreateTime(now);
                                memberRelationRecord.setUpdateTime(now);
                                memberRelationRecord.setCreateOperId(pkId1);
                                memberRelationRecord.setUpdateOperId(pkId1);
                                memberRelationRecord.setCreateOperName("系统");
                                memberRelationRecord.setUpdateOperName("系统");
                                list.add(memberRelationRecord);
                                if (list.size() == limit) {
                                    memberRelationMapper.batchInsert(list);
                                    list = new ArrayList<>();
                                }
                            }
                        }

                    } catch (Exception e) {
                        log.error("BuildChannelMemberRelationJob 建立渠道会员绑定关系任务 异常，channelMember=[{}]", JSON.toJSONString(channelMember));
                    }
                }

                // 取最后一条记录id
                id = channelMembers.get(channelMembers.size() - 1).getPkId();
            } else {
                memberRelationMapper.batchInsert(list);
                list = new ArrayList<>();
                break;
            }
            log.info("BuildChannelMemberRelationJob 建立渠道会员绑定关系任务 批量处理结束！id={}", id);
        }
        log.info("BuildChannelMemberRelationJob 建立渠道会员绑定关系任务 end！shardingContext={}", JSON.toJSONString(shardingContext));
    }

    public boolean needAdd(Long pkid1, Long pkId2) {
        // 判断是否 绑定关系
        return memberRelationService.needAdd(pkid1,pkId2,MemberRelationTypeEnums.CHANNEL_AND_INNER_MEMBER_RELATION.getType());
        // 第一次执行 true，后续跑 放开上面注释

    }

}
