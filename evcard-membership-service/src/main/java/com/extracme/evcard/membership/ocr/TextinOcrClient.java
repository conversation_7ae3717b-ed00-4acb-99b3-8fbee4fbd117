package com.extracme.evcard.membership.ocr;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.enums.CommonRiskTypeEnums;
import com.extracme.evcard.membership.core.enums.OcrChannelEnums;
import com.extracme.evcard.membership.core.enums.OcrTypeEnums;
import com.extracme.evcard.membership.core.enums.TextinIdCardItemKeyEnums;
import com.extracme.evcard.membership.core.manager.MembershipOcrCallLogManager;
import com.extracme.evcard.membership.third.baidu.Base64Util;
import com.extracme.evcard.membership.third.baidu.FileUtil;
import com.extracme.evcard.membership.third.textin.TextinIdCardOcrItem;
import com.extracme.evcard.rpc.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;

/**
 * 上海生腾OCR
 *
 * <AUTHOR>
 * @date 2022/8/18
 */
@Slf4j
@Component
public class TextinOcrClient implements OcrService {
    @Value("${textin.ocr.url}")
    private String baseUrl;

    @Value("${textin.ocr.appId}")
    private String appId;

    @Value("${textin.ocr.secretCode}")
    private String secretCode;

    @Resource
    private MembershipOcrCallLogManager membershipOcrCallLogManager;

    @Override
    public String getChannel() {
        return "textin";
    }

    @Override
    public CommonIdCardOcrResp idCardOcr(CommonIdCardOcrReq req) {
        String url = baseUrl + "robot/v1.0/api/id_card?crop_image=0&head_portrait=0&id_number_image=0";
        String reqParams = "";
        String respParams = "";
        try {
            log.info("用户[{}]调用生腾的身份证识别接口[{}]，参数：{}", req.getMid(), url, JSON.toJSONString(req));
            // 读取图片
            byte[] imgData = getImgData(req.getUrl(), req.getImage());
            // 发送请求
            String result = doPost(url, imgData);
            log.info("用户[{}]调用生腾的身份证识别接口[{}]，结果：{}", req.getMid(), url, result);
            respParams = result;
            // 封装应答
            if (StringUtils.isNotBlank(result)) {
                CommonIdCardOcrResp resp = new CommonIdCardOcrResp();
                JSONObject jsonObject = JSONObject.parseObject(result);
                int code = jsonObject.getIntValue("code");
                JSONObject jsonObjectResult = jsonObject.getJSONObject("result");
                if (code == 200 && jsonObjectResult != null) {
                    JSONArray item_list = jsonObjectResult.getJSONArray("item_list");
                    if (item_list != null && !item_list.isEmpty()) {
                        List<TextinIdCardOcrItem> itemList = item_list.toJavaList(TextinIdCardOcrItem.class);
                        for (TextinIdCardOcrItem item : itemList) {
                            if (TextinIdCardItemKeyEnums.NAME.getKey().equals(item.getKey())) {
                                resp.setName(item.getValue());
                            } else if (TextinIdCardItemKeyEnums.SEX.getKey().equals(item.getKey())) {
                                resp.setGender(item.getValue());
                            } else if (TextinIdCardItemKeyEnums.NATIONALITY.getKey().equals(item.getKey())) {
                                resp.setNation(item.getValue());
                            } else if (TextinIdCardItemKeyEnums.BIRTH.getKey().equals(item.getKey())) {
                                if (StringUtils.isNotBlank(item.getValue())) {
                                    resp.setBirthday(ComUtil.getFormatDate(item.getValue(), ComUtil.DATE_TYPE22, ComUtil.DATE_TYPE2));
                                }
                            } else if (TextinIdCardItemKeyEnums.ADDRESS.getKey().equals(item.getKey())) {
                                resp.setAddress(item.getValue());
                            } else if (TextinIdCardItemKeyEnums.ID_NUMBER.getKey().equals(item.getKey())) {
                                resp.setIdCardNo(item.getValue());
                            } else if (TextinIdCardItemKeyEnums.ISSUE_AUTHORITY.getKey().equals(item.getKey())) {
                                resp.setIssuedBy(item.getValue());
                            } else if (TextinIdCardItemKeyEnums.VALIDATE_DATE.getKey().equals(item.getKey())) {
                                // 原始格式：yyyy.MM.dd-yyyy.MM.dd，需要拆分
                                if (StringUtils.isNotBlank(item.getValue())) {
                                    String[] array = item.getValue().split("-");
                                    resp.setIssueDate(ComUtil.getFormatDate(array[0], ComUtil.DATE_TYPE23, ComUtil.DATE_TYPE2));
                                    resp.setExpirationDate("长期".equals(array[1]) ? array[1] : ComUtil.getFormatDate(array[1], ComUtil.DATE_TYPE23, ComUtil.DATE_TYPE2));
                                }
                            } else if (TextinIdCardItemKeyEnums.IS_GRAY.getKey().equals(item.getKey())) {
                                CommonRiskTypeEnums riskType = CommonRiskTypeEnums.NORMAL;
                                if ("True".equalsIgnoreCase(item.getValue())) {
                                    riskType = CommonRiskTypeEnums.COPY;
                                }
                                resp.setRiskType(riskType);
                            }
                        }
                    }
                }
                return resp;
            }
        } catch (Exception e) {
            log.error("用户[{}]调用生腾的身份证认证接口异常，request={}", req.getMid(), JSON.toJSONString(req), e);
            respParams = JSON.toJSONString(e);
        } finally {
            membershipOcrCallLogManager.addMembershipOcrCallLog(req.getMid(), OcrChannelEnums.TEXIN,
                    OcrTypeEnums.ID_CARD, url, reqParams, respParams);
        }
        return null;
    }

    @Override
    public CommonDrivingLicenseOcrResp drivingLicenseOcr(CommonDrivingLicenseOcrReq req) {
        return null;
    }

    private String doPost(String url, byte[] imgData) {
        BufferedReader in = null;
        DataOutputStream out = null;
        String result = "";
        try {
            URL realUrl = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) realUrl.openConnection();
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("Content-Type", "application/octet-stream");
            conn.setRequestProperty("x-ti-app-id", appId);
            conn.setRequestProperty("x-ti-secret-code", secretCode);
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setRequestMethod("POST");
            conn.setConnectTimeout(2 * 1000);
            conn.setReadTimeout(10 * 1000);
            out = new DataOutputStream(conn.getOutputStream());
            out.write(imgData);
            out.flush();
            out.close();
            in = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
        } catch (Exception e) {
            log.error("调用生腾的身份证认证接口异常！", e);
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                log.error("关闭流异常！", ex);
            }
        }
        return result;
    }

    private byte[] getImgData(String url, byte[] image) throws IOException, BusinessException {
        byte[] imgData;
        if (!ObjectUtils.isEmpty(url)) {
            String filePath = url;
            imgData = FileUtil.readFileByBytes(filePath);
        } else if (!ObjectUtils.isEmpty(image)) {
            imgData = image;
        } else {
            throw new BusinessException("图片数据不能为空！");
        }
        return imgData;
    }

}
