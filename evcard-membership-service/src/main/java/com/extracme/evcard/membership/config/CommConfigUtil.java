package com.extracme.evcard.membership.config;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 替换原config.properties ConfigUtil类
 * <AUTHOR>
 * @Discription
 * @date 2021/12/10
 */
@Configuration
public class CommConfigUtil {
    /**
     * 合同-e签宝
     */
    private static String esignAppId;
    private static String esignAppKey;
    private static String esignBaseUrl;

    private static String ENV;
    private static String ENV_SUB;
    private static String SMS_MODE;

    @Value("${env}")
    public void setENV(String value) {
        ENV = value;
    }
    @Value("${env_sub}")
    public void setEnvSub(String value) {
        ENV_SUB = value;
    }
    //@Value("${smsMode}:0")
    @Value("${smsMode}")
    public void setSmsMode(String value) {
        SMS_MODE = value;
    }

    @Value("${esign.appId}")
    public void setEsignAppId(String value) {
        esignAppId = value;
    }

    @Value("${esign.appKey}")
    public void setEsignAppKey(String value) {
        esignAppKey = value;
    }

    @Value("${esign.api}")
    public void setEsignBaseUrl(String value) {
        esignBaseUrl = value;
    }

    public static String getEsignAppId() {
        return esignAppId;
    }

    public static String getEsignAppKey() {
        return esignAppKey;
    }

    public static String getEsignBaseUrl() {
        return esignBaseUrl;
    }

    public static String getENV() {
        return ENV;
    }

    public static String getEnvSub() {
        return ENV_SUB;
    }

    public static String getSmsMode() {
        return SMS_MODE;
    }
}
