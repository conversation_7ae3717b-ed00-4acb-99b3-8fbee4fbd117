package com.extracme.evcard.membership.core.service.md;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.config.MdRestApiConfig;
import com.extracme.evcard.membership.core.dto.md.SearchCityConfigurationRequest;
import com.extracme.evcard.membership.core.dto.md.SearchCityConfigurationResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/8/30
 */
@Slf4j
@Component
public class MdStoreService {
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private MdRestApiConfig mdRestApiConfig;

    public SearchCityConfigurationResponse searchCityConfiguration(long cityId) {
        String path = mdRestApiConfig.getBaseUrl() + mdRestApiConfig.getSearchCityConfiguration();
        SearchCityConfigurationRequest req = new SearchCityConfigurationRequest();
        req.setCityId(cityId);
        req.setStoreBusinessStatus(1);
        req.setPageNum(1);
        req.setPageSize(10);
        log.info("调用[{}]，请求：{}", path, JSON.toJSONString(req));
        SearchCityConfigurationResponse response = restTemplate.postForObject(path, req, SearchCityConfigurationResponse.class);
        log.info("调用[{}]，应答：{}", path, response);
        return response;
    }
}
