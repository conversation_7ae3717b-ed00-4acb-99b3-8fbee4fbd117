package com.extracme.evcard.membership.mq;


import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.contract.service.IMemberShipContractServ;
import com.extracme.evcard.membership.core.dto.*;
import com.extracme.evcard.membership.core.dto.md.GetMdContractResponse;
import com.extracme.evcard.membership.core.dto.md.MdContractPayInfo;
import com.extracme.evcard.membership.core.dto.md.MdRentalContract;
import com.extracme.evcard.membership.core.dto.md.QueryOrderPayInfoRes;
import com.extracme.evcard.membership.core.exception.MemberException;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.membership.core.service.md.MdOrderService;
import com.extracme.evcard.membership.credit.dao.MmpUserTagMapper;
import com.extracme.evcard.membership.credit.dto.MemberPointsOfferDto;
import com.extracme.evcard.membership.credit.model.MmpUserTag;
import com.extracme.evcard.membership.credit.service.IMemberPointsService;
import com.extracme.evcard.mq.bean.MemPointsPushEnum;
import com.extracme.evcard.mq.bean.md.*;
import com.extracme.evcard.redis.JedisUtil;
import com.extracme.evcard.rpc.entity.HidLog;
import com.extracme.evcard.rpc.entity.LogPoint;
import com.extracme.evcard.rpc.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
//public class MdEventListener implements MessageOrderListener {
public class MdEventListener implements MessageListener {

    @Resource
    private IMemberPointsService memberPointsService;

    @Resource
    private IMemberShipService memberShipService;

    @Resource
    private IMemberShipContractServ memberShipContractServ;

    @Resource
    private RawListener rawListener;
    private static final String BASIC_POINTS_TYPE = "01";

    @Autowired
    private MmpUserTagMapper mmpUserTagMapper;

    @Resource
    private MdOrderService mdOrderService;

    /**
     * 门店
     * 支付事件： 有效消费更新、订单支付积分、首单奖励
     * 还车事件： 更新首单
     * 订单评价： 积分发放
     * 基本沿用原有逻辑，后续版本优化。
     */
    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        if (MdEventEnum.ORDER_PAY.getTag().equals(message.getTag())) {
            return handleOrderPay(message.getBody());
        }
        else if (MdEventEnum.RETURN_VEHICLE.getTag().equals(message.getTag())) {
            return handleReturnVehicle(message.getBody());
        }
        else if (MdEventEnum.ORDER_ASSESS.getTag().equals(message.getTag())) {
            return handleOrderAssess(message.getBody());
        }
        // app1.9 自动签署协议 由发起预约动作  改成了 取车完成时
        //else if (MdEventEnum.VEHICLE_ORDER.getTag().equals(message.getTag())) {
        else if (MdEventEnum.PICKUP_VEHICLE.getTag().equals(message.getTag())) {
            return handleVehicleOrder(message);
        }
        return Action.CommitMessage;
    }

    // 自动签署合同
    public Action handleVehicleOrder(Message message) {
        /**
         * 预约完成:  触发协议签署，若最新承诺版本的协议尚未签订，则本次完成签订
         */
        MdVehicleOrder mdVehicleOrder = JSON.parseObject(message.getBody(), MdVehicleOrder.class);
        log.info("门店事件消费-门店订单预约成功，input={}, msgId={}", JSON.toJSONString(mdVehicleOrder), message.getMsgID() + "_" + message.getKey());
        GetMdContractResponse mdOrderRes = mdOrderService.searchMdContract(mdVehicleOrder.getContractId());
        if(mdOrderRes.getCode() != 0) {
            log.warn("门店事件消费-预约：查询门店订单失败， contractId={}, resp={}", mdVehicleOrder.getContractId(), JSON.toJSONString(mdOrderRes));
            return Action.ReconsumeLater;
        }
        MdRentalContract mdOrder = mdOrderRes.getData();
        if(mdOrder == null || StringUtils.isBlank(mdOrder.getContractId())) {
            log.info("门店事件消费-预约：未查得门店订单， contractId={}, mdOrder={}", mdVehicleOrder.getContractId(), JSON.toJSONString(mdOrder));
            return Action.CommitMessage;
        }
        MembershipBasicInfo member = memberShipService.getUserBasicInfo(mdOrder.getMid());
        if(member == null) {
            log.info("门店事件消费-预约：用户不存在， contractId={}, mid={}", mdVehicleOrder.getContractId(), mdOrder.getMid());
            return Action.CommitMessage;
        }
        // 未签署过法大大电子合同的用户租车时()
        if (member.getMembershipType().intValue() == 0) {
            try {
                log.info("nnnnnnnmm=" + member.getAuthId() + "预约门店订单-开始校验/签署合同");
                String typeOneVersion = JedisUtil.hget("typeVersionMap", "1");
                String typeOneOssUrl = JedisUtil.hget("typeOssUrlMap", "1");
                String typeTwoVersion = JedisUtil.hget("typeVersionMap", "2");
                String typeTwoOssUrl = JedisUtil.hget("typeOssUrlMap", "2");
                SignContractDto signContractDto = new SignContractDto();
                signContractDto.setAuthId(member.getAuthId());
                signContractDto.setContractVersionDto(
                        new ContractVersionDto("SZ" + typeOneVersion, typeOneVersion, "会员条款",typeOneOssUrl));
                signContractDto.setContractVersionDto(
                        new ContractVersionDto("YS" + typeTwoVersion, typeTwoVersion, "隐私政策",typeTwoOssUrl));
                memberShipService.autoSignContract(signContractDto);
            } catch (MemberException e) {
                log.error("用户下单前校验/签署合同异常,mdVehicleOrder={}",JSON.toJSONString(mdVehicleOrder),e);
                return Action.ReconsumeLater;
            } catch (Exception e) {
                log.error("用户authid=" + member.getAuthId() + "下单前校验/签署合同异常", e);
            }
        }
        // 未签署过法大大电子合同的用户租车时
        return Action.CommitMessage;
    }


    public Action handleOrderPay(byte[] message) {
        /**
         * 订单支付完成:  订单支付积分发放/首单积分发放/有效消费及信用事件记录
         */
        MdOrderPay orderPay = JSON.parseObject(message, MdOrderPay.class);
        log.info("门店事件消费-门店订单支付，input={}", JSON.toJSONString(orderPay));
        if(!orderPay.getBillType().equals(2)) {
            log.info("门店事件消费-门店订单支付-非后付订单，input={}", JSON.toJSONString(orderPay));
            return Action.CommitMessage;
        }
        MembershipBasicInfo member = memberShipService.getUserBasicInfo(orderPay.getMid());
        if(member == null) {
            log.info("门店事件消费-支付：用户不存在， contractId={}, mid={}", orderPay.getContractId(), orderPay.getMid());
            return Action.CommitMessage;
        }
        GetMdContractResponse mdOrderRes = mdOrderService.searchMdContract(orderPay.getContractId());
        if(mdOrderRes.getCode() != 0) {
            log.warn("门店事件消费-支付：查询门店订单失败， contractId={}, resp={}", orderPay.getContractId(), JSON.toJSONString(mdOrderRes));
            return Action.ReconsumeLater;
        }
        MdRentalContract mdOrder = mdOrderRes.getData();
        if(mdOrder == null || StringUtils.isBlank(mdOrder.getContractId())) {
            log.info("门店事件消费-支付：未查得门店订单， contractId={}, mdOrder={}", orderPay.getContractId(), JSON.toJSONString(mdOrder));
            return Action.CommitMessage;
        }
        String authId = member.getAuthId();
        String orderSeq = orderPay.getContractId();
        /**
         * 获取订单支付明细
         */
        QueryOrderPayInfoRes orderPayInfoRes = mdOrderService.queryOrderPayInfo(orderPay.getContractId());
        if(orderPayInfoRes.getCode() != 0) {
            log.warn("门店事件消费-支付：查询门店订单的支付详情失败， contractId={}, resp={}", orderPay.getContractId(), JSON.toJSONString(orderPayInfoRes));
            return Action.ReconsumeLater;
        }
        MdContractPayInfo orderPayInfo = orderPayInfoRes.getData();
        if(orderPayInfo == null || StringUtils.isBlank(orderPayInfo.getRealAmount())) {
            log.warn("门店事件消费支付：未查得门店订单的支付详情， contractId={}", orderPay.getContractId());
            return Action.CommitMessage;
        }
        log.info("门店事件消费-支付：门店订单支付详情，contractId={}, payInfo={}", orderPay.getContractId(), JSON.toJSONString(orderPayInfo));
        //订单实付金额 + 充值e币支付金额 -->  校验当前是后付支付订单，才进行奖励
        BigDecimal realAmount = ComUtil.getBigDecimal(mdOrder.getRealAmount()); //现金实付金额
        BigDecimal totalEAmount = ComUtil.getBigDecimal(orderPayInfo.getTotalEAmount());      //e币总金额
        BigDecimal preChargeEAmount = ComUtil.getBigDecimal(orderPayInfo.getTotalEAmount());  //充值e币金额

        /**
         * 计算现金实付金额
         */
        BigDecimal amount = BigDecimal.ZERO.add(realAmount).add(preChargeEAmount);
        amount = amount.multiply(new BigDecimal(100));

        /**
         * 1. 若为首单则推送首单奖励积分
         */
        MemberPointsOfferDto offerDto = new MemberPointsOfferDto();
        offerDto.setAuthId(authId);
        offerDto.setPointsType(BASIC_POINTS_TYPE);
        MmpUserTagDto tag = memberShipService.queryUserTagByAuthId(authId);
        if(tag != null && StringUtils.equals(tag.getSpare1(), orderSeq)) {
            offerDto.setEventType(MemPointsPushEnum.MEMBER_FIRST_ORDER.getCode());
            offerDto.setCreateTime(new Date());
            offerDto.setEventRefSeq(orderSeq);
            offerDto.setAmount(amount);
            Map<String, Object> properties = new HashMap<>();
            properties.put("orderSeq", orderSeq);
            offerDto.setDetails(ComUtil.toJSONString(properties));
            log.info("门店事件消费：推送首单支付完成积分奖励, authId={}, orderSeq={}, input={}", authId, orderSeq, JSON.toJSONString(offerDto));
            HidLog.membership(LogPoint.MEMBER_POINTS_PUSH, MemPointsPushEnum.MEMBER_FIRST_ORDER.getTitle() + "_" + orderSeq, authId);
            memberPointsService.asyncOfferPoints(offerDto);
        }

        /**
         * 2.  订单支付完成积分赠送(积分数 = fx(订单实付金额))
         */
        if(BigDecimal.ZERO.compareTo(amount) < 0) {
            offerDto.setEventType(MemPointsPushEnum.ORDER_PAY.getCode());
            offerDto.setCreateTime(new Date());
            offerDto.setEventRefSeq(orderSeq);
            offerDto.setAmount(amount);
            Map<String, Object> properties = new HashMap<>();
            properties.put("orderSeq", orderSeq);
            properties.put("realAmount", realAmount);
            properties.put("rechargeEAmount", preChargeEAmount);
            properties.put("totalEAmount", totalEAmount);
            offerDto.setDetails(ComUtil.toJSONString(properties));
            log.info("门店事件消费：推送订单支付完成积分奖励, authId={}, orderSeq={}, input={}", authId, orderSeq, JSON.toJSONString(offerDto));
            HidLog.membership(LogPoint.MEMBER_POINTS_PUSH, MemPointsPushEnum.ORDER_PAY.getTitle() + "_" + orderSeq, authId);
            memberPointsService.asyncOfferPoints(offerDto);
        }else {
            log.info("门店事件消费-支付：订单orderSeq={}, authId={} 订单实付现金金额{}元，充值e币抵扣{}元，不做积分发放。", orderSeq, authId, realAmount, preChargeEAmount);
        }

        /**
         * 3. 有效消费&信用事件更新
         * 仅后付支付订单更新有效消费
         */
        OrderPayItems payItems = new OrderPayItems();
        payItems.setRealAmount(realAmount);
        payItems.setPreChargeEAmount(preChargeEAmount);
        payItems.setTotalEAmount(totalEAmount);
        OrderEffectivePay orderEffectivePay = new OrderEffectivePay();
        orderEffectivePay.setOrderSeq(orderPay.getContractId());
        orderEffectivePay.setPayTime(orderPay.getPayFinishTime());
        orderEffectivePay.setAuthId(member.getAuthId());
        orderEffectivePay.setReturnTime(DateUtil.getDateFromTimeStr(mdOrder.getRealReturnTime(), DateUtil.DATE_TYPE4));
        savePayCreditData(orderEffectivePay, payItems);
        return Action.CommitMessage;
    }

    public Action handleOrderAssess(byte[] message) {
        MdOrderAssess orderAssess = JSON.parseObject(message, MdOrderAssess.class);
        log.info("门店事件消费-评价：门店订单取还车评价，input={}", JSON.toJSONString(orderAssess));
        // 满意度调查 不需要校验订单
        if (orderAssess.getAssessGroup() == 3) {
            MembershipBasicInfo member = memberShipService.getUserBasicInfo(orderAssess.getMid());
            if(member == null) {
                log.warn("门店事件消费-评价：门店订单取还车评价，mid={}用户不存在.", orderAssess.getContractId());
                return Action.CommitMessage;
            }
            MemPointsPushEnum eventType = orderAssess.getAssessGroup().equals(1) ? MemPointsPushEnum.MD_ORDER_PICKUP_ASSESS
                    : MemPointsPushEnum.MD_ORDER_RETURN_ASSESS;
            Integer grade = orderAssess.getAssessGrade();
            if (StringUtils.isBlank(orderAssess.getEventRefSeq())) {
                orderAssess.setEventRefSeq(orderAssess.getContractId());
            }
            if(grade != null && grade > 0) {
                MemberPointsOfferDto offerDto = new MemberPointsOfferDto();
                String authId = member.getAuthId();
                offerDto.setAuthId(authId);
                offerDto.setPointsType(BASIC_POINTS_TYPE);
                offerDto.setEventType(eventType.getCode());
                offerDto.setCreateTime(new Date());
                offerDto.setEventRefSeq(orderAssess.getEventRefSeq());
                offerDto.setDetails(ComUtil.toJSONString(orderAssess));
                offerDto.setPointsNum(grade);
                offerDto.setMessageFlag(1);
                log.info("门店事件消费-评价：推送【订单评价】积分奖励, authId={}, input={}", authId, JSON.toJSONString(orderAssess));
                HidLog.membership(LogPoint.MEMBER_POINTS_PUSH, eventType.getTitle() + "_" + orderAssess.getContractId() + "_" + grade, authId);
                memberPointsService.asyncOfferPoints(offerDto);
            }
            return Action.CommitMessage;
        }
        GetMdContractResponse mdOrderRes = mdOrderService.searchMdContract(orderAssess.getContractId());
        if(mdOrderRes.getCode() != 0) {
            log.warn("门店事件消费-评价：查询门店订单失败， contractId={}, resp={}", orderAssess.getContractId(), JSON.toJSONString(mdOrderRes));
            return Action.ReconsumeLater;
        }
        MdRentalContract mdOrder = mdOrderRes.getData();
        if(mdOrder == null) {
            log.warn("门店事件消费-评价：门店订单取还车评价，contractId={}租车合同不存在.", orderAssess.getContractId());
            return Action.CommitMessage;
        }
        MembershipBasicInfo member = memberShipService.getUserBasicInfo(mdOrder.getMid());
        if(member == null) {
            log.warn("门店事件消费-评价：门店订单取还车评价，mid={}用户不存在.", orderAssess.getContractId());
            return Action.CommitMessage;
        }
        MemPointsPushEnum eventType = orderAssess.getAssessGroup().equals(1) ? MemPointsPushEnum.MD_ORDER_PICKUP_ASSESS
                : MemPointsPushEnum.MD_ORDER_RETURN_ASSESS;
        Integer grade = orderAssess.getAssessGrade();
        if (StringUtils.isBlank(orderAssess.getEventRefSeq())) {
            orderAssess.setEventRefSeq(orderAssess.getContractId());
        }
        if(grade != null && grade > 0) {
            MemberPointsOfferDto offerDto = new MemberPointsOfferDto();
            String authId = member.getAuthId();
            offerDto.setAuthId(authId);
            offerDto.setPointsType(BASIC_POINTS_TYPE);
            offerDto.setEventType(eventType.getCode());
            offerDto.setCreateTime(new Date());
            offerDto.setEventRefSeq(orderAssess.getEventRefSeq());
            offerDto.setDetails(ComUtil.toJSONString(orderAssess));
            offerDto.setPointsNum(grade);
            log.info("门店事件消费-评价：推送【订单评价】积分奖励, authId={}, input={}", authId, JSON.toJSONString(orderAssess));
            HidLog.membership(LogPoint.MEMBER_POINTS_PUSH, eventType.getTitle() + "_" + orderAssess.getContractId() + "_" + grade, authId);
            memberPointsService.asyncOfferPoints(offerDto);
        }
        return Action.CommitMessage;
    }

    public Action handleReturnVehicle(byte[] message) {
        MdReturnVehicle returnVehicle = JSON.parseObject(message, MdReturnVehicle.class);
        log.info("门店事件消费-还车：门店订单还车，input={}", JSON.toJSONString(returnVehicle));
        GetMdContractResponse mdOrderRes = mdOrderService.searchMdContract(returnVehicle.getContractId());
        if(mdOrderRes.getCode() != 0) {
            log.warn("门店事件消费-还车：门店订单还车，查询门店订单失败， contractId={}, resp={}", returnVehicle.getContractId(), JSON.toJSONString(mdOrderRes));
            return Action.ReconsumeLater;
        }
        MdRentalContract mdOrder = mdOrderRes.getData();
        if(mdOrder == null) {
            log.warn("门店事件消费-还车：门店订单还车，contractId={}租车合同不存在.", returnVehicle.getContractId());
            return Action.CommitMessage;
        }
        MembershipBasicInfo member = memberShipService.getUserBasicInfo(mdOrder.getMid());
        if(member == null) {
            log.info("门店事件消费-还车：用户不存在， contractId={}, mid={}", returnVehicle.getContractId(), mdOrder.getMid());
            return Action.CommitMessage;
        }
        String authId = member.getAuthId();
        String orderSeq = returnVehicle.getContractId();
        //还车成功后，为门店订单，同样更新首单字段
        MmpUserTag tag = mmpUserTagMapper.selectMmpUserByAuthId(authId);
        if(tag == null) {
            log.error("门店事件消费-还车：userTag.spare1: 更新首单信息失败，还车时仍无userTag信息, authId={}, orderSeq={}", authId, orderSeq);
            return Action.CommitMessage;
        }
        if(StringUtils.startsWith(tag.getSpare1(), "MC") || StringUtils.startsWith(tag.getSpare1(),"C")
                || StringUtils.startsWith(tag.getSpare1(), "D")) {
            log.warn("门店事件消费-还车：userTag.spare1: 非用户首单，不更新，authId={}, spare1={}, orderSeq={}", authId, tag.getSpare1(), returnVehicle.getContractId());
            return Action.CommitMessage;
        }
        mmpUserTagMapper.updateUserFirstOrderSeq(authId, orderSeq);
        return Action.CommitMessage;
    }

    private void savePayCreditData(OrderEffectivePay orderPay, OrderPayItems payItems) {
        /**
         * 已还车、已支付 --> 则记录有效消费 & 记录支付信用事件
         *
         * 仅后付支付完成时，更新有效消费
         * 预付款可能退款，不应在此时记录有效消费
         */
        log.info("门店事件消费：更新有效消费, pay={}, payDetail={}", JSON.toJSONString(orderPay), JSON.toJSONString(payItems));
        //已还车&已支付，则记录有效消费
        if (orderPay.getReturnTime() != null && orderPay.getPayTime() != null) {
            BigDecimal amount = payItems.getRealAmount().add(payItems.getTotalEAmount());
            mmpUserTagMapper.updateEffectiveContdByAuthId(orderPay.getAuthId(), amount, BigDecimal.ZERO);
            rawListener.savePayCreditEvent(orderPay);
        }
    }

}
