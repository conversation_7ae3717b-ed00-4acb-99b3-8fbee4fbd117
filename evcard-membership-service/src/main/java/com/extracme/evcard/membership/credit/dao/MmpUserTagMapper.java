package com.extracme.evcard.membership.credit.dao;

import com.extracme.evcard.membership.credit.model.MmpUserTag;

import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

public interface MmpUserTagMapper {
	
	/**
	 * 新增会员标签.<br>
	 * @param record
	 * @return
	 */
	int insertSelective(MmpUserTag record);

    int updateCreditAmountByAuthId(@Param("authId") String authId,
                                   @Param("amount") Integer amount);

    int updateCreditZeroAmountByAuthId(String authId);

    int saveUserTag(MmpUserTag userTag);

    int updateEffectiveContdByAuthId(@Param("authId") String authId,
                                     @Param("amount") BigDecimal amount,
                                     @Param("oldAmount") BigDecimal oldAmount);

    /**
     * 查询会员标签.<br>
     * @param authId
     * @return
     */
    MmpUserTag selectMmpUserByAuthId(String authId);


    /**
     * 查询会员标签.<br>
     * @param authIds
     * @return
     */
    List<MmpUserTag> selectMmpUserListByAuthIds(@Param("authIds") List<String> authIds);

    /**
     * 根据imei查询会员标签内容.<br>
     * @param imei	会员imei.<br>
     * @return
     */
    MmpUserTag selectByImei(String imei);
    
    /**
     * 修改用户标签.<br>
     * @param userTag
     * @return
     */
    int updateByPrimaryKey(MmpUserTag userTag);

    /**
     * 查询代扣签约号
     * @param authId
     * @return
     */
    String queryWithHoldSignInfo(@Param("authId") String authId);

    /**
     * 更新人脸相似度
     * @param mmpUserTag
     * @return
     */
    int updateFaceSimilarity(MmpUserTag mmpUserTag);

    /**
     * 修改用户支付宝id标签.<br>
     * @param userTag
     * @return
     */
    int updateZfbIdByAuthId(MmpUserTag userTag);

    /**
     * 修改用户标签.<br>
     * @param userTag
     * @return
     */
    int updateByAuthId(MmpUserTag userTag);

    void updateUserFirstOrderSeq(@Param("authId") String authId, @Param("orderSeq") String orderSeq);

    /**
     * 更新用户学生证图片url
     * @param authId
     * @param studentCardUrl
     */
    void updateUserStudentCardUrl(@Param("authId") String authId, @Param("studentCardUrl") String studentCardUrl);


    /**
     * 批量查询 spare4 不为空的 数据
     * @param id
     * @return
     */
    List<MmpUserTag> getSpare4NotEmptyList(@Param("id") Long id, @Param("limit") Integer limit);
}