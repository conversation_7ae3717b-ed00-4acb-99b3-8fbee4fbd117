<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.extracme.evcard.membership.core.dao.UserFaceContrastResultMapper">
    <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.UserFaceContrastResult">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="auth_id" jdbcType="VARCHAR" property="authId" />
        <result column="face_image" jdbcType="VARCHAR" property="faceImage" />
        <result column="user_face_image" jdbcType="VARCHAR" property="userFaceImage" />
        <result column="confidence" jdbcType="DECIMAL" property="confidence" />
        <result column="is_success" jdbcType="INTEGER" property="isSuccess" />
        <result column="error_code" jdbcType="INTEGER" property="errorCode" />
        <result column="face_origin" jdbcType="INTEGER" property="faceOrigin" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="misc_desc" jdbcType="VARCHAR" property="miscDesc" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="create_oper_id" jdbcType="BIGINT" property="createOperId" />
        <result column="create_oper_name" jdbcType="VARCHAR" property="createOperName" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="update_oper_id" jdbcType="BIGINT" property="updateOperId" />
        <result column="update_oper_name" jdbcType="VARCHAR" property="updateOperName" />
    </resultMap>
    <sql id="Base_Column_List">
        id, auth_id, face_image, user_face_image, confidence, is_success, error_code, face_origin,
    status, misc_desc, create_time, create_oper_id, create_oper_name, update_time, update_oper_id,
    update_oper_name
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mmp_user_face_contrast_result
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from mmp_user_face_contrast_result
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.extracme.evcard.membership.core.model.UserFaceContrastResult">
        insert into mmp_user_face_contrast_result (id, auth_id, face_image,
                                               user_face_image, confidence, is_success,
                                               error_code, face_origin, status,
                                               misc_desc, create_time, create_oper_id,
                                               create_oper_name, update_time, update_oper_id,
                                               update_oper_name)
        values (#{id,jdbcType=BIGINT}, #{authId,jdbcType=VARCHAR}, #{faceImage,jdbcType=VARCHAR},
                #{userFaceImage,jdbcType=VARCHAR}, #{confidence,jdbcType=DECIMAL}, #{isSuccess,jdbcType=INTEGER},
                #{errorCode,jdbcType=INTEGER}, #{faceOrigin,jdbcType=INTEGER}, #{status,jdbcType=INTEGER},
                #{miscDesc,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT},
                #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT},
                #{updateOperName,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.extracme.evcard.membership.core.model.UserFaceContrastResult">
        insert into mmp_user_face_contrast_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="authId != null">
                auth_id,
            </if>
            <if test="faceImage != null">
                face_image,
            </if>
            <if test="userFaceImage != null">
                user_face_image,
            </if>
            <if test="confidence != null">
                confidence,
            </if>
            <if test="isSuccess != null">
                is_success,
            </if>
            <if test="errorCode != null">
                error_code,
            </if>
            <if test="faceOrigin != null">
                face_origin,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="miscDesc != null">
                misc_desc,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="createOperId != null">
                create_oper_id,
            </if>
            <if test="createOperName != null">
                create_oper_name,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="updateOperId != null">
                update_oper_id,
            </if>
            <if test="updateOperName != null">
                update_oper_name,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="authId != null">
                #{authId,jdbcType=VARCHAR},
            </if>
            <if test="faceImage != null">
                #{faceImage,jdbcType=VARCHAR},
            </if>
            <if test="userFaceImage != null">
                #{userFaceImage,jdbcType=VARCHAR},
            </if>
            <if test="confidence != null">
                #{confidence,jdbcType=DECIMAL},
            </if>
            <if test="isSuccess != null">
                #{isSuccess,jdbcType=INTEGER},
            </if>
            <if test="errorCode != null">
                #{errorCode,jdbcType=INTEGER},
            </if>
            <if test="faceOrigin != null">
                #{faceOrigin,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="miscDesc != null">
                #{miscDesc,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createOperId != null">
                #{createOperId,jdbcType=BIGINT},
            </if>
            <if test="createOperName != null">
                #{createOperName,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateOperId != null">
                #{updateOperId,jdbcType=BIGINT},
            </if>
            <if test="updateOperName != null">
                #{updateOperName,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.core.model.UserFaceContrastResult">
        update mmp_user_face_contrast_result
        <set>
            <if test="authId != null">
                auth_id = #{authId,jdbcType=VARCHAR},
            </if>
            <if test="faceImage != null">
                face_image = #{faceImage,jdbcType=VARCHAR},
            </if>
            <if test="userFaceImage != null">
                user_face_image = #{userFaceImage,jdbcType=VARCHAR},
            </if>
            <if test="confidence != null">
                confidence = #{confidence,jdbcType=DECIMAL},
            </if>
            <if test="isSuccess != null">
                is_success = #{isSuccess,jdbcType=INTEGER},
            </if>
            <if test="errorCode != null">
                error_code = #{errorCode,jdbcType=INTEGER},
            </if>
            <if test="faceOrigin != null">
                face_origin = #{faceOrigin,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="miscDesc != null">
                misc_desc = #{miscDesc,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createOperId != null">
                create_oper_id = #{createOperId,jdbcType=BIGINT},
            </if>
            <if test="createOperName != null">
                create_oper_name = #{createOperName,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateOperId != null">
                update_oper_id = #{updateOperId,jdbcType=BIGINT},
            </if>
            <if test="updateOperName != null">
                update_oper_name = #{updateOperName,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.core.model.UserFaceContrastResult">
        update mmp_user_face_contrast_result
        set auth_id = #{authId,jdbcType=VARCHAR},
            face_image = #{faceImage,jdbcType=VARCHAR},
            user_face_image = #{userFaceImage,jdbcType=VARCHAR},
            confidence = #{confidence,jdbcType=DECIMAL},
            is_success = #{isSuccess,jdbcType=INTEGER},
            error_code = #{errorCode,jdbcType=INTEGER},
            face_origin = #{faceOrigin,jdbcType=INTEGER},
            status = #{status,jdbcType=INTEGER},
            misc_desc = #{miscDesc,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            create_oper_id = #{createOperId,jdbcType=BIGINT},
            create_oper_name = #{createOperName,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            update_oper_id = #{updateOperId,jdbcType=BIGINT},
            update_oper_name = #{updateOperName,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>