package com.extracme.evcard.membership.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.extracme.evcard.membership.contract.service.IMemberContractSignProvider;
import com.extracme.evcard.membership.core.dao.UserContractMapper;
import com.extracme.evcard.membership.core.dto.UserContractKeyDto;
import com.extracme.evcard.membership.core.model.UserContract;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;



/**
 * 法大大合同归档
 */

@Slf4j
@Component
@Deprecated
public class MemberContractArchiveJob implements SimpleJob {

    @Autowired
    private UserContractMapper userContractMapper;

    @Autowired
    IMemberContractSignProvider fddContractService;

    private static final int MAX_QUERY_SIZE = 500;
    private static final int MAX_OP_SIZE = 100;

    @Override
    public void execute(ShardingContext shardingContext) {
        String str = shardingContext.getJobParameter();
        String[] params = StringUtils.split(str, ",");
        if(params == null || params.length != 2) {
            return;
        }
        long startId = NumberUtils.toLong(params[0], 0);
        long ep = NumberUtils.toLong(params[1], 0);
        doWork(startId, ep);
    }

    public void doWork(Long startId, Long ep) {
        log.warn("归档法大大合同开始， batchArchive, {}->{}", startId, ep);
        int total = 0;
        Long sp = startId;
        long now = System.currentTimeMillis();
        while (true) {
            List<UserContractKeyDto> contracts = userContractMapper.selectFddContracts(sp, ep, MAX_QUERY_SIZE);
            if(CollectionUtils.isEmpty(contracts)) {
                break;
            }
            long st = System.currentTimeMillis();
            log.warn("batchArchive: start {}->{} + {}", sp, sp, contracts.size());
            //批量归档历史法大大合同文件
            total += batchArchive(contracts);
            log.warn("batchArchive: end {}->{} + {}, cost={}ms", sp, sp, contracts.size(),
                    System.currentTimeMillis() - st);
            //下一个起点
            sp = contracts.get(contracts.size() - 1).getId();
            log.warn("batchArchive: next sp={}", sp);
        }
        log.warn("归档法大大合同结束， batchArchive, {}->{}, total={}, cost={}",
                startId, ep, total, System.currentTimeMillis() - now);
    }


    public static final String FILE_PREFIX = "";
    public int batchArchive(List<UserContractKeyDto> contracts){
        int count = 0;
        Map<String, UserContractKeyDto> data = new HashMap<>();
        for(int i = 0; i < contracts.size(); i ++) {
            UserContractKeyDto userContract = contracts.get(i);
            data.put(userContract.getContractId(), userContract);
            if(data.keySet().size() == MAX_OP_SIZE || i == contracts.size() - 1) {
                long st = System.currentTimeMillis();
                log.warn("batchArchive: downUpload start count={}", data.keySet().size());
                //批量获取文档并上传
                Map<String, String> outMap = fddContractService.batchDownloadContract(data);
                data.clear();
                if(outMap == null || outMap.keySet().size() == 0) {
                    continue;
                }
                log.warn("batchArchive: downUpload loadFileCount={}", outMap.keySet().size());
                //批量更新文件路径
                try{
                    count += userContractMapper.updateArchiveUrl("/sensitiveBucket/userContract/",
                            0, outMap.keySet());
                }catch (Exception ex) {
                    log.warn("批量更新法大大协议文件路径失败", ex);
                }
                log.warn("batchArchive: downUpload start count={}, cost={}ms", outMap.keySet().size(),
                        System.currentTimeMillis() - st);
            }
        }
        return count;
    }
}
