package com.extracme.evcard.membership.core.model;

import java.util.Date;

public class OrderInfo {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.ORDER_SEQ
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String orderSeq;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.AUTH_ID
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String authId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.ORG_ID
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String orgId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.CARD_NO
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String cardNo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.VIN
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String vin;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.RENT_METHOD
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double rentMethod;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.GUARANTEE_MOBIL
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String guaranteeMobil;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.GUARANTEE_NAME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String guaranteeName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.GUARANTEE_IC_CARD
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String guaranteeIcCard;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.GUARANTEE_IC_CARD_KIND
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double guaranteeIcCardKind;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.PLANPICKUPSTORESEQ
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String planpickupstoreseq;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.PLANPICKUPDATETIME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String planpickupdatetime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.PLANRETURNSTORESEQ
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String planreturnstoreseq;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.PLANRETURNDATETIME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String planreturndatetime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.PICKUPDATETIME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String pickupdatetime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.RETURNDATETIME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String returndatetime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.PICKUP_STORE_SEQ
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String pickupStoreSeq;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.RETURN_STORE_SEQ
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String returnStoreSeq;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.RETURN_MILEAGE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double returnMileage;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.GET_MILEAGE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double getMileage;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.PURPOSE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double purpose;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.PRE_AUTHORISED_TYPE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double preAuthorisedType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.AUTHORIZATION_PRICE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double authorizationPrice;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.PAY_TRADE_NO
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String payTradeNo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.ADD_SERVICE_ITEMS
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String addServiceItems;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.ILLEGAL_PRE_AUTHORISED_FLG
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double illegalPreAuthorisedFlg;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.ILLEGAL_PRE_AUTHORISED_AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double illegalPreAuthorisedAmount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.OTHER_AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double otherAmount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.PAY_WAY
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double payWay;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.PRE_PAY_AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double prePayAmount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.OTHER_AMOUNT_TOTAL
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double otherAmountTotal;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.RENT_TOTAL
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double rentTotal;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double amount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.REAL_AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double realAmount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.EXEMPTION_AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double exemptionAmount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.EXEMPTION_REASON
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String exemptionReason;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.COMPENSATE_AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double compensateAmount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.INVOICE_TYPE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double invoiceType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.CREDIT_NO
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String creditNo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.PAYMENT_STATUS
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double paymentStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.ORDER_TYPE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double orderType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.RETURN_SOC
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double returnSoc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.GET_SOC
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double getSoc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.BILL_TIME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double billTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.COST_TIME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double costTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.EXEMPTION_TIME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double exemptionTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.RETURN_REMARK
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String returnRemark;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.GET_REMARK
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String getRemark;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.SMS_STATUS
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double smsStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.ILLEGAL_SEQ
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String illegalSeq;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.REMARK
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String remark;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.RENT_SHOPER
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String rentShoper;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.ACCOUNT_SHOPER
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String accountShoper;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.ORIGIN
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String origin;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.CANCEL_METHOD
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double cancelMethod;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.ORG_FREE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double orgFree;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.CREATED_TIME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String createdTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.CREATED_USER
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String createdUser;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.UPDATED_TIME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String updatedTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.UPDATED_USER
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String updatedUser;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.PAY_ITEM
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double payItem;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.CANCEL_FLAG
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double cancelFlag;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.ITEM_SEQ
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double itemSeq;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.VEHICLE_ORG_ID
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String vehicleOrgId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.RESTRICT_FLAT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double restrictFlat;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.RESTRICT_CONTENT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String restrictContent;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.RETURN_ERROR_CODE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double returnErrorCode;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.BILL_TIME_PRECHARGE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double billTimePrecharge;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.RETURN_CONDITION
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double returnCondition;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.ABNORMAL_TYPE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double abnormalType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.USER_COUPON_SEQ
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double userCouponSeq;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.DISCOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double discount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.OPERATION_ORG_ID
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String operationOrgId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.ORDER_PROPERTY
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Integer orderProperty;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.PICKUP_WAY
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Integer pickupWay;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.RETURN_WAY
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Integer returnWay;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.LOCATION_TYPE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double locationType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.ISVEHASSESS
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Boolean isvehassess;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.PICKVEH_AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double pickvehAmount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.BANCHE_FREE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Boolean bancheFree;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.RETURNVEH_AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double returnvehAmount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.PARK_AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double parkAmount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.UNIT_PRICE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double unitPrice;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.MEMDISCOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double memdiscount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.ORDER_AGENCY
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String orderAgency;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.ACTIVITY_TYPE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Integer activityType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.PAY_TIME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Date payTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.PAY_STATUS_FROM_APP
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Integer payStatusFromApp;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.OUT_TRADE_SEQ
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String outTradeSeq;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.MARKET_ACTIVITY_TYPE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Integer marketActivityType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.DEPOSIT_DEDUCT_AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private Double depositDeductAmount;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.ORDER_SEQ
     *
     * @return the value of order_info.ORDER_SEQ
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getOrderSeq() {
        return orderSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.ORDER_SEQ
     *
     * @param orderSeq the value for order_info.ORDER_SEQ
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setOrderSeq(String orderSeq) {
        this.orderSeq = orderSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.AUTH_ID
     *
     * @return the value of order_info.AUTH_ID
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getAuthId() {
        return authId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.AUTH_ID
     *
     * @param authId the value for order_info.AUTH_ID
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setAuthId(String authId) {
        this.authId = authId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.ORG_ID
     *
     * @return the value of order_info.ORG_ID
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getOrgId() {
        return orgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.ORG_ID
     *
     * @param orgId the value for order_info.ORG_ID
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.CARD_NO
     *
     * @return the value of order_info.CARD_NO
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getCardNo() {
        return cardNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.CARD_NO
     *
     * @param cardNo the value for order_info.CARD_NO
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.VIN
     *
     * @return the value of order_info.VIN
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getVin() {
        return vin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.VIN
     *
     * @param vin the value for order_info.VIN
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setVin(String vin) {
        this.vin = vin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.RENT_METHOD
     *
     * @return the value of order_info.RENT_METHOD
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getRentMethod() {
        return rentMethod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.RENT_METHOD
     *
     * @param rentMethod the value for order_info.RENT_METHOD
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setRentMethod(Double rentMethod) {
        this.rentMethod = rentMethod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.GUARANTEE_MOBIL
     *
     * @return the value of order_info.GUARANTEE_MOBIL
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getGuaranteeMobil() {
        return guaranteeMobil;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.GUARANTEE_MOBIL
     *
     * @param guaranteeMobil the value for order_info.GUARANTEE_MOBIL
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setGuaranteeMobil(String guaranteeMobil) {
        this.guaranteeMobil = guaranteeMobil;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.GUARANTEE_NAME
     *
     * @return the value of order_info.GUARANTEE_NAME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getGuaranteeName() {
        return guaranteeName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.GUARANTEE_NAME
     *
     * @param guaranteeName the value for order_info.GUARANTEE_NAME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setGuaranteeName(String guaranteeName) {
        this.guaranteeName = guaranteeName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.GUARANTEE_IC_CARD
     *
     * @return the value of order_info.GUARANTEE_IC_CARD
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getGuaranteeIcCard() {
        return guaranteeIcCard;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.GUARANTEE_IC_CARD
     *
     * @param guaranteeIcCard the value for order_info.GUARANTEE_IC_CARD
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setGuaranteeIcCard(String guaranteeIcCard) {
        this.guaranteeIcCard = guaranteeIcCard;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.GUARANTEE_IC_CARD_KIND
     *
     * @return the value of order_info.GUARANTEE_IC_CARD_KIND
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getGuaranteeIcCardKind() {
        return guaranteeIcCardKind;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.GUARANTEE_IC_CARD_KIND
     *
     * @param guaranteeIcCardKind the value for order_info.GUARANTEE_IC_CARD_KIND
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setGuaranteeIcCardKind(Double guaranteeIcCardKind) {
        this.guaranteeIcCardKind = guaranteeIcCardKind;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.PLANPICKUPSTORESEQ
     *
     * @return the value of order_info.PLANPICKUPSTORESEQ
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getPlanpickupstoreseq() {
        return planpickupstoreseq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.PLANPICKUPSTORESEQ
     *
     * @param planpickupstoreseq the value for order_info.PLANPICKUPSTORESEQ
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setPlanpickupstoreseq(String planpickupstoreseq) {
        this.planpickupstoreseq = planpickupstoreseq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.PLANPICKUPDATETIME
     *
     * @return the value of order_info.PLANPICKUPDATETIME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getPlanpickupdatetime() {
        return planpickupdatetime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.PLANPICKUPDATETIME
     *
     * @param planpickupdatetime the value for order_info.PLANPICKUPDATETIME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setPlanpickupdatetime(String planpickupdatetime) {
        this.planpickupdatetime = planpickupdatetime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.PLANRETURNSTORESEQ
     *
     * @return the value of order_info.PLANRETURNSTORESEQ
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getPlanreturnstoreseq() {
        return planreturnstoreseq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.PLANRETURNSTORESEQ
     *
     * @param planreturnstoreseq the value for order_info.PLANRETURNSTORESEQ
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setPlanreturnstoreseq(String planreturnstoreseq) {
        this.planreturnstoreseq = planreturnstoreseq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.PLANRETURNDATETIME
     *
     * @return the value of order_info.PLANRETURNDATETIME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getPlanreturndatetime() {
        return planreturndatetime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.PLANRETURNDATETIME
     *
     * @param planreturndatetime the value for order_info.PLANRETURNDATETIME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setPlanreturndatetime(String planreturndatetime) {
        this.planreturndatetime = planreturndatetime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.PICKUPDATETIME
     *
     * @return the value of order_info.PICKUPDATETIME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getPickupdatetime() {
        return pickupdatetime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.PICKUPDATETIME
     *
     * @param pickupdatetime the value for order_info.PICKUPDATETIME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setPickupdatetime(String pickupdatetime) {
        this.pickupdatetime = pickupdatetime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.RETURNDATETIME
     *
     * @return the value of order_info.RETURNDATETIME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getReturndatetime() {
        return returndatetime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.RETURNDATETIME
     *
     * @param returndatetime the value for order_info.RETURNDATETIME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setReturndatetime(String returndatetime) {
        this.returndatetime = returndatetime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.PICKUP_STORE_SEQ
     *
     * @return the value of order_info.PICKUP_STORE_SEQ
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getPickupStoreSeq() {
        return pickupStoreSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.PICKUP_STORE_SEQ
     *
     * @param pickupStoreSeq the value for order_info.PICKUP_STORE_SEQ
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setPickupStoreSeq(String pickupStoreSeq) {
        this.pickupStoreSeq = pickupStoreSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.RETURN_STORE_SEQ
     *
     * @return the value of order_info.RETURN_STORE_SEQ
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getReturnStoreSeq() {
        return returnStoreSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.RETURN_STORE_SEQ
     *
     * @param returnStoreSeq the value for order_info.RETURN_STORE_SEQ
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setReturnStoreSeq(String returnStoreSeq) {
        this.returnStoreSeq = returnStoreSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.RETURN_MILEAGE
     *
     * @return the value of order_info.RETURN_MILEAGE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getReturnMileage() {
        return returnMileage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.RETURN_MILEAGE
     *
     * @param returnMileage the value for order_info.RETURN_MILEAGE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setReturnMileage(Double returnMileage) {
        this.returnMileage = returnMileage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.GET_MILEAGE
     *
     * @return the value of order_info.GET_MILEAGE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getGetMileage() {
        return getMileage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.GET_MILEAGE
     *
     * @param getMileage the value for order_info.GET_MILEAGE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setGetMileage(Double getMileage) {
        this.getMileage = getMileage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.PURPOSE
     *
     * @return the value of order_info.PURPOSE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getPurpose() {
        return purpose;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.PURPOSE
     *
     * @param purpose the value for order_info.PURPOSE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setPurpose(Double purpose) {
        this.purpose = purpose;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.PRE_AUTHORISED_TYPE
     *
     * @return the value of order_info.PRE_AUTHORISED_TYPE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getPreAuthorisedType() {
        return preAuthorisedType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.PRE_AUTHORISED_TYPE
     *
     * @param preAuthorisedType the value for order_info.PRE_AUTHORISED_TYPE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setPreAuthorisedType(Double preAuthorisedType) {
        this.preAuthorisedType = preAuthorisedType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.AUTHORIZATION_PRICE
     *
     * @return the value of order_info.AUTHORIZATION_PRICE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getAuthorizationPrice() {
        return authorizationPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.AUTHORIZATION_PRICE
     *
     * @param authorizationPrice the value for order_info.AUTHORIZATION_PRICE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setAuthorizationPrice(Double authorizationPrice) {
        this.authorizationPrice = authorizationPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.PAY_TRADE_NO
     *
     * @return the value of order_info.PAY_TRADE_NO
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getPayTradeNo() {
        return payTradeNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.PAY_TRADE_NO
     *
     * @param payTradeNo the value for order_info.PAY_TRADE_NO
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setPayTradeNo(String payTradeNo) {
        this.payTradeNo = payTradeNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.ADD_SERVICE_ITEMS
     *
     * @return the value of order_info.ADD_SERVICE_ITEMS
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getAddServiceItems() {
        return addServiceItems;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.ADD_SERVICE_ITEMS
     *
     * @param addServiceItems the value for order_info.ADD_SERVICE_ITEMS
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setAddServiceItems(String addServiceItems) {
        this.addServiceItems = addServiceItems;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.ILLEGAL_PRE_AUTHORISED_FLG
     *
     * @return the value of order_info.ILLEGAL_PRE_AUTHORISED_FLG
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getIllegalPreAuthorisedFlg() {
        return illegalPreAuthorisedFlg;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.ILLEGAL_PRE_AUTHORISED_FLG
     *
     * @param illegalPreAuthorisedFlg the value for order_info.ILLEGAL_PRE_AUTHORISED_FLG
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setIllegalPreAuthorisedFlg(Double illegalPreAuthorisedFlg) {
        this.illegalPreAuthorisedFlg = illegalPreAuthorisedFlg;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.ILLEGAL_PRE_AUTHORISED_AMOUNT
     *
     * @return the value of order_info.ILLEGAL_PRE_AUTHORISED_AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getIllegalPreAuthorisedAmount() {
        return illegalPreAuthorisedAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.ILLEGAL_PRE_AUTHORISED_AMOUNT
     *
     * @param illegalPreAuthorisedAmount the value for order_info.ILLEGAL_PRE_AUTHORISED_AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setIllegalPreAuthorisedAmount(Double illegalPreAuthorisedAmount) {
        this.illegalPreAuthorisedAmount = illegalPreAuthorisedAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.OTHER_AMOUNT
     *
     * @return the value of order_info.OTHER_AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getOtherAmount() {
        return otherAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.OTHER_AMOUNT
     *
     * @param otherAmount the value for order_info.OTHER_AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setOtherAmount(Double otherAmount) {
        this.otherAmount = otherAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.PAY_WAY
     *
     * @return the value of order_info.PAY_WAY
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getPayWay() {
        return payWay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.PAY_WAY
     *
     * @param payWay the value for order_info.PAY_WAY
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setPayWay(Double payWay) {
        this.payWay = payWay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.PRE_PAY_AMOUNT
     *
     * @return the value of order_info.PRE_PAY_AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getPrePayAmount() {
        return prePayAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.PRE_PAY_AMOUNT
     *
     * @param prePayAmount the value for order_info.PRE_PAY_AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setPrePayAmount(Double prePayAmount) {
        this.prePayAmount = prePayAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.OTHER_AMOUNT_TOTAL
     *
     * @return the value of order_info.OTHER_AMOUNT_TOTAL
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getOtherAmountTotal() {
        return otherAmountTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.OTHER_AMOUNT_TOTAL
     *
     * @param otherAmountTotal the value for order_info.OTHER_AMOUNT_TOTAL
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setOtherAmountTotal(Double otherAmountTotal) {
        this.otherAmountTotal = otherAmountTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.RENT_TOTAL
     *
     * @return the value of order_info.RENT_TOTAL
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getRentTotal() {
        return rentTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.RENT_TOTAL
     *
     * @param rentTotal the value for order_info.RENT_TOTAL
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setRentTotal(Double rentTotal) {
        this.rentTotal = rentTotal;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.AMOUNT
     *
     * @return the value of order_info.AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getAmount() {
        return amount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.AMOUNT
     *
     * @param amount the value for order_info.AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setAmount(Double amount) {
        this.amount = amount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.REAL_AMOUNT
     *
     * @return the value of order_info.REAL_AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getRealAmount() {
        return realAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.REAL_AMOUNT
     *
     * @param realAmount the value for order_info.REAL_AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setRealAmount(Double realAmount) {
        this.realAmount = realAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.EXEMPTION_AMOUNT
     *
     * @return the value of order_info.EXEMPTION_AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getExemptionAmount() {
        return exemptionAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.EXEMPTION_AMOUNT
     *
     * @param exemptionAmount the value for order_info.EXEMPTION_AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setExemptionAmount(Double exemptionAmount) {
        this.exemptionAmount = exemptionAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.EXEMPTION_REASON
     *
     * @return the value of order_info.EXEMPTION_REASON
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getExemptionReason() {
        return exemptionReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.EXEMPTION_REASON
     *
     * @param exemptionReason the value for order_info.EXEMPTION_REASON
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setExemptionReason(String exemptionReason) {
        this.exemptionReason = exemptionReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.COMPENSATE_AMOUNT
     *
     * @return the value of order_info.COMPENSATE_AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getCompensateAmount() {
        return compensateAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.COMPENSATE_AMOUNT
     *
     * @param compensateAmount the value for order_info.COMPENSATE_AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setCompensateAmount(Double compensateAmount) {
        this.compensateAmount = compensateAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.INVOICE_TYPE
     *
     * @return the value of order_info.INVOICE_TYPE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getInvoiceType() {
        return invoiceType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.INVOICE_TYPE
     *
     * @param invoiceType the value for order_info.INVOICE_TYPE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setInvoiceType(Double invoiceType) {
        this.invoiceType = invoiceType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.CREDIT_NO
     *
     * @return the value of order_info.CREDIT_NO
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getCreditNo() {
        return creditNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.CREDIT_NO
     *
     * @param creditNo the value for order_info.CREDIT_NO
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setCreditNo(String creditNo) {
        this.creditNo = creditNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.PAYMENT_STATUS
     *
     * @return the value of order_info.PAYMENT_STATUS
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getPaymentStatus() {
        return paymentStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.PAYMENT_STATUS
     *
     * @param paymentStatus the value for order_info.PAYMENT_STATUS
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setPaymentStatus(Double paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.ORDER_TYPE
     *
     * @return the value of order_info.ORDER_TYPE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getOrderType() {
        return orderType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.ORDER_TYPE
     *
     * @param orderType the value for order_info.ORDER_TYPE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setOrderType(Double orderType) {
        this.orderType = orderType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.RETURN_SOC
     *
     * @return the value of order_info.RETURN_SOC
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getReturnSoc() {
        return returnSoc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.RETURN_SOC
     *
     * @param returnSoc the value for order_info.RETURN_SOC
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setReturnSoc(Double returnSoc) {
        this.returnSoc = returnSoc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.GET_SOC
     *
     * @return the value of order_info.GET_SOC
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getGetSoc() {
        return getSoc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.GET_SOC
     *
     * @param getSoc the value for order_info.GET_SOC
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setGetSoc(Double getSoc) {
        this.getSoc = getSoc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.BILL_TIME
     *
     * @return the value of order_info.BILL_TIME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getBillTime() {
        return billTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.BILL_TIME
     *
     * @param billTime the value for order_info.BILL_TIME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setBillTime(Double billTime) {
        this.billTime = billTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.COST_TIME
     *
     * @return the value of order_info.COST_TIME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getCostTime() {
        return costTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.COST_TIME
     *
     * @param costTime the value for order_info.COST_TIME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setCostTime(Double costTime) {
        this.costTime = costTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.EXEMPTION_TIME
     *
     * @return the value of order_info.EXEMPTION_TIME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getExemptionTime() {
        return exemptionTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.EXEMPTION_TIME
     *
     * @param exemptionTime the value for order_info.EXEMPTION_TIME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setExemptionTime(Double exemptionTime) {
        this.exemptionTime = exemptionTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.RETURN_REMARK
     *
     * @return the value of order_info.RETURN_REMARK
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getReturnRemark() {
        return returnRemark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.RETURN_REMARK
     *
     * @param returnRemark the value for order_info.RETURN_REMARK
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setReturnRemark(String returnRemark) {
        this.returnRemark = returnRemark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.GET_REMARK
     *
     * @return the value of order_info.GET_REMARK
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getGetRemark() {
        return getRemark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.GET_REMARK
     *
     * @param getRemark the value for order_info.GET_REMARK
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setGetRemark(String getRemark) {
        this.getRemark = getRemark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.SMS_STATUS
     *
     * @return the value of order_info.SMS_STATUS
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getSmsStatus() {
        return smsStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.SMS_STATUS
     *
     * @param smsStatus the value for order_info.SMS_STATUS
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setSmsStatus(Double smsStatus) {
        this.smsStatus = smsStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.ILLEGAL_SEQ
     *
     * @return the value of order_info.ILLEGAL_SEQ
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getIllegalSeq() {
        return illegalSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.ILLEGAL_SEQ
     *
     * @param illegalSeq the value for order_info.ILLEGAL_SEQ
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setIllegalSeq(String illegalSeq) {
        this.illegalSeq = illegalSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.REMARK
     *
     * @return the value of order_info.REMARK
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getRemark() {
        return remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.REMARK
     *
     * @param remark the value for order_info.REMARK
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.RENT_SHOPER
     *
     * @return the value of order_info.RENT_SHOPER
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getRentShoper() {
        return rentShoper;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.RENT_SHOPER
     *
     * @param rentShoper the value for order_info.RENT_SHOPER
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setRentShoper(String rentShoper) {
        this.rentShoper = rentShoper;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.ACCOUNT_SHOPER
     *
     * @return the value of order_info.ACCOUNT_SHOPER
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getAccountShoper() {
        return accountShoper;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.ACCOUNT_SHOPER
     *
     * @param accountShoper the value for order_info.ACCOUNT_SHOPER
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setAccountShoper(String accountShoper) {
        this.accountShoper = accountShoper;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.ORIGIN
     *
     * @return the value of order_info.ORIGIN
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getOrigin() {
        return origin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.ORIGIN
     *
     * @param origin the value for order_info.ORIGIN
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setOrigin(String origin) {
        this.origin = origin;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.CANCEL_METHOD
     *
     * @return the value of order_info.CANCEL_METHOD
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getCancelMethod() {
        return cancelMethod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.CANCEL_METHOD
     *
     * @param cancelMethod the value for order_info.CANCEL_METHOD
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setCancelMethod(Double cancelMethod) {
        this.cancelMethod = cancelMethod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.ORG_FREE
     *
     * @return the value of order_info.ORG_FREE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getOrgFree() {
        return orgFree;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.ORG_FREE
     *
     * @param orgFree the value for order_info.ORG_FREE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setOrgFree(Double orgFree) {
        this.orgFree = orgFree;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.CREATED_TIME
     *
     * @return the value of order_info.CREATED_TIME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getCreatedTime() {
        return createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.CREATED_TIME
     *
     * @param createdTime the value for order_info.CREATED_TIME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.CREATED_USER
     *
     * @return the value of order_info.CREATED_USER
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getCreatedUser() {
        return createdUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.CREATED_USER
     *
     * @param createdUser the value for order_info.CREATED_USER
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setCreatedUser(String createdUser) {
        this.createdUser = createdUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.UPDATED_TIME
     *
     * @return the value of order_info.UPDATED_TIME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getUpdatedTime() {
        return updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.UPDATED_TIME
     *
     * @param updatedTime the value for order_info.UPDATED_TIME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setUpdatedTime(String updatedTime) {
        this.updatedTime = updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.UPDATED_USER
     *
     * @return the value of order_info.UPDATED_USER
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getUpdatedUser() {
        return updatedUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.UPDATED_USER
     *
     * @param updatedUser the value for order_info.UPDATED_USER
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setUpdatedUser(String updatedUser) {
        this.updatedUser = updatedUser;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.PAY_ITEM
     *
     * @return the value of order_info.PAY_ITEM
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getPayItem() {
        return payItem;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.PAY_ITEM
     *
     * @param payItem the value for order_info.PAY_ITEM
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setPayItem(Double payItem) {
        this.payItem = payItem;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.CANCEL_FLAG
     *
     * @return the value of order_info.CANCEL_FLAG
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getCancelFlag() {
        return cancelFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.CANCEL_FLAG
     *
     * @param cancelFlag the value for order_info.CANCEL_FLAG
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setCancelFlag(Double cancelFlag) {
        this.cancelFlag = cancelFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.ITEM_SEQ
     *
     * @return the value of order_info.ITEM_SEQ
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getItemSeq() {
        return itemSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.ITEM_SEQ
     *
     * @param itemSeq the value for order_info.ITEM_SEQ
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setItemSeq(Double itemSeq) {
        this.itemSeq = itemSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.VEHICLE_ORG_ID
     *
     * @return the value of order_info.VEHICLE_ORG_ID
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getVehicleOrgId() {
        return vehicleOrgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.VEHICLE_ORG_ID
     *
     * @param vehicleOrgId the value for order_info.VEHICLE_ORG_ID
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setVehicleOrgId(String vehicleOrgId) {
        this.vehicleOrgId = vehicleOrgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.RESTRICT_FLAT
     *
     * @return the value of order_info.RESTRICT_FLAT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getRestrictFlat() {
        return restrictFlat;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.RESTRICT_FLAT
     *
     * @param restrictFlat the value for order_info.RESTRICT_FLAT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setRestrictFlat(Double restrictFlat) {
        this.restrictFlat = restrictFlat;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.RESTRICT_CONTENT
     *
     * @return the value of order_info.RESTRICT_CONTENT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getRestrictContent() {
        return restrictContent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.RESTRICT_CONTENT
     *
     * @param restrictContent the value for order_info.RESTRICT_CONTENT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setRestrictContent(String restrictContent) {
        this.restrictContent = restrictContent;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.RETURN_ERROR_CODE
     *
     * @return the value of order_info.RETURN_ERROR_CODE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getReturnErrorCode() {
        return returnErrorCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.RETURN_ERROR_CODE
     *
     * @param returnErrorCode the value for order_info.RETURN_ERROR_CODE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setReturnErrorCode(Double returnErrorCode) {
        this.returnErrorCode = returnErrorCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.BILL_TIME_PRECHARGE
     *
     * @return the value of order_info.BILL_TIME_PRECHARGE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getBillTimePrecharge() {
        return billTimePrecharge;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.BILL_TIME_PRECHARGE
     *
     * @param billTimePrecharge the value for order_info.BILL_TIME_PRECHARGE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setBillTimePrecharge(Double billTimePrecharge) {
        this.billTimePrecharge = billTimePrecharge;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.RETURN_CONDITION
     *
     * @return the value of order_info.RETURN_CONDITION
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getReturnCondition() {
        return returnCondition;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.RETURN_CONDITION
     *
     * @param returnCondition the value for order_info.RETURN_CONDITION
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setReturnCondition(Double returnCondition) {
        this.returnCondition = returnCondition;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.ABNORMAL_TYPE
     *
     * @return the value of order_info.ABNORMAL_TYPE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getAbnormalType() {
        return abnormalType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.ABNORMAL_TYPE
     *
     * @param abnormalType the value for order_info.ABNORMAL_TYPE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setAbnormalType(Double abnormalType) {
        this.abnormalType = abnormalType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.USER_COUPON_SEQ
     *
     * @return the value of order_info.USER_COUPON_SEQ
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getUserCouponSeq() {
        return userCouponSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.USER_COUPON_SEQ
     *
     * @param userCouponSeq the value for order_info.USER_COUPON_SEQ
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setUserCouponSeq(Double userCouponSeq) {
        this.userCouponSeq = userCouponSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.DISCOUNT
     *
     * @return the value of order_info.DISCOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getDiscount() {
        return discount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.DISCOUNT
     *
     * @param discount the value for order_info.DISCOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setDiscount(Double discount) {
        this.discount = discount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.OPERATION_ORG_ID
     *
     * @return the value of order_info.OPERATION_ORG_ID
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getOperationOrgId() {
        return operationOrgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.OPERATION_ORG_ID
     *
     * @param operationOrgId the value for order_info.OPERATION_ORG_ID
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setOperationOrgId(String operationOrgId) {
        this.operationOrgId = operationOrgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.ORDER_PROPERTY
     *
     * @return the value of order_info.ORDER_PROPERTY
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Integer getOrderProperty() {
        return orderProperty;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.ORDER_PROPERTY
     *
     * @param orderProperty the value for order_info.ORDER_PROPERTY
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setOrderProperty(Integer orderProperty) {
        this.orderProperty = orderProperty;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.PICKUP_WAY
     *
     * @return the value of order_info.PICKUP_WAY
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Integer getPickupWay() {
        return pickupWay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.PICKUP_WAY
     *
     * @param pickupWay the value for order_info.PICKUP_WAY
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setPickupWay(Integer pickupWay) {
        this.pickupWay = pickupWay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.RETURN_WAY
     *
     * @return the value of order_info.RETURN_WAY
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Integer getReturnWay() {
        return returnWay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.RETURN_WAY
     *
     * @param returnWay the value for order_info.RETURN_WAY
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setReturnWay(Integer returnWay) {
        this.returnWay = returnWay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.LOCATION_TYPE
     *
     * @return the value of order_info.LOCATION_TYPE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getLocationType() {
        return locationType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.LOCATION_TYPE
     *
     * @param locationType the value for order_info.LOCATION_TYPE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setLocationType(Double locationType) {
        this.locationType = locationType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.ISVEHASSESS
     *
     * @return the value of order_info.ISVEHASSESS
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Boolean getIsvehassess() {
        return isvehassess;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.ISVEHASSESS
     *
     * @param isvehassess the value for order_info.ISVEHASSESS
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setIsvehassess(Boolean isvehassess) {
        this.isvehassess = isvehassess;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.PICKVEH_AMOUNT
     *
     * @return the value of order_info.PICKVEH_AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getPickvehAmount() {
        return pickvehAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.PICKVEH_AMOUNT
     *
     * @param pickvehAmount the value for order_info.PICKVEH_AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setPickvehAmount(Double pickvehAmount) {
        this.pickvehAmount = pickvehAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.BANCHE_FREE
     *
     * @return the value of order_info.BANCHE_FREE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Boolean getBancheFree() {
        return bancheFree;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.BANCHE_FREE
     *
     * @param bancheFree the value for order_info.BANCHE_FREE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setBancheFree(Boolean bancheFree) {
        this.bancheFree = bancheFree;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.RETURNVEH_AMOUNT
     *
     * @return the value of order_info.RETURNVEH_AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getReturnvehAmount() {
        return returnvehAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.RETURNVEH_AMOUNT
     *
     * @param returnvehAmount the value for order_info.RETURNVEH_AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setReturnvehAmount(Double returnvehAmount) {
        this.returnvehAmount = returnvehAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.PARK_AMOUNT
     *
     * @return the value of order_info.PARK_AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getParkAmount() {
        return parkAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.PARK_AMOUNT
     *
     * @param parkAmount the value for order_info.PARK_AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setParkAmount(Double parkAmount) {
        this.parkAmount = parkAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.UNIT_PRICE
     *
     * @return the value of order_info.UNIT_PRICE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getUnitPrice() {
        return unitPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.UNIT_PRICE
     *
     * @param unitPrice the value for order_info.UNIT_PRICE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setUnitPrice(Double unitPrice) {
        this.unitPrice = unitPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.MEMDISCOUNT
     *
     * @return the value of order_info.MEMDISCOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getMemdiscount() {
        return memdiscount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.MEMDISCOUNT
     *
     * @param memdiscount the value for order_info.MEMDISCOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setMemdiscount(Double memdiscount) {
        this.memdiscount = memdiscount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.ORDER_AGENCY
     *
     * @return the value of order_info.ORDER_AGENCY
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getOrderAgency() {
        return orderAgency;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.ORDER_AGENCY
     *
     * @param orderAgency the value for order_info.ORDER_AGENCY
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setOrderAgency(String orderAgency) {
        this.orderAgency = orderAgency;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.ACTIVITY_TYPE
     *
     * @return the value of order_info.ACTIVITY_TYPE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Integer getActivityType() {
        return activityType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.ACTIVITY_TYPE
     *
     * @param activityType the value for order_info.ACTIVITY_TYPE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setActivityType(Integer activityType) {
        this.activityType = activityType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.PAY_TIME
     *
     * @return the value of order_info.PAY_TIME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Date getPayTime() {
        return payTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.PAY_TIME
     *
     * @param payTime the value for order_info.PAY_TIME
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.PAY_STATUS_FROM_APP
     *
     * @return the value of order_info.PAY_STATUS_FROM_APP
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Integer getPayStatusFromApp() {
        return payStatusFromApp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.PAY_STATUS_FROM_APP
     *
     * @param payStatusFromApp the value for order_info.PAY_STATUS_FROM_APP
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setPayStatusFromApp(Integer payStatusFromApp) {
        this.payStatusFromApp = payStatusFromApp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.OUT_TRADE_SEQ
     *
     * @return the value of order_info.OUT_TRADE_SEQ
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getOutTradeSeq() {
        return outTradeSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.OUT_TRADE_SEQ
     *
     * @param outTradeSeq the value for order_info.OUT_TRADE_SEQ
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setOutTradeSeq(String outTradeSeq) {
        this.outTradeSeq = outTradeSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.MARKET_ACTIVITY_TYPE
     *
     * @return the value of order_info.MARKET_ACTIVITY_TYPE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Integer getMarketActivityType() {
        return marketActivityType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.MARKET_ACTIVITY_TYPE
     *
     * @param marketActivityType the value for order_info.MARKET_ACTIVITY_TYPE
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setMarketActivityType(Integer marketActivityType) {
        this.marketActivityType = marketActivityType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.DEPOSIT_DEDUCT_AMOUNT
     *
     * @return the value of order_info.DEPOSIT_DEDUCT_AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public Double getDepositDeductAmount() {
        return depositDeductAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.DEPOSIT_DEDUCT_AMOUNT
     *
     * @param depositDeductAmount the value for order_info.DEPOSIT_DEDUCT_AMOUNT
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setDepositDeductAmount(Double depositDeductAmount) {
        this.depositDeductAmount = depositDeductAmount;
    }
}