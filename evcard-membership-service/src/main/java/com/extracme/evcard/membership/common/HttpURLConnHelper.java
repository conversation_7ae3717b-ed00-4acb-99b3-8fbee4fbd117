package com.extracme.evcard.membership.common;

import org.apache.commons.io.IOUtils;
import org.apache.pdfbox.io.MemoryUsageSetting;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: Elin
 * \* Date: 2018/2/9
 * \* Time: 14:36
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 获取网络文件
 * \
 */
public class HttpURLConnHelper {

    public static byte[] readInputStream(InputStream inputStream) throws IOException {
        byte[] buffer = new byte[1024];
        int len = 0;
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        while ((len = inputStream.read(buffer)) != -1) {
            bos.write(buffer, 0, len);
        }
        bos.close();
        return bos.toByteArray();
    }

    public static byte[] getImageFromURL(String urlPath) {
        byte[] data = null;
        BufferedInputStream bis = null;
        HttpURLConnection conn = null;
        try {
            // 获取输入流
            URL url = new URL(urlPath);
            conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(5000);
            conn.connect();
            if (conn.getResponseCode() == 200) {
                // 服务器有响应后，会将访问的url页面中的内容放进inputStream中，使用httpConn就可以获取到这个字节流
                bis = new BufferedInputStream(conn.getInputStream());
                data = readInputStream(bis);
                return data;
            }
        } catch (MalformedURLException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            IOUtils.closeQuietly(bis);
            IOUtils.close(conn);
        }
        return data;
    }

    /**
     * 根据文件url转化为文件流 ，上传到OSS
     *
     * @param fileAddress
     * @param fileUrl
     */
    public static void uploadFileToOSSByInputStream(String fileAddress, String fileUrl) {
        HttpURLConnection con = null;
        InputStream stream = null;
        try {
            URL url = new URL(fileAddress);
            con = (HttpURLConnection) url.openConnection();
            stream = con.getInputStream();
            UploadImgUtil.uploadStreamSyn(stream, fileUrl.toString());
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            IOUtils.closeQuietly(stream);
            IOUtils.close(con);
        }
    }

    /**
     * 将网络pdf文件，按每页拆分图片,返回每张图片路径
     *
     * @param address 文件地址 例：/member_provision/SZ0030.pdf
     */
    public static String returnAllPngUrl(String address) {
        HttpURLConnection con = null;
        InputStream stream = null;
        PDDocument pdf = null;
        String fileNames = "";
        try {
            //拼接完整的文件地址
            String fileAddress = ComUtil.getFileFullPath(address);
            URL url = new URL(fileAddress);
            con = (HttpURLConnection) url.openConnection();
            stream = con.getInputStream();
            //MemoryUsageSetting.setupTempFileOnly() 允许将源pdf暂存在磁盘中，而非内存中。
            pdf = PDDocument.load(stream, MemoryUsageSetting.setupTempFileOnly());
            //pdfRenderer.setSubsamplingAllowed(true);
            int actSize = pdf.getNumberOfPages();
            //截取名称作为OSS bucket ,png 图片分版本保存
            String name = address.substring(address.lastIndexOf("/") + 1, address.lastIndexOf("."));
            //截取并拼接不包含后缀的文件路径
            String fileName = address.substring(0, address.lastIndexOf(".")).concat("/").concat(name);
            StringBuffer buffer = new StringBuffer();
            for (int i = 0; i < actSize; i++) {
                String imgName = fileName + i + ".png";
                buffer.append(imgName);
                buffer.append(",");
            }
            fileNames = buffer.toString().substring(0, buffer.toString().length() - 1);
            System.out.println(fileNames);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            IOUtils.closeQuietly(pdf);
            IOUtils.closeQuietly(stream);
            IOUtils.close(con);
        }
        return fileNames;
    }

    /**
     * 将网络pdf文件，按每页写入png图片
     *
     * @param address 文件地址 例：/member_provision/SZ0030.pdf
     */
    public static void writeStreamToPng(String address) {
        HttpURLConnection con = null;
        InputStream stream = null;
        PDDocument pdf = null;
        String fileNames = "";
        try {
            //拼接完整的文件地址
            String fileAddress = ComUtil.getFileFullPath(address);
            URL url = new URL(fileAddress);
            con = (HttpURLConnection) url.openConnection();
            stream = con.getInputStream();
            /**
             * https://stackoverflow.com/questions/11301818/pdfbox-working-with-very-large-pdfs
             * 1. MemoryUsageSetting.setupTempFileOnly() 将设置缓冲内存使用仅使用大小不受限制的临时文件（无主内存）
             * 2. since 2.0.9 is subsampling (skip pixel lines/rows) with PDFRenderer.setSubsamplingAllowed(true)
             * 3. 增加启动参数  -Dsun.java2d.cmm=sun.java2d.cmm.kcms.KcmsServiceProvider
             * 4. 增加pdf.close
             *
             * 5. https://pdfbox.apache.org/blog/
             *    CVE-2021-31811, CVE-2021-31812 OutOfMemory and infinite loop
             *    --> 2.0.25
             */
            pdf = PDDocument.load(stream, MemoryUsageSetting.setupTempFileOnly());
            int actSize = pdf.getNumberOfPages();
            System.out.println("actSize:" + actSize);
            //截取名称作为OSS bucket ,png 图片分版本保存
            String name = address.substring(address.lastIndexOf("/") + 1, address.lastIndexOf("."));
            //截取并拼接不包含后缀的文件路径
            String fileName = address.substring(0, address.lastIndexOf(".")).concat("/").concat(name);
            PDFRenderer renderer = new PDFRenderer(pdf);
            /**
             * TODO DEBUG 确认此参数是否会带来其他问题，比如选然后图片缺失
             * 节省内存--->进行子采样（跳过像素行/行）
             */
            renderer.setSubsamplingAllowed(true);
            BufferedImage image = null;
            for (int i = 0; i < actSize; i++) {
                image = renderer.renderImage(i, 2.5f);
                String imgName = fileName + i + ".png";
                ByteArrayOutputStream os = new ByteArrayOutputStream();
                ImageIO.write(image, "PNG", os);
                InputStream inputStream = new ByteArrayInputStream(os.toByteArray());
                //写入图片并上传OOS
                UploadImgUtil.uploadStreamSyn(inputStream, imgName.toString());
                IOUtils.closeQuietly(inputStream);
                IOUtils.closeQuietly(os);
                image.flush();
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            IOUtils.closeQuietly(pdf);
            IOUtils.closeQuietly(stream);
            IOUtils.close(con);
        }
    }

//    public static void main(String[] args) {
//        String address = "/member_provision/SZ0030.pdf";
//        // /member_provision/png/SZ0030
//        String name = address.substring(address.lastIndexOf("/") + 1, address.lastIndexOf("."));
//        String fileName = address.substring(0, address.lastIndexOf(".")).concat("/").concat(name);
//        System.out.println(fileName);
//        returnAllPngUrl(address);
//    }
}