package com.extracme.evcard.membership.core.service.auth.login;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.bean.ProducerBean;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.common.EvcardTokenGenerator;
import com.extracme.evcard.membership.core.dao.MembershipClauseLogMapper;
import com.extracme.evcard.membership.core.dao.UserTokenMapper;
import com.extracme.evcard.membership.core.dto.JwtTokenDto;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.enums.MemberTypeEnum;
import com.extracme.evcard.membership.core.input.*;
import com.extracme.evcard.membership.core.model.MembershipClauseLog;
import com.extracme.evcard.membership.core.model.UserToken;
import com.extracme.evcard.membership.core.security.EnhancedTokenGenerator;

import javax.servlet.http.HttpServletRequest;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.membership.core.service.auth.IDescription;
import com.extracme.evcard.membership.core.service.auth.IThirdLogin;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.mq.bean.EventEnum;
import com.extracme.evcard.mq.bean.event.MemberRegister;
import com.extracme.evcard.protobuf.ProtobufUtil;
import com.extracme.evcard.rpc.enums.StatusCode;
import com.extracme.evcard.rpc.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.util.Date;
import java.util.UUID;

import static com.extracme.evcard.membership.common.ComUtil.pattern;

@Slf4j
public abstract class ThirdLoginAbstract implements IThirdLogin, IDescription {

    @Autowired
    private IMemberShipService memberShipService;
    @Resource
    private UserTokenMapper userTokenMapper;
    @Resource
    private EvcardTokenGenerator evcardTokenGenerator;
    @Autowired
    private EnhancedTokenGenerator enhancedTokenGenerator;
    @Autowired
    private DataSourceTransactionManager txManager;
    @Autowired
    private MembershipInfoMapper membershipInfoMapper;
    @Resource(name = "producer")
    private ProducerBean producer;
    @Value("${ons.raw.topic}")
    private String evcardRawDataTopic;
    @Autowired
    private MembershipClauseLogMapper membershipClauseLogMapper;

    /**
     * 是否启用JWT token（默认启用）
     */
    @Value("${jwt.enabled:true}")
    private boolean jwtEnabled;

    @Override
    public String getSecondAppKey() {
        return "";
    }

    @Override
    public String getDescription() {
        return "登录抽象类";
    }

    /**
     *  获取用户类型
     * @return 会员类型(0:外部会员 1：内部员工 2：虚拟用户）
     */
    protected int getMembershipType(){
        return MemberTypeEnum.OUT_USER.getValue();
    }

    @Override
    public ThirdLoginDto thirdLogin(ThirdLoginInput object) throws BusinessException {
        log.info("{}登录开始,SecondAppKey{},入参object={}", getDescription(),getSecondAppKey(),JSON.toJSONString(object));
        //初始化
        ThirdLoginDto result = new ThirdLoginDto();
        ThirdLoginContext context = new ThirdLoginContext();
        context.setInput(object);

        paramCheck(context);

        TransactionStatus transactionStatus = null;
        try {
            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
            transactionStatus = txManager.getTransaction(def);

            MembershipBasicInfo membershipInfo = null;
            if (userIsExist(context)) {
                membershipInfo = getExistedMembershipInfo(context);
                result.setNewUser(false);
            } else {
                result.setNewUser(true);
                membershipInfo = registerMembershipInfo(context);
            }

            context.setNewUser(result.isNewUser());
            if (membershipInfo == null) {
                log.error("{}登录失败,SecondAppKey{},原因：membershipInfo ==null，入参object={}",  getDescription(),getSecondAppKey(),JSON.toJSONString(object));
                throw new BusinessException(-1, "登录失败");
            }

            afterGetMembershipInfo(context);

            // 是否 需要token
            if (isNeedToken(object)) {
                String token = getToken(context);
                result.setToken(token);
            }
            txManager.commit(transactionStatus);

            log.info("{}登录完成,SecondAppKey{},事务commit成功。 入参object={}", getDescription(),getSecondAppKey(),JSON.toJSONString(object));
            result.setMid(membershipInfo.getMid());
        } catch (Exception e) {
            if (null != transactionStatus) {
                txManager.rollback(transactionStatus);
            }
            log.error("{}登录失败,SecondAppKey{},原因：创建会员记录失败，入参object={}",getDescription(),getSecondAppKey(), JSON.toJSONString(object), e);
            if (e instanceof BusinessException) {
                throw e;
            } else {
                throw new BusinessException(StatusCode.SYSTEM_ERROR);
            }
        }

        try {
            asynchronousNotify(context);
        } catch (Exception e) {
            log.error("{}登录失败,SecondAppKey{},异步通知失败={}", getDescription(),getSecondAppKey(),JSON.toJSONString(object), e);
        }
        return result;
    }



    /**
     * 异步通知
     * @param context
     */
    protected void asynchronousNotify(ThirdLoginContext context){
        return;
    }

    public boolean isNeedToken(ThirdLoginInput object){
        return true;
    }

    /**
     *  获取用户后的操作
     * @param context
     */
    public void afterGetMembershipInfo(ThirdLoginContext context) throws BusinessException{

    }

    @Override
    public EvcardTokenDto getThirdAccessToken(GetThirdAccessTokenInput input) throws BusinessException {
        String mid = input.getMid();
        String appKey = input.getAppKey();
        MembershipBasicInfo membership = memberShipService.getUserBasicInfo(mid);
        if (membership != null) {
            return getToken(appKey, membership);
        } else {
            return null;
        }
    }

    /**
     * 用户不存在，注册新用户
     *
     * @param context
     * @return
     * @throws BusinessException
     */
    public MembershipBasicInfo registerMembershipInfo(ThirdLoginContext context) throws BusinessException {
        MembershipBasicInfo membershipBasicInfo = doRegisterMembershipInfo(context);
        afterRegister(context);
        return membershipBasicInfo;
    }

    /**
     * Evcard注册用户
     *
     * @param context
     * @return
     * @throws BusinessException
     */
    protected abstract MembershipBasicInfo doRegisterMembershipInfo(ThirdLoginContext context) throws BusinessException;

    /**
     * 注册成功后 处理事件
     *
     * @param context
     */
    protected void afterRegister(ThirdLoginContext context) {
        sendMessage(context);
    }

    /**
     * 推送会员注册事件
     *
     * @param context
     */
    public void sendMessage(ThirdLoginContext context) {
        MembershipBasicInfo member = context.getMembershipBasicInfo();
        try {
            String cityName = context.getOtherDto().getCityName();
            MemberRegister memberRegister = new MemberRegister();
            memberRegister.setAuthId(member.getAuthId());
            memberRegister.setMemberType(0);
            memberRegister.setMobilePhone(member.getMobilePhone().trim());
            memberRegister.setDataOrigin(String.valueOf(member.getDataOrigin()));
            memberRegister.setCity(cityName);
            memberRegister.setCreatedTime(new Date());
            memberRegister.setAppKey(member.getAppKey());
            byte[] messageBody = ProtobufUtil.serializeProtobuf(memberRegister);
            String messageKey = "membership#" + UUID.randomUUID().toString().replace("-", "");
            Message msg = new Message(evcardRawDataTopic, EventEnum.MEMBER_REGISTER.getTag(), messageBody);
            msg.setKey(messageKey);
            producer.send(msg);
        } catch (Exception e) {
            log.error("第三方渠道注册登录，推送会员注册事件失败，member=[{}]", JSON.toJSONString(member), e);
        }
    }

    /**
     * 用户已存在，获取用户信息
     *
     * @param context
     * @return
     * @throws BusinessException
     */
    public MembershipBasicInfo getExistedMembershipInfo(ThirdLoginContext context) throws BusinessException {
        MembershipBasicInfo membership = context.getMembershipBasicInfo();
        /**
         * 验证账号是否被注销
         */
        if (membership.getAccountStatus() == 1) {
            throw new BusinessException(StatusCode.MOBILE_FREEZING);
        }
        return membership;
    }


    /**
     * 第三方渠道 登录入参校验
     *
     * @param context
     * @return
     */
    public void paramCheck(ThirdLoginContext context) throws BusinessException {
        ThirdLoginInput input = context.getInput();
        String appKey = input.getAppKey();
        String mobilePhone = input.getMobilePhone();
        if (StringUtils.isBlank(mobilePhone) || StringUtils.isBlank(appKey)) {
            throw new BusinessException(-1, "入参格式不正确");
        }

        if (!pattern.matcher(mobilePhone).matches()) {
            throw new BusinessException(StatusCode.MOBILE_FORMATE_ERROR);
        }

        if (membershipInfoMapper.isExistAppKey(appKey) == 0) {
            throw new BusinessException(StatusCode.APPKEY_INVALID);
        }
    }

    /**
     * 判断用户是否存在
     *
     * @param context
     * @return
     */
    public boolean userIsExist(ThirdLoginContext context) throws BusinessException {
        ThirdLoginInput input = context.getInput();
        String mobilePhone = input.getMobilePhone();

        MembershipBasicInfo membership = memberShipService.getMembershipByPhone(mobilePhone, getMembershipType());
        if (membership != null) {
            context.setMembershipBasicInfo(membership);
            return true;
        } else {
            return false;
        }
    }

    /**
     * 获取用户token
     *
     * @param appKey
     * @param membership
     * @return
     * @throws BusinessException
     */
    public EvcardTokenDto getToken(String appKey, MembershipBasicInfo membership) throws BusinessException {
        EvcardTokenDto result = new EvcardTokenDto();
        log.info("获取evcard登录token，getToken,appKey[{}],membership[{}]", appKey, JSON.toJSONString(membership));

        try {
            if (jwtEnabled) {
                // 使用JWT token
                return getJwtToken(appKey, membership, null);
            } else {
                // 使用传统token（向后兼容）
                return getLegacyToken(appKey, membership);
            }
        } catch (Exception e) {
            log.error("获取用户token失败，appKey[{}],membership[{}]", appKey, JSON.toJSONString(membership), e);
            throw new BusinessException(-1, "获取用户token失败");
        }
    }

    /**
     * 获取JWT token
     */
    private EvcardTokenDto getJwtToken(String appKey, MembershipBasicInfo membership, HttpServletRequest request) throws BusinessException {
        try {
            JwtTokenDto jwtTokenDto = enhancedTokenGenerator.generateJwtTokenPair(membership, appKey, request);

            EvcardTokenDto result = new EvcardTokenDto();
            result.setToken(jwtTokenDto.getAccessToken());
            result.setExpireDate(jwtTokenDto.getAccessTokenExpireTime());

            log.info("成功生成JWT token，用户ID: {}, MID: {}", membership.getPkId(), membership.getMid());
            return result;
        } catch (Exception e) {
            log.error("生成JWT token失败，用户ID: {}, MID: {}", membership.getPkId(), membership.getMid(), e);
            throw new BusinessException(-1, "生成JWT token失败");
        }
    }

    /**
     * 获取传统token（向后兼容）
     */
    private EvcardTokenDto getLegacyToken(String appKey, MembershipBasicInfo membership) throws BusinessException {
        EvcardTokenDto result = new EvcardTokenDto();
        try {
            String operateName = StringUtils.isNotEmpty(membership.getName()) ? membership.getName() : "系统创建";
            Long pkId = membership.getPkId();
            UserToken userToken = userTokenMapper.selectByUserId(pkId);

            String token = evcardTokenGenerator.getToken(membership.getMid());
            //token 失效期 30天
            Date now = new Date();
            Date expireDate = ComUtil.addDay(now, 30);

            UserToken newUserToken = new UserToken();
            newUserToken.setToken(token);
            newUserToken.setStartTime(now);
            newUserToken.setExpiresTime(expireDate);
            newUserToken.setAppKey(appKey);
            newUserToken.setUserId(pkId);
            newUserToken.setUpdateTime(now);
            newUserToken.setUpdateOperId(pkId);
            newUserToken.setUpdateOperName(operateName);

            if (userToken == null) {
                newUserToken.setCreateTime(now);
                newUserToken.setCreateOperId(pkId);
                newUserToken.setCreateOperName(operateName);
                int insert = userTokenMapper.insertSelective(newUserToken);
                if (insert < 0) {
                    log.error("第三方渠道注册登录，插入用户token失败，membership{}", JSON.toJSONString(membership));
                    throw new BusinessException(-1, "用户登录失败");
                }
            } else {
                newUserToken.setId(userToken.getId());
                int updateFlag = userTokenMapper.updateByPrimaryKeySelective(newUserToken);
                if (updateFlag < 0) {
                    log.error("第三方渠道注册登录，更新用户token失败，membership{}", JSON.toJSONString(membership));
                    throw new BusinessException(-1, "用户登录失败");
                }
            }

            result.setExpireDate(expireDate);
            result.setToken(token);
            return result;
        } catch (Exception e) {
            log.error("第三方渠道注册登录，生成用户token异常，membership{}", JSON.toJSONString(membership), e);
            throw new BusinessException(-1, "用户登录失败");
        }
    }

    /**
     * 获取evcard登录token
     *
     * @param context
     * @return
     * @throws BusinessException
     */
    public String getToken(ThirdLoginContext context) throws BusinessException {
        ThirdLoginInput object = context.getInput();
        MembershipBasicInfo membership = context.getMembershipBasicInfo();
        log.info("获取evcard登录token，getToken,object[{}],membership[{}]",JSON.toJSONString(object),JSON.toJSONString(membership));

        try {
            if (jwtEnabled) {
                // 使用JWT token
                JwtTokenDto jwtTokenDto = enhancedTokenGenerator.generateJwtTokenPair(membership, object.getAppKey(), null);
                return jwtTokenDto.getAccessToken();
            } else {
                // 使用传统token（向后兼容）
                return getLegacyTokenString(context);
            }
        } catch (Exception e) {
            log.error("获取evcard登录token失败，object[{}],membership[{}]", JSON.toJSONString(object), JSON.toJSONString(membership), e);
            throw new BusinessException(-1, "获取evcard登录token失败");
        }
    }

    /**
     * 获取传统格式的token字符串
     */
    private String getLegacyTokenString(ThirdLoginContext context) throws BusinessException {
        ThirdLoginInput object = context.getInput();
        MembershipBasicInfo membership = context.getMembershipBasicInfo();

        try {
            String operateName = StringUtils.isNotEmpty(membership.getName()) ? membership.getName() : object.getUserName();
            String appKey = object.getAppKey();
            Long pkId = membership.getPkId();
            UserToken userToken = userTokenMapper.selectByUserId(pkId);
            String token = evcardTokenGenerator.getToken(membership.getMid());

            //token 失效期 30天
            Date now = new Date();
            Date expireDate = ComUtil.addDay(now, 30);

            UserToken newUserToken = new UserToken();
            newUserToken.setToken(token);
            newUserToken.setStartTime(now);
            newUserToken.setExpiresTime(expireDate);
            newUserToken.setAppKey(appKey);
            newUserToken.setUserId(pkId);
            newUserToken.setUpdateTime(now);
            newUserToken.setUpdateOperId(pkId);
            newUserToken.setUpdateOperName(operateName);

            if (userToken == null) {
                newUserToken.setCreateTime(now);
                newUserToken.setCreateOperId(pkId);
                newUserToken.setCreateOperName(operateName);
                int insert = userTokenMapper.insertSelective(newUserToken);
                if (insert < 0) {
                    log.error("第三方渠道注册登录，插入用户token失败，membership{}", JSON.toJSONString(membership));
                    throw new BusinessException(-1, "用户登录失败");
                }
            } else {
                newUserToken.setId(userToken.getId());
                int updateFlag = userTokenMapper.updateByPrimaryKeySelective(newUserToken);
                if (updateFlag < 0) {
                    log.error("第三方渠道注册登录，更新用户token失败，membership{}", JSON.toJSONString(membership));
                    throw new BusinessException(-1, "用户登录失败");
                }
            }
            return token;
        } catch (Exception e) {
            log.error("第三方渠道注册登录，生成用户token异常，membership{}", JSON.toJSONString(membership), e);
            throw new BusinessException(-1, "用户登录失败");
        }
    }

    /**
     * 插入会员条款记录
     *
     * @param authId
     * @param operator
     * @param version
     */
    public int insertMemberClaseLog(String authId, String operator, String version) {
        MembershipClauseLog membershipClauseLog = new MembershipClauseLog();
        membershipClauseLog.setAuthid(authId);
        membershipClauseLog.setCreateOperName(operator);
        membershipClauseLog.setUpdateOperName(operator);
        membershipClauseLog.setCreateTime(new Date());
        membershipClauseLog.setUpdateTime(new Date());
        membershipClauseLog.setVersion(version);
        return membershipClauseLogMapper.insertSelective(membershipClauseLog);
    }

}
