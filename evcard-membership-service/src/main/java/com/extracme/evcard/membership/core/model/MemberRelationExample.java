package com.extracme.evcard.membership.core.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MemberRelationExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public MemberRelationExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andPkId1IsNull() {
            addCriterion("pk_id_1 is null");
            return (Criteria) this;
        }

        public Criteria andPkId1IsNotNull() {
            addCriterion("pk_id_1 is not null");
            return (Criteria) this;
        }

        public Criteria andPkId1EqualTo(Long value) {
            addCriterion("pk_id_1 =", value, "pkId1");
            return (Criteria) this;
        }

        public Criteria andPkId1NotEqualTo(Long value) {
            addCriterion("pk_id_1 <>", value, "pkId1");
            return (Criteria) this;
        }

        public Criteria andPkId1GreaterThan(Long value) {
            addCriterion("pk_id_1 >", value, "pkId1");
            return (Criteria) this;
        }

        public Criteria andPkId1GreaterThanOrEqualTo(Long value) {
            addCriterion("pk_id_1 >=", value, "pkId1");
            return (Criteria) this;
        }

        public Criteria andPkId1LessThan(Long value) {
            addCriterion("pk_id_1 <", value, "pkId1");
            return (Criteria) this;
        }

        public Criteria andPkId1LessThanOrEqualTo(Long value) {
            addCriterion("pk_id_1 <=", value, "pkId1");
            return (Criteria) this;
        }

        public Criteria andPkId1In(List<Long> values) {
            addCriterion("pk_id_1 in", values, "pkId1");
            return (Criteria) this;
        }

        public Criteria andPkId1NotIn(List<Long> values) {
            addCriterion("pk_id_1 not in", values, "pkId1");
            return (Criteria) this;
        }

        public Criteria andPkId1Between(Long value1, Long value2) {
            addCriterion("pk_id_1 between", value1, value2, "pkId1");
            return (Criteria) this;
        }

        public Criteria andPkId1NotBetween(Long value1, Long value2) {
            addCriterion("pk_id_1 not between", value1, value2, "pkId1");
            return (Criteria) this;
        }

        public Criteria andPkId2IsNull() {
            addCriterion("pk_id_2 is null");
            return (Criteria) this;
        }

        public Criteria andPkId2IsNotNull() {
            addCriterion("pk_id_2 is not null");
            return (Criteria) this;
        }

        public Criteria andPkId2EqualTo(Long value) {
            addCriterion("pk_id_2 =", value, "pkId2");
            return (Criteria) this;
        }

        public Criteria andPkId2NotEqualTo(Long value) {
            addCriterion("pk_id_2 <>", value, "pkId2");
            return (Criteria) this;
        }

        public Criteria andPkId2GreaterThan(Long value) {
            addCriterion("pk_id_2 >", value, "pkId2");
            return (Criteria) this;
        }

        public Criteria andPkId2GreaterThanOrEqualTo(Long value) {
            addCriterion("pk_id_2 >=", value, "pkId2");
            return (Criteria) this;
        }

        public Criteria andPkId2LessThan(Long value) {
            addCriterion("pk_id_2 <", value, "pkId2");
            return (Criteria) this;
        }

        public Criteria andPkId2LessThanOrEqualTo(Long value) {
            addCriterion("pk_id_2 <=", value, "pkId2");
            return (Criteria) this;
        }

        public Criteria andPkId2In(List<Long> values) {
            addCriterion("pk_id_2 in", values, "pkId2");
            return (Criteria) this;
        }

        public Criteria andPkId2NotIn(List<Long> values) {
            addCriterion("pk_id_2 not in", values, "pkId2");
            return (Criteria) this;
        }

        public Criteria andPkId2Between(Long value1, Long value2) {
            addCriterion("pk_id_2 between", value1, value2, "pkId2");
            return (Criteria) this;
        }

        public Criteria andPkId2NotBetween(Long value1, Long value2) {
            addCriterion("pk_id_2 not between", value1, value2, "pkId2");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Integer value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Integer value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Integer value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Integer value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Integer value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Integer> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Integer> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Integer value1, Integer value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdIsNull() {
            addCriterion("create_oper_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdIsNotNull() {
            addCriterion("create_oper_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdEqualTo(Long value) {
            addCriterion("create_oper_id =", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdNotEqualTo(Long value) {
            addCriterion("create_oper_id <>", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdGreaterThan(Long value) {
            addCriterion("create_oper_id >", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdGreaterThanOrEqualTo(Long value) {
            addCriterion("create_oper_id >=", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdLessThan(Long value) {
            addCriterion("create_oper_id <", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdLessThanOrEqualTo(Long value) {
            addCriterion("create_oper_id <=", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdIn(List<Long> values) {
            addCriterion("create_oper_id in", values, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdNotIn(List<Long> values) {
            addCriterion("create_oper_id not in", values, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdBetween(Long value1, Long value2) {
            addCriterion("create_oper_id between", value1, value2, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdNotBetween(Long value1, Long value2) {
            addCriterion("create_oper_id not between", value1, value2, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameIsNull() {
            addCriterion("create_oper_name is null");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameIsNotNull() {
            addCriterion("create_oper_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameEqualTo(String value) {
            addCriterion("create_oper_name =", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotEqualTo(String value) {
            addCriterion("create_oper_name <>", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameGreaterThan(String value) {
            addCriterion("create_oper_name >", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameGreaterThanOrEqualTo(String value) {
            addCriterion("create_oper_name >=", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameLessThan(String value) {
            addCriterion("create_oper_name <", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameLessThanOrEqualTo(String value) {
            addCriterion("create_oper_name <=", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameLike(String value) {
            addCriterion("create_oper_name like", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotLike(String value) {
            addCriterion("create_oper_name not like", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameIn(List<String> values) {
            addCriterion("create_oper_name in", values, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotIn(List<String> values) {
            addCriterion("create_oper_name not in", values, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameBetween(String value1, String value2) {
            addCriterion("create_oper_name between", value1, value2, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotBetween(String value1, String value2) {
            addCriterion("create_oper_name not between", value1, value2, "createOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdIsNull() {
            addCriterion("update_oper_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdIsNotNull() {
            addCriterion("update_oper_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdEqualTo(Long value) {
            addCriterion("update_oper_id =", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdNotEqualTo(Long value) {
            addCriterion("update_oper_id <>", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdGreaterThan(Long value) {
            addCriterion("update_oper_id >", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdGreaterThanOrEqualTo(Long value) {
            addCriterion("update_oper_id >=", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdLessThan(Long value) {
            addCriterion("update_oper_id <", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdLessThanOrEqualTo(Long value) {
            addCriterion("update_oper_id <=", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdIn(List<Long> values) {
            addCriterion("update_oper_id in", values, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdNotIn(List<Long> values) {
            addCriterion("update_oper_id not in", values, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdBetween(Long value1, Long value2) {
            addCriterion("update_oper_id between", value1, value2, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdNotBetween(Long value1, Long value2) {
            addCriterion("update_oper_id not between", value1, value2, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameIsNull() {
            addCriterion("update_oper_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameIsNotNull() {
            addCriterion("update_oper_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameEqualTo(String value) {
            addCriterion("update_oper_name =", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotEqualTo(String value) {
            addCriterion("update_oper_name <>", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameGreaterThan(String value) {
            addCriterion("update_oper_name >", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameGreaterThanOrEqualTo(String value) {
            addCriterion("update_oper_name >=", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameLessThan(String value) {
            addCriterion("update_oper_name <", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameLessThanOrEqualTo(String value) {
            addCriterion("update_oper_name <=", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameLike(String value) {
            addCriterion("update_oper_name like", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotLike(String value) {
            addCriterion("update_oper_name not like", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameIn(List<String> values) {
            addCriterion("update_oper_name in", values, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotIn(List<String> values) {
            addCriterion("update_oper_name not in", values, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameBetween(String value1, String value2) {
            addCriterion("update_oper_name between", value1, value2, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotBetween(String value1, String value2) {
            addCriterion("update_oper_name not between", value1, value2, "updateOperName");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}