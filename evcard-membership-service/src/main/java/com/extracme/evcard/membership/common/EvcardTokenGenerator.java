package com.extracme.evcard.membership.common;

import com.extracme.evcard.membership.core.exception.RegisterException;
import com.extracme.evcard.redis.JedisUtil;
import com.extracme.evcard.rpc.enums.StatusCode;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.UUID;


@Component
public class EvcardTokenGenerator {

    private static final String TOKEN_REDIS_KEY = "evcardToken";
    private static final int TOKEN_EXPIRE_TIME = 60 * 60 + 20;

    /**
     *
     */
    public String getToken(String p1) {
        String sysDate = ComUtil.getSystemDate(ComUtil.DATE_TYPE4);
        StringBuffer sb = new StringBuffer();
        sb.append(TOKEN_REDIS_KEY);
        sb.append(":");
        sb.append(sysDate);
        sb.append("-");
        sb.append(UUID.randomUUID());
        return sb.toString();
        //return generateId(p1, TOKEN_REDIS_KEY);
    }


    // TODO 生产token，需要 特殊样式，能区分出 token
    private static String generateId(String prefix, String redisPrefix) {
        StringBuffer seq = new StringBuffer();
        String sysDate = ComUtil.getSystemDate(ComUtil.DATE_TYPE9);
        seq.append(prefix).append(sysDate);
        String redisKey = redisPrefix + ":" + seq.toString();
        Long serialNumber = JedisUtil.incr(redisKey);
        if (serialNumber == 1L) {
            JedisUtil.expire(redisKey, TOKEN_EXPIRE_TIME);
        }
        seq.append(String.format("%05d", serialNumber));
        return seq.toString();
    }

    public static void main(String[] args) {
        String sysDate = ComUtil.getSystemDate(ComUtil.DATE_TYPE4);
        StringBuffer sb = new StringBuffer();
        sb.append(TOKEN_REDIS_KEY);
        sb.append(":");
        sb.append(sysDate);
        sb.append("-");
        sb.append(UUID.randomUUID());
        System.out.println(sb.toString()); ;
    }
}
