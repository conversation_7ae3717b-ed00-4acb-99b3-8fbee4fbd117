<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.credit.dao.MmpCreditEventTypeMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.credit.model.MmpCreditEventType" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 22 10:46:53 CST 2017.
    -->
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="event_name" property="eventName" jdbcType="VARCHAR" />
    <result column="event_nature" property="eventNature" jdbcType="VARCHAR" />
    <result column="amount" property="amount" jdbcType="INTEGER" />
    <result column="event_desc" property="eventDesc" jdbcType="VARCHAR" />
    <result column="event_way" property="eventWay" jdbcType="INTEGER" />
    <result column="black_list" property="blackList" jdbcType="BIT" />
    <result column="misc_desc" property="miscDesc" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 22 10:46:53 CST 2017.
    -->
    id, event_name, event_nature, amount, event_desc, event_way, black_list, misc_desc, 
    status, create_time, create_oper_id, create_oper_name, update_time, update_oper_id, 
    update_oper_name
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 22 10:46:53 CST 2017.
    -->
    select 
    <include refid="Base_Column_List" />
    from  ${issSchema}.mmp_credit_event_type
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 22 10:46:53 CST 2017.
    -->
    delete from ${issSchema}.mmp_credit_event_type
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.membership.credit.model.MmpCreditEventType"
          useGeneratedKeys="true" keyProperty="id">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 22 10:46:53 CST 2017.
    -->
    insert into ${issSchema}.mmp_credit_event_type (id, event_name, event_nature,
      amount, event_desc, event_way, 
      black_list, misc_desc, status, 
      create_time, create_oper_id, create_oper_name, 
      update_time, update_oper_id, update_oper_name
      )
    values (#{id,jdbcType=BIGINT}, #{eventName,jdbcType=VARCHAR}, #{eventNature,jdbcType=VARCHAR}, 
      #{amount,jdbcType=INTEGER}, #{eventDesc,jdbcType=VARCHAR}, #{eventWay,jdbcType=INTEGER}, 
      #{blackList,jdbcType=BIT}, #{miscDesc,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, #{createOperName,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, #{updateOperName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.credit.model.MmpCreditEventType"
          useGeneratedKeys="true" keyProperty="id">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 22 10:46:53 CST 2017.
    -->
    insert into ${issSchema}.mmp_credit_event_type
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="eventName != null" >
        event_name,
      </if>
      <if test="eventNature != null" >
        event_nature,
      </if>
      <if test="amount != null" >
        amount,
      </if>
      <if test="eventDesc != null" >
        event_desc,
      </if>
      <if test="eventWay != null" >
        event_way,
      </if>
      <if test="blackList != null" >
        black_list,
      </if>
      <if test="miscDesc != null" >
        misc_desc,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateOperId != null" >
        update_oper_id,
      </if>
      <if test="updateOperName != null" >
        update_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="eventName != null" >
        #{eventName,jdbcType=VARCHAR},
      </if>
      <if test="eventNature != null" >
        #{eventNature,jdbcType=VARCHAR},
      </if>
      <if test="amount != null" >
        #{amount,jdbcType=INTEGER},
      </if>
      <if test="eventDesc != null" >
        #{eventDesc,jdbcType=VARCHAR},
      </if>
      <if test="eventWay != null" >
        #{eventWay,jdbcType=INTEGER},
      </if>
      <if test="blackList != null" >
        #{blackList,jdbcType=BIT},
      </if>
      <if test="miscDesc != null" >
        #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        #{updateOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.credit.model.MmpCreditEventType" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 22 10:46:53 CST 2017.
    -->
    update ${issSchema}.mmp_credit_event_type
    <set >
      <if test="eventName != null" >
        event_name = #{eventName,jdbcType=VARCHAR},
      </if>
      <if test="eventNature != null" >
        event_nature = #{eventNature,jdbcType=VARCHAR},
      </if>
      <if test="amount != null" >
        amount = #{amount,jdbcType=INTEGER},
      </if>
      <if test="eventDesc != null" >
        event_desc = #{eventDesc,jdbcType=VARCHAR},
      </if>
      <if test="eventWay != null" >
        event_way = #{eventWay,jdbcType=INTEGER},
      </if>
      <if test="blackList != null" >
        black_list = #{blackList,jdbcType=BIT},
      </if>
      <if test="miscDesc != null" >
        misc_desc = #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.credit.model.MmpCreditEventType" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Nov 22 10:46:53 CST 2017.
    -->
    update ${issSchema}.mmp_credit_event_type
    set event_name = #{eventName,jdbcType=VARCHAR},
      event_nature = #{eventNature,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=INTEGER},
      event_desc = #{eventDesc,jdbcType=VARCHAR},
      event_way = #{eventWay,jdbcType=INTEGER},
      black_list = #{blackList,jdbcType=BIT},
      misc_desc = #{miscDesc,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <sql id="Base_CreditEventTypeDto_Column_List">
    id, event_name as eventName, event_nature as eventNature, amount, black_list as blackList, event_way as eventWay
  </sql>
  <sql id="Base_CreditEventTypeFullDto_Column_List">
    id, event_name as eventName, event_nature as eventNature, amount, event_desc as eventDesc, event_way as eventWay, black_list as blackList, misc_desc as miscDesc,
    status, create_time as createTime, create_oper_id as createOperId, create_oper_name as createOperName, update_time as updateTime, update_oper_id as updateOperId,
    update_oper_name as updateOperName
  </sql>
  <sql id="Base_CreditEventTypePageDto_Column_List">
    id, event_name as eventName, event_nature as eventNature, amount, event_way as eventWay, black_list as blackList,
    create_time as createTime, create_oper_id as createOperId, create_oper_name as createOperName
  </sql>
  <select id="getCreditEventTypes" resultType="com.extracme.evcard.membership.credit.dto.CreditEventTypeDto">
    select
    <include refid="Base_CreditEventTypeDto_Column_List" />
    from ${issSchema}.mmp_credit_event_type
    WHERE status=1
  </select>

  <select id="getCreditEventTypeCount" resultType="java.lang.Integer">
    select COUNT(1)
    from ${issSchema}.mmp_credit_event_type
    <where>
      status=1
      <if test="eventName !='' and eventName != null">
        AND event_name = #{eventName}
      </if>
      <if test="eventNature !='' and eventNature != null">
        AND event_nature =#{eventNature}
      </if>
      <if test="eventWay != null">
        AND event_way =#{eventWay}
      </if>
    </where>
  </select>

  <select id="getCreditEventTypePages" resultType="com.extracme.evcard.membership.credit.dto.CreditEventTypePageDto">
    select
    <include refid="Base_CreditEventTypePageDto_Column_List"/>,
    (case when id=1 or id=2 then '0' ELSE  '1' end) as typeStatus
    from ${issSchema}.mmp_credit_event_type
    <where>
      status=1
      <if test="paramsDto.eventName !='' and paramsDto.eventName != null">
        AND event_name = #{paramsDto.eventName}
      </if>
      <if test="paramsDto.eventNature !='' and paramsDto.eventNature != null">
        AND event_nature =#{paramsDto.eventNature}
      </if>
      <if test="paramsDto.eventWay != null">
        AND event_way =#{paramsDto.eventWay}
      </if>
    </where>
    ORDER BY create_time DESC ,id ASC
    limit  #{page.offSet} , #{page.limitSet}
  </select>

</mapper>