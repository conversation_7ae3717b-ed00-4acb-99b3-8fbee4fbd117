<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.credit.dao.MemberPointsPushRecordMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.credit.model.MemberPointsPushRecord" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="auth_id" property="authId" jdbcType="VARCHAR" />
    <result column="event_type" property="eventType" jdbcType="INTEGER" />
    <result column="points_num" property="pointsNum" jdbcType="INTEGER" />
    <result column="event_ref_seq" property="eventRefSeq" jdbcType="VARCHAR" />
    <result column="amount" property="amount" jdbcType="DECIMAL" />
    <result column="details" property="details" jdbcType="VARCHAR" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="result" property="result" jdbcType="INTEGER" />
    <result column="result_code" property="resultCode" jdbcType="INTEGER" />
    <result column="message" property="message" jdbcType="VARCHAR" />
    <result column="pay_order_id" property="payOrderId" jdbcType="VARCHAR" />
    <result column="gain_points" property="gainPoints" jdbcType="INTEGER" />
    <result column="expire_date" property="expireDate" jdbcType="VARCHAR" />
    <result column="misc_desc" property="miscDesc" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, auth_id, event_type, points_num, event_ref_seq, amount, details, remark, result, 
    result_code, message, pay_order_id, gain_points, expire_date, misc_desc, status, 
    create_time, create_oper_id, create_oper_name, update_time, update_oper_id, update_oper_name
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from ${siacSchema}.member_points_push_record
    where id = #{id,jdbcType=BIGINT}
  </select>


  <select id="queryByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${siacSchema}.member_points_push_record
    where 1 =1
    <if test="list != null and list.size>0">
      AND id in
      <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
        ${item}
      </foreach>
    </if>
  </select>


  <select id="queryByIdRefKeys" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${siacSchema}.member_points_push_record
    where 1 = 1
    <if test="list != null and list.size>0">
      AND event_ref_seq in
      <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
          #{item}
      </foreach>
    </if>
  </select>



  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from ${siacSchema}.member_points_push_record
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="insert" parameterType="com.extracme.evcard.membership.credit.model.MemberPointsPushRecord" >
    insert into ${siacSchema}.member_points_push_record (id, auth_id, event_type, 
      points_num, event_ref_seq, amount, 
      details, remark, result, 
      result_code, message, pay_order_id, 
      gain_points, expire_date, misc_desc, 
      status, create_time, create_oper_id, 
      create_oper_name, update_time, update_oper_id, 
      update_oper_name)
    values (#{id,jdbcType=BIGINT}, #{authId,jdbcType=VARCHAR}, #{eventType,jdbcType=INTEGER}, 
      #{pointsNum,jdbcType=INTEGER}, #{eventRefSeq,jdbcType=VARCHAR}, #{amount,jdbcType=DECIMAL}, 
      #{details,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{result,jdbcType=INTEGER}, 
      #{resultCode,jdbcType=INTEGER}, #{message,jdbcType=VARCHAR}, #{payOrderId,jdbcType=VARCHAR}, 
      #{gainPoints,jdbcType=INTEGER}, #{expireDate,jdbcType=VARCHAR}, #{miscDesc,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, 
      #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, 
      #{updateOperName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.credit.model.MemberPointsPushRecord" keyProperty="id" useGeneratedKeys="true">
    insert into ${siacSchema}.member_points_push_record
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="authId != null" >
        auth_id,
      </if>
      <if test="eventType != null" >
        event_type,
      </if>
      <if test="pointsNum != null" >
        points_num,
      </if>
      <if test="eventRefSeq != null" >
        event_ref_seq,
      </if>
      <if test="amount != null" >
        amount,
      </if>
      <if test="details != null" >
        details,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="result != null" >
        result,
      </if>
      <if test="resultCode != null" >
        result_code,
      </if>
      <if test="message != null" >
        message,
      </if>
      <if test="payOrderId != null" >
        pay_order_id,
      </if>
      <if test="gainPoints != null" >
        gain_points,
      </if>
      <if test="expireDate != null" >
        expire_date,
      </if>
      <if test="miscDesc != null" >
        misc_desc,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateOperId != null" >
        update_oper_id,
      </if>
      <if test="updateOperName != null" >
        update_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="authId != null" >
        #{authId,jdbcType=VARCHAR},
      </if>
      <if test="eventType != null" >
        #{eventType,jdbcType=INTEGER},
      </if>
      <if test="pointsNum != null" >
        #{pointsNum,jdbcType=INTEGER},
      </if>
      <if test="eventRefSeq != null" >
        #{eventRefSeq,jdbcType=VARCHAR},
      </if>
      <if test="amount != null" >
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="details != null" >
        #{details,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="result != null" >
        #{result,jdbcType=INTEGER},
      </if>
      <if test="resultCode != null" >
        #{resultCode,jdbcType=INTEGER},
      </if>
      <if test="message != null" >
        #{message,jdbcType=VARCHAR},
      </if>
      <if test="payOrderId != null" >
        #{payOrderId,jdbcType=VARCHAR},
      </if>
      <if test="gainPoints != null" >
        #{gainPoints,jdbcType=INTEGER},
      </if>
      <if test="expireDate != null" >
        #{expireDate,jdbcType=VARCHAR},
      </if>
      <if test="miscDesc != null" >
        #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        #{updateOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.credit.model.MemberPointsPushRecord" >
    update ${siacSchema}.member_points_push_record
    <set >
      <if test="authId != null" >
        auth_id = #{authId,jdbcType=VARCHAR},
      </if>
      <if test="eventType != null" >
        event_type = #{eventType,jdbcType=INTEGER},
      </if>
      <if test="pointsNum != null" >
        points_num = #{pointsNum,jdbcType=INTEGER},
      </if>
      <if test="eventRefSeq != null" >
        event_ref_seq = #{eventRefSeq,jdbcType=VARCHAR},
      </if>
      <if test="amount != null" >
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="details != null" >
        details = #{details,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="result != null" >
        result = #{result,jdbcType=INTEGER},
      </if>
      <if test="resultCode != null" >
        result_code = #{resultCode,jdbcType=INTEGER},
      </if>
      <if test="message != null" >
        message = #{message,jdbcType=VARCHAR},
      </if>
      <if test="payOrderId != null" >
        pay_order_id = #{payOrderId,jdbcType=VARCHAR},
      </if>
      <if test="gainPoints != null" >
        gain_points = #{gainPoints,jdbcType=INTEGER},
      </if>
      <if test="expireDate != null" >
        expire_date = #{expireDate,jdbcType=VARCHAR},
      </if>
      <if test="miscDesc != null" >
        misc_desc = #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.credit.model.MemberPointsPushRecord" >
    update ${siacSchema}.member_points_push_record
    set auth_id = #{authId,jdbcType=VARCHAR},
      event_type = #{eventType,jdbcType=INTEGER},
      points_num = #{pointsNum,jdbcType=INTEGER},
      event_ref_seq = #{eventRefSeq,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=DECIMAL},
      details = #{details,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      result = #{result,jdbcType=INTEGER},
      result_code = #{resultCode,jdbcType=INTEGER},
      message = #{message,jdbcType=VARCHAR},
      pay_order_id = #{payOrderId,jdbcType=VARCHAR},
      gain_points = #{gainPoints,jdbcType=INTEGER},
      expire_date = #{expireDate,jdbcType=VARCHAR},
      misc_desc = #{miscDesc,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <!-- 批量插入 -->
  <insert id="batchSaveTitleReward" parameterType="java.util.List">
    INSERT INTO ${siacSchema}.member_points_push_record (
    auth_id, event_type,
    points_num, event_ref_seq, amount,
    details, create_time, create_oper_id,
    create_oper_name, update_time, update_oper_id,
    update_oper_name)
     VALUES
    <foreach collection="list" item="item" index="index" separator=",">
      (
      #{item.authId,jdbcType=VARCHAR}, #{item.eventType,jdbcType=INTEGER},
      #{item.pointsNum,jdbcType=INTEGER}, #{item.eventRefSeq,jdbcType=VARCHAR}, #{item.amount,jdbcType=DECIMAL},
      #{item.details,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.createOperId,jdbcType=BIGINT},
      #{item.createOperName,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.updateOperId,jdbcType=BIGINT},
      #{item.updateOperName,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <select id="selectOneByRefKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from ${siacSchema}.member_points_push_record
    where event_type = #{eventType,jdbcType=BIGINT} and event_ref_seq = #{eventRefSeq,jdbcType=VARCHAR}
    <if test="authId != null" >
      and auth_id = #{authId,jdbcType=VARCHAR}
    </if>
    limit 1
  </select>

</mapper>