package com.extracme.evcard.membership.core.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <p>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2018/2/6
 */
public enum RegisterOriginEnum {

    /**
     * 网点注册
     */
    SHOP(0,"网点注册"),
    /**
     * 网站注册
     */
    WEB(1,"网站注册"),
    /**
     * 管理平台
     */
    MANAGER_PLAT(2,"管理平台"),
    /**
     * 手机APP
     */
    APP(3,"手机APP"),
    /**
     * H5第三方
     */
    H5(4,"H5第三方"),
    /**
     * CRM
     */
    CRM(7,"CRM");

    private int code;

    private String message;

    RegisterOriginEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public static String getMessageByCode(int errCode){
        String message = StringUtils.EMPTY;
        for(RegisterOriginEnum originEnum : RegisterOriginEnum.values()){
            if(errCode == originEnum.getCode()){
                message = originEnum.getMessage();
                break;
            }
        }
        return message;
    }
}
