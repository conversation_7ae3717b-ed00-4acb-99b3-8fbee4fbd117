package com.extracme.evcard.membership.core.model;

import java.util.Date;

public class MmpAgencyDiscountLog {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_agency_discount_log.id
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_agency_discount_log.AGENCY_ID
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    private String agencyId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_agency_discount_log.AGENCY_STATUS
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    private Double agencyStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_agency_discount_log.COOPERATE_START_TIME
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    private Date cooperateStartTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_agency_discount_log.COOPERATE_END_TIME
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    private Date cooperateEndTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_agency_discount_log.COOPERATE_STATUS
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    private Integer cooperateStatus;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_agency_discount_log.DISCOUNT_ID
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    private Long discountId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_agency_discount_log.DISCOUNT_PERSONAL_ID
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    private Long discountPersonalId;

    /**
     * 折扣规则（1：取还车时间点 2：用车时长分段）
     * 广铁使用
     */
    private Integer discountRule;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_agency_discount_log.VEHICLE_NO
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    private String vehicleNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_agency_discount_log.misc_desc
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    private String miscDesc;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_agency_discount_log.status
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_agency_discount_log.create_time
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_agency_discount_log.create_oper_id
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    private Long createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_agency_discount_log.create_oper_name
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    private String createOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_agency_discount_log.update_time
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_agency_discount_log.update_oper_id
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    private Long updateOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_agency_discount_log.update_oper_name
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    private String updateOperName;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_agency_discount_log.id
     *
     * @return the value of mmp_agency_discount_log.id
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_agency_discount_log.id
     *
     * @param id the value for mmp_agency_discount_log.id
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_agency_discount_log.AGENCY_ID
     *
     * @return the value of mmp_agency_discount_log.AGENCY_ID
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public String getAgencyId() {
        return agencyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_agency_discount_log.AGENCY_ID
     *
     * @param agencyId the value for mmp_agency_discount_log.AGENCY_ID
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public void setAgencyId(String agencyId) {
        this.agencyId = agencyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_agency_discount_log.AGENCY_STATUS
     *
     * @return the value of mmp_agency_discount_log.AGENCY_STATUS
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public Double getAgencyStatus() {
        return agencyStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_agency_discount_log.AGENCY_STATUS
     *
     * @param agencyStatus the value for mmp_agency_discount_log.AGENCY_STATUS
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public void setAgencyStatus(Double agencyStatus) {
        this.agencyStatus = agencyStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_agency_discount_log.COOPERATE_START_TIME
     *
     * @return the value of mmp_agency_discount_log.COOPERATE_START_TIME
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public Date getCooperateStartTime() {
        return cooperateStartTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_agency_discount_log.COOPERATE_START_TIME
     *
     * @param cooperateStartTime the value for mmp_agency_discount_log.COOPERATE_START_TIME
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public void setCooperateStartTime(Date cooperateStartTime) {
        this.cooperateStartTime = cooperateStartTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_agency_discount_log.COOPERATE_END_TIME
     *
     * @return the value of mmp_agency_discount_log.COOPERATE_END_TIME
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public Date getCooperateEndTime() {
        return cooperateEndTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_agency_discount_log.COOPERATE_END_TIME
     *
     * @param cooperateEndTime the value for mmp_agency_discount_log.COOPERATE_END_TIME
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public void setCooperateEndTime(Date cooperateEndTime) {
        this.cooperateEndTime = cooperateEndTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_agency_discount_log.COOPERATE_STATUS
     *
     * @return the value of mmp_agency_discount_log.COOPERATE_STATUS
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public Integer getCooperateStatus() {
        return cooperateStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_agency_discount_log.COOPERATE_STATUS
     *
     * @param cooperateStatus the value for mmp_agency_discount_log.COOPERATE_STATUS
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public void setCooperateStatus(Integer cooperateStatus) {
        this.cooperateStatus = cooperateStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_agency_discount_log.DISCOUNT_ID
     *
     * @return the value of mmp_agency_discount_log.DISCOUNT_ID
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public Long getDiscountId() {
        return discountId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_agency_discount_log.DISCOUNT_ID
     *
     * @param discountId the value for mmp_agency_discount_log.DISCOUNT_ID
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public void setDiscountId(Long discountId) {
        this.discountId = discountId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_agency_discount_log.DISCOUNT_PERSONAL_ID
     *
     * @return the value of mmp_agency_discount_log.DISCOUNT_PERSONAL_ID
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public Long getDiscountPersonalId() {
        return discountPersonalId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_agency_discount_log.DISCOUNT_PERSONAL_ID
     *
     * @param discountPersonalId the value for mmp_agency_discount_log.DISCOUNT_PERSONAL_ID
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public void setDiscountPersonalId(Long discountPersonalId) {
        this.discountPersonalId = discountPersonalId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_agency_discount_log.VEHICLE_NO
     *
     * @return the value of mmp_agency_discount_log.VEHICLE_NO
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public String getVehicleNo() {
        return vehicleNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_agency_discount_log.VEHICLE_NO
     *
     * @param vehicleNo the value for mmp_agency_discount_log.VEHICLE_NO
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_agency_discount_log.misc_desc
     *
     * @return the value of mmp_agency_discount_log.misc_desc
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public String getMiscDesc() {
        return miscDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_agency_discount_log.misc_desc
     *
     * @param miscDesc the value for mmp_agency_discount_log.misc_desc
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_agency_discount_log.status
     *
     * @return the value of mmp_agency_discount_log.status
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_agency_discount_log.status
     *
     * @param status the value for mmp_agency_discount_log.status
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_agency_discount_log.create_time
     *
     * @return the value of mmp_agency_discount_log.create_time
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_agency_discount_log.create_time
     *
     * @param createTime the value for mmp_agency_discount_log.create_time
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_agency_discount_log.create_oper_id
     *
     * @return the value of mmp_agency_discount_log.create_oper_id
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public Long getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_agency_discount_log.create_oper_id
     *
     * @param createOperId the value for mmp_agency_discount_log.create_oper_id
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_agency_discount_log.create_oper_name
     *
     * @return the value of mmp_agency_discount_log.create_oper_name
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_agency_discount_log.create_oper_name
     *
     * @param createOperName the value for mmp_agency_discount_log.create_oper_name
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_agency_discount_log.update_time
     *
     * @return the value of mmp_agency_discount_log.update_time
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_agency_discount_log.update_time
     *
     * @param updateTime the value for mmp_agency_discount_log.update_time
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_agency_discount_log.update_oper_id
     *
     * @return the value of mmp_agency_discount_log.update_oper_id
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public Long getUpdateOperId() {
        return updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_agency_discount_log.update_oper_id
     *
     * @param updateOperId the value for mmp_agency_discount_log.update_oper_id
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_agency_discount_log.update_oper_name
     *
     * @return the value of mmp_agency_discount_log.update_oper_name
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public String getUpdateOperName() {
        return updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_agency_discount_log.update_oper_name
     *
     * @param updateOperName the value for mmp_agency_discount_log.update_oper_name
     *
     * @mbggenerated Wed Apr 01 10:00:41 CST 2020
     */
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }
}