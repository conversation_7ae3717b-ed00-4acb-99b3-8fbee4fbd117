package com.extracme.evcard.membership.credit.model;

import java.io.Serializable;
import java.math.BigDecimal;

public class MembershipBaseInfo implements Serializable {
    private static final long serialVersionUID = -566969111288269261L;

    private Long pkId;
    /**
     * 会员id
     */
    private String authId;

    /**
     * 享道统一用户ID
     */
    private String uid;

    /**
     * 用户mid
     *
     */
    private String mid;

    /**
     * 证件号.<br>
     */
    private String driverCode;

    /**
     * 姓名
     */
    private String name;
    
    /**
     * 手机号.<br>
     */
    private String mobilePhone;
    
    /**
     * 0:普通会员, 1:内部会员
     */
    private int membershipType;

     //（用户所属）市编码
    private String cityOfOrigin;

     // 第三方接入的APP key
    private String appKey;

     //会员来源(0：网点注册 1：网站注册 2：管理平台注册 3:手机APP 4:第三方 5:CRM）
    private BigDecimal dataOrigin;

    // 审核状态(0:待审核 1：审核通过 2: 审核不通过 3：用户无效 4:重新审核） 5 未完成人脸识别
    private BigDecimal reviewStatus;

    //服务条款
    private String serverVer;

    //认证状态 0 未认证 1 未刷脸/未上传 2 已认证
    private Integer authenticationStatus;

    private String cardNo;

    private String createdTime;

    private String password;

    private String regTime;

    public String getRegTime() {
        return regTime;
    }

    public void setRegTime(String regTime) {
        this.regTime = regTime;
    }

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public Long getPkId() {
        return pkId;
    }

    public void setPkId(Long pkId) {
        this.pkId = pkId;
    }

    public String getCityOfOrigin() {
        return cityOfOrigin;
    }

    public void setCityOfOrigin(String cityOfOrigin) {
        this.cityOfOrigin = cityOfOrigin;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public BigDecimal getDataOrigin() {
        return dataOrigin;
    }

    public void setDataOrigin(BigDecimal dataOrigin) {
        this.dataOrigin = dataOrigin;
    }

    public BigDecimal getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(BigDecimal reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getServerVer() {
        return serverVer;
    }

    public void setServerVer(String serverVer) {
        this.serverVer = serverVer;
    }

	public String getAuthId() {
		return authId;
	}

	public void setAuthId(String authId) {
		this.authId = authId;
	}

	public String getDriverCode() {
		return driverCode;
	}

	public void setDriverCode(String driverCode) {
		this.driverCode = driverCode;
	}

	public int getMembershipType() {
		return membershipType;
	}

	public void setMembershipType(int membershipType) {
		this.membershipType = membershipType;
	}

	public String getMobilePhone() {
		return mobilePhone;
	}

	public void setMobilePhone(String mobilePhone) {
		this.mobilePhone = mobilePhone;
	}

    public Integer getAuthenticationStatus() {
        return authenticationStatus;
    }

    public void setAuthenticationStatus(Integer authenticationStatus) {
        this.authenticationStatus = authenticationStatus;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }
}
