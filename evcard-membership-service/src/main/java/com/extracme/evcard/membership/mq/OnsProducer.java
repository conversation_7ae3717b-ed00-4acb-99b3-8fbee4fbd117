package com.extracme.evcard.membership.mq;

import com.aliyun.openservices.ons.api.PropertyKeyConst;
import com.aliyun.openservices.ons.api.bean.ProducerBean;
import com.extracme.evcard.membership.config.AliyunConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

/**
 * <AUTHOR>
 * @Discription
 * @date 2021/12/10
 */
@Configuration
public class OnsProducer {
    @Autowired
    private AliyunConfig aliyunConfig;

    @Bean(initMethod = "start", destroyMethod = "shutdown")
    public ProducerBean producer() {
        ProducerBean producerBean = new ProducerBean();
        Properties properties = aliyunConfig.getOnsPropertie();
        properties.setProperty(PropertyKeyConst.GROUP_ID, aliyunConfig.getOnsGid());
        producerBean.setProperties(properties);
        return producerBean;
    }
}
