package com.extracme.evcard.membership.core.model;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户Token会话表
 * 支持多设备登录和token管理
 * 
 * <AUTHOR>
 * @since 2025-08-20
 */
public class UserTokenSession implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户MID
     */
    private String mid;
    
    /**
     * 应用标识
     */
    private String appKey;
    
    /**
     * 设备ID（用于区分不同设备）
     */
    private String deviceId;
    
    /**
     * 设备类型（MOBILE, WEB, MINI_PROGRAM, API, OTHER）
     */
    private String deviceType;
    
    /**
     * 设备信息（如设备型号、浏览器信息等）
     */
    private String deviceInfo;
    
    /**
     * 访问令牌JTI（JWT ID）
     */
    private String accessTokenJti;
    
    /**
     * 刷新令牌JTI（JWT ID）
     */
    private String refreshTokenJti;
    
    /**
     * 访问令牌过期时间
     */
    private Date accessTokenExpireTime;
    
    /**
     * 刷新令牌过期时间
     */
    private Date refreshTokenExpireTime;
    
    /**
     * 最后活跃时间
     */
    private Date lastActiveTime;
    
    /**
     * 登录IP地址
     */
    private String loginIp;
    
    /**
     * 登录地理位置
     */
    private String loginLocation;
    
    /**
     * 会话状态（0:正常 1:已注销 2:已过期 3:被踢出）
     */
    private Integer status;
    
    /**
     * 注销原因
     */
    private String logoutReason;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 创建操作员ID
     */
    private Long createOperId;
    
    /**
     * 创建操作员名称
     */
    private String createOperName;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 更新操作员ID
     */
    private Long updateOperId;
    
    /**
     * 更新操作员名称
     */
    private String updateOperName;
    
    /**
     * 是否删除（0:未删除 1:已删除）
     */
    private Integer isDeleted;

    // Getter and Setter methods
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public String getDeviceInfo() {
        return deviceInfo;
    }

    public void setDeviceInfo(String deviceInfo) {
        this.deviceInfo = deviceInfo;
    }

    public String getAccessTokenJti() {
        return accessTokenJti;
    }

    public void setAccessTokenJti(String accessTokenJti) {
        this.accessTokenJti = accessTokenJti;
    }

    public String getRefreshTokenJti() {
        return refreshTokenJti;
    }

    public void setRefreshTokenJti(String refreshTokenJti) {
        this.refreshTokenJti = refreshTokenJti;
    }

    public Date getAccessTokenExpireTime() {
        return accessTokenExpireTime;
    }

    public void setAccessTokenExpireTime(Date accessTokenExpireTime) {
        this.accessTokenExpireTime = accessTokenExpireTime;
    }

    public Date getRefreshTokenExpireTime() {
        return refreshTokenExpireTime;
    }

    public void setRefreshTokenExpireTime(Date refreshTokenExpireTime) {
        this.refreshTokenExpireTime = refreshTokenExpireTime;
    }

    public Date getLastActiveTime() {
        return lastActiveTime;
    }

    public void setLastActiveTime(Date lastActiveTime) {
        this.lastActiveTime = lastActiveTime;
    }

    public String getLoginIp() {
        return loginIp;
    }

    public void setLoginIp(String loginIp) {
        this.loginIp = loginIp;
    }

    public String getLoginLocation() {
        return loginLocation;
    }

    public void setLoginLocation(String loginLocation) {
        this.loginLocation = loginLocation;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getLogoutReason() {
        return logoutReason;
    }

    public void setLogoutReason(String logoutReason) {
        this.logoutReason = logoutReason;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateOperId() {
        return createOperId;
    }

    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    public String getCreateOperName() {
        return createOperName;
    }

    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdateOperId() {
        return updateOperId;
    }

    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    public String getUpdateOperName() {
        return updateOperName;
    }

    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }
}
