<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.core.dao.MmpDiscountRuleMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.MmpDiscountRule" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 09 13:53:04 CST 2018.
    -->
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="agency_id" property="agencyId" jdbcType="VARCHAR" />
    <result column="discount_type" property="discountType" jdbcType="INTEGER" />
    <result column="discount_rate" property="discountRate" jdbcType="DOUBLE" />
    <result column="peak_season_discount_rate" property="peakSeasonDiscountRate" jdbcType="DOUBLE" />
    <result column="beneficiary_number" property="beneficiaryNumber" jdbcType="INTEGER" />
    <result column="valid_start_time" property="validStartTime" jdbcType="TIMESTAMP" />
    <result column="valid_end_time" property="validEndTime" jdbcType="TIMESTAMP" />
    <result column="misc_desc" property="miscDesc" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 09 13:53:04 CST 2018.
    -->
    id, agency_id, discount_type, discount_rate, peak_season_discount_rate, beneficiary_number, valid_start_time,
    valid_end_time, misc_desc, status, create_time, create_oper_id, create_oper_name, 
    update_time, update_oper_id, update_oper_name
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed May 09 13:53:04 CST 2018.
    -->
    select 
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_discount_rule
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="findByAgencyId" resultType="com.extracme.evcard.membership.core.dto.agency.DiscountRuleDTO">
    select
      t.id as id,
      t.agency_id as agencyId,
      t.discount_type as discountType,
      t.discount_rate as discountRate,
      t.peak_season_discount_rate as peakSeasonDiscountRate,
      t.beneficiary_number as beneficiaryNumber,
      t.valid_start_time as validStartTime,
      t.valid_end_time as validEndTime
    from ${issSchema}.mmp_discount_rule t
    where 1 = 1
    <if test="agencyId!=null and agencyId!=''">
      and agency_id = #{agencyId,jdbcType=VARCHAR}
    </if>
    <if test="discountType!=null">
      and discount_type = #{discountType,jdbcType=INTEGER}
    </if>
  </select>
</mapper>