package com.extracme.evcard.membership.core.service;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.bean.ProducerBean;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.common.UploadImgUtil;
import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.dao.*;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.dto.input.CRGTMemberInputDto;
import com.extracme.evcard.membership.core.dto.input.ThirdPartMemberDto;
import com.extracme.evcard.membership.core.exception.UserMessageException;
import com.extracme.evcard.membership.core.input.*;
import com.extracme.evcard.membership.core.model.ApplyProgress;
import com.extracme.evcard.membership.core.model.CardInfo;
import com.extracme.evcard.membership.core.model.CardInfoHistroy;
import com.extracme.evcard.membership.core.model.SecondAppKeyManager;
import com.extracme.evcard.membership.core.service.auth.IRegister;
import com.extracme.evcard.membership.core.service.auth.IThirdLogin;
import com.extracme.evcard.membership.core.service.auth.login.ThirdLoginAdapter;
import com.extracme.evcard.membership.core.service.auth.register.RegisterAdapter;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.model.MembershipBaseInfo;
import com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs;
import com.extracme.evcard.membership.invitation.service.IMemberShipInvitationServ;
import com.extracme.evcard.mq.bean.EventEnum;
import com.extracme.evcard.mq.bean.event.MemberAudit;
import com.extracme.evcard.protobuf.ProtobufUtil;
import com.extracme.evcard.rpc.coupon.dto.CouponModelTOfferDto;
import com.extracme.evcard.rpc.coupon.service.ICouponServ;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.util.BeanCopyUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.UUID;


@Service("thirdPartMemberService")
public class ThirdPartMemberServiceImpl implements IThirdPartMemberService {

    private static Logger logger = LoggerFactory.getLogger(ThirdPartMemberServiceImpl.class);
    @Autowired
    private MembershipInfoMapper membershipInfoMapper;

    @Autowired
    private CardInfoMapper cardInfoMapper;

    @Autowired
    private ApplyProgressMapper applyProgressMapper;

    @Autowired
    private UserOperatorLogMapper userOperatorLogMapper;
    @Autowired
    private CardInfoHistroyMapper cardInfoHistroyMapper;
    @Autowired
    private UserCouponListMapper userCouponListMapper;
    @Autowired
    private IMemberShipInvitationServ memberShipInvitationServ;
    @Autowired
    private ICouponServ couponServ;
    @Resource(name = "producer")
    private ProducerBean producer;

    @Value("${ons.raw.topic}")
    private String evcardRawDataTopic;

    @Autowired
    private RegisterAdapter registerAdapter;

    @Override
    public MembershipBasicInfo insertCRGTmMember(CRGTMemberInputDto inputDto) {

//        String faceImgUrl="/crgtMemberImg/faceImg/2fc1e762-1ae7-4a40-aa41-148f93e5fc9d/15212341234_1545131448924.jpg";
//        String licImgUrl="/crgtMemberImg/liceImg/816b3634-3391-4ae4-8e22-3aa9feaa50ef/15212341234_1545131449681.jpg";
        String faceImgUrl = StringUtils.EMPTY;
        String licImgUrl = StringUtils.EMPTY;
        try {
            faceImgUrl = getImageStrFromUrl(inputDto.getFacePic(), 1, inputDto.getPhoneNumber());
            licImgUrl = getImageStrFromUrl(inputDto.getLicensePicFirst(), 2, inputDto.getPhoneNumber());
        } catch (IOException e) {
            e.printStackTrace();
        }
        ThirdPartMemberDto thirdPartMemberDto = new ThirdPartMemberDto(inputDto.getLicenseNo(), inputDto.getName(), inputDto.getPhoneNumber(),
                inputDto.getAuthId(), inputDto.getLicenseNo(), inputDto.getLicenseStartTime(), inputDto.getLicenseEndTime(), faceImgUrl, licImgUrl, inputDto.getAppKey(),
                faceImgUrl, inputDto.getLicenseNo(), inputDto.getLicenseLevel());
        return insertReviewStatus(thirdPartMemberDto);
    }


    /**
     * 下载国铁图片至oss并返回oss路径
     *
     * @param imgUrl
     * @param type   1:人脸图片 2.驾照图片
     * @return
     */
    @Override
    public String getImageStrFromUrl(String imgUrl, Integer type, String phone) throws IOException {
        InputStream is = null;
        try {
            // 创建URL
            URL url = new URL(imgUrl);
            byte[] by = new byte[1024];
            // 创建链接
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(5000);
            is = conn.getInputStream();

            //上传至oss
            String ossImageUrl = StringUtils.EMPTY;
            StringBuffer fileRoot = new StringBuffer("/crgtMemberImg/");
            if (type.equals(1)) {
                fileRoot.append("faceImg/");
            } else {
                fileRoot.append("liceImg/");
            }
            fileRoot.append(UUID.randomUUID()).append("/");
            int start = imgUrl.lastIndexOf(".");
            String filetype = imgUrl.substring(start + 1);
            String fileName = phone + "_" + System.currentTimeMillis();
            ossImageUrl = fileRoot.append(fileName).append(".").append(filetype).toString();
            UploadImgUtil.uploadStreamSyn(is, ossImageUrl, UploadImgUtil.OSS_BUCKET);

            return ossImageUrl;
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            is.close();
        }
        return null;
    }

//    /**
//     * 更新第三方会员信息
//     * @param thirdPartMemberDto
//     */
//    void updateThirdPartMember(ThirdPartMemberDto thirdPartMemberDto){
//
//        MembershipBaseInfo membershipBaseInfo = membershipInfoMapper.selectBaseByAuthId(thirdPartMemberDto.getAuthId(),0);
//
//        String cardNo = membershipBaseInfo.getCardNo();
//        if (StringUtils.isBlank(membershipBaseInfo.getCardNo())){
//            //卡id
//            cardNo = insertCardNo(membershipBaseInfo.getAuthId(),"third_part");
//        }
//
//        MembershipInfoWithBLOBs record = new MembershipInfoWithBLOBs();
//        BeanCopyUtils.copyProperties(thirdPartMemberDto,record);
//        // 状态(0：有效 1：无效)
//        record.setStatus((short) 0);
//        record.setCardNo(cardNo);
//        record.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
//        record.setUpdatedUser(thirdPartMemberDto.getAppKey());
//        record.setRegTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE4));
//        record.setReviewStatus((short)1);
//        record.setReviewMode(2);
//        record.setAuthenticationStatus(2);
//        record.setPkId(membershipBaseInfo.getPkId());
//        membershipInfoMapper.updateByPrimaryKeySelective(record);
//    }

    /**
     * 新增第三方会员时插入认证等信息
     *
     * @param thirdPartMemberDto
     */
    @Transactional(rollbackFor = Exception.class)
    MembershipBasicInfo insertReviewStatus(ThirdPartMemberDto thirdPartMemberDto) {

        MembershipBaseInfo driverMember = membershipInfoMapper.selectBaseByDriverCode(thirdPartMemberDto.getDriverCode(), 0,null);
        MembershipBasicInfo membershipBasicInfo = new MembershipBasicInfo();
        if (driverMember != null && !thirdPartMemberDto.getAuthId().equals(driverMember.getAuthId())) {
            membershipBasicInfo.setPkId(-1L);
            return membershipBasicInfo;
        }
        MembershipBaseInfo membershipBaseInfo = membershipInfoMapper.selectBaseByAuthId(thirdPartMemberDto.getAuthId(), 0);
        /**
         * 1.更新会员表
         */
        MembershipInfoWithBLOBs record = new MembershipInfoWithBLOBs();
        BeanCopyUtils.copyProperties(thirdPartMemberDto, record);
        String cardNo = membershipBaseInfo.getCardNo();
        if (StringUtils.isBlank(cardNo)) {
            cardNo = insertCardNo(thirdPartMemberDto.getAuthId(), "third_part");
        }
        //驾照信息
        record.setNational("中国");
        record.setRegTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE4));
        record.setDrivingLicenseType(thirdPartMemberDto.getDrivingLicenseType());
        record.setAuthenticationStatus(2);
        //自动审核
        record.setReviewStatus((short) 1);
        record.setReviewMode(2);
        record.setCardNo(cardNo);
        record.setReviewTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE4));
        record.setAppReviewTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE4));
        record.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
        record.setUpdatedUser(thirdPartMemberDto.getAuthId());
        record.setReviewItems("111111111");
        record.setMark(thirdPartMemberDto.getAppKey());
        record.setPkId(membershipBaseInfo.getPkId());
        int updateResult = membershipInfoMapper.updateByPrimaryKeySelective(record);
        if (updateResult > 0) {
            BeanCopyUtils.copyProperties(membershipBaseInfo, membershipBasicInfo);
            membershipBasicInfo.setCardNo(cardNo);
            int isOldUser = isOldUser(membershipBaseInfo.getCreatedTime(),1);
            sendAutoReviewCoupon(thirdPartMemberDto.getAuthId(),membershipBaseInfo.getMobilePhone(),isOldUser);
            return membershipBasicInfo;
        } else {
            return null;
        }
    }

    public String insertCardNo(String authId, String createUser) {
        /**
         * 1.制虚拟卡
         */
        CardInfo cardInfo = new CardInfo();
        String virtual_cardNo = ComUtil.getVirtualCardNo(cardInfoMapper);
        cardInfo.setCardNo(virtual_cardNo);
        cardInfo.setCardType(0.0);
        // 卡号类型（1:虚拟卡号，2:物理卡号）
        Integer cardNoType = 1;
        cardInfo.setCardNoType(cardNoType);
        cardInfo.setAuthId(authId);
        String internalNo = ComUtil.generateInternalNo(cardInfoMapper, 1);
        cardInfo.setInternalNo(internalNo);
        cardInfo.setValidityTime("20200101");
        // 初始秘钥
        cardInfo.setRwKeytAa("ffffffffffff");
        cardInfo.setRwKeytBb("ffffffffffff");
        cardInfo.setStatus(0);
        cardInfo.setActivateStatus(1.0);
        cardInfo.setCreatedUser(createUser);
        cardInfo.setCreatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
        cardInfo.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
        cardInfoMapper.addCardInfo(cardInfo);
        /**
         * 2.申请制卡进程
         */
        ApplyProgress progress = new ApplyProgress();
        progress.setAuthId(authId.toString());
        progress.setProgressContent("审核通过");
        progress.setCreatedUser(createUser);
        progress.setProgressTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE8));
        //新用户而且审核通过
        applyProgressMapper.addApplyProgress(progress);
        /**
         * 3.保存日志
         */
        String operatorContent = "添加会员卡【" + internalNo + "】";
        ComUtil.insertOperatorLog(operatorContent, authId.toString(), "0", createUser, userOperatorLogMapper);

        CardInfoHistroy cardInfoHistroy = new CardInfoHistroy();
        cardInfoHistroy.setCardStatus(1.0);
        cardInfoHistroy.setCreatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE4));
        cardInfoHistroy.setCreatedUser(createUser);
        cardInfoHistroy.setMembershipType(Double.valueOf(0));
        cardInfoHistroy.setCardNo(virtual_cardNo);
        cardInfoHistroy.setAuthId(authId.toString());
        cardInfoHistroyMapper.addCardInfoHistroy(cardInfoHistroy);
        return virtual_cardNo;
    }

    /**
     * 为新老用户发券
     *
     * @param authId
     * @param mobile
     * @param isOldUser
     */
    public void sendAutoReviewCoupon(String authId, String mobile, int isOldUser) {
        if (isOldUser == 1) {
            LocalDateTime nowDateTime = LocalDateTime.now();
            if (nowDateTime.isBefore(BussinessConstants.SEND_COUPON_DATETIME)) {
                //查询是否已经发送过认证奖励券
                if (userCouponListMapper.selectCount(authId,"会员认证奖励券") == 0) {
                    LocalDate startDate = LocalDate.now();
                    LocalDate expireDate = startDate.plus(2, ChronoUnit.MONTHS);
                    DateTimeFormatter ymd1 = DateTimeFormatter.ofPattern(ComUtil.DATE_TYPE5);
                    String startDateStr = startDate.format(ymd1);
                    String expireDateStr = expireDate.format(ymd1);
                    String couponName = "会员认证奖励券";
                    singleOfferCoupon(authId, 10f, couponName, startDateStr, expireDateStr);
                }
            }
        } else {
            //新用户发送新手礼包和好友邀请礼包
            try {
                BaseResponse baseResponse = memberShipInvitationServ.giveAnewGiftBag(authId, mobile, "00", "mas");
                if (baseResponse.getCode() != 0) {
                    logger.warn("会员" + authId + "调用会员服务发送新手礼包失败");
                }
                BaseResponse baseResponse2 = memberShipInvitationServ.giveFriendInvitationGiftBag(authId, mobile, "00", "mas");
                if (baseResponse2.getCode() != 0) {
                    logger.warn("会员" + authId + "调用会员服务发送好友邀请礼包失败");
                }
            } catch (Exception e) {
                logger.error("会员" + authId + "调用会员服务失败");
            }
            //推送会员系统消息
            sendMemberMessage(authId, mobile, "membership-rpc");
        }
    }


    public void singleOfferCoupon(String authId, float couponValue, String couponName, String startDate, String expireDate) {
        try {
            CouponModelTOfferDto couponModelTOfferDto = new CouponModelTOfferDto();
            couponModelTOfferDto.setOrgId("00");
            couponModelTOfferDto.setOptOrgId("00");
            couponModelTOfferDto.setAuthId(authId);
            couponModelTOfferDto.setCouponName(couponName);
            couponModelTOfferDto.setStartDate(startDate);
            couponModelTOfferDto.setExpiresDate(expireDate);
            couponModelTOfferDto.setOptUser("mas");
            couponModelTOfferDto.setServiceType(1);
            couponModelTOfferDto.setTimeType(0);
            couponModelTOfferDto.setCouponType(1);
            couponModelTOfferDto.setCouponValue(new BigDecimal(String.valueOf(couponValue)));
            com.extracme.evcard.rpc.coupon.dto.BaseResponse baseResponse = couponServ.singleOfferCoupon(couponModelTOfferDto);
            if (baseResponse.getCode() == 0) {
                logger.info("会员authId=" + authId + "优惠券发送成功");
            } else {
                logger.warn("会员authId=" + authId + "优惠券发送失败=" + baseResponse.getMessage());
                throw new UserMessageException(UserMessageException.SAVE_USER_MESSAGE_EXP.getCode(),
                        UserMessageException.SAVE_USER_MESSAGE_EXP.getMessage());
            }
        } catch (Exception e) {
            logger.warn("会员authId=" + authId + "优惠券发送失败");
        }
    }

    public int isOldUser(String createTime,int reviewStatus){
        if(StringUtils.isNotEmpty(createTime) && createTime.length() > 13){
            DateTimeFormatter ymd = DateTimeFormatter.ofPattern(ComUtil.DATE_TYPE4);
            LocalDateTime createDateTime = LocalDateTime.parse(createTime.substring(0, 14), ymd);
            if(reviewStatus == 1 && BussinessConstants.OLD_USER_DATETIME.isAfter(createDateTime)){
                return 1;
            }
        }
        return 0;
    }

    /**
     * 推送会员消息
     */
    private void sendMemberMessage(String authId, String mobilePhone, String createUser) {
        try {
            MemberAudit audit = new MemberAudit();
            audit.setAuthId(authId);
            audit.setMobilePhone(mobilePhone);
            audit.setOptUser(createUser);
            audit.setNewUser(1);
            audit.setReviewStatus("1");
            byte[] messageBody = ProtobufUtil.serializeProtobuf(audit);
            String messageKey = "membership#" + UUID.randomUUID().toString().replace("-", "");
            Message msg = new Message(evcardRawDataTopic, EventEnum.MEMBER_AUDIT.getTag(), messageBody);
            msg.setKey(messageKey);
            producer.send(msg);
            logger.info("推送会员系统审核通过消息成功|authId = " + authId );
        } catch (Exception e) {
            logger.info("推送会员系统审核通过消息失败|authId = " + authId);
        }
    }

    @Autowired
    private ThirdLoginAdapter thirdLoginAdapter;

    @Override
    public ThirdLoginDto carLifeLogin(CarLifeLoginInput input) throws BusinessException {
        logger.info("车生活用户注册登录，input{}", JSON.toJSONString(input));
        IThirdLogin thirdLoginService = thirdLoginAdapter.getThirdLoginService(BussinessConstants.CAR_LIFE_CHANNEL);
        ThirdLoginDto thirdLoginDto = thirdLoginService.thirdLogin(input);
        logger.info("车生活用户注册成功，thirdLoginDto{}", JSON.toJSONString(thirdLoginDto));
        return thirdLoginDto;
    }

    @Override
    public ThirdLoginDto qingLuLogin(ThirdLoginInput input) throws BusinessException {
        logger.info("第三方 用户注册登录，input{}", JSON.toJSONString(input));
        IThirdLogin thirdLoginService;
        if (BussinessConstants.CAR_LIFE_SECOND_APP_KEY.equals(input.getAppKey())) {
            thirdLoginService = thirdLoginAdapter.getThirdLoginService(input.getAppKey());
        } else if (BussinessConstants.TONGCHENG_SECOND_APP_KEY.equals(input.getAppKey())) {
            thirdLoginService = thirdLoginAdapter.getThirdLoginService(input.getAppKey());
        }else {
            thirdLoginService = thirdLoginAdapter.getThirdLoginService(BussinessConstants.QING_LU_CHANNEL);
        }

        ThirdLoginDto thirdLoginDto = thirdLoginService.thirdLogin(input);
        logger.info("第三方 用户注册成功，thirdLoginDto{}", JSON.toJSONString(thirdLoginDto));
        return thirdLoginDto;
    }

    @Override
    public ThirdLoginDto longShortMixLogin(ThirdLoginInput input) throws BusinessException {
        logger.info("长短租融合 用户注册登录，input{}", JSON.toJSONString(input));
        IThirdLogin thirdLoginService = thirdLoginAdapter.getThirdLoginService(BussinessConstants.LONG_SHORT_MIX);
        ThirdLoginDto thirdLoginDto = thirdLoginService.thirdLogin(input);
        logger.info("长短租融合 用户注册成功，thirdLoginDto{}", JSON.toJSONString(thirdLoginDto));
        return thirdLoginDto;
    }


    @Override
    public ThirdLoginDto saicTravelLogin(ThirdLoginInput input) throws BusinessException {
        logger.info("赛乐通 用户注册登录，input{}", JSON.toJSONString(input));
        IThirdLogin thirdLoginService = thirdLoginAdapter.getThirdLoginService(BussinessConstants.SAI_LE_TONG);
        ThirdLoginDto thirdLoginDto = thirdLoginService.thirdLogin(input);
        logger.info("赛乐通 用户注册成功，thirdLoginDto{}", JSON.toJSONString(thirdLoginDto));
        return thirdLoginDto;
    }

    @Override
    public ThirdLoginDto thirdLoginGetMid(ThirdLoginInput input) throws BusinessException {
        logger.info("第三方 thirdLoginGetMid，input{}", JSON.toJSONString(input));
        IThirdLogin thirdLoginService = thirdLoginAdapter.getThirdLoginService(input.getAppKey());
        ThirdLoginDto thirdLoginDto = thirdLoginService.thirdLogin(input);
        logger.info("第三方 thirdLoginGetMid，thirdLoginDto{}", JSON.toJSONString(thirdLoginDto));
        return thirdLoginDto;
    }

    @Override
    public EvcardTokenDto getThirdAccessToken(GetThirdAccessTokenInput input) throws BusinessException {
        logger.info("第三方 getThirdAccessToken，input{}",  JSON.toJSONString(input));
        IThirdLogin thirdLoginService = thirdLoginAdapter.getThirdLoginService(input.getAppKey());
        EvcardTokenDto thirdAccessToken = thirdLoginService.getThirdAccessToken(input);
        logger.info("第三方 getThirdAccessToken，input{},thirdAccessToken{}",  JSON.toJSONString(input),JSON.toJSONString(thirdAccessToken));
        return thirdAccessToken;
    }

    @Override
    public ThirdLoginDto commonUserLogin(ThirdLoginInput input) throws BusinessException {
        logger.info("通用用户 用户注册登录，input{}", JSON.toJSONString(input));
        IThirdLogin thirdLoginService = thirdLoginAdapter.getCommonLoginService(input.getSecondAppKey());
        if (thirdLoginService == null) {
            throw new BusinessException(-1, "未查询到登陆服务，用户登录失败");
        }
        ThirdLoginDto thirdLoginDto = thirdLoginService.thirdLogin(input);
        logger.info("通用用户 用户注册成功，thirdLoginDto{}", JSON.toJSONString(thirdLoginDto));
        return thirdLoginDto;
    }

    @Resource
    private SecondAppKeyManagerMapper secondAppKeyManagerMapper;

    @Override
    public void suiShenXingBind(String secondAppKey, Long userId) throws BusinessException {
        try {
            SecondAppKeyManager secondAppKeyManager = secondAppKeyManagerMapper.selectBySecondAppKey(secondAppKey);
            if (secondAppKeyManager != null) {
                MembershipInfoWithBLOBs membershipInfo = new MembershipInfoWithBLOBs();
                membershipInfo.setPkId(userId);
                membershipInfo.setAppKey(secondAppKeyManager.getFirstAppKey());
                membershipInfo.setPlatformId(secondAppKeyManager.getPlatformId());
                membershipInfo.setSecondAppKey(secondAppKey);
                if (membershipInfoMapper.updateByPrimaryKeySelective(membershipInfo) > 0) {
                    // 更改企业
                    IRegister registerHandler = registerAdapter.getRegisterService(secondAppKey);
                    if (registerHandler != null) {
                        NotifyRegisterDto notifyRegisterDto = new NotifyRegisterDto();
                        notifyRegisterDto.setSecondAppKey(secondAppKey);
                        notifyRegisterDto.setUserId(userId);
                        registerHandler.notifyRegisterSuccess(notifyRegisterDto);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("suiShenXingBind 业务异常，secondAppKey={}，userId={}",secondAppKey,userId);
            throw new BusinessException(-1,"业务异常");
        }

    }

    @Override
    public void userLoginNotify(NotifyLoginDto notifyLoginDto) throws BusinessException {
        Long userId = notifyLoginDto.getUserId();
        String secondAppKey = notifyLoginDto.getSecondAppKey();
        if (StringUtils.isBlank(secondAppKey) || userId == null) {
            return;
        }
        try {
            SecondAppKeyManager secondAppKeyManager = secondAppKeyManagerMapper.selectBySecondAppKey(secondAppKey);
            if (secondAppKeyManager == null) {
                return;
            }

            // 更改企业
            IRegister registerHandler = registerAdapter.getRegisterService(secondAppKey);
            if (registerHandler != null) {
                registerHandler.notifyLoginSuccess(notifyLoginDto);
            }
        } catch (Exception e) {
            logger.error("userLoginNotify 业务异常，secondAppKey={}，userId={}", secondAppKey, userId);
            throw new BusinessException(-1, "业务异常");
        }
    }
}
