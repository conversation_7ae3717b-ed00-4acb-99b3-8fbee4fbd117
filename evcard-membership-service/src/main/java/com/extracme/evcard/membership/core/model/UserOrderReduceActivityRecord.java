package com.extracme.evcard.membership.core.model;

import java.math.BigDecimal;
import java.util.Date;

public class UserOrderReduceActivityRecord {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_order_reduce_activity_record.id
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_order_reduce_activity_record.auth_id
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    private String authId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_order_reduce_activity_record.order_seq
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    private String orderSeq;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_order_reduce_activity_record.reduce_amount_activity_id
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    private Long reduceAmountActivityId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_order_reduce_activity_record.order_reduce_amount
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    private BigDecimal orderReduceAmount;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_order_reduce_activity_record.status
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    private Integer status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_order_reduce_activity_record.misc_desc
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    private String miscDesc;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_order_reduce_activity_record.create_time
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_order_reduce_activity_record.create_oper_id
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    private Long createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_order_reduce_activity_record.create_oper_name
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    private String createOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_order_reduce_activity_record.update_time
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_order_reduce_activity_record.update_oper_id
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    private Long updateOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_order_reduce_activity_record.update_oper_name
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    private String updateOperName;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_order_reduce_activity_record.id
     *
     * @return the value of user_order_reduce_activity_record.id
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_order_reduce_activity_record.id
     *
     * @param id the value for user_order_reduce_activity_record.id
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_order_reduce_activity_record.auth_id
     *
     * @return the value of user_order_reduce_activity_record.auth_id
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    public String getAuthId() {
        return authId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_order_reduce_activity_record.auth_id
     *
     * @param authId the value for user_order_reduce_activity_record.auth_id
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    public void setAuthId(String authId) {
        this.authId = authId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_order_reduce_activity_record.order_seq
     *
     * @return the value of user_order_reduce_activity_record.order_seq
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    public String getOrderSeq() {
        return orderSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_order_reduce_activity_record.order_seq
     *
     * @param orderSeq the value for user_order_reduce_activity_record.order_seq
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    public void setOrderSeq(String orderSeq) {
        this.orderSeq = orderSeq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_order_reduce_activity_record.reduce_amount_activity_id
     *
     * @return the value of user_order_reduce_activity_record.reduce_amount_activity_id
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    public Long getReduceAmountActivityId() {
        return reduceAmountActivityId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_order_reduce_activity_record.reduce_amount_activity_id
     *
     * @param reduceAmountActivityId the value for user_order_reduce_activity_record.reduce_amount_activity_id
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    public void setReduceAmountActivityId(Long reduceAmountActivityId) {
        this.reduceAmountActivityId = reduceAmountActivityId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_order_reduce_activity_record.order_reduce_amount
     *
     * @return the value of user_order_reduce_activity_record.order_reduce_amount
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    public BigDecimal getOrderReduceAmount() {
        return orderReduceAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_order_reduce_activity_record.order_reduce_amount
     *
     * @param orderReduceAmount the value for user_order_reduce_activity_record.order_reduce_amount
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    public void setOrderReduceAmount(BigDecimal orderReduceAmount) {
        this.orderReduceAmount = orderReduceAmount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_order_reduce_activity_record.status
     *
     * @return the value of user_order_reduce_activity_record.status
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_order_reduce_activity_record.status
     *
     * @param status the value for user_order_reduce_activity_record.status
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_order_reduce_activity_record.misc_desc
     *
     * @return the value of user_order_reduce_activity_record.misc_desc
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    public String getMiscDesc() {
        return miscDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_order_reduce_activity_record.misc_desc
     *
     * @param miscDesc the value for user_order_reduce_activity_record.misc_desc
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_order_reduce_activity_record.create_time
     *
     * @return the value of user_order_reduce_activity_record.create_time
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_order_reduce_activity_record.create_time
     *
     * @param createTime the value for user_order_reduce_activity_record.create_time
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_order_reduce_activity_record.create_oper_id
     *
     * @return the value of user_order_reduce_activity_record.create_oper_id
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    public Long getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_order_reduce_activity_record.create_oper_id
     *
     * @param createOperId the value for user_order_reduce_activity_record.create_oper_id
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_order_reduce_activity_record.create_oper_name
     *
     * @return the value of user_order_reduce_activity_record.create_oper_name
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_order_reduce_activity_record.create_oper_name
     *
     * @param createOperName the value for user_order_reduce_activity_record.create_oper_name
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_order_reduce_activity_record.update_time
     *
     * @return the value of user_order_reduce_activity_record.update_time
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_order_reduce_activity_record.update_time
     *
     * @param updateTime the value for user_order_reduce_activity_record.update_time
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_order_reduce_activity_record.update_oper_id
     *
     * @return the value of user_order_reduce_activity_record.update_oper_id
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    public Long getUpdateOperId() {
        return updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_order_reduce_activity_record.update_oper_id
     *
     * @param updateOperId the value for user_order_reduce_activity_record.update_oper_id
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_order_reduce_activity_record.update_oper_name
     *
     * @return the value of user_order_reduce_activity_record.update_oper_name
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    public String getUpdateOperName() {
        return updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_order_reduce_activity_record.update_oper_name
     *
     * @param updateOperName the value for user_order_reduce_activity_record.update_oper_name
     *
     * @mbggenerated Thu Jan 28 10:10:42 CST 2021
     */
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }
}