package com.extracme.evcard.membership.ocr;

import com.extracme.evcard.membership.core.canstant.OcrChannelConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * OCR服务适配器
 * 读取配置中心的路由，然后调用对应的具体实现类
 *
 * <AUTHOR>
 * @date 2022/8/19
 */
@Slf4j
@Component
public class OcrAdapterImpl implements InitializingBean, OcrAdapter {

    @Value("${id.card.ocr.route}")
    private String idCardOcrRoute;

    @Value("${driving.license.ocr.route}")
    private String drivingLicenseOcrRoute;

    @Autowired
    private List<OcrService> ocrServiceList;

    private static final Map<String, OcrService> OCR_SERVICE_MAP = new HashMap();

    @Override
    public void afterPropertiesSet() throws Exception {
        if (CollectionUtils.isNotEmpty(ocrServiceList)) {
            for (OcrService item : ocrServiceList) {
                OCR_SERVICE_MAP.put(item.getChannel(), item);
            }
        }
    }

    @Override
    public CommonIdCardOcrResp idCardOcr(CommonIdCardOcrReq req) {
        OcrService ocrService;
        String channel = getChannel("id.card.ocr.route", idCardOcrRoute);
        if (channel == null) {
            ocrService = OCR_SERVICE_MAP.get(OcrChannelConstants.BAIDU);
        } else {
            ocrService = OCR_SERVICE_MAP.get(channel);
        }
        CommonIdCardOcrResp commonIdCardOcrResp = ocrService.idCardOcr(req);
        commonIdCardOcrResp.setChannel(channel == null ? OcrChannelConstants.BAIDU : channel);
        return commonIdCardOcrResp;
    }

    @Override
    public CommonDrivingLicenseOcrResp drivingLicenseOcr(CommonDrivingLicenseOcrReq req) {
        OcrService ocrService;
        String channel = getChannel("driving.license.ocr.route", drivingLicenseOcrRoute);
        if (channel == null) {
            ocrService = OCR_SERVICE_MAP.get(OcrChannelConstants.TENCENT);
        } else {
            ocrService = OCR_SERVICE_MAP.get(channel);
        }
        CommonDrivingLicenseOcrResp commonDrivingLicenseOcrResp = ocrService.drivingLicenseOcr(req);
        commonDrivingLicenseOcrResp.setChannel(channel == null ? OcrChannelConstants.TENCENT : channel);
        return commonDrivingLicenseOcrResp;
    }

    public String getChannel(String key, String value) {
        try {
            if (StringUtils.isBlank(value)) {
                return null;
            }

            Map<String, Integer> channelWeightMap = new HashMap<>();
            // value 格式如：baidu=80,textin=20
            String[] array = value.split(",");
            if (!ObjectUtils.isEmpty(array)) {
                for (String item : array) {
                    String[] itemArray = item.split("=");
                    if (itemArray != null && itemArray.length == 2) {
                        channelWeightMap.put(itemArray[0], Integer.parseInt(itemArray[1]));
                    }
                }
            }
            if (MapUtils.isEmpty(channelWeightMap)) {
                return null;
            }

            // 根据路由，给各个渠道分配范围
            int low = 0; // 范围的下限，从0开始
            List<ChannelRangeDto> channelRangeList = new ArrayList<>();
            for (Map.Entry<String, Integer> entry : channelWeightMap.entrySet()) {
                String channel = entry.getKey();
                Integer weight = entry.getValue();
                channelRangeList.add(new ChannelRangeDto(low, low + weight - 1, channel));
                low += weight;
            }

            // 获取总权重
            int totalWeight = channelWeightMap.values().stream().reduce(0, (a, b) -> a + b);
            // 看随机数落在哪个渠道的范围内
            int random = (int) (totalWeight * Math.random());
            for (ChannelRangeDto range : channelRangeList) {
                if (random >= range.getLow() && random <= range.getHigh()) {
                    return range.getChannel();
                }
            }
        } catch (NumberFormatException e) {
            log.error("配置中心key[{}]value[{}]非法，正确格式如：baidu=80,textin=20", key, value, e);
        }
        return null;
    }

}
