package com.extracme.evcard.membership.common;

import com.extracme.evcard.membership.config.CommConfigUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.io.*;
import java.util.*;

/**
 * <p>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @Date 2020/6/23 15:08
 */
public class YiGuanTraceSearchUsersUtil {

    private static Logger logger = LoggerFactory.getLogger(YiGuanTraceSearchUsersUtil.class);

    public static String searchUser(int page,int pageSize){
        String result = "";
        try {
            String url;
            String APP_KEY;
            String token;
            String cohortCode;
            String env = CommConfigUtil.getENV();
            if("prod".equals(env)) {
                APP_KEY = "20456cc3aeaefd7f";
                token = "943d6d062926b315fef7da99ff356710";
                url = "http://hl.evcard.vip:4005/uba/api/cohort/users";
                cohortCode = "arkfq_943";
            } else {
                APP_KEY = "9c03f8b2095d71a2";
                token = "45a82b06c7dfeb9cad261dace5ab5762";
                url = "http://hl.evcard.vip:4005/uba/api/cohort/users";
                cohortCode = "arkfq_4";
            }
            String UTF = "UTF-8";
            CloseableHttpClient client = HttpClients.createDefault();
            HttpPost httpPost = new HttpPost(url);
            //Header 参数
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("appKey", APP_KEY);
            httpPost.setHeader("token", token);
            // post body params
            Map<String, Object> params = new HashMap<>();

            params.put("cohortCode", cohortCode);
            params.put("page", page);
            params.put("pageSize", pageSize);
            List<String> properties = Arrays.asList("xwho");
            params.put("properties", properties);

            ObjectMapper objectMapper = new ObjectMapper();
            StringEntity stringEntity = new StringEntity(objectMapper.writeValueAsString(params), UTF);
            stringEntity.setContentEncoding(UTF);
            httpPost.setEntity(stringEntity);
            //set timeout
            RequestConfig reqConfig = RequestConfig.custom()
                    .setSocketTimeout(10 * 60 * 1000).setConnectTimeout(60 * 1000).build();
            httpPost.setConfig(reqConfig);
            try {
//                logger.info("searchUser入參[{}]", JSON.toString(params));
                CloseableHttpResponse response = client.execute(httpPost);
//                logger.info("searchUser调用易观返回[{}]", JSON.toString(response));
                if (response.getStatusLine().getStatusCode() == 200) {
                    HttpEntity entity = response.getEntity();
                    if (entity != null) {
                        result = EntityUtils.toString(entity, "UTF-8");
                        response.close();
                    }
                } else {
                    logger.error("请求http地址：" + httpPost.getURI().getPath() + "失败，错误码是：" + response.getStatusLine().getStatusCode());
                    httpPost.abort();
                }
            } catch (Exception e) {
                logger.error("易观埋点，获取易观缺失用户信息失败", e);
            }
        } catch (Exception e) {
            logger.error("易观埋点，获取易观缺失用户信息失败", e);
        }
        logger.info("searchUser返回[{}]", result);
        return result;
    }

}
