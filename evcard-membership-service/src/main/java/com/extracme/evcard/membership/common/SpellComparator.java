package com.extracme.evcard.membership.common;

import java.util.Comparator;

/**
 * @ClassName: SpellComparator
 * @Author: wudi
 * @Date: 2020/3/31 11:02
 */
public class SpellComparator implements Comparator<Object> {
    @Override
    public int compare(Object o1, Object o2) {
        try{
            String s1 = new String(o1.toString().getBytes("GB2312"), "ISO-8859-1");
            String s2 = new String(o2.toString().getBytes("GB2312"), "ISO-8859-1");
            return s1.compareTo(s2);
        }catch (Exception e){
            e.printStackTrace();
        }
        return 0;
    }
}
