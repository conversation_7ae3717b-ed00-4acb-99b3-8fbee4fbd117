package com.extracme.evcard.membership.mq;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.dto.MmpUserTagDto;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.membership.credit.dto.MemberPointsOfferDto;
import com.extracme.evcard.membership.credit.service.IMemberPointsService;
import com.extracme.evcard.mq.bean.EventEnum;
import com.extracme.evcard.mq.bean.MemPointsPushEnum;
import com.extracme.evcard.mq.bean.event.*;
import com.extracme.evcard.protobuf.ProtobufUtil;
import com.extracme.evcard.rpc.entity.HidLog;
import com.extracme.evcard.rpc.entity.LogPoint;
import com.extracme.evcard.rpc.order.dto.OrderInfoDto;
import com.extracme.evcard.rpc.order.service.IOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 推送积分发放事件
 * 订单支付/押金充值/e币充值
 * <AUTHOR>
 * @Discription
 * @date 2020/8/19
 */
@Slf4j
@Component("memberRawCreditsListener")
public class MemberRawCreditsListener implements MessageListener {

    @Resource
    private IMemberPointsService memberPointsService;

    @Resource
    private IMemberShipService memberShipService;

    @Resource
    IOrderService orderService;


    @Override
    public Action consume(Message message, ConsumeContext context) {
        String tag = message.getTag();
        try{
            log.info("积分相关事件消费：tag={}, msgId={}", tag, message.getMsgID());
            //积分发送事件触发
            if(StringUtils.equalsIgnoreCase(tag, EventEnum.MEMBER_AUDIT.toString())) {
                MemberAudit memberAudit = new MemberAudit();
                ProtobufUtil.deserializeProtobuf(message.getBody(), memberAudit);
                handleMemberAuditEvent(memberAudit);
            } else if(StringUtils.equalsIgnoreCase(tag, EventEnum.MEMBER_TO_AUDIT_IDCARD.toString())) {
                MemberToAudit memberAudit = new MemberToAudit();
                ProtobufUtil.deserializeProtobuf(message.getBody(), memberAudit);
                handleMemberToAuditEvent(memberAudit);
            } else if(StringUtils.equalsIgnoreCase(tag, EventEnum.ORDER_PAY_NEW.toString())) {
                /**
                 * 订单支付完成 --》积分发放：  订单支付/拍照还车/首单
                 */
                OrderPay orderPay = new OrderPay();
                ProtobufUtil.deserializeProtobuf(message.getBody(), orderPay);
                handleOrderPayEvent(orderPay);
            }
            /*else if(StringUtils.equalsIgnoreCase(tag, EventEnum.CHARGE_DEPOSIT.toString())) {
                //押金充值 --》积分发放
                ChargeDeposit chargeDeposit = new ChargeDeposit();
                ProtobufUtil.deserializeProtobuf(message.getBody(), chargeDeposit);
                handleChargeDepositEvent(chargeDeposit);
            } else if(StringUtils.equalsIgnoreCase(tag, EventEnum.MEMBER_RECHARGE_ECOIN.toString())) {
                //e币充值 --》积分发放
                MemberRechargeEcoin memberRechargeEcoin = new MemberRechargeEcoin();
                ProtobufUtil.deserializeProtobuf(message.getBody(), memberRechargeEcoin);
                handleEcoinRechargeEvent(memberRechargeEcoin);
            }*/
            else if(StringUtils.equalsIgnoreCase(tag, EventEnum.ORDER_SUBMIT_ACCESS.toString())) {
                //订单评价
                OrderSubmitAssess submitAssess = new OrderSubmitAssess();
                ProtobufUtil.deserializeProtobuf(message.getBody(), submitAssess);
                handleOrderAssessEvent(submitAssess);
            }
            else if(StringUtils.equalsIgnoreCase(tag, EventEnum.REPORT_VEHICLE_CONDITION.toString())) {
                //车况上报
                ReportVehicleCondition reportVehicleCondition = new ReportVehicleCondition();
                ProtobufUtil.deserializeProtobuf(message.getBody(), reportVehicleCondition);
                handleVehicleFeedbackEvent(reportVehicleCondition);
            }
            else if(StringUtils.equalsIgnoreCase(tag, EventEnum.MEMBER_DAILY_SIGN.toString())) {
                //每日签到
                MemberDailySign dailySign = new MemberDailySign();
                ProtobufUtil.deserializeProtobuf(message.getBody(), dailySign);
                handleDailySignEvent(dailySign);
            }
            else if(StringUtils.equalsIgnoreCase(tag, EventEnum.MEMBER_ORDER_ACHIEVEMENT.toString())) {
                //订单成就任务达成
                MemberOrderAchieve orderAchieve = new MemberOrderAchieve();
                ProtobufUtil.deserializeProtobuf(message.getBody(), orderAchieve);
                handleOrderAchieveEvent(orderAchieve);
            }
            else if(StringUtils.equalsIgnoreCase(tag, EventEnum.MEMBER_INVITATION_COMPLETE.toString())) {
                //好友邀请任务达成
                MemberInviteSuccess inviteSuccess = new MemberInviteSuccess();
                ProtobufUtil.deserializeProtobuf(message.getBody(), inviteSuccess);
                handleMemberInviteSuccessEvent(inviteSuccess);
            }
            else if(StringUtils.equalsIgnoreCase(tag, EventEnum.ORDER_SHARE_SUCCESS.toString())) {
                //订单分享任务达成
                OrderShareSuccess orderShareSuccess = new OrderShareSuccess();
                ProtobufUtil.deserializeProtobuf(message.getBody(), orderShareSuccess);
                handleOrderShareSuccessEvent(orderShareSuccess);
            }
        }
        catch (Exception ex) {
            log.warn("消费消息失败: " +  message.toString(), ex);
            return Action.ReconsumeLater;
        }
        return Action.CommitMessage;
    }

    public void handleMemberAuditEvent(MemberAudit memberAudit){
        log.info("MemberPoints：事件消费-会员审核，input={}", JSON.toJSONString(memberAudit));
        String authId = memberAudit.getAuthId();
        if(!StringUtils.equals(memberAudit.getReviewStatus(), "1") || memberAudit.getMemberType() != 0) {
            log.warn("MemberPoints：事件消费-会员审核非外部会员审核通过，不发放积分, authId={}, input={}", authId, JSON.toJSONString(memberAudit));
            return;
        }
        //会员审核事件
        MemberPointsOfferDto offerDto = new MemberPointsOfferDto();
        offerDto.setAuthId(authId);
        offerDto.setPointsType(BASIC_POINTS_TYPE);
        offerDto.setEventType(MemPointsPushEnum.AUTHENTICATE_PASS.getCode());
        offerDto.setCreateTime(new Date());
        offerDto.setEventRefSeq(StringUtils.EMPTY);
        offerDto.setDetails(ComUtil.toJSONString(memberAudit));
        log.info("MemberPoints：推送【审核认证通过】奖励, authId={}, input={}", authId, JSON.toJSONString(memberAudit));
        HidLog.membership(LogPoint.MEMBER_POINTS_PUSH, MemPointsPushEnum.AUTHENTICATE_PASS.getTitle(), authId);
        memberPointsService.asyncOfferPoints(offerDto);
    }

    public void handleMemberToAuditEvent(MemberToAudit memberAudit){
        log.info("MemberPoints：事件消费-会员提交人工审核，input={}", JSON.toJSONString(memberAudit));
        String authId = memberAudit.getAuthId();
        if(!memberAudit.getCertType().equals(1)) {
            log.warn("MemberPoints：事件消费-会员提交人工审核, 非身份证件提审，不发放积分, authId={}, input={}", authId, JSON.toJSONString(memberAudit));
            return;
        }
        //会员身份证件提交人工审核同样发放 身份认证积分 --> 奖励前置，推动用户先门店预约。
        MemberPointsOfferDto offerDto = new MemberPointsOfferDto();
        offerDto.setAuthId(authId);
        offerDto.setPointsType(BASIC_POINTS_TYPE);
        offerDto.setEventType(MemPointsPushEnum.AUTHENTICATE_PASS.getCode());
        offerDto.setCreateTime(new Date());
        offerDto.setEventRefSeq(StringUtils.EMPTY);
        offerDto.setDetails(ComUtil.toJSONString(memberAudit));
        log.info("MemberPoints：推送【审核认证通过】奖励-身份证件提交审核, authId={}, input={}", authId, JSON.toJSONString(memberAudit));
        HidLog.membership(LogPoint.MEMBER_POINTS_PUSH, MemPointsPushEnum.AUTHENTICATE_PASS.getTitle(), authId);
        memberPointsService.asyncOfferPoints(offerDto);
    }


    private static final String BASIC_POINTS_TYPE = "01";
    public void handleOrderPayEvent(OrderPay orderPay){
        log.info("MemberPoints：事件消费-订单支付，orderSeq={}, authId={}", orderPay.getOrderSeq(), orderPay.getAuthId());
        /**
         * 订单支付完成:  订单支付积分发放/拍照还车/首单积分发放
         */
        String authId = orderPay.getAuthId();
        String orderSeq = orderPay.getOrderSeq();
        if(!isValidOrder(orderSeq, orderPay.getOrderType())) {
            log.info("MemberPoints：订单支付，orderSeq={}, authId={} 非外部订单或短租订单，不做积分发放。", orderSeq, authId);
            return;
        }
        BigDecimal amount = BigDecimal.ZERO;
        if(orderPay.getRealAmount() != null) {
            amount = amount.add(orderPay.getRealAmount());
        }
        if(orderPay.getBillTimePrecharge() != null) {
            amount = amount.add(orderPay.getBillTimePrecharge());
        }
        amount = amount.multiply(new BigDecimal(100));
        /**
         * 1.  订单支付完成积分赠送(积分数 = fx(订单实付金额))
         */
        MemberPointsOfferDto offerDto = new MemberPointsOfferDto();
        offerDto.setAuthId(authId);
        offerDto.setPointsType(BASIC_POINTS_TYPE);
        if(BigDecimal.ZERO.compareTo(amount) < 0) {
            offerDto.setEventType(MemPointsPushEnum.ORDER_PAY.getCode());
            offerDto.setCreateTime(new Date());
            offerDto.setEventRefSeq(orderSeq);
            offerDto.setAmount(amount);
            Map<String, Object> properties = new HashMap<>();
            properties.put("orderSeq", orderSeq);
            properties.put("realAmount", orderPay.getRealAmount());
            properties.put("rechargeEAmount", orderPay.getBillTimePrecharge());
            properties.put("totalEAmount", orderPay.getBillTime());
            offerDto.setDetails(ComUtil.toJSONString(properties));
            log.info("MemberPoints：推送订单支付完成积分奖励, authId={}, orderSeq={}, input={}", authId, orderSeq, JSON.toJSONString(offerDto));
            HidLog.membership(LogPoint.MEMBER_POINTS_PUSH, MemPointsPushEnum.ORDER_PAY.getTitle() + "_" + orderSeq, authId);
            memberPointsService.asyncOfferPoints(offerDto);
        }else {
            log.info("MemberPoints：订单orderSeq={}, authId={} 订单实付现金金额{}元，充值e币抵扣{}元，不做积分发放。",
                    orderSeq, authId, orderPay.getRealAmount(), orderPay.getBillTimePrecharge());
        }
        /**
         * 2.  拍照还车订单支付完成积分赠送(记一次，赠送固定积分)
         */
        if(orderService.queryIsSubmitPicture(orderSeq) > 0) {
            offerDto.setEventType(MemPointsPushEnum.TAKE_PHOTO_RETURN_VEHICLE.getCode());
            offerDto.setCreateTime(new Date());
            offerDto.setEventRefSeq(orderSeq);
            offerDto.setAmount(amount);
            Map<String, Object> properties = new HashMap<>();
            properties.put("orderSeq", orderSeq);
            offerDto.setDetails(ComUtil.toJSONString(properties));
            log.info("MemberPoints：推送拍照还车订单支付完成积分奖励, authId={}, orderSeq={}, input={}", authId, orderSeq, JSON.toJSONString(offerDto));
            HidLog.membership(LogPoint.MEMBER_POINTS_PUSH, MemPointsPushEnum.TAKE_PHOTO_RETURN_VEHICLE.getTitle() + "_" + orderSeq, authId);
            memberPointsService.asyncOfferPoints(offerDto);
        }
        /**
         * 3. 若为首单则推送首单奖励积分
         */
        MmpUserTagDto tag = memberShipService.queryUserTagByAuthId(authId);
        if(tag != null && StringUtils.equals(tag.getSpare1(), orderSeq)) {
            offerDto.setEventType(MemPointsPushEnum.MEMBER_FIRST_ORDER.getCode());
            offerDto.setCreateTime(new Date());
            offerDto.setEventRefSeq(orderSeq);
            offerDto.setAmount(amount);
            Map<String, Object> properties = new HashMap<>();
            properties.put("orderSeq", orderSeq);
            offerDto.setDetails(ComUtil.toJSONString(properties));
            log.info("MemberPoints：推送首单支付完成积分奖励, authId={}, orderSeq={}, input={}", authId, orderSeq, JSON.toJSONString(offerDto));
            HidLog.membership(LogPoint.MEMBER_POINTS_PUSH, MemPointsPushEnum.MEMBER_FIRST_ORDER.getTitle() + "_" + orderSeq, authId);
            memberPointsService.asyncOfferPoints(offerDto);
        }
    }

    public void handleChargeDepositEvent(ChargeDeposit chargeDeposit){
        log.info("MemberPoints：事件消费-押金支付，input={}", JSON.toJSONString(chargeDeposit));
        String authId = chargeDeposit.getAuthId();
        if(!NumberUtils.INTEGER_ONE.equals(chargeDeposit.getChargeType())) {
            log.debug("MemberPoints：银联预授权/芝麻信用代扣不奖励会员积分, authId={}, input={}", authId, JSON.toJSONString(chargeDeposit));
            return;
        }
        //押金充值事件
        MemberPointsOfferDto offerDto = new MemberPointsOfferDto();
        offerDto.setAuthId(authId);
        offerDto.setPointsType(BASIC_POINTS_TYPE);
        offerDto.setEventType(MemPointsPushEnum.RECHARGE_DEPOSIT.getCode());
        offerDto.setCreateTime(new Date());
        offerDto.setEventRefSeq(StringUtils.EMPTY);
        offerDto.setAmount(chargeDeposit.getAmount());
        offerDto.setDetails(ComUtil.toJSONString(chargeDeposit));
        log.info("MemberPoints：推送【押金充值积分】奖励, authId={}, input={}", authId, JSON.toJSONString(chargeDeposit));
        HidLog.membership(LogPoint.MEMBER_POINTS_PUSH, MemPointsPushEnum.RECHARGE_DEPOSIT.getTitle(), authId);
        memberPointsService.asyncOfferPoints(offerDto);
    }

    public void handleEcoinRechargeEvent(MemberRechargeEcoin memberRechargeEcoin){
        log.info("MemberPoints：事件消费-E币充值，input={}", JSON.toJSONString(memberRechargeEcoin));
        //e币充值事件
        String authId = memberRechargeEcoin.getAuthId();
        //押金充值事件
        MemberPointsOfferDto offerDto = new MemberPointsOfferDto();
        offerDto.setAuthId(authId);
        offerDto.setPointsType(BASIC_POINTS_TYPE);
        offerDto.setEventType(MemPointsPushEnum.RECHARGE_E_AMOUNT.getCode());
        offerDto.setCreateTime(memberRechargeEcoin.getRechargeTime());
        offerDto.setEventRefSeq(memberRechargeEcoin.getOutTradeSeq());
        offerDto.setAmount(memberRechargeEcoin.getRechargeAmount());
        offerDto.setDetails(ComUtil.toJSONString(memberRechargeEcoin));
        log.info("MemberPoints：推送【E币充值积分】奖励, authId={}, input={}", authId, JSON.toJSONString(memberRechargeEcoin));
        HidLog.membership(LogPoint.MEMBER_POINTS_PUSH, MemPointsPushEnum.RECHARGE_E_AMOUNT.getTitle() + "_" + memberRechargeEcoin.getOutTradeSeq(), authId);
        memberPointsService.asyncOfferPoints(offerDto);
    }

    public void handleOrderAssessEvent(OrderSubmitAssess submitAssess){
        log.info("MemberPoints：事件消费-订单评价，input={}", JSON.toJSONString(submitAssess));
        if(!"2".equals(String.valueOf(submitAssess.getAssessType()))) {
            log.info("MemberPoints：事件消费-订单评价，不为订单完成后评价，不发放积分，input={}", JSON.toJSONString(submitAssess));
            return;
        }
        String authId = submitAssess.getAuthId();
        String orderSeq = submitAssess.getOrderSeq();
        if(StringUtils.isBlank(orderSeq)) {
            return;
        }
        MemberPointsOfferDto offerDto = new MemberPointsOfferDto();
        offerDto.setAuthId(authId);
        offerDto.setPointsType(BASIC_POINTS_TYPE);
        offerDto.setEventType(MemPointsPushEnum.ORDER_ASSESS.getCode());
        offerDto.setCreateTime(submitAssess.getDateTime());
        offerDto.setEventRefSeq(submitAssess.getOrderSeq());
        offerDto.setDetails(ComUtil.toJSONString(submitAssess));
        OrderInfoDto order = orderService.getOrderInfoById(submitAssess.getOrderSeq());
        if(order == null || !isValidOrder(orderSeq, order.getOrderType())) {
            log.info("MemberPoints：订单评价，orderSeq={}, authId={} 非外部订单或短租订单，不做积分发放。", orderSeq, authId);
            return;
        }
        BigDecimal amount = BigDecimal.ZERO;
        if(order.getRealAmount() != null) {
            amount = amount.add(order.getRealAmount());
        }
        if(order.getBillTimePrecharge() != null) {
            amount = amount.add(order.getBillTimePrecharge());
        }
        amount = amount.multiply(new BigDecimal(100));
        offerDto.setAmount(amount);
        log.info("MemberPoints：推送【订单评价】积分奖励, authId={}, input={}", authId, JSON.toJSONString(submitAssess));
        HidLog.membership(LogPoint.MEMBER_POINTS_PUSH, MemPointsPushEnum.ORDER_ASSESS.getTitle() + "_" + submitAssess.getOrderSeq(), authId);
        memberPointsService.asyncOfferPoints(offerDto);
    }

    public void handleVehicleFeedbackEvent(ReportVehicleCondition reportVehicleCondition){
        log.info("MemberPoints：事件消费-车况上报，input={}", JSON.toJSONString(reportVehicleCondition));
        String authId = reportVehicleCondition.getAuthId();
        if(StringUtils.isBlank(authId)) {
            return;
        }
        MemberPointsOfferDto offerDto = new MemberPointsOfferDto();
        offerDto.setAuthId(authId);
        offerDto.setPointsType(BASIC_POINTS_TYPE);
        offerDto.setEventType(MemPointsPushEnum.VEHICLE_FEEDBACK.getCode());
        offerDto.setCreateTime(reportVehicleCondition.getDateTime());
        offerDto.setAmount(BigDecimal.ZERO);
        if(reportVehicleCondition.getImageNum() != null) {
            offerDto.setAmount(new BigDecimal(reportVehicleCondition.getImageNum()));
        }
        Map<String, Object> properties = new HashMap<>();
        properties.put("orderSeq", reportVehicleCondition.getOrderSeq());
        properties.put("id", reportVehicleCondition.getId());
        offerDto.setDetails(ComUtil.toJSONString(properties));
        log.info("MemberPoints：推送【车况上报】积分奖励, authId={}, input={}", authId, JSON.toJSONString(properties));
        HidLog.membership(LogPoint.MEMBER_POINTS_PUSH, MemPointsPushEnum.VEHICLE_FEEDBACK.getTitle() + "_" + reportVehicleCondition.getId(), authId);
        memberPointsService.asyncOfferPoints(offerDto);
    }

    public void handleDailySignEvent(MemberDailySign dailySign){
        log.info("MemberPoints：事件消费-每日签到，input={}", JSON.toJSONString(dailySign));
        String authId = dailySign.getAuthId();
        if(StringUtils.isBlank(authId)) {
            return;
        }
        MemberPointsOfferDto offerDto = new MemberPointsOfferDto();
        offerDto.setAuthId(authId);
        offerDto.setPointsType(BASIC_POINTS_TYPE);
        offerDto.setEventType(MemPointsPushEnum.DAILY_ATTENDANCE.getCode());
        offerDto.setCreateTime(dailySign.getSignTime());
        offerDto.setDetails(ComUtil.toJSONString(dailySign));
        log.info("MemberPoints：推送【会员签到】积分奖励, authId={}, input={}", authId, JSON.toJSONString(dailySign));
        HidLog.membership(LogPoint.MEMBER_POINTS_PUSH, MemPointsPushEnum.DAILY_ATTENDANCE.getTitle() + "_" + dailySign.getTaskId(), authId);
        memberPointsService.asyncOfferPoints(offerDto);
    }


    public void handleOrderAchieveEvent(MemberOrderAchieve orderAchieve){
        log.info("MemberPoints：事件消费-订单成就，input={}", JSON.toJSONString(orderAchieve));
        String authId = orderAchieve.getAuthId();
        if(StringUtils.isBlank(authId)) {
            return;
        }
        MemberPointsOfferDto offerDto = new MemberPointsOfferDto();
        offerDto.setAuthId(authId);
        offerDto.setPointsType(BASIC_POINTS_TYPE);
        offerDto.setEventType(MemPointsPushEnum.ORDER_ACHIEVEMENT.getCode());
        offerDto.setEventRefSeq(orderAchieve.getOrderSeq());
        offerDto.setCreateTime(orderAchieve.getDateTime());
        offerDto.setDetails(ComUtil.toJSONString(orderAchieve));
        log.info("MemberPoints：推送【会员订单成就达成】积分奖励, authId={}, input={}", authId, JSON.toJSONString(orderAchieve));
        HidLog.membership(LogPoint.MEMBER_POINTS_PUSH, MemPointsPushEnum.ORDER_ACHIEVEMENT.getTitle() + "_" + orderAchieve.getOrderSeq(), authId);
        memberPointsService.asyncOfferPoints(offerDto);
    }

    public void handleMemberInviteSuccessEvent(MemberInviteSuccess inviteSuccess){
        log.info("MemberPoints：事件消费-邀请完成，input={}", JSON.toJSONString(inviteSuccess));
        String authId = inviteSuccess.getInviterAuthId();
        MemberPointsOfferDto offerDto = new MemberPointsOfferDto();
        offerDto.setAuthId(authId);
        offerDto.setPointsType(BASIC_POINTS_TYPE);
        offerDto.setEventType(MemPointsPushEnum.MEMBER_INVITATION.getCode());
        offerDto.setEventRefSeq(inviteSuccess.getAuthId());
        offerDto.setCreateTime(inviteSuccess.getPayTime());
        offerDto.setDetails(ComUtil.toJSONString(inviteSuccess));
        log.info("MemberPoints：推送【邀请完成】积分奖励, authId={}, input={}", authId, JSON.toJSONString(inviteSuccess));
        HidLog.membership(LogPoint.MEMBER_POINTS_PUSH, MemPointsPushEnum.MEMBER_INVITATION.getTitle() + "_"
                + inviteSuccess.getOrderSeq() + "_" + inviteSuccess.getAuthId(), authId);
        memberPointsService.asyncOfferPoints(offerDto);
    }

    public void handleOrderShareSuccessEvent(OrderShareSuccess orderShareSuccess){
        log.info("MemberPoints：事件消费-订单分享完成，input={}", JSON.toJSONString(orderShareSuccess));
        String authId = orderShareSuccess.getAuthId();
        MemberPointsOfferDto offerDto = new MemberPointsOfferDto();
        offerDto.setAuthId(authId);
        offerDto.setPointsType(BASIC_POINTS_TYPE);
        offerDto.setEventType(MemPointsPushEnum.ORDER_SHARE.getCode());
        offerDto.setEventRefSeq(orderShareSuccess.getOrderSeq());
        offerDto.setCreateTime(orderShareSuccess.getDateTime());
        offerDto.setDetails(ComUtil.toJSONString(orderShareSuccess));
        log.info("MemberPoints：推送【订单分享完成】积分奖励, authId={}, input={}", authId, JSON.toJSONString(orderShareSuccess));
        HidLog.membership(LogPoint.MEMBER_POINTS_PUSH, MemPointsPushEnum.ORDER_SHARE.getTitle() + "_"
                + orderShareSuccess.getOrderSeq() + "_" + orderShareSuccess.getActivityId(), authId);
        memberPointsService.asyncOfferPoints(offerDto);
    }

    private boolean isValidOrder(String orderSeq, Integer orderType){
        if(StringUtils.isBlank(orderSeq) || (!StringUtils.startsWithIgnoreCase(orderSeq, "C")
                && !StringUtils.startsWithIgnoreCase(orderSeq, "D"))) {
            return false;
        }
        if(orderType != null && orderType != 0 && orderType != 3) {
            return false;
        }
        return true;
    }
}

