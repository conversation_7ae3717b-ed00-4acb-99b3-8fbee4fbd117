package com.extracme.evcard.membership.login.util;


import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.contract.service.DepositBaseInfo;
import com.extracme.evcard.membership.contract.service.OfcQueryContractBo;
import com.extracme.evcard.membership.contract.service.OrderBaseInfo;
import com.extracme.evcard.membership.contract.service.OrderFeesBaseInfo;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class FreemarkerUtil {

    public static void main2(String[] args) {
        // 设置 Freemarker 配置
        Configuration configuration = new Configuration(Configuration.VERSION_2_3_31);
        configuration.setClassForTemplateLoading(FreemarkerUtil.class, "/template");

        try {
            // 获取模板
            Template template = configuration.getTemplate("test.ftl");

            // 创建数据模型
            Map<String, Object> dataModel = new HashMap<>();
            dataModel.put("title", "My Static Page");
            dataModel.put("content", "Hello, Freemarker!");
            dataModel.put("name", "Hello, Freemarker!");

            // 指定输出文件
            File outputHtml = new File("output.html");

            // 使用模板引擎填充数据并生成静态页面
            try (FileWriter fileWriter = new FileWriter(outputHtml)) {
                template.process(dataModel, fileWriter);
            }

            System.out.println("Static page generated successfully.");

        } catch (IOException | TemplateException e) {
            e.printStackTrace();
        }
    }

    /**
     *  获取渲染结果
     * @param dataModel
     * @param ftlName
     * @return
     * @throws Exception
     */
    public static ByteArrayOutputStream getTemplate(Map<String, Object> dataModel,String ftlName){
        // 在内存中生成 HTML
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        Writer writer = new OutputStreamWriter(outputStream);
        try {
            // 初始化 FreeMarker 配置
            Configuration configuration = new Configuration(Configuration.VERSION_2_3_31);
            configuration.setClassForTemplateLoading(FreemarkerUtil.class, "/template"); // 模板文件所在的目录

            // 获取 FreeMarker 模板
            Template template = configuration.getTemplate(ftlName); // 替换为您的模板文件名

            // 渲染
            template.process(dataModel, writer);
            writer.flush();
        } catch (TemplateException e) {
           log.error("模板渲染业务异常，dataModel={}，ftlName={}",JSON.toJSONString(dataModel),ftlName,e);
            try {
                outputStream.close();
            } catch (IOException e1) {
                e1.printStackTrace();
            }
            return null;
        } catch (Exception e) {
            log.error("模板渲染失败，dataModel={}，ftlName={}",JSON.toJSONString(dataModel),ftlName,e);
            try {
                outputStream.close();
            } catch (IOException e1) {
                e1.printStackTrace();
            }
            return null;
        } finally {
            try {
                writer.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return outputStream;
    }


    public static void main(String[] args) {
        // 设置 Freemarker 配置
        Configuration configuration = new Configuration(Configuration.VERSION_2_3_31);
        configuration.setClassForTemplateLoading(FreemarkerUtil.class, "/template");

        try {
            // 获取模板
            Template template = configuration.getTemplate("rental.ftl");

            // 创建数据模型
            OfcQueryContractBo bo = new OfcQueryContractBo();

            bo.setName("xl");
            bo.setMobile("1212312312");
            bo.setIdCardNo("1212312312");
            bo.setIdCardType(2);
            bo.setType(5);
            bo.setTodayDate("2023-12-11");

            OrderBaseInfo orderBaseInfo = new OrderBaseInfo();
            orderBaseInfo.setVehicleNo("皖p110");
            orderBaseInfo.setCarInfoName("lanbo");
            orderBaseInfo.setPickUpDate("2023-12-11 09:00:00");
            orderBaseInfo.setReturnDate("2023-12-12 09:00:00");
            orderBaseInfo.setRentDay("1天2小时");
            orderBaseInfo.setPickUpStoreName("合肥");
            orderBaseInfo.setReturnStoreName("南京");
            orderBaseInfo.setPickCarWay("上门取车");
            orderBaseInfo.setReturnCarWay("门店还车");
            orderBaseInfo.setOilType(2);
            orderBaseInfo.setOilCapacity("100");
            orderBaseInfo.setElectricCapacity("200");

            orderBaseInfo.setOrderNo("MC101");
            orderBaseInfo.setThirdOrderNo("1234");
            orderBaseInfo.setFaceLiveResult(0);
            orderBaseInfo.setFaceLiveTime("2023-12-11 09:00:00");
            bo.setOrderBaseInfo(orderBaseInfo);


            DepositBaseInfo depositBaseInfo = new DepositBaseInfo();
            depositBaseInfo.setIllegalDepositDepositWay("芝麻免押");
            depositBaseInfo.setVehicleDepositDepositWay("企业免押");
            bo.setDepositBaseInfo(depositBaseInfo);


            List<OrderFeesBaseInfo> list = new ArrayList<>();
            for (int i = 0; i < 5; i++) {
                OrderFeesBaseInfo info = new OrderFeesBaseInfo();
                info.setName("费用" + i);
                info.setAmount(StringUtils.EMPTY + i);
                list.add(info);
            }
            bo.setOrderFeesBaseInfos(list);

            Map<String, Object> dataModel = new HashMap<>();
            dataModel.put("bo",bo);
            System.out.println(JSON.toJSONString(bo));
            // 指定输出文件
            File outputHtml = new File("output3.html");

            // 使用模板引擎填充数据并生成静态页面
            try (FileWriter fileWriter = new FileWriter(outputHtml)) {
                template.process(dataModel, fileWriter);
            }

            System.out.println("Static page generated successfully.");

        } catch (IOException | TemplateException e) {
            e.printStackTrace();
        }
    }

}
