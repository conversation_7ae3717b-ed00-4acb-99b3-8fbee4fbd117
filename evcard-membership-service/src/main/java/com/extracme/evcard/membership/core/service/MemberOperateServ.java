package com.extracme.evcard.membership.core.service;

import java.util.Calendar;
import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.baosight.iplat4j.security.util.Crypto;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.bean.LoginBeanResult;
import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.dao.AgencyInfoMapper;
import com.extracme.evcard.membership.core.dao.AppDeviceInfoMapper;
import com.extracme.evcard.membership.core.dao.MmpUserOperationLogMapper;
import com.extracme.evcard.membership.core.dao.TEsUserMapper;
import com.extracme.evcard.membership.core.dao.UserOperatorLogMapper;
import com.extracme.evcard.membership.core.dao.VehicleControlUserMapper;
import com.extracme.evcard.membership.core.dto.AccountStatusDto;
import com.extracme.evcard.membership.core.dto.HealthCodeDto;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.dto.OrgUserInfoDto;
import com.extracme.evcard.membership.core.dto.UpdateUserDto;
import com.extracme.evcard.membership.core.enums.MemOperateTypeEnum;
import com.extracme.evcard.membership.core.exception.CheckLoginException;
import com.extracme.evcard.membership.core.exception.UserMessageException;
import com.extracme.evcard.membership.core.input.LoginInput;
import com.extracme.evcard.membership.core.input.UserOperationLogInput;
import com.extracme.evcard.membership.core.model.EnterpriseAgencyInfo;
import com.extracme.evcard.membership.core.model.MmpUserOperationLog;
import com.extracme.evcard.membership.core.model.UserOperatorLog;
import com.extracme.evcard.membership.core.model.VehicleControlUser;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.dao.MmpUserTagMapper;
import com.extracme.evcard.membership.credit.dto.MembershipInfoDto;
import com.extracme.evcard.membership.credit.model.MembershipInfo;
import com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs;
import com.extracme.evcard.membership.credit.model.MmpUserTag;
import com.extracme.evcard.redis.JedisUtil;
import com.extracme.evcard.rpc.entity.HidLog;
import com.extracme.evcard.rpc.entity.LogPoint;
import com.extracme.evcard.rpc.enums.StatusCode;
import com.extracme.evcard.rpc.exception.BusinessRuntimeException;
import com.extracme.evcard.rpc.messagepush.service.IMessagepushServ;
import com.extracme.evcard.rpc.messagepush.service.ISensorsdataService;
import com.extracme.evcard.rpc.util.DateType;

import javax.annotation.Resource;

@Service
public class MemberOperateServ implements IMemberOperateServ {


    Logger logger = (Logger) LoggerFactory.getLogger(MemberOperateServ.class);

    // 加密的密钥
    public static final String ENCRYPT_KEY = "evcardbs";

    private static final int BIND_IMEI_NUM = 200;

    @Autowired
    private TEsUserMapper tEsUserMapper;

    @Autowired
    private VehicleControlUserMapper vehicleControlUserMapper;

    @Autowired
    private AgencyInfoMapper agencyInfoMapper;

    @Autowired
    private MembershipInfoMapper membershipInfoMapper;

    @Autowired
    private AppDeviceInfoMapper appDeviceInfoMapper;

    @Autowired
    private IMessagepushServ messageServ;
    @Autowired
    private UserOperatorLogMapper userOperatorLogMapper;
    
    @Autowired
    MmpUserTagMapper mmpUserTagMapper;
    
    @Autowired
	ISensorsdataService sensorsdataService;

    @Autowired
    IMemberShipService memberShipService;

    @Autowired
    private MmpUserOperationLogMapper mmpUserOperationLogMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LoginBeanResult checkVerifyCodeLogin(LoginInput loginInput) throws UserMessageException{
        try {
            return this.checkVerifyCodeLoginV2(loginInput);
        }catch (CheckLoginException e){
            int code = e.getCode();
            if(code == 30036){
                code = 2;
            } else if(code == 30035){
                code = 3;
            } else {
                code = -1;
            }
            throw new UserMessageException(code, e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LoginBeanResult checkVerifyCodeLoginChange(LoginInput loginInput) throws UserMessageException{
        try {
            return this.checkVerifyCodeLoginV2(loginInput);
        }catch (CheckLoginException e){
            int code = e.getCode();
            if(code == 30036){
                code = 2;
            } else if(code == 30035){
                code = 3;
            } else if(code == 30029){
                code = 4;
            }else {
                code = -1;
            }
            throw new UserMessageException(code, e.getMessage());
        }
    }

    @Override
    public LoginBeanResult checkVerifyCodeLoginV2(LoginInput loginInput) throws CheckLoginException {
        LoginBeanResult loginBeanResult = new LoginBeanResult();
        loginBeanResult.setAppType(loginInput.getAppType());
        Integer checkType = loginInput.getCheckType();
        if(checkType == null) {
            checkType = 0;
        }
        if (checkType.equals(2)) {
            //新版本不保存imei，只进行验证码校验，先判断验证码
            Boolean verifyCodeResult = checkVerifyCode(loginInput.getLoginName() ,loginInput.getVerifyCode(), false);
            if (!verifyCodeResult) {
                throw new CheckLoginException(StatusCode.VALIDATE_CODE_ERROR);
            }
        }
        //判断用户是否存在
        MembershipInfoDto membershipInfoDto = membershipInfoMapper.getMemberInfoByPhone(loginInput.getLoginName(), loginInput.getOrgId());
        if (membershipInfoDto == null){
            throw new CheckLoginException(StatusCode.USER_INFO_NO_EXIST);
        }
        /**
         * 判断设备是否在黑名单
         */
        if (memberShipService.checkImeiBlackList(loginInput.getImei())) {
            HidLog.membership(LogPoint.PWD_LOGIN, "验证码登录，设备号在黑名单,入参:" + JSON.toJSONString(loginInput), membershipInfoDto.getAuthId(), false);
            throw new CheckLoginException(StatusCode.UPDATE_FAILED);
        }
        /**
         * 验证账号是否被注销
         */
        AccountStatusDto accountStatusDto = memberShipService.getAccountStatusByMobileV2(loginInput.getLoginName(), 0,loginInput.getOrgId());
        if (accountStatusDto != null) {
            if (accountStatusDto.getAccountStatus() == 1) {
                throw new CheckLoginException(StatusCode.MOBILE_PHONE_LOGOUTED);
            }
            if(accountStatusDto.getAccountStatus() == 2){
                throw new CheckLoginException(StatusCode.USER_INFO_NO_EXIST);
            }
        }
        String imei = loginInput.getImei();
        String authId = membershipInfoDto.getAuthId();
        int appType = loginInput.getAppType();
        String mobilePhone = loginInput.getLoginName();
        String verifyCode = loginInput.getVerifyCode();

        if (checkType.equals(2)){
            //新版本不保存imei，只进行验证码校验
//            Boolean verifyCodeResult = checkVerifyCode(mobilePhone,verifyCode,5,true);
//            if (!verifyCodeResult){
//                throw new CheckLoginException(StatusCode.VALIDATE_CODE_ERROR);
//            }
            if (loginInput.getBindImei() != null && loginInput.getBindImei().equals(1)){
                changeImei(loginInput.getImei(),loginInput.getLoginName().trim(),membershipInfoDto.getAuthId(),2);
            }

        }else{
            checkImeiAndVerifyCode(imei, authId, appType, mobilePhone, verifyCode, 1, checkType);
        }

        loginBeanResult.setToken(loginInput.getToken());
        loginBeanResult.setDisplayName(membershipInfoDto.getName());
        loginBeanResult.setAuthId(membershipInfoDto.getAuthId());
        loginBeanResult.setCardNo(membershipInfoDto.getCardNo());
        loginBeanResult.setAgencyId(membershipInfoDto.getAgencyId());
        // 0:个人用户 1：企业用户
        loginBeanResult.setOrgUser(0);
        loginBeanResult.setMemberType(membershipInfoDto.getMembershipType());
        loginBeanResult.setPkId(membershipInfoDto.getPkId());
        loginBeanResult.setOrgId(loginInput.getOrgId());
        // 修改会员条款版本
        String serviceVer = membershipInfoDto.getServiceVer();
        checkServiceVer(loginBeanResult,serviceVer);
        this.bindChannel(loginInput.getChannelId(), loginBeanResult.getAuthId(), membershipInfoDto.getMembershipType());
        logger.debug("验证码登录参数{},返回值{}",JSON.toJSONString(loginInput),JSON.toJSONString(loginBeanResult));

        HidLog.membership(LogPoint.PWD_LOGIN, "验证码登录", membershipInfoDto.getAuthId(), true);

        return loginBeanResult;
    }
    public  void  checkHealthCode(String authId){
        try {
            HealthCodeDto healthCodeDto = memberShipService.queryHealthCode(authId,1);

            if (healthCodeDto == null){
                return;
            }
            String checked = JedisUtil.get("healthCodeCheck_" + authId);
            if(StringUtils.isBlank(checked)){
                //当日没有校验过健康码
                //当日没有校验过健康码
                HealthCodeDto healthCodeDto1 = memberShipService.queryHealthCode(authId,2);
                if (healthCodeDto1 != null){
                    JedisUtil.set("healthCodeCheck_" + authId,"1",getSecondsNextEarlyMorning().intValue());
                }
            }
        }catch (Exception e){
            logger.info("checktoken查询健康码失败");
        }

    }
    public Long getSecondsNextEarlyMorning() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_YEAR, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return (cal.getTimeInMillis() - System.currentTimeMillis()) / 1000;
    }

    static LogPoint PWD_LOGIN = new LogPoint("123456", "PWD_LOGIN");
    @Override
    @Transactional(rollbackFor = Exception.class)
    public LoginBeanResult checkLogin(LoginInput loginInput) throws UserMessageException{
        try {
            return this.checkLoginV2(loginInput);
        }catch (CheckLoginException e){
            int code = e.getCode();
            if(code == 30036){
                code = 2;
            } else if(code == 30035){
                code = 3;
            } else {
                code = -1;
            }
            throw new UserMessageException(code, e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LoginBeanResult checkLoginChange(LoginInput loginInput) throws UserMessageException{
        try {
            return this.checkLoginV2(loginInput);
        }catch (CheckLoginException e){
            int code = e.getCode();
            if(code == 30033){
                code = 5;
            } else if(code == 30029){
                code = 6;
            } else if (code == 4){
                code = 4;
            }else if (code == 30076){
                code = 7;
            }else {
                code = -1;
            }
            throw new UserMessageException(code, e.getMessage());
        }
    }

    @Override
    public LoginBeanResult checkLoginV2(LoginInput loginInput) throws CheckLoginException {
        LoginBeanResult loginBeanResult = new LoginBeanResult();
        loginBeanResult.setAppType(loginInput.getAppType());
        if (loginInput.getLoginName() == null || loginInput.getLoginName().length() < 1) {
            throw new CheckLoginException(StatusCode.USER_INFO_NO_EXIST);
        }
        char firstChar = loginInput.getLoginName().charAt(0);
        /**
         * 判断是不是车门控制用户
         * 暂时的理解是 个人用户
         */
        if (loginInput.getLoginOrigin() == 2) {
            // 首字母非数字的情况，为企业用户
            if (firstChar >= '0' && firstChar <= '9') {
                throw new CheckLoginException(StatusCode.ERROR_USER);
            }
        }
        /**
         * 企业用户
         */
        if (firstChar < '0' || firstChar > '9') {
            //0:个人用户 1：企业用户
            loginBeanResult.setOrgUser(1);
            String password = ComUtil.MD5(loginInput.getPassword());
            OrgUserInfoDto orgUserInfoDto = tEsUserMapper.getByNameAndPass(loginInput.getLoginName(), password);
            if (orgUserInfoDto == null) {
                throw new CheckLoginException(StatusCode.PASSWORD_WRONG);
            }
            loginBeanResult.setDisplayName(orgUserInfoDto.getDisplayName());
            loginBeanResult.setAuthId(orgUserInfoDto.getOrgCode());
            loginBeanResult.setAgencyId(orgUserInfoDto.getAgencyId());
            loginBeanResult.setPkId(orgUserInfoDto.getPkId());

            //车辆控制用户登录直接失败
            if (loginInput.getLoginOrigin() == 2) {
                VehicleControlUser controlUser = vehicleControlUserMapper.getUser(loginInput.getLoginName());
                if (controlUser == null) {
                    throw new CheckLoginException(StatusCode.USER_INFO_NO_EXIST);
                }
            }

            EnterpriseAgencyInfo agencyInfoDto = agencyInfoMapper.getAgencyInfoByName(loginInput.getLoginName());
            if (agencyInfoDto == null) {
                throw new CheckLoginException(StatusCode.USER_INFO_NO_EXIST);
            }
            if (agencyInfoDto.getStatus() == null || !agencyInfoDto.getStatus().equals(1)) {
                throw new CheckLoginException(StatusCode.COMPANY_SUSPENDED_USE.getCode(), String.format(StatusCode.COMPANY_SUSPENDED_USE.getMsg(), agencyInfoDto.getAgencyName()));
            }
            loginBeanResult.setAgencyId(agencyInfoDto.getAgencyId());
            loginBeanResult.setOrgName(agencyInfoDto.getAgencyName());
            Integer userType = StringUtils.isBlank(agencyInfoDto.getUserType())?0:Integer.parseInt(agencyInfoDto.getUserType());
            loginBeanResult.setUserType(userType);
            return loginBeanResult;
        } else {
            logger.debug("用户登陆，用户{}", loginInput.getLoginName());
            MembershipInfoDto membershipInfoDto = membershipInfoMapper.getMemberInfoByPhone(loginInput.getLoginName(), loginInput.getOrgId());
            /**
             * 1、验证用户是否存在
             */
            if (membershipInfoDto == null || StringUtils.isBlank(membershipInfoDto.getPassword())) {
                HidLog.membership(LogPoint.PWD_LOGIN, "用户不存在", loginInput.getLoginName(), false);
                throw new CheckLoginException(StatusCode.USER_INFO_NO_EXIST);
            }
            /**
             * 1.1、验证账号是否被注销
             */
            AccountStatusDto accountStatusDto = memberShipService.getAccountStatusByMobileV2(loginInput.getLoginName(), 0,loginInput.getOrgId());
            if (accountStatusDto != null) {
                if (accountStatusDto.getAccountStatus() == 1 || accountStatusDto.getAccountStatus() == 2) {
                    throw new CheckLoginException(StatusCode.MOBILE_PHONE_LOGOUTED);
                }
            }
            /**
             * 2、验证密码是否正确
             */
            String decPwd = Crypto.decryptDES(membershipInfoDto.getPassword(), ENCRYPT_KEY);
            if (!decPwd.equals(loginInput.getPassword())) {
                HidLog.membership(LogPoint.PWD_LOGIN, "用户密码错误", loginInput.getLoginName(), false);
                throw new CheckLoginException(StatusCode.PASSWORD_WRONG);
            }
            /**
             * 3、验证会员的审核状态(0:待审核 1：审核通过 2: 审核不通过 3：用户无效）
             */
            String cardNo = membershipInfoDto.getCardNo();
            loginBeanResult.setToken(loginInput.getToken());
            loginBeanResult.setDisplayName(membershipInfoDto.getName());
            loginBeanResult.setAuthId(membershipInfoDto.getAuthId());
            loginBeanResult.setCardNo(cardNo);
            loginBeanResult.setAgencyId(membershipInfoDto.getAgencyId());
            // 0:个人用户 1：企业用户
            loginBeanResult.setOrgUser(0);
            loginBeanResult.setMemberType(membershipInfoDto.getMembershipType());
            loginBeanResult.setPkId(membershipInfoDto.getPkId());
            loginBeanResult.setOrgId(loginInput.getOrgId());

            /**
             * 4、验证会员条款.
             */
            String serviceVer = membershipInfoDto.getServiceVer();
            checkServiceVer(loginBeanResult,serviceVer);


            /**
             * 5、app登录需要验证IMEI信息
             */
            if (loginInput.getLoginOrigin() == 0) {
                /**
                 * 判断设备是否在黑名单
                 */
                if (memberShipService.checkImeiBlackList(loginInput.getImei())) {
                    HidLog.membership(LogPoint.PWD_LOGIN, "密码登录，设备号在黑名单,入参:" + JSON.toJSONString(loginInput), loginBeanResult.getAuthId(), false);
                    throw new CheckLoginException(StatusCode.UPDATE_FAILED);
                }
            }
            if (loginInput.getLoginOrigin() == 0 &&  loginInput.getCheckType() != null && !loginInput.getCheckType().equals(2)) {
                String imei = loginInput.getImei();
                String authId = loginBeanResult.getAuthId();
                int appType = loginInput.getAppType();
                String mobilePhone = loginInput.getLoginName().trim();
                String verifyCode = loginInput.getVerifyCode();
                checkImeiAndVerifyCode(imei, authId, appType, mobilePhone, verifyCode, 0, 1);
            }
            if (loginInput.getLoginOrigin() == 0 && loginInput.getCheckType() != null && loginInput.getCheckType().equals(2)){
//                changeImei(loginInput.getImei(),loginInput.getLoginName().trim(),loginBeanResult.getAuthId(),1);
            }
            this.bindChannel(loginInput.getChannelId(),loginBeanResult.getAuthId(), loginBeanResult.getMemberType());
            HidLog.membership(LogPoint.PWD_LOGIN, "密码登录", membershipInfoDto.getAuthId(), true);

            //健康码查询
            if (membershipInfoDto != null && membershipInfoDto.getAuthenticationStatus().equals(2) && membershipInfoDto.getReviewStatus().equals(1)) {
                checkHealthCode(membershipInfoDto.getAuthId());
            }
            return loginBeanResult;
        }
    }
    
    private static String verifyCodeTimesKey = "verifycode_nume";
    /**
     * 验证验证码.<br>
     * @param mobilePhone	手机号.<br>
     * @param verifyCode	验证码.<br>
     * @param type			验证码类型.<br>
     * @param delete	true 验证通过之后删除验证码; false 验证通过之后不删除验证码.<br>
     * @return	验证码验证结果.<br>
     */
    @Override
    public boolean checkVerifyCode(String mobilePhone, String verifyCode, Boolean delete) {
    	if(delete == null) {
    		delete = false;
    	}
    	if(StringUtils.isBlank(verifyCode)) {
    		return false;
    	}
    	String checkNum = JedisUtil.hget(verifyCodeTimesKey, mobilePhone);
    	int checkNumInt = 0;
    	/**
    	 * 验证码失败测试超过10，直接失败.<br>
    	 */
    	if(StringUtils.isNotBlank(checkNum)) {
    		checkNumInt = Integer.parseInt(checkNum);
    	}
        if (checkNumInt >= 10) {
            logger.debug(mobilePhone + "验证码超过重试的次数");
            return false; // 验证码不正确
        }
        /**
         * 验证码有效性 
         */
        String smsVerfifyCode = JedisUtil.get(mobilePhone);
        if (!verifyCode.equals(smsVerfifyCode)){
            logger.debug("登录设备检测-输入验证码错误" + verifyCode);
            JedisUtil.hincrby(verifyCodeTimesKey, mobilePhone, 1);
            return false;
        }
        
        /**
         * 验证通过，删除验证码失败册数
         */
        if(checkNumInt > 0) {
        	JedisUtil.hdel(verifyCodeTimesKey, mobilePhone);
        }
        /**
         * 验证通过，删除验证码.<br>
         */
        if(delete) {
        	JedisUtil.del(mobilePhone);
        }
        /**
         * 部分校验第二部校验不验证验证码，需要redis中key值
         */
        JedisUtil.set(BussinessConstants.CHECK_VERIFY_STATUS + mobilePhone,"1",300);
        return true;
    }
    
    @Override
    public boolean checkVerifyCode(String mobilePhone, String verifyCode, Integer type, Boolean delete) {
    	return this.checkVerifyCode(mobilePhone, verifyCode, delete);
    }

    //会员设备绑定变更操作编号
    private static final int OPT_MEM_BINDIMEI = 9;
    /**
     * 检查imei码.<br>
     * @param imei		设备id.<br>
     * @param authId	会员id.<br>
     * @param appType	终端类型.<br>
     * @param mobilePhone	手机号.<br>
     * @param verifyCode	验证码.<br>
     * @param loginType 	0:密码登录, 1:验证码登录.<br>
     * @param checkType		0：发生重新绑定时 不绑定设备而是给出绑定提示；1:发生重新绑定时 直接重新绑定并扣减次数.<br>
     * @throws UserMessageException
     */
    @Transactional(rollbackFor = Exception.class)
	private void checkImeiAndVerifyCode(String imei, String authId, int appType, String mobilePhone, String verifyCode, int loginType, int checkType) throws CheckLoginException {
    	//该账号用于ios审核，特殊处理.
    	if(StringUtils.equals(mobilePhone, "15587654321")) {
    		return;
    	}
    	/**
    	 * 1、验证imei是否为空
    	 */
        if(StringUtils.isBlank(imei)) {
        	throw new CheckLoginException(StatusCode.IMEI_EMPTY);
        }
    	/**
    	 * 2、查询会员imei
    	 */
    	MmpUserTag userTag = mmpUserTagMapper.selectMmpUserByAuthId(authId);
    	String bindImei = StringUtils.EMPTY;
    	if(userTag != null) {
    		bindImei = userTag.getSpare5();
    	}
        /**
         * 3、密码登录，先验证imei是否一致，如果一致return 0;
         */
        if(loginType == 0 && StringUtils.isNotEmpty(bindImei) && bindImei.equals(imei)){
            logger.debug(mobilePhone + "登录设备检测-绑定设备与登录设备相同,imei=" + bindImei);
            return ; // 正常
        }
        /**
         * 4、验证码登录需要先校验验证码
         */
        if(loginType == 1) {
            boolean delete = false;
            if(checkType == 1) {
            	delete = true;
            }
            if(!this.checkVerifyCode(mobilePhone, verifyCode, delete)) {
            	logger.debug(mobilePhone + "验证码错误");
            	throw new CheckLoginException(StatusCode.VALIDATE_CODE_ERROR);
            }
            if (StringUtils.isNotEmpty(bindImei) && bindImei.equals(imei)) {
                logger.debug(mobilePhone + "登录设备检测-绑定设备与登录设备相同,imei=" + bindImei);
                return ; // 正常
            }
        }
        /**
         * 5、验证码登录，在验证完验证码之后 再验证imei
         */
        if(loginType == 1 && StringUtils.isNotEmpty(bindImei) && bindImei.equals(imei)){
            logger.debug(mobilePhone + "登录设备检测-绑定设备与登录设备相同,imei=" + bindImei);
            return ; // 正常
        }
        /**
         * 6、不相同 判断当月设备更换次数是否超过上限
         */
        String changeNum = JedisUtil.get(BussinessConstants.IMEI_CHANGE_NUM + mobilePhone);
        String currentDate = DateFormatUtils.format(System.currentTimeMillis(), DateType.DATE_TYPE17);
        String dateStr = StringUtils.EMPTY;
        int changeTimes = 0;
        if (StringUtils.isNotBlank(changeNum)){
            String[] array = changeNum.split("_");
            dateStr = array[0];
            if(currentDate.equals(dateStr)) {
            	changeTimes = Integer.valueOf(array[1]);
            }
        }
        if (StringUtils.isNotEmpty(changeNum)) {
            if (changeTimes >= BIND_IMEI_NUM) {
                if (StringUtils.isNotEmpty(bindImei)) {
                	logger.debug("绑定次数：" + mobilePhone + "  " + changeNum);
                    throw new CheckLoginException(StatusCode.CHANGE_BIND_IMEI_REACHED_UPPER_LIMIT.getCode(), "请使用上一次登录的设备或以游客身份登录&本月更换登录设备次数已达上限");
                } else {
                	logger.debug("绑定次数：" + mobilePhone + "  " + changeNum);
                    throw new CheckLoginException(StatusCode.CHANGE_BIND_IMEI_REACHED_UPPER_LIMIT.getCode(), "仅能以游客身份登录&本月更换登录设备次数已达上限");
                }
            }
        }
        /**
         * 7、如果需要重新绑定设备
         * 密码登录且没有验证码  或者 验证码登录且为需要提示.
         */
        if ((StringUtils.isBlank(verifyCode) && loginType == 0) || (loginType == 1 && checkType ==  0)){
            logger.debug(mobilePhone + "本月可绑定" + BIND_IMEI_NUM + "  " + changeTimes + "次");
            throw new CheckLoginException(StatusCode.CHANGE_BIND_IMEI_PROMPT.getCode(), String.format(StatusCode.CHANGE_BIND_IMEI_PROMPT.getMsg(), String.valueOf(BIND_IMEI_NUM-changeTimes)));
        }

        /**
         * 8、密码登录需要在验证绑定次数之后再校验验证码
         */
        if(loginType == 0) {
        	if(!this.checkVerifyCode(mobilePhone, verifyCode, true)) {
            	logger.debug(mobilePhone + "验证码错误");
                throw new CheckLoginException(StatusCode.VALIDATE_CODE_ERROR);
            }
        }
        
        /**
         * 更新用户绑定的imei.
         */
        /**
         * 9、设备更换，发送推送
         */
        if (StringUtils.isNotEmpty(bindImei) && !bindImei.equals(imei)){
            messageServ.syncPush(authId, 0, 174, 1, null, "login");
            MembershipBasicInfo membershipInfo = memberShipService.getUserBasicInfo(authId, (short)0);
            messageServ.asyncSendSMSTemplate(membershipInfo.getMobilePhone(),
                    174, null, "login");
        }

        /**
         * 10、判断新的imei是否已经绑定过设备.
         *  如果已经绑定过设备，直接把设备解绑.
         */
        MmpUserTag oldUserTag = mmpUserTagMapper.selectByImei(imei);
        if(oldUserTag != null) {
        	MmpUserTag tag = new MmpUserTag();
        	tag.setSpare5(StringUtils.EMPTY);
        	tag.setUpdateOperName("system");
        	tag.setUpdateTime(new Date());
        	tag.setId(oldUserTag.getId());
        	mmpUserTagMapper.updateByPrimaryKey(tag);
        	logger.error(mobilePhone + "登录设备检测-删除设备之前绑定账号" + oldUserTag.getAuthId());
            insertOptLog(authId,"0","清除设备原有绑定账号" + mobilePhone,mobilePhone);
        }
        /**
         * 11、绑定imei.<br>
         */
        if(userTag != null) {
        	MmpUserTag tag = new MmpUserTag();
        	tag.setId(userTag.getId());
        	tag.setSpare5(imei);
        	tag.setUpdateOperName("system");
        	tag.setUpdateTime(new Date());
        	mmpUserTagMapper.updateByPrimaryKey(tag);
        	logger.debug(mobilePhone + "登录设备检测-更新绑定记录");
        } else {
        	MmpUserTag tag = new MmpUserTag();
        	tag.setAuthId(authId);
        	tag.setSpare5(imei);
        	tag.setUpdateOperName("system");
        	Date now = new Date();
        	tag.setUpdateTime(now);
        	tag.setCreateOperName("system");
        	tag.setCreateTime(now);
        	mmpUserTagMapper.insertSelective(tag);
        	logger.warn(authId + "该会员没有userTag");
        }
        String optContent = "登录设备绑定更新|" + "账号:" + mobilePhone + "，" + "imei设备号：" + imei;
        //插入日志
        insertOptLog(authId,"0",optContent, mobilePhone);
        //记录会员操作日志
        UserOperationLogInput userOptRecord = new UserOperationLogInput();
        userOptRecord.setAuthId(authId);
        userOptRecord.setOperationType(MemOperateTypeEnum.BIND_IMEI.getCode());
        userOptRecord.setRefKey1(mobilePhone);
        userOptRecord.setRefKey2(imei);
        userOptRecord.setOperationContent(optContent);
        userOptRecord.setOperator("APP");
        saveMemberOperateLog(userOptRecord);

        /**
         * 11.1 修改缓存中用户绑定次数.<br>
         */
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.MONTH, 1);
        cal.set(Calendar.DAY_OF_MONTH,1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        int time = new Long((cal.getTime().getTime() - System.currentTimeMillis()) /1000).intValue();
        logger.debug(BussinessConstants.IMEI_CHANGE_NUM + mobilePhone + "  " + String.valueOf(changeTimes + 1) + "  " + time);
        JedisUtil.setCover(BussinessConstants.IMEI_CHANGE_NUM + mobilePhone, currentDate + "_" + String.valueOf(changeTimes + 1), time);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindImei(String imei, String mobilePhone, String authId) {
        this.bindImei(imei, mobilePhone, authId, false);
    }

    /**
     * 更改imei
     * @param imei
     * @param mobilePhone
     * @param authId
     * @param type 2：修改imei 1：校验imei
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeImei(String imei, String mobilePhone, String authId,Integer type) {

        if(StringUtils.equals(mobilePhone, "15587654321")) {
            return;
        }

        /**
         * 2、查询会员imei
         */
        MmpUserTag userTag = mmpUserTagMapper.selectMmpUserByAuthId(authId);

        if(type.equals(2)){
            /**
             * 1、判断新的imei是否已经绑定过设备.
             *  如果已经绑定过设备，直接把设备解绑.
             */
            MmpUserTag oldUserTag = mmpUserTagMapper.selectByImei(imei);
            if(oldUserTag != null) {
                MmpUserTag tag = new MmpUserTag();
                tag.setSpare5(StringUtils.EMPTY);
                tag.setUpdateOperName("system");
                tag.setUpdateTime(new Date());
                tag.setId(oldUserTag.getId());
                mmpUserTagMapper.updateByPrimaryKey(tag);
                logger.error(mobilePhone + "登录设备检测-删除设备之前绑定账号" + oldUserTag.getAuthId());
                insertOptLog(authId,"0","清除设备原有绑定账号" + mobilePhone, mobilePhone);
                //记录新表操作日志
                UserOperationLogInput userOptRecord = new UserOperationLogInput();
                userOptRecord.setAuthId(authId);
                userOptRecord.setRefKey1(mobilePhone);
                userOptRecord.setOperationType(MemOperateTypeEnum.CLEAN_BIND_IMEI.getCode());
                userOptRecord.setOperationContent(MemOperateTypeEnum.CLEAN_BIND_IMEI.getOperate());
                userOptRecord.setOperator("APP");
                saveMemberOperateLog(userOptRecord);
            }

            /**
             * 11、绑定imei.<br>
             */
            if(userTag != null) {
                MmpUserTag tag = new MmpUserTag();
                tag.setId(userTag.getId());
                tag.setSpare5(imei);
                tag.setUpdateOperName("system");
                tag.setUpdateTime(new Date());
                mmpUserTagMapper.updateByPrimaryKey(tag);
                logger.debug(mobilePhone + "登录设备检测-更新绑定记录");
            } else {
                MmpUserTag tag = new MmpUserTag();
                tag.setAuthId(authId);
                tag.setSpare5(imei);
                tag.setUpdateOperName("system");
                Date now = new Date();
                tag.setUpdateTime(now);
                tag.setCreateOperName("system");
                tag.setCreateTime(now);
                mmpUserTagMapper.insertSelective(tag);
                logger.warn(authId + "该会员没有userTag");
            }
            String optContent = "登录设备绑定更新|" + "账号:" + mobilePhone + "，" + "imei设备号：" + imei;
            //插入日志
            insertOptLog(authId,"0",optContent, mobilePhone);
            //记录会员操作日志
            UserOperationLogInput userOptRecord = new UserOperationLogInput();
            userOptRecord.setAuthId(authId);
            userOptRecord.setOperationType(MemOperateTypeEnum.BIND_IMEI.getCode());
            userOptRecord.setRefKey1(mobilePhone);
            userOptRecord.setRefKey2(imei);
            userOptRecord.setOperationContent(optContent);
            userOptRecord.setOperator("APP");
            saveMemberOperateLog(userOptRecord);
            return;
        }

        String bindImei = StringUtils.EMPTY;
        if(userTag != null) {
            bindImei = userTag.getSpare5();
        }

        /**
         * 3、密码登录，先验证imei是否一致，如果一致return 0;
         */
        if(StringUtils.isNotEmpty(bindImei) && bindImei.equals(imei)){
            logger.debug(mobilePhone + "登录设备检测-绑定设备与登录设备相同,imei=" + bindImei);
            return ; // 正常
        }
        if ("0".equals(imei)){
            //注册时自动登录
            return;
        }

        //不一致
        throw new CheckLoginException(4,"登陆设备更换");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean bindImei(String imei, String mobilePhone, String authId, boolean subtractTimes) {
    	if(subtractTimes) {
    		String changeNum = JedisUtil.get(BussinessConstants.IMEI_CHANGE_NUM + mobilePhone);
            String currentDate = DateFormatUtils.format(System.currentTimeMillis(), DateType.DATE_TYPE17);
            String dateStr = StringUtils.EMPTY;
            int changeTimes = 0;
            if (StringUtils.isNotBlank(changeNum)){
                String[] array = changeNum.split("_");
                dateStr = array[0];
                if(currentDate.equals(dateStr)) {
                	changeTimes = Integer.valueOf(array[1]);
                }
            }
            if (StringUtils.isNotEmpty(changeNum) && changeTimes >= BIND_IMEI_NUM) {
            	return false;
            }
    	}
    	/**
         * 1、判断新的imei是否已经绑定过设备.
         *  如果已经绑定过设备，直接把设备解绑.
         */
        MmpUserTag oldUserTag = mmpUserTagMapper.selectByImei(imei);
        if(oldUserTag != null) {
        	MmpUserTag tag = new MmpUserTag();
        	tag.setSpare5(StringUtils.EMPTY);
        	tag.setUpdateOperName("system");
        	tag.setUpdateTime(new Date());
        	tag.setId(oldUserTag.getId());
        	mmpUserTagMapper.updateByPrimaryKey(tag);
        	logger.error(mobilePhone + "登录设备检测-删除设备之前绑定账号" + oldUserTag.getAuthId());
            insertOptLog(authId,"0","清除设备原有绑定账号" + mobilePhone, mobilePhone);
            //记录新表操作日志
            UserOperationLogInput userOptRecord = new UserOperationLogInput();
            userOptRecord.setAuthId(authId);
            userOptRecord.setRefKey1(mobilePhone);
            userOptRecord.setOperationType(MemOperateTypeEnum.CLEAN_BIND_IMEI.getCode());
            userOptRecord.setOperationContent(MemOperateTypeEnum.CLEAN_BIND_IMEI.getOperate());
            userOptRecord.setOperator("APP");
            saveMemberOperateLog(userOptRecord);
        }
        /**
         * 2、绑定imei.<br>
         */
        MmpUserTag userTag = mmpUserTagMapper.selectMmpUserByAuthId(authId);
        if(userTag != null) {
        	MmpUserTag tag = new MmpUserTag();
        	tag.setId(userTag.getId());
        	tag.setSpare5(imei);
        	tag.setUpdateOperName("system");
        	tag.setUpdateTime(new Date());
        	mmpUserTagMapper.updateByPrimaryKey(tag);
        	logger.debug(mobilePhone + "登录设备检测-更新绑定记录");
        } else {
        	MmpUserTag tag = new MmpUserTag();
        	tag.setAuthId(authId);
        	tag.setSpare5(imei);
        	tag.setUpdateOperName("system");
        	Date now = new Date();
        	tag.setUpdateTime(now);
        	tag.setCreateOperName("system");
        	tag.setCreateTime(now);
        	mmpUserTagMapper.insertSelective(tag);
        	logger.warn(authId + "该会员没有userTag");
        }

        String optContent = "登录设备绑定更新|" + "账号:" + mobilePhone + "，" + "imei设备号：" + imei;
        //插入日志
        insertOptLog(authId,"0", optContent, mobilePhone);
        //记录会员操作日志
        UserOperationLogInput userOptRecord = new UserOperationLogInput();
        userOptRecord.setAuthId(authId);
        userOptRecord.setOperationType(MemOperateTypeEnum.BIND_IMEI.getCode());
        userOptRecord.setRefKey1(mobilePhone);
        userOptRecord.setRefKey2(imei);
        userOptRecord.setOperationContent(optContent);
        userOptRecord.setOperator("app");
        saveMemberOperateLog(userOptRecord);

        /**
         * 3、 修改缓存中用户绑定次数.<br>
         */
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.MONTH, 1);
        cal.set(Calendar.DAY_OF_MONTH,1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        int time = new Long((cal.getTime().getTime() - System.currentTimeMillis()) /1000).intValue();
        //计算当前缓存中的次数.
        String changeNum = JedisUtil.get(BussinessConstants.IMEI_CHANGE_NUM + mobilePhone);
        String currentDate = DateFormatUtils.format(System.currentTimeMillis(), DateType.DATE_TYPE17);
        String dateStr = StringUtils.EMPTY;
        int changeTimes = 0;
        if (StringUtils.isNotBlank(changeNum)){
            String[] array = changeNum.split("_");
            dateStr = array[0];
            if(currentDate.equals(dateStr)) {
            	changeTimes = Integer.valueOf(array[1]);
            }
        }
        logger.debug(BussinessConstants.IMEI_CHANGE_NUM + mobilePhone + "  " + String.valueOf(changeTimes + 1) + "  " + time);
        return true;
    }
    

    /**
     * 检查服务条款
     * @param loginBeanResult
     * @param serviceVer
     */
    private void checkServiceVer(LoginBeanResult loginBeanResult,String serviceVer){
        if (StringUtils.isNotEmpty(serviceVer) && serviceVer.contains(",")) {
            // 逗号分隔的版本号
            String userTypeOneVersion = serviceVer.split(",")[0];
            String userTypeTwoVersion = serviceVer.split(",")[1];
            String typeOneVersion = JedisUtil.hget(BussinessConstants.TYPE_VERSION_MAP, "1");
            String typeTwoVersion = JedisUtil.hget(BussinessConstants.TYPE_VERSION_MAP, "2");
            if (StringUtils.isNotEmpty(userTypeOneVersion)
                    && userTypeOneVersion.equals(typeOneVersion)) {
                loginBeanResult.setServiceExp(0);
            } else {
                loginBeanResult.setServiceExp(1);
            }
            if (StringUtils.isNotEmpty(userTypeTwoVersion)
                    && userTypeTwoVersion.equals(typeTwoVersion)) {
                loginBeanResult.setPrivacyPolicyExp(0);
            } else {
                loginBeanResult.setPrivacyPolicyExp(1);
            }
        } else {
            loginBeanResult.setServiceExp(1);
            loginBeanResult.setPrivacyPolicyExp(1);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    private void insertOptLog(String authId,String key2,String content,String mobilePhone){
        UserOperatorLog userOperatorLog = new UserOperatorLog();
        userOperatorLog.setForeignKey(authId);
        userOperatorLog.setForeignKey2(key2);
        userOperatorLog.setOperatorContent(content);
        userOperatorLog.setCreatedUser(mobilePhone);
        userOperatorLog.setCreatedTime(DateFormatUtils.format(new Date(), DateType.DATE_TYPE3));
        userOperatorLogMapper.saveSelective(userOperatorLog);
    }

    @Transactional(rollbackFor = Exception.class)
    private void saveMemberOperateLog(UserOperationLogInput userOperationLogInput){
        Long userId = userOperationLogInput.getUserId();
        //获取会员主键
        if(null == userOperationLogInput.getUserId()
                && StringUtils.isNotBlank(userOperationLogInput.getAuthId())) {
            String authId = userOperationLogInput.getAuthId();
            MembershipInfoWithBLOBs membershipInfo = membershipInfoMapper.selectByAuthId(authId,
                    userOperationLogInput.getMembershipType());
            if(membershipInfo != null){
                userId = membershipInfo.getPkId();
            }
        }
        if(null == userOperationLogInput.getOperationTime()) {
            userOperationLogInput.setOperationTime(new Date());
        }
        if(null != userId) {
            MmpUserOperationLog mmpUserOperationLog = new MmpUserOperationLog();
            BeanUtils.copyProperties(userOperationLogInput,mmpUserOperationLog);
            mmpUserOperationLog.setUserId(userId);
            mmpUserOperationLog.setCreateOperId(userOperationLogInput.getOperatorId());
            mmpUserOperationLog.setCreateTime(userOperationLogInput.getOperationTime());
            mmpUserOperationLog.setUpdateTime(userOperationLogInput.getOperationTime());
            mmpUserOperationLog.setUpdateOperId(userOperationLogInput.getOperatorId());
            mmpUserOperationLog.setCreateOperName(userOperationLogInput.getOperator());
            mmpUserOperationLog.setUpdateOperName(userOperationLogInput.getOperator());
            mmpUserOperationLogMapper.insertSelective(mmpUserOperationLog);
        }
    }

	@Override
	public void bindChannel(String channelId, String authId, int membershipType) {
		String key = authId + "_" + membershipType;
        if (StringUtils.isNotEmpty(channelId)) {
            String channelIdOld = JedisUtil.hget(BussinessConstants.MEMBER_CHANNEL_ID_KEY, key);
            if(StringUtils.equals(channelIdOld, channelId)) {
            	return;
            }
            String authId2 = JedisUtil.hget(BussinessConstants.CHANNEL_ID_MEMBER_KEY, channelId);
            if (StringUtils.isNotEmpty(authId2) && !authId2.equals(authId)) {
                membershipInfoMapper.updateChannelId(authId2, StringUtils.EMPTY,(short) membershipType);
                JedisUtil.hdel(BussinessConstants.MEMBER_CHANNEL_ID_KEY, authId2 + "_" + membershipType);
                if (StringUtils.isNotEmpty(channelIdOld)) {
                    JedisUtil.hdel(BussinessConstants.CHANNEL_ID_MEMBER_KEY, channelIdOld);
                }
            }
            JedisUtil.hset(BussinessConstants.MEMBER_CHANNEL_ID_KEY, key, channelId);
            JedisUtil.hset(BussinessConstants.CHANNEL_ID_MEMBER_KEY, channelId, authId);
            membershipInfoMapper.updateChannelId(authId, channelId, (short)membershipType);
        } else {
            JedisUtil.hdel(BussinessConstants.MEMBER_CHANNEL_ID_KEY, key);
            membershipInfoMapper.updateChannelId(authId, StringUtils.EMPTY, (short)membershipType);
        }
	}

	@Override
	public Integer getBindImeiTimes(String authId, String mobilePhone) {
		if(StringUtils.isBlank(authId) && StringUtils.isBlank(mobilePhone)) {
			throw BusinessRuntimeException.PARAM_EXEPTION;
		}
		if(StringUtils.isBlank(mobilePhone)) {
			MembershipBasicInfo member = memberShipService.getUserBasicInfo(authId, (short)0);
			if(member == null) {
				throw BusinessRuntimeException.PARAM_EXEPTION;
			}
			mobilePhone = member.getMobilePhone();
		}
		String changeNum = JedisUtil.get(BussinessConstants.IMEI_CHANGE_NUM + mobilePhone);
        String currentDate = DateFormatUtils.format(System.currentTimeMillis(), DateType.DATE_TYPE17);
        String dateStr = StringUtils.EMPTY;
        int changeTimes = 0;
        if (StringUtils.isNotBlank(changeNum)){
            String[] array = changeNum.split("_");
            dateStr = array[0];
            if(currentDate.equals(dateStr)) {
            	changeTimes = Integer.valueOf(array[1]);
            }
        }
		return changeTimes;
	}

    @Override
    public void changeUserImage(String authId, String orgId, String imageUrl, UpdateUserDto updateUserDto) throws CheckLoginException {
        /**
         * 1.查询用户信息
         */
        MembershipInfo membershipInfo = membershipInfoMapper.selectMembershipInfoByAuthId(authId, orgId);
        if (membershipInfo == null) {
            throw new CheckLoginException(StatusCode.USER_INFO_NO_EXIST);
        }
        /**
         * 2、修改会员表.<br>
         */
        int result = membershipInfoMapper.updateUserImage(imageUrl, authId, orgId, updateUserDto.getUserName(),
                    ComUtil.getFormatDate(updateUserDto.getUpdateTime(), ComUtil.DATE_TYPE3));
        if(result <= 0) {
            logger.error("修改会员头像失败, authId=" + authId + ",orgId=" + orgId);
            throw new CheckLoginException(StatusCode.SYSTEM_ERROR);
        }
        /**
         * 3、添加操作日志.<br>
         */
        try {
            String content = "修改会员头像";
            UserOperatorLog userOperatorLog = new UserOperatorLog();
            userOperatorLog.setOperatorContent(content);
            userOperatorLog.setForeignKey(authId);
            if (updateUserDto.getUserId() != null) {
                userOperatorLog.setForeignKey2(updateUserDto.getUserId().toString());
            } else {
                userOperatorLog.setForeignKey2(authId);
            }
            userOperatorLog.setCreatedUser(updateUserDto.getUserName());
            userOperatorLog.setCreatedTime(ComUtil.getFormatDate(updateUserDto.getUpdateTime(), ComUtil.DATE_TYPE3));
            userOperatorLogMapper.saveSelective(userOperatorLog);

            UserOperationLogInput userOptRecord = new UserOperationLogInput();
            userOptRecord.setRefKey1(authId);
            userOptRecord.setAuthId(authId);
            userOptRecord.setOperationType(MemOperateTypeEnum.UPDATE_EMAL.getCode());
            userOptRecord.setOperatorId(updateUserDto.getUserId());
            userOptRecord.setOperationContent(content);
            userOptRecord.setOperator(updateUserDto.getUserName());
            memberShipService.saveUserOperationLog(userOptRecord);
        } catch (Exception e) {
            logger.error("记录修改会员邮箱操作日志失败，authId=" + authId, e);
         }
    }

}
