package com.extracme.evcard.membership.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.extracme.evcard.elasticjob.ElasticSimpleJob;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.dto.AccountStatusDto;
import com.extracme.evcard.membership.core.service.auth.MembershipLifeCycle;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.util.DateType;
import com.extracme.evcard.saic.service.ISaicMemberService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.List;

/**
 * 会员注销定时任务
 * 每天0点
 */
@Slf4j
@Component
@ElasticSimpleJob(jobName = "evcard-membership-autoCompleteUnregister",
		cron = "0 0 0 * * ?", description = "会员账号注销定时任务", overwrite = true)
public class MemberRegisterJob implements SimpleJob{

	@Autowired
	private MembershipInfoMapper membershipInfoMapper;

	@Resource
	private ISaicMemberService saicMemberService;

	@Resource
	private MembershipLifeCycle membershipLifeCycle;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void execute(ShardingContext arg0) {
		try {
			log.info("注销定时任务|开始扫码当日冻结期已满的会员");
			unregister();
		} catch (BusinessException e) {
			log.error("注销定时任务|注销当日冻结期已满的会员失败", e);
		}
	}

	@Transactional(rollbackFor = Exception.class)
	public void unregister() throws BusinessException {
		/**
		 *  定时扫描会员状态，删除注销冻结期满的外部会员。
		 */
		String currentDate = ComUtil.getSystemDate(DateType.DATE_TYPE8);
		String registerDate = StringUtils.EMPTY;
		log.info("注销定时任务|冻结期满的会员状态更新");
		Calendar calendar = Calendar.getInstance(ComUtil.timeZoneChina);
		calendar.add(Calendar.DATE, -181);
		registerDate = ComUtil.getFormatDate(calendar, DateType.DATE_TYPE8);
		String updateTime = ComUtil.getSystemDate(ComUtil.DATE_TYPE3);
		List<AccountStatusDto> members = membershipInfoMapper.selectAutoCompleteUnregister(registerDate);
		if(CollectionUtils.isEmpty(members)) {
			log.warn("注销定时任务|昨日无会员由冻结期转为注销完成, unregisterTime<=" + registerDate);
			return;
		}
		//1.变更会员状态
		long st = System.currentTimeMillis();
		log.info("会员注销|冻结期满的会员状态更新开始");
		try {
			int cnt = membershipInfoMapper.autoCompleteUnregister(registerDate, updateTime, "auto");
			log.info("会员注销|冻结期满的会员状态更新，更新记录数{}, cost={}ms", cnt, System.currentTimeMillis() - st);
		} catch (Exception e) {
			st = System.currentTimeMillis() -st;
			log.error("注销定时任务|冻结期满的会员状态更新失败, unregisterTime<=" + registerDate + ", cost=" + String.valueOf(st), e);
			throw e;
		}

		//2. 若开启注销对接，则通知享道会员账号注销
		if (saicMemberService.beginSaic()) {
			log.info("享道：同步会员注销记录，注销定时任务触发");
			for(AccountStatusDto member : members){
				saicMemberService.cancelAccount(member.getAuthId(), null, member.getMobilePhone(), member.getMobilePhone());
			}
		}

		// 3. 推送注销事件
		for (AccountStatusDto member : members) {
			membershipLifeCycle.unRegisterMemberMqPush(member.getMid());
		}

	}

	public static void main(String[] args) {
		MemberRegisterJob job = new MemberRegisterJob();
		job.execute(null);
	}
}
