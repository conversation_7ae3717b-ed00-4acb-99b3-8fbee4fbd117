package com.extracme.evcard.membership.core.service.auth.idcard;

import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.canstant.OcrChannelConstants;
import com.extracme.evcard.membership.core.dao.MemberIdentityDocumentMapper;
import com.extracme.evcard.membership.core.dto.AuditIdCardDTO;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.dto.input.AuditIdCardInput;
import com.extracme.evcard.membership.core.enums.IdTypeEnum;
import com.extracme.evcard.membership.core.enums.MemOperateTypeEnum;
import com.extracme.evcard.membership.core.exception.AuthenticationException;
import com.extracme.evcard.membership.core.input.UserOperationLogInput;
import com.extracme.evcard.membership.core.model.ApplyProgress;
import com.extracme.evcard.membership.core.model.MemberIdentityDocument;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs;
import com.extracme.evcard.membership.invitation.service.IMemberShipInvitationServ;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.enums.StatusCode;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@Service
public class IdCardAuthPassService extends IdCardAuthAbstract {

    @Resource
    MembershipInfoMapper membershipInfoMapper;

    @Resource
    MemberIdentityDocumentMapper memberIdentityDocumentMapper;

    @Resource
    IMemberShipService memberShipService;

    @Resource
    IMemberShipInvitationServ memberShipInvitationServ;




    @Override
    protected void messagePush(AuditIdCardDTO auditIdCardDTO) {
        //1操作日志记录
        UserOperationLogInput logRecord = getLogRecord(auditIdCardDTO, "身份证人工审核通过", MemOperateTypeEnum.IDCARD_MANUAL_AUDIT_PASS);
        memberShipService.saveUserOperationLog(logRecord);

        //埋点
        identityCertService.memberAuditTrack(true,auditIdCardDTO.getMembershipBasicInfo().getAuthId(),0,"成功","","");
        //2 mq
        pushOldMemberMq(auditIdCardDTO, 1);
        pushNewMemberMq(auditIdCardDTO, 0, 1, 4);


        //3.新用户推送新手礼包&邀请好友相关事件
        if (auditIdCardDTO.getNewUser()) {
          /*
           // 1.1 推送新用户审核通过事件，用于礼包发送
            String mobilePhone = auditIdCardDTO.getMembershipBasicInfo().getMobilePhone();
            String authId = auditIdCardDTO.getMembershipBasicInfo().getAuthId();
            String operatorUserName = auditIdCardDTO.getAuditIdCardInput().getOperatorUserName();
            //都是旧的活动
            BaseResponse baseResponse = memberShipInvitationServ.giveAnewGiftBag(authId, mobilePhone, null, operatorUserName);
            if (null != baseResponse && 0 == baseResponse.getCode()) {
                log.debug("新手礼包优惠券发放成功 ，authId:" + authId + " , mobilePhone :" + mobilePhone);
            } else {
                log.warn("新手礼包优惠券发放失败 ，authId:" + authId + " , mobilePhone :" + mobilePhone);
            }*/
            //1.2 推送好友邀请成功事件
            pushMemberInvitation(auditIdCardDTO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    protected Pair<Integer, String> updateDbRecord(AuditIdCardDTO auditIdCardDTO) {
        MembershipBasicInfo membershipBasicInfo = auditIdCardDTO.getMembershipBasicInfo();
        AuditIdCardInput auditIdCardInput = auditIdCardDTO.getAuditIdCardInput();
        String authId = membershipBasicInfo.getAuthId();

        //membershipInfoMapper 更新
        MembershipInfoWithBLOBs membershipInfo = getUpdatedMembershipInfo(auditIdCardDTO, 1);
        //初次身份认证通过时间
        if (membershipBasicInfo.getIdentityFirstAuthTime() == null) {
            membershipInfo.setIdentityFirstAuthTime(auditIdCardDTO.getOperaterDate());
        }
        //membershipInfoMapper.updateByPrimaryKey(membershipInfo);
        membershipInfoMapper.updateByPrimaryKeySelective(membershipInfo);

        //memberIdentityDocumentMapper 更新
        MemberIdentityDocument updateMemberIdentityDocument = getUpdatedMemberIdentityDocument(auditIdCardDTO, 4);
        memberIdentityDocumentMapper.updateByPrimaryKeySelective(updateMemberIdentityDocument);

        //用户 制卡
        if (auditIdCardDTO.getNewUser()) {
            try {
                memberShipService.setVirtualCard(authId,membershipBasicInfo.getMembershipType());
            } catch (AuthenticationException e) {
                log.error("身份证审核通过时，用户制卡异常<{}>", e);
                return new Pair<>(e.getCode(), e.getMessage());
            }
        } else {
            // 添加申请进度
            ApplyProgress applyProgress = new ApplyProgress();
            applyProgress.setAuthId(authId);
            applyProgress.setProgressContent("审核通过");
            applyProgress.setCreatedUser(auditIdCardInput.getOperatorUserName());
            applyProgress.setProgressTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE1));
            applyProgressMapper.addApplyProgress(applyProgress);
        }
        return new Pair<>(0, OcrChannelConstants.IDCARD_AUDIT_OK_MESSAGE);
    }


    @Override
    protected Pair<Integer, String> serviceCheck(AuditIdCardDTO auditIdCardDTO) {
        AuditIdCardInput idCardInput = auditIdCardDTO.getAuditIdCardInput();

        String reviewItems = idCardInput.getReviewItems();
        if (!BussinessConstants.IDCARD_REVIEW_ITEMS_OK.equals(reviewItems)) {
            return new Pair<>(StatusCode.ILLEGAL_PARAM.getCode(), "ReviewItems" + StatusCode.ILLEGAL_PARAM.getMsg());
        }


        MemberIdentityDocument memberIdentityDocument = auditIdCardDTO.getMemberIdentityDocument();
        Integer identityType = memberIdentityDocument.getIdentityType();
        // 本国籍
        if (IdTypeEnum.isMainlandId(identityType)) {
            if (StringUtils.isBlank(memberIdentityDocument.getIdentityCardImgUrl())) {
                return new Pair<>(-1, "审核失败，请检查用户的身份证正面照片");
            }
            if (StringUtils.isBlank(memberIdentityDocument.getReverseIdentityCardImgUrl())) {
                return new Pair<>(-1, "审核失败，请检查用户的身份证背面照片");
            }
        } else {
            // 外国籍校验规则
            if (StringUtils.isBlank(memberIdentityDocument.getIdentityCardImgUrl())) {
                return new Pair<>(-1, "审核失败，请检查用户的护照或通行证照片");
            }
            if (StringUtils.isBlank(memberIdentityDocument.getHoldIdcardPicUrl())) {
                return new Pair<>(-1, "审核失败，请检查用户的手持证件照片");
            }
            if (StringUtils.isBlank(memberIdentityDocument.getFaceRecognitionImgUrl())) {
                return new Pair<>(-1, "审核失败，请检查用户的人脸照片");
            }
        }

        auditIdCardDTO.setOperaterDate(new Date());
        return new Pair<>(0, OcrChannelConstants.IDCARD_AUDIT_OK_MESSAGE);
    }

}
