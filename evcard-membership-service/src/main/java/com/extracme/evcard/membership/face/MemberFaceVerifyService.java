package com.extracme.evcard.membership.face;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.core.dto.*;
import com.extracme.evcard.membership.core.dto.input.FaceMatchInput;
import com.extracme.evcard.membership.core.dto.input.FacePersonVerifyInput;
import com.extracme.evcard.membership.core.exception.MemberException;
import com.extracme.evcard.membership.core.service.IMemberFaceVerifyService;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.membership.third.baidu.*;
import com.extracme.evcard.membership.third.baidu.facelive.FaceLiveVerifyRequest;
import com.extracme.evcard.membership.third.baidu.facelive.FaceLiveVerifyResp;
import com.extracme.evcard.membership.third.baidu.facelive.SessionCodeRequest;
import com.extracme.evcard.membership.third.baidu.facelive.SessionCodeResult;
import com.extracme.evcard.rpc.entity.HidLog;
import com.extracme.evcard.rpc.entity.LogPoint;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.logging.Level;

/**
 * <AUTHOR>
 * @Discription
 * @date 2021/3/17
 */
@Slf4j
@Service
public class MemberFaceVerifyService implements IMemberFaceVerifyService {

    public static final String PASS = "pass";
    public static final double FACE_LIVE_PASS_THRESHOLD = 0.3;
    @Resource
    private BaiduFaceVerifyClient baiduFaceVerifyClient;

    @Resource
    private BaiduFaceVerifySecClient baiduFaceVerifySecClient;

    @Resource
    private BaiduFaceMatchClient faceMatchClient;
    @Resource
    private BaiduFaceMatchEnhancerClient baiduFaceMatchEnhancerClient;

    @Resource
    private BaiduFaceLiveNessClient faceLiveNessClient;

    @Resource
    private IMemberShipService memberShipService;

    private static final String[] IMAGE_TYPES = {"BASE64", "URL", "FACE_TOKEN"};


    private static final Integer SCENE_TYPE_APP = 1;
    private static final String APP = "APP";
    private static final String IOS = "ios";
    private static final String ANDROID = "android";

    private static final Integer SUCCESS = 0;


    @Override
    public FacePersonVerifyResult personVerify(FacePersonVerifyInput input) {
        input = checkParamAndFillUserInfo(input);

        PersonVerifyRequest request = new PersonVerifyRequest();
        request.setImage(input.getImage());
        request.setImage_type(transImageType(input.getImageType()));
        request.setId_card_number(input.getIdCardNo());
        request.setName(input.getName());
        request.setLiveness_control("NORMAL");
        request.setSpoofing_control("NORMAL");
        FacePersonVerifyResult resp = new FacePersonVerifyResult();
        BaseResult<PersonVerifyResult> result = baiduFaceVerifyClient.personVerify(request);
        if (result != null) {
            PersonVerifyResult verifyResult = result.getResult();
            resp.setCode(result.getError_code());
            resp.setMessage(result.getError_msg());
            if (verifyResult != null && verifyResult.getScore() != null) {
                resp.setScore(result.getResult().getScore());
                resp.setCode(0);
            }
        } else {
            resp.setCode(-1);
            resp.setMessage("人脸实名认证失败");
        }
        return resp;
    }

    @Override
    public FacePersonVerifyResult personVerifySec(FacePersonVerifyInput input) {
        input = checkParamAndFillUserInfo(input);

        PersonVerifySecRequest request = new PersonVerifySecRequest();
        request.setImage(input.getImage());
        request.setImage_type(transImageType(input.getImageType()));
        request.setId_card_number(input.getIdCardNo());
        request.setName(input.getName());
        request.setApp(transAppType(input.getApp()));
        request.setLiveness_control("NORMAL");
        request.setSpoofing_control("NORMAL");

        FacePersonVerifyResult resp = new FacePersonVerifyResult();
        BaseResult<PersonVerifySecResult> result = baiduFaceVerifySecClient.personVerify(request);
        PersonVerifySecResult verifyResult;
        if (result != null && (verifyResult = result.getResult()) != null) {
            if (PersonVerifySecResult.SUCCESS.equals(verifyResult.getVerifyStatus()) && verifyResult.getScore() != null) {
                // 正常情况
                resp.setScore(verifyResult.getScore());
                resp.setCode(0);
                resp.setImage(verifyResult.getDecImage());
            } else if (verifyResult.getVerifyStatus() != null) {
                resp.setCode(-1);
                resp.setMessage(transVerifyStatus(verifyResult.getVerifyStatus()));
            } else {
                resp.setCode(result.getError_code());
                resp.setMessage(result.getError_msg());
            }
        } else {
            resp.setCode(-1);
            resp.setMessage("人脸实名认证失败");
        }
        return resp;
    }

    private String transVerifyStatus(Integer verifyStatus) {
        if (PersonVerifySecResult.NOT_MATCH.equals(verifyStatus)) {
            return "身份证号与姓名不匹配或该身份证号不存在";
        } else if (PersonVerifySecResult.IMAGE_NOT_EXIST.equals(verifyStatus)) {
            return "公安网图片不存在或质量过低";
        }
        return null;
    }

    @Override
    public FacePersonVerifyResult faceMatch(FaceMatchInput input) {

        List<FaceMatchRequest> faceMatchRequests = new ArrayList<>();
        FaceMatchRequest imageDTO = new FaceMatchRequest();
        imageDTO.setImage(input.getImage());
        imageDTO.setImageType(transImageType(input.getImageType()));
        imageDTO.setLivenessControl("NORMAL");

        FaceMatchRequest registerImageDTO = new FaceMatchRequest();
        registerImageDTO.setImage(input.getRegisterImage());
        registerImageDTO.setImageType(transImageType(input.getRegisterImageType()));

        faceMatchRequests.add(imageDTO);
        faceMatchRequests.add(registerImageDTO);

        FacePersonVerifyResult facePersonVerifyResult = new FacePersonVerifyResult();

        HidLog.membership(LogPoint.GET_USER_SIMILARITY_BAI_DU, "人脸对比(百度api)开始", input.getAuthId(), true);

        BaseResult<FaceMatchResult> result = null;
        try {
            result = faceMatchClient.faceMatch(faceMatchRequests);
            HidLog.membership(LogPoint.GET_USER_SIMILARITY_BAI_DU, "人脸对比(百度api)返回结果：" + JSON.toJSONString(result), input.getAuthId(), true);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            HidLog.membership(LogPoint.GET_USER_SIMILARITY_BAI_DU, "人脸对比(百度api)失败 " + e.getMessage(), input.getAuthId(), false);
        }
        if (result != null && SUCCESS.equals(result.getError_code())) {
            Optional.of(result)
                    .map(BaseResult::getResult)
                    .map(FaceMatchResult::getScore)
                    .ifPresent(facePersonVerifyResult::setScore);
        } else {
            facePersonVerifyResult.setCode(-1);
            facePersonVerifyResult.setMessage("人脸对比失败");
        }
        return facePersonVerifyResult;
    }


    @Override
    public FacePersonVerifyResult enhancerFaceMatch(FaceMatchInput input) {

        HidLog.membership(LogPoint.GET_USER_SIMILARITY_BAI_DU, "人脸对比(百度api)开始", input.getAuthId(), true);

        FaceMatchEnhancerRequest request = new FaceMatchEnhancerRequest();
        if (SCENE_TYPE_APP.equals(input.getSceneType())) {
            request.setApp(transAppType(input.getApp()));
        }
        request.setScene_type(transSceneType(input.getSceneType()));
        request.setImage(input.getImage());
        request.setImage_type(transImageType(input.getImageType()));
        request.setLiveness_control("NORMAL");

        request.setRegister_image(input.getRegisterImage());
        request.setRegister_image_type(transImageType(input.getRegisterImageType()));

        FacePersonVerifyResult resp = new FacePersonVerifyResult();
        BaseResult<FaceMatchEnhancerResult> result = null;
        try {
            result = baiduFaceMatchEnhancerClient.faceMatch(request);
            HidLog.membership(LogPoint.GET_USER_SIMILARITY_BAI_DU, "人脸对比(百度api)返回结果：" + JSON.toJSONString(result), input.getAuthId(), true);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            HidLog.membership(LogPoint.GET_USER_SIMILARITY_BAI_DU, "人脸对比(百度api)失败 " + e.getMessage(), input.getAuthId(), false);
        }
        if (result != null) {
            FaceMatchEnhancerResult verifyResult = result.getResult();
            if (verifyResult != null && verifyResult.getScore() != null) {
                resp.setScore(verifyResult.getScore());
                resp.setCode(0);
                resp.setImage(verifyResult.getDecImage());
            } else {
                resp.setCode(result.getError_code());
                resp.setMessage(result.getError_msg());
            }
        } else {
            resp.setCode(-1);
            resp.setMessage("人脸实名认证失败");
        }
        HidLog.membership(LogPoint.GET_USER_SIMILARITY_BAI_DU, "人脸对比(百度api)结束", input.getAuthId(), true);
        return resp;
    }


    @Override
    public FaceLiveSessionCode getFaceLiveSessionCode() {
        SessionCodeRequest request = new SessionCodeRequest();
        request.setType(1);
        request.setMinCodeLength(1);
        request.setMaxCodeLength(1);
        FaceLiveSessionCode faceLiveSessionCode = new FaceLiveSessionCode();
        BaseResult<SessionCodeResult> sessionCode = null;
        try {
            sessionCode = faceLiveNessClient.getSessionCode(request);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        if (sessionCode != null && SUCCESS.equals(sessionCode.getError_code())) {
            SessionCodeResult result = sessionCode.getResult();
            faceLiveSessionCode.setSessionId(result.getSessionId());
            faceLiveSessionCode.setVerifyCode(result.getCode());
        } else {
            faceLiveSessionCode.setCode(-1);
            faceLiveSessionCode.setMessage("获取随机校验码失败");
        }
        return faceLiveSessionCode;
    }


    @Override
    public FaceLiveVerifyResult faceLiveVerify(FaceLiveVerifyInput input) {
        FaceLiveVerifyRequest faceLiveVerifyRequest = new FaceLiveVerifyRequest();
        faceLiveVerifyRequest.setTypeIdentify("action");
        faceLiveVerifyRequest.setVideoBase64(input.getVideoBase64());
        faceLiveVerifyRequest.setSessionId(input.getSessionId());

        FaceLiveVerifyResult faceLiveVerifyResult = new FaceLiveVerifyResult();

        BaseResult<FaceLiveVerifyResp> response = null;
        try {
            response = faceLiveNessClient.verify(faceLiveVerifyRequest);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        if (response != null && SUCCESS.equals(response.getError_code()) && response.getResult() != null) {
            FaceLiveVerifyResp result = response.getResult();
            if (PASS.equals(result.getActionVerify()) && result.getScore() >= FACE_LIVE_PASS_THRESHOLD) {
                // 活体检测通过
                Optional.of(result)
                        .map(FaceLiveVerifyResp::getBestImage)
                        .map(FaceLiveVerifyResp.BestImageDTO::getPic)
                        .ifPresent(faceLiveVerifyResult::setBestImage);
            } else {
                faceLiveVerifyResult.setCode(-1);
                faceLiveVerifyResult.setMessage("视频活体检测校验不通过");
            }
        } else {
            faceLiveVerifyResult.setCode(-1);
            faceLiveVerifyResult.setMessage("视频活体检测失败");
        }
        return faceLiveVerifyResult;
    }

    private String transAppType(Integer appType) {
        String result = null;
        if (appType != null) {
            switch (appType) {
                case 1:
                    result = IOS;
                    break;
                case 2:
                    result = ANDROID;
                    break;
                default:
                    result = "";
                    break;
            }
        }
        return result;
    }

    private String transSceneType(Integer sceneType) {
        String result = null;
        if (sceneType != null) {
            if (sceneType == 1) {
                result = APP;
            } else {
                result = "";
            }
        }
        return result;
    }


    private String transImageType(Integer imageType) {
        return IMAGE_TYPES[imageType];
    }


    private FacePersonVerifyInput checkParamAndFillUserInfo(FacePersonVerifyInput input) {
        if (input.getImageType() < 0 || input.getImageType() > 3) {
            throw new MemberException(-1, "非法的图片信息");
        }
        if (StringUtils.isBlank(input.getIdCardNo()) || StringUtils.isBlank(input.getName())) {
            if (StringUtils.isBlank(input.getAuthId())) {
                throw new MemberException(-1, "会员ID不可为空");
            }
            //暂时注释，直接使用入参中的证件号和证件名称
//            MembershipBasicInfo member = memberShipService.getUserBasicInfo(input.getAuthId(), (short) 0);
//            String idNo = member.getDriverCode();
//            String name = member.getName();
//            if (StringUtils.isBlank(idNo) || StringUtils.isBlank(name)) {
//                throw new MemberException(-1, "会员证件号码/姓名不可为空");
//            }
            input.setIdCardNo(input.getIdCardNo());
            input.setName(input.getName());
        }
        return input;
    }
}
