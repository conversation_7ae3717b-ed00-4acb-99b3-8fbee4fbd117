<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.core.dao.AgencyInfoBlacklistMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.AgencyInfoBlacklist" >
    <id column="AGENCY_ID" property="agencyId" jdbcType="VARCHAR" />
    <result column="AGENCY_NAME" property="agencyName" jdbcType="VARCHAR" />
    <result column="ADD_TIME" property="addTime" jdbcType="VARCHAR" />
    <result column="org_id" property="orgId" jdbcType="VARCHAR" />
    <result column="org_name" property="orgName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    AGENCY_ID, AGENCY_NAME, ADD_TIME, org_id, org_name
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from ${siacSchema}.agency_info_blacklist
    where AGENCY_ID = #{agencyId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from ${siacSchema}.agency_info_blacklist
    where AGENCY_ID = #{agencyId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.membership.core.model.AgencyInfoBlacklist" >
    insert into ${siacSchema}.agency_info_blacklist (AGENCY_ID, AGENCY_NAME, ADD_TIME, 
      org_id, org_name)
    values (#{agencyId,jdbcType=VARCHAR}, #{agencyName,jdbcType=VARCHAR}, #{addTime,jdbcType=VARCHAR}, 
      #{orgId,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.core.model.AgencyInfoBlacklist" >
    insert into ${siacSchema}.agency_info_blacklist
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="agencyId != null" >
        AGENCY_ID,
      </if>
      <if test="agencyName != null" >
        AGENCY_NAME,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="orgId != null" >
        org_id,
      </if>
      <if test="orgName != null" >
        org_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="agencyId != null" >
        #{agencyId,jdbcType=VARCHAR},
      </if>
      <if test="agencyName != null" >
        #{agencyName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null" >
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null" >
        #{orgName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.core.model.AgencyInfoBlacklist" >
    update ${siacSchema}.agency_info_blacklist
    <set >
      <if test="agencyName != null" >
        AGENCY_NAME = #{agencyName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null" >
        org_id = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null" >
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
    </set>
    where AGENCY_ID = #{agencyId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.core.model.AgencyInfoBlacklist" >
    update ${siacSchema}.agency_info_blacklist
    set AGENCY_NAME = #{agencyName,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=VARCHAR},
      org_id = #{orgId,jdbcType=VARCHAR},
      org_name = #{orgName,jdbcType=VARCHAR}
    where AGENCY_ID = #{agencyId,jdbcType=VARCHAR}
  </update>

  <select id="queryAgencyInfoBlackByCondition" resultMap="BaseResultMap" parameterType="com.extracme.evcard.membership.core.input.QueryAgencyInfoBlackConditionInput">
    select
    <include refid="Base_Column_List"/>
    from ${siacSchema}.agency_info_blacklist
    <where>
      <if test="orgId != null ">
        <choose>
          <when test="isLikeQueryOrgId == 1">
            and ORG_ID like concat('%',#{orgId})
          </when>
          <when test="isLikeQueryOrgId == 2">
            and ORG_ID like concat(#{orgId},'%')
          </when>
          <when test="isLikeQueryOrgId == 3">
            and ORG_ID like concat('%',#{orgId},'%')
          </when>
          <otherwise>
            and ORG_ID = #{orgId}
          </otherwise>
        </choose>
      </if>
    </where>
  </select>
</mapper>