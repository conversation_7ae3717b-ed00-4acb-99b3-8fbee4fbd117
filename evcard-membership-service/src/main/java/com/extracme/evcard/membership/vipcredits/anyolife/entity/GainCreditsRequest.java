package com.extracme.evcard.membership.vipcredits.anyolife.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/8/11
 */
@Data
@NoArgsConstructor
public class GainCreditsRequest extends BaseCreditsRequest{
    /**
     * 事件类型: 0固定积分 1完成认证 2补全资料 3...
     */
    private Integer feeType;

    /**
     * 数额：
     * 固定发放时，为发放数量
     * 订单支付时，为订单实付金额
     * 押金充值时，为充值金额
     * e币充值时，为e币充值金额
     * 取环车评价积分时，为档次，取值为1、2
     */
    private BigDecimal gainCredits;

    /**
     * 发放说明: json string
     */
    private String feeDetail;

    /**
     * 事件推送流水号
     */
    private String orderNo;

    /**
     * 订单详情: json string
     */
    private String orderDetail;

    /**
     * add, 订单分享订单号
     */
    private String shareOrderNo;

    /**
     * 操作时间
     */
    private Date operateTime;

    public GainCreditsRequest(String channel, String uid){
        this.setUid(uid);
        this.setChannel(channel);
    }
}
