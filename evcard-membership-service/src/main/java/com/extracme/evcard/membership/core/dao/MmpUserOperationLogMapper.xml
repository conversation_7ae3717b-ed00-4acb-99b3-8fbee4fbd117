<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.core.dao.MmpUserOperationLogMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.MmpUserOperationLog" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="user_id" property="userId" jdbcType="BIGINT" />
    <result column="operation_type" property="operationType" jdbcType="BIGINT" />
    <result column="operation_content" property="operationContent" jdbcType="VARCHAR" />
    <result column="ref_key1" property="refKey1" jdbcType="VARCHAR" />
    <result column="ref_key2" property="refKey2" jdbcType="VARCHAR" />
    <result column="ref_key3" property="refKey3" jdbcType="VARCHAR" />
    <result column="misc_desc" property="miscDesc" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, user_id, operation_type, operation_content, ref_key1, ref_key2, ref_key3, misc_desc, 
    status, create_time, create_oper_id, create_oper_name, update_time, update_oper_id, 
    update_oper_name
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from iss.mmp_user_operation_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from iss.mmp_user_operation_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.membership.core.model.MmpUserOperationLog" >
    insert into iss.mmp_user_operation_log (id, user_id, operation_type,
      operation_content, ref_key1, ref_key2, 
      ref_key3, misc_desc, status, 
      create_time, create_oper_id, create_oper_name, 
      update_time, update_oper_id, update_oper_name
      )
    values (#{id,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{operationType,jdbcType=BIGINT}, 
      #{operationContent,jdbcType=VARCHAR}, #{refKey1,jdbcType=VARCHAR}, #{refKey2,jdbcType=VARCHAR}, 
      #{refKey3,jdbcType=VARCHAR}, #{miscDesc,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, #{createOperName,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, #{updateOperName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.core.model.MmpUserOperationLog" >
    insert into iss.mmp_user_operation_log
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="userId != null" >
        user_id,
      </if>
      <if test="operationType != null" >
        operation_type,
      </if>
      <if test="operationContent != null" >
        operation_content,
      </if>
      <if test="refKey1 != null" >
        ref_key1,
      </if>
      <if test="refKey2 != null" >
        ref_key2,
      </if>
      <if test="refKey3 != null" >
        ref_key3,
      </if>
      <if test="miscDesc != null" >
        misc_desc,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateOperId != null" >
        update_oper_id,
      </if>
      <if test="updateOperName != null" >
        update_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="operationType != null" >
        #{operationType,jdbcType=BIGINT},
      </if>
      <if test="operationContent != null" >
        #{operationContent,jdbcType=VARCHAR},
      </if>
      <if test="refKey1 != null" >
        #{refKey1,jdbcType=VARCHAR},
      </if>
      <if test="refKey2 != null" >
        #{refKey2,jdbcType=VARCHAR},
      </if>
      <if test="refKey3 != null" >
        #{refKey3,jdbcType=VARCHAR},
      </if>
      <if test="miscDesc != null" >
        #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        #{updateOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.core.model.MmpUserOperationLog" >
    update iss.mmp_user_operation_log
    <set >
      <if test="userId != null" >
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="operationType != null" >
        operation_type = #{operationType,jdbcType=BIGINT},
      </if>
      <if test="operationContent != null" >
        operation_content = #{operationContent,jdbcType=VARCHAR},
      </if>
      <if test="refKey1 != null" >
        ref_key1 = #{refKey1,jdbcType=VARCHAR},
      </if>
      <if test="refKey2 != null" >
        ref_key2 = #{refKey2,jdbcType=VARCHAR},
      </if>
      <if test="refKey3 != null" >
        ref_key3 = #{refKey3,jdbcType=VARCHAR},
      </if>
      <if test="miscDesc != null" >
        misc_desc = #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.core.model.MmpUserOperationLog" >
    update iss.mmp_user_operation_log
    set user_id = #{userId,jdbcType=BIGINT},
      operation_type = #{operationType,jdbcType=BIGINT},
      operation_content = #{operationContent,jdbcType=VARCHAR},
      ref_key1 = #{refKey1,jdbcType=VARCHAR},
      ref_key2 = #{refKey2,jdbcType=VARCHAR},
      ref_key3 = #{refKey3,jdbcType=VARCHAR},
      misc_desc = #{miscDesc,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectOneMemOptLogByType" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from iss.mmp_user_operation_log
    where user_id = #{userId,jdbcType=BIGINT}
    <if test="operateTime != null">
      and create_time &gt; #{operateTime}
    </if>
    AND operation_type = #{operationType}
    limit 1
  </select>

  <select id="selectOneMemOptLogByTypes" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from iss.mmp_user_operation_log
    where user_id = #{userId,jdbcType=BIGINT}
    <if test="operateTime != null ">
      and create_time &gt; #{operateTime}
    </if>
    <if test="operationTypes != null and operationTypes.size>0 ">
      AND operation_type in
      <foreach collection="operationTypes" item="item" separator="," open="("
               close=")">
        #{item}
      </foreach>
    </if>
    limit 1
  </select>

  <select id="selectlastestMemOptLogByType" resultMap="BaseResultMap" >
    select
    <include refid="Base_Column_List" />
    from iss.mmp_user_operation_log
    where user_id = #{userId,jdbcType=BIGINT}
    AND operation_type = #{operationType}
    ORDER BY create_time desc
    limit 1
  </select>

  <select id="selectMmpUserOperationLog" resultMap="BaseResultMap">
    SELECT
      <include refid="Base_Column_List"/>
    FROM iss.mmp_user_operation_log
    where user_id = #{userId,jdbcType=BIGINT}
    ORDER BY create_time desc
    limit #{pageNum},#{pageSize}
  </select>

  <select id="selectOperationLogsByType" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM iss.mmp_user_operation_log
    where user_id = #{userId,jdbcType=BIGINT}
    <if test="operationTypes != null and operationTypes.size > 0 ">
      AND operation_type in
      <foreach collection="operationTypes" item="item" separator="," open="(" close=")">
        #{item}
      </foreach>
    </if>
    ORDER BY create_time desc
  </select>

</mapper>