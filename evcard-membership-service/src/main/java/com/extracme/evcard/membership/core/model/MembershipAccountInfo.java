package com.extracme.evcard.membership.core.model;

import com.alibaba.fastjson.annotation.JSONField;

import java.math.BigDecimal;
import java.util.Date;

public class MembershipAccountInfo {
    /**
     * 会员主键
     */
    private Long pkId;

    /**
     * 会员编号
     */
    private String authId;

    /**
     * 享道统一用户ID
     */
    private String uid;

    /**
     * 会员编号
     */
    private String mid;

    /**
     * 会员类别
     */
    private Short membershipType;

    /**
     * 手机号码
     */
    private String mobilePhone;

    /**
     * 姓名
     */
    private String name;

    /**
     * 注册时间
     */
    private String regTime;

    /**
     * 注销时间
     */
    private String unregisterTime;

    /**
     * 租车机构ID 默认00
     */
    private String agencyId;

    /**
     * 第三方接入的APP key
     */
    private String appKey;

    /**
     * 推送的通道id
     */
    private String channelId;

    /**
     * 会员来源(0：网点注册 1：网站注册 2：管理平台注册 3:手机APP 4:第三方 5：e享天开 6：e享天开/evcard共同会员 7：CRM)
     */
    private Integer dataOrigin;

    /**
     * （用户所属）市
     */
    private String cityOfOrigin;


    /**
     * 押金
     */
    private BigDecimal deposit;

    /**
     * 车辆押金
     */
    private BigDecimal depositVehicle;

    /**
     * E币
     */
    private BigDecimal rentMins;

    /**
     * 是否免押金 0：不免押金 1：免押金
     */
    private Integer exemptDeposit;


    /**
     * 状态(0：有效  1：无效)
     */
    private Integer status;

    /**
     * 审核状态(0:待审核 1：审核通过 2: 审核不通过 3：用户无效 4:重新审核）
     */
    private Integer reviewStatus;

    /**
     * 会员卡号
     */
    private String cardNo;

    /**
     * 卡激活状态
     */
    private Integer cardActiveStatus;

    /**
     * 卡状态
     */
    private Integer cardStatus;


    /**
     * 认证状态 0 未认证 1 未刷脸/未上传 2 已认证
     */
    private Integer authenticationStatus;

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public Short getMembershipType() {
        return membershipType;
    }

    public void setMembershipType(Short membershipType) {
        this.membershipType = membershipType;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRegTime() {
        return regTime;
    }

    public void setRegTime(String regTime) {
        this.regTime = regTime;
    }

    public String getUnregisterTime() {
        return unregisterTime;
    }

    public void setUnregisterTime(String unregisterTime) {
        this.unregisterTime = unregisterTime;
    }

    public String getAgencyId() {
        return agencyId;
    }

    public void setAgencyId(String agencyId) {
        this.agencyId = agencyId;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public Integer getDataOrigin() {
        return dataOrigin;
    }

    public void setDataOrigin(Integer dataOrigin) {
        this.dataOrigin = dataOrigin;
    }

    public String getCityOfOrigin() {
        return cityOfOrigin;
    }

    public void setCityOfOrigin(String cityOfOrigin) {
        this.cityOfOrigin = cityOfOrigin;
    }

    public BigDecimal getDeposit() {
        return deposit;
    }

    public void setDeposit(BigDecimal deposit) {
        this.deposit = deposit;
    }

    public BigDecimal getDepositVehicle() {
        return depositVehicle;
    }

    public void setDepositVehicle(BigDecimal depositVehicle) {
        this.depositVehicle = depositVehicle;
    }

    public BigDecimal getRentMins() {
        return rentMins;
    }

    public void setRentMins(BigDecimal rentMins) {
        this.rentMins = rentMins;
    }

    public Integer getExemptDeposit() {
        return exemptDeposit;
    }

    public void setExemptDeposit(Integer exemptDeposit) {
        this.exemptDeposit = exemptDeposit;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(Integer reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public Integer getCardActiveStatus() {
        return cardActiveStatus;
    }

    public void setCardActiveStatus(Integer cardActiveStatus) {
        this.cardActiveStatus = cardActiveStatus;
    }

    public Integer getCardStatus() {
        return cardStatus;
    }

    public void setCardStatus(Integer cardStatus) {
        this.cardStatus = cardStatus;
    }

    public Integer getAuthenticationStatus() {
        return authenticationStatus;
    }

    public void setAuthenticationStatus(Integer authenticationStatus) {
        this.authenticationStatus = authenticationStatus;
    }

    public Long getPkId() {
        return pkId;
    }

    public void setPkId(Long pkId) {
        this.pkId = pkId;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }
}
