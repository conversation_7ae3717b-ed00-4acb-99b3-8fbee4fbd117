package com.extracme.evcard.membership.credit.model;

import java.util.Date;

public class MmpUserAnalysis {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_analysis.id
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_analysis.year
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    private Integer year;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_analysis.month
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    private Integer month;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_analysis.org_id
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    private String orgId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_analysis.user_level
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    private Integer userLevel;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_analysis.member_cnt
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    private String memberCnt;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_analysis.member_rate
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    private String memberRate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_analysis.order_cnt
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    private String orderCnt;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_analysis.order_rate
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    private String orderRate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_analysis.real_pay
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    private String realPay;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_analysis.real_pay_rate
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    private String realPayRate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_analysis.remark
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    private String remark;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_analysis.create_time
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_analysis.create_oper_id
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    private Long createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_analysis.create_oper_name
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    private String createOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_analysis.update_time
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_analysis.update_oper_id
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    private Long updateOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column mmp_user_analysis.update_oper_name
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    private String updateOperName;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_analysis.id
     *
     * @return the value of mmp_user_analysis.id
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_analysis.id
     *
     * @param id the value for mmp_user_analysis.id
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_analysis.year
     *
     * @return the value of mmp_user_analysis.year
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public Integer getYear() {
        return year;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_analysis.year
     *
     * @param year the value for mmp_user_analysis.year
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public void setYear(Integer year) {
        this.year = year;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_analysis.month
     *
     * @return the value of mmp_user_analysis.month
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public Integer getMonth() {
        return month;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_analysis.month
     *
     * @param month the value for mmp_user_analysis.month
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public void setMonth(Integer month) {
        this.month = month;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_analysis.org_id
     *
     * @return the value of mmp_user_analysis.org_id
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public String getOrgId() {
        return orgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_analysis.org_id
     *
     * @param orgId the value for mmp_user_analysis.org_id
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_analysis.user_level
     *
     * @return the value of mmp_user_analysis.user_level
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public Integer getUserLevel() {
        return userLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_analysis.user_level
     *
     * @param userLevel the value for mmp_user_analysis.user_level
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public void setUserLevel(Integer userLevel) {
        this.userLevel = userLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_analysis.member_cnt
     *
     * @return the value of mmp_user_analysis.member_cnt
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public String getMemberCnt() {
        return memberCnt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_analysis.member_cnt
     *
     * @param memberCnt the value for mmp_user_analysis.member_cnt
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public void setMemberCnt(String memberCnt) {
        this.memberCnt = memberCnt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_analysis.member_rate
     *
     * @return the value of mmp_user_analysis.member_rate
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public String getMemberRate() {
        return memberRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_analysis.member_rate
     *
     * @param memberRate the value for mmp_user_analysis.member_rate
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public void setMemberRate(String memberRate) {
        this.memberRate = memberRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_analysis.order_cnt
     *
     * @return the value of mmp_user_analysis.order_cnt
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public String getOrderCnt() {
        return orderCnt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_analysis.order_cnt
     *
     * @param orderCnt the value for mmp_user_analysis.order_cnt
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public void setOrderCnt(String orderCnt) {
        this.orderCnt = orderCnt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_analysis.order_rate
     *
     * @return the value of mmp_user_analysis.order_rate
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public String getOrderRate() {
        return orderRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_analysis.order_rate
     *
     * @param orderRate the value for mmp_user_analysis.order_rate
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public void setOrderRate(String orderRate) {
        this.orderRate = orderRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_analysis.real_pay
     *
     * @return the value of mmp_user_analysis.real_pay
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public String getRealPay() {
        return realPay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_analysis.real_pay
     *
     * @param realPay the value for mmp_user_analysis.real_pay
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public void setRealPay(String realPay) {
        this.realPay = realPay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_analysis.real_pay_rate
     *
     * @return the value of mmp_user_analysis.real_pay_rate
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public String getRealPayRate() {
        return realPayRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_analysis.real_pay_rate
     *
     * @param realPayRate the value for mmp_user_analysis.real_pay_rate
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public void setRealPayRate(String realPayRate) {
        this.realPayRate = realPayRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_analysis.remark
     *
     * @return the value of mmp_user_analysis.remark
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public String getRemark() {
        return remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_analysis.remark
     *
     * @param remark the value for mmp_user_analysis.remark
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_analysis.create_time
     *
     * @return the value of mmp_user_analysis.create_time
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_analysis.create_time
     *
     * @param createTime the value for mmp_user_analysis.create_time
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_analysis.create_oper_id
     *
     * @return the value of mmp_user_analysis.create_oper_id
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public Long getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_analysis.create_oper_id
     *
     * @param createOperId the value for mmp_user_analysis.create_oper_id
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_analysis.create_oper_name
     *
     * @return the value of mmp_user_analysis.create_oper_name
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_analysis.create_oper_name
     *
     * @param createOperName the value for mmp_user_analysis.create_oper_name
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_analysis.update_time
     *
     * @return the value of mmp_user_analysis.update_time
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_analysis.update_time
     *
     * @param updateTime the value for mmp_user_analysis.update_time
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_analysis.update_oper_id
     *
     * @return the value of mmp_user_analysis.update_oper_id
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public Long getUpdateOperId() {
        return updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_analysis.update_oper_id
     *
     * @param updateOperId the value for mmp_user_analysis.update_oper_id
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column mmp_user_analysis.update_oper_name
     *
     * @return the value of mmp_user_analysis.update_oper_name
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public String getUpdateOperName() {
        return updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column mmp_user_analysis.update_oper_name
     *
     * @param updateOperName the value for mmp_user_analysis.update_oper_name
     *
     * @mbggenerated Mon Dec 11 10:49:35 CST 2017
     */
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }
}