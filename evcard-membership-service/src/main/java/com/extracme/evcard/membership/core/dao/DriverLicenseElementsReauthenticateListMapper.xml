<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.core.dao.DriverLicenseElementsReauthenticateListMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.DriverLicenseElementsReauthenticateList" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="user_id" property="userId" jdbcType="BIGINT" />
    <result column="authenticate_status" property="authenticateStatus" jdbcType="INTEGER" />
    <result column="license_status" property="licenseStatus" jdbcType="INTEGER" />
    <result column="license_status_msg" property="licenseStatusMsg" jdbcType="VARCHAR" />
    <result column="lastest_authenticate_time" property="lastestAuthenticateTime" jdbcType="TIMESTAMP" />
    <result column="next_authenticate_time" property="nextAuthenticateTime" jdbcType="TIMESTAMP" />
    <result column="retry_times" property="retryTimes" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="misc_desc" property="miscDesc" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, user_id, authenticate_status, license_status, license_status_msg, lastest_authenticate_time, 
    next_authenticate_time, retry_times, status, misc_desc, create_time, create_oper_id, create_oper_name, update_time,
    update_oper_id, update_oper_name
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from ${siacSchema}.driver_license_elements_reauthenticate_list
    where id = #{id,jdbcType=BIGINT}
  </select>


  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from ${siacSchema}.driver_license_elements_reauthenticate_list
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.membership.core.model.DriverLicenseElementsReauthenticateList" >
    insert into ${siacSchema}.driver_license_elements_reauthenticate_list (id, user_id, authenticate_status, 
      license_status, license_status_msg, lastest_authenticate_time, next_authenticate_time,
      retry_times, status, misc_desc, 
      create_time, create_oper_id, create_oper_name, 
      update_time, update_oper_id, update_oper_name
      )
    values (#{id,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{authenticateStatus,jdbcType=INTEGER}, 
      #{licenseStatus,jdbcType=INTEGER}, #{licenseStatusMsg,jdbcType=VARCHAR}, #{lastestAuthenticateTime,jdbcType=TIMESTAMP},
      #{nextAuthenticateTime,jdbcType=TIMESTAMP},
      #{retryTimes,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{miscDesc,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, #{createOperName,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, #{updateOperName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.core.model.DriverLicenseElementsReauthenticateList" >
    insert into ${siacSchema}.driver_license_elements_reauthenticate_list
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="userId != null" >
        user_id,
      </if>
      <if test="authenticateStatus != null" >
        authenticate_status,
      </if>
      <if test="licenseStatus != null" >
        license_status,
      </if>
      <if test="licenseStatusMsg != null" >
        license_status_msg,
      </if>
      <if test="lastestAuthenticateTime != null" >
        lastest_authenticate_time,
      </if>
      <if test="nextAuthenticateTime != null" >
        next_authenticate_time,
      </if>
      <if test="retryTimes != null" >
        retry_times,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="miscDesc != null" >
        misc_desc,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateOperId != null" >
        update_oper_id,
      </if>
      <if test="updateOperName != null" >
        update_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="authenticateStatus != null" >
        #{authenticateStatus,jdbcType=INTEGER},
      </if>
      <if test="licenseStatus != null" >
        #{licenseStatus,jdbcType=INTEGER},
      </if>
      <if test="licenseStatusMsg != null" >
        #{licenseStatusMsg,jdbcType=VARCHAR},
      </if>
      <if test="lastestAuthenticateTime != null" >
        #{lastestAuthenticateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="nextAuthenticateTime != null" >
        #{nextAuthenticateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="retryTimes != null" >
        #{retryTimes,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="miscDesc != null" >
        #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        #{updateOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.core.model.DriverLicenseElementsReauthenticateList" >
    update ${siacSchema}.driver_license_elements_reauthenticate_list
    <set >
      <if test="userId != null" >
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="authenticateStatus != null" >
        authenticate_status = #{authenticateStatus,jdbcType=INTEGER},
      </if>
      <if test="licenseStatus != null" >
        license_status = #{licenseStatus,jdbcType=INTEGER},
      </if>
      <if test="licenseStatusMsg != null" >
        license_status_msg = #{licenseStatusMsg,jdbcType=VARCHAR},
      </if>
      <if test="lastestAuthenticateTime != null" >
        lastest_authenticate_time = #{lastestAuthenticateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="nextAuthenticateTime != null" >
        next_authenticate_time = #{nextAuthenticateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="retryTimes != null" >
        retry_times = #{retryTimes,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="miscDesc != null" >
        misc_desc = #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.core.model.DriverLicenseElementsReauthenticateList" >
    update ${siacSchema}.driver_license_elements_reauthenticate_list
    set user_id = #{userId,jdbcType=BIGINT},
      authenticate_status = #{authenticateStatus,jdbcType=INTEGER},
      license_status = #{licenseStatus,jdbcType=INTEGER},
      license_status_msg = #{licenseStatusMsg,jdbcType=VARCHAR},
      lastest_authenticate_time = #{lastestAuthenticateTime,jdbcType=TIMESTAMP},
      next_authenticate_time = #{nextAuthenticateTime,jdbcType=TIMESTAMP},
      retry_times = #{retryTimes,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      misc_desc = #{miscDesc,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="disableLastRecordsByUserId" parameterType="com.extracme.evcard.membership.core.model.DriverLicenseElementsReauthenticateList" >
    update ${siacSchema}.driver_license_elements_reauthenticate_list
    set status = 0,
    <if test="authenticateStatus != null" >
      authenticate_status = #{authenticateStatus,jdbcType=INTEGER},
    </if>
    <if test="licenseStatus != null" >
      license_status = #{licenseStatus,jdbcType=INTEGER},
    </if>
    <if test="licenseStatusMsg != null" >
      license_status_msg = #{licenseStatusMsg,jdbcType=VARCHAR},
    </if>
    <if test="lastestAuthenticateTime != null" >
      lastest_authenticate_time = #{lastestAuthenticateTime,jdbcType=TIMESTAMP},
    </if>
    <if test="updateOperId != null" >
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
    </if>
    <if test="updateOperName != null" >
      update_oper_name = #{updateOperName,jdbcType=VARCHAR},
    </if>
    <if test="miscDesc != null" >
      misc_desc = concat(ifNULL(misc_desc,''), #{miscDesc,jdbcType=VARCHAR}),
    </if>
    update_time = CURRENT_TIMESTAMP ()
    where user_id = #{userId,jdbcType=BIGINT}
    AND status in (1,2)
    AND authenticate_status = 3
  </update>

  <select id="selectLatestRecordByUserId" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from ${siacSchema}.driver_license_elements_reauthenticate_list
    where user_id = #{userId,jdbcType=BIGINT}
    AND status = 1
    AND authenticate_status = 3
    order by id
    limit 1
  </select>


  <update id="updateWhenReAuthByPrimaryKey" parameterType="com.extracme.evcard.membership.core.model.DriverLicenseElementsReauthenticateList" >
    update ${siacSchema}.driver_license_elements_reauthenticate_list
    <set >
      <if test="authenticateStatus != null" >
        authenticate_status = #{authenticateStatus,jdbcType=INTEGER},
      </if>
      <if test="licenseStatus != null" >
        license_status = #{licenseStatus,jdbcType=INTEGER},
      </if>
      <if test="licenseStatusMsg != null" >
        license_status_msg = #{licenseStatusMsg,jdbcType=VARCHAR},
      </if>
      <if test="lastestAuthenticateTime != null" >
        lastest_authenticate_time = #{lastestAuthenticateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="nextAuthenticateTime != null" >
        next_authenticate_time = #{nextAuthenticateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="retryTimes != null" >
        retry_times = retry_times + #{retryTimes,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
        <if test="miscDesc != null" >
        misc_desc = #{miscDesc,jdbcType=VARCHAR},
        </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
    AND status = 1
  </update>

<!--旧的查询方案，不保存下次触发时间时使用-->
<!--  <select id="selectReAuthenticateList" resultMap="BaseResultMap" >-->
<!--    select <include refid="Base_Column_List" />-->
<!--    from ${siacSchema}.driver_license_elements_reauthenticate_list-->
<!--    where `status` = 1-->
<!--    AND authenticate_status = 3-->
<!--    AND create_time BETWEEN #{createStartTime} AND #{createEndTime}-->
<!--    AND retry_times &lt;= #{maxRetryTimes}-->
<!--    <if test="retryConditions != null and retryConditions.size > 0">-->
<!--      AND-->
<!--      <foreach item="item" collection="retryConditions" index="index" open="(" close=")"  separator=" OR ">-->
<!--        (retry_times = #{item.retryTimes} AND lastest_authenticate_time BETWEEN #{item.startTime} AND #{item.endTime})-->
<!--      </foreach>-->
<!--    </if>-->
<!--    ORDER BY id desc-->
<!--  </select>-->


  <select id="selectReAuthenticateList" resultMap="BaseResultMap" >
    select <include refid="Base_Column_List" />
    from ${siacSchema}.driver_license_elements_reauthenticate_list
    where `status` = 1
    AND authenticate_status = 3
    AND create_time BETWEEN #{createStartTime} AND #{createEndTime}
    AND retry_times &lt;= #{maxRetryTimes}
    AND next_authenticate_time &lt;= CURRENT_TIMESTAMP
    ORDER BY id desc
  </select>



</mapper>