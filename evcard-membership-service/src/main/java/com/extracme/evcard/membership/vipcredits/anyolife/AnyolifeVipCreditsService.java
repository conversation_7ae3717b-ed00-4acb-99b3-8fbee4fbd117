package com.extracme.evcard.membership.vipcredits.anyolife;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.core.dto.UserTitlePointsOfferSingleDto;
import com.extracme.evcard.membership.core.dto.UserTitlePointsQueryDto;
import com.extracme.evcard.membership.core.dto.UserTitleUpgradeDto;
import com.extracme.evcard.membership.core.dto.UserTitleUpgradePointsDto;
import com.extracme.evcard.membership.core.exception.MemberException;
import com.extracme.evcard.membership.credit.dto.*;
import com.extracme.evcard.membership.credit.service.IMemberCreditsServiceProvider;
import com.extracme.evcard.membership.vipcredits.anyolife.entity.*;
import com.extracme.evcard.mq.bean.MemPointsPushEnum;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.dto.Page;
import com.extracme.evcard.rpc.dto.PageBeanDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 安悦anyolife积分商城对接服务
 * <AUTHOR>
 * @Discription
 * @date 2020/8/12
 */
@Slf4j
@Component("anyolifeVipCreditsService")
public class AnyolifeVipCreditsService implements IMemberCreditsServiceProvider {

    @Resource
    private AnyolifeRestClient anyolifeRestClient;

    private static final String BASIC_FEE_CHANNEL = "01";

    /**
     * 需要重新尝试发放的code
     * 10039051	订单[orderNo=EVPOINTS_11]已存在
     * 10039002	客户端请求超时	    重新消费
     * 99		该账户已存在
     * 9001		系统错误，请稍后再试   重新消费
     */
    private static final Integer[] OFFER_RETRY_CODES = {10039002, 9001};

    @Override
    public MemberPointsOfferResp offerPoints(MemberPointsOfferDto offerDto) throws MemberException {
        GainCreditsRequest request = new GainCreditsRequest(BASIC_FEE_CHANNEL, offerDto.getAuthId());
        request.setFeeType(offerDto.getEventType());
        request.setOrderNo(offerDto.getRequestKey());
        if(MemPointsPushEnum.POINTS_REWARD.getCode().equals(offerDto.getEventType())) {
            request.setGainCredits(new BigDecimal(offerDto.getPointsNum()));
        } else if(MemPointsPushEnum.MEMBER_LOTTERY_REWARD.getCode().equals(offerDto.getEventType())) {
            request.setGainCredits(new BigDecimal(offerDto.getPointsNum()));
        } else if(MemPointsPushEnum.CARBON_ORDER_SHARE.getCode().equals(offerDto.getEventType())) {
            request.setShareOrderNo(offerDto.getEventRefSeq());
        } else if(MemPointsPushEnum.MD_ORDER_PICKUP_ASSESS.getCode().equals(offerDto.getEventType())
            || MemPointsPushEnum.MD_ORDER_RETURN_ASSESS.getCode().equals(offerDto.getEventType())) {
            /**
             * 门店取还车评价积分推送
             */
            request.setGainCredits(new BigDecimal(offerDto.getPointsNum()));
            request.setOrderNo(offerDto.getEventRefSeq() + request.getFeeType() + request.getGainCredits());
        }
        else{
            request.setGainCredits(offerDto.getAmount());
        }
        request.setFeeDetail(offerDto.getRemark());
        request.setOrderDetail(offerDto.getRemark());
        request.setOperateTime(offerDto.getCreateTime());
        log.info("anyolife.vipcredits：gainCredits积分发放开始, input={}", JSON.toJSONString(request));
        GainCreditsResponse response = anyolifeRestClient.gainCredits(request);
        if(response == null) {
            throw new MemberException(-1, "调用第三方积分发放接口失败");
        }
        MemberPointsOfferResp resp = new MemberPointsOfferResp();
        if(!NumberUtils.INTEGER_ZERO.equals(response.getRetCode())) {
            log.warn("offerPoints: gainCredits积分发放失败，input={}, result={}", JSON.toJSONString(request), JSON.toJSONString(response));
            resp.setCode(response.getRetCode());
            resp.setMessage(response.getRetMsg());
            //标记此次积分发放操作是否需要重试。
            if(ArrayUtils.contains(OFFER_RETRY_CODES, response.getRetCode())) {
                resp.setRetry(1);
            }
            return resp;
        }
        resp.setCode(response.getRetCode());
        resp.setMessage(response.getRetMsg());
        resp.setExpireDate(response.getExpireDate());
        resp.setPayOrderId(response.getPayOrderId());
        resp.setGainPoints(response.getGainCredits());
        resp.setOfferStatus(response.getStatus());
        resp.setSuccess("1".equals(response.getStatus()));
        return resp;
    }

    @Override
    public PageBeanDto<MemberPointsHistoryDto> queryUserHistoryPage(MemberPointsHistoryQueryDto queryDto) {
        QueryCreditsHistoryRequest request = new QueryCreditsHistoryRequest(BASIC_FEE_CHANNEL, queryDto.getAuthId());
        request.setCreditType(queryDto.getOptionType());
        request.setBegDate(queryDto.getStartDate());
        request.setEndDate(queryDto.getEndDate());
        request.setExpireType(null);
        request.setPage(queryDto.getPageNum());
        request.setPageSize(queryDto.getPageSize());
        log.info("anyolife.vipcredits：queryUserCreditsHistory开始, input={}", JSON.toJSONString(request));
        QueryCreditsHistoryResponse response = anyolifeRestClient.queryUserCreditsHistory(request);
        if(response == null) {
            throw new MemberException(-1, "调用第三方积分履历查询接口失败");
        }
        PageBeanDto<MemberPointsHistoryDto> pageBeanDto = new PageBeanDto<>();
        if(!NumberUtils.INTEGER_ZERO.equals(response.getRetCode())) {
            log.warn("queryUserHistoryPage: 积分变更履历查询失败，input={}, result={}", JSON.toJSONString(request), JSON.toJSONString(response));
            pageBeanDto.setCode(response.getRetCode());
            pageBeanDto.setMessage(response.getRetMsg());
            return pageBeanDto;
        }
        List<MemberPointsHistoryDto> records = new ArrayList<>();
        List<CreditHistoryRecord> list = response.getCreditsInfo();
        if(CollectionUtils.isNotEmpty(list)) {
            for(CreditHistoryRecord record : list) {
                MemberPointsHistoryDto dto = new MemberPointsHistoryDto();
                dto.setChangeReason(record.getChangeReason());
                dto.setCredits(record.getCredits());
                dto.setExpireDate(record.getExpireDate());
                dto.setCauseType(record.getFeeType());
                dto.setOptionType(record.getCreditsType());
                dto.setOptionDate(record.getUpdateDate());
                dto.setBalanceCredits(record.getLeftCredits());
                dto.setRecordNo(record.getOrderNo());
                dto.setPayOrderId(record.getPayOrderId());
                records.add(dto);
            }
        }
        Page page = new Page(queryDto.getPageNum(),queryDto.getPageSize());
        page.setCount(response.getTotalSize().intValue());
        pageBeanDto.setPage(page);
        pageBeanDto.setList(records);
        return pageBeanDto;
    }

    @Override
    public MemberPointsAccountDto queryUserAccount(MemberPointsAccountQueryDto queryDto) {
        BaseCreditsRequest request = new BaseCreditsRequest(queryDto.getPointsType(), queryDto.getAuthId());
        GetUserCreditsResponse response = anyolifeRestClient.getUserCreditsAccount(request);
        //调用成功
        if(response == null || response.getRetCode() == null) {
            log.warn("queryUserAccount: 积分账户查询失败，input={}, result={}", JSON.toJSONString(request), JSON.toJSONString(response));
            return null;
        }
        MemberPointsAccountDto result = new MemberPointsAccountDto();
        result.setCode(response.getRetCode());
        result.setMessage(response.getRetMsg());
        result.setCredits(response.getCredits());
        result.setMinExpireDate(response.getMinExpireDate());
        result.setMaxExpireDate(response.getMaxExpireDate());
        result.setStatus(response.getCreditsStatus());
        result.setUserStatus(response.getUserStatus());
        return result;
    }

    @Override
    public MemberPointsGainQueryResp queryGainCredits(MemberPointsGainQueryDto queryDto) {
        QueryCreditsBeforeRequest request = new QueryCreditsBeforeRequest(queryDto.getPointsType(), queryDto.getAuthId());
        request.setFeeType(queryDto.getFeeType());
        if(queryDto.getGainCredits() != null) {
            request.setGainCredits(BigDecimal.valueOf(queryDto.getGainCredits()));
        }
        if(MemPointsPushEnum.CARBON_ORDER_SHARE.getCode().equals(queryDto.getFeeType())) {
            request.setShareOrderNo(queryDto.getEventRefSeq());
        }
        QueryCreditsBeforeResponse response = anyolifeRestClient.queryGainCredits(request);
        //调用成功
        if(response == null || response.getRetCode() == null) {
            log.warn("queryGainCredits: 查询用户将获得的积分数失败，input={}, result={}", JSON.toJSONString(request), JSON.toJSONString(response));
            return null;
        }
        MemberPointsGainQueryResp result = new MemberPointsGainQueryResp();
        result.setCode(response.getRetCode());
        result.setMessage(response.getRetMsg());
        result.setGainCredits(response.getGainCredits());
        return result;
    }

    @Override
    public List<UserTitleUpgradePointsDto> queryCarbonReduceTitlePoints(UserTitlePointsQueryDto queryDto) {
        /**
         * 入参转换
         */
        UserTitlePointsQueryRequest input = new UserTitlePointsQueryRequest();
        input.setUid(queryDto.getAuthId());
        List<TitleUpgradeInfo> datalist = new ArrayList<>();
        for(UserTitleUpgradeDto upgradeDto : queryDto.getUpgradeRecords()) {
            TitleUpgradeInfo record = new TitleUpgradeInfo();
            record.fromUpgradeDto(upgradeDto);
            datalist.add(record);
        }
        input.setDatalist(datalist);
        TitlePointsQueryResponse response = anyolifeRestClient.queryCarbonReduceTitlePoints(input);
        if(response == null || response.getRetCode() == null || response.getDatalist() == null) {
            log.warn("queryCarbonReduceTitlePoints: 查询用户将获得的称号奖励积分数失败，input={}, result={}",
                    JSON.toJSONString(input), JSON.toJSONString(response));
            return null;
        }
        //调用成功
        List<UserTitleUpgradePointsDto> pointsResult = new ArrayList<>();
        for(TitleUpgradeInfoResp upgradeInfoResp : response.getDatalist()) {
            UserTitleUpgradePointsDto resultDto = upgradeInfoResp.toPointsResultDto();
            if(pointsResult == null) {
                continue;
            }
            pointsResult.add(resultDto);
        }
        return pointsResult;
    }

    @Override
    public MemberPointsOfferResp offerCarbonReduceTitlePoints(UserTitlePointsOfferSingleDto offerDto) {
        UserTitlePointsOfferRequest request = new UserTitlePointsOfferRequest();
        request.setUid(offerDto.getAuthId());
        request.setOrderId("CarbonRTitle_" + String.valueOf(offerDto.getId()));
        request.fromUpgradeDto(offerDto);
        log.info("anyolife.vipcredits：offerCarbonReduceTitlePoints积分发放开始, input={}", JSON.toJSONString(request));
        TitlePointsOfferResponse response = anyolifeRestClient.offerCarbonReduceTitlePoints(request);
        if(response == null) {
            throw new MemberException(-1, "调用第三方积分发放接口失败");
        }
        MemberPointsOfferResp resp = new MemberPointsOfferResp();
        if(!NumberUtils.INTEGER_ZERO.equals(response.getRetCode()) || !"1".equals(response.getStatus())) {
            log.warn("offerPoints: offerCarbonReduceTitlePoints积分发放失败，input={}, result={}", JSON.toJSONString(request), JSON.toJSONString(response));
            resp.setCode(response.getRetCode());
            resp.setMessage(response.getRetMsg());
            resp.setSuccess(false);
            //标记此次积分发放操作是否需要重试。
            if(ArrayUtils.contains(OFFER_RETRY_CODES, response.getRetCode())) {
                resp.setRetry(1);
            }
            return resp;
        }
        resp.setCode(response.getRetCode());
        resp.setMessage(response.getRetMsg());
        resp.setOfferStatus(response.getStatus());
        resp.setSuccess("1".equals(response.getStatus()));
        return resp;
    }
}
