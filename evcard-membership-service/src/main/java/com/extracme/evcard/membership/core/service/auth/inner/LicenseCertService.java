package com.extracme.evcard.membership.core.service.auth.inner;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.dao.UserOperatorLogMapper;
import com.extracme.evcard.membership.core.dto.AccountStatusDto;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.dto.OperatorDto;
import com.extracme.evcard.membership.core.dto.SubmitDriverLicenseRespDto;
import com.extracme.evcard.membership.core.dto.input.MemberReAuditInput;
import com.extracme.evcard.membership.core.enums.*;
import com.extracme.evcard.membership.core.exception.AuthenticationException;
import com.extracme.evcard.membership.core.input.SubmitDriverLicenseInput;
import com.extracme.evcard.membership.core.input.UserOperationLogInput;
import com.extracme.evcard.membership.core.manager.MemberDocumentManager;
import com.extracme.evcard.membership.core.model.MemberIdentityDocument;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.membership.core.service.MemberOptLogService;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.model.MembershipBaseInfo;
import com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs;
import com.extracme.evcard.redis.JedisUtil;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.entity.HidLog;
import com.extracme.evcard.rpc.entity.LogPoint;
import com.extracme.evcard.rpc.enums.StatusCode;
import com.extracme.evcard.rpc.exception.BusinessRuntimeException;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class LicenseCertService {

    @Autowired
    IMemberShipService memberShipService;

    @Resource
    private CertSubmitChecker certSubmitChecker;

    @Resource
    private MembershipInfoMapper membershipInfoMapper;

    @Resource
    private MemberDocumentManager memberDocumentManager;

    @Resource
    private MemberOptLogService memberOptLogService;

    @Value("${env_sub}")
    private String envSub;

    @Resource
    private UserOperatorLogMapper userOperatorLogMapper;

    @Transactional(rollbackFor = Exception.class)
    public DefaultServiceRespDTO reAuditMember(MemberReAuditInput input,boolean checkLicenseStatus){
        String mid = input.getMid();
        String operatorId = input.getOperatorId();
        String operatorName = input.getOperatorName();
        String operateSource = input.getOperateSource();
        Date now = new Date();

        // 查询membershipInfo
        MembershipBasicInfo membershipInfo = membershipInfoMapper.getUserBasicInfoByMid(mid);
        if (membershipInfo == null) {
            return new DefaultServiceRespDTO(-1, "未查询到用户");
        }

        if (checkLicenseStatus) {
            // 未认证 不能重新认证
            if (LicenseAuthStatusEnum.unAuthed(membershipInfo.getLicenseReviewStatus())) {
                return new DefaultServiceRespDTO(-1, "用户驾照认证状态不允许重新认证");
            }
        }

        //membershipInfoMapper 更新
        MembershipInfoWithBLOBs updateMember = new MembershipInfoWithBLOBs();
        //审核状态
        updateMember.setReviewStatus((short) -1);
        //主键
        updateMember.setPkId(membershipInfo.getPkId());
        //更新时间
        updateMember.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
        //更新操作人
        updateMember.setUpdatedUser(operatorName);
        //驾照认证状态 1:未认证、2:待认证、3:已认证、4:认证失败、 5:即将过期（剩余90天）、6:已过期
        updateMember.setLicenseReviewStatus(LicenseAuthStatusEnum.UN_SUBMIT.getValue());
        updateMember.setReviewUser(operatorName);
        // 这里给 ReviewItems 设置为 0000000
        updateMember.setReviewItems(BussinessConstants.IDCARD_REVIEW_ITEMS_INIT);
        // 清除驾照数据
        updateMember.setDriverCode(StringUtils.EMPTY);
        updateMember.setObtainDriverTimer(StringUtils.EMPTY);
        updateMember.setLicenseExpirationTime(StringUtils.EMPTY);
        updateMember.setFileNoImgUrl(StringUtils.EMPTY);
        updateMember.setDrivingLicenseImgUrl(StringUtils.EMPTY);
        updateMember.setFileNo(StringUtils.EMPTY);
        membershipInfoMapper.clearLicenseByPrimaryKeySelective(updateMember);

        // 记录操作日志
        ComUtil.insertOperatorLog(MemOperateTypeEnum.LICENSE_MEMBER_REAUDIT.getOperate(), membershipInfo.getAuthId(),
                membershipInfo.getMobilePhone(), operatorName, userOperatorLogMapper);

        // 操作日志
        UserOperationLogInput operationLog = new UserOperationLogInput();
        operationLog.setUserId(membershipInfo.getPkId());
        operationLog.setAuthId(membershipInfo.getAuthId());
        operationLog.setMembershipType((int)membershipInfo.getMembershipType());
        operationLog.setOperationType(MemOperateTypeEnum.LICENSE_MEMBER_REAUDIT.getCode());
        operationLog.setOperationContent("驾驶证重新认证");
        operationLog.setRefKey1(operateSource);
        operationLog.setOperatorId(Long.valueOf(operatorId));
        operationLog.setOperator(operatorName);
        operationLog.setOperationTime(now);
        memberShipService.saveUserOperationLog(operationLog);
        return DefaultServiceRespDTO.SUCCESS;
    }

    @Transactional(rollbackFor = Exception.class)
    public SubmitDriverLicenseRespDto submitDriverLicense(SubmitDriverLicenseInput input) throws AuthenticationException {
        log.info("提交驾照信息-提交开始：mid={}, appKey={}, input={}", input.getMid(), input.getAppKey(), JSON.toJSONString(input));
        HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, "提交驾照信息开始，appKey=" + input.getAppKey()
                + ", input=" + JSON.toJSONString(input), input.getMid(), true);
        /**
         * 1. 参数校验
         * //档案编号是否可不再校验 || StringUtils.isBlank(input.getFileNo())
         */
        if(StringUtils.isBlank(input.getMid()) || StringUtils.isBlank(input.getDriverCode()) || StringUtils.isBlank(input.getName())
                || input.getAppKey() == null || input.getDriverLicenseType() == null || StringUtils.isBlank(input.getLicenseType())
                || input.getExpireType() == null || StringUtils.isBlank(input.getDriverLicenseImgUrl())
                || (input.getExpireType().equals(1) && StringUtils.isBlank(input.getExpirationDate()))) {
            throw new AuthenticationException(StatusCode.ILLEGAL_PARAM);
        }
        input.setDriverLicenseImgUrl(ComUtil.splitPicUrl(input.getDriverLicenseImgUrl()));
        input.setReverseDriverLicenseImgUrl(ComUtil.splitPicUrl(input.getReverseDriverLicenseImgUrl()));
        input.setDriverCode(input.getDriverCode().toUpperCase());

        boolean isNeedOccupied = StringUtils.equals(BussinessConstants.APPKEY_OFC, input.getAppKey()) && StringUtils.isNotEmpty(input.getOccupiedMid());
        //1.1 频繁提交
        // 20221208 由于用户流程走不下去，业务要求去掉该验证
       /* BaseResponse checkTimesResp = certSubmitChecker.checkSubmitTimes("license", input.getMid());
        if (checkTimesResp != null) {
            log.warn("提交驾照信息，不可频繁提交, mid={}, resp={}", input.getMid(), JSON.toJSONString(checkTimesResp));
            return new SubmitDriverLicenseRespDto(AuthStateCodeEnum.SUBMIT_TIMES_LIMIT, checkTimesResp.getMessage());
        }*/
        /**
         * 1.2 检查用户状态
         */
        MembershipBasicInfo member = membershipInfoMapper.getUserBasicInfoByMid(input.getMid());
        if (member == null || member.getAccountStatus().equals(2)) {
            HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, StatusCode.USER_INFO_NO_EXIST.getMsg(), input.getMid(), false);
            throw new AuthenticationException(StatusCode.USER_INFO_NO_EXIST);
        }
        //身份认证状态非已认证，不能提交驾照
        MemberIdentityDocument memberIdentity = memberDocumentManager.getMemberIdentity(input.getMid());
        //TODO 暂时不校验需要先身份认证通过
        //身份认证状态为待认证、已认证状态允许提交驾照
//        if(memberIdentity == null || !IdentityAuthStatusEnum.AUTHENTICATED.eq(memberIdentity.getAuthenticationStatus())) {
//            throw new AuthenticationException(StatusCode.AUTHENTICATION_ERROR_ONE);
//        }

        //1.2 校验驾照号是否在冻结中
        String driverCode = input.getDriverCode();
        AccountStatusDto accountStatusDto = memberShipService.getAccountStatusByDriverCode(driverCode, 0);
        if (accountStatusDto != null) {
            if (accountStatusDto.getAccountStatus() == 1) {
                HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, StatusCode.DRIVER_CODE_LOGOUT_FREEZE_ERROR.getMsg(), input.getMid(), false);
                return new SubmitDriverLicenseRespDto(AuthStateCodeEnum.DRIVER_LICENSE_IN_USE);
            }
        }

        //1.5 证件有效期检查
        if(input.getExpireType().equals(1)) {
            AuthStateCodeEnum checkResp = certSubmitChecker.checkDriverLicense(input.getLicenseType(), input.getExpirationDate());
            if(checkResp != null) {
                return new SubmitDriverLicenseRespDto(checkResp);
            }
        }

        //1.4 驾照格式校验：  军官校验必须15位、 大陆校验符合身份证规则、 外籍港澳台校验必须18位。
        boolean checkDriverCodeRes = (input.getDriverCode().length() >= 15); //若身份认证尚未校验，则提示。
        if(memberIdentity != null) {
            if(memberIdentity.getIdentityType().equals(1)) {
                checkDriverCodeRes = ComUtil.checkIDCard(input.getDriverCode());
            } else if(memberIdentity.getIdentityType().equals(5)) {
                checkDriverCodeRes = (input.getDriverCode().length() == 15);
            }else {
                checkDriverCodeRes = (input.getDriverCode().length() <= 18);
            }
        }
        if (!checkDriverCodeRes) {
            HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, StatusCode.DRIVER_CODE_ERROR.getMsg(), input.getMid(), false);
            return new SubmitDriverLicenseRespDto(AuthStateCodeEnum.DRIVER_LICENSE_ERROR);
        }

        //1.6 本籍校验 证件编号及姓名是否与身份证件一致(军官仅校验姓名一致)
        if(!StringUtils.equals(envSub, BussinessConstants.ENV_SUB_STRESS)) {
            if (memberIdentity != null && IdTypeEnum.isMainlandId(memberIdentity.getIdentityType())) {
                if(IdTypeEnum.ID_CARD.eq(memberIdentity.getIdentityType()) &&
                        !StringUtils.equals(memberIdentity.getIdentityNo(), input.getDriverCode())) {
                    HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO,
                            AuthStateCodeEnum.LICENSE_ID_NO_NOT_MATCH.getMsg() + "驾照号/姓名", input.getMid(), false);
                    return new SubmitDriverLicenseRespDto(AuthStateCodeEnum.LICENSE_ID_NO_NOT_MATCH);
                }
                if (!StringUtils.equals(memberIdentity.getName(), input.getName())) {
                    HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO,
                            AuthStateCodeEnum.LICENSE_ID_NAME_NOT_MATCH.getMsg() + "驾照号/姓名", input.getMid(), false);
                    return new SubmitDriverLicenseRespDto(AuthStateCodeEnum.LICENSE_ID_NAME_NOT_MATCH);
                }
            }
        }

        /**
         * 1.2 检查驾照号是否被占用
         */
        String authId = member.getAuthId();
        // 虚拟用户驾照号可以被重复占用
        if (member.getMembershipType() != MemberTypeEnum.QINGLU_USER.getValue().shortValue()) {
            List<MembershipBaseInfo> membershipBaseInfoList = membershipInfoMapper.checkDriverCodeOccupied(authId, input.getDriverCode(), 0);
            if (CollectionUtils.isNotEmpty(membershipBaseInfoList) && !isNeedOccupied) {
                HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, StatusCode.DRIVER_CODE_OCCUPIED.getMsg(), authId, false);
                SubmitDriverLicenseRespDto submitDriverLicenseRespDto = new SubmitDriverLicenseRespDto(AuthStateCodeEnum.SAME_LICENSE_ID);
                if (memberIdentity != null && IdTypeEnum.isMainlandId(memberIdentity.getIdentityType())) {
                    if (StringUtils.equals(BussinessConstants.APPKEY_OFC, input.getAppKey())) {
                        submitDriverLicenseRespDto.setOccupiedMid(membershipBaseInfoList.get(0).getMid());
                        submitDriverLicenseRespDto.setOccupiedMobilePhone(membershipBaseInfoList.get(0).getMobilePhone());
                    }
                }
                return submitDriverLicenseRespDto;
            }
        }
        /**
         * 1.3 检查档案编号
         */
        if (StringUtils.isNotBlank(input.getFileNo())) {
            if(input.getFileNo().length() != 12 || !ComUtil.isNumeric(input.getFileNo())) {
                HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, StatusCode.FILE_NO_ERROR.getMsg(), authId, false);
                throw new AuthenticationException(StatusCode.FILE_NO_ERROR);
            }

            // 虚拟用户驾照号可以被重复占用
            if (member.getMembershipType() != MemberTypeEnum.QINGLU_USER.getValue().shortValue()) {
                List<MembershipBaseInfo> fileNoMember = membershipInfoMapper.checkFileNoOccupied(authId,input.getFileNo(),0);
                if (CollectionUtils.isNotEmpty(fileNoMember) && !isNeedOccupied){
                    HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, StatusCode.FILE_NO_OCCUPIED.getMsg(), authId, false);
                    SubmitDriverLicenseRespDto submitDriverLicenseRespDto = new SubmitDriverLicenseRespDto(AuthStateCodeEnum.SAME_LICENSE_ID);
                    if (memberIdentity != null && IdTypeEnum.isMainlandId(memberIdentity.getIdentityType())) {
                        if (StringUtils.equals(BussinessConstants.APPKEY_OFC, input.getAppKey())) {
                            submitDriverLicenseRespDto.setOccupiedMid(fileNoMember.get(0).getMid());
                            submitDriverLicenseRespDto.setOccupiedMobilePhone(fileNoMember.get(0).getMobilePhone());
                        }
                    }
                    return submitDriverLicenseRespDto;
                }
            }
        }

        /**
         * 2. 驾照数据提交
         * 初次提交(未认证)、重新提交(认证不通过)、还是更新有效期(认证通过)
         */
        Integer currentReviewStatus = member.getLicenseReviewStatus();
        if(LicenseAuthStatusEnum.TO_MANUAL_REVIEW.eq(currentReviewStatus)) {
            log.error("提交提交驾照信息，当前驾照认证状态为待认证，请勿重复提交, mid={}", input.getMid());
            throw new AuthenticationException(StatusCode.SUBMIT_FAILURE);
        }
        if(LicenseAuthStatusEnum.AUTHENTICATED.eq(currentReviewStatus)) {
            log.info("提交提交驾照信息，当前身份证状态为已认证，更新证件有效期, mid={}", input.getMid());
            return updateCertExpiredDate(input, member);
        }
        boolean firstSubmit = LicenseAuthStatusEnum.UN_SUBMIT.eq(currentReviewStatus);
        submitForReview(input, member, firstSubmit);

        HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, "提交驾照信息结束，appKey=" + input.getAppKey()
                + ", input=" + JSON.toJSONString(input), input.getMid(), true);
        log.info("提交驾照信息-提交开始：mid={}, appKey={}, input={}", input.getMid(), input.getAppKey(), JSON.toJSONString(input));

        try {
            //90天有效期
            JedisUtil.set("SubmitLicence_Operation_Type_"+member.getAuthId(),input.getOperationModel(),60*60*24*90);
        } catch (Exception e) {
        }

        return new SubmitDriverLicenseRespDto(AuthStateCodeEnum.SUCCESS);
    }

    public SubmitDriverLicenseRespDto submitForReview(SubmitDriverLicenseInput input, MembershipBasicInfo member,
                                                      boolean firstSubmit) throws AuthenticationException {
        String content = "提交驾照";
        OperatorDto operator = OperatorDto.buildUserOperator(input.getMid() + "@" + input.getAppKey());
        if(!firstSubmit) {
            content = "重新提交驾照";
        }
        //判断之前是否 根据驾照审核认证状态决定是否需要熟练
        log.info("提交驾照： content={}, firstSubmit={}, mid={}", content, firstSubmit, input.getMid());
        PostCertSubmitEnum nextOperationType = PostCertSubmitEnum.TO_MANUAL_REVIEW;

        MembershipInfoWithBLOBs updateMember = new MembershipInfoWithBLOBs();
        BeanUtils.copyProperties(input, updateMember);
        // appKey 不能更新
        updateMember.setAppKey(null);
        //TODO 确认仅更新外籍人员姓名，还是全部都更新。
        updateMember.setDriverCode(input.getDriverCode());
        updateMember.setName(input.getName());
        updateMember.setFileNo(input.getFileNo());
        updateMember.setObtainDriverTimer(input.getFirstObtainTime());
        updateMember.setLicenseSubmitAppkey(input.getAppKey());
        updateMember.setLicenseExpirationTime(input.getExpirationDate());
        updateMember.setDrivingLicenseImgUrl(input.getDriverLicenseImgUrl());
        updateMember.setFileNoImgUrl(input.getReverseDriverLicenseImgUrl());
        if(input.getExpireType().equals(2)) {
            updateMember.setLicenseExpirationTime(BussinessConstants.MAX_CERT_EXPIRE_TIME);
        } else {
            updateMember.setLicenseExpirationTime(input.getExpirationDate());
        }
        updateMember.setLicenseImgType(input.getDriverLicenseType());
        updateMember.setDrivingLicenseType(input.getLicenseType());
        /**
         * 提交完驾照资料，立即提交审核。 变更，提交驾照时，不变更认证状态
         */
        //updateRow.setAuthenticationStatus(1);
        updateMember.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
        updateMember.setUpdatedUser(input.getMid());
        updateMember.setPkId(member.getPkId());
        // 特殊渠道 驾照提交直接认证成功
        if (BussinessConstants.APPKEY_QL.equals(input.getAppKey())) {
            content += "，认证通过";
            updateMember.setLicenseReviewStatus(LicenseAuthStatusEnum.AUTHENTICATED.getValue());
        }else{
            content += "，待人工审核";
            updateMember.setLicenseReviewStatus(LicenseAuthStatusEnum.TO_MANUAL_REVIEW.getValue());
        }
        int updateResult = membershipInfoMapper.updateByPrimaryKeySelective(updateMember);
        if (updateResult < 1) {
            log.error("提交驾照, content={}, 保存驾照失败，mid={}", content, input.getMid());
            HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, StatusCode.SUBMIT_FAILURE.getMsg(), input.getMid(), false);
            throw new AuthenticationException(StatusCode.SUBMIT_FAILURE);
        }
        // 履约证件号码被原⼿机号账号占⽤,清除被占用人信息，并设置为未认证
        if (StringUtils.equals(BussinessConstants.APPKEY_OFC, input.getAppKey())) {
            if (StringUtils.isNotEmpty(input.getOccupiedMid())) {
                clearIDCardAndLicenseByOccupied(input.getOccupiedMid(), operator);
            }
        }
        /*
         * 3. 保存操作日志
         * 冗余记录MemOperateTypeEnum.OCR日志，用于兼容旧数据
         */
        if(input.getOperator() == null || StringUtils.isBlank(input.getOperator().getOperatorName())) {
            input.setOperator(OperatorDto.buildUserOperator("USER"));
        }
        memberOptLogService.saveCertOptLog(content, member, MemOperateTypeEnum.OCR, input.getAppKey(), null, true, operator);
        memberOptLogService.saveCertOptLog(content, member, MemOperateTypeEnum.LICENSE_SUBMIT_DATA, input.getAppKey(), null, true, operator);
        return new SubmitDriverLicenseRespDto(AuthStateCodeEnum.SUCCESS);
    }

    public SubmitDriverLicenseRespDto updateCertExpiredDate(SubmitDriverLicenseInput input, MembershipBasicInfo member) throws AuthenticationException {
        if(StringUtils.isBlank(input.getDriverLicenseImgUrl())
                || input.getDriverLicenseType().equals(2) && StringUtils.isBlank(input.getReverseDriverLicenseImgUrl())
                || null == input.getExpireType()
                || (input.getExpireType().equals(1) && StringUtils.isBlank(input.getExpirationDate()))) {
            throw BusinessRuntimeException.PARAM_EXEPTION;
        }
        log.info("提交驾照： 更新驾照有效期，mid={}, input={}", input.getMid(), JSON.toJSONString(input));

        /**
         * 1.判断有效期是否在原有效期时间之后
         */
        if (input.getExpireType().equals(1) && StringUtils.isNotBlank(member.getLicenseExpirationTime())) {
            DateTimeFormatter dfm = DateTimeFormatter.ofPattern(ComUtil.DATE_TYPE5);
            LocalDate firstExpirationTime = LocalDate.parse(member.getLicenseExpirationTime(), dfm);
            LocalDate expirationDate = LocalDate.parse(input.getExpirationDate(), dfm);
            if (!expirationDate.isAfter(firstExpirationTime)) {
                HidLog.membership(LogPoint.UPDATE_DRIVER_EXPIRED_TIME, StatusCode.LICENSE_EXPIRATION_TIME_ERROR.getMsg(), input.getMid(), false);
                return new SubmitDriverLicenseRespDto(AuthStateCodeEnum.LICENSE_EARLY_EXPIRE_DATE);
            }
        }
        //驾照走人工审核，更新驾照有效期时是否不再需要校验驾照和身份证未并更
        if(StringUtils.isNotBlank(input.getDriverCode()) && !StringUtils.equalsIgnoreCase(input.getDriverCode(), member.getDriverCode())) {
            log.error("提交驾照： 更新驾照有效期，驾照编号&姓名不可更改， mid={}", input.getMid());
            return new SubmitDriverLicenseRespDto(AuthStateCodeEnum.LICENSE_ID_NO_NOT_MATCH, "更新的驾照信息非认证用户");
        }
        if(StringUtils.isNotBlank(input.getName()) && !StringUtils.equalsIgnoreCase(input.getName(), member.getName())) {
            log.error("提交驾照： 更新驾照有效期，驾照编号&姓名不可更改， mid={}", input.getMid());
            return new SubmitDriverLicenseRespDto(AuthStateCodeEnum.LICENSE_ID_NAME_NOT_MATCH, "更新的驾照信息非认证用户");
        }
        /**
         * 2. 数据提交及状态变更
         */
        String content = "更新驾照有效期成功";
        MembershipInfoWithBLOBs updateMember = new MembershipInfoWithBLOBs();
        //TODO 确认仅更新外籍人员姓名，还是全部都更新。
        updateMember.setDrivingLicenseImgUrl(input.getDriverLicenseImgUrl());
        updateMember.setFileNoImgUrl(input.getReverseDriverLicenseImgUrl());
        updateMember.setLicenseImgType(input.getDriverLicenseType());
        updateMember.setLicenseExpirationTime(input.getExpirationDate());
        if(input.getExpireType().equals(2)) {
            updateMember.setLicenseExpirationTime(BussinessConstants.MAX_CERT_EXPIRE_TIME);
        }
        //updateMember.setLicenseImgType(input.getDriverLicenseType());
        //updateMember.setDrivingLicenseType(input.getLicenseType());
        /**
         * 更新驾照信息，提交人工审核
         */
        //updateRow.setAuthenticationStatus(1);
        updateMember.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
        updateMember.setUpdatedUser(input.getMid());
        updateMember.setPkId(member.getPkId());
        // 特殊渠道 驾照提交直接认证成功
        if (BussinessConstants.APPKEY_QL.equals(input.getAppKey())) {
            content += "，认证通过";
            updateMember.setLicenseReviewStatus(LicenseAuthStatusEnum.AUTHENTICATED.getValue());
        }else{
            content += "，提交人工审核";
            updateMember.setLicenseReviewStatus(LicenseAuthStatusEnum.TO_MANUAL_REVIEW.getValue());
        }
        int updateResult = membershipInfoMapper.updateByPrimaryKeySelective(updateMember);
        if (updateResult < 1) {
            log.error("提交驾照, 更新驾照有效期，保存驾照失败，mid={}", input.getMid());
            HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, StatusCode.SUBMIT_FAILURE.getMsg(), input.getMid(), false);
            throw new AuthenticationException(StatusCode.SUBMIT_FAILURE);
        }

        /**
         * 3. 保存操作日志
         */
        if(input.getOperator() == null || StringUtils.isBlank(input.getOperator().getOperatorName())) {
            input.setOperator(OperatorDto.buildUserOperator("USER"));
        }
        memberOptLogService.saveCertOptLog(content, member, MemOperateTypeEnum.LICENSE_SUBMIT_DATA, input.getAppKey(), null, true, input.getOperator());
        HidLog.membership(LogPoint.UPDATE_DRIVER_EXPIRED_TIME, content, member.getMid(), true);
        return new SubmitDriverLicenseRespDto(AuthStateCodeEnum.SUCCESS);
    }


    void clearIDCardAndLicenseByOccupied(String occupiedMid, OperatorDto operator) {
        Date now = new Date();
        log.info("清除驾照认证信息开始,mid:{}.", occupiedMid);
        MembershipBasicInfo occupiedMembershipInfo = membershipInfoMapper.getUserBasicInfoByMid(occupiedMid);
        if (occupiedMembershipInfo != null) {
            log.info("存在需要清除的驾驶证认证, mid:{}.", occupiedMembershipInfo.getMid());
            //membershipInfoMapper 更新
            MembershipInfoWithBLOBs occupiedUpdateMember = new MembershipInfoWithBLOBs();
            //审核状态
            occupiedUpdateMember.setReviewStatus((short) -1);
            //主键
            occupiedUpdateMember.setPkId(occupiedMembershipInfo.getPkId());
            //更新时间
            occupiedUpdateMember.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
            //更新操作人
            occupiedUpdateMember.setUpdatedUser(operator.getOperatorName());
            //驾照认证状态 1:未认证、2:待认证、3:已认证、4:认证失败、 5:即将过期（剩余90天）、6:已过期
            occupiedUpdateMember.setLicenseReviewStatus(LicenseAuthStatusEnum.UN_SUBMIT.getValue());
            occupiedUpdateMember.setReviewUser(operator.getOperatorName());
            // 这里给 ReviewItems 设置为 0000000
            occupiedUpdateMember.setReviewItems(BussinessConstants.IDCARD_REVIEW_ITEMS_INIT);
            // 清除驾照数据
            occupiedUpdateMember.setDriverCode(StringUtils.EMPTY);
            occupiedUpdateMember.setObtainDriverTimer(StringUtils.EMPTY);
            occupiedUpdateMember.setLicenseExpirationTime(StringUtils.EMPTY);
            occupiedUpdateMember.setFileNoImgUrl(StringUtils.EMPTY);
            occupiedUpdateMember.setDrivingLicenseImgUrl(StringUtils.EMPTY);
            occupiedUpdateMember.setFileNo(StringUtils.EMPTY);
            membershipInfoMapper.clearLicenseByPrimaryKeySelective(occupiedUpdateMember);

            // 记录操作日志
            ComUtil.insertOperatorLog(MemOperateTypeEnum.LICENSE_MEMBER_REAUDIT.getOperate(), occupiedMembershipInfo.getAuthId(),
                    occupiedMembershipInfo.getMobilePhone(), operator.getOperatorName(), userOperatorLogMapper);

            // 操作日志
            UserOperationLogInput operationLog = new UserOperationLogInput();
            operationLog.setUserId(occupiedMembershipInfo.getPkId());
            operationLog.setAuthId(occupiedMembershipInfo.getAuthId());
            operationLog.setMembershipType((int) occupiedMembershipInfo.getMembershipType());
            operationLog.setOperationType(MemOperateTypeEnum.LICENSE_MEMBER_REAUDIT.getCode());
            operationLog.setOperationContent("驾照证占用清除驾照信息");
            operationLog.setRefKey1("履约");
            operationLog.setOperatorId(operator.getOperatorId());
            operationLog.setOperator(operator.getOperatorName());
            operationLog.setOperationTime(now);
            memberShipService.saveUserOperationLog(operationLog);
        }
        log.info("清除驾照认证信息结束,mid:{}.", occupiedMid);
    }
}
