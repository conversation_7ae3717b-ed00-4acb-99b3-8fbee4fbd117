package com.extracme.evcard.membership.mq;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.aliyun.openservices.ons.api.bean.ProducerBean;
import com.extracme.evcard.activity.dto.RewardDetailDto;
import com.extracme.evcard.activity.dubboService.IRegisterRewardService;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.dao.MemberRelationMapper;
import com.extracme.evcard.membership.core.dao.UserOrderReduceActivityRecordMapper;
import com.extracme.evcard.membership.core.dto.*;
import com.extracme.evcard.membership.core.manager.MemberDocumentManager;
import com.extracme.evcard.membership.core.model.UserDeviceInfo;
import com.extracme.evcard.membership.core.model.UserOrderReduceActivityRecord;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.membership.core.service.auth.MemberRelationService;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.dao.MmpCreditEventRecordMapper;
import com.extracme.evcard.membership.credit.dao.MmpCreditEventTypeMapper;
import com.extracme.evcard.membership.credit.dao.MmpUserTagMapper;
import com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs;
import com.extracme.evcard.membership.credit.model.MmpCreditEventRecord;
import com.extracme.evcard.membership.credit.model.MmpCreditEventType;
import com.extracme.evcard.membership.credit.model.MmpUserTag;
import com.extracme.evcard.membership.inner.service.UserDeviceInfoService;
import com.extracme.evcard.mq.bean.EventEnum;
import com.extracme.evcard.mq.bean.event.*;
import com.extracme.evcard.protobuf.ProtobufUtil;
import com.extracme.evcard.redis.JedisUtil;
import com.extracme.evcard.rpc.messagepush.service.IMessagepushServ;
import com.extracme.evcard.rpc.order.dto.OrderInfoPriceDto;
import com.extracme.evcard.rpc.order.dto.OrderPriceDetailDTO;
import com.extracme.evcard.rpc.order.dto.PriceItemDto;
import com.extracme.evcard.rpc.order.service.IOrderPriceService;
import com.extracme.evcard.rpc.util.BeanCopyUtils;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.OfferSuixiangCardPreCdkInput;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.OfferSuixiangCardPreCdkOutput;
import com.extracme.evcard.rpc.vipcard.service.ISuixiangCardCdkService;
import com.extracme.evcard.saic.service.ISaicMemberService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * \* Created with IntelliJ IDEA.
 * \* User: Elin
 * \* Date: 2017/12/5
 * \* Time: 14:15
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 会员消费信用积分
 *                 会员审核事件消费-消息通知
 * \
 */
@Slf4j
@Service
public class RawListener implements MessageListener {
    @Resource
    private ISaicMemberService saicMemberService;

    @Resource
    private IMemberShipService memberShipService;

    @Resource
    IMessagepushServ messagepushServ;

    @Resource
    MessageListener memberRawCreditsListener;

    @Autowired
    private MembershipInfoMapper membershipInfoMapper;

    @Autowired
    private MmpUserTagMapper mmpUserTagMapper;

    @Autowired
    private MmpCreditEventRecordMapper mmpCreditEventRecordMapper;

    @Autowired
    private MmpCreditEventTypeMapper mmpCreditEventTypeMapper;

    @Autowired
    private IOrderPriceService orderPriceService;

    @Autowired
    private UserOrderReduceActivityRecordMapper userOrderReduceActivityRecordMapper;

    @Autowired
    private MemberDocumentManager memberDocumentManager;

    @Autowired
    private ISuixiangCardCdkService suixiangCardCdkService;

    @Resource(name = "producer")
    private ProducerBean producer;

    @Value("${ons.raw.topic}")
    private String evcardRawDataTopic;

    @Value("${auditNoPassSmsTemplateId}")
    private String auditNoPassSmsTemplateId;

    @Value("${smsMode}")
    private String smsMode;

    // 会员审核-身份证人工审核 不通过短信模板id
    @Value("${idCardAuditNoPassSmsTemplateId:244}")
    private String idCardAuditNoPassSmsTemplateId;

    // 会员审核-身份证人工审核 (非首次)通过短信模板id
    @Value("${idCardAuditPassSmsTemplateId:241}")
    private String idCardAuditPassSmsTemplateId;

    // 会员审核-身份证人工审 （首次）通过短信模板id
    @Value("${idCardAuditFirstPassSmsTemplateId:240}")
    private String idCardAuditFirstPassSmsTemplateId;


    @Resource
    private IRegisterRewardService registerRewardService;

    @Resource
    private MemberRelationMapper memberRelationMapper;

    @Resource
    private MemberRelationService memberRelationService;

    @Resource
    private UserDeviceInfoService userDeviceInfoService;

    // 用新的tag ,旧的EventEnum.MEMBER_AUDIT去掉
    String[] memberListenerEvent = new String[]{
            EventEnum.MEMBER_AUDIT_IDCARD.getTag(),
            //提交身份证人工审核
            EventEnum.MEMBER_TO_AUDIT_IDCARD.getTag(),
            // 用户登录
            EventEnum.MEMBER_LOGIN.getTag(),
            // 非注册用户发券
            EventEnum.MEMBER_REGISTER_COUPON.getTag(),
    };
    
    String[] gradeListenerEvent = new String[]{
    		EventEnum.ORDER_PAY_NEW.getTag()
    };

    String[] creditListenerEvent = new String[]{
    		EventEnum.ACCIDENT.getTag(),
    		EventEnum.ILLEGAL.getTag(),
    		EventEnum.ILLEGAL_NOT_HANDLED_IN_TIME.getTag(),
    		EventEnum.ORDER_PAY_NEW.getTag(),
    		EventEnum.MEMBER_REGISTER.getTag(),
    		EventEnum.MEMBER_UNREGISTER.getTag()
    };


    /**
     * 会员信用系统：积分/权益等
     */
    String[] memberCreditsListenerEvent = new String[]{
            EventEnum.ORDER_PAY_NEW.getTag(),
            EventEnum.CHARGE_DEPOSIT.getTag(),
            EventEnum.MEMBER_RECHARGE_ECOIN.getTag(),
            EventEnum.ORDER_SUBMIT_ACCESS.getTag(),
            EventEnum.REPORT_VEHICLE_CONDITION.getTag(),
            EventEnum.MEMBER_DAILY_SIGN.getTag(),
            EventEnum.MEMBER_ORDER_ACHIEVEMENT.getTag(),
            EventEnum.ORDER_SHARE_SUCCESS.getTag(),
            EventEnum.MEMBER_INVITATION_COMPLETE.getTag(),
            //身份审核通过
            EventEnum.MEMBER_AUDIT.getTag(),
            //提交身份证人工审核
            EventEnum.MEMBER_TO_AUDIT_IDCARD.getTag(),
    };

    String[] instantReduceActivityListenerEvent = new String[]{
            EventEnum.ORDER_PAY_NEW.getTag()
    };

    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        log.debug("消费: " + message.getMsgID() + ", 标签: " + message.getTag());
        try {
            //消费还车事件，更新用户首单信息，仅C/D订单认为是首单
            if(StringUtils.equals(message.getTag(), EventEnum.RETURN_VEHICLE.getTag())) {
                returnVehicleFirstOrder(message.getBody(), consumeContext);
            }
            if(ArrayUtils.contains(memberListenerEvent, message.getTag())) {
                this.memberListener(message, consumeContext);
            }
            if(ArrayUtils.contains(creditListenerEvent, message.getTag())) {
            	this.creditListener(message, consumeContext);
            }
            if(ArrayUtils.contains(gradeListenerEvent, message.getTag())) {
            	this.gradeListener(message, consumeContext);
            }
            if(ArrayUtils.contains(instantReduceActivityListenerEvent, message.getTag())) {
            	this.instantReduceActivityListener(message, consumeContext);
            }
            if(ArrayUtils.contains(memberCreditsListenerEvent, message.getTag())) {
                //不重试
                memberRawCreditsListener.consume(message, consumeContext);
            }
            return Action.CommitMessage;
        } catch (Exception e) {
            //消费失败
        	log.error("消费" + message.getMsgID() + "," + message.getTag() + "失败...", e);
            return Action.ReconsumeLater;
        }
    }

    public void memberListener(Message message, ConsumeContext consumeContext) {
        log.debug("消费: " + message.getMsgID() + ", 标签: " + message.getTag());
        if (message.getTag().equals(EventEnum.MEMBER_AUDIT_IDCARD.getTag())) {
            //this.memberAuditEvent(message.getBody());
            this.memberNewAuditEvent(message.getBody());
        } else if (message.getTag().equals(EventEnum.MEMBER_TO_AUDIT_IDCARD.getTag())) {
            //this.memberAuditEvent(message.getBody());
            this.memberIdentityToReviewEvent(message.getBody());
        }else if (message.getTag().equals(EventEnum.MEMBER_LOGIN.getTag())) {
            this.memberLoginEvent(message.getBody());
        } else if (message.getTag().equals(EventEnum.MEMBER_REGISTER_COUPON.getTag())) {
            this.registerAndOfferCoupon(message.getBody());
        }
    }
    
    public void gradeListener(Message message, ConsumeContext consumeContext) {
        log.debug("消费: " + message.getMsgID() + ", 标签: " + message.getTag());
        if (message.getTag().equals(EventEnum.ORDER_PAY_NEW.getTag())) {//订单支付事件
            this.payEventGrade(message.getBody());
        }
    }
    
    public void creditListener(Message message, ConsumeContext consumeContext) {
        log.debug("消费: " + message.getMsgID() + ", 标签: " + message.getTag());
        if (message.getTag().equals(EventEnum.ACCIDENT.getTag())) {//事故
            accidentEvent(message.getBody());
        } else if (message.getTag().equals(EventEnum.ILLEGAL.getTag())) {//违章
            illegalEvent(message.getBody());
        } else if (message.getTag().equals(EventEnum.ILLEGAL_NOT_HANDLED_IN_TIME.getTag())) {//违章未及时处理
            illegalNotHandleEvent(message.getBody());
        } else if (message.getTag().equals(EventEnum.ORDER_PAY_NEW.getTag())) {//订单支付事件
            payEvent(message.getBody());
        }
        else if (message.getTag().equals(EventEnum.MEMBER_REGISTER.getTag())) {
            //会员注册事件
            memberRegisterEvent(message.getBody());
        }else if (message.getTag().equals(EventEnum.MEMBER_UNREGISTER.getTag())) {
            //会员注销事件
            memberUnRegisterEvent(message.getBody());
        }

    }

    public void instantReduceActivityListener(Message message, ConsumeContext consumeContext) {
        log.debug("消费: " + message.getMsgID() + ", 标签: " + message.getTag());
        if (message.getTag().equals(EventEnum.ORDER_PAY_NEW.getTag())) {//订单支付事件
            this.payEventInstantReduce(message.getBody());
        }
    }

    /**
     * 会员登录事件
     *
     * @param registerBody
     */
    public void memberLoginEvent(byte[] registerBody) {
        MemberLogin memberLogin = new MemberLogin();
        ProtobufUtil.deserializeProtobuf(registerBody, memberLogin);
        MembershipInfoWithBLOBs membershipInfo = membershipInfoMapper.selectByAuthId(memberLogin.getAuthId(), 0);
        // 保存设备登录信息
        String deviceCode = memberLogin.getDeviceCode();
        if (StringUtils.isNotBlank(deviceCode)) {
            try {
                if (membershipInfo != null) {
                    UserDeviceInfo userDeviceInfo = new UserDeviceInfo();
                    userDeviceInfo.setMid(membershipInfo.getMid());
                    userDeviceInfo.setDeviceCode(memberLogin.getDeviceCode());
                    userDeviceInfo.setDeviceCodeType(memberLogin.getDeviceCodeType());
                    int appType = memberLogin.getAppType();
                    userDeviceInfo.setOsType(appType == 1 ? 1:2);
                    userDeviceInfo.setType(1);
                    userDeviceInfoService.bindUserDeviceInfo(userDeviceInfo);
                }
            } catch (Exception e) {
                log.error("memberLoginEvent，绑定设备号异常，inputDto={}", JSON.toJSONString(memberLogin),e);
            }

        }
        // 虚构转换为会员
        if (membershipInfo != null && membershipInfo.getIsFictional() == 1) {
            MembershipInfoWithBLOBs membershipInfoWithBLOBs = new MembershipInfoWithBLOBs();
            membershipInfoWithBLOBs.setPkId(membershipInfo.getPkId());
            membershipInfoWithBLOBs.setIsFictional(2);
            membershipInfoMapper.updateFictional(membershipInfoWithBLOBs);
        }
    }

    /**
     * 会员审核事件消费
     *
     * @param body
     */
    @Deprecated
    public void memberAuditEvent(byte[] body) {
        MemberAudit audit = new MemberAudit();
        ProtobufUtil.deserializeProtobuf(body, audit);
        String authId = audit.getAuthId();
        String mobilePhone = audit.getMobilePhone();
        log.warn("会员审核事件消费memberAuditEvent: authId:" + authId + ", mobilePhone: " + mobilePhone);
        //获取会员渠道
        String appKey = membershipInfoMapper.queryAppKey(authId);
        if(StringUtils.equals("1", audit.getReviewStatus())) {
            reviewSuccessNotify(audit, appKey);
            //享道：同步用户认证相关信息
            saicMemberReviewNotify(audit);
        }else if(StringUtils.equals("2", audit.getReviewStatus())) {
            reviewFailedNotify(audit, appKey);
        }
    }

    @Deprecated
    public void saicMemberReviewNotify(MemberAudit audit){
        String authId = audit.getAuthId();
        try {
            if (saicMemberService.beginSaic()) {
                log.info("享道：会员审核通过，同步用户认证信息， authId={}", authId);
                MembershipBasicInfo membership = memberShipService.getUserBasicInfo(audit.getAuthId(), (short)0);
                String faceImg = membership.getFaceRecognitionImgUrl();
                saicMemberService.updateUserAuth(membership, faceImg);
            }
        }catch (Exception ex) {
            log.error("享道：会员审核通过，同步用户认证信息失败, authId=" + authId, ex);
        }
    }

    @Value("${licenseFirstAuthTimeLimit:2022-09-16 22:00:00}")
    private String licenseFirstAuthTimeLimit;

    /**
     * 判断 用户的首单奖励
     *
     * @param authId
     * @param mid
     * @return
     */
    public BigDecimal getFirstAuditRewards(String authId, String mid) {
        BigDecimal totalAmount = BigDecimal.ZERO;
        try {
            // 校验此次审核通过是否需要发放首单奖励
            RewardDetailDto dto = registerRewardService.checkUserRewards(authId);
            if (dto != null && dto.getTotalCouponValue() != null) {
                // 该账号 未查询到，就可以 发放奖励
                int count = membershipInfoMapper.checkUserHavaAuth(authId, licenseFirstAuthTimeLimit);
                if (count == 0) {
                    if (isOldMemberByCreateTime(authId) && memberDocumentManager.hasOrder(mid, authId)) {
                        log.info("用户有过历史订单，不再发新人券！mid[{}]authId[{}]", mid, authId);
                    } else {
                        totalAmount = dto.getTotalCouponValue();
                    }
                } else {
                    log.info("用户<{}>,已经领过首次奖励", authId);
                }
            }
        } catch (Exception ex) {
            log.warn("检查是否满足首单奖励条件失败， authId=" + authId, ex);
        }
        return totalAmount;
    }

    @Value("${first.reward.todb.time:2021-06-21 10:41:52}")
    private String firstRewardTime;
    @Value("${first.reward.isold.enable:1}")
    private String firstRewardIsOldEnable;

    /**
     * 通过奖励表第一笔时间 和 用户注册时间比较 判断用户是否是老用户
     * 老用户：通过是否下过单，来判断是否 发过新手奖励
     * 新用户 ： 通过 奖励表，来判断是否发过新手奖励
     * @param authId
     * @return
     */
    public boolean isOldMemberByCreateTime(String authId) {
        boolean result = true;
        if ("1".equals(firstRewardIsOldEnable)) {
            try {
                MembershipInfoWithBLOBs membership = membershipInfoMapper.selectByAuthId(authId,0);
                if (membership != null) {
                    String regTime = membership.getRegTime();
                    Date regDate = ComUtil.getDateFromStr(regTime, ComUtil.DATE_TYPE4);
                    Date firstRewardDate = ComUtil.getDateFromStr(firstRewardTime, ComUtil.DATE_TYPE1);
                    if (regDate.before(firstRewardDate)) {
                        result = true;
                    }else{
                        result = false;
                    }
                }
            } catch (Exception e) {
                log.error("isOldMemberByCreateTime异常，authId=[{}]",authId,e);
            }
        }
        log.info("authId=[{}],isOldMemberByCreateTime,result=[{}]",authId,result);
        return result;
    }

    @Deprecated
    public void reviewSuccessNotify(MemberAudit audit, String appKey) {

        /**
         * 2021.6 联动首单活动
         */
        BigDecimal totalAmount = getFirstAuditRewards(audit.getAuthId(), null);
        /*BigDecimal totalAmount = BigDecimal.ZERO;
        try {
            // 校验此次审核通过是否需要发放首单奖励
            RewardDetailDto dto = registerRewardService.checkUserRewards(audit.getAuthId());
            if (dto != null && dto.getTotalCouponValue() != null) {
                totalAmount = dto.getTotalCouponValue();
            }
        }catch (Exception ex) {
            log.warn("审核通过事件：检查是否满足首单奖励条件失败， authId=" + audit.getAuthId(), ex);
        }*/

        String authId = audit.getAuthId();
        String mobilePhone = audit.getMobilePhone();
        String optUser = audit.getOptUser();
        String title = "会员审核通过";
        //向审核通过的新老会员发送短信
        /*if (1 == audit.getNewUser()) {
            msgId = REVIEW_SUCCESS_MSGID;
        }else if (0 == audit.getNewUser()){
            msgId = RE_REVIEW_SUCCESS_MSGID;
        }*/
        //统一更换为新的短信模板， 只区分有无奖励，不区分首次审核通过与重新审核。
        boolean hasReward = (BigDecimal.ZERO.compareTo(totalAmount) < 0);
        String msgId = hasReward ? "117" : "118";
        log.info("审核通过事件：审核通过，新手奖励={}， authId={}", hasReward, audit.getAuthId());
        //通用、别克代步车、松江校园不发短信
        if (!ComUtil.checkIsSgmAppKey(appKey)) {
            //1. 短信发送
            Map<String, String> param = new HashMap<>(1);
            param.put("couponValue", String.valueOf(totalAmount));
            notifyReviewResultBySms(authId, mobilePhone, msgId, param, title, optUser);

            //2. app通知推送
            // pushReviewAppMsg(authId, title, REVIEW_SUCCESS_NOTIFY_MSGKEY, reviewRemark);
        }
        //3. 推送审核奖励待发放事件
        try{
            if(hasReward) {
                byte[] messageBody = ProtobufUtil.serializeProtobuf(audit);
                String messageKey = "membership#" + UUID.randomUUID().toString().replace("-", "");
                Message msg = new Message(evcardRawDataTopic, EventEnum.MEMBER_AUDIT_REWARD.getTag(), messageBody);
                msg.setKey(messageKey);
                producer.send(msg);
                log.info("审核通过事件：推送审核通过奖励成功，authId={}, memberAudit={}", audit.getAuthId(), audit);
            }
        } catch (Exception e) {
            log.error("审核通过事件：推送审核通过奖励失败，authId={}, memberAudit={}", audit.getAuthId(), audit);
        }
    }

    public void reviewFailedNotify(MemberAudit audit, String appKey) {
        String authId = audit.getAuthId();
        String mobilePhone = audit.getMobilePhone();
        String reviewRemark = audit.getReviewRemark();
        String optUser = audit.getOptUser();
        String title = "会员审核不通过";
        //向审核通过不通过的会员发送审核不通过短信, 新老用户均发
        if (ComUtil.checkIsSgmAppKey(appKey)) {
            return;
        }
        //1. 短信发送
        Map<String, String> param = new HashMap<>(1);
        param.put("reviewRemark", reviewRemark);
        notifyReviewResultBySms(authId, mobilePhone, auditNoPassSmsTemplateId, param, title, optUser);
        //2. app通知推送
        // pushReviewAppMsg(authId, title, REVIEW_FAILED_MSGKEY, reviewRemark);
    }

    public void notifyReviewResultBySms(String authId, String mobilePhone, String smsId,Map<String, String> param, String title, String optUser){
        if (StringUtils.isBlank(mobilePhone)) {
            return;
        }
        try {
            if (StringUtils.isNotBlank(smsId) && StringUtils.equals(smsMode, "1")) {
                int templateId = Integer.valueOf(smsId).intValue();
                com.extracme.evcard.rpc.dto.BaseResponse baseResponse = messagepushServ.asyncSendSMSTemplate(mobilePhone,templateId, param, optUser);
                if (baseResponse.getCode() == 0) {
                    log.info(title + "-短信成功通知，authId={}", authId);
                } else {
                    log.error(title + "-短信通知失败，authId={}, 原因={}", authId, baseResponse.getMessage());
                }
            }
        }catch (Exception ex) {
            log.error(title + "-短信通知失败，authId=" + authId, ex);
        }
    }

//    public void pushReviewAppMsg(String authId, String title, String messageKey, String reviewRemark){
//        try {
//            String msgfmt = SystemPropertyUtils.getString(messageKey);
//            String pushMsg = String.formatm(sgfmt, reviewRemark);
//            messagepushServ.push(authId, 0, title, pushMsg + "通知", "mmpbership");
//        } catch (Exception e) {
//            log.error("消息推送: " + title + ", auth=" + authId, e);
//        }
//    }

    /**
     * 会员注销事件
     *
     * @param unRegisterBody
     */
    @Transactional(rollbackFor = Exception.class)
    public void memberUnRegisterEvent(byte[] unRegisterBody) {
        MemberUnRegister memberRegister = new MemberUnRegister();
        ProtobufUtil.deserializeProtobuf(unRegisterBody, memberRegister);
        String mid = memberRegister.getMid();
        log.info("接收到会员注销mq，开始处理，mid={}",mid);
        try {
            //
            MembershipBasicInfo membershipBasicInfo = membershipInfoMapper.getUserBasicInfoByMid(mid);
            if (membershipBasicInfo != null && membershipBasicInfo.getMembershipType() == 0) {
                //删除 渠道会员 和 外部会员关联关系
                if (memberRelationService.deleteMemberRelations(membershipBasicInfo.getPkId()) > 0) {
                    log.info("注销外部会员，删除渠道会员和外部会员关联关系成功,membershipBasicInfo={}",JSON.toJSONString(membershipBasicInfo));
                }
            }
        } catch (Exception e) {
            log.error("处理注销外部会员，异常,mid={}",mid,e);
            throw e;
        }


    }

    /**
     * 会员注册事件
     *
     * @param registerBody
     */
    @Transactional(rollbackFor = Exception.class)
    public void memberRegisterEvent(byte[] registerBody) {
        MemberRegister memberRegister = new MemberRegister();
        ProtobufUtil.deserializeProtobuf(registerBody, memberRegister);
        Map<String, String> map = getRedisEventType(13L);
        MmpCreditEventRecord mmpCreditEventRecord = new MmpCreditEventRecord();
        mmpCreditEventRecord.setAuthId(memberRegister.getAuthId());
        mmpCreditEventRecord.setEventName((String) map.get("eventName"));
        mmpCreditEventRecord.setEventSource("自动触发");
        mmpCreditEventRecord.setEventTypeId(Long.valueOf((String) map.get("id")));
        mmpCreditEventRecord.setEventNature((String) map.get("eventNature"));
        Integer amount = Integer.valueOf((String) map.get("amount"));
        mmpCreditEventRecord.setAmount(amount);
        mmpCreditEventRecord.setEventDesc((String) map.get("eventName"));
        mmpCreditEventRecord.setStatus(1);
        mmpCreditEventRecordMapper.insertSelective(mmpCreditEventRecord);
        MmpUserTag userTag = new MmpUserTag();
        userTag.setAuthId(memberRegister.getAuthId());
        userTag.setCreditAmount(Double.parseDouble(amount + ""));
        mmpUserTagMapper.saveUserTag(userTag);

        // 保存渠道和内部会员 关联关系
        memberRelationService.bindRelation(memberRegister.getMobilePhone(),2, null);


        try {
            OfferSuixiangCardPreCdkInput input = new OfferSuixiangCardPreCdkInput();
            MembershipBlobDTO membershipByAuthId = memberShipService.getMembershipByAuthId(memberRegister.getAuthId(), 0);
            input.setMid(membershipByAuthId.getMid());
            OfferSuixiangCardPreCdkOutput offerSuixiangCardPreCdkOutput = suixiangCardCdkService.offerSuixiangCardPreCdk(input);
            if (offerSuixiangCardPreCdkOutput.getBaseResponse().getCode() == 0) {
                log.info("会员注册成功，预发随享卡领取成功，mid={}",input.getMid());
            }
            else {
                log.error("会员注册成功，预发随享卡领取失败，mid={}",input.getMid());
            }
        } catch (Exception e) {
            log.error("会员注册成功，预发随享卡领取异常，authId={}",memberRegister.getAuthId(),e);
        }
    }


    /**
     * 违章未及时处理（违章>30天未处理）：-200
     *
     * @param illegalBody
     */
    @Transactional(rollbackFor = Exception.class)
    public void illegalNotHandleEvent(byte[] illegalBody) {
        Illegal illegal = new Illegal();
        ProtobufUtil.deserializeProtobuf(illegalBody, illegal);

        Map<String, String> map = getRedisEventType(6L);
        MmpCreditEventRecord mmpCreditEventRecord = new MmpCreditEventRecord();
        mmpCreditEventRecord.setAuthId(illegal.getAuthId());
        mmpCreditEventRecord.setOrderSeq(illegal.getOrderSeq());
        mmpCreditEventRecord.setEventName((String) map.get("eventName"));
        mmpCreditEventRecord.setEventSource("自动触发");
        mmpCreditEventRecord.setEventTypeId(Long.valueOf((String) map.get("id")));
        mmpCreditEventRecord.setEventNature((String) map.get("eventNature"));
        Integer amount = Integer.valueOf((String) map.get("amount"));
        mmpCreditEventRecord.setAmount(amount);
        mmpCreditEventRecord.setEventDesc(illegal.getOrderSeq() + map.get("eventName"));
        mmpCreditEventRecord.setStatus(1);
        mmpCreditEventRecordMapper.insertSelective(mmpCreditEventRecord);
        mmpUserTagMapper.updateCreditAmountByAuthId(illegal.getAuthId(), amount);
    }

    /**
     * 事故（依据责任比）
     *
     * @param accidentBody
     */
    @Transactional(rollbackFor = Exception.class)
    public void accidentEvent(byte[] accidentBody) {
        Accident accident = new Accident();
        ProtobufUtil.deserializeProtobuf(accidentBody, accident);
        Map<String, String> map = new HashMap<String, String>();
        if (accident.getBlame() == 0) {//无责任
            map = getRedisEventType(7L);
        } else if (accident.getBlame() == 1) {//次要责任
            map = getRedisEventType(8L);
        } else if (accident.getBlame() == 2) {//平分责任
            map = getRedisEventType(9L);
        } else if (accident.getBlame() == 3) {//主要责任
            map = getRedisEventType(10L);
        } else if (accident.getBlame() == 4) {//全责
            map = getRedisEventType(11L);
        }
        MmpCreditEventRecord mmpCreditEventRecord = new MmpCreditEventRecord();
        mmpCreditEventRecord.setAuthId(accident.getAuthId());
        mmpCreditEventRecord.setOrderSeq(accident.getOrderSeq());
        mmpCreditEventRecord.setEventName((String) map.get("eventName"));
        mmpCreditEventRecord.setEventSource("自动触发");
        mmpCreditEventRecord.setEventTypeId(Long.valueOf((String) map.get("id")));
        mmpCreditEventRecord.setEventNature((String) map.get("eventNature"));
        Integer amount = Integer.valueOf((String) map.get("amount"));
        if (accident.getBlame() == 0) {
            mmpCreditEventRecord.setAmount(-0);
        } else {
            mmpCreditEventRecord.setAmount(amount);
        }
        String content = map.get("eventName") + accident.getAccidentDesc();
        if (content.length() > 500) {
            content = content.substring(0, 500) + "...";
        }
        mmpCreditEventRecord.setEventDesc(content);
        mmpCreditEventRecord.setStatus(1);
        mmpCreditEventRecordMapper.insertSelective(mmpCreditEventRecord);
        mmpUserTagMapper.updateCreditAmountByAuthId(accident.getAuthId(), amount);
    }

    /**
     * 违章:-(罚款*1+违章分*20)
     *
     * @param illegalBody
     */
    @Transactional(rollbackFor = Exception.class)
    public void illegalEvent(byte[] illegalBody) {
        Illegal illegal = new Illegal();
        ProtobufUtil.deserializeProtobuf(illegalBody, illegal);
        if (StringUtils.isNotBlank(illegal.getAuthId())) {
            Map<String, String> map = getRedisEventType(5L);
            MmpCreditEventRecord mmpCreditEventRecord = new MmpCreditEventRecord();
            mmpCreditEventRecord.setAuthId(illegal.getAuthId());
            mmpCreditEventRecord.setOrderSeq(illegal.getOrderSeq());
            mmpCreditEventRecord.setEventName((String) map.get("eventName"));
            mmpCreditEventRecord.setEventSource("自动触发");
            mmpCreditEventRecord.setEventTypeId(Long.valueOf((String) map.get("id")));
            mmpCreditEventRecord.setEventNature((String) map.get("eventNature"));
            BigDecimal value = illegal.getIllegalAmount().multiply(new BigDecimal(1));
            Integer illegalAmount = -(value.intValue() + illegal.getIllegalPoint() * 20);
            mmpCreditEventRecord.setAmount(illegalAmount);
            if(illegal.getIllegalTime() == null) {
                illegal.setIllegalTime(new Date());
            }
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String illegalTime = format.format(illegal.getIllegalTime());
            String content = "您所驾驶的车牌号为：" + illegal.getVehicleNo() + "在" + illegal.getIllegalPlace() +
                    "违章，违章内容：" + illegal.getIllegalContent() + "，违章时间：" + illegalTime;
            if (content.length() > 500) {
                content = content.substring(0, 500) + "...";
            }
            mmpCreditEventRecord.setEventDesc(content);
            mmpCreditEventRecord.setStatus(1);
            mmpCreditEventRecordMapper.insertSelective(mmpCreditEventRecord);
            mmpUserTagMapper.updateCreditAmountByAuthId(illegal.getAuthId(), illegalAmount);
        }
    }

    /**
     * 及时支付 24小时内支付订单，+1，每年上线+50
     * 拖延支付 租车还车后>48小时未支付 -10
     *
     * @param payBody
     */
    @Transactional(rollbackFor = Exception.class)
    public void payEvent(byte[] payBody) {
        OrderPay orderPay = new OrderPay();
        ProtobufUtil.deserializeProtobuf(payBody, orderPay);
        OrderEffectivePay orderEffectivePay = new OrderEffectivePay();
        BeanCopyUtils.copyProperties(orderPay, orderEffectivePay);
        savePayCreditEvent(orderEffectivePay);
    }

    @Transactional(rollbackFor = Exception.class)
    public void savePayCreditEvent(OrderEffectivePay pay) {
        if (StringUtils.isNotBlank(pay.getAuthId())) {
            if (pay.getReturnTime() != null && pay.getPayTime() != null) {//已还车并支付
                //支付时间-还车时间 >48h 拖延支付 ，在24h内及时支付
                long returnTime = pay.getReturnTime().getTime();
                long payTime = pay.getPayTime().getTime();
                long subTime = (payTime - returnTime) / (60 * 60 * 1000);//小时
                if (subTime > 48) {
                    Map<String, String> map = getRedisEventType(4L);
                    MmpCreditEventRecord mmpCreditEventRecord = new MmpCreditEventRecord();
                    mmpCreditEventRecord.setAuthId(pay.getAuthId());
                    mmpCreditEventRecord.setOrderSeq(pay.getOrderSeq());
                    mmpCreditEventRecord.setEventName((String) map.get("eventName"));
                    mmpCreditEventRecord.setEventSource("自动触发");
                    mmpCreditEventRecord.setEventTypeId(Long.valueOf((String) map.get("id")));
                    mmpCreditEventRecord.setEventNature((String) map.get("eventNature"));
                    Integer amount = Integer.valueOf((String) map.get("amount"));
                    mmpCreditEventRecord.setAmount(amount);
                    mmpCreditEventRecord.setEventDesc(pay.getOrderSeq() + map.get("eventName"));
                    mmpCreditEventRecord.setStatus(1);
                    mmpCreditEventRecordMapper.insertSelective(mmpCreditEventRecord);
                    mmpUserTagMapper.updateCreditAmountByAuthId(pay.getAuthId(), amount);
                } else if (subTime <= 24) {
                    //查询是否超过上限
                    Integer payTotalAmount = mmpCreditEventRecordMapper.getThisYearInTimePayTotalAmount(pay.getAuthId(), 3L);
                    if (payTotalAmount == null) {
                        payTotalAmount = 0;
                    }
                    if (payTotalAmount < 50) {
                        Map<String, String> map = getRedisEventType(3L);
                        MmpCreditEventRecord mmpCreditEventRecord = new MmpCreditEventRecord();
                        mmpCreditEventRecord.setAuthId(pay.getAuthId());
                        mmpCreditEventRecord.setOrderSeq(pay.getOrderSeq());
                        mmpCreditEventRecord.setEventName((String) map.get("eventName"));
                        mmpCreditEventRecord.setEventSource("自动触发");
                        mmpCreditEventRecord.setEventTypeId(Long.valueOf((String) map.get("id")));
                        mmpCreditEventRecord.setEventNature((String) map.get("eventNature"));
                        Integer amount = Integer.valueOf((String) map.get("amount"));
                        mmpCreditEventRecord.setAmount(amount);
                        mmpCreditEventRecord.setEventDesc(pay.getOrderSeq() + map.get("eventName"));
                        mmpCreditEventRecord.setStatus(1);
                        mmpCreditEventRecordMapper.insertSelective(mmpCreditEventRecord);
                        mmpUserTagMapper.updateCreditAmountByAuthId(pay.getAuthId(), amount);
                    }
                }
            }
        }
    }

    /**
     * 从redis中获取事件类型对象
     *
     * @param typeId
     * @return
     */
    private Map<String, String> getRedisEventType(long typeId) {
        Map<String, String> resultMap = JedisUtil.hgetAll("credit_event_type_" + typeId);
        if (resultMap == null || resultMap.size() == 0) {
            MmpCreditEventType creditEventType = mmpCreditEventTypeMapper.selectByPrimaryKey(typeId);
            resultMap.put("id", String.valueOf(creditEventType.getId()));
            resultMap.put("eventName", creditEventType.getEventName());
            resultMap.put("eventNature", creditEventType.getEventNature());
            resultMap.put("amount", String.valueOf(creditEventType.getAmount()));
            JedisUtil.hmset("credit_event_type_" + typeId, resultMap);
            JedisUtil.expire("credit_event_type_" + typeId, 3600);
        }
        return resultMap;
    }
    
    


    /**
     * 及时支付 更新有效消费金额(实付金额+e币支付，不再包含押金抵扣)
     *
     * @param payBody
     */
    @Transactional(rollbackFor = Exception.class)
    public void payEventGrade(byte[] payBody) {
        OrderPay pay = new OrderPay();
        ProtobufUtil.deserializeProtobuf(payBody, pay);
        if (StringUtils.isNotBlank(pay.getAuthId())) {
            if (pay.getReturnTime() != null && pay.getPayTime() != null) {//已还车并支付
            	if(pay.getRealAmount() == null) {
            		pay.setRealAmount(new BigDecimal(0));
            	}
            	if(pay.geteAmount() == null) {
            		pay.seteAmount(new BigDecimal(0));
            	}
            	BigDecimal amount = pay.getRealAmount().add(pay.geteAmount());
                mmpUserTagMapper.updateEffectiveContdByAuthId(pay.getAuthId(), amount, new BigDecimal(0));
            }
        }
    }

    public void returnVehicleFirstOrder(byte[] data, ConsumeContext consumeContext) {
        ReturnVehicle returnVehicle = new ReturnVehicle();
        ProtobufUtil.deserializeProtobuf(data, returnVehicle);
        if (StringUtils.isNotBlank(returnVehicle.getAuthId())) {
            String authId = returnVehicle.getAuthId();
            String orderSeq = returnVehicle.getOrderSeq();
            if(!StringUtils.startsWithIgnoreCase(orderSeq, "C")
                    && !StringUtils.startsWithIgnoreCase(orderSeq, "D")) {
                return;
            }
            //还车成功后，如果订单为C订单或D订单则更新会员的tag字段
            MmpUserTag tag = mmpUserTagMapper.selectMmpUserByAuthId(authId);
            if(tag == null) {
                log.error("userTag.spare1: 更新首单信息失败，还车时仍无userTag信息, authId={}, orderSeq={}", authId, orderSeq);
            }
            if(tag.getSpare1() != null && (tag.getSpare1().startsWith("C") || tag.getSpare1().startsWith("D"))) {
                log.debug("userTag.spare1: 非用户首单，不更新，authId={}", authId);
                return;
            }
            mmpUserTagMapper.updateUserFirstOrderSeq(authId, orderSeq);
        }
    }


    /**
     * 及时支付 更新有效消费金额(实付金额+e币支付，不再包含押金抵扣)
     *
     * @param payBody
     */
    @Transactional(rollbackFor = Exception.class)
    public void payEventInstantReduce(byte[] payBody) {
        OrderPay pay = new OrderPay();
        ProtobufUtil.deserializeProtobuf(payBody, pay);
        if (StringUtils.isNotBlank(pay.getAuthId())) {
            if (pay.getReturnTime() != null && pay.getPayTime() != null) {//已还车并支付
                //支付成功，如果有立减明细，则记录立减活动记录
                OrderInfoPriceDto orderInfoPriceDto = orderPriceService.queryOrderPriceBySeq(pay.getOrderSeq());
                if (orderInfoPriceDto != null) {
                    log.debug(pay.getOrderSeq() + "支付事件，记录立减活动记录" + JSON.toJSONString(orderInfoPriceDto));
                    OrderPriceDetailDTO orderPriceDetail = orderInfoPriceDto.getOrderPriceDetail();
                    if (orderPriceDetail != null) {
                        Long canReduceAmountId = orderPriceDetail.getCanReduceAmountId();
                        if (canReduceAmountId != null && canReduceAmountId > 0) {
                            Map<Integer, PriceItemDto> priceItemMap = orderInfoPriceDto.getPriceItems();
                            PriceItemDto priceItemDto = priceItemMap.get(-31);
                            if (priceItemDto != null) {
                                UserOrderReduceActivityRecord insertRow = new UserOrderReduceActivityRecord();
                                insertRow.setAuthId(pay.getAuthId());
                                insertRow.setOrderSeq(pay.getOrderSeq());
                                insertRow.setReduceAmountActivityId(canReduceAmountId);
                                insertRow.setOrderReduceAmount(priceItemDto.getAmount());
                                Date nowTime = new Date();
                                insertRow.setCreateTime(nowTime);
                                insertRow.setUpdateTime(nowTime);
                                insertRow.setCreateOperName("membership-rpc");
                                insertRow.setUpdateOperName("membership-rpc");
                                userOrderReduceActivityRecordMapper.insertSelective(insertRow);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 会员审核  新消费者 处理逻辑
     *
     * @param body
     */
    public void memberNewAuditEvent(byte[] body) {
        NewMemberAudit audit = new NewMemberAudit();
        ProtobufUtil.deserializeProtobuf(body, audit);
        String authId = audit.getAuthId();
        String mobilePhone = audit.getMobilePhone();
        log.warn("会员审核事件消费memberAuditEvent: authId:" + authId + ", mobilePhone: " + mobilePhone);
        //获取会员渠道
        String appKey = membershipInfoMapper.queryAppKey(authId);
        if (StringUtils.equals("1", audit.getReviewStatus())) {
            auditSuccessNotify(audit, appKey);
            //享道：同步用户认证相关信息
            saicNewMemberReviewNotify(audit.getAuthId());
        } else if (StringUtils.equals("2", audit.getReviewStatus())) {
            newAuditFailedNotify(audit, appKey);
        }
    }

    public void newAuditFailedNotify(NewMemberAudit audit, String appKey) {
        log.info("newAuditFailedNotify入参：audit={},appKey={}", JSON.toJSONString(audit), appKey);
        String authId = audit.getAuthId();
        String mobilePhone = audit.getMobilePhone();
        String optUser = audit.getOperatorUserName();
        String title = "会员审核-身份证人工审核不通过";
        //向审核通过不通过的会员发送审核不通过短信, 新老用户均发
        if (ComUtil.checkIsSgmAppKey(appKey)) {
            return;
        }
        MembershipBasicInfo membership = memberShipService.getUserBasicInfo(authId, (short) 0);
//        //1. 短信发送
//        Map<String, String> param = new HashMap<>(1);
//        param.put("name", membership.getName());
        // 来自门店小程序（履约）不发短信
        if (!isFromStoreApp(audit)) {
            notifyReviewResultBySms(authId, mobilePhone, idCardAuditNoPassSmsTemplateId, null, title, optUser);
        }

    }

    public void auditSuccessNotify(NewMemberAudit audit, String appKey) {
        log.info("auditSuccessNotify入参：audit={},appKey={}", JSON.toJSONString(audit), appKey);

        /**
         * 2021.6 联动首单活动
         */

        BigDecimal totalAmount = getFirstAuditRewards(audit.getAuthId(), audit.getMid());
       /* BigDecimal totalAmount = BigDecimal.ZERO;
        try {
            // 校验此次审核通过是否需要发放首单奖励
            RewardDetailDto dto = registerRewardService.checkUserRewards(audit.getAuthId());
            if (dto != null && dto.getTotalCouponValue() != null) {
                totalAmount = dto.getTotalCouponValue();
            }
        } catch (Exception ex) {
            log.warn("审核通过事件：检查是否满足首单奖励条件失败， authId=" + audit.getAuthId(), ex);
        }*/

        String authId = audit.getAuthId();
        String mobilePhone = audit.getMobilePhone();
        String optUser = audit.getOperatorUserName();
        String title = "会员审核-身份证人工通过";
        MembershipBasicInfo membership = memberShipService.getUserBasicInfo(authId, (short) 0);

        //统一更换为新的短信模板， 只区分有无奖励，不区分首次审核通过与重新审核。
        boolean hasReward = (BigDecimal.ZERO.compareTo(totalAmount) < 0);
        //首次审核通过的判断。
        String msgId = hasReward ? idCardAuditFirstPassSmsTemplateId : idCardAuditPassSmsTemplateId;
        log.info("审核通过事件：审核通过，新手奖励={}， authId={}", hasReward, audit.getAuthId());
        //通用、别克代步车、松江校园不发短信；来自门店小程序（履约）不发短信
        if (!ComUtil.checkIsSgmAppKey(appKey) && !isFromStoreApp(audit)) {
            //1. 短信发送
            Map<String, String> param = new HashMap<>(2);
            param.put("couponValue", String.valueOf(totalAmount));
//            param.put("name", membership.getName());
            notifyReviewResultBySms(authId, mobilePhone, msgId, param, title, optUser);
        }
        //3. 推送审核奖励待发放事件
        try {
            if (hasReward) {
                //兼容老的MEMBER_AUDIT_REWARD FIXME 可在消费端调整
                MemberAudit memberAudit = new MemberAudit();
                memberAudit.setMemberType(audit.getMemberType());
                memberAudit.setOptUser(audit.getOperatorUserName());
                memberAudit.setReviewStatus(audit.getReviewStatus());
                memberAudit.setAuthId(audit.getAuthId());
                byte[] messageBody = ProtobufUtil.serializeProtobuf(memberAudit);
                String messageKey = "membership#" + UUID.randomUUID().toString().replace("-", "");
                Message msg = new Message(evcardRawDataTopic, EventEnum.MEMBER_AUDIT_REWARD.getTag(), messageBody);
                msg.setKey(messageKey);
                producer.send(msg);
                log.info("审核通过事件：推送审核通过奖励成功，authId={}, memberAudit={}", audit.getAuthId(), audit);
            }
        } catch (Exception e) {
            log.error("审核通过事件：推送审核通过奖励失败，authId={}, memberAudit={}", audit.getAuthId(), audit);
        }
    }

    private boolean isFromStoreApp(NewMemberAudit audit) {
        return audit.getOperateSourceType() != null && audit.getOperateSourceType() == 1;
    }

    public void saicNewMemberReviewNotify(String authId) {
        try {
            if (saicMemberService.beginSaic()) {
                log.info("享道：会员审核通过，同步用户认证信息， authId={}", authId);
                MembershipBasicInfo membership = memberShipService.getUserBasicInfo(authId, (short) 0);
                String faceImg = membership.getFaceRecognitionImgUrl();
                saicMemberService.updateUserAuth(membership, faceImg);
            }
        } catch (Exception ex) {
            log.error("享道：会员审核通过，同步用户认证信息失败, authId=" + authId, ex);
        }
    }


    public void memberIdentityToReviewEvent(byte[] body) {
        //身份证件提交人工审核时，同样处理
        MemberToAudit memberToAudit = new MemberToAudit();
        ProtobufUtil.deserializeProtobuf(body, memberToAudit);
        BigDecimal totalAmount = getFirstAuditRewards(memberToAudit.getAuthId(), memberToAudit.getMid());
        boolean hasReward = (BigDecimal.ZERO.compareTo(totalAmount) < 0);
        if(!hasReward) {
            log.info("身份证件提审/审核通过奖励：无需奖励，不做推送，authId={}, memberAudit={}", memberToAudit.getAuthId(), memberToAudit);
            return;
        }

        String authId = memberToAudit.getAuthId();
        try {
            //首次审核通过的短信模板
            String msgId =  idCardAuditFirstPassSmsTemplateId;
            MembershipInfoWithBLOBs member = membershipInfoMapper.selectByAuthId(authId, 0);
            if(member != null){
                log.info("会员身份证件提交-人工审核：审核通过，新手奖励totalAmount={}， authId={}", totalAmount, authId);
                //通用、别克代步车、松江校园不发短信
                if (!ComUtil.checkIsSgmAppKey(memberToAudit.getSubmitAppKey())) {
                    //1. 短信发送
                    Map<String, String> param = new HashMap<>(2);
                    param.put("couponValue", String.valueOf(totalAmount));
//                    param.put("name", member.getName());
                    String optUser = StringUtils.isNotBlank(member.getUpdatedUser()) ? member.getUpdatedUser() : "system";
                    notifyReviewResultBySms(authId, member.getMobilePhone(), msgId, param, "会员身份证件提交审核-人工审核",optUser);
                }
            }
        } catch (Exception e) {
            log.error("会员身份证件提交-人工审核,authId[{}],发送短信异常[{}]",authId,e);
        }


        try{
            //状态为提交人工审核，推送新手券待奖励事件(仅推送必要字段)
            MemberAudit audit = new MemberAudit();
            audit.setAuthId(memberToAudit.getAuthId());
            audit.setReviewStatus("0");
            audit.setReviewMode(1);
            audit.setOptUser(memberToAudit.getMid());
            audit.setMemberType(0);
            audit.setCreatedTime(memberToAudit.getSubmitTime());
            audit.setDataOrigin(memberToAudit.getSubmitAppKey());
            byte[] messageBody = ProtobufUtil.serializeProtobuf(audit);
            String messageKey = "membership#" + memberToAudit.getMid() + "#" + UUID.randomUUID().toString().replace("-", "");
            Message msg = new Message(evcardRawDataTopic, EventEnum.MEMBER_AUDIT_REWARD.getTag(), messageBody);
            msg.setKey(messageKey);
            producer.send(msg);
            log.info("身份证件提审/审核通过奖励：推送成功，authId={}, memberAudit={}", memberToAudit.getAuthId(), audit);
        } catch (Exception e) {
            log.error("身份证件提审/审核通过奖励：推送失败，authId=" + memberToAudit.getAuthId(), e);
        }
    }


    public void registerAndOfferCoupon(byte[] body) {
        List<UnregisterCouponDto> unregisterCouponDtos = ProtobufUtil.deserializeListProtobuf(body, UnregisterCouponDto.class);
        List<UnregisterCouponDto> registerCouponDtos = new ArrayList<>();
        for (UnregisterCouponDto unregisterCouponDto : unregisterCouponDtos) {
            CouponImport couponImport = unregisterCouponDto.getCouponImport();
            RegisterDto registerDto = unregisterCouponDto.getRegisterDto();
            try {
                String authId = memberShipService.registerV1(registerDto);
                if (StringUtils.isEmpty(authId)) {
                    log.error("注册会员失败，mobilePhone={}", registerDto.getMobilePhone());
                    continue;
                }
                couponImport.setAuthId(authId);
//                byte[] couponBody = ProtobufUtil.serializeProtobuf(unregisterCouponDto);
//                Message msg = new Message(evcardRawDataTopic, EventEnum.MEMBER_REGISTER_COUPON_OFFER.getTag(), couponBody);
//                producer.send(msg);
                registerCouponDtos.add(unregisterCouponDto);
                Thread.sleep(50);
            } catch (Exception e) {
                log.error("注册会员并赠送优惠券失败（注册部分失败），mobilePhone={}", registerDto.getMobilePhone(), e);
            }
        }
        byte[] couponBody = ProtobufUtil.serializeListProtobuf(registerCouponDtos);
        Message msg = new Message(evcardRawDataTopic, EventEnum.MEMBER_REGISTER_COUPON_OFFER.getTag(), couponBody);
        producer.send(msg);
    }
}
