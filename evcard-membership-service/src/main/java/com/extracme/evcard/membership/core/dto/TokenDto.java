package com.extracme.evcard.membership.core.dto;

import java.io.Serializable;

public class TokenDto implements Serializable {
    private static final long serialVersionUID = -3241884998121918302L;

    private String authId;
    private Integer memberType;
    private String cardNo;
    private String displayName;
    private String ip;
    private String appKey;

    // 0:个人用户 1：企业用户
    private Integer orgUser;

    // 0:普通 1：管理员
    private Integer userType;

    //	 登录来源 0： app 1:门户网站 2:车门控制
    private Integer loginOrigin = 0;

    private String agencyId;

    private String loginName;
    private String passWord;

    //	 APP类型 0：android 1：ios
    private Integer appType ;

    // APP版本号
    private String appVersion;

    private String orgId;

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public Integer getMemberType() {
        return memberType;
    }

    public void setMemberType(Integer memberType) {
        this.memberType = memberType;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public Integer getOrgUser() {
        return orgUser;
    }

    public void setOrgUser(Integer orgUser) {
        this.orgUser = orgUser;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public Integer getLoginOrigin() {
        return loginOrigin;
    }

    public void setLoginOrigin(Integer loginOrigin) {
        this.loginOrigin = loginOrigin;
    }

    public String getAgencyId() {
        return agencyId;
    }

    public void setAgencyId(String agencyId) {
        this.agencyId = agencyId;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getPassWord() {
        return passWord;
    }

    public void setPassWord(String passWord) {
        this.passWord = passWord;
    }

    public Integer getAppType() {
        return appType;
    }

    public void setAppType(Integer appType) {
        this.appType = appType;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }
}
