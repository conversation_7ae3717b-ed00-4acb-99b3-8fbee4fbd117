package com.extracme.evcard.membership.contract.service;

import com.extracme.evcard.membership.core.dto.ContractVersionDto;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class ContractSignInput implements Serializable {

    private static final long serialVersionUID = -1723176156209425025L;

    private String authId;

    private Long pkId;

    private String name;

    private String certNo;

    private String customerId;

    private String mobilePhone;

    private List<ContractVersionDto> contractVersionDtoList;
}
