package com.extracme.evcard.membership.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 统一的正反面枚举
 *
 * <AUTHOR>
 * @date 2022/8/16
 */
@AllArgsConstructor
@Getter
public enum CommonSideEnums {
    FRONT("front"),
    BACK("back");
    private String side;

    public static boolean isValid(String side) {
        return FRONT.getSide().equals(side) || BACK.getSide().equals(side);
    }

    public static CommonSideEnums getBySide(String side) {
        for (CommonSideEnums item : CommonSideEnums.values()) {
            if (item.getSide().equals(side)) {
                return item;
            }
        }
        return CommonSideEnums.FRONT;
    }

    public static CommonSideEnums getBySide(int side) {
        if(side > 0 && side <= CommonSideEnums.values().length) {
            return CommonSideEnums.values()[side - 1];
        }
        return CommonSideEnums.FRONT;
    }
}
