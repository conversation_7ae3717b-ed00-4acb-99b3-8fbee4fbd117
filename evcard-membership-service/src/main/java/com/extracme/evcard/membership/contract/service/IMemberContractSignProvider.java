package com.extracme.evcard.membership.contract.service;

import com.extracme.evcard.membership.core.dto.UserContractInfo;
import com.extracme.evcard.membership.core.dto.UserContractKeyDto;
import com.extracme.evcard.membership.core.exception.MemberException;

import java.util.Map;

/**
 * 用户合同签约服务提供者
 * <AUTHOR>
 * @Discription
 * @date 2021/2/10
 */
public interface IMemberContractSignProvider {
    /**
     * 模板上传 <br>
     * @param templateKey 模板关键字
     * @param docUrl  模板文件地址
     * @return 模板文件Id
     */
    UpdateTemplateResponse uploadTemplate(String templateKey, String docUrl);

    /**
     * 合同签署
     * @param input
     * @return
     */
    ContractSignOutput autoSignContract(ContractSignInput input) throws MemberException;

    /**
     * 生成下载链接
     * @param contract
     * @return
     */
    UserContractInfo getContractUrl(UserContractInfo contract);

    /**
     *
     * @param contractMap
     * @return
     */
    Map<String, String> batchDownloadContract(Map<String, UserContractKeyDto> contractMap);

}
