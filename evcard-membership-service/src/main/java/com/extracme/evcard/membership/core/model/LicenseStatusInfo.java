package com.extracme.evcard.membership.core.model;

import lombok.Data;

/**
 * 驾照状态信息
 */
@Data
public class LicenseStatusInfo {
    private boolean valid;
    /**
     * 驾照状态信息
     */
    private int licenseStatus;
    /**
     * 驾照状态描述： 综合了驾照证件状态以及准驾车型与有效期信息
     */
    private String licenseStatusMsg;
    /**
     * 第三方验证接口(如聚合)返回的驾照证件状态
     * 如：驾照已超分等
     */
    private String licenseFileStatusMsg;

    private boolean invalidExpireDate = false;

    private boolean invalidLicenseType = false;

    private boolean invalidLicenseStatus = false;
}
