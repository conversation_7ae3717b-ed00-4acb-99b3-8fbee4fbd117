package com.extracme.evcard.membership.core.controller;

import com.extracme.evcard.membership.core.dto.JwtTokenDto;
import com.extracme.evcard.membership.core.dto.TokenValidationResult;
import com.extracme.evcard.membership.core.exception.BusinessException;
import com.extracme.evcard.membership.core.interceptor.TokenValidationInterceptor;
import com.extracme.evcard.membership.core.service.ITokenService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Token管理控制器
 * 提供token相关的API接口
 * 
 * <AUTHOR>
 * @since 2025-08-20
 */
@Slf4j
@RestController
@RequestMapping("/api/token")
public class TokenController {
    
    @Autowired
    private ITokenService tokenService;
    
    /**
     * 验证访问令牌
     */
    @PostMapping("/validate")
    public Map<String, Object> validateToken(@RequestBody Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String accessToken = request.get("accessToken");
            if (StringUtils.isBlank(accessToken)) {
                response.put("code", -1);
                response.put("message", "访问令牌不能为空");
                return response;
            }
            
            TokenValidationResult result = tokenService.validateAccessToken(accessToken);
            
            if (result.isValid()) {
                response.put("code", 0);
                response.put("message", "令牌验证成功");
                response.put("data", result);
            } else {
                response.put("code", -1);
                response.put("message", result.getErrorMessage());
                response.put("errorCode", result.getErrorCode());
            }
            
        } catch (Exception e) {
            log.error("验证访问令牌失败: {}", e.getMessage(), e);
            response.put("code", -1);
            response.put("message", "验证访问令牌失败");
        }
        
        return response;
    }
    
    /**
     * 刷新访问令牌
     */
    @PostMapping("/refresh")
    public Map<String, Object> refreshToken(@RequestBody Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String refreshToken = request.get("refreshToken");
            if (StringUtils.isBlank(refreshToken)) {
                response.put("code", -1);
                response.put("message", "刷新令牌不能为空");
                return response;
            }
            
            JwtTokenDto tokenDto = tokenService.refreshAccessToken(refreshToken);
            
            response.put("code", 0);
            response.put("message", "刷新令牌成功");
            response.put("data", tokenDto);
            
        } catch (BusinessException e) {
            response.put("code", -1);
            response.put("message", e.getMessage());
        } catch (Exception e) {
            log.error("刷新访问令牌失败: {}", e.getMessage(), e);
            response.put("code", -1);
            response.put("message", "刷新访问令牌失败");
        }
        
        return response;
    }
    
    /**
     * 撤销令牌
     */
    @PostMapping("/revoke")
    public Map<String, Object> revokeToken(@RequestBody Map<String, String> request, HttpServletRequest httpRequest) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String token = request.get("token");
            String reason = request.get("reason");
            
            if (StringUtils.isBlank(token)) {
                response.put("code", -1);
                response.put("message", "令牌不能为空");
                return response;
            }
            
            if (StringUtils.isBlank(reason)) {
                reason = "用户主动撤销";
            }
            
            tokenService.revokeToken(token, reason);
            
            response.put("code", 0);
            response.put("message", "撤销令牌成功");
            
        } catch (BusinessException e) {
            response.put("code", -1);
            response.put("message", e.getMessage());
        } catch (Exception e) {
            log.error("撤销令牌失败: {}", e.getMessage(), e);
            response.put("code", -1);
            response.put("message", "撤销令牌失败");
        }
        
        return response;
    }
    
    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public Map<String, Object> logout(@RequestBody Map<String, String> request, HttpServletRequest httpRequest) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 从请求头中获取访问令牌
            String accessToken = extractTokenFromRequest(httpRequest);
            String reason = request.get("reason");
            
            if (StringUtils.isBlank(accessToken)) {
                response.put("code", -1);
                response.put("message", "访问令牌不能为空");
                return response;
            }
            
            if (StringUtils.isBlank(reason)) {
                reason = "用户主动登出";
            }
            
            tokenService.logout(accessToken, reason);
            
            response.put("code", 0);
            response.put("message", "登出成功");
            
        } catch (BusinessException e) {
            response.put("code", -1);
            response.put("message", e.getMessage());
        } catch (Exception e) {
            log.error("用户登出失败: {}", e.getMessage(), e);
            response.put("code", -1);
            response.put("message", "用户登出失败");
        }
        
        return response;
    }
    
    /**
     * 登出所有设备
     */
    @PostMapping("/logout-all")
    public Map<String, Object> logoutAllDevices(@RequestBody Map<String, String> request, HttpServletRequest httpRequest) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Long userId = TokenValidationInterceptor.getUserIdFromRequest(httpRequest);
            String reason = request.get("reason");
            
            if (userId == null) {
                response.put("code", -1);
                response.put("message", "用户信息不存在");
                return response;
            }
            
            if (StringUtils.isBlank(reason)) {
                reason = "用户主动登出所有设备";
            }
            
            tokenService.logoutAllDevices(userId, reason);
            
            response.put("code", 0);
            response.put("message", "登出所有设备成功");
            
        } catch (BusinessException e) {
            response.put("code", -1);
            response.put("message", e.getMessage());
        } catch (Exception e) {
            log.error("登出所有设备失败: {}", e.getMessage(), e);
            response.put("code", -1);
            response.put("message", "登出所有设备失败");
        }
        
        return response;
    }
    
    /**
     * 获取用户活跃会话列表
     */
    @GetMapping("/sessions")
    public Map<String, Object> getUserActiveSessions(HttpServletRequest httpRequest) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Long userId = TokenValidationInterceptor.getUserIdFromRequest(httpRequest);
            
            if (userId == null) {
                response.put("code", -1);
                response.put("message", "用户信息不存在");
                return response;
            }
            
            List<JwtTokenDto> sessions = tokenService.getUserActiveSessions(userId);
            
            response.put("code", 0);
            response.put("message", "获取会话列表成功");
            response.put("data", sessions);
            
        } catch (BusinessException e) {
            response.put("code", -1);
            response.put("message", e.getMessage());
        } catch (Exception e) {
            log.error("获取用户活跃会话列表失败: {}", e.getMessage(), e);
            response.put("code", -1);
            response.put("message", "获取会话列表失败");
        }
        
        return response;
    }
    
    /**
     * 踢出指定设备
     */
    @PostMapping("/kickout")
    public Map<String, Object> kickoutDevice(@RequestBody Map<String, String> request, HttpServletRequest httpRequest) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            Long userId = TokenValidationInterceptor.getUserIdFromRequest(httpRequest);
            String deviceId = request.get("deviceId");
            String reason = request.get("reason");
            
            if (userId == null) {
                response.put("code", -1);
                response.put("message", "用户信息不存在");
                return response;
            }
            
            if (StringUtils.isBlank(deviceId)) {
                response.put("code", -1);
                response.put("message", "设备ID不能为空");
                return response;
            }
            
            if (StringUtils.isBlank(reason)) {
                reason = "用户主动踢出设备";
            }
            
            tokenService.kickoutDevice(userId, deviceId, reason);
            
            response.put("code", 0);
            response.put("message", "踢出设备成功");
            
        } catch (BusinessException e) {
            response.put("code", -1);
            response.put("message", e.getMessage());
        } catch (Exception e) {
            log.error("踢出设备失败: {}", e.getMessage(), e);
            response.put("code", -1);
            response.put("message", "踢出设备失败");
        }
        
        return response;
    }
    
    /**
     * 从请求中提取token
     */
    private String extractTokenFromRequest(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");
        if (StringUtils.isNotBlank(authHeader) && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }
        
        String accessToken = request.getHeader("X-Access-Token");
        if (StringUtils.isNotBlank(accessToken)) {
            return accessToken;
        }
        
        return request.getParameter("access_token");
    }
}
