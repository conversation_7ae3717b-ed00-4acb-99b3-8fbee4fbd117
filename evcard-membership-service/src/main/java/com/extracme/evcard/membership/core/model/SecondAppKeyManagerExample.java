package com.extracme.evcard.membership.core.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SecondAppKeyManagerExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public SecondAppKeyManagerExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSecondAppKeyIsNull() {
            addCriterion("second_app_key is null");
            return (Criteria) this;
        }

        public Criteria andSecondAppKeyIsNotNull() {
            addCriterion("second_app_key is not null");
            return (Criteria) this;
        }

        public Criteria andSecondAppKeyEqualTo(String value) {
            addCriterion("second_app_key =", value, "secondAppKey");
            return (Criteria) this;
        }

        public Criteria andSecondAppKeyNotEqualTo(String value) {
            addCriterion("second_app_key <>", value, "secondAppKey");
            return (Criteria) this;
        }

        public Criteria andSecondAppKeyGreaterThan(String value) {
            addCriterion("second_app_key >", value, "secondAppKey");
            return (Criteria) this;
        }

        public Criteria andSecondAppKeyGreaterThanOrEqualTo(String value) {
            addCriterion("second_app_key >=", value, "secondAppKey");
            return (Criteria) this;
        }

        public Criteria andSecondAppKeyLessThan(String value) {
            addCriterion("second_app_key <", value, "secondAppKey");
            return (Criteria) this;
        }

        public Criteria andSecondAppKeyLessThanOrEqualTo(String value) {
            addCriterion("second_app_key <=", value, "secondAppKey");
            return (Criteria) this;
        }

        public Criteria andSecondAppKeyLike(String value) {
            addCriterion("second_app_key like", value, "secondAppKey");
            return (Criteria) this;
        }

        public Criteria andSecondAppKeyNotLike(String value) {
            addCriterion("second_app_key not like", value, "secondAppKey");
            return (Criteria) this;
        }

        public Criteria andSecondAppKeyIn(List<String> values) {
            addCriterion("second_app_key in", values, "secondAppKey");
            return (Criteria) this;
        }

        public Criteria andSecondAppKeyNotIn(List<String> values) {
            addCriterion("second_app_key not in", values, "secondAppKey");
            return (Criteria) this;
        }

        public Criteria andSecondAppKeyBetween(String value1, String value2) {
            addCriterion("second_app_key between", value1, value2, "secondAppKey");
            return (Criteria) this;
        }

        public Criteria andSecondAppKeyNotBetween(String value1, String value2) {
            addCriterion("second_app_key not between", value1, value2, "secondAppKey");
            return (Criteria) this;
        }

        public Criteria andSecondAppSecretIsNull() {
            addCriterion("second_app_secret is null");
            return (Criteria) this;
        }

        public Criteria andSecondAppSecretIsNotNull() {
            addCriterion("second_app_secret is not null");
            return (Criteria) this;
        }

        public Criteria andSecondAppSecretEqualTo(String value) {
            addCriterion("second_app_secret =", value, "secondAppSecret");
            return (Criteria) this;
        }

        public Criteria andSecondAppSecretNotEqualTo(String value) {
            addCriterion("second_app_secret <>", value, "secondAppSecret");
            return (Criteria) this;
        }

        public Criteria andSecondAppSecretGreaterThan(String value) {
            addCriterion("second_app_secret >", value, "secondAppSecret");
            return (Criteria) this;
        }

        public Criteria andSecondAppSecretGreaterThanOrEqualTo(String value) {
            addCriterion("second_app_secret >=", value, "secondAppSecret");
            return (Criteria) this;
        }

        public Criteria andSecondAppSecretLessThan(String value) {
            addCriterion("second_app_secret <", value, "secondAppSecret");
            return (Criteria) this;
        }

        public Criteria andSecondAppSecretLessThanOrEqualTo(String value) {
            addCriterion("second_app_secret <=", value, "secondAppSecret");
            return (Criteria) this;
        }

        public Criteria andSecondAppSecretLike(String value) {
            addCriterion("second_app_secret like", value, "secondAppSecret");
            return (Criteria) this;
        }

        public Criteria andSecondAppSecretNotLike(String value) {
            addCriterion("second_app_secret not like", value, "secondAppSecret");
            return (Criteria) this;
        }

        public Criteria andSecondAppSecretIn(List<String> values) {
            addCriterion("second_app_secret in", values, "secondAppSecret");
            return (Criteria) this;
        }

        public Criteria andSecondAppSecretNotIn(List<String> values) {
            addCriterion("second_app_secret not in", values, "secondAppSecret");
            return (Criteria) this;
        }

        public Criteria andSecondAppSecretBetween(String value1, String value2) {
            addCriterion("second_app_secret between", value1, value2, "secondAppSecret");
            return (Criteria) this;
        }

        public Criteria andSecondAppSecretNotBetween(String value1, String value2) {
            addCriterion("second_app_secret not between", value1, value2, "secondAppSecret");
            return (Criteria) this;
        }

        public Criteria andSecondAppKeyNameIsNull() {
            addCriterion("second_app_key_name is null");
            return (Criteria) this;
        }

        public Criteria andSecondAppKeyNameIsNotNull() {
            addCriterion("second_app_key_name is not null");
            return (Criteria) this;
        }

        public Criteria andSecondAppKeyNameEqualTo(String value) {
            addCriterion("second_app_key_name =", value, "secondAppKeyName");
            return (Criteria) this;
        }

        public Criteria andSecondAppKeyNameNotEqualTo(String value) {
            addCriterion("second_app_key_name <>", value, "secondAppKeyName");
            return (Criteria) this;
        }

        public Criteria andSecondAppKeyNameGreaterThan(String value) {
            addCriterion("second_app_key_name >", value, "secondAppKeyName");
            return (Criteria) this;
        }

        public Criteria andSecondAppKeyNameGreaterThanOrEqualTo(String value) {
            addCriterion("second_app_key_name >=", value, "secondAppKeyName");
            return (Criteria) this;
        }

        public Criteria andSecondAppKeyNameLessThan(String value) {
            addCriterion("second_app_key_name <", value, "secondAppKeyName");
            return (Criteria) this;
        }

        public Criteria andSecondAppKeyNameLessThanOrEqualTo(String value) {
            addCriterion("second_app_key_name <=", value, "secondAppKeyName");
            return (Criteria) this;
        }

        public Criteria andSecondAppKeyNameLike(String value) {
            addCriterion("second_app_key_name like", value, "secondAppKeyName");
            return (Criteria) this;
        }

        public Criteria andSecondAppKeyNameNotLike(String value) {
            addCriterion("second_app_key_name not like", value, "secondAppKeyName");
            return (Criteria) this;
        }

        public Criteria andSecondAppKeyNameIn(List<String> values) {
            addCriterion("second_app_key_name in", values, "secondAppKeyName");
            return (Criteria) this;
        }

        public Criteria andSecondAppKeyNameNotIn(List<String> values) {
            addCriterion("second_app_key_name not in", values, "secondAppKeyName");
            return (Criteria) this;
        }

        public Criteria andSecondAppKeyNameBetween(String value1, String value2) {
            addCriterion("second_app_key_name between", value1, value2, "secondAppKeyName");
            return (Criteria) this;
        }

        public Criteria andSecondAppKeyNameNotBetween(String value1, String value2) {
            addCriterion("second_app_key_name not between", value1, value2, "secondAppKeyName");
            return (Criteria) this;
        }

        public Criteria andThirdidIsNull() {
            addCriterion("thirdId is null");
            return (Criteria) this;
        }

        public Criteria andThirdidIsNotNull() {
            addCriterion("thirdId is not null");
            return (Criteria) this;
        }

        public Criteria andThirdidEqualTo(Integer value) {
            addCriterion("thirdId =", value, "thirdid");
            return (Criteria) this;
        }

        public Criteria andThirdidNotEqualTo(Integer value) {
            addCriterion("thirdId <>", value, "thirdid");
            return (Criteria) this;
        }

        public Criteria andThirdidGreaterThan(Integer value) {
            addCriterion("thirdId >", value, "thirdid");
            return (Criteria) this;
        }

        public Criteria andThirdidGreaterThanOrEqualTo(Integer value) {
            addCriterion("thirdId >=", value, "thirdid");
            return (Criteria) this;
        }

        public Criteria andThirdidLessThan(Integer value) {
            addCriterion("thirdId <", value, "thirdid");
            return (Criteria) this;
        }

        public Criteria andThirdidLessThanOrEqualTo(Integer value) {
            addCriterion("thirdId <=", value, "thirdid");
            return (Criteria) this;
        }

        public Criteria andThirdidIn(List<Integer> values) {
            addCriterion("thirdId in", values, "thirdid");
            return (Criteria) this;
        }

        public Criteria andThirdidNotIn(List<Integer> values) {
            addCriterion("thirdId not in", values, "thirdid");
            return (Criteria) this;
        }

        public Criteria andThirdidBetween(Integer value1, Integer value2) {
            addCriterion("thirdId between", value1, value2, "thirdid");
            return (Criteria) this;
        }

        public Criteria andThirdidNotBetween(Integer value1, Integer value2) {
            addCriterion("thirdId not between", value1, value2, "thirdid");
            return (Criteria) this;
        }

        public Criteria andFirstAppKeyIsNull() {
            addCriterion("first_app_key is null");
            return (Criteria) this;
        }

        public Criteria andFirstAppKeyIsNotNull() {
            addCriterion("first_app_key is not null");
            return (Criteria) this;
        }

        public Criteria andFirstAppKeyEqualTo(String value) {
            addCriterion("first_app_key =", value, "firstAppKey");
            return (Criteria) this;
        }

        public Criteria andFirstAppKeyNotEqualTo(String value) {
            addCriterion("first_app_key <>", value, "firstAppKey");
            return (Criteria) this;
        }

        public Criteria andFirstAppKeyGreaterThan(String value) {
            addCriterion("first_app_key >", value, "firstAppKey");
            return (Criteria) this;
        }

        public Criteria andFirstAppKeyGreaterThanOrEqualTo(String value) {
            addCriterion("first_app_key >=", value, "firstAppKey");
            return (Criteria) this;
        }

        public Criteria andFirstAppKeyLessThan(String value) {
            addCriterion("first_app_key <", value, "firstAppKey");
            return (Criteria) this;
        }

        public Criteria andFirstAppKeyLessThanOrEqualTo(String value) {
            addCriterion("first_app_key <=", value, "firstAppKey");
            return (Criteria) this;
        }

        public Criteria andFirstAppKeyLike(String value) {
            addCriterion("first_app_key like", value, "firstAppKey");
            return (Criteria) this;
        }

        public Criteria andFirstAppKeyNotLike(String value) {
            addCriterion("first_app_key not like", value, "firstAppKey");
            return (Criteria) this;
        }

        public Criteria andFirstAppKeyIn(List<String> values) {
            addCriterion("first_app_key in", values, "firstAppKey");
            return (Criteria) this;
        }

        public Criteria andFirstAppKeyNotIn(List<String> values) {
            addCriterion("first_app_key not in", values, "firstAppKey");
            return (Criteria) this;
        }

        public Criteria andFirstAppKeyBetween(String value1, String value2) {
            addCriterion("first_app_key between", value1, value2, "firstAppKey");
            return (Criteria) this;
        }

        public Criteria andFirstAppKeyNotBetween(String value1, String value2) {
            addCriterion("first_app_key not between", value1, value2, "firstAppKey");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNull() {
            addCriterion("platform_id is null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNotNull() {
            addCriterion("platform_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdEqualTo(Long value) {
            addCriterion("platform_id =", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotEqualTo(Long value) {
            addCriterion("platform_id <>", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThan(Long value) {
            addCriterion("platform_id >", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThanOrEqualTo(Long value) {
            addCriterion("platform_id >=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThan(Long value) {
            addCriterion("platform_id <", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThanOrEqualTo(Long value) {
            addCriterion("platform_id <=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIn(List<Long> values) {
            addCriterion("platform_id in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotIn(List<Long> values) {
            addCriterion("platform_id not in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdBetween(Long value1, Long value2) {
            addCriterion("platform_id between", value1, value2, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotBetween(Long value1, Long value2) {
            addCriterion("platform_id not between", value1, value2, "platformId");
            return (Criteria) this;
        }

        public Criteria andChannelPurposeIsNull() {
            addCriterion("channel_purpose is null");
            return (Criteria) this;
        }

        public Criteria andChannelPurposeIsNotNull() {
            addCriterion("channel_purpose is not null");
            return (Criteria) this;
        }

        public Criteria andChannelPurposeEqualTo(String value) {
            addCriterion("channel_purpose =", value, "channelPurpose");
            return (Criteria) this;
        }

        public Criteria andChannelPurposeNotEqualTo(String value) {
            addCriterion("channel_purpose <>", value, "channelPurpose");
            return (Criteria) this;
        }

        public Criteria andChannelPurposeGreaterThan(String value) {
            addCriterion("channel_purpose >", value, "channelPurpose");
            return (Criteria) this;
        }

        public Criteria andChannelPurposeGreaterThanOrEqualTo(String value) {
            addCriterion("channel_purpose >=", value, "channelPurpose");
            return (Criteria) this;
        }

        public Criteria andChannelPurposeLessThan(String value) {
            addCriterion("channel_purpose <", value, "channelPurpose");
            return (Criteria) this;
        }

        public Criteria andChannelPurposeLessThanOrEqualTo(String value) {
            addCriterion("channel_purpose <=", value, "channelPurpose");
            return (Criteria) this;
        }

        public Criteria andChannelPurposeLike(String value) {
            addCriterion("channel_purpose like", value, "channelPurpose");
            return (Criteria) this;
        }

        public Criteria andChannelPurposeNotLike(String value) {
            addCriterion("channel_purpose not like", value, "channelPurpose");
            return (Criteria) this;
        }

        public Criteria andChannelPurposeIn(List<String> values) {
            addCriterion("channel_purpose in", values, "channelPurpose");
            return (Criteria) this;
        }

        public Criteria andChannelPurposeNotIn(List<String> values) {
            addCriterion("channel_purpose not in", values, "channelPurpose");
            return (Criteria) this;
        }

        public Criteria andChannelPurposeBetween(String value1, String value2) {
            addCriterion("channel_purpose between", value1, value2, "channelPurpose");
            return (Criteria) this;
        }

        public Criteria andChannelPurposeNotBetween(String value1, String value2) {
            addCriterion("channel_purpose not between", value1, value2, "channelPurpose");
            return (Criteria) this;
        }

        public Criteria andTaxMainCompanyIsNull() {
            addCriterion("tax_main_company is null");
            return (Criteria) this;
        }

        public Criteria andTaxMainCompanyIsNotNull() {
            addCriterion("tax_main_company is not null");
            return (Criteria) this;
        }

        public Criteria andTaxMainCompanyEqualTo(String value) {
            addCriterion("tax_main_company =", value, "taxMainCompany");
            return (Criteria) this;
        }

        public Criteria andTaxMainCompanyNotEqualTo(String value) {
            addCriterion("tax_main_company <>", value, "taxMainCompany");
            return (Criteria) this;
        }

        public Criteria andTaxMainCompanyGreaterThan(String value) {
            addCriterion("tax_main_company >", value, "taxMainCompany");
            return (Criteria) this;
        }

        public Criteria andTaxMainCompanyGreaterThanOrEqualTo(String value) {
            addCriterion("tax_main_company >=", value, "taxMainCompany");
            return (Criteria) this;
        }

        public Criteria andTaxMainCompanyLessThan(String value) {
            addCriterion("tax_main_company <", value, "taxMainCompany");
            return (Criteria) this;
        }

        public Criteria andTaxMainCompanyLessThanOrEqualTo(String value) {
            addCriterion("tax_main_company <=", value, "taxMainCompany");
            return (Criteria) this;
        }

        public Criteria andTaxMainCompanyLike(String value) {
            addCriterion("tax_main_company like", value, "taxMainCompany");
            return (Criteria) this;
        }

        public Criteria andTaxMainCompanyNotLike(String value) {
            addCriterion("tax_main_company not like", value, "taxMainCompany");
            return (Criteria) this;
        }

        public Criteria andTaxMainCompanyIn(List<String> values) {
            addCriterion("tax_main_company in", values, "taxMainCompany");
            return (Criteria) this;
        }

        public Criteria andTaxMainCompanyNotIn(List<String> values) {
            addCriterion("tax_main_company not in", values, "taxMainCompany");
            return (Criteria) this;
        }

        public Criteria andTaxMainCompanyBetween(String value1, String value2) {
            addCriterion("tax_main_company between", value1, value2, "taxMainCompany");
            return (Criteria) this;
        }

        public Criteria andTaxMainCompanyNotBetween(String value1, String value2) {
            addCriterion("tax_main_company not between", value1, value2, "taxMainCompany");
            return (Criteria) this;
        }

        public Criteria andOrgIdIsNull() {
            addCriterion("org_id is null");
            return (Criteria) this;
        }

        public Criteria andOrgIdIsNotNull() {
            addCriterion("org_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrgIdEqualTo(String value) {
            addCriterion("org_id =", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotEqualTo(String value) {
            addCriterion("org_id <>", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdGreaterThan(String value) {
            addCriterion("org_id >", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdGreaterThanOrEqualTo(String value) {
            addCriterion("org_id >=", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdLessThan(String value) {
            addCriterion("org_id <", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdLessThanOrEqualTo(String value) {
            addCriterion("org_id <=", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdLike(String value) {
            addCriterion("org_id like", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotLike(String value) {
            addCriterion("org_id not like", value, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdIn(List<String> values) {
            addCriterion("org_id in", values, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotIn(List<String> values) {
            addCriterion("org_id not in", values, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdBetween(String value1, String value2) {
            addCriterion("org_id between", value1, value2, "orgId");
            return (Criteria) this;
        }

        public Criteria andOrgIdNotBetween(String value1, String value2) {
            addCriterion("org_id not between", value1, value2, "orgId");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdIsNull() {
            addCriterion("create_oper_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdIsNotNull() {
            addCriterion("create_oper_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdEqualTo(Long value) {
            addCriterion("create_oper_id =", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdNotEqualTo(Long value) {
            addCriterion("create_oper_id <>", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdGreaterThan(Long value) {
            addCriterion("create_oper_id >", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdGreaterThanOrEqualTo(Long value) {
            addCriterion("create_oper_id >=", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdLessThan(Long value) {
            addCriterion("create_oper_id <", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdLessThanOrEqualTo(Long value) {
            addCriterion("create_oper_id <=", value, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdIn(List<Long> values) {
            addCriterion("create_oper_id in", values, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdNotIn(List<Long> values) {
            addCriterion("create_oper_id not in", values, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdBetween(Long value1, Long value2) {
            addCriterion("create_oper_id between", value1, value2, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperIdNotBetween(Long value1, Long value2) {
            addCriterion("create_oper_id not between", value1, value2, "createOperId");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameIsNull() {
            addCriterion("create_oper_name is null");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameIsNotNull() {
            addCriterion("create_oper_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameEqualTo(String value) {
            addCriterion("create_oper_name =", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotEqualTo(String value) {
            addCriterion("create_oper_name <>", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameGreaterThan(String value) {
            addCriterion("create_oper_name >", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameGreaterThanOrEqualTo(String value) {
            addCriterion("create_oper_name >=", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameLessThan(String value) {
            addCriterion("create_oper_name <", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameLessThanOrEqualTo(String value) {
            addCriterion("create_oper_name <=", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameLike(String value) {
            addCriterion("create_oper_name like", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotLike(String value) {
            addCriterion("create_oper_name not like", value, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameIn(List<String> values) {
            addCriterion("create_oper_name in", values, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotIn(List<String> values) {
            addCriterion("create_oper_name not in", values, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameBetween(String value1, String value2) {
            addCriterion("create_oper_name between", value1, value2, "createOperName");
            return (Criteria) this;
        }

        public Criteria andCreateOperNameNotBetween(String value1, String value2) {
            addCriterion("create_oper_name not between", value1, value2, "createOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdIsNull() {
            addCriterion("update_oper_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdIsNotNull() {
            addCriterion("update_oper_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdEqualTo(Long value) {
            addCriterion("update_oper_id =", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdNotEqualTo(Long value) {
            addCriterion("update_oper_id <>", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdGreaterThan(Long value) {
            addCriterion("update_oper_id >", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdGreaterThanOrEqualTo(Long value) {
            addCriterion("update_oper_id >=", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdLessThan(Long value) {
            addCriterion("update_oper_id <", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdLessThanOrEqualTo(Long value) {
            addCriterion("update_oper_id <=", value, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdIn(List<Long> values) {
            addCriterion("update_oper_id in", values, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdNotIn(List<Long> values) {
            addCriterion("update_oper_id not in", values, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdBetween(Long value1, Long value2) {
            addCriterion("update_oper_id between", value1, value2, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperIdNotBetween(Long value1, Long value2) {
            addCriterion("update_oper_id not between", value1, value2, "updateOperId");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameIsNull() {
            addCriterion("update_oper_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameIsNotNull() {
            addCriterion("update_oper_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameEqualTo(String value) {
            addCriterion("update_oper_name =", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotEqualTo(String value) {
            addCriterion("update_oper_name <>", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameGreaterThan(String value) {
            addCriterion("update_oper_name >", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameGreaterThanOrEqualTo(String value) {
            addCriterion("update_oper_name >=", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameLessThan(String value) {
            addCriterion("update_oper_name <", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameLessThanOrEqualTo(String value) {
            addCriterion("update_oper_name <=", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameLike(String value) {
            addCriterion("update_oper_name like", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotLike(String value) {
            addCriterion("update_oper_name not like", value, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameIn(List<String> values) {
            addCriterion("update_oper_name in", values, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotIn(List<String> values) {
            addCriterion("update_oper_name not in", values, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameBetween(String value1, String value2) {
            addCriterion("update_oper_name between", value1, value2, "updateOperName");
            return (Criteria) this;
        }

        public Criteria andUpdateOperNameNotBetween(String value1, String value2) {
            addCriterion("update_oper_name not between", value1, value2, "updateOperName");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}