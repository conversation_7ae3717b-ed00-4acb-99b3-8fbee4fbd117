package com.extracme.evcard.membership.core.model;

import java.io.Serializable;
import java.util.Date;

public class UserToken implements Serializable {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_token.id
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    private Long id;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_token.user_id
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    private Long userId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_token.app_key
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    private String appKey;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_token.token
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    private String token;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_token.expires_time
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    private Date expiresTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_token.start_time
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    private Date startTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_token.create_time
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_token.create_oper_id
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    private Long createOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_token.create_oper_name
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    private String createOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_token.update_time
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_token.update_oper_id
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    private Long updateOperId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_token.update_oper_name
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    private String updateOperName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column user_token.is_deleted
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    private Integer isDeleted;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_token.id
     *
     * @return the value of user_token.id
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_token.id
     *
     * @param id the value for user_token.id
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_token.user_id
     *
     * @return the value of user_token.user_id
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    public Long getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_token.user_id
     *
     * @param userId the value for user_token.user_id
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_token.app_key
     *
     * @return the value of user_token.app_key
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    public String getAppKey() {
        return appKey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_token.app_key
     *
     * @param appKey the value for user_token.app_key
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_token.token
     *
     * @return the value of user_token.token
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    public String getToken() {
        return token;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_token.token
     *
     * @param token the value for user_token.token
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    public void setToken(String token) {
        this.token = token;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_token.expires_time
     *
     * @return the value of user_token.expires_time
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    public Date getExpiresTime() {
        return expiresTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_token.expires_time
     *
     * @param expiresTime the value for user_token.expires_time
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    public void setExpiresTime(Date expiresTime) {
        this.expiresTime = expiresTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_token.start_time
     *
     * @return the value of user_token.start_time
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    public Date getStartTime() {
        return startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_token.start_time
     *
     * @param startTime the value for user_token.start_time
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_token.create_time
     *
     * @return the value of user_token.create_time
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_token.create_time
     *
     * @param createTime the value for user_token.create_time
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_token.create_oper_id
     *
     * @return the value of user_token.create_oper_id
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    public Long getCreateOperId() {
        return createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_token.create_oper_id
     *
     * @param createOperId the value for user_token.create_oper_id
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_token.create_oper_name
     *
     * @return the value of user_token.create_oper_name
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    public String getCreateOperName() {
        return createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_token.create_oper_name
     *
     * @param createOperName the value for user_token.create_oper_name
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_token.update_time
     *
     * @return the value of user_token.update_time
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_token.update_time
     *
     * @param updateTime the value for user_token.update_time
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_token.update_oper_id
     *
     * @return the value of user_token.update_oper_id
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    public Long getUpdateOperId() {
        return updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_token.update_oper_id
     *
     * @param updateOperId the value for user_token.update_oper_id
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_token.update_oper_name
     *
     * @return the value of user_token.update_oper_name
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    public String getUpdateOperName() {
        return updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_token.update_oper_name
     *
     * @param updateOperName the value for user_token.update_oper_name
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column user_token.is_deleted
     *
     * @return the value of user_token.is_deleted
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    public Integer getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column user_token.is_deleted
     *
     * @param isDeleted the value for user_token.is_deleted
     *
     * @mbggenerated Fri Feb 24 17:25:13 CST 2023
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }
}