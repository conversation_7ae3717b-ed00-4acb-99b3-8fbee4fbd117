package com.extracme.evcard.membership.third.baidu;

import lombok.Data;

import java.io.Serializable;

@Data
public class BaiDuAddressResp implements Serializable {

    /**
     * 请求唯一标识码
     */
    private Long logId;

    /**
     * 原始输入的文本内容
     */
    private String text;

    /**
     * 省（直辖市/自治区
     */
    private String province;

    /**
     * 省国标code
     */
    private String province_code;

    /**
     * 市
     */
    private String city;

    /**
     * 城市国标code
     */
    private String city_code;

    /**
     * 区（县）
     */
    private String county;

    /**
     * 区县国标code
     */
    private String county_code;

    /**
     * 街道（乡/镇）
     */
    private String town;

    /**
     * 街道/乡镇国标code
     */
    private String town_code;

    /**
     * 姓名，如果出现多个则按顺序输出
     */
    private String person;

    /**
     * 详细地址
     */
    private String detail;

    /**
     * 电话号码，如果出现多个则按顺序输出
     */
    private String phonenum;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 经度
     */
    private String lng;
}
