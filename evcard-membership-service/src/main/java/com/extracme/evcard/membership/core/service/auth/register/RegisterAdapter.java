package com.extracme.evcard.membership.core.service.auth.register;

import com.extracme.evcard.membership.core.service.auth.IDescription;
import com.extracme.evcard.membership.core.service.auth.IRegister;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

@Service
public class RegisterAdapter {

    @Resource
    List<IRegister> registers;

    /**
     * 获取押金具体服务类
     *
     * @param secondAppKey
     * @return
     */
    public IRegister getRegisterService(String secondAppKey) {
        if (StringUtils.isBlank(secondAppKey)) {
            return null;
        }
        Optional<IRegister> optional = registers.stream().filter(d -> {
            if (d instanceof IDescription) {
                IDescription depositService = (IDescription) d;
                if (secondAppKey.equals(depositService.getSecondAppKey())) {
                    return true;
                }
            }
            return false;
        }).findFirst();

        return optional.isPresent() ? optional.get() : null;
    }

}
