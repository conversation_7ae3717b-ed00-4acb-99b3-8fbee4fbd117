package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.dto.FeedbackBaseDto;
import com.extracme.evcard.membership.core.dto.FeedbackReplyMess;
import com.extracme.evcard.membership.core.dto.UpdataUndealReport;
import com.extracme.evcard.membership.core.dto.input.DealFeedbackInput;
import com.extracme.evcard.membership.core.input.FeedbackBaseInput;
import com.extracme.evcard.membership.core.input.FeedbackReadInput;
import com.extracme.evcard.membership.core.input.QueryFeedbackInput;
import com.extracme.evcard.membership.core.model.ReportFeedbackInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ReportFeedbackInfoMapper {

    int insert(ReportFeedbackInfo record);

    int insertSelective(ReportFeedbackInfo record);

    /**
     * 根据authId和类型查询用户反馈信息
     * @param authId
     * @param types
     * @return
     */
    List<FeedbackBaseDto> getFeedbackByAuthId(FeedbackBaseInput feedbackBaseInput);

    /**
     * 分页根据条件查询反馈信息
     * @param queryFeedbackInput
     * @return
     */
    List<FeedbackBaseDto> getFeedbackPage(QueryFeedbackInput queryFeedbackInput);

    /**
     * 更新反馈信息为已读
     * @param feedbackReadInput
     */
    void updateFeedbackRead(FeedbackReadInput feedbackReadInput);

    /**
     * 查询未读的反馈回复数量
     * @param authId
     * @param name
     * @return
     */
    int queryUnReadFeedbackReplyCount(@Param("authId") String authId, @Param("name") String name);

    /**
     * 查询用户的反馈信息的回复信息
     * @param id
     */
    List<FeedbackReplyMess> getFeedbackReplyById(@Param("id") Long id);

    /**
     * 根据主键查询用户
     * @param id
     * @return
     */
    FeedbackBaseDto queryFeedbackInfoById(@Param("id")Integer id);

    /**
     * 更新不需要处理的反馈信息
     * @param updataUndealReport
     */
    void updataUndealReport(UpdataUndealReport updataUndealReport);

}