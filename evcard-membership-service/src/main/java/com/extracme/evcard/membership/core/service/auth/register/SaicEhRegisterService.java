package com.extracme.evcard.membership.core.service.auth.register;

import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.input.NotifyLoginDto;
import com.extracme.evcard.membership.core.input.NotifyRegisterDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 上汽职工之家
 */
@Slf4j
@Service
public class SaicEhRegisterService extends AbstractRegister {

    @Value("${saic.eh.agencyId:00C1}")
    private String saicEhAgencyId;
    @Value("${saic.eh.default.roleId:369}")
    private String saicEhRoleId;


    @Override
    public String getBindAgencyId() {
        return saicEhAgencyId;
    }

    @Override
    public String getBindAgencyRoleId() {
        return saicEhRoleId;
    }

    @Override
    public String getSecondAppKey() {
        return ComUtil.SAIC_EH_SECOND_APP_KEY;
    }

    @Override
    public String getDescription() {
        return "上汽职工之家";
    }

    @Override
    public void notifyRegisterSuccess(NotifyRegisterDto notifyRegisterDto) {
        super.bindAgency(notifyRegisterDto);
    }

    @Override
    public void notifyLoginSuccess(NotifyLoginDto dto) {
        NotifyRegisterDto notifyRegisterDto  = new NotifyRegisterDto();
        BeanUtils.copyProperties(dto,notifyRegisterDto);
        super.bindAgency(notifyRegisterDto);
    }
}
