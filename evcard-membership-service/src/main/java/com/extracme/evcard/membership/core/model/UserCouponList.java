package com.extracme.evcard.membership.core.model;

import java.util.Date;

public class UserCouponList {
    private Long userCouponSeq;

    private String authId;

    private Long couponSeq;

    private String startDate;

    private String expiresDate;

    private Short status;

    private String createdTime;

    private String createdUser;

    private String updatedTime;

    private String updatedUser;

    private String couponOrigin;

    private String couponCode;

    private Integer crmUserCouponSeq;

    private Date exchangetime;

    private String remark;

    private Integer offerType;

    private String actionId;

    private Long orgSeq;

    private Long orderOrgSeq;

    private String orderSeq;

    private Double discount;

    private String originRefSeq;

    public Long getUserCouponSeq() {
        return userCouponSeq;
    }

    public void setUserCouponSeq(Long userCouponSeq) {
        this.userCouponSeq = userCouponSeq;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public Long getCouponSeq() {
        return couponSeq;
    }

    public void setCouponSeq(Long couponSeq) {
        this.couponSeq = couponSeq;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getExpiresDate() {
        return expiresDate;
    }

    public void setExpiresDate(String expiresDate) {
        this.expiresDate = expiresDate;
    }

    public Short getStatus() {
        return status;
    }

    public void setStatus(Short status) {
        this.status = status;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public String getCreatedUser() {
        return createdUser;
    }

    public void setCreatedUser(String createdUser) {
        this.createdUser = createdUser;
    }

    public String getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(String updatedTime) {
        this.updatedTime = updatedTime;
    }

    public String getUpdatedUser() {
        return updatedUser;
    }

    public void setUpdatedUser(String updatedUser) {
        this.updatedUser = updatedUser;
    }

    public String getCouponOrigin() {
        return couponOrigin;
    }

    public void setCouponOrigin(String couponOrigin) {
        this.couponOrigin = couponOrigin;
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    public Integer getCrmUserCouponSeq() {
        return crmUserCouponSeq;
    }

    public void setCrmUserCouponSeq(Integer crmUserCouponSeq) {
        this.crmUserCouponSeq = crmUserCouponSeq;
    }

    public Date getExchangetime() {
        return exchangetime;
    }

    public void setExchangetime(Date exchangetime) {
        this.exchangetime = exchangetime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getOfferType() {
        return offerType;
    }

    public void setOfferType(Integer offerType) {
        this.offerType = offerType;
    }

    public String getActionId() {
        return actionId;
    }

    public void setActionId(String actionId) {
        this.actionId = actionId;
    }

    public Long getOrgSeq() {
        return orgSeq;
    }

    public void setOrgSeq(Long orgSeq) {
        this.orgSeq = orgSeq;
    }

    public Long getOrderOrgSeq() {
        return orderOrgSeq;
    }

    public void setOrderOrgSeq(Long orderOrgSeq) {
        this.orderOrgSeq = orderOrgSeq;
    }

    public String getOrderSeq() {
        return orderSeq;
    }

    public void setOrderSeq(String orderSeq) {
        this.orderSeq = orderSeq;
    }

    public Double getDiscount() {
        return discount;
    }

    public void setDiscount(Double discount) {
        this.discount = discount;
    }

    public String getOriginRefSeq() {
        return originRefSeq;
    }

    public void setOriginRefSeq(String originRefSeq) {
        this.originRefSeq = originRefSeq;
    }
}