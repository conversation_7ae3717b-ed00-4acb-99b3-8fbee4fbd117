package com.extracme.evcard.membership.core.service;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.bean.ProducerBean;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.config.CommConfigUtil;
import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.dao.ApplyProgressMapper;
import com.extracme.evcard.membership.core.dao.DriverLicenseElementsAuthenticateLogMapper;
import com.extracme.evcard.membership.core.dao.DriverLicenseElementsAuthenticateRecordMapper;
import com.extracme.evcard.membership.core.dao.DriverLicenseElementsReauthenticateListMapper;
import com.extracme.evcard.membership.core.dao.MembershipAdditionalInfoMapper;
import com.extracme.evcard.membership.core.dao.UserOperatorLogMapper;
import com.extracme.evcard.membership.core.dto.AccountStatusDto;
import com.extracme.evcard.membership.core.dto.DrivingLicenseAuthResult;
import com.extracme.evcard.membership.core.dto.DrivingLicenseReviewResult;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.dto.UpdateReviewStatusDto;
import com.extracme.evcard.membership.core.dto.UpdateUserDto;
import com.extracme.evcard.membership.core.dto.input.MemberReviewAdditionalInput;
import com.extracme.evcard.membership.core.enums.MemOperateTypeEnum;
import com.extracme.evcard.membership.core.enums.MemReviewTypeEnum;
import com.extracme.evcard.membership.core.enums.MemberReivewOriginEnum;
import com.extracme.evcard.membership.core.exception.AuthenticationException;
import com.extracme.evcard.membership.core.input.DrivingLicenseReviewInfo;
import com.extracme.evcard.membership.core.input.SaveDriverElementsAuthenticateInput;
import com.extracme.evcard.membership.core.input.SaveDriverElementsAuthenticateLogInput;
import com.extracme.evcard.membership.core.input.SubmitDrivingLicenseInput;
import com.extracme.evcard.membership.core.input.UpdateFileNoInput;
import com.extracme.evcard.membership.core.input.UserOperationLogInput;
import com.extracme.evcard.membership.core.model.ApplyProgress;
import com.extracme.evcard.membership.core.model.DriverLicenseElementsAuthenticateLog;
import com.extracme.evcard.membership.core.model.DriverLicenseElementsAuthenticateRecord;
import com.extracme.evcard.membership.core.model.DriverLicenseElementsReauthenticateList;
import com.extracme.evcard.membership.core.model.LicenseStatusInfo;
import com.extracme.evcard.membership.core.model.MembershipAdditionalInfo;
import com.extracme.evcard.membership.core.service.license.LicenseAuthenticateProxy;
import com.extracme.evcard.membership.core.service.license.LicenseStatusChecker;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.dao.MmpUserTagMapper;
import com.extracme.evcard.membership.credit.model.MembershipBaseInfo;
import com.extracme.evcard.membership.credit.model.MembershipInfo;
import com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs;
import com.extracme.evcard.membership.invitation.service.IMemberShipInvitationServ;
import com.extracme.evcard.mq.bean.EventEnum;
import com.extracme.evcard.mq.bean.event.MemberAudit;
import com.extracme.evcard.protobuf.ProtobufUtil;
import com.extracme.evcard.redis.JedisLock;
import com.extracme.evcard.redis.JedisUtil;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.entity.HidLog;
import com.extracme.evcard.rpc.entity.LogPoint;
import com.extracme.evcard.rpc.enums.StatusCode;
import com.extracme.evcard.rpc.exception.BusinessRuntimeException;
import com.extracme.evcard.rpc.messagepush.service.IMessagepushServ;
import com.extracme.evcard.rpc.messagepush.service.ISensorsdataService;
import com.extracme.evcard.sts.rpc.service.AppConfigRpcService;
import com.extracme.framework.core.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.Jedis;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;


/**
 * 会员认证服务
 * <AUTHOR> @Discription
 * @date 2020/4/23
 * TODO 提交驾照信息&三要相关代码整理
 * TODO 驾照状态校准流程添加hidLog
 */
@Slf4j
@Service
public class MemberReviewService implements IMemberReviewService {
    @Autowired
    private UserOperatorLogMapper userOperatorLogMapper;
    @Autowired
    private MembershipInfoMapper membershipInfoMapper;
    @Autowired
    private MembershipAdditionalInfoMapper membershipAdditionalInfoMapper;
    @Autowired
    private ApplyProgressMapper applyProgressMapper;
    @Autowired
    private DriverLicenseElementsAuthenticateRecordMapper licenseElementsAuthenticateRecordMapper;
    @Autowired
    private DriverLicenseElementsAuthenticateLogMapper licenseElementsAuthenticateLogMapper;
    @Autowired
    private DriverLicenseElementsReauthenticateListMapper licenseElementsReauthenticateListMapper;

    @Resource(name = "producer")
    private ProducerBean producer;
    @Autowired
    private LicenseAuthenticateProxy licenseAuthenticateService;
    @Autowired
    IMemberShipService memberShipService;
    @Autowired
    ISensorsdataService sensorsdataService;
    @Resource
    IMessagepushServ messageServ;

    @Autowired
    private AppConfigRpcService appConfigRpcService;
    @Autowired
    private IMemberShipInvitationServ memberShipInvitationServ;

    @Autowired
    private MmpUserTagMapper mmpUserTagMapper;

    /** 三要素查验开关，默认开启，用于开发及压测环境跳过查验步骤 */
    @Value("elements_authenticate_enable:0")
    private String elementsAuthenticateEnable;

    /**
     * 三要素校验顺序
     */
    @Value("${elements.authenticate.sequence}")
    private String licenseAuthSequence;


    @Value("${elements.authenticate.deduction.supplier}")
    private String deductionSupplier;


    @Value("${newAuditPassSmsTemplateId}")
    private String ADDITION_REVIEW_SUCCESS_MSGID;

    @Value("${newAuditNoPassSmsTemplateId}")
    private String ADDITION_RE_REVIEW_FAIL_MSGID;

    @Value("${ons.raw.topic}")
    private String evcardRawDataTopic;

    @Value("${licenseCheckOkSmsTemplateId}")
    private String LICENSE_CHECK_SUCCESS_MSGID;
    @Value("${licenseCheckFailSmsTemplateId}")
    private String LICENSE_CHECK_FAIL_MSGID;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reviewPassAdditional(MemberReviewAdditionalInput memberReviewInput, UpdateUserDto operator) throws AuthenticationException {
        if (StringUtils.isBlank(memberReviewInput.getAuthId())) {
            throw new AuthenticationException(-1, "会员id不能为空");
        }
        String authId = memberReviewInput.getAuthId();
        MembershipInfo membershipInfo = membershipInfoMapper.selectMembershipInfoByAuthId(authId, null);
        if(membershipInfo == null) {
            throw new AuthenticationException(-1, "会员不存在或已注销");
        }

        //更新会员新驾照信息到会员表中
        MembershipAdditionalInfo additionalInfo = membershipAdditionalInfoMapper.selectByAuthIdAndReviewStatus(authId,0, 0);
        if (additionalInfo == null) {
            throw new AuthenticationException(-1, "会员当前无待审核的新驾照信息");
        }
        int cnt = membershipInfoMapper.updateFromAdditionalInfoByAuthId(additionalInfo);
        if(cnt == 0) {
            log.warn("会员新驾照审核失败， authId={}. ", authId);
            throw new AuthenticationException(-1, "新驾照审核失败");
        }
        //更新新驾照信息表中的记录
        MembershipAdditionalInfo updateRow = new MembershipAdditionalInfo();
        updateRow.setAuthId(authId);
        updateRow.setReviewMode(1);
        updateRow.setReviewItems(memberReviewInput.getReviewItems());
        updateRow.setUpdateOperId(operator.getUserId());
        updateRow.setUpdateOperName(operator.getUserName());
        updateRow.setReviewStatus(1);
        updateRow.setReviewTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE4));
        updateRow.setReviewUser(operator.getUserName());
        updateRow.setReviewItemIds(StringUtils.EMPTY);
        updateRow.setReviewItemName(StringUtils.EMPTY);
        updateRow.setStatus(1);
        membershipAdditionalInfoMapper.reviewAdditionalInfoByAuthId(updateRow);

        //驾照三要素状态->待审核
        if(StringUtils.isNotBlank(additionalInfo.getFileNo())) {
            //新驾照审核通过后更新三要素为待认证
            SaveDriverElementsAuthenticateInput driverElementsAuthenticateInput = new SaveDriverElementsAuthenticateInput();
            driverElementsAuthenticateInput.setUserId(membershipInfo.getPkId());
            driverElementsAuthenticateInput.setAuthenticateStatus(0); //待审核
            driverElementsAuthenticateInput.setName(membershipInfo.getName());
            driverElementsAuthenticateInput.setCreateTime(new Date());
            driverElementsAuthenticateInput.setCreateOperName(authId);
            driverElementsAuthenticateInput.setDriverCode(membershipInfo.getDriverCode());
            driverElementsAuthenticateInput.setFileNo(additionalInfo.getFileNo());
            driverElementsAuthenticateInput.setDriverLicenseImgUrl(additionalInfo.getDrivingLicenseImgUrl().substring(additionalInfo.getDrivingLicenseImgUrl().indexOf("sensitiveBucket")));
            driverElementsAuthenticateInput.setFileNoImgUrl(additionalInfo.getFileNoImgUrl().substring(additionalInfo.getFileNoImgUrl().indexOf("sensitiveBucket")));
            driverElementsAuthenticateInput.setMiscDesc("新驾照审核通过，三要素待重新认证");
            saveDriverLicenseElementsAuthenticateRecord(authId, driverElementsAuthenticateInput);
        }
        //短信推送
        String mobilePhone = membershipInfo.getMobilePhone();
        if(!ComUtil.checkIsSgmAppKey(membershipInfo.getAppKey())) {
            String smsTemplateId = ADDITION_REVIEW_SUCCESS_MSGID;
            String smsMode = CommConfigUtil.getSmsMode();
            int templateId = Integer.valueOf(smsTemplateId).intValue();
            if (StringUtils.isNotBlank(smsTemplateId) && StringUtils.equals(smsMode, "1")) {
                BaseResponse baseResponse = messageServ.asyncSendSMSTemplate(mobilePhone, templateId, operator.getUserName());
                if (baseResponse.getCode() == 0) {
                    log.info("新驾照审核通过短信通知成功，authId={}", authId);
                }
            }
            appMessagePush(authId, 3, templateId, null);
        }

        String foreignKey = authId;
        String operatorContent = "新驾照信息人工审核通过";
        ComUtil.insertOperatorLog(operatorContent, foreignKey, String.valueOf(operator.getUserId()), operator.getUserName(), userOperatorLogMapper);
        UserOperationLogInput userOperationLogInput = new UserOperationLogInput();
        userOperationLogInput.setUserId(membershipInfo.getPkId());
        userOperationLogInput.setRefKey1(authId);
        userOperationLogInput.setAuthId(authId);
        userOperationLogInput.setOperationType(MemOperateTypeEnum.REVIEW_NEW_LICENSE.getCode());
        userOperationLogInput.setOperationContent(operatorContent);
        userOperationLogInput.setOperationTime(new Date());
        userOperationLogInput.setOperator(operator.getUserName());
        userOperationLogInput.setOperatorId(operator.getUserId());
        memberShipService.saveUserOperationLog(userOperationLogInput);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DrivingLicenseReviewResult submitDrivingLicenseInfo(String authId, String appKey, SubmitDrivingLicenseInput input) throws AuthenticationException {
        log.debug("用户认证，提交驾照信息开始：authId={}, appKey={}, input={}", authId, appKey, JSON.toJSONString(input));
        long tm = System.currentTimeMillis();
        HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, "提交驾照信息开始，submitType=" + input.getSubmitType() +
                ", appKey=" + appKey + ", input=" + JSON.toJSONString(input), authId, true);
        /**
         * 1. 参数校验
         */
        if(StringUtils.isBlank(authId) || StringUtils.isBlank(input.getDriverCode()) || StringUtils.isBlank(input.getName())
            || StringUtils.isBlank(input.getFileNo()) || input.getSubmitType() == null) {
            throw BusinessRuntimeException.PARAM_EXEPTION;
        }
        //校验驾照号是否在冻结中
        String driverCode = input.getDriverCode();
        AccountStatusDto accountStatusDto = memberShipService.getAccountStatusByDriverCode(driverCode, 0);
        if (accountStatusDto != null) {
            if (accountStatusDto.getAccountStatus() == 1) {
                HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, StatusCode.DRIVER_CODE_LOGOUT_FREEZE_ERROR.getMsg(), authId, false);
                throw new AuthenticationException(StatusCode.DRIVER_CODE_LOGOUT_FREEZE_ERROR);
            }
        }
        /**
         * 2.查询用户信息
         */
        //MembershipBasicInfo membershipInfo = membershipInfoMapper.getUserBasicInfo(authId, (short)0);
        MembershipInfoWithBLOBs membershipInfo = membershipInfoMapper.selectByAuthId(authId, 0);
        if (membershipInfo == null) {
            HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, StatusCode.USER_INFO_NO_EXIST.getMsg(), authId, false);
            throw new AuthenticationException(StatusCode.USER_INFO_NO_EXIST);
        }

        /**
         * 3. 驾照数据提交
         */
        boolean isUsersInMainlandChina = ComUtil.checkIsUsersInMainlandResidents(input.getNational());
        boolean autoCheckLicenseElements = isUsersInMainlandChina && checkCityIsAutoLicenseAuth(membershipInfo.getCityOfOrigin());
        if (StringUtils.isNotBlank(input.getOfficerIdNumber())){
            autoCheckLicenseElements = false;
        }
        //数据检查与提交
        DrivingLicenseReviewResult result;
        if (input.getSubmitType() == 1) {
            //3.1 更新驾照有效期
            //驾照三要素校验，若校验不通过则不提交
            result = updateDrivingLicenseExpiredDate(authId, appKey, input, membershipInfo, autoCheckLicenseElements);
        } else if (input.getSubmitType() == 2){
            //3.2 补全档案编号
            //驾照三要素校验，若校验不通过则不提交
            result = updateUserFileNo(authId, appKey, input, membershipInfo, autoCheckLicenseElements);
        }else {
            //3.0 提交驾照认证信息(初次提交/审核不通过后再次提交/驾照过期后再次提交)
            submitDrivingLicenseForReview(authId, appKey, input, membershipInfo, autoCheckLicenseElements);
            String content = "提交驾照信息成功";
            /**
             * 4. 提交驾照信息后处理(审核认证状态变更)
             */
            //提交驾照，则做三要素校验
            result = drivingLicenseReviewAfterSubmit(authId, appKey, input, membershipInfo, autoCheckLicenseElements);
            //对于人脸认证已经通过的会员，可能集合根据三要素校验结果，确定是否需要审核通过
            //审核通过的会员
            if(membershipInfo.getReviewStatus() == 1 && membershipInfo.getAuthenticationStatus() == 2) {
                HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, "提交驾照信息-补全驾照信息，驾照信息已提交，开始审核", authId, true);
                drivingLicenseReviewAfterCompleted(authId, input, membershipInfo, result);
                HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, "提交驾照信息-补全驾照信息，驾照信息已提交，审核结束", authId, true);
            }else {
                //自动审核/审核不通过/人脸已认证
                if(result.getCode() != 0) {
                    HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, "提交驾照信息-驾照信息已提交，但三要素或驾照状态认证不通过审核状态不变更", authId, true);
                    if(membershipInfo.getAuthenticationStatus() == 2 && membershipInfo.getReviewStatus() == -1) {
                        content += "(驾照认证未通过)，审核状态由资料不全->后台自动审核不通过";
                        autoReviewFaildWhenSubmit(input, membershipInfo, result, MemberReivewOriginEnum.SYS_AUTO_REVIEW_FAILED_FACEFIRST);
                    }else {
                        content += "(驾照认证未通过)，审核状态不变更";
                        HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, "提交驾照信息-已提交(驾照认证未通过)，审核状态不变更", authId, true);
                    }
                }else {
                    //（资料不全/审核不通过）&（已认证）
                    if(membershipInfo.getAuthenticationStatus() == 2 && (membershipInfo.getReviewStatus() == 2 || membershipInfo.getReviewStatus() == -1)) {
                        //自动审核
                        if(input.getDriverLicenseInputType() == 1) {
                            content += "(驾照认证通过)，当前人脸认证已完成，系统自动审核通过";
                            //仅驾照三要素认证和驾照状态认证均通过者，需要做 机驾照自动审核通过处理
                            UpdateUserDto operator = UpdateUserDto.buildUserOperator(authId);
                            autoMemberReviewSuccess(membershipInfo, 0, operator, MemReviewTypeEnum.SYSTEM_AUTO_REVIEW);
                            HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, "提交驾照信息-已认证的审核不通过会员系统自动审核通过", authId, true);
                        }else {
                            content += "(驾照认证通过)，当前人脸认证已完成，提交人工审核";
                            //手动修改过，则若驾照认证通过则提交人工审核(仅老用户，新用户刷脸过后再变更)
                            submitManualReview(membershipInfo, "提交驾照信息");
                            HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, "提交驾照信息-已提交，审核状态改为待审核->提交人工审核", authId, true);
                        }
                    }
                    //三要素认证通过，且人脸认证状态不为已通过，则更新会员认证状态为未刷脸
                    else if(membershipInfo.getAuthenticationStatus() == 0) {
                        content += "(驾照认证通过)，当前人脸认证尚未完成，待人脸认证";
                        MembershipInfo updateRow = new MembershipInfo();
                        updateRow.setAuthId(membershipInfo.getAuthId());
                        updateRow.setMembershipType((short)0);
                        updateRow.setAuthenticationStatus(1);
                        membershipInfoMapper.updateReviewStatusByAuthId(updateRow);
                        HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, "提交驾照信息-已提交，审核状态不变更，认证状态改未刷脸->后续进入刷脸", authId, true);
                    }
                }
                //保存操作日志
                ComUtil.insertOperatorLog(content, authId, null, authId, userOperatorLogMapper);
                UserOperationLogInput userOperationLogInput = new UserOperationLogInput();
                userOperationLogInput.setOperationType(MemOperateTypeEnum.UPDATE_FILE_NO.getCode());
                userOperationLogInput.setOperationContent(content);
                userOperationLogInput.setOperationTime(new Date());
                userOperationLogInput.setOperator(authId);
                userOperationLogInput.setRefKey1(authId);
                userOperationLogInput.setUserId(membershipInfo.getPkId());
                userOperationLogInput.setAuthId(authId);
                memberShipService.saveUserOperationLog(userOperationLogInput);
                HidLog.membership(LogPoint.UPDATE_DRIVER_FILE_NO, content, authId, true);
            }
        }
        HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, "提交驾照信息结束，submitType=" + input.getSubmitType(), authId, true);
        long costSeconds = (System.currentTimeMillis() - tm );
        log.debug("用户认证，提交驾照信息：authId={}, appKey={}, input={}, cost={}", authId, appKey, JSON.toJSONString(input), costSeconds);
        return result;
    }


    public void drivingLicenseReviewAfterCompleted(String authId, SubmitDrivingLicenseInput input,
                                                   MembershipInfoWithBLOBs membershipInfo, DrivingLicenseReviewResult result) throws AuthenticationException {
        /**
         * 审核通过已认证状态下，提交驾照视为 补全驾照
         * 1. 外籍或三要素认证未开启或三要素验证通过且驾照认证通过  -> 若ocr则保持审核通过已认证 / 若手工修改则提交人工审核
         * 2. 三要素查验中  ->  三要素查验中，驾照认证状态待认证；保持审核通过已认证
         *                    【确认】查验中时要求新用户不允许用车，老用户允许用车，因此此处不能先提交待审核
         * 3. 三要素验证不通过或驾照认证不通过  -> 系统自动审核不通过
         * 4. 需要查验三要素，但信息不完整 此次补全失败
         */
        boolean autoReviewFailed = false;
        String content = "补全驾照信息成功";
        if(membershipInfo.getReviewStatus() != 1 || membershipInfo.getAuthenticationStatus() != 2) {
            log.error("提交驾照信息-补全驾照信息，但状态不为审核通过已认证， authenticationStatus={}", membershipInfo.getAuthenticationStatus());
            HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, "提交驾照信息-补全驾照信息，状态不为审核通过已认证->不做审核操作", authId, false);
        }else {
            //1. 外籍或三要素认证已经未启，或三要素验证通过且驾照认证通过
            if(result.getCode() == 0) {
                //自动审核-》保持审核通过状态不变化
                if (input.getDriverLicenseInputType() == 1) {
                    content += "验证通过或无需验证，保持审核通过已认证";
                    HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, "提交驾照信息-补全驾照信息，三要素及驾照状态认证通过->保持审核通过", authId, true);
                } else {
                    content += "，提交人工审核";
                    //手动修改过，则若驾照认证通过则提交人工审核(仅老用户，新用户刷脸过后再变更)
                    submitManualReview(membershipInfo, "提交驾照信息-补全驾照信息");
                    HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, "提交驾照信息-补全驾照信息，审核状态改为待审核->提交人工审核", authId, true);
                }
            }
            //2. 三要素查验中
            else if(result.getCode() == StatusCode.LICENSE_ELEMENTS_AUTH_ING.getCode()) {
                content += "，三要素查验中，保持审核通过已认证";
                HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, "提交驾照信息-补全驾照信息，三要素查验中->保持审核通过", authId, true);
            }
            //3. 三要素认证不一致或驾照认证不通过
            else if(result.getCode() == StatusCode.LICENSE_ELEMENTS_AUTH_FAILED.getCode()
                    || result.getCode() == StatusCode.LICENSE_STATUS_INVALID.getCode()) {
                autoReviewFailed = true;
                content += "，系统自动审核不通过(驾照认证失败)";
                autoReviewFaildWhenSubmit(input, membershipInfo, result, MemberReivewOriginEnum.SYS_AUTO_REVIEW_FAILED_SUPPLY);
            }
            else{
                HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, "提交驾照信息-补全驾照信息，补全失败，原因：认证结果为 " + result.getMessage(), authId, false);
                throw new AuthenticationException(StatusCode.SUBMIT_FAILURE);
            }
        }
        if(!autoReviewFailed) {
            //保存操作日志
            ComUtil.insertOperatorLog(content, authId, null, authId, userOperatorLogMapper);
            UserOperationLogInput userOperationLogInput = new UserOperationLogInput();
            userOperationLogInput.setOperationType(MemOperateTypeEnum.UPDATE_FILE_NO.getCode());
            userOperationLogInput.setOperationContent(content);
            userOperationLogInput.setOperationTime(new Date());
            userOperationLogInput.setOperator(authId);
            userOperationLogInput.setRefKey1(authId);
            userOperationLogInput.setUserId(membershipInfo.getPkId());
            userOperationLogInput.setAuthId(authId);
            memberShipService.saveUserOperationLog(userOperationLogInput);
            HidLog.membership(LogPoint.UPDATE_DRIVER_FILE_NO, content, authId, true);
        }
    }


    public void autoReviewFaildWhenSubmit(SubmitDrivingLicenseInput input, MembershipInfoWithBLOBs membershipInfo,
                                          DrivingLicenseReviewResult result, MemberReivewOriginEnum reviewOrigin) {
        String authId = membershipInfo.getAuthId();
        //自动审核不通过
        LicenseStatusInfo licenseStatusInfo = LicenseStatusChecker.check(input.getDrivingLicenseType(),
                input.getExpirationDate(), result.getLicenseStatusMsg());
        DrivingLicenseReviewInfo licenseReviewInfo = new DrivingLicenseReviewInfo();
        licenseReviewInfo.setLicenseStatusMsg(result.getLicenseStatusMsg());
        licenseReviewInfo.setLicenseStatus(result.getLicenseStatus());
        licenseReviewInfo.setAuthenticateStatus(result.getAuthenticateStatus());
        licenseReviewInfo.setElementsReviewItems(result.getElementsReviewItems());
        //0驾照认证不通过 1三要素认证不通过 2二者均不通过
        Integer reviewFailedCause = null;
        if(!licenseStatusInfo.isValid()) {
            reviewFailedCause = 0;
        }
        if(licenseReviewInfo.getAuthenticateStatus() != 1) {
            reviewFailedCause = (reviewFailedCause == null) ? 1 : 2;
        }
        if(reviewFailedCause != null) {
            autoMemberReviewFailed(membershipInfo, licenseReviewInfo, licenseStatusInfo, MemberReivewOriginEnum.SYS_AUTO_REVIEW_FAILED_SUPPLY, reviewFailedCause);
            HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, "提交驾照信息-" +
                    reviewOrigin.getOrigin() + "，三要素或驾照状态认证不通过->系统自动审核不通过", authId, true);
        }
    }

    public void submitManualReview(MembershipInfo membershipInfo, String origion) {
        String authId = membershipInfo.getAuthId();
        MembershipInfo updateRow = new MembershipInfo();
        updateRow.setAuthId(membershipInfo.getAuthId());
        updateRow.setMembershipType((short)0);
        updateRow.setReviewStatus((short)0);
        updateRow.setReviewItems(REVIEW_ITEMS_DEFUFALT);
        updateRow.setReviewItemIds(StringUtils.EMPTY);
        updateRow.setReviewItemName(StringUtils.EMPTY);
        updateRow.setReviewMode(1);
        updateRow.setAppReviewTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE4));
        membershipInfoMapper.updateReviewStatusByAuthId(updateRow);
        HidLog.membership(LogPoint.UPDATE_USER_REVIEW_STATUS, "提交人工待审核-" + origion, authId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Deprecated
    public DrivingLicenseReviewResult refreshDrivingLicenseStatus(String authId, String appKey) throws AuthenticationException {
        return null;
//        log.debug("校准会员驾照审核状态：开始，authId={}, appKey={}.", authId, appKey);
//        MembershipInfoWithBLOBs membershipInfo = membershipInfoMapper.selectByAuthId(authId, 0);
//        if(membershipInfo == null) {
//            log.error("校准会员驾照审核状态：获取会员驾照信息失败，会员不存在, authId={}", authId);
//            return null;
//        }
//        DrivingLicenseReviewResult resp = refreshDrivingLicenseStatus(membershipInfo, appKey, MemberReivewOriginEnum.SYS_AUTO_REVIEW_FAILED_LOGIN);
//        log.debug("校准会员驾照审核状态：结束，authId={}, result={}.", authId, JSON.toJSON(resp));
//        return resp;
    }

    /**
     * 会员驾照状态更新
     * @param membershipInfo
     * @param appKey
     * @param operateType
     *        4自动审核不通过-登录校准
     *        5自动审核不通过-后台查无校准
     * @return
     * @remark 登录及查无定时任务使用。
     * @throws AuthenticationException
     */
    @Transactional(rollbackFor = Exception.class)
    public DrivingLicenseReviewResult refreshDrivingLicenseStatus(MembershipInfoWithBLOBs membershipInfo, String appKey, MemberReivewOriginEnum operateType) throws AuthenticationException {
        String authId = membershipInfo.getAuthId();
        HidLog.membership(BussinessConstants.MEM_RVIEW_CORRECT, "call refreshDrivingLicenseStatus，appKey=" + appKey + "，type=" + operateType.getOrigin(), authId, true);
        boolean isReAuthenticate = (operateType.equals(MemberReivewOriginEnum.SYS_AUTO_REVIEW_FAILED_RETRY));
        String operation = operateType.getOrigin();
        int reviewStatus = membershipInfo.getReviewStatus().intValue();
        int authenticationStatus = membershipInfo.getAuthenticationStatus().intValue();
        Integer licenseElementsAuthStatus = membershipInfo.getLicenseElementsAuthStatus();
        DrivingLicenseReviewResult result = new DrivingLicenseReviewResult();
        result.setAuthenticateStatus(licenseElementsAuthStatus);
        result.setLicenseStatus(membershipInfo.getLicenseAuthStatus());
        result.setLicenseStatusMsg(membershipInfo.getLicenseStatusMsg());
        result.setElementsReviewItems(membershipInfo.getElementsReviewItems());
        //1. 登录/查无时， 仅对会员审核状态为通过的会员做驾照三要素的校准。
        if(reviewStatus != 1 && !(reviewStatus == 0 && authenticationStatus == 2)) {
            log.warn("校准会员驾照状态：会员审核未通过(且不为待审核已认证)，不做校准, authId={}, reviewStatus={}, authenticationStatus={}",
                    authId, reviewStatus, authenticationStatus);
            return result;
        }
        if(licenseElementsAuthStatus == 3 && !isReAuthenticate) {
            log.warn("校准会员驾照状态：登录时若用户当前三要素为查无状态，则不做校准, authId={}, reviewStatus={}, authenticationStatus={}",
                    authId, reviewStatus, authenticationStatus);
            return result;
        }
        //ADD. 校准驾照状态时，若准驾车型或驾照到期日期为空-》 不对审核状态做校准
        if(StringUtils.isBlank(membershipInfo.getDrivingLicenseType()) || StringUtils.isBlank(membershipInfo.getLicenseExpirationTime())) {
            log.warn("校准会员驾照状态：准驾车型或过期日期缺失，不做校准, 优先由app提示补全, authId={}", authId);
            HidLog.membership(BussinessConstants.MEM_RVIEW_CORRECT, "校准驾照状态结束，准驾车型或过期日期缺失，不做校准，type="
                    + operateType.getOperate(), authId, false);
            return result;
        }
        //ADD. 校准驾照状态时，若档案编号或驾照副本为空-》 不对审核状态做校准
        if(StringUtils.isBlank(membershipInfo.getFileNo()) || StringUtils.isBlank(membershipInfo.getFileNoImgUrl())) {
            log.warn("校准会员驾照状态：档案编号或驾照副页缺失，不做校准, 优先由app提示补全, authId={}", authId);
            HidLog.membership(BussinessConstants.MEM_RVIEW_CORRECT, "校准驾照状态结束，档案编号或驾照副页缺失，不做校准，type="
                    + operateType.getOperate(), authId, false);
            return result;
        }
        //2. 会员审核状态通过，判断驾照三要素认证状态
        HidLog.membership(BussinessConstants.MEM_RVIEW_CORRECT, "校准驾照状态开始，appKey=" + appKey + "，type=" + operateType.getOrigin(), authId, true);

        Jedis jedis = null;
        JedisLock jedisLock = null;
        try {
            jedis = JedisUtil.getJedis();
            jedisLock = new JedisLock("refreshLicAuth-" + operateType.getCode() + "_" + authId);
            if (!jedisLock.acquire(jedis)) {
                HidLog.membership(LogPoint.MEM_RVIEW_CORRECT, "校准驾照状态中止，已存在进行中的查验，appKey=" + appKey + "，type=" + operateType.getOrigin(), authId, false);
                //throw new RegisterException(StatusCode.SYSTEM_ERROR);
                result.setCode(30069);
                result.setMessage("驾照三要素查验中");
                return result;
            }

            UpdateUserDto operator = UpdateUserDto.buildSysOperator(operation);
            boolean isUsersInMainlandChina = ComUtil.checkIsUsersInMainlandResidents(membershipInfo.getNational());
            boolean autoCheckLicenseElements = checkCityIsAutoLicenseAuth(membershipInfo.getCityOfOrigin());
            //2.1 驾照三要素有且一致
            if (licenseElementsAuthStatus == 1) {
                log.debug("校准会员驾照状态：审核状态通过，三要素认证已通过，校验会员驾照有效状态, authId={}", authId);
                //检查驾照有效状态, 此处需要校验待查验项目。
                //根据最新的有效期车型检查，及当前的licenseStatusMsg综合得到驾照认证信息
                LicenseStatusInfo licenseStatusInfo = LicenseStatusChecker.check(membershipInfo.getDrivingLicenseType(),
                        membershipInfo.getLicenseExpirationTime(), membershipInfo.getLicenseStatusMsg());
                String licenseStatusMsg = licenseStatusInfo.getLicenseStatusMsg();
                Integer licenseStatus = licenseStatusInfo.getLicenseStatus();
                result.setLicenseStatus(licenseStatus);
                result.setLicenseStatusMsg(licenseStatusMsg);
                if (licenseStatusInfo.isValid()) {
                    log.debug("校准会员驾照状态：三要素认证通过，驾照状态检查结果有效->更新会员驾照状态为有效, authId={}", authId);
                    if (!NumberUtils.INTEGER_ONE.equals(membershipInfo.getLicenseAuthStatus())) {
                        log.warn("校准会员驾照状态：三要素认证通过，驾照状态检查结果有效->更新会员驾照状态为有效, authId={}", authId);
                        //更新驾照认证状态为可用，更新审核状态为通过
                        updateMemberLicenseStatus(authId, null, null, 1, StringUtils.EMPTY, UpdateUserDto.SYSTEM);
                    }
                } else {
                    log.warn("校准会员驾照状态：三要素认证通过，会员驾照状态校验无效, authId={}, licenseStatusInfo={}，licType={}, expireDate={}, statusMsg={}",
                            authId, JSON.toJSONString(licenseStatusInfo), membershipInfo.getDrivingLicenseType(),
                            membershipInfo.getLicenseExpirationTime(), membershipInfo.getLicenseStatusMsg());
                    //将会员自动审核为不通过， cause为驾照状态不可用
                    DrivingLicenseReviewInfo licenseReviewInfo = new DrivingLicenseReviewInfo();
                    licenseReviewInfo.setLicenseStatus(licenseStatus);
                    licenseReviewInfo.setLicenseStatusMsg(licenseStatusMsg);
                    licenseReviewInfo.setAuthenticateStatus(null);
                    autoMemberReviewFailed(membershipInfo, licenseReviewInfo, licenseStatusInfo, operateType, 0);
                }
                return result;
            }
            //2.2 驾照三要素待认证/查无/不通过，则尝试重新认证。
            log.debug("校准会员驾照状态：三要素当前非认证通过，尝试重新做校验, authId={}, licenseElementsAuthStatus={}", authId, licenseElementsAuthStatus);
            //非大陆用户，无需做驾照三要素校验
            if (!isUsersInMainlandChina) {
                log.debug("校准会员驾照审核状态：非本籍不自动校验三要素, authId={}", authId);
                return result;
            }
            //大陆用户，生成待认证记录--》驾照三要素认证(if 所属城市开启自动验证)/下单时校验
            if (licenseElementsAuthStatus != 0 && licenseElementsAuthStatus != 3) {
                //生成待认证记录
                saveDriverLicenseElementAuthRecord(membershipInfo, appKey, operator);
            }
            //会员所属城市未开启自动校验驾照三要素，则不做后续操作
            if (!autoCheckLicenseElements) {
                log.debug("校准会员驾照状态：城市未开启自动校验三要素，不自动校验三要素, authId={}, city={}", authId, membershipInfo.getCityOfOrigin());
                return result;
            }
            log.info("校准会员驾照状态：城市已开启自动校验三要素，自动开始校验三要素, authId={}, city={}", authId, membershipInfo.getCityOfOrigin());
            //3. 调用驾照三要素认证接口，进行认证
            DrivingLicenseAuthResult authResult = drivingLicenseAuthenticate(authId, isReAuthenticate, operator);
            if (authResult == null) {
                log.warn("校准会员驾照状态：驾照三要素验证失败，不满足校验条件, authId={}, authResult=null", authId);
                //TODO LICENSE_STATUS_FAILED 三要素认证失败，不符合认证条件
                result.setCode(30069);
                result.setMessage("不符合驾照认证条件");
                return result;
            }
            log.info("校准会员驾照状态：重新校验三要素，authId={}, 结果={}", authId, JSON.toJSONString(authResult));
            BeanCopyUtils.copyProperties(authResult, result);
            //3.1 三要素认证结果-->查无记录
            if (authResult.getAuthenticateStatus() == 3) {
                //查无，提示用户驾照正在验证中[您的驾照验证中，暂不可用车，如有疑问，请联系客服]
                result.buildErrorCode(StatusCode.LICENSE_ELEMENTS_AUTH_ING);
                //修改会员驾照三要素认证状态为查证中，其余状态不变更
                updateMemberLicenseStatus(authId, authResult.getAuthenticateStatus(), authResult.getElementsReviewItems(),
                        null, null, UpdateUserDto.buildUserOperator(authId));
                HidLog.membership(LogPoint.MEMBER_AUTH_FAIL, "三要素认证查无记录", authId, true);
                return result;
            }
            //判断驾照有效状态
            LicenseStatusInfo licenseStatusInfo = LicenseStatusChecker.check(membershipInfo.getDrivingLicenseType(),
                    membershipInfo.getLicenseExpirationTime(), authResult.getLicenseStatusMsg());
            String licenseStatusMsg = licenseStatusInfo.getLicenseStatusMsg();
            int licenseStatus = licenseStatusInfo.getLicenseStatus();
            result.setLicenseStatus(licenseStatus);
            result.setLicenseStatusMsg(licenseStatusMsg);
            //4.综合驾照三要素验证结果与驾照有效状态
            Integer reviewFailedCause = null;
            if (!licenseStatusInfo.isValid()) {
                reviewFailedCause = 0;
                HidLog.membership(BussinessConstants.MEM_RVIEW_CORRECT, "驾照状态不符合要求", authId, true);
                //result.buildErrorCode(licenseCheckResult);
            }
            if (authResult.getAuthenticateStatus() == 1) {
                //认证通过，则返回正常code
                HidLog.membership(BussinessConstants.MEM_RVIEW_CORRECT, "三要素认证一致", authId, true);
                if (licenseStatus == 1) {
                    //4.1 三要素验证通过&驾照状态认证通过 -> 更新三要素认证状态及驾照认证状态
                    HidLog.membership(BussinessConstants.MEM_RVIEW_CORRECT, "更新三要素认证状态及驾照认证状态为通过", authId, true);
                    updateMemberLicenseStatus(authId, 1, StringUtils.EMPTY, 1, StringUtils.EMPTY, operator);
                }
            } else if (authResult.getAuthenticateStatus() == 2) {
                log.info("校准会员驾照状态：重新校验三要素，结果不通过，变更会员审核状态为不通过，authId={}.", authId);
                reviewFailedCause = (reviewFailedCause == null) ? 1 : 2;
                //不一致，提示用户重新上传驾照信息
                //TODO 是否组织综合信息，此处是优先提示三要素认证不一致
                result.buildErrorCode(StatusCode.LICENSE_ELEMENTS_AUTH_FAILED);
                HidLog.membership(BussinessConstants.MEM_RVIEW_CORRECT, "三要素认证不一致", authId, true);
            }
            //三要素验证不通过或驾照状态认证不通过时，需要变更会员审核状态
            if (reviewFailedCause != null) {
                DrivingLicenseReviewInfo licenseReviewInfo = new DrivingLicenseReviewInfo();
                licenseReviewInfo.setLicenseStatusMsg(licenseStatusMsg);
                licenseReviewInfo.setLicenseStatus(licenseStatus);
                licenseReviewInfo.setAuthenticateStatus(authResult.getAuthenticateStatus());
                licenseReviewInfo.setElementsReviewItems(authResult.getElementsReviewItems());
                //4.2 三要素验证不通过或驾照状态认证不通过->系统自动审核不通过
                HidLog.membership(BussinessConstants.MEM_RVIEW_CORRECT, "三要素验证不通过或驾照状态认证不通过->系统自动审核不通过", authId, true);
                log.warn("校准会员驾照状态：三要素认证不通过或驾照状态认证不通过, authId={}, licenseReviewInfo={}",
                        authId, JSON.toJSONString(licenseReviewInfo));
                //将会员自动审核为不通过， cause为驾照状态不可
                autoMemberReviewFailed(membershipInfo, licenseReviewInfo, licenseStatusInfo, operateType, reviewFailedCause);
            }
        }finally {
            if (jedisLock != null && jedis != null) {
                jedisLock.releaseLua(jedis);
                jedis.close();
                log.debug("校准会员驾照状态-释放redis锁和链接成功");
            }
        }
        HidLog.membership(BussinessConstants.MEM_RVIEW_CORRECT, "校准驾照状态完成，appKey=" + appKey + "，type=" + operateType.getOrigin()
                + ", result=" + JSON.toJSONString(result), authId, true);
        return result;
    }


    /**
     * 查无-三要素校验重试
     * @param membershipInfo
     * @return
     * @remark 查无定时任务使用。
     * @throws AuthenticationException
     */
    @Transactional(rollbackFor = Exception.class)
    public DrivingLicenseReviewResult reAuthenticateDrivingLicense(MembershipInfoWithBLOBs membershipInfo, Integer timeout) throws AuthenticationException {
        String authId = membershipInfo.getAuthId();
        String operation = MemberReivewOriginEnum.SYS_AUTO_REVIEW_FAILED_RETRY.getOrigin();
        Integer licenseElementsAuthStatus = membershipInfo.getLicenseElementsAuthStatus();
        DrivingLicenseReviewResult result = new DrivingLicenseReviewResult();
        result.setAuthenticateStatus(licenseElementsAuthStatus);
        result.setLicenseStatus(membershipInfo.getLicenseAuthStatus());
        result.setLicenseStatusMsg(membershipInfo.getLicenseStatusMsg());
        result.setElementsReviewItems(membershipInfo.getElementsReviewItems());
        if(licenseElementsAuthStatus != 3) {
            log.warn("驾照三要素查无job：用户三要素最新状态非查证中, 不做重新验证，authId={}, licenseElementsAuthStatus={}",
                    membershipInfo.getAuthId(), licenseElementsAuthStatus);
            return result;
        }
        //1. 会员审核状态通过，判断驾照三要素认证状态
        HidLog.membership(BussinessConstants.MEM_RVIEW_CORRECT_JOB, "开始重新校验三要素", authId, true);
        log.info("驾照三要素查无job：开始重新校验三要素, authId={}, city={}", authId, membershipInfo.getCityOfOrigin());
        UpdateUserDto operator = UpdateUserDto.buildSysOperator(operation);
        //3. 调用驾照三要素认证接口，进行认证
        DrivingLicenseAuthResult authResult = drivingLicenseAuthenticate(authId, true, operator, timeout);
        HidLog.membership(BussinessConstants.MEM_RVIEW_CORRECT_JOB, "重新校验三要素完成， result=" +
                JSON.toJSONString(authResult), authId, true);
        log.info("驾照三要素查无job：重新校验三要素，authId={}, 结果={}", authId, JSON.toJSONString(authResult));
        if(authResult == null) {
            log.warn("驾照三要素查无job：驾照三要素重新验证失败，不满足校验条件, authId={}, authResult=null", authId);
            result.setCode(30069);
            result.setMessage("不符合驾照认证条件");
            return result;
        }
        BeanCopyUtils.copyProperties(authResult, result);
        //3.1 三要素认证结果
        if(authResult.getAuthenticateStatus() == 3) {
            result.buildErrorCode(StatusCode.LICENSE_ELEMENTS_AUTH_ING);
            HidLog.membership(BussinessConstants.MEM_RVIEW_CORRECT_JOB, "重新校验三要素完成，仍无结果。", authId, true);
        }
        else if(authResult.getAuthenticateStatus() == 2) {
            result.buildErrorCode(StatusCode.LICENSE_ELEMENTS_AUTH_FAILED);
            HidLog.membership(BussinessConstants.MEM_RVIEW_CORRECT_JOB, "三要素认证一致", authId, true);
        }
        else{
            HidLog.membership(BussinessConstants.MEM_RVIEW_CORRECT_JOB, "三要素认证不一致", authId, true);
        }
        return result;
    }


    private static final String[] REVIEW_ITEMS_NAMES = {"姓名","驾驶证号","驾驶证照片","准驾车型","初次领证日期",
            "驾照到期日期","邮寄地址", "身份证件照片","手持身份证件照片","驾驶证档案编号","身份证件编号","人脸照片"};
    //cause: 0驾照状态不可用 1驾照三要素认证不一致，  2三要素认证不一致&状态不可用
    public static final int[] REVIEW_ITEMS_ELEMENTS_INDEX = {1, 0, 9};
    public static final String REVIEW_ITEMS_ELEMENTS_AUTH = "222111111211";
    public static final String REVIEW_ITEMS_SUCCESS = "111111111111";
    public static final String REVIEW_ITEMS_DEFUFALT = "000000000000";
    public static final String REVIEW_ITEMS_LICENSE_PIC = "112111111111";
    //变更来源operateType： 1自动审核通过  2人工审核通过 3人工审核不通过 4自动审核不通过-登录校准 5自动审核不通过-后台查无校准
    public void autoMemberReviewFailed(MembershipInfo member, DrivingLicenseReviewInfo reviewInfo,
                                       LicenseStatusInfo licenseStatusInfo, MemberReivewOriginEnum operateType, int cause) {
        String authId = member.getAuthId();
        HidLog.membership(LogPoint.UPDATE_USER_REVIEW_STATUS, "更新用户审核状态开始-自动不通过", authId, true);
        /**
         * 会员为本籍&会员所属城市开启了自动三要素认证
         * 自动审核不通过，出现场景
         * 1. 审核状态通过的会员
         *    登录时刷新认证状态，因驾照三要素认证不通过或驾照认证不通过--》 从而审核状态由通过变为不通过
         *
         * 2. 查无定时任务，若三要素认证不通过或驾照认证不通过--》 从而审核状态由通过变为不通过
         */
        UpdateUserDto operator = UpdateUserDto.buildSysOperator(operateType.getOrigin());
        UpdateReviewStatusDto updateReviewStatus = new UpdateReviewStatusDto();
        BeanCopyUtils.copyProperties(reviewInfo, updateReviewStatus);
        updateReviewStatus.setAuthId(member.getAuthId());
        updateReviewStatus.setOperateType(operateType.getCode());
        updateReviewStatus.setOperatorContent(reviewInfo.getOperatorContent());
        //重置驾照输入方式
        updateReviewStatus.setDriverLicenseInputType(0);
        //组织审核不通过项
        String reviewRemark = StringUtils.EMPTY;
        char[] reviewItems = REVIEW_ITEMS_SUCCESS.toCharArray();
        //驾照三要素不一致
        if(cause == 1 || cause == 2) {
            int elementsCnt = REVIEW_ITEMS_ELEMENTS_INDEX.length;
            if(StringUtils.isNotBlank(reviewInfo.getElementsReviewItems()) && reviewInfo.getElementsReviewItems().length() == elementsCnt) {
                for(int i = 0; i < elementsCnt; i ++) {
                    if(reviewInfo.getElementsReviewItems().charAt(i) == '0') {
                        reviewItems[REVIEW_ITEMS_ELEMENTS_INDEX[i]] = '2';
                    }
                }
            }
            reviewRemark = "驾照三要素认证不通过";
        }
        //处理驾照状态
        if(!licenseStatusInfo.isValid()) {
            if(licenseStatusInfo.isInvalidExpireDate()) {
                reviewItems[5] = '2';
            }
            if(licenseStatusInfo.isInvalidLicenseType()) {
                reviewItems[3] = '2';
            }
            if(licenseStatusInfo.isInvalidLicenseStatus()) {
                //驾驶证当前状态不可用
                reviewItems[1] = '2';
            }
            if(StringUtils.isNotBlank(reviewRemark)){
                reviewRemark += '/';
            }
            reviewRemark += "驾照认证不通过(" + licenseStatusInfo.getLicenseStatusMsg() + ")";
        }
        String reviewItemStr = String.valueOf(reviewItems);
        if(StringUtils.equals(reviewItemStr, REVIEW_ITEMS_SUCCESS)) {
            reviewItemStr = REVIEW_ITEMS_LICENSE_PIC;
        }
        updateReviewStatus.setReviewItems(reviewItemStr);
        updateReviewStatus.setReviewRemark(reviewRemark);
        updateReviewStatus.setLicenseStatusMsg(licenseStatusInfo.getLicenseStatusMsg());
        //updateReviewStatus.setReviewItemIds();
        //updateReviewStatus.setReviewItemName();
        updateMemberReviewNoPass(member, updateReviewStatus, operator, MemReviewTypeEnum.SYSTEM_AUTO_REVIEW);
        log.warn("自动审核会员不通过， authId={}, cause={}", member.getAuthId(), reviewInfo.getOperatorContent());
        HidLog.membership(LogPoint.UPDATE_USER_REVIEW_STATUS, "更新用户审核状态结束", authId, true);
    }

    public void updateMemberReviewNoPass(MembershipInfo member, UpdateReviewStatusDto updateReviewStatusDto,
                                                 UpdateUserDto operator, MemReviewTypeEnum operateType) {
        //1. 更新审核状态
        String authId = member.getAuthId();
        String mobilePhone = member.getMobilePhone();
        String operatorName = operator.getUserName();
        Integer reviewMode = 2;
        if(operateType.equals(MemReviewTypeEnum.MANUAL_REVIEW)) {
            reviewMode = 1;
        }
        // 原驾照输入方式
        Integer driverLicenseInputType = updateReviewStatusDto.getDriverLicenseInputType();
        // 更新会员审核不通过原因
        MembershipInfo updateMember = new MembershipInfo();
        updateMember.setAuthId(member.getAuthId());
        updateMember.setMembershipType((short)0);
        updateMember.setReviewStatus((short)2);
        updateMember.setAuthenticationStatus(updateReviewStatusDto.getAuthenticationStatus());
        updateMember.setLicenseElementsAuthStatus(updateReviewStatusDto.getAuthenticateStatus());
        updateMember.setLicenseAuthStatus(updateReviewStatusDto.getLicenseStatus());
        updateMember.setLicenseStatusMsg(updateReviewStatusDto.getLicenseStatusMsg());
        updateMember.setReviewItemIds(updateReviewStatusDto.getReviewItemIds());
        updateMember.setReviewItems(updateReviewStatusDto.getReviewItems());
        updateMember.setReviewItemName(updateReviewStatusDto.getReviewItemName());
        updateMember.setReviewRemark(updateReviewStatusDto.getReviewRemark());
        updateMember.setReviewUser(operator.getUserName());
        updateMember.setReviewMode(reviewMode);
        updateMember.setReviewTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE4));
        updateMember.setUpdatedUser(operatorName);
        updateMember.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE4));
        updateMember.setAppReviewTime(updateReviewStatusDto.getAppReviewTime());
        membershipInfoMapper.updateReviewStatusByAuthId(updateMember);

        //2. 更新账号申请进度
        String reviewDesc = buildReviewDetailStr(updateReviewStatusDto.getReviewItems());
        ApplyProgress progress = new ApplyProgress();
        progress.setAuthId(authId);
        progress.setProgressContent("审核未通过，" + reviewDesc + "有误");
        progress.setCreatedUser(operator.getUserName());
        progress.setProgressTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE1));
        applyProgressMapper.addApplyProgress(progress);

        //3.1 会员审核事件推送
        MemberAudit audit = new MemberAudit();
        audit.setAuthId(authId);
        audit.setMobilePhone(mobilePhone);
        audit.setOptUser(operatorName);
        audit.setNewUser(-1);//0：老用户 1：新用户 -1：未知
        audit.setReviewStatus("2");
        //1.人工 2自动
        audit.setReviewMode(reviewMode);
        audit.setReviewRemark(updateReviewStatusDto.getReviewRemark());
        //推送审核不通过事件
        sendMQMemberAuditEvent(audit);
        //3.4 埋点
        //trackMemberReviewResult(authId, member, updateReviewStatusDto);
        String operation = operator.getRemark() + "-" + operateType.getOperate();
        //添加日志埋点
        HidLog.membership(LogPoint.MEMBER_AUTH_FAIL, operation + "不通过", authId);
        // 添加操作日志
        String operatorContent = operation + "不通过，原因：" + updateReviewStatusDto.getReviewRemark();
        if(updateReviewStatusDto.getDriverLicenseInputType() != null) {
            operatorContent += " , 驾照输入方式" + getLicenseInputTypeDesc(driverLicenseInputType)
                    + "->" + getLicenseInputTypeDesc(updateReviewStatusDto.getDriverLicenseInputType());
        }
        ComUtil.insertOperatorLog(operatorContent, authId, null, operatorName, userOperatorLogMapper);
        UserOperationLogInput userOperationLogInput = new UserOperationLogInput();
        userOperationLogInput.setOperationType(MemOperateTypeEnum.REVIEW.getCode());
        userOperationLogInput.setOperationContent(operatorContent);
        userOperationLogInput.setOperationTime(new Date());
        userOperationLogInput.setOperator(operatorName);
        userOperationLogInput.setOperatorId(operator.getUserId());
        userOperationLogInput.setRefKey1(authId);
        userOperationLogInput.setUserId(member.getPkId());
        userOperationLogInput.setAuthId(authId);
        memberShipService.saveUserOperationLog(userOperationLogInput);

        log.warn("变更会员审核状态成功， authId={}， reviewInput={}, operation={}", authId, JSON.toJSONString(updateReviewStatusDto), operateType.getOperate());

        /**
         * 三要素认证不通过，系统自动审核不通过埋点
         */
        try {
            String[] reviewArray = new String[]{"资料不全", "待审核", "审核通过", "审核不通过", "用户无效", "重新审核"};
            Map<String, Object> map = new HashMap<>();
            map.put("type", "自动审核");
            map.put("is_success", false);
            map.put("reason", updateReviewStatusDto.getReviewRemark());
            map.put("duration", 0);
            map.put("desc", reviewArray[member.getReviewStatus() + 1]);
            sensorsdataService.track(authId, true, "verify_completed", map);
        } catch (Exception e) {
            log.error(authId +"三要素验证不通过，自动审核不通过埋点异常", e);
        }
    }

    ////0：老用户 1：新用户 -1：未知
    public void autoMemberReviewSuccess(MembershipInfo member, int isNewUser, UpdateUserDto operator, MemReviewTypeEnum operateType) throws AuthenticationException{
        //1. 更新审核状态
        int reviewStatus = BussinessConstants.REVIEW_SUCCESS;
        String authId = member.getAuthId();
        String mobilePhone = member.getMobilePhone();
        String operatorName = operator.getUserName();
        String reviewRemark = operator.getRemark();
        if(StringUtils.isBlank(reviewRemark)) {
            reviewRemark = operateType.getOperate();
        }
        //更新会员审核状态为审核通过，认证状态不变更
        MembershipInfo updateMember = new MembershipInfo();
        updateMember.setAuthId(member.getAuthId());
        updateMember.setMembershipType((short)0);
        updateMember.setReviewStatus((short)reviewStatus);
        updateMember.setReviewItemIds(StringUtils.EMPTY);
        updateMember.setReviewItems(REVIEW_ITEMS_SUCCESS);
        updateMember.setReviewItemName(StringUtils.EMPTY);
        updateMember.setReviewUser(operator.getUserName());
        updateMember.setReviewRemark(reviewRemark);
        updateMember.setAppReviewTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE4));
        updateMember.setReviewTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE4));
        updateMember.setUpdatedUser(operatorName);
        updateMember.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE4));
        //审核方式为系统自动审核
        updateMember.setReviewMode(2);
        membershipInfoMapper.updateReviewStatusByAuthId(updateMember);

        //2. 更新账号申请进度
        ApplyProgress progress = new ApplyProgress();
        progress.setAuthId(authId);
        progress.setProgressContent("自动审核通过");
        progress.setCreatedUser(operator.getUserName());
        progress.setProgressTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE1));
        applyProgressMapper.addApplyProgress(progress);

        //后台系统自动审核通过，增加制卡检查和制卡逻辑(检查是否无卡)
        memberShipService.setVirtualCard(authId);

        //3.1 会员审核事件推送
        sendReviewSuccessMesage(authId, mobilePhone, reviewStatus, isNewUser, operatorName);

        //3.4 埋点
        //TODO 审核通过埋点
        //trackMemberReviewResult(authId, member, updateReviewStatusDto);
        String operation = operateType.getOperate();
        //添加日志埋点
        HidLog.membership(LogPoint.MEMBER_AUTH_SUCCESS, operation, authId);
        // 添加操作日志
        String operatorContent = operation;
        //ComUtil.insertOperatorLog(operatorContent, authId, null, operatorName, userOperatorLogMapper);
        UserOperationLogInput userOperationLogInput = new UserOperationLogInput();
        userOperationLogInput.setOperationType(MemOperateTypeEnum.REVIEW.getCode());
        userOperationLogInput.setOperationContent(operatorContent);
        userOperationLogInput.setOperationTime(new Date());
        userOperationLogInput.setOperator(operatorName);
        userOperationLogInput.setOperatorId(operator.getUserId());
        userOperationLogInput.setRefKey1(authId);
        userOperationLogInput.setUserId(member.getPkId());
        userOperationLogInput.setAuthId(authId);
        //系统自动审核通过无单独入口，此处不单独记录日志
//        memberShipService.saveUserOperationLog(userOperationLogInput);

        log.warn("自动审核会员通过， authId={}, cause={}", member.getAuthId(), operatorContent);
    }

    /**
     * 驾照信息提交(初次提交，审核不通过后重新提交)
     * 提交驾照后-驾照三要素校验及有效性认证
     * @param authId
     * @param appKey
     * @param input
     * @param membershipInfo
     * @param autoCheckLicenseElements
     * @return
     * @throws AuthenticationException
     */
    public DrivingLicenseReviewResult drivingLicenseReviewAfterSubmit(String authId, String appKey, SubmitDrivingLicenseInput input,
                                                                      MembershipInfoWithBLOBs membershipInfo,
                                                                      boolean autoCheckLicenseElements) throws AuthenticationException {
        DrivingLicenseReviewResult result = new DrivingLicenseReviewResult();
        //1. 保存待认证记录
        HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, "驾照状态认证开始-变更驾照三要素状态为->待审核", authId, true);
        log.info("提交驾照信息，变更驾照三要素状态为->待审核, authId={}, submitLicenseInput={}", authId, JSON.toJSONString(input));
        SaveDriverElementsAuthenticateInput authenticateInput = new SaveDriverElementsAuthenticateInput();
        authenticateInput.setUserId(membershipInfo.getPkId());
        authenticateInput.setName(input.getName());
        authenticateInput.setDriverCode(input.getDriverCode());
        authenticateInput.setFileNo(input.getFileNo());
        authenticateInput.setDriverLicenseImgUrl(ComUtil.splitPicUrl(input.getDrivingLicenseImgUrl()));
        authenticateInput.setFileNoImgUrl(ComUtil.splitPicUrl(input.getFileNoImgUrl()));
        authenticateInput.setAuthenticateStatus(0);
        authenticateInput.setCreateTime(new Date());
        authenticateInput.setCreateOperName(authId);
        authenticateInput.setAppKey(appKey);
        authenticateInput.setMiscDesc("提交驾照信息，三要素待认证");
        this.saveDriverLicenseElementsAuthenticateRecord(authId, authenticateInput);

        if (StringUtils.isNotBlank(input.getOfficerIdNumber())){
            HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, "驾照状态认证-用户是大陆军官，不做三要素认证", authId, true);
            log.error("提交驾照信息，用户是大陆军官，不做三要素认证, authId={}, national={}", authId, input.getNational());
            return result;
        }
        //2. 会员为外籍或所属城市未开启自动校验驾照三要素，则不做后续操作
        if (!ComUtil.checkIsUsersInMainlandResidents(input.getNational())) {
            HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, "驾照状态认证-用户不是本籍大陆用户，不做三要素认证", authId, true);
            log.error("提交驾照信息，用户不是本籍大陆用户，不做三要素认证, authId={}, national={}", authId, input.getNational());
            return result;
        }
        //如城市未开通三要素认证，则不做后续操作。
        if(!StringUtils.equals(appKey, BussinessConstants.APP_KEY_CAR_SHARING) && !autoCheckLicenseElements) {
            HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, "驾照状态认证-不为共享汽车渠道且城市未开启自动校验三要素，不做三要素认证", authId, true);
            log.info("提交驾照信息，不为共享汽车渠道且城市未开启自动校验三要素，不做三要素校验, authId={}, appKey={}", authId, appKey);
            return result;
        }
        //3. 开启驾照三要素则，调用驾照三要素认证接口，进行认证
        DrivingLicenseAuthResult authResult = drivingLicenseAuthenticate(authId, UpdateUserDto.buildUserOperator(authId, "提交驾照信息"));
        if(authResult == null) {
            HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, "驾照状态认证-尝试进行驾照三要素校验，但不符合三要素认证条件", authId, false);
            log.warn("提交驾照信息，尝试进行驾照三要素校验，不符合三要素认证条件, authId={}", authId);
            result.setCode(30069);
            result.setMessage("不符合驾照认证条件");
            return result;
        }
        //3.1 三要素认证结果-->会员驾照审核结果
        result = buildLicenseSubmitResult(authId, input, authResult);
        HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, "驾照状态认证-驾照三要素校验，结果=" + JSON.toJSONString(result), authId, false);
        //3.2 根据会员驾照审核结果，变更会员相应的认证状态
        if(authResult.getAuthenticateStatus() == 3) {
            //修改会员驾照三要素认证状态为查证中，其余状态不变更
            updateMemberLicenseStatus(authId, result.getAuthenticateStatus(), result.getElementsReviewItems(),
                    0, "", UpdateUserDto.buildUserOperator(authId));
        }else {
            //更新驾照三要素认证状态和驾照认证状态
            updateMemberLicenseStatus(authId, result.getAuthenticateStatus(), result.getElementsReviewItems(),
                    result.getLicenseStatus(), result.getLicenseStatusMsg(), UpdateUserDto.buildUserOperator(authId));
        }
        return result;
    }

    /**
     * 驾照补全或更新驾照有效期
     * 提交驾照后-驾照三要素校验及有效性认证
     * @param authId
     * @param appKey
     * @param input
     * @param membershipInfo
     * @param autoCheckLicenseElements
     * @return
     */
    public DrivingLicenseReviewResult drivingLicenseReviewAfterManualUpdate(String authId, String appKey, SubmitDrivingLicenseInput input,
                                                                            MembershipInfoWithBLOBs membershipInfo,
                                                                            boolean autoCheckLicenseElements) throws AuthenticationException{
        DrivingLicenseReviewResult result = new DrivingLicenseReviewResult();
        if (!ComUtil.checkIsUsersInMainlandResidents(membershipInfo.getNational())) {
            log.error("驾照信息补全或更新有效期，用户不是本籍大陆用户， 不做三要素校验, authId={}, national={}", authId, membershipInfo.getNational());
            return result;
        }
        //如城市未开通三要素认证，则不做后续操作。
        if(!StringUtils.equals(appKey, BussinessConstants.APP_KEY_CAR_SHARING) && !autoCheckLicenseElements) {
            log.info("驾照信息补全或更新有效期，不为共享汽车渠道且城市未开启自动校验三要素，不做三要素校验, authId={}, appKey={}", authId, appKey);
            return result;
        }
        //若需要做驾照三要验证，则限制同会员每小时的提交次数。
        //三要校验次数限制
        BaseResponse checkTimesResp = checkMemberLicenseAuthenticateTimes(authId);
        if(checkTimesResp != null) {
            //驾照三要素校验次数
            log.debug("提交驾照信息，驾照不可频繁提交, authId={}", authId);
            throw new AuthenticationException(checkTimesResp.getCode(), checkTimesResp.getMessage());
        }
        /**
         * 如城市开通三要素认证，则需要单独做验证并提示，但并不修改会员的三要素认证状态
         * (因新驾照信息尚未更新至会员)
         */
        DriverLicenseElementsAuthenticateRecord insertRow = new DriverLicenseElementsAuthenticateRecord();
        BeanUtils.copyProperties(input, insertRow);
        insertRow.setAuthenticateStatus(0);
        insertRow.setUserId(-membershipInfo.getPkId());
        insertRow.setCreateOperId(-1L);
        insertRow.setCreateOperName(authId);
        try {
            licenseElementsAuthenticateRecordMapper.insertSelective(insertRow);
        }catch (Exception e) {
            log.error("save licenseElementsAuthenticateRecord failed, authId=" + authId
                    + ", appKey=" + appKey + ", input=" + JSON.toJSONString(input), e);
            throw e;
        }
        //调用驾照三要素验证接口
        DriverLicenseElementsAuthenticateRecord record = licenseAuthenticateSequence(insertRow.getId(), authId, UpdateUserDto.buildUserOperator(authId),
                input.getDriverCode(), input.getName(), input.getFileNo(), licenseAuthSequence, null);
        DrivingLicenseAuthResult authResult = new DrivingLicenseAuthResult();
        BeanCopyUtils.copyProperties(record, authResult);
        //三要素认证结果-->会员驾照审核结果
        DrivingLicenseReviewResult reviewResult = buildLicenseSubmitResult(authId, input, authResult);
        if(reviewResult.getCode() == 0) {
            DriverLicenseElementsAuthenticateRecord updateRow = new DriverLicenseElementsAuthenticateRecord();
            updateRow.setId(insertRow.getId());
            updateRow.setAuthenticateStatus(1);
            updateRow.setElementsReviewItems(StringUtils.EMPTY);
            updateRow.setLicenseStatusMsg(StringUtils.EMPTY);
            updateRow.setLicenseStatus(1);
            updateRow.setUserId(membershipInfo.getPkId());
            updateRow.setUpdateOperId(-1L);
            updateRow.setUpdateOperName(authId);
            licenseElementsAuthenticateRecordMapper.updateByPrimaryKeySelective(updateRow);
        }
        return reviewResult;
    }

    private DrivingLicenseReviewResult buildLicenseSubmitResult(String authId, SubmitDrivingLicenseInput input, DrivingLicenseAuthResult authResult){
        DrivingLicenseReviewResult result = new DrivingLicenseReviewResult();
        //2.1 三要素查询结果-查无记录
        BeanCopyUtils.copyProperties(authResult, result);
        if(authResult.getAuthenticateStatus() == 3) {
            //查无，提示用户驾照正在验证中[您的驾照验证中，暂不可用车，如有疑问，请联系客服]
            result.buildErrorCode(StatusCode.LICENSE_ELEMENTS_AUTH_ING);
            HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, "三要素认证查无记录", authId, true);
            return result;
        }
        //判断驾照有效状态
        LicenseStatusInfo licenseStatusInfo = LicenseStatusChecker.check(input.getDrivingLicenseType(), input.getExpirationDate(),
                authResult.getLicenseStatusMsg());
        int licenseStatus = licenseStatusInfo.getLicenseStatus();
        String licenseStatusMsg = licenseStatusInfo.getLicenseStatusMsg();
        result.setLicenseStatus(licenseStatus);
        result.setLicenseStatusMsg(licenseStatusMsg);
        if(!licenseStatusInfo.isValid()) {
            HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, "驾照状态不符合要求", authId, true);
        }
        if(authResult.getAuthenticateStatus() == 2) {
            //不一致，提示用户重新上传驾照信息
            result.buildErrorCode(StatusCode.LICENSE_ELEMENTS_AUTH_FAILED);
            HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, "三要素认证不一致", authId, true);
        }
        else if(authResult.getAuthenticateStatus() == 1) {
            //认证通过，则返回正常code
            HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, "三要素认证一致", authId, true);
            if(!licenseStatusInfo.isValid()) {
                HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, licenseStatusMsg, authId, false);
                result.buildErrorCode(StatusCode.LICENSE_STATUS_INVALID);
                if(StringUtils.isNotBlank(result.getLicenseStatusMsg())) {
                    result.setMessage(result.getLicenseStatusMsg());
                }
            }
        }
        return result;
    }

    /**
     * 提交驾照信息-提交审核
     * @param authId
     * @param appKey
     * @param input
     * @param membershipInfo
     * @throws AuthenticationException
     */
    public void submitDrivingLicenseForReview(String authId, String appKey, SubmitDrivingLicenseInput input,
                                              MembershipInfoWithBLOBs membershipInfo, boolean autoCheckLicenseElements) throws AuthenticationException {
        /**
         * 1.判断驾照格式
         */
        input.setDriverCode(input.getDriverCode().toUpperCase());
        String inputNational = input.getNational();
        if (StringUtils.isNotEmpty(inputNational)) {
            if(input.getDriverCode().length() > 20) {
                HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, StatusCode.DRIVER_CODE_CHECK_ERROR.getMsg(), authId, false);
                throw new AuthenticationException(StatusCode.DRIVER_CODE_CHECK_ERROR);
            }
            if (inputNational.contains(BussinessConstants.CHINA_NATIONAL)) {
                if (StringUtils.isNotBlank(input.getOfficerIdNumber())){
                    inputNational = "中国（军人）";
                }else {
                    //本国国籍校验驾照号
                    if (!ComUtil.checkIDCard(input.getDriverCode())) {
                        HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, StatusCode.DRIVER_CODE_CHECK_ERROR.getMsg(), authId, false);
                        throw new AuthenticationException(StatusCode.DRIVER_CODE_CHECK_ERROR);
                    }
                }
            } else {
                input.setDriverLicenseInputType(2);
                //外籍驾照号符合是否是本籍
                if (ComUtil.checkIDCard(input.getDriverCode())) {
                    HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, StatusCode.SUBMIT_DRIVER_MODE_ERROR.getMsg(), authId, false);
                    throw new AuthenticationException(StatusCode.SUBMIT_DRIVER_MODE_ERROR);
                }
            }
        }
        /**
         * 2.检查驾照号是否被占用
         */
        List<MembershipBaseInfo> membershipBaseInfoList = membershipInfoMapper.checkDriverCodeOccupied(authId, input.getDriverCode(), 0);
        if (CollectionUtils.isNotEmpty(membershipBaseInfoList)) {
            HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, StatusCode.DRIVER_CODE_OCCUPIED.getMsg(), authId, false);
            throw new AuthenticationException(StatusCode.DRIVER_CODE_OCCUPIED);
        }

        /**
         * 军人驾照查验身份证是否被注册
         */
        if (StringUtils.isNotBlank(input.getOfficerIdNumber())){
            List<MembershipBaseInfo> baseInfos = membershipInfoMapper.checkDriverCodeOccupied(authId, input.getOfficerIdNumber(), 0);
            if (CollectionUtils.isNotEmpty(baseInfos)) {
                HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, StatusCode.DRIVER_CODE_OCCUPIED.getMsg(), authId, false);
                //throw new AuthenticationException(StatusCode.PASSPORT_CODE_OCCUPIED);
            }
            List<MembershipBaseInfo> passportMember = membershipInfoMapper. checkPassPortOccupied(authId,input.getOfficerIdNumber(),0);
            if (CollectionUtils.isNotEmpty(passportMember)) {
                HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, StatusCode.DRIVER_CODE_OCCUPIED.getMsg(), authId, false);
                //throw new AuthenticationException(StatusCode.PASSPORT_CODE_OCCUPIED);
            }
        }
        /**
         * 2.1检查档案编号
         */
        if (StringUtils.isNotBlank(input.getFileNo())){
            List<MembershipBaseInfo>  fileNoMember = membershipInfoMapper.checkFileNoOccupied(authId,input.getFileNo(),0);
            if (CollectionUtils.isNotEmpty(fileNoMember)){
                HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, StatusCode.FILE_NO_OCCUPIED.getMsg(), authId, false);
                throw new AuthenticationException(StatusCode.FILE_NO_OCCUPIED);
            }
        }
        int reviewStatus = membershipInfo.getReviewStatus().intValue();
        String driverCode = membershipInfo.getDriverCode();
        /**
         * 3.审核不通过,本籍用户需要校验之前驾照号与旧的相同
         */
        if (reviewStatus == 2) {
            String reviewItems = membershipInfo.getReviewItems();
            String national = membershipInfo.getNational();
            if (StringUtils.isNotEmpty(driverCode) && StringUtils.isNotEmpty(reviewItems) || StringUtils.isNotEmpty(national)) {
                if (!"2".equals(reviewItems.substring(1, 2)) && !driverCode.equalsIgnoreCase(input.getDriverCode()) && national.contains(BussinessConstants.CHINA_NATIONAL)) {
                    HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, StatusCode.DRIVER_DIFFERENT_ERROR.getMsg(), authId, false);
                    throw new AuthenticationException(StatusCode.DRIVER_DIFFERENT_ERROR);
                }
            }
        }
        /**
         * 4.审核通过老用户需要与之前驾照号相同
         */
        String createdTime = membershipInfo.getCreatedTime();
        LocalDateTime createDateTime = LocalDateTime.parse(createdTime.substring(0, 14), DateTimeFormatter.ofPattern(ComUtil.DATE_TYPE4));
        if (reviewStatus == 1 && BussinessConstants.OLD_USER_DATETIME.isAfter(createDateTime)) {
            if (!driverCode.equalsIgnoreCase(input.getDriverCode())) {
                HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, StatusCode.DRIVER_DIFFERENT_ERROR.getMsg(), authId, false);
                throw new AuthenticationException(StatusCode.DRIVER_DIFFERENT_ERROR);
            }
            //老用户不更新名字和驾照号
            //TODO 此逻辑可能导致保存待审核记录失败
//            input.setName(null);
//            input.setDriverCode(null);
        }
        /**
         * 5.判断驾照状态
         *   驾照有效期 & 驾照准驾车型
         */
        StatusCode licenseCheckResult = LicenseStatusChecker.checkLicenseAuthStatus(input.getDrivingLicenseType(), input.getExpirationDate(), StringUtils.EMPTY);
        if(licenseCheckResult != null) {
            HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, licenseCheckResult.getMsg(), authId, false);
            throw new AuthenticationException(licenseCheckResult);
        }
        /**
         * 6. 提交驾照信息次数限制，仅需要调用三要素的场景下使用
         */
        if(autoCheckLicenseElements) {
            BaseResponse checkTimesResp = checkMemberLicenseAuthenticateTimes(authId);
            if (checkTimesResp != null) {
                //驾照三要素校验次数
                log.debug("提交驾照信息，驾照不可频繁提交, authId={}", authId);
                throw new AuthenticationException(checkTimesResp.getCode(), checkTimesResp.getMessage());
            }
        }
        /**
         * 7.更新用户信息
         */
        input.setDrivingLicenseImgUrl(ComUtil.splitPicUrl(input.getDrivingLicenseImgUrl()));
        input.setFileNoImgUrl(ComUtil.splitPicUrl(input.getFileNoImgUrl()));
        MembershipInfoWithBLOBs updateRow = new MembershipInfoWithBLOBs();
        BeanUtils.copyProperties(input, updateRow);
        if (StringUtils.isEmpty(inputNational)) {
            updateRow.setNational(membershipInfo.getNational());
        }else {
            updateRow.setNational(inputNational);

        }
        updateRow.setObtainDriverTimer(input.getFirstGetLicenseDate());
//        if (APP_KEY_CAR_SHARING.equals(appKey)){
//            updateRow.setLicenseExpirationTime("2020-01-01");
//        }else {
            updateRow.setLicenseExpirationTime(input.getExpirationDate());
//        }
        updateRow.setDriverLicenseInputType(input.getDriverLicenseInputType());

        /**
         * 提交完驾照资料，立即提交审核。 变更，提交驾照时，不变更认证状态
         */
        //updateRow.setAuthenticationStatus(1);
        updateRow.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
        updateRow.setUpdatedUser(authId);
        updateRow.setPkId(membershipInfo.getPkId());
        if (StringUtils.isNotBlank(input.getOfficerIdNumber())){
            updateRow.setIdType(5);
            updateRow.setPassportNo(input.getOfficerIdNumber());
            updateRow.setIdCardNumber(input.getOfficerIdNumber());
        }
        int updateResult = membershipInfoMapper.updateByPrimaryKeySelective(updateRow);
        if (updateResult < 1) {
            log.error("会员保存驾照信息失败, authId={}", authId);
            HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, StatusCode.SUBMIT_FAILURE.getMsg(), authId, false);
            throw new AuthenticationException(StatusCode.SUBMIT_FAILURE);
        } else {
            /**
             * 8.保存操作日志
             */
//            ComUtil.insertOperatorLog(content, authId, null, authId, userOperatorLogMapper);
//            UserOperationLogInput userOperationLogInput = new UserOperationLogInput();
//            userOperationLogInput.setOperationType(0L);
//            userOperationLogInput.setOperationContent("提交驾照成功");
//            userOperationLogInput.setOperationTime(new Date());
//            userOperationLogInput.setOperator(authId);
//            userOperationLogInput.setRefKey1(appKey);
//            userOperationLogInput.setAuthId(authId);
//            userOperationLogInput.setUserId(membershipInfo.getPkId());
//            memberShipService.saveUserOperationLog(userOperationLogInput);
            log.warn("会员authId=" + authId + "保存驾照信息成功");
        }

        //驾照住址更新
//        MmpUserTag mmpUserTag = mmpUserTagMapper.selectMmpUserByAuthId(authId);
//        if(!ObjectUtils.isEmpty(mmpUserTag)){
//            mmpUserTag.setSpare9(input.getAddress());
//            mmpUserTagMapper.saveUserTag(mmpUserTag);
//        }

        HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, "保存驾照信息完成，进入后续驾照认证", authId, true);
    }

    /**
     * 提交驾照信息-更新驾照有效期
     * @param authId
     * @param input
     * @param membershipInfo
     * @throws AuthenticationException
     */
    public DrivingLicenseReviewResult updateDrivingLicenseExpiredDate(String authId, String appKey, SubmitDrivingLicenseInput input,
                                                MembershipInfoWithBLOBs membershipInfo, boolean autoCheckLicenseElements) throws AuthenticationException {
        /**
         * 1.判断有效期是否在原有效期时间之后
         */
        if (StringUtils.isNotBlank(membershipInfo.getLicenseExpirationTime())) {
            DateTimeFormatter ymd = DateTimeFormatter.ofPattern(ComUtil.DATE_TYPE5);
            LocalDate firstExpirationTime = LocalDate.parse(membershipInfo.getLicenseExpirationTime(), ymd);
            LocalDate expirationDate = LocalDate.parse(input.getExpirationDate(), ymd);
            if (!expirationDate.isAfter(firstExpirationTime)) {
                HidLog.membership(LogPoint.UPDATE_DRIVER_EXPIRED_TIME, StatusCode.LICENSE_EXPIRATION_TIME_ERROR.getMsg(), authId, false);
                throw new AuthenticationException(StatusCode.LICENSE_EXPIRATION_TIME_ERROR);
            }
        }

        if (StringUtils.isNotBlank(input.getFileNo())){
            List<MembershipBaseInfo>  fileNoMember = membershipInfoMapper.checkFileNoOccupied(authId,input.getFileNo(),0);
            if (CollectionUtils.isNotEmpty(fileNoMember)){
                HidLog.membership(LogPoint.UPDATE_DRIVER_EXPIRED_TIME, StatusCode.FILE_NO_OCCUPIED.getMsg(), authId, false);
                throw new AuthenticationException(StatusCode.FILE_NO_OCCUPIED);
            }
        }
        //驾照即将到期更新驾照，若三要素校验不通过，则不提交，直接返回失败。
        //风险，三要素认证接口运维期间，造成信息无法提交（可通过关闭城市自动三要素校验来解决）
        HidLog.membership(LogPoint.UPDATE_DRIVER_EXPIRED_TIME, "更新驾照有效期-判断是否需要作三要素验证", authId, true);
        input.setFileNoImgUrl(ComUtil.splitPicUrl(input.getFileNoImgUrl()));
        input.setDrivingLicenseImgUrl(ComUtil.splitPicUrl(input.getDrivingLicenseImgUrl()));
        DrivingLicenseReviewResult reviewResult = drivingLicenseReviewAfterManualUpdate(authId, appKey, input, membershipInfo, autoCheckLicenseElements);
        if(reviewResult.getCode() != 0) {
            return reviewResult;
        }
        String content = "更新驾照有效期成功";
        /**
         * 2.输入类型 ：驾照ocr识别，则自动更新驾照有效期及驾照图片；若手动输入，则走人工审核
         */
        if (input.getDriverLicenseInputType() == 1) {
            /**
             * 3.更新会员的驾照有效期
             */
            MembershipInfoWithBLOBs updateRow = new MembershipInfoWithBLOBs();
            updateRow.setLicenseExpirationTime(input.getExpirationDate());
            updateRow.setDrivingLicenseImgUrl(ComUtil.splitPicUrl(input.getDrivingLicenseImgUrl()));
            updateRow.setFileNoImgUrl(ComUtil.splitPicUrl(input.getFileNoImgUrl()));
            updateRow.setFileNo(input.getFileNo());
            updateRow.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE3));
            updateRow.setUpdatedUser(authId);
            updateRow.setPkId(membershipInfo.getPkId());
            int updateResult = membershipInfoMapper.updateByPrimaryKeySelective(updateRow);
            if (updateResult < 1) {
                log.warn("会员authId=" + authId + "更新驾照有效期失败");
                log.error("会员authId=" + authId + "保存驾照信息失败");
                HidLog.membership(LogPoint.UPDATE_DRIVER_EXPIRED_TIME, StatusCode.SUBMIT_FAILURE.getMsg(), authId, false);
                throw new AuthenticationException(StatusCode.SUBMIT_FAILURE);
            } else {
                log.warn("会员authId=" + authId + "更新驾照有效期成功");
            }
            /**
             * 4.更新用户补充信息无效
             */
            MembershipAdditionalInfo additionalInfo = membershipAdditionalInfoMapper.selectByAuthId(authId,0);
            if (additionalInfo != null) {
                MembershipAdditionalInfo updateAddInfoRow = new MembershipAdditionalInfo();
                updateAddInfoRow.setStatus(1);
                updateAddInfoRow.setUpdateTime(new Date());
                updateAddInfoRow.setUpdateOperName(authId);
                updateAddInfoRow.setId(additionalInfo.getId());
                membershipAdditionalInfoMapper.updateByPrimaryKeySelective(updateAddInfoRow);
            }
            HidLog.membership(LogPoint.UPDATE_DRIVER_EXPIRED_TIME, "自动更新驾照有效期成功", authId, true);
        } else {
            /**
             * 5.插入用户驾照待审核信息
             */
            MembershipAdditionalInfo additionalInfo = membershipAdditionalInfoMapper.selectByAuthId(authId,0);
            MembershipAdditionalInfo updateAddInfoRow = new MembershipAdditionalInfo();
            updateAddInfoRow.setAuthId(authId);
            updateAddInfoRow.setDrivingLicense(input.getDriverCode());
            updateAddInfoRow.setDrivingLicenseType(input.getDrivingLicenseType());
            updateAddInfoRow.setNational(input.getNational());
            updateAddInfoRow.setObtainDriverTimer(input.getFirstGetLicenseDate());
            updateAddInfoRow.setLicenseExpirationTime(input.getExpirationDate());
            updateAddInfoRow.setDrivingLicenseImgUrl(ComUtil.splitPicUrl(input.getDrivingLicenseImgUrl()));
            updateAddInfoRow.setAppReviewTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE4));
            updateAddInfoRow.setReviewStatus(0);
            updateAddInfoRow.setReviewMode(1);
            updateAddInfoRow.setFileNoImgUrl(ComUtil.splitPicUrl(input.getFileNoImgUrl()));
            updateAddInfoRow.setFileNo(input.getFileNo());
            Date nowTime = new Date();
            updateAddInfoRow.setUpdateTime(nowTime);
            updateAddInfoRow.setUpdateOperName(authId);
//            updateAddInfoRow.setPersonalAddress(input.getAddress());
            if (additionalInfo == null) {
                updateAddInfoRow.setCreateTime(nowTime);
                updateAddInfoRow.setCreateOperName(authId);
                membershipAdditionalInfoMapper.insertSelective(updateAddInfoRow);
            } else {
                updateAddInfoRow.setId(additionalInfo.getId());
                membershipAdditionalInfoMapper.updateByPrimaryKeySelective(updateAddInfoRow);
            }
            content += "，提交新驾照人工审核";
            HidLog.membership(LogPoint.UPDATE_DRIVER_EXPIRED_TIME, "新增驾照待审核信息成功", authId, true);
        }
        //保存操作日志
        ComUtil.insertOperatorLog(content, authId, null, authId, userOperatorLogMapper);
        UserOperationLogInput userOperationLogInput = new UserOperationLogInput();
        userOperationLogInput.setOperationType(MemOperateTypeEnum.UPDATE_LICENSE_EXPIRE_DATE.getCode());
        userOperationLogInput.setOperationContent(content);
        userOperationLogInput.setOperationTime(new Date());
        userOperationLogInput.setOperator(authId);
        userOperationLogInput.setRefKey1(appKey);
        userOperationLogInput.setAuthId(authId);
        userOperationLogInput.setUserId(membershipInfo.getPkId());
        memberShipService.saveUserOperationLog(userOperationLogInput);
        HidLog.membership(LogPoint.UPDATE_DRIVER_FILE_NO, content, authId, true);
        return reviewResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DrivingLicenseReviewResult updateUserFileNo(UpdateFileNoInput input) throws AuthenticationException {
        String authId = input.getAuthId();
        String appKey = input.getAppkey();
        /**
         * 1.查询用户信息
         */
        MembershipInfoWithBLOBs membershipInfo = membershipInfoMapper.selectByAuthId(authId, 0);
        if (membershipInfo == null) {
            HidLog.membership(LogPoint.MEMBER_SUBMIT_DRIVER_INFO, StatusCode.USER_INFO_NO_EXIST.getMsg(), authId, false);
            throw new AuthenticationException(StatusCode.USER_INFO_NO_EXIST);
        }
        SubmitDrivingLicenseInput fullLicenseInfo = new SubmitDrivingLicenseInput();
        BeanCopyUtils.copyProperties(membershipInfo, fullLicenseInfo);
        fullLicenseInfo.setFileNo(input.getFileNo());
        fullLicenseInfo.setFileNoImgUrl(input.getFileNoImgUrl());
        fullLicenseInfo.setFirstGetLicenseDate(membershipInfo.getObtainDriverTimer());
        fullLicenseInfo.setExpirationDate(membershipInfo.getLicenseExpirationTime());
        fullLicenseInfo.setSubmitType(2);
        if(input.getDriverLicenseInputType() == null) {
            fullLicenseInfo.setDriverLicenseInputType(2);
        }
        /**
         * 2. 驾照数据提交
         */
        boolean isUsersInMainlandChina = ComUtil.checkIsUsersInMainlandResidents(fullLicenseInfo.getNational());
        boolean autoCheckLicenseElements = isUsersInMainlandChina && checkCityIsAutoLicenseAuth(membershipInfo.getCityOfOrigin());
        return updateUserFileNo(authId, appKey, fullLicenseInfo, membershipInfo, autoCheckLicenseElements);
    }


    public DrivingLicenseReviewResult updateUserFileNo(String authId, String appKey, SubmitDrivingLicenseInput input,
                                    MembershipInfoWithBLOBs membershipInfo, boolean autoCheckLicenseElements) throws AuthenticationException {
        if (StringUtils.isBlank(authId) || StringUtils.isBlank(input.getFileNo()) || StringUtils.isBlank(input.getFileNoImgUrl())){
            HidLog.membership(LogPoint.UPDATE_DRIVER_FILE_NO, StatusCode.ILLEGAL_PARAM.getMsg(), authId, false);
            throw new AuthenticationException(StatusCode.ILLEGAL_PARAM);
        }
        if(input.getFileNo().length() != 12){
            HidLog.membership(LogPoint.UPDATE_DRIVER_FILE_NO, StatusCode.FILE_NO_ERROR.getMsg(), authId, false);
            throw new AuthenticationException(StatusCode.FILE_NO_ERROR);
        }
        if (StringUtils.isNotBlank(input.getFileNo())){
            List<MembershipBaseInfo>  fileNoMember = membershipInfoMapper.checkFileNoOccupied(authId, input.getFileNo(),0);
            if (CollectionUtils.isNotEmpty(fileNoMember)){
                HidLog.membership(LogPoint.UPDATE_DRIVER_FILE_NO, StatusCode.FILE_NO_OCCUPIED.getMsg(), authId, false);
                throw new AuthenticationException(StatusCode.FILE_NO_OCCUPIED);
            }
        }
        //补全档案编号，若三要素校验不通过，则不提交
        //风险，三要素认证接口运维期间，造成信息无法提交（可通过关闭城市自动三要素校验来解决）
        HidLog.membership(LogPoint.UPDATE_DRIVER_FILE_NO, "补全档案编号-判断是否需要作三要素验证", authId, true);
        input.setFileNoImgUrl(ComUtil.splitPicUrl(input.getFileNoImgUrl()));
        input.setDrivingLicenseImgUrl(ComUtil.splitPicUrl(input.getDrivingLicenseImgUrl()));
        DrivingLicenseReviewResult reviewResult = drivingLicenseReviewAfterManualUpdate(authId, appKey, input, membershipInfo, autoCheckLicenseElements);
        log.warn("补全挡案编号: 三要素验证，authId={}, 结果={}", authId, JSON.toJSONString(reviewResult));
        if(reviewResult.getCode() != 0) {
            return reviewResult;
        }
        //组织补全信息
        MembershipInfo updateRow = new MembershipInfo();
        updateRow.setAuthId(authId);
        updateRow.setFileNo(input.getFileNo());
        updateRow.setFileNoImgUrl(ComUtil.splitPicUrl(input.getFileNoImgUrl()));
        updateRow.setLicenseAuthStatus(1);
        updateRow.setLicenseElementsAuthStatus(1);
        updateRow.setElementsReviewItems(StringUtils.EMPTY);
        updateRow.setLicenseStatusMsg(StringUtils.EMPTY);
        String content = "更新档案编号成功";
        //三要素校验通过，若提交人工审核，则需要变更会员审核状态为待审核
        if(input.getDriverLicenseInputType() == 2) {
            if(membershipInfo.getAuthenticationStatus() == 2) {
                //手动修改过，则若驾照认证通过则提交人工审核(仅老用户，新用户刷脸过后再变更)
                updateRow.setReviewStatus((short)0);
                updateRow.setReviewItems(REVIEW_ITEMS_DEFUFALT);
                updateRow.setReviewItemIds(StringUtils.EMPTY);
                updateRow.setReviewItemName(StringUtils.EMPTY);
                updateRow.setReviewMode(1);
                updateRow.setAppReviewTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE4));
                content += "，提交人工审核";
                HidLog.membership(LogPoint.UPDATE_DRIVER_FILE_NO, "补全档案编号-已提交，已认证会员提交人工审核", authId, false);
            }else{
                log.error("补全档案编号-补全数据已提交，异常数据-认证状态不为已认证， authId={}", authId);
                HidLog.membership(LogPoint.UPDATE_DRIVER_FILE_NO, "补全档案编号-已提交，但认证状态不为已认证", authId, false);
            }
        }
        //三要素及驾照状态认证通过，则更新驾照信息
        int i = membershipInfoMapper.updateUserFileNoAndStatus(updateRow);
        if (i<=0){
            HidLog.membership(LogPoint.UPDATE_DRIVER_FILE_NO, StatusCode.SUBMIT_FILE_NO_FAIL.getMsg(), authId, false);
            throw new AuthenticationException(StatusCode.SUBMIT_FILE_NO_FAIL);
        }
        //保存操作日志
        ComUtil.insertOperatorLog(content, authId, null, authId, userOperatorLogMapper);
        UserOperationLogInput userOperationLogInput = new UserOperationLogInput();
        userOperationLogInput.setOperationType(MemOperateTypeEnum.UPDATE_FILE_NO.getCode());
        userOperationLogInput.setOperationContent(content);
        userOperationLogInput.setOperationTime(new Date());
        userOperationLogInput.setOperator(authId);
        userOperationLogInput.setRefKey1(appKey);
        userOperationLogInput.setAuthId(authId);
        userOperationLogInput.setUserId(membershipInfo.getPkId());
        memberShipService.saveUserOperationLog(userOperationLogInput);
        HidLog.membership(LogPoint.UPDATE_DRIVER_FILE_NO, content, authId, true);
        return reviewResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DrivingLicenseAuthResult drivingLicenseAuthenticate(String authId) {
        UpdateUserDto operator = new UpdateUserDto();
        operator.setUserId(-1L);
        operator.setUserName(authId);
        operator.setUpdateTime(new Date());
        return drivingLicenseAuthenticate(authId, operator);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DrivingLicenseAuthResult drivingLicenseAuthenticate(String authId, UpdateUserDto operator) {
        log.warn("驾照三要素核查功能已下线, authId={}", authId);
        return null;
        //return drivingLicenseAuthenticate(authId, false, operator);
    }

    @Transactional(rollbackFor = Exception.class)
    public DrivingLicenseAuthResult drivingLicenseAuthenticate(String authId, boolean isReAuthenticate, UpdateUserDto operator) {
        log.warn("驾照三要素核查功能已下线, authId={}", authId);
        return null;
        //return drivingLicenseAuthenticate(authId, isReAuthenticate, operator, null);
    }

    @Transactional(rollbackFor = Exception.class)
    public DrivingLicenseAuthResult drivingLicenseAuthenticate(String authId, boolean isReAuthenticate, UpdateUserDto operator, Integer timeout) {
        log.warn("驾照三要素核查功能已下线, authId={}", authId);
        return null;
//        MembershipBasicInfo membershipInfo = membershipInfoMapper.getUserBasicInfo(authId, (short)0);
//        if (membershipInfo == null) {
//            log.error("三要素认证：不做认证，用户信息不存在或已注销完成, authId={}", authId);
//            return null;
//        }
//        return drivingLicenseAuthenticate(membershipInfo, isReAuthenticate, operator, null);
    }

    @Transactional(rollbackFor = Exception.class)
    public DrivingLicenseAuthResult drivingLicenseAuthenticate(MembershipBasicInfo membershipInfo, boolean isReAuthenticate, UpdateUserDto operator, Integer timeout) {
        //1. 仅本籍用户需要做驾照三要素认证
        //MembershipBasicInfo membershipInfo = membershipInfoMapper.getUserBasicInfo(authId, (short)0);
        String authId = membershipInfo.getAuthId();
        if (!ComUtil.checkIsUsersInMainlandResidents(membershipInfo.getNational())) {
            log.error("三要素认证：不做认证，用户不是本籍大陆用户, authId={}, national={}", authId, membershipInfo.getNational());
            return null;
        }
        //2. 仅待认证/待重新认证/查证中的会员能够做三要素认证
        DriverLicenseElementsAuthenticateRecord lastRecord = licenseElementsAuthenticateRecordMapper.selectUserWaitReviewInfo(membershipInfo.getPkId());
        if(lastRecord == null || lastRecord.getAuthenticateStatus() != 0 && lastRecord.getAuthenticateStatus() != 3) {
            log.warn("三要素认证：会员当前不是待认证/待重新认证/查证中状态，不做三要素校验, authId={}, lastAuthRecord={}", authId, JSON.toJSONString(lastRecord));
            return null;
        }
        //3. 驾照三要素认证并保存变更会员三要素认证状态
        Long authRecordId = lastRecord.getId();
        //3.1 三要素认证
        DriverLicenseElementsAuthenticateRecord record = licenseAuthenticateSequence(authRecordId, authId, operator,
                membershipInfo.getDriverCode(), membershipInfo.getName(), membershipInfo.getFileNo(), licenseAuthSequence, timeout);
        //3.2 更新会员驾照三要素记录及同步更新会员表三要素认证状态
        /**
         * modify 20200520, 由于licenseStatus及licenseStatusMsg需结合准驾类型及有效期
         * 因此此处结合有效期做检查之后，方确定此2字段的值
         */
        LicenseStatusInfo licenseStatusInfo = LicenseStatusChecker.check(membershipInfo.getDrivingLicenseType(), membershipInfo.getLicenseExpirationTime(),
                record.getLicenseStatusMsg());
        String licenseStatusMsg = licenseStatusInfo.getLicenseStatusMsg();
        int licenseStatus = licenseStatusInfo.getLicenseStatus();
        record.setLicenseStatus(licenseStatus);
        record.setLicenseStatusMsg(licenseStatusMsg);
        //更新三要素认证记录
        licenseElementsAuthenticateRecordMapper.updateByPrimaryKeySelective(record);
        //更新会员表相关字段
        updateMemberLicenseStatus(authId, record.getAuthenticateStatus(), record.getElementsReviewItems(),
                record.getLicenseStatus(), record.getLicenseStatusMsg(), operator);
        record.setUserId(membershipInfo.getPkId());
        //3.3 若当前操作非复查，则更新待复查列表(保存新的待复查记录OR从复查列表中移除)
        if(!isReAuthenticate) {
            updateAuthenticateList(record);
        }
        //4. 若三要素认证一致，则更新用户驾驶证扣分情况(暂未使用)
        if(record.getAuthenticateStatus() == 1) {
            //分数查询及埋点
            SaveDriverElementsAuthenticateLogInput driverDeduction = licenseAuthenticateService.queryDriverDeduction(authId,
                    lastRecord.getDriverCode(), lastRecord.getName(), lastRecord.getFileNo(), ComUtil.getIntValue(deductionSupplier));
            driverDeduction.setRecordId(lastRecord.getId());
            driverDeduction.setCreateTime(record.getUpdateTime());
            driverDeduction.setCreateOperName(operator.getUserName());
            driverDeduction.setCreateOperId(operator.getUserId());
            this.saveDriverLicenseElementsAuthenticateLog(driverDeduction);
        }

        String desc = getLicenseAuthStatusDesc(record);
        //5. 三要素认证状态埋点
//        try {
//            Map<String, Object> map = new HashMap<>();
//            map.put("user_threeElementsOfDrivingLicenseCertificationStatus", desc);
//            sensorsdataService.profileSet(authId,true, map);
//        } catch (Exception e) {
//            log.error("三要素用户表认证状态埋点失败, authId=" + authId, e);
//        }
        /**
         * 6. 会员操作日志记录
         */
        String content = "驾照三要素认证：" + desc;
        if(StringUtils.isNotBlank(operator.getRemark())) {
            content = operator.getRemark() + "-" + content;
        }
        ComUtil.insertOperatorLog(content, authId, String.valueOf(operator.getUserId()),
                operator.getUserName(), userOperatorLogMapper);
        //记录操作日志
        UserOperationLogInput userOptRecord = new UserOperationLogInput();
        userOptRecord.setUserId(membershipInfo.getPkId());
        userOptRecord.setRefKey1(membershipInfo.getMobilePhone());
        userOptRecord.setOperationType(MemOperateTypeEnum.LIC_AUTH.getCode());
        userOptRecord.setOperationContent(content);
        userOptRecord.setOperatorId(operator.getUserId());
        userOptRecord.setOperator(operator.getUserName());
        userOptRecord.setUserId(membershipInfo.getPkId());
        memberShipService.saveUserOperationLog(userOptRecord);

        //会员系统重新校验or查无场景下，若结果为一致或不一致，则发送短信通知
        if("evcard-mmp".equals(operator.getOriginSystem())
                ||  StringUtils.contains(operator.getRemark(), "手动") || isReAuthenticate) {
            if(!membershipInfo.getLicenseElementsAuthStatus().equals(record.getAuthenticateStatus())) {
                if(record.getAuthenticateStatus().equals(1)) {
                    notifyLicenseCheckResultBySms(membershipInfo.getMobilePhone(), true, operator.getUserName());
                }else if(record.getAuthenticateStatus().equals(2)) {
                    notifyLicenseCheckResultBySms(membershipInfo.getMobilePhone(), false, operator.getUserName());
                }
            }
        }

        DrivingLicenseAuthResult licenseAuthResult = new DrivingLicenseAuthResult();
        BeanCopyUtils.copyProperties(record, licenseAuthResult);
        return licenseAuthResult;
    }

    private void notifyLicenseCheckResultBySms(String mobilePhone, boolean succeed, String optUser){
        String smsTemplateId = succeed ? LICENSE_CHECK_SUCCESS_MSGID : LICENSE_CHECK_FAIL_MSGID;
        String smsMode = CommConfigUtil.getSmsMode();
        int templateId = Integer.valueOf(smsTemplateId).intValue();
        try {
            if (StringUtils.equals(smsMode, "1")) {
                BaseResponse baseResponse = messageServ.asyncSendSMSTemplate(mobilePhone, templateId, optUser);
                if (baseResponse.getCode() == 0) {
                    log.info("驾照三要素核验结果短信通知成功，mobile={}", mobilePhone);
                }else {
                    log.warn("驾照三要素核验结果短信通知失败，mobile={}", mobilePhone);
                }
            }
        }catch (Exception ex) {
            log.warn("驾照三要素核验结果短信通知异常，mobile=" + mobilePhone, ex);
        }
    }

    public void updateAuthenticateList(DriverLicenseElementsAuthenticateRecord record) {
        //普通三要素查询场景，非查无
        //1. 无效掉旧的待查记录
        DriverLicenseElementsReauthenticateList updateRecord = new DriverLicenseElementsReauthenticateList();
        BeanCopyUtils.copyProperties(record, updateRecord);
        updateRecord.setUserId(record.getUserId());
        updateRecord.setStatus(0);
        String desc = "/" + record.getId();
        updateRecord.setMiscDesc(desc);
        licenseElementsReauthenticateListMapper.disableLastRecordsByUserId(updateRecord);
        //若本次查无，则除了无效旧的查无逻辑外，还需创建新的查无记录
        if(record.getAuthenticateStatus() == 3) {
            DriverLicenseElementsReauthenticateList reAuthRecord = new DriverLicenseElementsReauthenticateList();
            BeanCopyUtils.copyProperties(record, reAuthRecord);
            reAuthRecord.setId(null);
            reAuthRecord.setRetryTimes(0);
            reAuthRecord.setLastestAuthenticateTime(new Date());
            Calendar calendar = Calendar.getInstance(ComUtil.timeZoneChina);
            calendar.add(Calendar.MINUTE, 5);
            reAuthRecord.setNextAuthenticateTime(calendar.getTime());
            reAuthRecord.setMiscDesc(String.valueOf(record.getId()));
            licenseElementsReauthenticateListMapper.insertSelective(reAuthRecord);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public DrivingLicenseAuthResult drivingLicenseManualReAuthenticate(String authId, UpdateUserDto operator) throws AuthenticationException {
        //获取会员信息
        MembershipBasicInfo membershipInfo = membershipInfoMapper.getUserBasicInfo(authId, (short)0);
        if (membershipInfo == null) {
            log.error("三要素认证：不做认证，用户信息不存在或已注销完成, authId={}", authId);
            throw new AuthenticationException(-1, "用户不存在或已注销完成");
        }
        if (membershipInfo == null || StringUtils.isBlank(membershipInfo.getName())
                || StringUtils.isBlank(membershipInfo.getDriverCode())
                || StringUtils.isBlank(membershipInfo.getFileNo())
                || StringUtils.isBlank(membershipInfo.getDrivingLicenseImgUrl())
                || StringUtils.isBlank(membershipInfo.getFileNoImgUrl())) {
            //驾照信息不完整，不更新驾照三要素记录
            log.warn("驾照信息不完整，不更新驾照三要素记录");
            throw new AuthenticationException(-1, "驾照信息不完整，不做三要素核验");
        }
        //保存并变更状态为待认证
        SaveDriverElementsAuthenticateInput driverElementsAuthenticateInput = new SaveDriverElementsAuthenticateInput();
        driverElementsAuthenticateInput.setUserId(membershipInfo.getPkId());
        driverElementsAuthenticateInput.setAuthenticateStatus(0);
        driverElementsAuthenticateInput.setName(membershipInfo.getName());
        driverElementsAuthenticateInput.setCreateTime(new Date());
        driverElementsAuthenticateInput.setCreateOperName(authId);
        driverElementsAuthenticateInput.setDriverCode(membershipInfo.getDriverCode());
        driverElementsAuthenticateInput.setFileNo(membershipInfo.getFileNo());
        driverElementsAuthenticateInput.setDriverLicenseImgUrl(membershipInfo.getDrivingLicenseImgUrl());
        driverElementsAuthenticateInput.setFileNoImgUrl(membershipInfo.getFileNoImgUrl());
        driverElementsAuthenticateInput.setMiscDesc("会员系统三要素重新验证");
        //saveDriverLicenseElementsAuthenticateRecordOnly(authId, driverElementsAuthenticateInput);
        saveDriverLicenseElementsAuthenticateRecord(authId, driverElementsAuthenticateInput);
        //调用第三方接口进行驾照三要素校验
        return drivingLicenseAuthenticate(membershipInfo, false, operator, null);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void manualLicenseElementsAuthenticateSuccess(String authId, UpdateUserDto operator) throws AuthenticationException {
        //1. 仅当前驾照三要素认证状态不为通过时可用
        MembershipInfoWithBLOBs member = membershipInfoMapper.selectByAuthId(authId, 0);
        if(member == null || member.getAccountStatus() != 0) {
            throw new AuthenticationException(StatusCode.MEMBER_NOT_EXIT);
        }
        boolean isMainland = ComUtil.checkIsUsersInMainland(member.getNational());
        if(!isMainland) {
            throw new AuthenticationException(-1, "非本籍大陆用户无需做三要素认证");
        }
        if(NumberUtils.INTEGER_ONE.equals(member.getLicenseElementsAuthStatus())) {
            throw new AuthenticationException(-1, "三要素认证通过者无需做三要素认证");
        }
        if(StringUtils.isBlank(member.getDriverCode()) ) {
            throw new AuthenticationException(-1, "驾照编号不完整，不能认证通过");
        }
        //暂时不做三要素完整性、有效期及准驾车型的检查（原产品需求,后续理应恢复校验）
//        if(StringUtils.isBlank(member.getDriverCode()) || StringUtils.isBlank(member.getName())
//                || StringUtils.isBlank(member.getFileNo()) ) {
//            throw new AuthenticationException(-1, "驾照编号/姓名/档案编号不完整，不能认证通过");
//        }
//        StatusCode checkResult = LicenseStatusChecker.checkLicenseAuthStatus(member.getDrivingLicenseType(),
//                member.getLicenseExpirationTime(), StringUtils.EMPTY);
//        if(checkResult != null) {
//            throw new AuthenticationException(-1, checkResult.getMsg() + "不能认证通过");
//        }
        //1. 更新驾照三要素
        HidLog.membership(BussinessConstants.MEM_LIC_AUTH, "手动认证三要素通过-开始", member.getAuthId(), true);
        String remark = "会员系统手动三要素认证通过";
        if(StringUtils.isNotBlank(operator.getRemark())) {
            remark += operator.getRemark();
        }
        Date now = new Date();
        DriverLicenseElementsAuthenticateRecord insertRecord = new DriverLicenseElementsAuthenticateRecord();
        insertRecord.setDriverCode(member.getDriverCode());
        insertRecord.setName(member.getName());
        insertRecord.setFileNo(member.getFileNo());
        insertRecord.setDriverLicenseImgUrl(member.getFaceRecognitionImgUrl());
        insertRecord.setFileNoImgUrl(member.getFileNoImgUrl());
        insertRecord.setUserId(member.getPkId());
        insertRecord.setStatus(1);
        insertRecord.setAuthenticateStatus(1);
        insertRecord.setLicenseStatusMsg(StringUtils.EMPTY);
        insertRecord.setLicenseStatus(1);
        insertRecord.setElementsReviewItems(StringUtils.EMPTY);
        insertRecord.setUpdateOperId(operator.getUserId());
        insertRecord.setUpdateOperName(operator.getUserName());
        insertRecord.setUpdateTime(now);
        insertRecord.setCreateOperId(operator.getUserId());
        insertRecord.setCreateOperName(operator.getUserName());
        insertRecord.setCreateTime(now);
        insertRecord.setMiscDesc(remark);
        licenseElementsAuthenticateRecordMapper.insertSelective(insertRecord);

        //2. 同步更新会员表，会员驾照三要素信息, 驾照三要素状态未待认证。
        Integer authenticateStatus = null;
        if(member.getAuthenticationStatus().equals(0)) {
            authenticateStatus = 1;
        }
        updateMemberLicenseStatus(member.getPkId(), null,
                insertRecord.getAuthenticateStatus(), insertRecord.getElementsReviewItems(),
                insertRecord.getLicenseStatus(), insertRecord.getLicenseStatusMsg(), authenticateStatus, operator);
        HidLog.membership(BussinessConstants.MEM_UPDATE_LICENSE_STATUS, "手动认证三要素通过-变更三要素、驾照状态为认证通过", member.getAuthId(), true);
        //3. 更新驾照认证日志履历
        DriverLicenseElementsAuthenticateLog insertLog = new DriverLicenseElementsAuthenticateLog();
        BeanCopyUtils.copyProperties(insertRecord, insertLog);
        insertLog.setRecordId(insertRecord.getId());
        insertLog.setLogType(0);
        insertLog.setSupplier(-1);
        insertLog.setResult("1");
        insertLog.setResultCode("1");
        insertLog.setResultMsg("手动认证通过");
        insertLog.setId(null);
        licenseElementsAuthenticateLogMapper.insertSelective(insertLog);
        //4. 从待复查列表中移除
        updateAuthenticateList(insertRecord);
        HidLog.membership(BussinessConstants.MEM_LIC_AUTH, "手动认证三要素通过-结束", member.getAuthId(), true);
        //5. 会员操作日志记录
        String content = "驾照三要素认证：" + remark + ", 三要素认证状态/驾照认证状态均通过";
        if(authenticateStatus != null) {
            content += "，身份认证状态变更为未刷脸";
        }
        ComUtil.insertOperatorLog(content, authId, String.valueOf(operator.getUserId()),
                operator.getUserName(), userOperatorLogMapper);
        //记录操作日志
        UserOperationLogInput userOptRecord = new UserOperationLogInput();
        userOptRecord.setUserId(member.getPkId());
        userOptRecord.setRefKey1(member.getMobilePhone());
        userOptRecord.setOperationType(MemOperateTypeEnum.LIC_AUTH.getCode());
        userOptRecord.setOperationContent(content);
        userOptRecord.setOperatorId(operator.getUserId());
        userOptRecord.setOperator(operator.getUserName());
        memberShipService.saveUserOperationLog(userOptRecord);
        //增加三要素校验通过短信通知
        notifyLicenseCheckResultBySms(member.getMobilePhone(), true, operator.getUserName());
    }

    public DriverLicenseElementsAuthenticateRecord licenseAuthenticateSequence(Long recordId, String authId, UpdateUserDto operator,
                                                                               String driverCode, String name, String fileNo, String sequence, Integer timeout) {
        Date nowTime = new Date();
        /**
         * 1. 调用驾照三要素接口，进行认证
         * 默认为0， 仅聚合认证;  1小视 10先小视再聚合 01先聚合再小视
         */
        sequence = (StringUtils.isBlank(sequence)) ? "0" : sequence;
        String lastResultCode = null;
        String licenseStatusMsg = StringUtils.EMPTY;
        String checkItem = "";
        for(int i = 0; i < sequence.length(); i ++) {
            int supplier = sequence.charAt(i) - '0';
            //调用驾照三要素认证
            SaveDriverElementsAuthenticateLogInput authResult;
            if("0".equals(elementsAuthenticateEnable)) {
                log.info("开发、测试环境 不认证三要素， 默认直接返回查证中");
                authResult = getDefaultElementsAuthResult(null, authId, operator, driverCode, name, fileNo, supplier);
            }else {
                authResult = licenseAuthenticateService.authenticate(authId, driverCode, name, fileNo, supplier, timeout);
            }
            //保存认证日志
            authResult.setRecordId(recordId);
            authResult.setCreateTime(nowTime);
            authResult.setCreateOperName(operator.getUserName());
            authResult.setCreateOperId(operator.getUserId());
            this.saveDriverLicenseElementsAuthenticateLog(authResult);
            //若查询结果为，三要素认证一致则不再尝试下一种认证
            if("1".equals(authResult.getResult())) {
                lastResultCode = authResult.getResult();
                //综合驾驶证状态msg
                if(StringUtils.isNotBlank(authResult.getLicenseStatusMsg())) {
                    licenseStatusMsg = authResult.getLicenseStatusMsg();
                }
                break;
            }
            /**
             * 三要素认证接口返回值(1认证一致 2不一致 3查无记录/无法核查 4异常情况)
             * 认证结果组合策略：
             * 已知：前一种认证结果！=认证通过
             * a. 若后一种认证结果为 一致/不一致，则以后一种为最终认证结果
             * b. 否则表示 两次均为查无记录或服务异常
             *    若两次有一次是查无记录，则综合查证结果为 查无记录;
             *    若两次均为服务异常，则综合查证结果为 服务异常;
             */
            if ("2".equals(authResult.getResult()) || "1".equals(authResult.getResult())) {
                lastResultCode = authResult.getResult();
                checkItem = authResult.getElementsReviewItems();
            }else {
                if("3".equals(lastResultCode) || "3".equals(authResult.getResult())) {
                    lastResultCode = "3";
                }else {
                    lastResultCode = "4";
                }
            }
        }
        /**
         * 2. 驾照三要素认证结果 --> 三要素认证状态
         * 认证结果 1认证一致 2不一致 3查无记录/无法核查 4异常情况
         * 三要素认证状态 0待认证 1认证通过 2认证不通过 3待重新认证（改为查证中)
         */
        Integer authenticateStatus;
        //查无记录/无法核查 or 服务异常 状态均为查证中。
        if(lastResultCode == "3" || lastResultCode == "4") {
            authenticateStatus = 3;
        }else {
            authenticateStatus = Integer.valueOf(lastResultCode);
        }
        Integer licenseStatus = LicenseStatusChecker.getLicenseStatus(licenseStatusMsg);
        //此处统一，只保存EVCARD不许可的驾照状态，(供应商不许可但EVCARD许可的状态不记录，可通过查询日志获得)
        licenseStatusMsg = (licenseStatus == 1) ? StringUtils.EMPTY : licenseStatusMsg;
        /**
         * 3. 更新用户三要素认证状态
         */
        //3.1 更新会员驾照三要素记录
        DriverLicenseElementsAuthenticateRecord updateRow = new DriverLicenseElementsAuthenticateRecord();
        updateRow.setAuthenticateStatus(authenticateStatus);
        updateRow.setLicenseStatus(licenseStatus);
        updateRow.setLicenseStatusMsg(licenseStatusMsg);
        updateRow.setElementsReviewItems(checkItem);
        updateRow.setDriverCode(driverCode);
        updateRow.setFileNo(fileNo);
        updateRow.setName(name);
        updateRow.setId(recordId);
        updateRow.setUpdateTime(nowTime);
        updateRow.setUpdateOperName(operator.getUserName());
        updateRow.setUpdateOperId(operator.getUserId());
        return updateRow;
    }

    public int updateMemberLicenseStatus(Long pkId, String authId, Integer elementsAuthStatus, String elementsReviewItems,
                                         Integer licenseStatus, String licenseStatusMsg, UpdateUserDto operator) {
        return updateMemberLicenseStatus(pkId, authId, elementsAuthStatus, elementsReviewItems, licenseStatus, licenseStatusMsg, null, operator);
    }

    public int updateMemberLicenseStatus(Long pkId, String authId, Integer elementsAuthStatus, String elementsReviewItems,
                                         Integer licenseStatus, String licenseStatusMsg, Integer authenticationStatus, UpdateUserDto operator) {
        HidLog.membership(BussinessConstants.MEM_UPDATE_LICENSE_STATUS, "更新会员驾照认证状态", authId, true);
        MembershipInfo updateMember = new MembershipInfo();
        updateMember.setAuthId(authId);
        updateMember.setPkId(pkId);
        updateMember.setMembershipType((short)0);
        updateMember.setLicenseElementsAuthStatus(elementsAuthStatus);
        updateMember.setElementsReviewItems(elementsReviewItems);
        updateMember.setLicenseAuthStatus(licenseStatus);
        updateMember.setLicenseStatusMsg(licenseStatusMsg);
        updateMember.setUpdatedUser(operator.getUserName());
        updateMember.setUpdatedTime(ComUtil.getSystemDate(ComUtil.DATE_TYPE4));
        StringBuffer sb = new StringBuffer("更新会员驾照认证状态");
        sb.append(", elementsAuthStatus=").append(elementsAuthStatus);
        sb.append(", elementsReviewItems=").append(elementsReviewItems);
        sb.append(", licenseStatus=").append(licenseStatus);
        sb.append(", licenseStatusMsg=").append(licenseStatusMsg);
        sb.append(", operator=").append(JSON.toJSONString(operator));
        if(authenticationStatus != null) {
            updateMember.setAuthenticationStatus(authenticationStatus);
            sb.append(", authenticationStatus=").append(authenticationStatus);
        }
        HidLog.membership(BussinessConstants.MEM_UPDATE_LICENSE_STATUS,  sb.toString(), authId, true);
        return membershipInfoMapper.updateReviewStatusByAuthId(updateMember);
    }

    public int updateMemberLicenseStatus(String authId, Integer elementsAuthStatus, String elementsReviewItems,
                                         Integer licenseStatus, String licenseStatusMsg, UpdateUserDto operator){
        return updateMemberLicenseStatus(null, authId, elementsAuthStatus, elementsReviewItems, licenseStatus, licenseStatusMsg, operator);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDriverLicenseElementsAuthenticateRecord(SaveDriverElementsAuthenticateInput input) throws AuthenticationException {
        saveDriverLicenseElementsAuthenticateRecord(String.valueOf(input.getUserId()), input);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDriverLicenseElementsAuthenticateRecord(String authId, SaveDriverElementsAuthenticateInput input) throws AuthenticationException {
        log.debug("保存驾驶证认证记录开始，input={}", JSON.toJSONString(input));
        if (!BussinessConstants.APP_KEY_CAR_SHARING.equals(input.getAppKey())
                && (input.getUserId() == null || StringUtils.isBlank(input.getName()) || StringUtils.isBlank(input.getDriverCode())
                || StringUtils.isBlank(input.getFileNo()) || StringUtils.isBlank(input.getDriverLicenseImgUrl())
                || StringUtils.isBlank(input.getFileNoImgUrl()) || input.getAuthenticateStatus() == null
                || input.getCreateTime() == null || StringUtils.isBlank(input.getCreateOperName()))) {
            log.error("保存驾驶证认证记录失败，参数不完整，authId={}, input={}", authId, JSON.toJSONString(input));
            throw new AuthenticationException(StatusCode.PARAM_EMPTY);
        }
        if(input.getCreateOperId() == null) {
            input.setCreateOperId(-1L);
        }
        DriverLicenseElementsAuthenticateRecord insertRow = new DriverLicenseElementsAuthenticateRecord();
        BeanUtils.copyProperties(input, insertRow);
        insertRow.setUpdateOperId(input.getCreateOperId());
        insertRow.setUpdateTime(input.getCreateTime());
        insertRow.setUpdateOperName(input.getCreateOperName());
        licenseElementsAuthenticateRecordMapper.insertSelective(insertRow);
        UpdateUserDto operator = new UpdateUserDto();
        operator.setUserId(input.getCreateOperId());
        operator.setUserName(input.getCreateOperName());
        operator.setUpdateTime(input.getCreateTime());
        //同步更新会员表，会员驾照三要素信息, 驾照三要素状态未待认证。
        updateMemberLicenseStatus(input.getUserId(), null, 0, "",
                0, StringUtils.EMPTY, operator);
        HidLog.membership(BussinessConstants.MEM_LIC_AUTH, "写待认证记录，驾照三要素状态->待认证", authId, true);
        log.debug("保存驾驶证认证记录结束，input={}", JSON.toJSONString(input));
        /**
         * 三要素认证状态埋点
         */
        //TODO 确认
//        try {
//            MembershipInfoWithBLOBs member = membershipInfoMapper.selectByPrimaryKey(input.getUserId());
//            if (member != null) {
//                Map<String, Object> userProperties = new HashMap<>();
//                userProperties.put("user_threeElementsOfDrivingLicenseCertificationStatus", "待认证");
//                sensorsdataService.profileSet(member.getAuthId(), true, userProperties);
//            }
//        } catch (Exception e) {
//            log.error("三要素用户表认证状态埋点失败, userId=" + input.getUserId(), e);
//        }
        //会员操作日志记录
        String content = "驾照三要素认证：生成待认证记录";
        if(StringUtils.isNotBlank(input.getMiscDesc())) {
            content += "(" + input.getMiscDesc() + ")";
        }
        ComUtil.insertOperatorLog(content, authId, String.valueOf(operator.getUserId()),
                operator.getUserName(), userOperatorLogMapper);
        //记录操作日志
        UserOperationLogInput userOptRecord = new UserOperationLogInput();
        userOptRecord.setUserId(input.getUserId());
        userOptRecord.setRefKey1(authId);
        userOptRecord.setOperationType(MemOperateTypeEnum.LIC_AUTH.getCode());
        userOptRecord.setOperationContent(content);
        userOptRecord.setOperatorId(operator.getUserId());
        userOptRecord.setOperator(operator.getUserName());
        memberShipService.saveUserOperationLog(userOptRecord);
    }

    //Cache<String, Boolean> licenseAuthConfigCache = CacheBuilder.newBuilder().maximumSize(100).expireAfterWrite(1, TimeUnit.HOURS).build();
    public boolean checkCityIsAutoLicenseAuth(String cityName){
        try{
//            //城市是否开启自动三要素认证，此配置项变更后6小时生效
//            Boolean cachedStatus = licenseAuthConfigCache.getIfPresent(cityName);
//            if(cachedStatus != null){
//                return cachedStatus;
//            }
            Integer status = appConfigRpcService.queryDriverLienseConfigByName(cityName);
            boolean enableAutoCheck = NumberUtils.INTEGER_ONE.equals(status);
            //licenseAuthConfigCache.put(cityName, autoCheck);
            return enableAutoCheck;
        }catch (Exception ex) {
            log.error("获取城市是否开启三要素自动验证配置失败, cityName=" + cityName, ex);
        }
        return false;
    }


    public void saveDriverLicenseElementAuthRecord(MembershipInfoWithBLOBs membershipInfo, String appKey, UpdateUserDto operator)
            throws AuthenticationException {
        SaveDriverElementsAuthenticateInput authenticateInput = new SaveDriverElementsAuthenticateInput();
        authenticateInput.setUserId(membershipInfo.getPkId());
        authenticateInput.setName(membershipInfo.getName());
        authenticateInput.setDriverCode(membershipInfo.getDriverCode());
        authenticateInput.setFileNo(membershipInfo.getFileNo());
        authenticateInput.setDriverLicenseImgUrl(ComUtil.splitPicUrl(membershipInfo.getDrivingLicenseImgUrl()));
        authenticateInput.setFileNoImgUrl(ComUtil.splitPicUrl(membershipInfo.getFileNoImgUrl()));
        authenticateInput.setAuthenticateStatus(0);
        authenticateInput.setCreateTime(new Date());
        authenticateInput.setCreateOperName(operator.getUserName());
        authenticateInput.setMiscDesc(operator.getRemark());
        authenticateInput.setAppKey(appKey);
        this.saveDriverLicenseElementsAuthenticateRecord(membershipInfo.getAuthId(), authenticateInput);
    }

    @Override
    public void saveDriverLicenseElementsAuthenticateLog(SaveDriverElementsAuthenticateLogInput input) {
        DriverLicenseElementsAuthenticateLog insertRow = new DriverLicenseElementsAuthenticateLog();
        if(input.getCreateOperId() == null) {
            input.setCreateOperId(-1L);
        }
        if(StringUtils.isNotBlank(input.getResultMsg()) && input.getResultMsg().length() > 50) {
            input.setResultMsg(input.getResultMsg().substring(0, 50));
        }
        BeanUtils.copyProperties(input, insertRow);
        if(StringUtils.isNotBlank(input.getRequestId())) {
            insertRow.setMiscDesc(input.getRequestId());
        }
        insertRow.setCreateOperId(input.getCreateOperId());
        insertRow.setUpdateTime(input.getCreateTime());
        insertRow.setUpdateOperName(input.getCreateOperName());
        licenseElementsAuthenticateLogMapper.insertSelective(insertRow);
    }

    private static final String PREFIX_MEM_LIC_AUTH = "mem_license_auth:";
    private static final int SECONDS_MEM_LIC_AUTH = 60 * 60;
    private static final int MEM_LIC_AUTH_MAXRETRY_TIMES = 3;
    private synchronized BaseResponse checkMemberLicenseAuthenticateTimes(String authId){
        try(Jedis jedis = JedisUtil.getJedis()){
            String key = PREFIX_MEM_LIC_AUTH + authId;
            String memLicenseAuthTimes = jedis.get(key);
            if(StringUtils.isNotBlank(memLicenseAuthTimes)) {
                if(Integer.valueOf(memLicenseAuthTimes) > MEM_LIC_AUTH_MAXRETRY_TIMES) {
                    double minutes = Math.ceil(jedis.ttl(key) * 1.0 / 60);
                    return new BaseResponse(StatusCode.LICENSE_SUBMIT_TIME_LIMIT.getCode(),
                            StatusCode.LICENSE_SUBMIT_TIME_LIMIT.getMsg() + "，请" + (int)minutes +"分钟后重试");
                }
                jedis.incr(key);
                //处理并发情况下，计数器有效期被至为-1的问题
                long seconds = jedis.ttl(key);
                if(seconds < 0) {
                    jedis.expire(key, SECONDS_MEM_LIC_AUTH);
                }
            }else {
                jedis.set(key, "1");
                jedis.expire(key, SECONDS_MEM_LIC_AUTH);
            }
        }catch (Exception e) {
        }
        return null;
    }

    public static final String[] LICENSE_AUTH_STATUS = {"待认证","认证通过","认证不通过","查证中"};
    private String getLicenseAuthStatusDesc(DriverLicenseElementsAuthenticateRecord record) {
        Integer authenticateStatus = record.getAuthenticateStatus();
        StringBuffer sb = new StringBuffer(LICENSE_AUTH_STATUS[authenticateStatus]);
        if(record.getLicenseStatus() == 1) {
            sb.append("，驾照状态：可用车");
        }else if(record.getLicenseStatus() == 2) {
            sb.append("，驾照状态：不可用车");
        }else {
            sb.append("，驾照状态：待认证");
        }
        if(StringUtils.isNotBlank(record.getLicenseStatusMsg())) {
            sb.append("(" + record.getLicenseStatusMsg() + ")" );
        }
        return sb.toString();
    }

    private static final int REVIEW_ITEMS_COUNT = 12;
    private String buildReviewDetailStr(String reviewItems){
        if(StringUtils.isBlank(reviewItems)) {
            return StringUtils.EMPTY;
        }
        StringBuffer sb = new StringBuffer();
        for(int i = 0; i < reviewItems.length() && i < REVIEW_ITEMS_COUNT; i ++) {
            String flag = String.valueOf(reviewItems.charAt(0));
            if("1".equals(flag)) {
                sb.append(REVIEW_ITEMS_NAMES[i]).append("，");
            }
        }
        String desc = sb.toString();
        if (desc.endsWith("，")) {
            desc = desc.substring(0, desc.length() - 1);
        }
        return desc;
    }

    public void trackMemberReviewResult(String authId, MembershipInfo member, UpdateReviewStatusDto reviewStatusDto){
        try{
            //TODO 待确认埋点需求
            Integer reviewStatus = reviewStatusDto.getReviewStatus();
            String driverCode = member.getDriverCode();
            String mobile = member.getMobilePhone();
            String name = member.getName();
            String regTime = member.getRegTime();
            String reviewResultDesc = "无";
            Map<String, Object> trackParams = new HashMap<>();
            //审核通过
            if(reviewStatus == 1) {
                Map<String, Object> params = new HashMap<>();
                params.put("userId", authId);
                params.put("mobile", mobile);
                //TODO
                //params.put("registerTime", DateUtil.parseDate(regTime, ComUtil.DATE_TYPE4));
                if (driverCode.length() == 18) {
                    String gender = (Integer.parseInt(driverCode.substring(16, 17)) % 2 == 1 ? "男" : "女");
                    params.put("userGender", gender);
                } else {
                    params.put("userGender", "男");
                }
                params.put("userName", name);
                params.put("userType", "外部会员");
                params.put("checkPassTime", new Date());
                //sensorsdataService.profileSet(authId, true, params);
            } else if(reviewStatus == 2) {
                reviewResultDesc = "审核未通过，[" + buildReviewDetailStr(reviewStatusDto.getReviewItems()) + "]验证不正确";
            } else {
                reviewResultDesc = "审核失败";
            }
            trackParams.put("notPassReasons", reviewResultDesc);
            trackParams.put("result", reviewStatus == 1);
            //sensorsdataService.track(authId, true, "RegisterAndImproveInformationAudit", trackParams);
        }catch (Exception ex) {
            log.error("数据埋点-记录事件时捕获到异常, memberReview:authId=" + authId, ex);
        }
    }

    private String getLicenseInputTypeDesc(Integer licenseInputType) {
        if(NumberUtils.INTEGER_ONE.equals(licenseInputType)) {
            return "自动";
        }else if(NumberUtils.INTEGER_ZERO.equals(licenseInputType)) {
            return "手动";
        }
        return StringUtils.EMPTY;
    }

    /**
     * 推送APP系统消息
     * @param authId
     * @param msgType
     * @param templateId
     * @param params
     */
    private void appMessagePush(String authId, Integer msgType, int templateId, Map<String,String> params){
        try {
//            MessagePush messagePush = new MessagePush();
//            messagePush.setAuthId(authId);
//            messagePush.setMemberType(0);      // 会员类型 0：外部 1：内部
//            messagePush.setMessage(pushMsg);   // 消息内容
//            messagePush.setTitle(title);
//            // 1：网点消息 2：订单消息 3 : 系统消息
//            messagePush.setType(msgType);
//            messagePush.setMessageType(0);     // 0 ：消息通知 1：提醒
//            messageServ.push(messagePush, "was", "membership");
            messageServ.push(authId, 0, templateId, msgType, params, "membership");
        } catch (Exception e) {
            log.error("app消息推送: authId=" + authId + "template=" + templateId, e);
        }
    }

    private static final SaveDriverElementsAuthenticateLogInput getDefaultElementsAuthResult(Integer resultCode, String authId, UpdateUserDto operator,
                                                                                             String driverCode, String name, String fileNo, int suplier){
        if(resultCode == null) {
            //resultCode = (int)(Math.random() * 10) / 3 + 1;
            resultCode = 3;
        }
        SaveDriverElementsAuthenticateLogInput result = new SaveDriverElementsAuthenticateLogInput();
        result.setDriverCode(driverCode);
        result.setFileNo(fileNo);
        result.setName(name);
        result.setLogType(0);
        result.setLicenseStatusMsg("");
        result.setElementsReviewItems("");
        result.setResultMsg("");
        result.setResult(String.valueOf(resultCode));
        result.setResultCode(String.valueOf(resultCode));
        result.setSupplier(suplier);
//        if(resultCode == 1) {
//            int index = (int)(Math.random() * 10) / 13;
//            if(index == 12) {
//                result.setLicenseStatusMsg("");
//            }else {
//                result.setLicenseStatusMsg(UNVALID_LICENSE_STATUS[index]);
//            }
//        }
        return result;
    }


    @Override
    public void saveDriverLicenseElementsAuthenticateRecordOnly(String authId, SaveDriverElementsAuthenticateInput input) throws AuthenticationException {
        if (!BussinessConstants.APP_KEY_CAR_SHARING.equals(input.getAppKey())
                && (input.getUserId() == null || StringUtils.isBlank(input.getName()) || StringUtils.isBlank(input.getDriverCode())
                || StringUtils.isBlank(input.getFileNo()) || StringUtils.isBlank(input.getDriverLicenseImgUrl())
                || StringUtils.isBlank(input.getFileNoImgUrl()) || input.getAuthenticateStatus() == null
                || input.getCreateTime() == null || StringUtils.isBlank(input.getCreateOperName()))) {
            log.error("保存驾驶证认证记录失败，参数不完整，authId={}, input={}", authId, JSON.toJSONString(input));
            throw new AuthenticationException(StatusCode.PARAM_EMPTY);
        }
        if(input.getCreateOperId() == null) {
            input.setCreateOperId(-1L);
        }
        DriverLicenseElementsAuthenticateRecord insertRow = new DriverLicenseElementsAuthenticateRecord();
        BeanUtils.copyProperties(input, insertRow);
        insertRow.setUpdateOperId(input.getCreateOperId());
        insertRow.setUpdateTime(input.getCreateTime());
        insertRow.setUpdateOperName(input.getCreateOperName());
        licenseElementsAuthenticateRecordMapper.insertSelective(insertRow);
        HidLog.membership(BussinessConstants.MEM_LIC_AUTH, "写待认证记录，驾照三要素状态->待认证", authId, true);
        log.debug("保存驾驶证认证记录结束，input={}", JSON.toJSONString(input));
    }

    public void sendReviewSuccessMesage(String authId, String mobilePhone, int reviewStatus, Integer isNewUser, String operatorName) {
        if(reviewStatus != 1) {
            log.error("审核状态不为通过，不进行审核事件及好友邀请事件推送, authId={}, reviewStatus={}", authId, reviewStatus);
            return;
        }
        try {
            //1. 推送邀请好友消息
            BaseResponse baseResponse2 = memberShipInvitationServ.giveFriendInvitationGiftBag(authId, mobilePhone, "00", "mas");
            if (baseResponse2.getCode() != 0) {
                log.warn("会员" + authId + "调用会员服务发送好友邀请礼包失败");
            }
        } catch (Exception e) {
            log.error("会员" + authId + "审核事件及好友邀请事件推送失败", e);
        }//2. 推送会员审核事件
        MemberAudit audit = new MemberAudit();
        audit.setAuthId(authId);
        audit.setMobilePhone(mobilePhone);
        audit.setOptUser(operatorName);
        audit.setNewUser(isNewUser);
        audit.setReviewStatus(String.valueOf(reviewStatus));
        //推送审核不通过事件
        sendMQMemberAuditEvent(audit);
    }

    /**
     * 推送审核事件消息推送
     */
    private void sendMQMemberAuditEvent(MemberAudit memberAudit) {
        try {
            byte[] messageBody = ProtobufUtil.serializeProtobuf(memberAudit);
            String messageKey = "membership#" + UUID.randomUUID().toString().replace("-", "");
            Message msg = new Message(evcardRawDataTopic, EventEnum.MEMBER_AUDIT.getTag(), messageBody);
            msg.setKey(messageKey);
            producer.send(msg);
            log.info("推送会员审核事件，推送成功，authId={}, memberAudit={}", memberAudit.getAuthId(), memberAudit);
        } catch (Exception e) {
            log.error("推送会员审核事件，推送失败，authId={}, memberAudit={}", memberAudit.getAuthId(), memberAudit);
        }
    }
}
