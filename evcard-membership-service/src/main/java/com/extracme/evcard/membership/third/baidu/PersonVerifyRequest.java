package com.extracme.evcard.membership.third.baidu;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Discription
 * @date 2021/3/17
 */
@Data
public class PersonVerifyRequest implements Serializable {
    /**
     * 图片信息，图片上传方式根据image_type来指定
     * 必须
     */
    private String image;

    /**
     * 图片类型(推荐使用类型 2 FACE_TOKEN)
     * BASE64. 图片base64值
     * URL. 图片下载url
     * FACE_TOKEN. 人脸图片唯一标识，FACE_TOKEN（人脸检测时，为每个人连赋予的一个唯一face_token）
     * 必须
     */
    private String image_type;

    /**
     * 身份证号
     * 必须
     */
    private String id_card_number;

    /**
     * 姓名(UTF-8编码)
     * 必须
     */
    private String name;

    /**
     * 图片质量控制
     * NONE/LOW/NORMAL/HIGH/NONE
     * 默认NORMAL
     * 必须
     */
    private String quality_control;

    /**
     * 活体检测控制
     * NONE/LOW/NORMAL/HIGH/NONE
     * 默认NONE
     * 必须
     */
    private String liveness_control;

    /**
     * 合成图控制
     * NONE/LOW/NORMAL/HIGH/NONE
     * 默认NORMAL
     * 必须
     */
    private String spoofing_control;

}
