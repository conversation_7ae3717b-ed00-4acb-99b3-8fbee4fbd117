<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.core.dao.OrgInfoMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.OrgInfo" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="ORG_ID" property="orgId" jdbcType="VARCHAR" />
    <result column="ORG_NAME" property="orgName" jdbcType="VARCHAR" />
    <result column="ORG_KIND" property="orgKind" jdbcType="VARCHAR" />
    <result column="ORG_CLASS" property="orgClass" jdbcType="DOUBLE" />
    <result column="CONTACT" property="contact" jdbcType="VARCHAR" />
    <result column="TEL" property="tel" jdbcType="VARCHAR" />
    <result column="MOBILE_PHONE" property="mobilePhone" jdbcType="VARCHAR" />
    <result column="ADDRESS" property="address" jdbcType="VARCHAR" />
    <result column="MAIL" property="mail" jdbcType="VARCHAR" />
    <result column="LICENSE_NO" property="licenseNo" jdbcType="VARCHAR" />
    <result column="FAX" property="fax" jdbcType="VARCHAR" />
    <result column="COUNTY" property="county" jdbcType="VARCHAR" />
    <result column="CITY" property="city" jdbcType="DOUBLE" />
    <result column="PROVINCE" property="province" jdbcType="DOUBLE" />
    <result column="CORPORATE" property="corporate" jdbcType="VARCHAR" />
    <result column="LOCATION" property="location" jdbcType="VARCHAR" />
    <result column="RTOLN" property="rtoln" jdbcType="VARCHAR" />
    <result column="LICENSE_NO_IMG_URL" property="licenseNoImgUrl" jdbcType="VARCHAR" />
    <result column="TAX_REGISTRATION_IMG_URL" property="taxRegistrationImgUrl" jdbcType="VARCHAR" />
    <result column="ORG_CODE_IMG_URL" property="orgCodeImgUrl" jdbcType="VARCHAR" />
    <result column="REMARK" property="remark" jdbcType="VARCHAR" />
    <result column="PAY_WAY" property="payWay" jdbcType="DOUBLE" />
    <result column="CREATED_USER" property="createdUser" jdbcType="VARCHAR" />
    <result column="CREATED_TIME" property="createdTime" jdbcType="VARCHAR" />
    <result column="UPDATED_USER" property="updatedUser" jdbcType="VARCHAR" />
    <result column="UPDATED_TIME" property="updatedTime" jdbcType="VARCHAR" />
    <result column="STATUS" property="status" jdbcType="DOUBLE" />
    <result column="ORIGIN" property="origin" jdbcType="DOUBLE" />
    <result column="DEPOSIT" property="deposit" jdbcType="DOUBLE" />
    <result column="RESERVE_AMOUNT" property="reserveAmount" jdbcType="DOUBLE" />
    <result column="RENT_MINS" property="rentMins" jdbcType="DOUBLE" />
    <result column="AGENCY_ID" property="agencyId" jdbcType="VARCHAR" />
    <result column="CITY_SHORT" property="cityShort" jdbcType="VARCHAR" />
    <result column="INSIDE_FLAG" property="insideFlag" jdbcType="INTEGER" />
    <result column="ORG_ALIAS" property="orgAlias" jdbcType="VARCHAR" />
    <result column="CHECK_DATE" property="checkDate" jdbcType="VARCHAR" />
    <result column="CHECK_ALERT" property="checkAlert" jdbcType="INTEGER" />
    <result column="BALANCE_MAIL" property="balanceMail" jdbcType="VARCHAR" />
    <result column="ORG_PROPERTY" property="orgProperty" jdbcType="TINYINT" />
    <result column="ORG_PROTRETY" property="orgProtrety" jdbcType="BIT" />
    <result column="ORG_SUB_NAME" property="orgSubName" jdbcType="VARCHAR" />
    <result column="ZHI_MA_CREDIT_FLAG" property="zhiMaCreditFlag" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, ORG_ID, ORG_NAME, ORG_KIND, ORG_CLASS, CONTACT, TEL, MOBILE_PHONE, ADDRESS, MAIL, 
    LICENSE_NO, FAX, COUNTY, CITY, PROVINCE, CORPORATE, LOCATION, RTOLN, LICENSE_NO_IMG_URL, 
    TAX_REGISTRATION_IMG_URL, ORG_CODE_IMG_URL, REMARK, PAY_WAY, CREATED_USER, CREATED_TIME, 
    UPDATED_USER, UPDATED_TIME, STATUS, ORIGIN, DEPOSIT, RESERVE_AMOUNT, RENT_MINS, AGENCY_ID, 
    CITY_SHORT, INSIDE_FLAG, ORG_ALIAS, CHECK_DATE, CHECK_ALERT, BALANCE_MAIL, ORG_PROPERTY, 
    ORG_PROTRETY, ORG_SUB_NAME, ZHI_MA_CREDIT_FLAG
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from ${isvSchema}.org_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from ${isvSchema}.org_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.membership.core.model.OrgInfo" >
    insert into ${isvSchema}.org_info (id, ORG_ID, ORG_NAME,
      ORG_KIND, ORG_CLASS, CONTACT, 
      TEL, MOBILE_PHONE, ADDRESS, 
      MAIL, LICENSE_NO, FAX, 
      COUNTY, CITY, PROVINCE, 
      CORPORATE, LOCATION, RTOLN, 
      LICENSE_NO_IMG_URL, TAX_REGISTRATION_IMG_URL, 
      ORG_CODE_IMG_URL, REMARK, PAY_WAY, 
      CREATED_USER, CREATED_TIME, UPDATED_USER, 
      UPDATED_TIME, STATUS, ORIGIN, 
      DEPOSIT, RESERVE_AMOUNT, RENT_MINS, 
      AGENCY_ID, CITY_SHORT, INSIDE_FLAG, 
      ORG_ALIAS, CHECK_DATE, CHECK_ALERT, 
      BALANCE_MAIL, ORG_PROPERTY, ORG_PROTRETY, 
      ORG_SUB_NAME, ZHI_MA_CREDIT_FLAG)
    values (#{id,jdbcType=BIGINT}, #{orgId,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR}, 
      #{orgKind,jdbcType=VARCHAR}, #{orgClass,jdbcType=DOUBLE}, #{contact,jdbcType=VARCHAR}, 
      #{tel,jdbcType=VARCHAR}, #{mobilePhone,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, 
      #{mail,jdbcType=VARCHAR}, #{licenseNo,jdbcType=VARCHAR}, #{fax,jdbcType=VARCHAR}, 
      #{county,jdbcType=VARCHAR}, #{city,jdbcType=DOUBLE}, #{province,jdbcType=DOUBLE}, 
      #{corporate,jdbcType=VARCHAR}, #{location,jdbcType=VARCHAR}, #{rtoln,jdbcType=VARCHAR}, 
      #{licenseNoImgUrl,jdbcType=VARCHAR}, #{taxRegistrationImgUrl,jdbcType=VARCHAR}, 
      #{orgCodeImgUrl,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{payWay,jdbcType=DOUBLE}, 
      #{createdUser,jdbcType=VARCHAR}, #{createdTime,jdbcType=VARCHAR}, #{updatedUser,jdbcType=VARCHAR}, 
      #{updatedTime,jdbcType=VARCHAR}, #{status,jdbcType=DOUBLE}, #{origin,jdbcType=DOUBLE}, 
      #{deposit,jdbcType=DOUBLE}, #{reserveAmount,jdbcType=DOUBLE}, #{rentMins,jdbcType=DOUBLE}, 
      #{agencyId,jdbcType=VARCHAR}, #{cityShort,jdbcType=VARCHAR}, #{insideFlag,jdbcType=INTEGER}, 
      #{orgAlias,jdbcType=VARCHAR}, #{checkDate,jdbcType=VARCHAR}, #{checkAlert,jdbcType=INTEGER}, 
      #{balanceMail,jdbcType=VARCHAR}, #{orgProperty,jdbcType=TINYINT}, #{orgProtrety,jdbcType=BIT}, 
      #{orgSubName,jdbcType=VARCHAR}, #{zhiMaCreditFlag,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.core.model.OrgInfo" >
    insert into ${isvSchema}.org_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="orgId != null" >
        ORG_ID,
      </if>
      <if test="orgName != null" >
        ORG_NAME,
      </if>
      <if test="orgKind != null" >
        ORG_KIND,
      </if>
      <if test="orgClass != null" >
        ORG_CLASS,
      </if>
      <if test="contact != null" >
        CONTACT,
      </if>
      <if test="tel != null" >
        TEL,
      </if>
      <if test="mobilePhone != null" >
        MOBILE_PHONE,
      </if>
      <if test="address != null" >
        ADDRESS,
      </if>
      <if test="mail != null" >
        MAIL,
      </if>
      <if test="licenseNo != null" >
        LICENSE_NO,
      </if>
      <if test="fax != null" >
        FAX,
      </if>
      <if test="county != null" >
        COUNTY,
      </if>
      <if test="city != null" >
        CITY,
      </if>
      <if test="province != null" >
        PROVINCE,
      </if>
      <if test="corporate != null" >
        CORPORATE,
      </if>
      <if test="location != null" >
        LOCATION,
      </if>
      <if test="rtoln != null" >
        RTOLN,
      </if>
      <if test="licenseNoImgUrl != null" >
        LICENSE_NO_IMG_URL,
      </if>
      <if test="taxRegistrationImgUrl != null" >
        TAX_REGISTRATION_IMG_URL,
      </if>
      <if test="orgCodeImgUrl != null" >
        ORG_CODE_IMG_URL,
      </if>
      <if test="remark != null" >
        REMARK,
      </if>
      <if test="payWay != null" >
        PAY_WAY,
      </if>
      <if test="createdUser != null" >
        CREATED_USER,
      </if>
      <if test="createdTime != null" >
        CREATED_TIME,
      </if>
      <if test="updatedUser != null" >
        UPDATED_USER,
      </if>
      <if test="updatedTime != null" >
        UPDATED_TIME,
      </if>
      <if test="status != null" >
        STATUS,
      </if>
      <if test="origin != null" >
        ORIGIN,
      </if>
      <if test="deposit != null" >
        DEPOSIT,
      </if>
      <if test="reserveAmount != null" >
        RESERVE_AMOUNT,
      </if>
      <if test="rentMins != null" >
        RENT_MINS,
      </if>
      <if test="agencyId != null" >
        AGENCY_ID,
      </if>
      <if test="cityShort != null" >
        CITY_SHORT,
      </if>
      <if test="insideFlag != null" >
        INSIDE_FLAG,
      </if>
      <if test="orgAlias != null" >
        ORG_ALIAS,
      </if>
      <if test="checkDate != null" >
        CHECK_DATE,
      </if>
      <if test="checkAlert != null" >
        CHECK_ALERT,
      </if>
      <if test="balanceMail != null" >
        BALANCE_MAIL,
      </if>
      <if test="orgProperty != null" >
        ORG_PROPERTY,
      </if>
      <if test="orgProtrety != null" >
        ORG_PROTRETY,
      </if>
      <if test="orgSubName != null" >
        ORG_SUB_NAME,
      </if>
      <if test="zhiMaCreditFlag != null" >
        ZHI_MA_CREDIT_FLAG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orgId != null" >
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null" >
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="orgKind != null" >
        #{orgKind,jdbcType=VARCHAR},
      </if>
      <if test="orgClass != null" >
        #{orgClass,jdbcType=DOUBLE},
      </if>
      <if test="contact != null" >
        #{contact,jdbcType=VARCHAR},
      </if>
      <if test="tel != null" >
        #{tel,jdbcType=VARCHAR},
      </if>
      <if test="mobilePhone != null" >
        #{mobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="address != null" >
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="mail != null" >
        #{mail,jdbcType=VARCHAR},
      </if>
      <if test="licenseNo != null" >
        #{licenseNo,jdbcType=VARCHAR},
      </if>
      <if test="fax != null" >
        #{fax,jdbcType=VARCHAR},
      </if>
      <if test="county != null" >
        #{county,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        #{city,jdbcType=DOUBLE},
      </if>
      <if test="province != null" >
        #{province,jdbcType=DOUBLE},
      </if>
      <if test="corporate != null" >
        #{corporate,jdbcType=VARCHAR},
      </if>
      <if test="location != null" >
        #{location,jdbcType=VARCHAR},
      </if>
      <if test="rtoln != null" >
        #{rtoln,jdbcType=VARCHAR},
      </if>
      <if test="licenseNoImgUrl != null" >
        #{licenseNoImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="taxRegistrationImgUrl != null" >
        #{taxRegistrationImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="orgCodeImgUrl != null" >
        #{orgCodeImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="payWay != null" >
        #{payWay,jdbcType=DOUBLE},
      </if>
      <if test="createdUser != null" >
        #{createdUser,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        #{createdTime,jdbcType=VARCHAR},
      </if>
      <if test="updatedUser != null" >
        #{updatedUser,jdbcType=VARCHAR},
      </if>
      <if test="updatedTime != null" >
        #{updatedTime,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=DOUBLE},
      </if>
      <if test="origin != null" >
        #{origin,jdbcType=DOUBLE},
      </if>
      <if test="deposit != null" >
        #{deposit,jdbcType=DOUBLE},
      </if>
      <if test="reserveAmount != null" >
        #{reserveAmount,jdbcType=DOUBLE},
      </if>
      <if test="rentMins != null" >
        #{rentMins,jdbcType=DOUBLE},
      </if>
      <if test="agencyId != null" >
        #{agencyId,jdbcType=VARCHAR},
      </if>
      <if test="cityShort != null" >
        #{cityShort,jdbcType=VARCHAR},
      </if>
      <if test="insideFlag != null" >
        #{insideFlag,jdbcType=INTEGER},
      </if>
      <if test="orgAlias != null" >
        #{orgAlias,jdbcType=VARCHAR},
      </if>
      <if test="checkDate != null" >
        #{checkDate,jdbcType=VARCHAR},
      </if>
      <if test="checkAlert != null" >
        #{checkAlert,jdbcType=INTEGER},
      </if>
      <if test="balanceMail != null" >
        #{balanceMail,jdbcType=VARCHAR},
      </if>
      <if test="orgProperty != null" >
        #{orgProperty,jdbcType=TINYINT},
      </if>
      <if test="orgProtrety != null" >
        #{orgProtrety,jdbcType=BIT},
      </if>
      <if test="orgSubName != null" >
        #{orgSubName,jdbcType=VARCHAR},
      </if>
      <if test="zhiMaCreditFlag != null" >
        #{zhiMaCreditFlag,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.core.model.OrgInfo" >
    update ${isvSchema}.org_info
    <set >
      <if test="orgId != null" >
        ORG_ID = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null" >
        ORG_NAME = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="orgKind != null" >
        ORG_KIND = #{orgKind,jdbcType=VARCHAR},
      </if>
      <if test="orgClass != null" >
        ORG_CLASS = #{orgClass,jdbcType=DOUBLE},
      </if>
      <if test="contact != null" >
        CONTACT = #{contact,jdbcType=VARCHAR},
      </if>
      <if test="tel != null" >
        TEL = #{tel,jdbcType=VARCHAR},
      </if>
      <if test="mobilePhone != null" >
        MOBILE_PHONE = #{mobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="address != null" >
        ADDRESS = #{address,jdbcType=VARCHAR},
      </if>
      <if test="mail != null" >
        MAIL = #{mail,jdbcType=VARCHAR},
      </if>
      <if test="licenseNo != null" >
        LICENSE_NO = #{licenseNo,jdbcType=VARCHAR},
      </if>
      <if test="fax != null" >
        FAX = #{fax,jdbcType=VARCHAR},
      </if>
      <if test="county != null" >
        COUNTY = #{county,jdbcType=VARCHAR},
      </if>
      <if test="city != null" >
        CITY = #{city,jdbcType=DOUBLE},
      </if>
      <if test="province != null" >
        PROVINCE = #{province,jdbcType=DOUBLE},
      </if>
      <if test="corporate != null" >
        CORPORATE = #{corporate,jdbcType=VARCHAR},
      </if>
      <if test="location != null" >
        LOCATION = #{location,jdbcType=VARCHAR},
      </if>
      <if test="rtoln != null" >
        RTOLN = #{rtoln,jdbcType=VARCHAR},
      </if>
      <if test="licenseNoImgUrl != null" >
        LICENSE_NO_IMG_URL = #{licenseNoImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="taxRegistrationImgUrl != null" >
        TAX_REGISTRATION_IMG_URL = #{taxRegistrationImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="orgCodeImgUrl != null" >
        ORG_CODE_IMG_URL = #{orgCodeImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="payWay != null" >
        PAY_WAY = #{payWay,jdbcType=DOUBLE},
      </if>
      <if test="createdUser != null" >
        CREATED_USER = #{createdUser,jdbcType=VARCHAR},
      </if>
      <if test="createdTime != null" >
        CREATED_TIME = #{createdTime,jdbcType=VARCHAR},
      </if>
      <if test="updatedUser != null" >
        UPDATED_USER = #{updatedUser,jdbcType=VARCHAR},
      </if>
      <if test="updatedTime != null" >
        UPDATED_TIME = #{updatedTime,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        STATUS = #{status,jdbcType=DOUBLE},
      </if>
      <if test="origin != null" >
        ORIGIN = #{origin,jdbcType=DOUBLE},
      </if>
      <if test="deposit != null" >
        DEPOSIT = #{deposit,jdbcType=DOUBLE},
      </if>
      <if test="reserveAmount != null" >
        RESERVE_AMOUNT = #{reserveAmount,jdbcType=DOUBLE},
      </if>
      <if test="rentMins != null" >
        RENT_MINS = #{rentMins,jdbcType=DOUBLE},
      </if>
      <if test="agencyId != null" >
        AGENCY_ID = #{agencyId,jdbcType=VARCHAR},
      </if>
      <if test="cityShort != null" >
        CITY_SHORT = #{cityShort,jdbcType=VARCHAR},
      </if>
      <if test="insideFlag != null" >
        INSIDE_FLAG = #{insideFlag,jdbcType=INTEGER},
      </if>
      <if test="orgAlias != null" >
        ORG_ALIAS = #{orgAlias,jdbcType=VARCHAR},
      </if>
      <if test="checkDate != null" >
        CHECK_DATE = #{checkDate,jdbcType=VARCHAR},
      </if>
      <if test="checkAlert != null" >
        CHECK_ALERT = #{checkAlert,jdbcType=INTEGER},
      </if>
      <if test="balanceMail != null" >
        BALANCE_MAIL = #{balanceMail,jdbcType=VARCHAR},
      </if>
      <if test="orgProperty != null" >
        ORG_PROPERTY = #{orgProperty,jdbcType=TINYINT},
      </if>
      <if test="orgProtrety != null" >
        ORG_PROTRETY = #{orgProtrety,jdbcType=BIT},
      </if>
      <if test="orgSubName != null" >
        ORG_SUB_NAME = #{orgSubName,jdbcType=VARCHAR},
      </if>
      <if test="zhiMaCreditFlag != null" >
        ZHI_MA_CREDIT_FLAG = #{zhiMaCreditFlag,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.core.model.OrgInfo" >
    update ${isvSchema}.org_info
    set ORG_ID = #{orgId,jdbcType=VARCHAR},
      ORG_NAME = #{orgName,jdbcType=VARCHAR},
      ORG_KIND = #{orgKind,jdbcType=VARCHAR},
      ORG_CLASS = #{orgClass,jdbcType=DOUBLE},
      CONTACT = #{contact,jdbcType=VARCHAR},
      TEL = #{tel,jdbcType=VARCHAR},
      MOBILE_PHONE = #{mobilePhone,jdbcType=VARCHAR},
      ADDRESS = #{address,jdbcType=VARCHAR},
      MAIL = #{mail,jdbcType=VARCHAR},
      LICENSE_NO = #{licenseNo,jdbcType=VARCHAR},
      FAX = #{fax,jdbcType=VARCHAR},
      COUNTY = #{county,jdbcType=VARCHAR},
      CITY = #{city,jdbcType=DOUBLE},
      PROVINCE = #{province,jdbcType=DOUBLE},
      CORPORATE = #{corporate,jdbcType=VARCHAR},
      LOCATION = #{location,jdbcType=VARCHAR},
      RTOLN = #{rtoln,jdbcType=VARCHAR},
      LICENSE_NO_IMG_URL = #{licenseNoImgUrl,jdbcType=VARCHAR},
      TAX_REGISTRATION_IMG_URL = #{taxRegistrationImgUrl,jdbcType=VARCHAR},
      ORG_CODE_IMG_URL = #{orgCodeImgUrl,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      PAY_WAY = #{payWay,jdbcType=DOUBLE},
      CREATED_USER = #{createdUser,jdbcType=VARCHAR},
      CREATED_TIME = #{createdTime,jdbcType=VARCHAR},
      UPDATED_USER = #{updatedUser,jdbcType=VARCHAR},
      UPDATED_TIME = #{updatedTime,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=DOUBLE},
      ORIGIN = #{origin,jdbcType=DOUBLE},
      DEPOSIT = #{deposit,jdbcType=DOUBLE},
      RESERVE_AMOUNT = #{reserveAmount,jdbcType=DOUBLE},
      RENT_MINS = #{rentMins,jdbcType=DOUBLE},
      AGENCY_ID = #{agencyId,jdbcType=VARCHAR},
      CITY_SHORT = #{cityShort,jdbcType=VARCHAR},
      INSIDE_FLAG = #{insideFlag,jdbcType=INTEGER},
      ORG_ALIAS = #{orgAlias,jdbcType=VARCHAR},
      CHECK_DATE = #{checkDate,jdbcType=VARCHAR},
      CHECK_ALERT = #{checkAlert,jdbcType=INTEGER},
      BALANCE_MAIL = #{balanceMail,jdbcType=VARCHAR},
      ORG_PROPERTY = #{orgProperty,jdbcType=TINYINT},
      ORG_PROTRETY = #{orgProtrety,jdbcType=BIT},
      ORG_SUB_NAME = #{orgSubName,jdbcType=VARCHAR},
      ZHI_MA_CREDIT_FLAG = #{zhiMaCreditFlag,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryOrgInfoByOrgId" resultMap="BaseResultMap" parameterType="string" >
    select
    <include refid="Base_Column_List" />
    from ${isvSchema}.org_info
    where ORG_ID = #{orgId,jdbcType=VARCHAR}
  </select>

  <select id="queryAllOrgInfo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${isvSchema}.org_info
  </select>

  <select id="batchQueryOrgName" resultType="map" parameterType="list">
    SELECT
     org_id as orgId,
     ORG_NAME as orgName
    from ${isvSchema}.org_info
    where ORG_ID in
    <foreach item="item" collection="list" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>
</mapper>