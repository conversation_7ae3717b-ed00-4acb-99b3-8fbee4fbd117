package com.extracme.evcard.membership.credit.dao;

import com.extracme.evcard.membership.credit.dto.AuthCreditEventRecordPageDto;
import com.extracme.evcard.membership.credit.dto.CreditEventRecordDetailDto;
import com.extracme.evcard.membership.credit.dto.CreditEventRecordPageDto;
import com.extracme.evcard.membership.credit.dto.CreditEventRecordParamsDto;
import com.extracme.evcard.membership.credit.model.MmpCreditEventRecord;
import com.extracme.evcard.rpc.dto.Page;

import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MmpCreditEventRecordMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_credit_event_record
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_credit_event_record
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    int insert(MmpCreditEventRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_credit_event_record
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    int insertSelective(MmpCreditEventRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_credit_event_record
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    MmpCreditEventRecord selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_credit_event_record
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    int updateByPrimaryKeySelective(MmpCreditEventRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_credit_event_record
     *
     * @mbggenerated Wed Nov 22 16:26:13 CST 2017
     */
    int updateByPrimaryKey(MmpCreditEventRecord record);

    Integer getCreditEventCountByAuthId(String authId);

    List<AuthCreditEventRecordPageDto> getCreditEventPagesByAuthId(@Param("authId") String authId,
                                                                   @Param("page") Page page);

    CreditEventRecordDetailDto getCreditEventDetailByAuthIdAndEventId(@Param("authId") String authId,
                                                                @Param("eventId") Long eventId);

    Integer getCreditEventRecordCount(CreditEventRecordParamsDto paramsDto);

    List<CreditEventRecordPageDto> getCreditEventRecordPages(@Param("paramsDto") CreditEventRecordParamsDto paramsDto,
                                                             @Param("page") Page page);

    int updateCreditEventRecordStatusByEventIdAndAuthId(@Param("eventId") Long eventId, @Param("authId") String authId,
                                                        @Param("updateOperId") Long updateOperId,
                                                        @Param("updateOperName") String updateOperName);

    MmpCreditEventRecord getCreditEventByAuthIdAndEventId(@Param("authId") String authId,
                                                          @Param("eventId") Long eventId);

    Integer getThisYearInTimePayTotalAmount(@Param("authId") String authId, @Param("eventTypeId") Long eventTypeId);

}