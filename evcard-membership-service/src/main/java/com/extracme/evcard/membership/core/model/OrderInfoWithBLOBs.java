package com.extracme.evcard.membership.core.model;

public class OrderInfoWithBLOBs extends OrderInfo {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.SETTLEMENT_SCAN_URL
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String settlementScanUrl;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.CHECK_CAR_URL
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String checkCarUrl;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column order_info.RENT_SCAN_URL
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    private String rentScanUrl;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.SETTLEMENT_SCAN_URL
     *
     * @return the value of order_info.SETTLEMENT_SCAN_URL
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getSettlementScanUrl() {
        return settlementScanUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.SETTLEMENT_SCAN_URL
     *
     * @param settlementScanUrl the value for order_info.SETTLEMENT_SCAN_URL
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setSettlementScanUrl(String settlementScanUrl) {
        this.settlementScanUrl = settlementScanUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.CHECK_CAR_URL
     *
     * @return the value of order_info.CHECK_CAR_URL
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getCheckCarUrl() {
        return checkCarUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.CHECK_CAR_URL
     *
     * @param checkCarUrl the value for order_info.CHECK_CAR_URL
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setCheckCarUrl(String checkCarUrl) {
        this.checkCarUrl = checkCarUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column order_info.RENT_SCAN_URL
     *
     * @return the value of order_info.RENT_SCAN_URL
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public String getRentScanUrl() {
        return rentScanUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column order_info.RENT_SCAN_URL
     *
     * @param rentScanUrl the value for order_info.RENT_SCAN_URL
     *
     * @mbggenerated Sat Jan 20 21:12:36 CST 2018
     */
    public void setRentScanUrl(String rentScanUrl) {
        this.rentScanUrl = rentScanUrl;
    }
}