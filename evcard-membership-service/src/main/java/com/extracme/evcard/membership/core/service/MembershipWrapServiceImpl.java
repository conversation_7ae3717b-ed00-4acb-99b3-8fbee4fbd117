package com.extracme.evcard.membership.core.service;

import com.extracme.evcard.flow.dto.UserCurrentCarbonEmissionsInfo;
import com.extracme.evcard.flow.service.IUserCarbonEmissionsService;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.dao.CityMapper;
import com.extracme.evcard.membership.core.dao.OrgInfoMapper;
import com.extracme.evcard.membership.core.dto.*;
import com.extracme.evcard.membership.core.model.City;
import com.extracme.evcard.membership.core.model.OrgInfo;
import com.extracme.evcard.membership.core.service.license.LicenseAuthenticateProxy;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.model.MembershipInfo;
import com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.extracme.evcard.rpc.exception.BusinessRuntimeException;
import com.extracme.evcard.rpc.order.service.ICancelOrderService;
import com.extracme.evcard.rpc.order.service.IReturnVehicleService;
import com.extracme.evcard.rpc.util.BeanCopyUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Service("membershipWrapService")
public class MembershipWrapServiceImpl implements IMembershipWrapService {
	private static final Logger logger = LoggerFactory.getLogger(MembershipWrapServiceImpl.class);

	@Autowired
	IMemberShipService memberShipService;
	
	@Autowired
	IMemberOperateServ memberOperateServ;
	
	@Autowired
	ICancelOrderService cancelOrderService;
	
	@Autowired
	IReturnVehicleService returnVehicleService;

	@Autowired
	LicenseAuthenticateProxy licenseAuthenticateService;

	@Resource
	IUserCarbonEmissionsService userCarbonEmissionsService;

	@Autowired
	private MembershipInfoMapper membershipInfoMapper;

	@Autowired
	private IBaseInfoService baseInfoService;

	@Autowired
	private CityMapper cityMapper;

	@Autowired
	private OrgInfoMapper orgInfoMapper;


	@Override
	public MemberWrapInfoDto getMemberWrapInfoByPkId(Long pkId) {
		MembershipBasicInfo member = memberShipService.getUserBasicInfoByPkId(pkId);
		if(member != null) {
			MemberWrapInfoDto dto = new MemberWrapInfoDto();
			BeanCopyUtils.copyProperties(member, dto);
			dto.setAge(calcMemberAge(member));
			return dto;
		}
		return null;
	}

	@Override
	public MembershipDetailInfo getMembershipByPhone(String mobilePhone, Integer membershipType) {
		if(StringUtils.isBlank(mobilePhone) || membershipType == null) {
			throw BusinessRuntimeException.PARAM_EXEPTION;
		}
		MembershipBasicInfo memberBasic = memberShipService.getMembershipByPhone(mobilePhone, membershipType);
		if(memberBasic == null) {
			return null;
		}
		/**
		 * 查询会员基础数据.
		 */
		MembershipDetailInfo result = new MembershipDetailInfo();
		BeanCopyUtils.copyProperties(memberBasic, result);
		/**
		 * 查询会员取消订单次数.
		 */
		if(StringUtils.isNotBlank(result.getCardNo())){
			Integer cancelTimes = cancelOrderService.getCancelOrderTimes(result.getAuthId(), result.getCardNo());
			result.setCancelOrderTimes(cancelTimes);
		} else {
			result.setCancelOrderTimes(0);
		}
		/**
		 * 绑定设备次数.<br>
		 */
		Integer bindImeiTimes = memberOperateServ.getBindImeiTimes(result.getAuthId(), mobilePhone);
		result.setBindImeiTimes(bindImeiTimes);
		/**
		 * 免单次数.<br>
		 */
	 	Integer freeReturnVehicleTimes = returnVehicleService.getFreeReturnVehicleTimes(result.getAuthId());
	 	result.setFreeOrderTimes(freeReturnVehicleTimes);
		return result;
	}

	@Override
	public MembershipDetailInfo getMembershipByAuthid(String authId, Integer membershipType) {
		if(StringUtils.isBlank(authId) || membershipType == null) {
			throw BusinessRuntimeException.PARAM_EXEPTION;
		}
		MembershipBasicInfo memberBasic = memberShipService.getUserBasicInfo(authId, membershipType.shortValue());
		if(memberBasic == null) {
			return null;
		}
		/**
		 * 查询会员基础数据.
		 */
		MembershipDetailInfo result = new MembershipDetailInfo();
		BeanCopyUtils.copyProperties(memberBasic, result);


		/**
		 * 查询会员取消订单次数.
		 */
		if(StringUtils.isNotBlank(result.getCardNo())) {
			try {
				Integer cancelTimes = cancelOrderService.getCancelOrderTimes(result.getAuthId(), result.getCardNo());
				result.setCancelOrderTimes(cancelTimes);
			}catch (Exception e) {
				logger.warn("获取用户取消订单次数失败，authId={}", result.getAuthId());
			}
		}

		/**
		 * 绑定设备次数.<br>
		 */
		Integer bindImeiTimes = memberOperateServ.getBindImeiTimes(result.getAuthId(), result.getMobilePhone());
		result.setBindImeiTimes(bindImeiTimes);
		
		/**
		 * 免单次数.<br>
		 */
	 	Integer freeReturnVehicleTimes = returnVehicleService.getFreeReturnVehicleTimes(result.getAuthId());
	 	result.setFreeOrderTimes(freeReturnVehicleTimes);
		return result;
	}

	@Override
	public List<LicenseAuthLogDTO> getUserLastLicenseAuthLogs(String authId, Integer logType, Integer num) {
		List<LicenseAuthLogDTO> result = new ArrayList<>(10);
		List<DriverLicenseElementsAuthenticateLogDTO> list = null;
		list = memberShipService.getLastAuthenticateLogsByUser(authId, logType, num);
		if(CollectionUtils.isNotEmpty(list)) {
			//解析驾照三要素认证结果详情
			for (DriverLicenseElementsAuthenticateLogDTO logDTO : list){
				LicenseAuthLogDTO licenseAuthLog = new LicenseAuthLogDTO();
				BeanCopyUtils.copyProperties(logDTO, licenseAuthLog);
				if(logDTO.getLogType() == 0){
					String authDetail = licenseAuthenticateService.buildAuthResultDetail(logDTO);
					licenseAuthLog.setLicenseStatusMsg(logDTO.getLicenseStatusMsg());
					licenseAuthLog.setDetails(authDetail);
				}
				result.add(licenseAuthLog);
			}
		}
		return result;
	}

	private static final String CARBON_TITLE = "节碳";
	private static final String[] TITLE_CLASS_NAMES = {"新手", "新秀", "先锋", "精英", "大使"};
	@Override
	public UserCarbonReduceTitleDto getCarbonReduceTitle(String authId) {
		/**
		 * 查询用户当前碳减排信息
		 */
		try {
			String titleName = StringUtils.EMPTY;
			UserCurrentCarbonEmissionsInfo carbonReduceResp = userCarbonEmissionsService.getUserCurrentCarbonEmissionsInfo(authId);
			if (carbonReduceResp != null) {
				Integer titleClass = carbonReduceResp.getTitleName();
				if(titleClass != null && titleClass <= TITLE_CLASS_NAMES.length) {
					titleName = CARBON_TITLE + TITLE_CLASS_NAMES[titleClass];
					if(carbonReduceResp.getTitleLevel() != null) {
						titleName += " LV" + carbonReduceResp.getTitleLevel();
					}
				}
				UserCarbonReduceTitleDto resp = new UserCarbonReduceTitleDto();
				BeanCopyUtils.copyProperties(carbonReduceResp, resp);
				resp.setTitleDesc(titleName);
				return resp;
			}
		}catch (Exception ex) {
			//logger.error("查询用户节碳称号失败， authId=" + authId, ex);
			logger.error("查询用户节碳称号失败， authId=" + authId);
		}
		return null;
	}

	public Integer calcMemberAge(MembershipBasicInfo member){
		Integer ageYear = null;
		if(member != null) {
			if (member.getReviewStatus() == 1 && member.getAuthenticationStatus() == 2
					&& member.getLicenseElementsAuthStatus() == 1 && member.getNational().contains(BussinessConstants.CHINA_NATIONAL)) {
				String idCardNum = member.getDriverCode();
				if (member.getIdType() != null && member.getIdType() == 5) {
					idCardNum = member.getIdCardNumber();
				}
				if (StringUtils.isNotBlank(idCardNum) && idCardNum.length() >= 14) {
					String birthDay = idCardNum.substring(6, 14);
					if (StringUtils.isNotBlank(birthDay)) {
						Date birthDayDate = ComUtil.getDateFromStr(birthDay, "yyyyMMdd");
						if (birthDayDate != null) {
							Date today = new Date();
							long age = (today.getTime() - birthDayDate.getTime()) / 1000;
							ageYear = (int) (age / 60 / 60 / 24 / 365);
						}
					}
				}
			}
		}
		return ageYear;
	}

	@Override
	public List<MembershipBasicInfo> getMembersByUserIds (Set<Long> userIds) {
		if(CollectionUtils.isEmpty(userIds) || userIds.size() > 200) {
			return null;
		}
		List<MembershipBasicInfo> result = new ArrayList<>();
		List<MembershipInfoWithBLOBs> list = membershipInfoMapper.selectByPkIds(userIds);
		if(CollectionUtils.isEmpty(list)) {
			return null;
		}
		for(MembershipInfoWithBLOBs record : list) {
			MembershipBasicInfo member = new MembershipBasicInfo();
			BeanCopyUtils.copyProperties(record, member);
			result.add(member);
		}
		return result;
	}

	@Override
	public List<MembershipBasicInfo> getMembersByAuthIds (Set<String> authIds) {
		if(CollectionUtils.isEmpty(authIds) || authIds.size() > 1000) {
			return null;
		}
		List<MembershipBasicInfo> result = new ArrayList<>();
		List<MembershipInfo> list = membershipInfoMapper.selectByAuthIds(authIds);
		if(CollectionUtils.isEmpty(list)) {
			return null;
		}
		for(MembershipInfo record : list) {
			MembershipBasicInfo member = new MembershipBasicInfo();
			BeanCopyUtils.copyProperties(record, member);
			result.add(member);
		}
		return result;
	}

	@Override
	public List<MembershipBasicInfo> getMembersByMobilePhones(Set<String> mobilePhones) {
		if (CollectionUtils.isEmpty(mobilePhones) || mobilePhones.size() > 1000) {
			return null;
		}
		List<MembershipBasicInfo> result = new ArrayList<>();
		List<MembershipInfo> list = membershipInfoMapper.selectByMobilePhones(mobilePhones);
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}
		for (MembershipInfo record : list) {
			MembershipBasicInfo member = new MembershipBasicInfo();
			BeanCopyUtils.copyProperties(record, member);
			result.add(member);
		}
		return result;
	}

	@Override
	public MembershipBasicInfo getMemberByMid(String mid) {
		if (StringUtils.isBlank(mid)) {
			return null;
		}
		return membershipInfoMapper.getUserBasicInfoByMid(mid);
	}

	@Override
	public MemberOrgInfoDto getMemberWithOrgInfoByMid(String mid) {
		if (StringUtils.isBlank(mid)) {
			return null;
		}
		MembershipBasicInfo member = membershipInfoMapper.getUserBasicInfoByMid(mid);
		if (member != null) {
			MemberOrgInfoDto dto = new MemberOrgInfoDto();
			BeanCopyUtils.copyProperties(member, dto);

			AgencyInfoDto agencyInfoDto = baseInfoService.queryAgencyInfoByAgencyId(dto.getAgencyId());
			if (agencyInfoDto != null) {
				dto.setAgencyName(agencyInfoDto.getAgencyName());
			}
			if (StringUtils.isNotBlank(dto.getCityOfOrigin())) {
				City cityInfo = cityMapper.getCityByName(dto.getCityOfOrigin().trim());
				if (cityInfo != null) {
					dto.setCityId(cityInfo.getCityid());
					dto.setCity(cityInfo.getCity());
					String orgId = cityInfo.getOrgId();
					OrgInfo orgInfo = orgInfoMapper.queryOrgInfoByOrgId(orgId);
					if (orgInfo != null) {
						dto.setOrgSeq(orgInfo.getId());
						dto.setOrgId(orgInfo.getOrgId());
						dto.setOrgName(orgInfo.getOrgName());
					}
				}
			}
			return dto;
		}
		return null;
	}

	@Override
	public MembershipDetailInfo getMemberWithAdditionByMid(String mid) {
		if(StringUtils.isBlank(mid)) {
			throw BusinessRuntimeException.PARAM_EXEPTION;
		}
		MembershipBasicInfo memberBasic = membershipInfoMapper.getUserBasicInfoByMid(mid);
		if(memberBasic == null) {
			return null;
		}
		/**
		 * 查询会员基础数据.
		 */
		MembershipDetailInfo result = new MembershipDetailInfo();
		BeanCopyUtils.copyProperties(memberBasic, result);


		/**
		 * 查询会员取消订单次数.
		 */
		if(StringUtils.isNotBlank(result.getCardNo())) {
			try {
				Integer cancelTimes = cancelOrderService.getCancelOrderTimes(result.getAuthId(), result.getCardNo());
				result.setCancelOrderTimes(cancelTimes);
			}catch (Exception e) {
				logger.warn("获取用户取消订单次数失败，authId={}", result.getAuthId());
			}
		}

		/**
		 * 绑定设备次数.<br>
		 */
		Integer bindImeiTimes = memberOperateServ.getBindImeiTimes(result.getAuthId(), result.getMobilePhone());
		result.setBindImeiTimes(bindImeiTimes);

		/**
		 * 免单次数.<br>
		 */
		Integer freeReturnVehicleTimes = returnVehicleService.getFreeReturnVehicleTimes(result.getAuthId());
		result.setFreeOrderTimes(freeReturnVehicleTimes);
		return result;
	}


}
