package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.input.QueryAgencyInfoBlackConditionInput;
import com.extracme.evcard.membership.core.model.AgencyInfoBlacklist;

import java.util.List;

public interface AgencyInfoBlacklistMapper {
    int deleteByPrimaryKey(String agencyId);

    int insert(AgencyInfoBlacklist record);

    int insertSelective(AgencyInfoBlacklist record);

    AgencyInfoBlacklist selectByPrimaryKey(String agencyId);

    int updateByPrimaryKeySelective(AgencyInfoBlacklist record);

    int updateByPrimaryKey(AgencyInfoBlacklist record);

    List<AgencyInfoBlacklist> queryAgencyInfoBlackByCondition(QueryAgencyInfoBlackConditionInput queryAgencyInfoBlackConditionInput);
}