<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.extracme.evcard.membership.core.dao.ChannelBlacklistLogMapper">
    <resultMap id="BaseResultMap"
               type="com.extracme.evcard.membership.core.model.ChannelBlacklistLog">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="channel_blacklist_id" jdbcType="INTEGER" property="channelBlacklistId" />
        <result column="content" jdbcType="VARCHAR" property="content" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    </resultMap>

    <sql id="Base_Column_List">
        id, channel_blacklist_id, content, create_time, create_by
    </sql>
    <select id="listChannelBlacklistLog" parameterType="com.extracme.evcard.membership.core.model.ChannelBlacklistLog"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM ${siacSchema}.channel_blacklist_log
        WHERE channel_blacklist_id = #{channelBlacklistId,jdbcType=BIGINT}
        ORDER BY create_time DESC
        <if test="pageNum != null and pageSize != null">
            LIMIT #{offset},#{pageSize}
        </if>
    </select>

    <select id="countChannelBlacklistLog" parameterType="com.extracme.evcard.membership.core.dto.blacklist.ListChannelBlacklistLogDto"
            resultType="java.lang.Integer">
        select
        count(1)
        from ${siacSchema}.channel_blacklist_log
        where channel_blacklist_id = #{channelBlacklistId,jdbcType=BIGINT}
    </select>

    <select id="selectByChannelBlacklistId" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from ${siacSchema}.channel_blacklist_log
        where channel_blacklist_id = #{cardRecoverSeq,jdbcType=BIGINT}
    </select>

    <insert id="insert" parameterType="com.extracme.evcard.membership.core.model.ChannelBlacklistLog">
        insert into ${siacSchema}.channel_blacklist_log
        (channel_blacklist_id,
        content, create_time,
        create_by,
        update_time,
        update_by
        )
        VALUES
        (#{channelBlacklistId,jdbcType=BIGINT},
        #{content,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{createBy,jdbcType=VARCHAR},
        #{updateTime,jdbcType=TIMESTAMP},
        #{updateBy,jdbcType=VARCHAR});
    </insert>
</mapper>