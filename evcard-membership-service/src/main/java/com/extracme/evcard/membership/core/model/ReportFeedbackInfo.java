package com.extracme.evcard.membership.core.model;

import java.util.Date;

public class ReportFeedbackInfo {
    private Long id;

    private String authId;

    private String name;

    private String mobilePhone;

    private Integer reportType;

    private String shopAddressName;

    private String shopAddressDesc;

    private String feedbackDesc;

    private String imageUrls;

    private String vehicleNo;

    private Integer shopSeq;

    private String shopName;

    private String parkNum;

    private Long latitude;

    private Long longitude;

    private String orderSeq;

    private Integer reportStatus;

    private String taskId;

    private Integer infoType;

    private Long reportFeedbackInfoId;

    private String replyDesc;

    private Date replyTime;

    private Integer isRead;

    private Integer status;

    private String miscDesc;

    private Date createTime;

    private Long createOperId;

    private String createOperName;

    private Date updateTime;

    private Integer updateOperId;

    private String updateOperName;

    private Integer isOrderVehicle;

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public Integer getReportType() {
        return reportType;
    }

    public void setReportType(Integer reportType) {
        this.reportType = reportType;
    }

    public String getShopAddressName() {
        return shopAddressName;
    }

    public void setShopAddressName(String shopAddressName) {
        this.shopAddressName = shopAddressName;
    }

    public String getShopAddressDesc() {
        return shopAddressDesc;
    }

    public void setShopAddressDesc(String shopAddressDesc) {
        this.shopAddressDesc = shopAddressDesc;
    }

    public String getFeedbackDesc() {
        return feedbackDesc;
    }

    public void setFeedbackDesc(String feedbackDesc) {
        this.feedbackDesc = feedbackDesc;
    }

    public String getImageUrls() {
        return imageUrls;
    }

    public void setImageUrls(String imageUrls) {
        this.imageUrls = imageUrls;
    }

    public String getVehicleNo() {
        return vehicleNo;
    }

    public void setVehicleNo(String vehicleNo) {
        this.vehicleNo = vehicleNo;
    }

    public Integer getShopSeq() {
        return shopSeq;
    }

    public void setShopSeq(Integer shopSeq) {
        this.shopSeq = shopSeq;
    }

    public String getParkNum() {
        return parkNum;
    }

    public void setParkNum(String parkNum) {
        this.parkNum = parkNum;
    }

    public Long getLatitude() {
        return latitude;
    }

    public void setLatitude(Long latitude) {
        this.latitude = latitude;
    }

    public Long getLongitude() {
        return longitude;
    }

    public void setLongitude(Long longitude) {
        this.longitude = longitude;
    }

    public String getOrderSeq() {
        return orderSeq;
    }

    public void setOrderSeq(String orderSeq) {
        this.orderSeq = orderSeq;
    }

    public Integer getReportStatus() {
        return reportStatus;
    }

    public void setReportStatus(Integer reportStatus) {
        this.reportStatus = reportStatus;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public Integer getInfoType() {
        return infoType;
    }

    public void setInfoType(Integer infoType) {
        this.infoType = infoType;
    }

    public Long getReportFeedbackInfoId() {
        return reportFeedbackInfoId;
    }

    public void setReportFeedbackInfoId(Long reportFeedbackInfoId) {
        this.reportFeedbackInfoId = reportFeedbackInfoId;
    }

    public String getReplyDesc() {
        return replyDesc;
    }

    public void setReplyDesc(String replyDesc) {
        this.replyDesc = replyDesc;
    }

    public Date getReplyTime() {
        return replyTime;
    }

    public void setReplyTime(Date replyTime) {
        this.replyTime = replyTime;
    }

    public Integer getIsRead() {
        return isRead;
    }

    public void setIsRead(Integer isRead) {
        this.isRead = isRead;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMiscDesc() {
        return miscDesc;
    }

    public void setMiscDesc(String miscDesc) {
        this.miscDesc = miscDesc;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateOperId() {
        return createOperId;
    }

    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    public String getCreateOperName() {
        return createOperName;
    }

    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getUpdateOperId() {
        return updateOperId;
    }

    public void setUpdateOperId(Integer updateOperId) {
        this.updateOperId = updateOperId;
    }

    public String getUpdateOperName() {
        return updateOperName;
    }

    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }

    public Integer getIsOrderVehicle() {
        return isOrderVehicle;
    }

    public void setIsOrderVehicle(Integer isOrderVehicle) {
        this.isOrderVehicle = isOrderVehicle;
    }
}