package com.extracme.evcard.membership.core.model;

import java.util.Date;

public class MemberIdentityDocument {
    private Long id;

    private String mid;

    private String identityNo;

    /**
     * 证件类型 1:居民身份证 2:外籍护照 3:港澳通行证 4:台湾通行证 5:军人身份证
     */
    private Integer identityType;

    private String name;

    private Integer expireType;

    private String expirationDate;

    /**
     * 本国籍：身份证正页地址  外籍：护照 or 港澳台证
     */
    private String identityCardImgUrl;

    /**
     * 身份证副页地址 只有本国籍有
     */
    private String reverseIdentityCardImgUrl;

    /**
     * 手持证件照片地址
     */
    private String holdIdcardPicUrl;

    /**
     * 人脸照片地址
     */
    private String faceRecognitionImgUrl;

    private Integer certInputType;

    /**
     * 身份认证状态  1未认证(未上传) 2未刷脸 3待认证(待人工认证) 4已认证 5认证不通过
     */
    private Integer authenticationStatus;

    private Date submitTime;

    private String submitAppkey;

    private String reviewUser;

    private Date reviewTime;

    private String reviewItems;

    private String reviewIds;

    private String reviewRemark;

    private String reviewItemNames;

    private Integer reviewMode;

    private Integer status;

    private Date createTime;

    private Long createOperId;

    private String createOperName;

    private Date updateTime;

    private Long updateOperId;

    private String updateOperName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public String getIdentityNo() {
        return identityNo;
    }

    public void setIdentityNo(String identityNo) {
        this.identityNo = identityNo;
    }

    public Integer getIdentityType() {
        return identityType;
    }

    public void setIdentityType(Integer identityType) {
        this.identityType = identityType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getExpireType() {
        return expireType;
    }

    public void setExpireType(Integer expireType) {
        this.expireType = expireType;
    }

    public String getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(String expirationDate) {
        this.expirationDate = expirationDate;
    }

    public String getIdentityCardImgUrl() {
        return identityCardImgUrl;
    }

    public void setIdentityCardImgUrl(String identityCardImgUrl) {
        this.identityCardImgUrl = identityCardImgUrl;
    }

    public String getReverseIdentityCardImgUrl() {
        return reverseIdentityCardImgUrl;
    }

    public void setReverseIdentityCardImgUrl(String reverseIdentityCardImgUrl) {
        this.reverseIdentityCardImgUrl = reverseIdentityCardImgUrl;
    }

    public String getHoldIdcardPicUrl() {
        return holdIdcardPicUrl;
    }

    public void setHoldIdcardPicUrl(String holdIdcardPicUrl) {
        this.holdIdcardPicUrl = holdIdcardPicUrl;
    }

    public String getFaceRecognitionImgUrl() {
        return faceRecognitionImgUrl;
    }

    public void setFaceRecognitionImgUrl(String faceRecognitionImgUrl) {
        this.faceRecognitionImgUrl = faceRecognitionImgUrl;
    }

    public Integer getCertInputType() {
        return certInputType;
    }

    public void setCertInputType(Integer certInputType) {
        this.certInputType = certInputType;
    }

    public Integer getAuthenticationStatus() {
        return authenticationStatus;
    }

    public void setAuthenticationStatus(Integer authenticationStatus) {
        this.authenticationStatus = authenticationStatus;
    }

    public Date getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(Date submitTime) {
        this.submitTime = submitTime;
    }

    public String getSubmitAppkey() {
        return submitAppkey;
    }

    public void setSubmitAppkey(String submitAppkey) {
        this.submitAppkey = submitAppkey;
    }

    public String getReviewUser() {
        return reviewUser;
    }

    public void setReviewUser(String reviewUser) {
        this.reviewUser = reviewUser;
    }

    public Date getReviewTime() {
        return reviewTime;
    }

    public void setReviewTime(Date reviewTime) {
        this.reviewTime = reviewTime;
    }

    public String getReviewItems() {
        return reviewItems;
    }

    public void setReviewItems(String reviewItems) {
        this.reviewItems = reviewItems;
    }

    public String getReviewIds() {
        return reviewIds;
    }

    public void setReviewIds(String reviewIds) {
        this.reviewIds = reviewIds;
    }

    public String getReviewRemark() {
        return reviewRemark;
    }

    public void setReviewRemark(String reviewRemark) {
        this.reviewRemark = reviewRemark;
    }

    public String getReviewItemNames() {
        return reviewItemNames;
    }

    public void setReviewItemNames(String reviewItemNames) {
        this.reviewItemNames = reviewItemNames;
    }

    public Integer getReviewMode() {
        return reviewMode;
    }

    public void setReviewMode(Integer reviewMode) {
        this.reviewMode = reviewMode;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateOperId() {
        return createOperId;
    }

    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    public String getCreateOperName() {
        return createOperName;
    }

    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdateOperId() {
        return updateOperId;
    }

    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    public String getUpdateOperName() {
        return updateOperName;
    }

    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }
}