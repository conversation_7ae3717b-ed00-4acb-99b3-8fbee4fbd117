package com.extracme.evcard.membership.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.extracme.evcard.elasticjob.ElasticSimpleJob;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.contract.service.IMemberShipContractServ;
import com.extracme.evcard.membership.core.dao.MembershipClauseLogMapper;
import com.extracme.evcard.membership.core.dao.MmpProvisionInfoMapper;
import com.extracme.evcard.membership.core.dao.UserContractMapper;
import com.extracme.evcard.membership.core.dto.ContractVersionDto;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.dto.SignContractDto;
import com.extracme.evcard.membership.core.dto.md.GetMdContractResponse;
import com.extracme.evcard.membership.core.dto.md.MdRentalContract;
import com.extracme.evcard.membership.core.model.MembershipClauseLog;
import com.extracme.evcard.membership.core.model.MmpProvisionInfo;
import com.extracme.evcard.membership.core.model.UserContract;
import com.extracme.evcard.membership.core.model.UserContractExample;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.membership.core.service.md.MdOrderService;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.rpc.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;


/**
 * 增加补偿任务
 * <p>
 * 历史有很多未签署用户协议的单子，用job来修复
 * <p>
 * 用E签宝新的sdk实现
 */

@Slf4j
@Component
@ElasticSimpleJob(jobName = "evcard-membership-newContractArchiveJob", cron = "0 0 0 * * ? 2099", description = "新合约归档文件补偿处理", overwrite = true)
public class NewUserContractArchiveRecoverJob implements SimpleJob {

    @Autowired
    private MembershipInfoMapper membershipInfoMapper;

    @Autowired
    private UserContractMapper userContractMapper;

    @Resource
    private IMemberShipContractServ memberShipContractServ;

    @Resource
    MdOrderService mdOrderService;

    @Autowired
    private MembershipClauseLogMapper membershipClauseLogMapper;

    @Autowired
    private MmpProvisionInfoMapper mmpProvisionInfoMapper;
    @Resource
    private IMemberShipService memberShipService;


    @Override
    public void execute(ShardingContext arg0) {
        String params = arg0.getJobParameter();
        log.info("NewUserContractArchiveRecoverJob 执行 params={}",params);
        if (StringUtils.isNotBlank(params)) {
            /**
             * 处理个别合约记录
             */
            batchArchiveSeveralFiles(params);
        }
    }


    public void batchArchiveSeveralFiles(String contractIs) {
        String[] contractIdArray = StringUtils.split(contractIs, ",");
        if (contractIdArray == null || contractIdArray.length == 0) {
            return;
        }

        Set<String> set = new HashSet<>();
        for (String contractId : contractIdArray) {
            if (StringUtils.isNotBlank(contractId)) {
                set.add(contractId);
            }
        }

        if (CollectionUtils.isEmpty(set)) {
            return;
        }

        for (String contractId : set) {
            try {
                log.info("NewUserContractArchiveRecoverJob-start 开始处理合同 contractId={}",contractId);
                GetMdContractResponse getMdContractResponse = mdOrderService.searchMdContract(contractId);
                if (getMdContractResponse.getCode() == 0) {
                    MdRentalContract mdRentalContract = getMdContractResponse.getData();
                    if (mdRentalContract == null) {
                        log.error("未查询到订单，contractId={}", contractId);
                        continue;
                    }

                    String contractCreateTime = mdRentalContract.getContractCreateTime();
                    String mid = mdRentalContract.getMid();
                    MembershipBasicInfo membershipBasicInfo = membershipInfoMapper.getUserBasicInfoByMid(mid);
                    if (membershipBasicInfo == null) {
                        log.error("未查询到用户，contractId={}", contractId);
                        continue;
                    }

                    String authId = membershipBasicInfo.getAuthId();
                    // 用户存在协议不处理。
                    UserContractExample example = new UserContractExample();
                    example.createCriteria().andAuthIdEqualTo(authId);
                    List<UserContract> userContracts = userContractMapper.selectByExample(example);
                    if (CollectionUtils.isNotEmpty(userContracts)) {
                        log.error("用户存在协议不处理，contractId={},authId={}", contractId, authId);
                        continue;
                    }

                     /*List<MembershipClauseLog> membershipClauseLogs = membershipClauseLogMapper.queryListsByAuthIdAndCreateTime(authId, DateUtil.dateToString(DateUtil.getDateFromTimeStr(contractCreateTime, DateUtil.DATE_TYPE4), DateUtil.DATE_TYPE2));

                    // 增加一个时间点，防止没有记录的情况
                    if (CollectionUtils.isEmpty(membershipClauseLogs)) {
                        Date date1 = DateUtil.getDateFromTimeStr(contractCreateTime, DateUtil.DATE_TYPE4);
                        LocalDateTime localDateTime = DateUtil.addDay(date1, 5);
                        String addDateString = DateUtil.dateToString(localDateTime, DateUtil.DATE_TYPE2);
                        membershipClauseLogs = membershipClauseLogMapper.queryListsByAuthIdAndCreateTime(authId, addDateString);
                    }
                    }*/

                    Date date1 = DateUtil.getDateFromTimeStr(contractCreateTime, DateUtil.DATE_TYPE4);
                    LocalDateTime localDateTime = DateUtil.addDay(date1, 5);
                    String addDateString = DateUtil.dateToString(localDateTime, DateUtil.DATE_TYPE2);
                    List<MembershipClauseLog> membershipClauseLogs = membershipClauseLogMapper.queryListsByAuthIdAndCreateTime(authId, addDateString);


                    if (CollectionUtils.isNotEmpty(membershipClauseLogs)) {
                        Optional<MembershipClauseLog> oneOptional = membershipClauseLogs.stream().filter(m -> m.getVersion().startsWith("1_")).findFirst();
                        String oneTypeUrl = StringUtils.EMPTY;
                        String typeOneVersion = StringUtils.EMPTY;
                        if (oneOptional.isPresent()) {
                            MembershipClauseLog oneMembershipClauseLog = oneOptional.get();
                            String version = oneMembershipClauseLog.getVersion();
                            String[] versionArray = version.split("_");
                            int type = Integer.valueOf(versionArray[0]);
                            typeOneVersion = versionArray[1];
                            //查询合同所属版本的会员条款
                            MmpProvisionInfo provision = mmpProvisionInfoMapper.queryMmpProvisionByVersion(typeOneVersion, type);
                            if (provision != null) {
                                oneTypeUrl = ComUtil.getFileFullPath(provision.getProvisionAddress());
                            }
                        }


                        Optional<MembershipClauseLog> twoOptional = membershipClauseLogs.stream().filter(m -> m.getVersion().startsWith("2_")).findFirst();
                        String twoTypeUrl = StringUtils.EMPTY;
                        String typeTwoVersion = StringUtils.EMPTY;
                        if (twoOptional.isPresent()) {
                            MembershipClauseLog twoMembershipClauseLog = twoOptional.get();
                            String version = twoMembershipClauseLog.getVersion();
                            String[] versionArray = version.split("_");
                            int type = Integer.valueOf(versionArray[0]);
                            typeTwoVersion = versionArray[1];
                            //查询合同所属版本的会员条款
                            MmpProvisionInfo provision = mmpProvisionInfoMapper.queryMmpProvisionByVersion(typeTwoVersion, type);
                            if (provision != null) {
                                twoTypeUrl = ComUtil.getFileFullPath(provision.getProvisionAddress());
                            }
                        }
                        autoSignContract(authId, typeOneVersion, typeTwoVersion, oneTypeUrl, twoTypeUrl);
                        log.info("NewUserContractArchiveRecoverJob-end 处理合同结束 contractId={}",contractId);
                    }else{
                        log.error("membershipClauseLogs 为空，contractId={}",contractId);
                    }
                }
            } catch (Exception e) {
                log.error("根据合同签订协议异常，contractId={}", contractId, e);
            }
        }
    }


    /**
     *
     *
     * @param authId
     * @param typeOneVersion
     * @param typeTwoVersion
     * @param typeOneUrl
     * @param typeTwoUrl
     */
    public void autoSignContract(String authId, String typeOneVersion, String typeTwoVersion, String typeOneUrl, String typeTwoUrl) {
        SignContractDto signContractDto = new SignContractDto();
        signContractDto.setAuthId(authId);
        if (StringUtils.isNotBlank(typeOneVersion) && StringUtils.isNotBlank(typeOneUrl)) {
            signContractDto.setContractVersionDto(new ContractVersionDto("SZ" + typeOneVersion, typeOneVersion, "会员条款", typeOneUrl));
        }
        if (StringUtils.isNotBlank(typeTwoVersion) && StringUtils.isNotBlank(typeTwoUrl)) {
            signContractDto.setContractVersionDto(new ContractVersionDto("YS" + typeTwoVersion, typeTwoVersion, "隐私政策", typeTwoUrl));
        }
        memberShipService.autoSignContract(signContractDto);
    }




}
