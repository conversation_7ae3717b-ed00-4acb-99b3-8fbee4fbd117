package com.extracme.evcard.membership.face;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.extracme.evcard.membership.third.baidu.BaseResult;
import com.extracme.evcard.membership.third.baidu.HttpUtil;
import com.extracme.evcard.membership.third.baidu.facelive.FaceLiveVerifyRequest;
import com.extracme.evcard.membership.third.baidu.facelive.FaceLiveVerifyResp;
import com.extracme.evcard.membership.third.baidu.facelive.SessionCodeRequest;
import com.extracme.evcard.membership.third.baidu.facelive.SessionCodeResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Optional;

/**
 * 百度视频活体检测
 * 文档： https://cloud.baidu.com/doc/FACE/s/Ikrycq2k2
 */
@Slf4j
@Component
public class BaiduFaceLiveNessClient {

    @Autowired
    private BaiduAccessTokenClient accessTokenClient;

    @Value("j5rgtVrKpk5y9aETFQwzK9pf")
    private String ak;

    @Value("61BLGR2CUS31xoDU4irSRwKl761wOXvK")
    private String sk;

    @Value("${baidu.face.api}")
    private String baseUrl;

    private static final String SESSION_CODE_URL = "rest/2.0/face/v1/faceliveness/sessioncode";
    private static final String VERIFY_URL = "rest/2.0/face/v1/faceliveness/verify";


    /**
     * 获取验证码
     */
    public BaseResult<SessionCodeResult> getSessionCode(SessionCodeRequest request) {
        String url = baseUrl + SESSION_CODE_URL;
        String accessToken = getAuthToken();
        if(StringUtils.isBlank(accessToken)) {
            log.error("获取 baidubce accessToken失败");
            return null;
        }
        StringBuilder param = new StringBuilder();
        Optional.ofNullable(request).map(SessionCodeRequest::getType).ifPresent(type -> param.append("type=").append(type).append("&"));
        Optional.ofNullable(request).map(SessionCodeRequest::getMinCodeLength).ifPresent(min -> param.append("min_code_length=").append(min).append("&"));
        Optional.ofNullable(request).map(SessionCodeRequest::getType).ifPresent(max -> param.append("max_code_length=").append(max));
        log.info("baidu:faceliveness/sessioncode input：{}", param);
        try {
            String result = HttpUtil.post(url, accessToken, param.toString());
            BaseResult<SessionCodeResult> resp = JSON.parseObject(result, new TypeReference<BaseResult<SessionCodeResult>>() {});
            log.info("baidu:faceliveness/sessioncode result：{}", result);
            return resp;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 视频活体检测
     */
    public BaseResult<FaceLiveVerifyResp> verify(FaceLiveVerifyRequest request) {
        // 请求url
        String url = baseUrl + VERIFY_URL;
        try {
            if (StringUtils.isBlank(request.getVideoBase64())) {
                log.error("视频为空");
                return null;
            }
            String accessToken = getAuthToken();
            if(StringUtils.isBlank(accessToken)) {
                log.error("获取baidubce accessToken失败");
                return null;
            }

            StringBuilder param = new StringBuilder();
            Optional.ofNullable(request).map(FaceLiveVerifyRequest::getTypeIdentify).ifPresent(type -> param.append("type_identify=").append(type).append("&"));
            Optional.ofNullable(request).map(FaceLiveVerifyRequest::getSessionId).ifPresent(sessionId -> param.append("session_id=").append(sessionId).append("&"));
            param.append("video_base64=").append(URLEncoder.encode(request.getVideoBase64(), "utf8"));

            log.info("baidu:faceliveness/verify input： {}", param.length() > 100 ? param.substring(0, 100) : param);
            String result = HttpUtil.post(url, accessToken, param.toString());
            BaseResult<FaceLiveVerifyResp> resp = JSON.parseObject(result, new TypeReference<BaseResult<FaceLiveVerifyResp>>() {});
            log.warn("baidu:faceliveness/verify result： {}, {}", resp.getError_code(), resp.getError_msg());
            return resp;
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }

    public String getAuthToken(){
        return accessTokenClient.getAuthToken(ak, sk);
    }

}
