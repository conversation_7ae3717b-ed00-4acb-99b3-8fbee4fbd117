package com.extracme.evcard.membership.core.task;

import com.extracme.evcard.membership.core.service.ITokenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * Token清理定时任务
 * 定期清理过期的token会话记录
 * 
 * <AUTHOR>
 * @since 2025-08-20
 */
@Slf4j
@Component
public class TokenCleanupTask {
    
    @Autowired
    private ITokenService tokenService;
    
    /**
     * 清理过期的token会话
     * 每小时执行一次
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void cleanupExpiredSessions() {
        try {
            log.info("开始执行token会话清理任务");
            
            int cleanedCount = tokenService.cleanupExpiredSessions();
            
            log.info("token会话清理任务执行完成，清理了 {} 条过期记录", cleanedCount);
        } catch (Exception e) {
            log.error("token会话清理任务执行失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 清理过期的token会话（每天凌晨2点执行）
     * 这是一个更彻底的清理任务
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void deepCleanupExpiredSessions() {
        try {
            log.info("开始执行深度token会话清理任务");
            
            // 执行深度清理
            int cleanedCount = tokenService.cleanupExpiredSessions();
            
            log.info("深度token会话清理任务执行完成，清理了 {} 条过期记录", cleanedCount);
        } catch (Exception e) {
            log.error("深度token会话清理任务执行失败: {}", e.getMessage(), e);
        }
    }
}
