package com.extracme.evcard.membership.core.dto.md;

import lombok.Data;

@Data
public class GetMemberDepositInfosData {
    // 押金余额押金状态 1:未支付2:担保中3：担保结束
    private int depositBalanceStatus;
    // 订单押金押金状态 1:未支付2:担保中3：担保结束
    private int depositOrderStatus;
    // 芝麻免押押金状态 1:未支付2:担保中3：担保结束
    private int depositZhiMaStatus;

    // 押金余额，冻结总金额
    private String depositBalanceFrozenAmount;
    // 押金余额，非冻结总金额
    private String depositBalanceUnFrozenAmount;
    // 订单押金，所有担保中订单押金总金额
    private String depositOrderAmount;
    // 芝麻免押，所有担保中订单押金总金额
    private String depositZhiMaAmount;

}
