package com.extracme.evcard.membership.core.service.license;

import lombok.Data;
import org.apache.commons.lang.StringUtils;

@Data
public class LicenseAuthenticateResult {
    private Integer errCode;
    private String reason;
    /**
     * 0不一致  1一致
     */
    private String nameCheckResult;
    private String cardNoCheckResult;
    private String archviesNoCheckResult;
    /**
     * 驾照状态信息：吊销、撤销、注销、停止使用、锁定等异常状态
     */
    private String msg;
    private String response;

    /**
     * 核查一致1
     * 核查存在不一致2
     * 无法核查 3
     */
    private String result;

    /**
     * 接口请求ID
     */
    private String requestId;


    public LicenseAuthenticateResult() {

    }

    public LicenseAuthenticateResult(String result, Boolean name, Boolean cid, Boolean fileNo, String response, String msg, String reason) {
        this.set(result, name, cid, fileNo, msg);
        this.setReason(reason);
        this.setResponse(response);
    }

    public void set(String result, Boolean name, Boolean cid, Boolean fileNo, String msg) {
        this.setErrCode(0);
        this.setArchviesNoCheckResult(getFlag(fileNo));
        this.setCardNoCheckResult(getFlag(cid));
        this.setNameCheckResult(getFlag(name));
        this.setResult(result);
        this.setMsg(msg);
    }

    public void setItems(Boolean name, Boolean cid, Boolean fileNo) {
        this.setArchviesNoCheckResult(getFlag(fileNo));
        this.setCardNoCheckResult(getFlag(cid));
        this.setNameCheckResult(getFlag(name));
    }

    public static LicenseAuthenticateResult getSuccess(String response, String driverStatus) {
        return new LicenseAuthenticateResult("1", true, true, true, response, driverStatus, "success");
    }

    public static LicenseAuthenticateResult getFail(String response, String message) {
        return new LicenseAuthenticateResult("4", false, false, false, response,
                StringUtils.EMPTY, message);
    }

    public static LicenseAuthenticateResult getException(String response, String message) {
        LicenseAuthenticateResult result = getFail(response, message);
        result.setErrCode(-1);
        return result;
    }


    private static String getFlag(Boolean flag) {
        return (Boolean.TRUE.equals(flag)) ? "1" : "0";
    }
}
