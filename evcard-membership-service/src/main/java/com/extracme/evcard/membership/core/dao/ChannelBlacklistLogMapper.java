package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.dto.blacklist.ChannelBlacklistLogDto;
import com.extracme.evcard.membership.core.dto.blacklist.ListChannelBlacklistDto;
import com.extracme.evcard.membership.core.dto.blacklist.ListChannelBlacklistLogDto;
import com.extracme.evcard.membership.core.model.ChannelBlacklistLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 渠道黑名单日志表
 */
public interface ChannelBlacklistLogMapper {

    List<ChannelBlacklistLog> selectByChannelBlacklistId(@Param("channelBlacklistId") Long channelBlacklistId);

    List<ChannelBlacklistLog> listChannelBlacklistLog(ListChannelBlacklistLogDto dto);

    Integer countChannelBlacklistLog(ListChannelBlacklistLogDto dto);

    Integer insert(ChannelBlacklistLog channelBlacklistLog);
}
