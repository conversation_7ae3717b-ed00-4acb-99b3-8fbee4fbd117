package com.extracme.evcard.membership.core.service.license;

import com.extracme.evcard.membership.core.dto.DriverLicenseDetailDTO;
import lombok.Data;

/**
 * <AUTHOR>
 * @Discription
 * @date 2019/12/25
 */
@Data
public class MinVisionDriverLicenseInfo {
    /**
     * 驾照号
     */
    private String idCard;

    /**
     * 驾照证状态
     */
    private String driverStatus;

    /**
     * 档案编号
     */
    private String fileNumber;
    /**
     * 用户姓名
     */
    private String name;
    /**
     * 准驾车型
     */
    private String quasiDrivingType;
    /**
     * 累计计分
     */
    private String scoring;
    /**
     * 初次领证日期
     */
    private String firstIssueDate;
    /**
     * 有效期至
     */
    private String termoFvalidity;

    private String startDate;

    /**
     * 签发机关
     */
    private String trafficpolice;


    /**
     * 驾照信息
     * @return
     */
    public DriverLicenseDetailDTO buildDriverLicenseDetail() {
        DriverLicenseDetailDTO driverLicenseDetail = new DriverLicenseDetailDTO();
        driverLicenseDetail.setDriverCode(this.getIdCard());
        driverLicenseDetail.setFileNo(this.getFileNumber());
        driverLicenseDetail.setName(this.getName());
        driverLicenseDetail.setQuasiDrivingType(this.getQuasiDrivingType());
        driverLicenseDetail.setScoring(this.getScoring());
        driverLicenseDetail.setStartDate(this.getStartDate());
        driverLicenseDetail.setExpireDate(this.getTermoFvalidity());
        driverLicenseDetail.setFirstIssueDate(this.getFirstIssueDate());
        driverLicenseDetail.setIssuingAuthority(this.getTrafficpolice());
        driverLicenseDetail.setLicenseStatus(this.getDriverStatus());
        return driverLicenseDetail;
    }
}
