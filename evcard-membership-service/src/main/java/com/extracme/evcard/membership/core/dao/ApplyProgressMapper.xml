<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ApplyProgressMapper，，对应表apply_progress -->
<mapper namespace="com.extracme.evcard.membership.core.dao.ApplyProgressMapper">
    <!-- 返回结果集Map -->
    <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.ApplyProgress">
        <id column="APPLY_PROGRESS_SEQ" jdbcType="BIGINT" property="applyProgressSeq" />
        <result column="APPLY_PROGRESS_SEQ" jdbcType="BIGINT" property="applyProgressSeq" />
        <result column="AUTH_ID" jdbcType="VARCHAR" property="authId" />
        <result column="PROGRESS_CONTENT" jdbcType="VARCHAR" property="progressContent" />
        <result column="PROGRESS_TIME" jdbcType="VARCHAR" property="progressTime" />
        <result column="CREATED_USER" jdbcType="VARCHAR" property="createdUser" />
    </resultMap>
    
    <!--数据列-->
    <sql id="Base_Column_List" >
            APPLY_PROGRESS_SEQ,
            AUTH_ID,
            PROGRESS_CONTENT,
            PROGRESS_TIME,
            CREATED_USER
    </sql>

     <!-- 保存数据 -->
    <insert id="addApplyProgress" parameterType="com.extracme.evcard.membership.core.model.ApplyProgress">
        insert into ${siacSchema}.apply_progress (
            AUTH_ID,
            PROGRESS_CONTENT,
            PROGRESS_TIME,
            CREATED_USER
        ) values (
            #{authId,jdbcType=VARCHAR},
            #{progressContent,jdbcType=VARCHAR},
            #{progressTime,jdbcType=VARCHAR},
            #{createdUser,jdbcType=VARCHAR}
        )
    </insert>
    
</mapper>