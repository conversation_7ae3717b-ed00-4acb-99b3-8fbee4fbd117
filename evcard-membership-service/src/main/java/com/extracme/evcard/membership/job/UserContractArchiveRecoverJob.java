package com.extracme.evcard.membership.job;

import com.dangdang.ddframe.job.api.ShardingContext;
import com.dangdang.ddframe.job.api.simple.SimpleJob;
import com.extracme.evcard.elasticjob.ElasticSimpleJob;
import com.extracme.evcard.membership.common.ComUtil;
import com.extracme.evcard.membership.contract.service.IMemberShipContractServ;
import com.extracme.evcard.membership.core.dao.UserContractMapper;
import com.extracme.evcard.membership.core.dto.UserContractInfo;
import com.extracme.evcard.membership.core.model.UserContract;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.rpc.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;


/**
 * 增加补偿任务
 * 不全未成功归档的用户协议
 * @remark 未成功归档原因：归档时供应商尚未完成生成协议文件
 */

@Slf4j
@Component
@ElasticSimpleJob(jobName = "evcard-membership-contractArchiveJob",
		cron = "0 0 1 * * ?", description = "合约归档文件补偿处理", overwrite = true)
public class UserContractArchiveRecoverJob implements SimpleJob{

	@Autowired
	private MembershipInfoMapper membershipInfoMapper;

    @Autowired
    private UserContractMapper userContractMapper;

	@Resource
	private IMemberShipContractServ memberShipContractServ;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void execute(ShardingContext arg0) {
		String params = arg0.getJobParameter();
		if(StringUtils.isBlank(params)) {
            /**
             * 定时任务默认处理前一天的数据
             */
            Calendar calendar = Calendar.getInstance(ComUtil.timeZoneChina);
            calendar.add(Calendar.DATE, -1);
            Date startTime = calendar.getTime();
            batchArchiveFilesByTime(startTime);
		}
		else if(StringUtils.equals(params, "all")) {
            /**
             * 处理全部历史数据
             */
            batchArchiveFilesByTime(null);
        }
		else if(StringUtils.contains(params, ",")) {
            /**
             * 处理个别合约记录
             */
            batchArchiveSeveralFiles(params);
        }
	}

    public void batchArchiveFilesByTime(Date startTime) {
        Calendar calendar = Calendar.getInstance(ComUtil.timeZoneChina);
        calendar.add(Calendar.MINUTE, -10);
        Date endTime = calendar.getTime();
        List<UserContract> userContracts = userContractMapper.selectUnArchiveContracts(startTime, endTime);
        batchArchiveFiles(userContracts);
    }


	public void batchArchiveSeveralFiles(String params) {
        String[] recordIds = StringUtils.split(params, ",");
        if(recordIds == null || recordIds.length == 0) {
            return;
        }
        Set<Long> set = new HashSet<>();
        for(String str : recordIds) {
            Long id = ComUtil.getLongValue(str);
            if(id != null) {
                set.add(id);
            }
        }
        if(CollectionUtils.isEmpty(set)) {
            return;
        }

        List<UserContract> userContracts = userContractMapper.selectContractsByIds(set);
        batchArchiveFiles(userContracts);
    }

	public void batchArchiveFiles(List<UserContract> list) {
	    int cnt = 0;
	    for(UserContract userContract : list) {
	        if(StringUtils.isBlank(userContract.getTransactionId()) || StringUtils.isNotBlank(userContract.getContractId())) {
	            continue;
            }
	        log.warn("处理未归档合约：id={}", userContract.getId());
	        UserContractInfo userContractInfo = new UserContractInfo();
            BeanCopyUtils.copyProperties(userContract, userContractInfo);
            memberShipContractServ.archiveSignFiles(userContractInfo);
            if(++cnt % 20 == 0) {
                try {
                    Thread.sleep(1 * 1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
        log.warn("处理未归档合约：完成，total={}", cnt);
    }

}
