package com.extracme.evcard.membership.core.service.license;

import org.apache.commons.lang3.StringUtils;

public class LicenseAuthUtils {

    /**
     * 认证成功
     */
    public static final String CHECK_OK = "1";
    /**
     * 认证不一致
     */
    public static final String CHECK_FAIL = "2";
    /**
     * 查无记录或无法核查
     */
    public static final String CHECK_RETRY = "3";
    /**
     * 异常情况
     */
    public static final String CHECK_UNKNOWN = "4";

    public static String itemToDesc(String itemCheckResult, int index) {
        if(StringUtils.isNotBlank(itemCheckResult) && itemCheckResult.length() == 3) {
            char ch = itemCheckResult.charAt(index);
            if('0' == ch) {
                return "不一致";
            } else if('1' == ch) {
                return "一致";
            }
        }
        return "未校验";
    }
}
