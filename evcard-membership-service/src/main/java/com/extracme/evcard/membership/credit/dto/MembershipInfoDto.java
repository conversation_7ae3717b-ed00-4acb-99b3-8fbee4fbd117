package com.extracme.evcard.membership.credit.dto;

import java.io.Serializable;

public class MembershipInfoDto implements Serializable {
    private static final long serialVersionUID = -812000275730512035L;

    private Long pkId;

    //会员id
    private String authId;

    /**
     * 享道统一用户ID
     */
    private String uid;

    //会员名字
    private String name;

    //密码
    private String password;

    //审核状态
    private Short reviewStatus;;

    //会员类型
    private Short membershipType;

    //卡id
    private String cardNo;

    //企业id
    private String agencyId;

    //appkey
    private String appKey;

    //服务条款版本
    private String serviceVer;

    //卡id活动状态
    private Integer activateStatus;
    
    /**
     * 注册时所属城市.
     */
    private String cityOfOrigin;

    /** 状态：1有效 0无效 */
    private Integer status;

    /** 用户所属机构 */
    private String orgId;

    private Integer authenticationStatus;


    public Long getPkId() {
        return pkId;
    }

    public void setPkId(Long pkId) {
        this.pkId = pkId;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Short getMembershipType() {
        return membershipType;
    }

    public void setMembershipType(Short membershipType) {
        this.membershipType = membershipType;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getAgencyId() {
        return agencyId;
    }

    public void setAgencyId(String agencyId) {
        this.agencyId = agencyId;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getServiceVer() {
        return serviceVer;
    }

    public void setServiceVer(String serviceVer) {
        this.serviceVer = serviceVer;
    }

    public Integer getActivateStatus() {
        return activateStatus;
    }

    public void setActivateStatus(Integer activateStatus) {
        this.activateStatus = activateStatus;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Short getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(Short reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

	public String getCityOfOrigin() {
		return cityOfOrigin;
	}

	public void setCityOfOrigin(String cityOfOrigin) {
		this.cityOfOrigin = cityOfOrigin;
	}

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public Integer getAuthenticationStatus() {
        return authenticationStatus;
    }

    public void setAuthenticationStatus(Integer authenticationStatus) {
        this.authenticationStatus = authenticationStatus;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }
}
