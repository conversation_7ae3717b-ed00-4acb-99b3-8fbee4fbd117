package com.extracme.evcard.membership.common;

import java.math.BigDecimal;
import java.net.URL;
import java.security.MessageDigest;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.TimeZone;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.config.CommConfigUtil;
import com.extracme.evcard.membership.config.OssConfigUtil;
import com.extracme.evcard.membership.credit.model.MembershipInfo;
import com.extracme.evcard.mq.bean.event.MemberInviteSuccess;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.dao.CardInfoMapper;
import com.extracme.evcard.membership.core.dao.UserOperatorLogMapper;
import com.extracme.evcard.membership.core.model.CardInfo;
import com.extracme.evcard.membership.core.model.UserOperatorLog;
import org.apache.commons.lang3.StringEscapeUtils;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: Elin
 * \* Date: 2018/1/9
 * \* Time: 17:22
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 * \
 */
public class ComUtil {


    public static TimeZone timeZoneChina = TimeZone.getTimeZone("Asia/Shanghai");// 获取时区

    public static final String DATE_TYPE1 = "yyyy-MM-dd HH:mm:ss";

    public static final String DATE_TYPE2 = "yyyyMMdd";

    public static final String DATE_TYPE3 = "yyyyMMddHHmmssSSS";

    public static final String DATE_TYPE4 = "yyyyMMddHHmmss";

    public static final String DATE_TYPE5 = "yyyy-MM-dd";

    public static final String DATE_TYPE6 = "HHmmssSSS";

    public static final String DATE_TYPE7 = "yyyyMM";

    public static final String DATE_TYPE8 = "yyyy-MM-dd HH:mm:ss";

    public static final String DATE_TYPE9 = "yyMMddHH";

    public static final String DATE_TYPE22 = "yyyy年MM月dd日";
    public static final String DATE_TYPE23 = "yyyy.MM.dd";
    public static final String DATE_TYPE24 = "yyyyMMdd000000";

    public static final int BIND_IMEI_NUM = 4;

    public static  Pattern pA = Pattern.compile("^[A-Z]{6,20}$");
    public static  Pattern pa = Pattern.compile("^[a-z]{6,20}$");
    public static  Pattern pNum = Pattern.compile("^[0-9]{6,20}$");

    public static  Pattern pattern = Pattern.compile("([0-9]){11}");


    /**  加密的密钥 */
    public static final String ENCRYPT_KEY = "evcardbs";
    /** imei key  */
    public static final String IMEI_KEY = "imei_";
    public static final String IMEI_COUNT_KEY = "imeiChangeNum_";

    public static final List<String> APPKEY_REGISTER_CALLBACK = Arrays.asList("csmslm");


    /** 随身行数据 */
    public final static String SUISHENXING_APP_KEY = "suishenxing";
    public final static String SUISHENXING_SECOND_APP_KEY = "second_suishenxing";

    /**
     * 上汽职工之家
     */
    public final static String SAIC_EH_SECOND_APP_KEY = "second_saic_eh";
    /**  需要绑定企业的注册 特殊的 appkey **/
    public final static List<String> REGISTER_AGENCY_APP_KEY = Arrays.asList(SUISHENXING_APP_KEY);

    /**
     * 获取当前时间
     * @param type
     *            指定格式
     * @return
     */
    public static String getSystemDate(String type) {

        // 指定格式
        DateFormat date_format = new SimpleDateFormat(type);
        date_format.setTimeZone(timeZoneChina);

        // 范围指定格式的字符串
        return date_format.format(new Date());
    }


    public static Date addMillisecond(Date dt, long millisecond) {
        Date newDate = new Date(dt.getTime() + millisecond);
        return newDate;
    }

    public static Date addDay(Date dt, int day) {
       return addMillisecond(dt,86400000L * day);
    }

    /***************************************************************************
     * 时间运算 (月份加算)
     * @param dateStr 运算前时间
     * @param mon add的值
     * @return String
     */
    public static String AddMonth(String dateStr, String format, int mon) {
        DateFormat dateFmt = new SimpleDateFormat(format);
        dateFmt.setTimeZone(timeZoneChina);
        Date tmpDate;

        try {
            tmpDate = dateFmt.parse(dateStr);
            Calendar cal = Calendar.getInstance();
            cal.setTime(tmpDate);
            cal.add(Calendar.MONTH, mon);
            return dateFmt.format(cal.getTime());
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            return "";
        }
    }

    /***************************************************************************
     * 时间运算 (年份加算)
     * @param dateStr 运算前时间
     * @param year add的值
     * @return String
     */
    public static String addYear(String dateStr, String format, int year) {
        DateFormat dateFmt = new SimpleDateFormat(format);
        dateFmt.setTimeZone(timeZoneChina);
        Date tmpDate;

        try {
            tmpDate = dateFmt.parse(dateStr);
            Calendar cal = Calendar.getInstance();
            cal.setTime(tmpDate);
            cal.add(Calendar.YEAR, year);
            return dateFmt.format(cal.getTime());
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            return "";
        }
    }

    public static double daysBetween(Date startDate, Date endDate) {
        double days = (endDate.getTime()-startDate.getTime()) * 1.0/(1000*3600*24);
        return days;
    }

    /**
     * 比较是否到期
     *
     * @param
     * @return
     * @throws ParseException
     */
    public static boolean isActiveEnd(String validDate){
        SimpleDateFormat sdf =   new SimpleDateFormat( "yyyy-MM-dd" );
        String Nowdate = sdf.format(new Date());//获取当前时间

        try {
            if(sdf.parse(Nowdate).getTime() > sdf.parse(validDate).getTime()){
                return true;//当前时间大于到期时间
            }else{
                return false;//当前时间小于到期时间
            }
        } catch (ParseException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 对象转化为字符串
     *
     * @param obj
     * @return
     */
    public static String ObjectToString(Object obj) {
        if (obj == null) {
            return "";
        } else {
            return obj.toString();
        }
    }

    /**
     * 取得当前类和当前方法的异常信息
     * @return
     */
    public static String getExceptionMsg(Exception ex) {
        StackTraceElement[] stackTrace = new Throwable().getStackTrace();
        // 类名
        String className = stackTrace[1].getClassName();
        // 方法名
        String methodName = stackTrace[1].getMethodName();

        return "类名:" + className + " 方法名:" + methodName + " 异常信息:" + ex.toString();
    }

    public static String getUserIDCardNo(MembershipInfo member) {
        String national = member.getNational();
        if (StringUtils.isNotBlank(national)) {
            //本籍用户返回驾照号
            if(StringUtils.startsWith(national, "中国")) {
                if (ComUtil.checkIDCard(member.getDriverCode())) {
                    return member.getDriverCode();
                }
            } else {
                //港澳台及外籍返回文档号(护照/港澳台)
                if(StringUtils.isNotBlank(member.getPassportNo())) {
                    return member.getPassportNo();
                }
            }
        }
        //无国籍者或其他
        if (ComUtil.checkIDCard(member.getIdCardNumber())) {
            return member.getIdCardNumber();
        }
        return StringUtils.EMPTY;
    }

    /**
     * 验证密码正确性
     * @param password
     * @return
     * <AUTHOR>
     */
    public static String checkPassword(String password) {
        String msg = null;
        if(password == null || password.length() < 6 || password.length() > 20) {
            msg = "密码长度不可以低于6位且不可以超过20位！";
            return msg;
        }

        String regEx="[`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]{6,20}$";		//①构造一个模式.
        Pattern p=Pattern.compile(regEx);

//		②建造一个匹配器
        Matcher m = p.matcher(password);
        Matcher m1 = pA.matcher(password);
        Matcher m2 = pa.matcher(password);
        Matcher m3 = pNum.matcher(password);
//		大小写字母，数字，特殊字符 全集匹配（只要字符串匹配其中任何一个或多个都可以）
        String reg="([A-Z]|[a-z]|[0-9]|[`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]){6,20}$";
        Pattern pAll=Pattern.compile(reg);
        Matcher mAll = pAll.matcher(password);
//		③进行判断，得到结果
//		因为字符串str如果匹配一个就不可能匹配其他的，具有互异性。还要排除都不匹配的情况，不满足这四项的字符
        if(m.matches()||m1.matches()||m2.matches()||m3.matches()){
            msg = "密码需同时包含英文、数字、特殊符号中的2种及以上组合";
        }else if(mAll.matches()){
        }else{
            msg = "密码需同时包含英文、数字、特殊符号中的2种及以上组合";
        }
        return msg;
    }

    public static boolean checkIDCard(String Id)
    {
        boolean bRet;
        if(Id == null) {
            return false;
        }

        // 长度是18位的身份证
        if (Id.length() == 18)
        {
            // 18位身份证验证
            bRet =  checkIDCard18(Id);

            return bRet;
        }
        // 长度是15位的身份证
        else if (Id.length() == 15)
        {
            // 15位身份证验证
            bRet =  checkIDCard15(Id);

            return bRet;
        }
        else
        {
            return false;
        }
    }

    public static boolean checkIDCard18(String Id)
    {
        long n = 0;
        int y = -1;
        int sum = 0;

        // 数字验证

        if(!isNumeric(Id.substring(0, 17))) {
            return false;
        }

        // 省份验证
        String address = "11x22x35x44x53x12x23x36x45x54x13x31x37x46x61x14x32x41x50x62x15x33x42x51x63x21x34x43x52x64x65x71x81x82x91";
        if (address.indexOf(Id.substring(0, 2)) == -1)
        {
            return false;
        }

        // 生日验证
        String birth = Id.substring(6, 14);
        SimpleDateFormat  dateFormat = new SimpleDateFormat("yyyyMMdd");
        dateFormat.setLenient(false);
        try {
            dateFormat.parse(birth);
        } catch(Exception ex) {
            return false;
        }

        // 校验码验证
        // 校验码
        String[] arrVarifyCode = ("1,0,x,9,8,7,6,5,4,3,2").split(",");
        // Wi表示第i位置上的加权因子
        String[] Wi = ("7,9,10,5,8,4,2,1,6,3,7,9,10,5,8,4,2").split(",");
        // Ai表示第i位置上的身份证号码数字值
        char[] Ai = Id.toCharArray();
        // 对前17位数字本体码加权求和
        for (int i = 0; i < 17; i++)
        {
            sum += Integer.parseInt(Wi[i]) * (Integer.parseInt("" + Ai[i]));
        }
        // 以11对计算结果取模
        int yy = sum % 11;
        if (!arrVarifyCode[yy].equals(Id.substring(17, 18).toLowerCase()))
        {
            return false;
        }

        // 符合GB11643-1999标准
        return true;
    }

    public static boolean checkIDCard15(String Id)
    {
        long n = 0;

        // 数字验证
        if(!isNumeric(Id.substring(0, 14))) {
            return false;
        }

        // 省份验证
        String address = "11x22x35x44x53x12x23x36x45x54x13x31x37x46x61x14x32x41x50x62x15x33x42x51x63x21x34x43x52x64x65x71x81x82x91";
        if (address.indexOf(Id.substring(0, 2)) == -1)
        {
            return false;
        }

        // 生日验证
        String birth = Id.substring(6, 12);
        SimpleDateFormat  dateFormat = new SimpleDateFormat("yyyyMMdd");
        dateFormat.setLenient(false);
        try {
            dateFormat.parse(birth);
        } catch(Exception ex) {
            return false;
        }

        // 符合15位身份证标准
        return true;
    }

    private static Pattern pattern_1 =  Pattern.compile("[0-9]*");

    public static boolean isNumeric(String str) {
        Matcher isNum = pattern_1.matcher(str);
        if (isNum.matches()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 时间转化
     * @param dateStr
     * @param fromType
     * @param toType
     * @return
     */
    public static String getFormatDate(String dateStr, String fromType, String toType) {

        try {
            DateFormat dateFromFmt = new SimpleDateFormat(fromType);
            DateFormat dateToFmt = new SimpleDateFormat(toType);
            // 非空检查
            if (dateStr == null || "".equals(dateStr)) {
                return "";
            } else {
                Date tmpDate = dateFromFmt.parse(dateStr);

                if (dateFromFmt.format(tmpDate).equals(dateStr)) {
                    return dateToFmt.format(tmpDate);
                } else {
                    return "";
                }
            }
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 格式化时间为DATE类型
     *
     * @param dateStr
     * @param fromType
     * @return
     */
    public static Date getDateFromStr(String dateStr, String fromType) {
        if(StringUtils.isBlank(dateStr)) {
            return null;
        }
        try {
            DateFormat dateFromFmt = new SimpleDateFormat(fromType);
            return dateFromFmt.parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String getFormatDate(Date date, String toType) {

        try {
            DateFormat dateToFmt = new SimpleDateFormat(toType);
            dateToFmt.setTimeZone(timeZoneChina);
            // 非空检查
            if (date == null) {
                return "";
            } else {
                return dateToFmt.format(date);
            }
        } catch (Exception e) {
            return "";
        }
    }

    public static String getFormatDate(Calendar calendar, String toType) {
        try {
            DateFormat dateToFmt = new SimpleDateFormat(toType);
            dateToFmt.setTimeZone(timeZoneChina);
            // 非空检查
            if (calendar == null) {
                return StringUtils.EMPTY;
            } else {
                return dateToFmt.format(calendar.getTime());
            }
        } catch (Exception e) {
            return StringUtils.EMPTY;
        }
    }

    /**
     * 获取会员卡的内部编号
     *
     * @return
     * @userType 会员类型(0:内部会员 1：个人分时会员卡 5:旅游分时用户卡 6:企业分时会员卡）
     */
    public static String generateInternalNo(CardInfoMapper cardInfoMapper, int userType) {
        Map condition = new HashMap();
        condition.put("userType", userType);
        List<Integer> list = cardInfoMapper.queryInternalNo(condition);
        int seq = list.get(0);
        int index = seq + 1;
        String str = index + "";
        while (true) {
            if (str.indexOf("4") > -1) {
                str = (++index) + "";
            } else {
                break;
            }
        }
        condition.put("newSeq", str);
        cardInfoMapper.updateCardSeq(condition);
        DecimalFormat df = new DecimalFormat("000000000");
        String strSeq = df.format(seq);
        Random random = new Random();
        int randomNum = random.nextInt(100);
        df = new DecimalFormat("00");
        String strrandomNum = df.format(randomNum);
        while (true) {
            if (strrandomNum.indexOf("4") > -1) {
                randomNum = random.nextInt(100);
                strrandomNum = df.format(randomNum);
            } else {
                break;
            }
        }
        return strSeq.substring(0, 1) + strrandomNum + strSeq.substring(3);
    }

    /**
     * 生成虚拟卡号
     *
     * @return
     */
    public static String getVirtualCardNo(CardInfoMapper cardInfoMapper) {
        Random random = new Random();
        String virtual_cardNo = "";
        boolean flag = true;
        while (flag) {
            virtual_cardNo = String.valueOf(random.nextInt(99999999));
            String temp = virtual_cardNo;
            if (temp.length() < 8) {
                for (int i = 0; i < 8 - temp.length(); i++) {
                    virtual_cardNo = "0" + virtual_cardNo;
                }
            }
            List<CardInfo> cardlist = cardInfoMapper.getCardInfo(virtual_cardNo);
            if (CollectionUtils.isNotEmpty(cardlist)) {
                CardInfo newcardInfo = cardlist.get(0);
                // 状态(0：有效 1：失效 2:已注销)
                if (!"2".equals(newcardInfo.getStatus())) {
                    //会员卡已经被使用了，请使用其他会员卡！  重新生成虚拟卡号
                    //getVirtualCardNo();
                    flag = true;
                    continue;
                }
            }
            flag = false;
        }
        return virtual_cardNo;
    }

    /**
     * 新增操作日志
     *
     * @param operatorContent       日志内容
     * @param foreignKey
     * @param foreignKey2
     * @param createUser            操作人员
     * @param userOperatorLogMapper 操作日志mapper
     */
    public static void insertOperatorLog(String operatorContent, String foreignKey, String foreignKey2, String createUser, UserOperatorLogMapper userOperatorLogMapper) {
        UserOperatorLog userOperatorLog = new UserOperatorLog();
        userOperatorLog.setOperatorContent(operatorContent);
        userOperatorLog.setForeignKey(foreignKey);
        userOperatorLog.setForeignKey2(foreignKey2);
        userOperatorLog.setCreatedUser(createUser);
        userOperatorLog.setCreatedTime(getSystemDate("yyyyMMddHHmmssSSS"));
        userOperatorLogMapper.saveSelective(userOperatorLog);
    }

    /**
     * MD5加密返回16进制小写
     * @param s
     * @return
     */
    public final static String MD5(String s) {

        char[] hexDigits = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

        try {
            byte[] btInput = s.getBytes();
            // 获得MD5摘要算法的 MessageDigest 对象
            MessageDigest mdInst = MessageDigest.getInstance("MD5");
            // 使用指定的字节更新摘要
            mdInst.update(btInput);
            // 获得密文
            byte[] md = mdInst.digest();
            // 把密文转换成十六进制的字符串形式
            int j = md.length;
            char[] str = new char[j * 2];
            int k = 0;
            for (int i = 0; i < j; i++) {
                byte byte0 = md[i];
                str[k++] = hexDigits[byte0 >>> 4 & 0xf];
                str[k++] = hexDigits[byte0 & 0xf];
            }
            return new String(str);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 用于截取阿里云版本之前图片路径包含upload
     *
     * @param imageUrl
     * @return
     */
    public static String subString(String imageUrl) {
        if (StringUtils.isNotBlank(imageUrl)) {
            if (imageUrl.startsWith("/sensitiveBucket") || imageUrl.startsWith("sensitiveBucket")){
                if(StringUtils.startsWith(imageUrl, "/")) {
                    imageUrl = imageUrl.substring(1);
                }
                imageUrl = getStsUrlTokenGet("evcard-sensitive", CommConfigUtil.getENV() + "/"+ imageUrl).toString();
            }else{
                if (imageUrl.startsWith("/")) {
                    if (imageUrl.startsWith("/upload")) {
                        imageUrl = imageUrl.substring(7);
                    }
                } else if (imageUrl.startsWith("upload")) {
                    imageUrl = imageUrl.substring(6);
                } else {
                    imageUrl = "/" + imageUrl;
                }
                imageUrl = OssConfigUtil.getFileBaseUrl() + imageUrl;
            }
        }
        return imageUrl;
    }

    public static String getFileFullPath(String imageUrl) {
        //http or https
        if(StringUtils.startsWithIgnoreCase(imageUrl, "http")) {
            return imageUrl;
        }
        return subString(imageUrl);
    }



    /**
     * 获取验证码
     * @param numberFlag 是否是数字
     * @param length
     * @return
     */
    public static String getVerifyCode(boolean numberFlag, int length) {
        String retStr = "";
        String strTable = numberFlag ? "1234567890" : "1234567890abcdefghijkmnpqrstuvwxyz";
        int len = strTable.length();
        boolean bDone = true;
        do {
            retStr = "";
            int count = 0;
            for (int i = 0; i < length; i++) {
                double dblR = Math.random() * len;
                int intR = (int) Math.floor(dblR);
                char c = strTable.charAt(intR);
                if (('0' <= c) && (c <= '9')) {
                    count++;
                }
                retStr += strTable.charAt(intR);
            }
            if (count >= 2) {
                bDone = false;
            }
        } while (bDone);

        return retStr;
    }

    /**
     * 截取图片url
     * @param picUrl
     * @return
     */
    public static String splitPicUrl(String picUrl){
        if(StringUtils.isNotEmpty(picUrl) && picUrl.contains(OssConfigUtil.getFileBaseUrl())){
            picUrl = picUrl.substring(OssConfigUtil.getFileBaseUrl().length() + 1);
        }
        if(StringUtils.isNotEmpty(picUrl) && picUrl.contains(OssConfigUtil.getSensitiveFileUrl())){
            picUrl = picUrl.substring(OssConfigUtil.getSensitiveFileUrl().length() + 1);
        }
        if (StringUtils.isNotBlank(picUrl) && picUrl.contains("?Expires=")) {
            picUrl = picUrl.split("\\?Expires=")[0];
        }
        return picUrl;
    }

    /**
     * 使用签名URL进行临时授权
     * 生成以GET方法访问的签名URL
     * @param bucketName
     * @param objectName
     * @return
     */
    public static URL getStsUrlTokenGet(String bucketName, String objectName) {

        String accessKeyId = OssConfigUtil.getAliAccessId();
        String accessKeySecret = OssConfigUtil.getAliAccessKey();
        String endPoint = OssConfigUtil.getOssEndpoint();
        // 创建OSSClient实例。
        OSS ossClient = new OSSClientBuilder().build(endPoint, accessKeyId, accessKeySecret);

        // 设置URL过期时间为5分钟。
        Date expiration = new Date(System.currentTimeMillis() + 300 * 1000);

        // 生成以GET方法访问的签名URL，访客可以直接通过浏览器访问相关内容。
        URL url = ossClient.generatePresignedUrl(bucketName, objectName, expiration);

        // 关闭OSSClient。
        ossClient.shutdown();
        return url;
    }

    public static boolean checkIsSgmAppKey(String appKey){
        // 通用、别克代步车、校园版不发短信
        if (StringUtils.equals(appKey, BussinessConstants.APP_KEY_SGM)
                || StringUtils.equals(appKey, BussinessConstants.APP_KEY_IBUICK)
                || StringUtils.equals(appKey, BussinessConstants.APP_KEY_SGM_SONGJIANG)) {
            return true;
        }
        return false;
    }

    /**
     * 判断用户是否中国居民
     * 非外籍、非港澳台、非中国军人
     * @param national
     * @return
     */
    public static boolean checkIsUsersInMainlandResidents(String national){
        if (StringUtils.isNotEmpty(national)) {
            if (national.contains(BussinessConstants.CHINA_NATIONAL)
                    && !BussinessConstants.CHINA_NATIONAL_SOLIER.equals(national)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断用户是否本籍，包括中国居民和中国军人
     * 非外籍、非港澳台
     * @param national
     * @return
     */
    public static boolean checkIsUsersInMainland(String national){
        if (StringUtils.isNotEmpty(national)) {
            if (national.contains(BussinessConstants.CHINA_NATIONAL)) {
                return true;
            }
        }
        return false;
    }

    public static <T> T parseObjectNonEx(String text, Class<T> clazz) {
        if(StringUtils.isNotBlank(text)) {
            try {
                text = StringEscapeUtils.unescapeJava(text);
                return JSON.parseObject(text, clazz);
            }catch (Exception ex) {
            }
        }
        return null;
    }

    public static String toJSONString(Object object) {
        String value = JSON.toJSONString(object);
        if(StringUtils.isNotBlank(value)) {
            value = StringEscapeUtils.unescapeJava(value);
        }
        return value;
    }

    public static Integer getIntValue(String msg) {
        try {
            return Integer.valueOf(msg);
        }catch (Exception ex) {
            return null;
        }
    }

    public static Integer getIntValue(String msg, Integer def) {
        try {
            return Integer.valueOf(msg);
        }catch (Exception ex) {
            return def;
        }
    }

    public static Long getLongValue(String msg) {
        try {
            return Long.valueOf(msg);
        }catch (Exception ex) {
            return null;
        }
    }

    public static BigDecimal getBigDecimal(String str) {
        if(org.apache.commons.lang3.StringUtils.isBlank(str)) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(str).setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 从查询字符串中提取参数值
     *
     * @param queryString 查询字符串，如 "?orderPlatform=secondAppKey&relationId=123"
     * @return 参数名和参数值的 Map
     */
    public static Map<String, String> extractParams(String queryString) {
        Map<String, String> params = new HashMap<>();

        if (queryString == null || queryString.isEmpty()) {
            return params;
        }

        // 去除开头的问号（如果有）
        if (queryString.startsWith("?")) {
            queryString = queryString.substring(1);
        }

        // 按 & 分割参数
        String[] pairs = queryString.split("&");

        for (String pair : pairs) {
            String[] keyValue = pair.split("=");
            if (keyValue.length == 2) {
                String key = keyValue[0];
                String value = keyValue[1];
                params.put(key, value);
            }
        }
        return params;
    }

    public static void main(String[] args) {
       String a =  "{\"authId\":\"12111103277152957905\",\"inviterAuthId\":\"12111103249141022820\",\"mobilePhone\":\"12111103277\",\"orderSeq\":\"C2020090715370000014\",\"payTime\":1599464624000,\"registerTime\":\"20200907152957\"}";
       MemberInviteSuccess b = parseObjectNonEx(a, MemberInviteSuccess.class);
    }

}