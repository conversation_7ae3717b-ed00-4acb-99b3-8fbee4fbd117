package com.extracme.evcard.membership.core.service.license;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.dao.DriverLicenseElementsAuthenticateLogMapper;
import com.extracme.evcard.membership.core.dto.DriverLicenseElementsAuthenticateLogDTO;
import com.extracme.evcard.membership.core.dto.DriverLicenseQueryResultDTO;
import com.extracme.evcard.membership.core.dto.DriverLicenseValidResultDto;
import com.extracme.evcard.membership.core.dto.UpdateUserDto;
import com.extracme.evcard.membership.core.input.SaveDriverElementsAuthenticateLogInput;
import com.extracme.evcard.membership.core.model.DriverLicenseElementsAuthenticateLog;
import com.extracme.evcard.rpc.entity.HidLog;
import com.extracme.evcard.rpc.entity.LogPoint;
import com.extracme.evcard.rpc.messagepush.service.ISensorsdataService;
import com.extracme.framework.core.util.BeanCopyUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 驾照三要素认证服务
 * 认证及埋点
 * @Discription
 * @date 2019/12/16
 * TODO 后续认证流程迁移至此
 *      埋点&认证记录&日志保存
 * IMemberShipService.driverLicenseElementsAuthenticate
 */
@Slf4j
@Service("LicenseAuthenticateService")
public class LicenseAuthenticateProxy{

    @Autowired
    ISensorsdataService sensorsdataService;

    /**
     * 默认三要素校验超时时间为10秒钟
     */
    public static final Integer DEF_READ_TIMEOUT = 10000;

    /**
     * 三要素认证
     * @param authId
     * @param driverCode
     * @param name
     * @param fileNo
     * @param useJuHe 是否
     * @return
     */
    public SaveDriverElementsAuthenticateLogInput authenticate(String authId, String driverCode, String name, String fileNo, Boolean useJuHe, Integer readTimeOut) {
        int supplier = (useJuHe) ? 0 : 1;
        return authenticate(authId, driverCode, name, fileNo, supplier, readTimeOut);
    }

    /**
     * 三要素认证
     * @param authId
     * @param driverCode
     * @param name
     * @param fileNo
     * @param supplier 供应商 0聚合 1小视
     * @return
     */
    public SaveDriverElementsAuthenticateLogInput authenticate(String authId, String driverCode, String name, String fileNo, int supplier, Integer readTimeOut) {
        SaveDriverElementsAuthenticateLogInput resultDto = null;
        ILicenseAuthenticateService licenseAuthService = getInstance(supplier);
        if(licenseAuthService != null) {
            long tm = System.currentTimeMillis();
            log.debug("调用第三方三要素认证接口，authId={}, supplier={}", authId, supplier);
            if(readTimeOut == null) {
                readTimeOut = DEF_READ_TIMEOUT;
            }
            resultDto = licenseAuthService.authenticate(driverCode, name, fileNo, readTimeOut);
            long costs = System.currentTimeMillis() - tm;
            log.info("调用第三方三要素认证接口，authId={}, supplier={}, result={}， cost={}ms", authId, supplier, JSON.toJSONString(resultDto), costs);
            HidLog.membership(BussinessConstants.MEM_LIC_AUTH, "调用第三方三要素认证接口, cost=" + costs, authId, true);
            try {
                String[] resultString = new String[]{"", "认证一致", "不一致", "查无记录", "异常情况"};
                Map<String, Object> map = new HashMap<>();
                map.put("type", "驾照三要素");
                map.put("supplier", BussinessConstants.LICENSE_AUTHENTICATE_SUPPLIERS[supplier]);
                if ("1".equals(resultDto.getResult())) {
                    map.put("is_success", true);
                    map.put("reason", StringUtils.EMPTY);
                } else {
                    map.put("is_success", false);
                    DriverLicenseElementsAuthenticateLogDTO dto = new DriverLicenseElementsAuthenticateLogDTO();
                    BeanCopyUtils.copyProperties(resultDto, dto);
                    map.put("reason", licenseAuthService.buildAuthResultDetail(dto));
                }
                sensorsdataService.track(authId, true, "third_party_verify", map);
            } catch (Exception e) {
                log.error("驾照三要素-认证埋点失败，supplier=" + supplier + "，authId=" + authId, e);
            }
        }
        return resultDto;
    }

    /**
     * 查询驾照扣分情况
     * @param driverCode
     * @param name
     * @param fileNo
     * @param supplier
     * @return
     */
    public SaveDriverElementsAuthenticateLogInput queryDriverDeduction(String authId, String driverCode, String name, String fileNo, Integer supplier) {
        if(supplier == null) {
            supplier = 0;
        }
        SaveDriverElementsAuthenticateLogInput driverDeduction = null;
        ILicenseAuthenticateService licenseAuthService = getInstance(supplier);
        if(licenseAuthService != null) {
            driverDeduction = licenseAuthService.queryDriverDeduction(driverCode, name, fileNo);
            try {
                String[] resultString = new String[]{"", "认证一致", "不一致", "查无记录", "异常情况"};
                /**
                 * 查询分数埋点
                 */
                Map<String, Object> map = new HashMap<>();
                map.put("FailureReason", driverDeduction.getResultMsg());
                if ("0".equals(driverDeduction.getResultCode())) {
                    map.put("SuccessOrFailure", 0);
                } else {
                    map.put("SuccessOrFailure", -1);
                }
                if (org.apache.commons.lang3.StringUtils.isNotBlank(driverDeduction.getResult())) {
                    map.put("alreadyBuckleScores", driverDeduction.getResult());
                } else {
                    map.put("alreadyBuckleScores", "0");
                }
                //sensorsdataService.track(authId, true, "DrivingLicenseInspectionResult", map);
            } catch (Exception e) {
                log.error("驾照三要素-扣分查询埋点失败，supplier=" + supplier + "，authId=" + authId, e);

            }
        }
        return driverDeduction;
    }


    /**
     * 查询驾照信息
     * @param authId
     * @param driverCode 驾照编号
     * @param name 姓名
     * @param supplier
     * @return
     * @remark 保存日志并埋点
     */
    public DriverLicenseQueryResultDTO queryDriverLicenseInfo(String authId, String driverCode, String name, Integer supplier) {
        //当前仅聚合提供查询服务
        if(supplier == null) {
            supplier = 1;
        }
        DriverLicenseQueryResultDTO licenseResultDTO = null;
        ILicenseAuthenticateService licenseAuthService = getInstance(supplier);
        if(licenseAuthService != null) {
            licenseResultDTO = licenseAuthService.queryDriverLicenseInfo(driverCode, name);
            //埋点
            try {
                Map<String, Object> map = new HashMap<>();
                map.put("searchResult", licenseResultDTO.getResultCode());
                if ("1".equals(licenseResultDTO.getResultCode())) {
                    map.put("SuccessOrFailure", 0);
                    map.put("FailureReason", "");
                } else {
                    map.put("SuccessOrFailure", -1);
                    map.put("FailureReason", licenseResultDTO.getResultMsg());
                }
                //sensorsdataService.track(authId, true, "DrivingLicenseDetailQuery", map);
            } catch (Exception e) {
                log.error("驾照详情查询-埋点失败，supplier=" + supplier + "，authId=" + authId, e);
            }
        }
        return licenseResultDTO;
    }


    /**
     * 三要素认证详情解析
     * @param logDTO 认证日志对象
     * @return
     */
    public String buildAuthResultDetail(DriverLicenseElementsAuthenticateLogDTO logDTO) {
        Integer supplier = logDTO.getSupplier();
        if(supplier != null) {
            //对手动认证通过日志做特殊处理
            if(supplier == -1) {
                return "会员系统手动认证通过";
            }
            ILicenseAuthenticateService licenseAuthService = getInstance(supplier);
            if(licenseAuthService != null) {
                return licenseAuthService.buildAuthResultDetail(logDTO);
            }
        }
        return StringUtils.EMPTY;
    }
    
    public DriverLicenseValidResultDto checkDriverLicenseValid(String name, String cardNo, String archviesNo) {
    	ILicenseAuthenticateService licenseAuthService = getInstance(0);
    	return licenseAuthService.checkDriverLicenseValid(name, cardNo, archviesNo);
    }

    @Autowired
    private LicenseAuthServiceContext licenseAuthServiceContext;
    private ILicenseAuthenticateService getInstance(int supplier){
        ILicenseAuthenticateService authService = null;
        if(supplier < 0 || supplier >= BussinessConstants.LICENSE_AUTHENTICATE_SUPPLIER_NAMES.length) {
            supplier = 2;
        }
        String supplierName = BussinessConstants.LICENSE_AUTHENTICATE_SUPPLIER_NAMES[supplier];
        return licenseAuthServiceContext.getInstance(supplierName);
    }
}
