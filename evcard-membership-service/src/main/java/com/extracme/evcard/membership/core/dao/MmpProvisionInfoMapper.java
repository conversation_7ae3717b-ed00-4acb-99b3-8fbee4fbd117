package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.model.MmpProvisionInfo;
import org.apache.ibatis.annotations.Param;

public interface MmpProvisionInfoMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MmpProvisionInfo record);

    int insertSelective(MmpProvisionInfo record);

    MmpProvisionInfo selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MmpProvisionInfo record);

    int updateByPrimaryKey(MmpProvisionInfo record);

    String queryLatestMmpProvisionVersion(Integer provisionType);

    MmpProvisionInfo queryLatestMmpProvision(Integer provisionType);

    MmpProvisionInfo queryMmpProvisionByVersion(@Param("version")String version, @Param("provisionType")Integer provisionType);
}