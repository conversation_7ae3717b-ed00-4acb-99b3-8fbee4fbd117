package com.extracme.evcard.membership.core.service.auth.login;

import com.alibaba.dubbo.common.Constants;
import com.alibaba.fastjson.JSON;
import com.baosight.iplat4j.security.util.Crypto;
import com.extracme.evcard.membership.common.MidGenerator;
import com.extracme.evcard.membership.core.canstant.BussinessConstants;
import com.extracme.evcard.membership.core.dao.CityMapper;
import com.extracme.evcard.membership.core.dao.MemberIdentityDocumentMapper;
import com.extracme.evcard.membership.core.dao.SecondAppKeyManagerMapper;
import com.extracme.evcard.membership.core.dto.MembershipBasicInfo;
import com.extracme.evcard.membership.core.input.ThirdLoginContext;
import com.extracme.evcard.membership.core.input.ThirdLoginInput;
import com.extracme.evcard.membership.core.input.ThirdLoginOtherDto;
import com.extracme.evcard.membership.core.model.City;
import com.extracme.evcard.membership.core.model.MemberIdentityDocument;
import com.extracme.evcard.membership.core.model.SecondAppKeyManager;
import com.extracme.evcard.membership.core.service.IMemberShipService;
import com.extracme.evcard.membership.credit.dao.MembershipInfoMapper;
import com.extracme.evcard.membership.credit.model.MembershipInfoWithBLOBs;
import com.extracme.evcard.redis.JedisLock;
import com.extracme.evcard.redis.JedisUtil;
import com.extracme.evcard.rpc.enums.StatusCode;
import com.extracme.evcard.rpc.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

import static com.extracme.evcard.membership.common.ComUtil.*;

/**
 * 通用用户 登录注册
 */
@Slf4j
@Service
public class CommonUserLogin extends ThirdLoginAbstract {

    @Autowired
    private IMemberShipService memberShipService;
    @Autowired
    private CityMapper cityMapper;
    @Resource
    private MidGenerator midGenerator;
    @Autowired
    private MembershipInfoMapper membershipInfoMapper;
    @Resource
    private MemberIdentityDocumentMapper memberIdentityDocumentMapper;
    @Resource
    private SecondAppKeyManagerMapper secondAppKeyManagerMapper;


    @Override
    public String getSecondAppKey() {
        return "通用用户登陆二级渠道";
    }

    @Override
    public String getDescription() {
        return "通用用户登陆";
    }

    @Override
    public void paramCheck(ThirdLoginContext context) throws BusinessException {
        ThirdLoginOtherDto otherDto = context.getOtherDto();

        ThirdLoginInput input = context.getInput();
        String cityId = input.getCityId();
        String mobilePhone = input.getMobilePhone();
        String secondAppKey = input.getSecondAppKey();

        if (StringUtils.isBlank(mobilePhone) || StringUtils.isBlank(secondAppKey)) {
            throw new BusinessException(-1, "入参不能为空");
        }

        if (!pattern.matcher(mobilePhone).matches()) {
            throw new BusinessException(StatusCode.MOBILE_FORMATE_ERROR);
        }

        SecondAppKeyManager secondAppKeyManager = secondAppKeyManagerMapper.selectBySecondAppKey(secondAppKey);
        if (secondAppKeyManager == null) {
            throw new BusinessException(StatusCode.APPKEY_INVALID);
        }

        otherDto.setFirstAppKey(secondAppKeyManager.getFirstAppKey());
        otherDto.setPlatFormId(secondAppKeyManager.getPlatformId());

        String cityName = "上海市";
        if (StringUtils.isNotEmpty(cityId)) {
            City city = cityMapper.getCityByCityId(cityId);
            if (city != null && StringUtils.isNotEmpty(city.getCity())) {
                cityName = city.getCity();
            }
        }
        otherDto.setCityName(cityName);
    }


    @Override
    protected MembershipBasicInfo doRegisterMembershipInfo(ThirdLoginContext context) throws BusinessException {
        ThirdLoginOtherDto otherDto = context.getOtherDto();
        String cityName = otherDto.getCityName();
        ThirdLoginInput input = context.getInput();
        log.info("{}，doRegisterMembershipInfo,input[{}]", getDescription(), JSON.toJSONString(input));
        String mobilePhone = input.getMobilePhone();
        String firstAppKey = otherDto.getFirstAppKey();
        String userName = input.getUserName();
        String secondAppKey = input.getSecondAppKey();
        String opeatorName = userName;
        if (StringUtils.isBlank(opeatorName)) {
            opeatorName = BussinessConstants.APP_KEY_EVCARD_MEMBERSHIP_RPC;
        }


        String redisKey = mobilePhone + Constants.COMMA_SEPARATOR + firstAppKey + Constants.COMMA_SEPARATOR + Constants.COMMA_SEPARATOR + getMembershipType();
        Jedis jedis = null;
        JedisLock jedisLock = null;
        try {
            //防止手机号并发注册
            jedis = JedisUtil.getJedis();
            jedisLock = new JedisLock(redisKey);
            if (!jedisLock.acquire(jedis)) {
                throw new BusinessException(StatusCode.SYSTEM_ERROR);
            }

            // 密码校验
            String password = mobilePhone.substring(1, 6).trim();
            //加密密码
            password = Crypto.encryptDES(password, ENCRYPT_KEY);
            //生成会员ID
            LocalDateTime localDateTime = LocalDateTime.now();
            String formattedTime = localDateTime.format(DateTimeFormatter.ofPattern(DATE_TYPE3));
            String authId = mobilePhone + localDateTime.format(DateTimeFormatter.ofPattern(DATE_TYPE6));
            //插入记录
            MembershipInfoWithBLOBs member = new MembershipInfoWithBLOBs();
            member.setAuthId(authId);
            member.setName(userName);
            member.setPassword(password);
            member.setMobilePhone(mobilePhone);
            member.setCityOfOrigin(cityName);
            member.setMembershipType((short) getMembershipType());

            //审核状态
            member.setReviewStatus((short) -1);
            member.setCreatedTime(formattedTime);
            member.setCreatedUser(opeatorName);
            member.setUpdatedTime(formattedTime);
            member.setUpdatedUser(opeatorName);
            member.setRegTime(localDateTime.format(DateTimeFormatter.ofPattern(DATE_TYPE4)));

            // 注册来源
            member.setSecondAppKey(secondAppKey);
            member.setAppKey(firstAppKey);
            member.setPlatformId(otherDto.getPlatFormId());
            member.setMid(midGenerator.getMid());
            //member.setManufacturer(BussinessConstants.MANUFACTURER_EVCARD_MMP);

            // 新增记录
            if (otherDto.isNeedIdentitySupplement()) {
                MemberIdentityDocument insertDocument = insertIdentityDocument(input, member.getMid());
                member.setIdentityId(insertDocument.getId());
            }

            //更新会员条款版本号
            String typeTwoVersion = input.getPrivacyPolicyVersion();
            String typeOneVersion = input.getMembershipPolicyVersion();

            if (input.isAgreePolicy()) {
                if (StringUtils.isBlank(typeOneVersion)) {
                    typeOneVersion = JedisUtil.hget("typeVersionMap", "1");
                }
                if (StringUtils.isBlank(typeTwoVersion)) {
                    typeTwoVersion = JedisUtil.hget("typeVersionMap", "2");
                }
                member.setServiceVer(typeOneVersion + "," + typeTwoVersion);
            }

            int insertSelective = membershipInfoMapper.insertSelective(member);
            if (insertSelective > 0) {
                if (input.isAgreePolicy()) {
                    //插入条款历史
                    if (StringUtils.isNotBlank(typeOneVersion)) {
                        insertMemberClaseLog(member.getAuthId(), opeatorName, "1_" + typeOneVersion);
                    }
                    if (StringUtils.isNotBlank(typeTwoVersion)) {
                        insertMemberClaseLog(member.getAuthId(), opeatorName, "2_" + typeTwoVersion);
                    }
                }
                log.info("{},mobile={},insertSelective成功", getDescription(), mobilePhone);
                MembershipBasicInfo membership = membershipInfoMapper.getUserBasicInfoByMid(member.getMid());
                context.setMembershipBasicInfo(membership);
                return membership;
            } else {
                throw new BusinessException(-1, "新增会员失败");
            }
        } catch (Exception e) {
            log.error("{} 失败,原因：创建会员记录失败", getDescription(), e);
            throw new BusinessException(StatusCode.SYSTEM_ERROR);
        } finally {
            if (jedisLock != null && jedis != null) {
                jedisLock.releaseLua(jedis);
                jedis.close();
                log.debug("通用用户注册服务-释放redis锁和链接成功");
            }
        }
    }

    /**
     * 新增 MemberIdentityDocument记录
     *
     * @param input
     * @param mid
     * @return
     */
    private MemberIdentityDocument insertIdentityDocument(ThirdLoginInput input, String mid) throws BusinessException {
        String userName = input.getUserName();
        MemberIdentityDocument insertDocument = new MemberIdentityDocument();
        insertDocument.setMid(mid);
        insertDocument.setIdentityNo(input.getIdCardNo());
        insertDocument.setName(userName);
        insertDocument.setIdentityType(input.getIdentityType());
        insertDocument.setCreateTime(new Date());
        insertDocument.setCreateOperName(StringUtils.isNotBlank(userName) ? userName : BussinessConstants.APP_KEY_EVCARD_MEMBERSHIP_RPC);
        int i = memberIdentityDocumentMapper.insertSelective(insertDocument);
        if (i < 0) {
            throw new BusinessException(-1, "新增MemberIdentityDocument记录失败");
        }
        return insertDocument;
    }


    @Override
    public void afterGetMembershipInfo(ThirdLoginContext context) throws BusinessException {
        MembershipBasicInfo membershipInfo = context.getMembershipBasicInfo();
        //制卡，该方法已实现幂等
        if (StringUtils.isBlank(membershipInfo.getCardNo())) {
            log.info("{}membershipInfo=[{}],开始制卡", getDescription(), JSON.toJSONString(membershipInfo));
            memberShipService.setVirtualCard(membershipInfo.getAuthId(), getMembershipType());
            log.info("{}membershipInfo=[{}],制卡成功", getDescription(), JSON.toJSONString(membershipInfo));
        }
    }


    @Override
    public boolean isNeedToken(ThirdLoginInput object) {
        return false;
    }


}
