package com.extracme.evcard.membership.core.model;

import java.io.Serializable;
import java.math.BigDecimal;

public class City implements Serializable{
    private static final long serialVersionUID = -7964474307597040861L;

    private Long id;
    //城市id
    private Long cityid;
    //城市名称
    private String city;
    //父
    private Long fatherid;
    private BigDecimal lon;
    private BigDecimal lat;
    /**
     * 是否投入运营 0：不投入  1：投入运营
     */
    private Integer status;
    //运营机构id
    private String orgId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCityid() {
        return cityid;
    }

    public void setCityid(Long cityid) {
        this.cityid = cityid;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public Long getFatherid() {
        return fatherid;
    }

    public void setFatherid(Long fatherid) {
        this.fatherid = fatherid;
    }

    public BigDecimal getLon() {
        return lon;
    }

    public void setLon(BigDecimal lon) {
        this.lon = lon;
    }

    public BigDecimal getLat() {
        return lat;
    }

    public void setLat(BigDecimal lat) {
        this.lat = lat;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }
}
