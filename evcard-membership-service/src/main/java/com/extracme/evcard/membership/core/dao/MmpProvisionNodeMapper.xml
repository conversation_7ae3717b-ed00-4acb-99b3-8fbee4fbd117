<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.extracme.evcard.membership.core.dao.MmpProvisionNodeMapper" >
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.MmpProvisionNode" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="node_id" property="nodeId" jdbcType="VARCHAR" />
    <result column="node_name" property="nodeName" jdbcType="VARCHAR" />
    <result column="provision_type" property="provisionType" jdbcType="INTEGER" />
    <result column="provision_id" property="provisionId" jdbcType="BIGINT" />
    <result column="version" property="version" jdbcType="VARCHAR" />
    <result column="close_provision_id" property="closeProvisionId" jdbcType="BIGINT" />
    <result column="close_version" property="closeVersion" jdbcType="VARCHAR" />
    <result column="misc_desc" property="miscDesc" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_oper_id" property="createOperId" jdbcType="BIGINT" />
    <result column="create_oper_name" property="createOperName" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="update_oper_id" property="updateOperId" jdbcType="BIGINT" />
    <result column="update_oper_name" property="updateOperName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, node_id, node_name, provision_type, provision_id, version, close_provision_id, 
    close_version, misc_desc, status, create_time, create_oper_id, create_oper_name, 
    update_time, update_oper_id, update_oper_name
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from ${issSchema}.mmp_provision_node
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from ${issSchema}.mmp_provision_node
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.extracme.evcard.membership.core.model.MmpProvisionNode" >
    insert into ${issSchema}.mmp_provision_node (id, node_id, node_name, 
      provision_type, provision_id, version, 
      close_provision_id, close_version, misc_desc, 
      status, create_time, create_oper_id, 
      create_oper_name, update_time, update_oper_id, 
      update_oper_name)
    values (#{id,jdbcType=BIGINT}, #{nodeId,jdbcType=VARCHAR}, #{nodeName,jdbcType=VARCHAR}, 
      #{provisionType,jdbcType=INTEGER}, #{provisionId,jdbcType=BIGINT}, #{version,jdbcType=VARCHAR}, 
      #{closeProvisionId,jdbcType=BIGINT}, #{closeVersion,jdbcType=VARCHAR}, #{miscDesc,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, 
      #{createOperName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, 
      #{updateOperName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.extracme.evcard.membership.core.model.MmpProvisionNode" >
    insert into ${issSchema}.mmp_provision_node
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="nodeId != null" >
        node_id,
      </if>
      <if test="nodeName != null" >
        node_name,
      </if>
      <if test="provisionType != null" >
        provision_type,
      </if>
      <if test="provisionId != null" >
        provision_id,
      </if>
      <if test="version != null" >
        version,
      </if>
      <if test="closeProvisionId != null" >
        close_provision_id,
      </if>
      <if test="closeVersion != null" >
        close_version,
      </if>
      <if test="miscDesc != null" >
        misc_desc,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="createOperId != null" >
        create_oper_id,
      </if>
      <if test="createOperName != null" >
        create_oper_name,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="updateOperId != null" >
        update_oper_id,
      </if>
      <if test="updateOperName != null" >
        update_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="nodeId != null" >
        #{nodeId,jdbcType=VARCHAR},
      </if>
      <if test="nodeName != null" >
        #{nodeName,jdbcType=VARCHAR},
      </if>
      <if test="provisionType != null" >
        #{provisionType,jdbcType=INTEGER},
      </if>
      <if test="provisionId != null" >
        #{provisionId,jdbcType=BIGINT},
      </if>
      <if test="version != null" >
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="closeProvisionId != null" >
        #{closeProvisionId,jdbcType=BIGINT},
      </if>
      <if test="closeVersion != null" >
        #{closeVersion,jdbcType=VARCHAR},
      </if>
      <if test="miscDesc != null" >
        #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        #{updateOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.core.model.MmpProvisionNode" >
    update ${issSchema}.mmp_provision_node
    <set >
      <if test="nodeId != null" >
        node_id = #{nodeId,jdbcType=VARCHAR},
      </if>
      <if test="nodeName != null" >
        node_name = #{nodeName,jdbcType=VARCHAR},
      </if>
      <if test="provisionType != null" >
        provision_type = #{provisionType,jdbcType=INTEGER},
      </if>
      <if test="provisionId != null" >
        provision_id = #{provisionId,jdbcType=BIGINT},
      </if>
      <if test="version != null" >
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="closeProvisionId != null" >
        close_provision_id = #{closeProvisionId,jdbcType=BIGINT},
      </if>
      <if test="closeVersion != null" >
        close_version = #{closeVersion,jdbcType=VARCHAR},
      </if>
      <if test="miscDesc != null" >
        misc_desc = #{miscDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null" >
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null" >
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null" >
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null" >
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.core.model.MmpProvisionNode" >
    update ${issSchema}.mmp_provision_node
    set node_id = #{nodeId,jdbcType=VARCHAR},
      node_name = #{nodeName,jdbcType=VARCHAR},
      provision_type = #{provisionType,jdbcType=INTEGER},
      provision_id = #{provisionId,jdbcType=BIGINT},
      version = #{version,jdbcType=VARCHAR},
      close_provision_id = #{closeProvisionId,jdbcType=BIGINT},
      close_version = #{closeVersion,jdbcType=VARCHAR},
      misc_desc = #{miscDesc,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_oper_id = #{createOperId,jdbcType=BIGINT},
      create_oper_name = #{createOperName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_oper_id = #{updateOperId,jdbcType=BIGINT},
      update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>