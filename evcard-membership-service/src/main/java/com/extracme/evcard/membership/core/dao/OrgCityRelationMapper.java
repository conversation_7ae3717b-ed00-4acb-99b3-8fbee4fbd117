package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.model.OrgCityRelation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrgCityRelationMapper {
    int deleteByPrimaryKey(Long id);

    int insert(OrgCityRelation record);

    int insertSelective(OrgCityRelation record);

    OrgCityRelation selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OrgCityRelation record);

    int updateByPrimaryKey(OrgCityRelation record);

    OrgCityRelation selectByCityName(String name);

    List<OrgCityRelation> selectByOrgId(@Param("list") List<String> orgId);

}