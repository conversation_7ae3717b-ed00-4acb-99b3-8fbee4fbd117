package com.extracme.evcard.membership.core.service.license;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class LicenseAuthServiceContext {
    @Autowired
    private final Map<String, ILicenseAuthenticateService> serviceMap = new ConcurrentHashMap<>(5);

    /**
     * 创建市场活动服务对象
     * @return
     */
    public ILicenseAuthenticateService getInstance(String name) {
        ILicenseAuthenticateService serviceObject = serviceMap.get(name);
        if(serviceObject == null) {
            log.error("service [" + name + "] not found.");
            throw new IllegalArgumentException("获取服务失败: " + name);
        }
        return serviceObject;
    }

}
