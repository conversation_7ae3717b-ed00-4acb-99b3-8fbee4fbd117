package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.dto.MemberClauseRecord;
import com.extracme.evcard.membership.core.dto.ProvisionInfoDto;
import com.extracme.evcard.membership.core.dto.UserContractInfo;
import com.extracme.evcard.membership.core.dto.UserContractKeyDto;
import com.extracme.evcard.membership.core.model.UserContract;
import com.extracme.evcard.membership.core.model.UserContractExample;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface UserContractMapper {
    int countByExample(UserContractExample example);

    int deleteByExample(UserContractExample example);

    int deleteByPrimaryKey(Long id);

    int insert(UserContract record);

    int insertSelective(UserContract record);

    List<UserContract> selectByExample(UserContractExample example);

    UserContract selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") UserContract record, @Param("example") UserContractExample example);

    int updateByExample(@Param("record") UserContract record, @Param("example") UserContractExample example);

    int updateByPrimaryKeySelective(UserContract record);

    int updateByPrimaryKey(UserContract record);

    List<MemberClauseRecord> selectMemberClauseRecord(@Param("authId")String authId,@Param("pageNum") int pageNum,@Param("pageSize") int pageSize);

    /**
     * 查询用户时刻点前签署的最新条款
     * @param authId
     * @param prefix 条款前缀 SZ会员守则 YS隐私条款
     * @param date
     * @return
     */
    UserContract selectMemberLastContract(@Param("authId")String authId, @Param("prefix")String prefix, @Param("date")String date);

    /**
     * 查询用户时刻点后签署的第一份条款
     * @param authId
     * @param prefix 条款前缀 SZ会员守则 YS隐私条款
     * @param date
     * @return
     */
    UserContract selectMemberCloseContract(@Param("authId")String authId, @Param("prefix")String prefix,
                                           @Param("date")String date, @Param("versionId")String versionId);

    /**
     * 查询指定的协议模板信息
     * @param provisionType
     * @param version
     * @return
     */
    ProvisionInfoDto selectProvisionInfo(@Param("provisionType")Integer provisionType, @Param("version")String version);

    UserContractInfo selectUserContractInfo(@Param("authId") String authId,
                                            @Param("templateId") String templateId);

    /**
     * 查询历史法大大合同
     * @param sp
     * @param ep
     * @param limit
     * @return
     */
    List<UserContractKeyDto> selectFddContracts(@Param("sp")Long sp, @Param("ep")Long ep,
                                                @Param("limit")Integer limit);

    int updateArchiveUrl(@Param("prefix")String prefix,
                         @Param("supplier")Integer supplier, @Param("list") Set<String> list);

    List<UserContract> selectContractsByIds(@Param("list") Set<Long> list);


    List<UserContract> selectUnArchiveContracts(@Param("startTime") Date startTime, @Param("endTime") Date endTime);
}