<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.extracme.evcard.membership.core.dao.MemberIdentityDocumentMapper">
  <resultMap id="BaseResultMap" type="com.extracme.evcard.membership.core.model.MemberIdentityDocument">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="mid" jdbcType="VARCHAR" property="mid" />
    <result column="identity_no" jdbcType="VARCHAR" property="identityNo" />
    <result column="identity_type" jdbcType="INTEGER" property="identityType" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="expire_type" jdbcType="INTEGER" property="expireType" />
    <result column="expiration_date" jdbcType="VARCHAR" property="expirationDate" />
    <result column="identity_card_img_url" jdbcType="VARCHAR" property="identityCardImgUrl" />
    <result column="reverse_identity_card_img_url" jdbcType="VARCHAR" property="reverseIdentityCardImgUrl" />
    <result column="hold_idcard_pic_url" jdbcType="VARCHAR" property="holdIdcardPicUrl" />
    <result column="face_recognition_img_url" jdbcType="VARCHAR" property="faceRecognitionImgUrl" />
    <result column="cert_input_type" jdbcType="INTEGER" property="certInputType" />
    <result column="authentication_status" jdbcType="INTEGER" property="authenticationStatus" />
    <result column="submit_time" jdbcType="TIMESTAMP" property="submitTime" />
    <result column="submit_appkey" jdbcType="VARCHAR" property="submitAppkey" />
    <result column="review_user" jdbcType="VARCHAR" property="reviewUser" />
    <result column="review_time" jdbcType="TIMESTAMP" property="reviewTime" />
    <result column="review_items" jdbcType="VARCHAR" property="reviewItems" />
    <result column="review_ids" jdbcType="VARCHAR" property="reviewIds" />
    <result column="review_remark" jdbcType="VARCHAR" property="reviewRemark" />
    <result column="review_item_names" jdbcType="VARCHAR" property="reviewItemNames" />
    <result column="review_mode" jdbcType="INTEGER" property="reviewMode" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_oper_id" jdbcType="BIGINT" property="createOperId" />
    <result column="create_oper_name" jdbcType="VARCHAR" property="createOperName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_oper_id" jdbcType="BIGINT" property="updateOperId" />
    <result column="update_oper_name" jdbcType="VARCHAR" property="updateOperName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, mid, identity_no, identity_type, name, expire_type, expiration_date, identity_card_img_url,
    reverse_identity_card_img_url, hold_idcard_pic_url, face_recognition_img_url, cert_input_type,
    authentication_status, submit_time, submit_appkey, review_user, review_time, review_items,
    review_ids, review_remark, review_item_names, review_mode, status, create_time, create_oper_id,
    create_oper_name, update_time, update_oper_id, update_oper_name
  </sql>
  <select id="selectByExample" parameterType="com.extracme.evcard.membership.core.model.MemberIdentityDocumentExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from ${siacSchema}.member_identity_document
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${siacSchema}.member_identity_document
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ${siacSchema}.member_identity_document
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="com.extracme.evcard.membership.core.model.MemberIdentityDocument">
    insert into ${siacSchema}.member_identity_document (id, mid, identity_no,
    identity_type, name, expire_type,
    expiration_date, identity_card_img_url, reverse_identity_card_img_url,
    hold_idcard_pic_url, face_recognition_img_url,
    cert_input_type, authentication_status, submit_time,
    submit_appkey, review_user, review_time,
    review_items, review_ids, review_remark,
    review_item_names, review_mode, status,
    create_time, create_oper_id, create_oper_name,
    update_time, update_oper_id, update_oper_name
    )
    values (#{id,jdbcType=BIGINT}, #{mid,jdbcType=VARCHAR}, #{identityNo,jdbcType=VARCHAR},
    #{identityType,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{expireType,jdbcType=INTEGER},
    #{expirationDate,jdbcType=VARCHAR}, #{identityCardImgUrl,jdbcType=VARCHAR}, #{reverseIdentityCardImgUrl,jdbcType=VARCHAR},
    #{holdIdcardPicUrl,jdbcType=VARCHAR}, #{faceRecognitionImgUrl,jdbcType=VARCHAR},
    #{certInputType,jdbcType=INTEGER}, #{authenticationStatus,jdbcType=INTEGER}, #{submitTime,jdbcType=TIMESTAMP},
    #{submitAppkey,jdbcType=VARCHAR}, #{reviewUser,jdbcType=VARCHAR}, #{reviewTime,jdbcType=TIMESTAMP},
    #{reviewItems,jdbcType=VARCHAR}, #{reviewIds,jdbcType=VARCHAR}, #{reviewRemark,jdbcType=VARCHAR},
    #{reviewItemNames,jdbcType=VARCHAR}, #{reviewMode,jdbcType=INTEGER}, #{status,jdbcType=INTEGER},
    #{createTime,jdbcType=TIMESTAMP}, #{createOperId,jdbcType=BIGINT}, #{createOperName,jdbcType=VARCHAR},
    #{updateTime,jdbcType=TIMESTAMP}, #{updateOperId,jdbcType=BIGINT}, #{updateOperName,jdbcType=VARCHAR}
    )
  </insert>
  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="com.extracme.evcard.membership.core.model.MemberIdentityDocument">
    insert into ${siacSchema}.member_identity_document
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="mid != null">
        mid,
      </if>
      <if test="identityNo != null">
        identity_no,
      </if>
      <if test="identityType != null">
        identity_type,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="expireType != null">
        expire_type,
      </if>
      <if test="expirationDate != null">
        expiration_date,
      </if>
      <if test="identityCardImgUrl != null">
        identity_card_img_url,
      </if>
      <if test="reverseIdentityCardImgUrl != null">
        reverse_identity_card_img_url,
      </if>
      <if test="holdIdcardPicUrl != null">
        hold_idcard_pic_url,
      </if>
      <if test="faceRecognitionImgUrl != null">
        face_recognition_img_url,
      </if>
      <if test="certInputType != null">
        cert_input_type,
      </if>
      <if test="authenticationStatus != null">
        authentication_status,
      </if>
      <if test="submitTime != null">
        submit_time,
      </if>
      <if test="submitAppkey != null">
        submit_appkey,
      </if>
      <if test="reviewUser != null">
        review_user,
      </if>
      <if test="reviewTime != null">
        review_time,
      </if>
      <if test="reviewItems != null">
        review_items,
      </if>
      <if test="reviewIds != null">
        review_ids,
      </if>
      <if test="reviewRemark != null">
        review_remark,
      </if>
      <if test="reviewItemNames != null">
        review_item_names,
      </if>
      <if test="reviewMode != null">
        review_mode,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createOperId != null">
        create_oper_id,
      </if>
      <if test="createOperName != null">
        create_oper_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateOperId != null">
        update_oper_id,
      </if>
      <if test="updateOperName != null">
        update_oper_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="mid != null">
        #{mid,jdbcType=VARCHAR},
      </if>
      <if test="identityNo != null">
        #{identityNo,jdbcType=VARCHAR},
      </if>
      <if test="identityType != null">
        #{identityType,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="expireType != null">
        #{expireType,jdbcType=INTEGER},
      </if>
      <if test="expirationDate != null">
        #{expirationDate,jdbcType=VARCHAR},
      </if>
      <if test="identityCardImgUrl != null">
        #{identityCardImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="reverseIdentityCardImgUrl != null">
        #{reverseIdentityCardImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="holdIdcardPicUrl != null">
        #{holdIdcardPicUrl,jdbcType=VARCHAR},
      </if>
      <if test="faceRecognitionImgUrl != null">
        #{faceRecognitionImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="certInputType != null">
        #{certInputType,jdbcType=INTEGER},
      </if>
      <if test="authenticationStatus != null">
        #{authenticationStatus,jdbcType=INTEGER},
      </if>
      <if test="submitTime != null">
        #{submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="submitAppkey != null">
        #{submitAppkey,jdbcType=VARCHAR},
      </if>
      <if test="reviewUser != null">
        #{reviewUser,jdbcType=VARCHAR},
      </if>
      <if test="reviewTime != null">
        #{reviewTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reviewItems != null">
        #{reviewItems,jdbcType=VARCHAR},
      </if>
      <if test="reviewIds != null">
        #{reviewIds,jdbcType=VARCHAR},
      </if>
      <if test="reviewRemark != null">
        #{reviewRemark,jdbcType=VARCHAR},
      </if>
      <if test="reviewItemNames != null">
        #{reviewItemNames,jdbcType=VARCHAR},
      </if>
      <if test="reviewMode != null">
        #{reviewMode,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        #{updateOperName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map">
    update ${siacSchema}.member_identity_document
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.mid != null">
        mid = #{record.mid,jdbcType=VARCHAR},
      </if>
      <if test="record.identityNo != null">
        identity_no = #{record.identityNo,jdbcType=VARCHAR},
      </if>
      <if test="record.identityType != null">
        identity_type = #{record.identityType,jdbcType=INTEGER},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.expireType != null">
        expire_type = #{record.expireType,jdbcType=INTEGER},
      </if>
      <if test="record.expirationDate != null">
        expiration_date = #{record.expirationDate,jdbcType=VARCHAR},
      </if>
      <if test="record.identityCardImgUrl != null">
        identity_card_img_url = #{record.identityCardImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.reverseIdentityCardImgUrl != null">
        reverse_identity_card_img_url = #{record.reverseIdentityCardImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.holdIdcardPicUrl != null">
        hold_idcard_pic_url = #{record.holdIdcardPicUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.faceRecognitionImgUrl != null">
        face_recognition_img_url = #{record.faceRecognitionImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.certInputType != null">
        cert_input_type = #{record.certInputType,jdbcType=INTEGER},
      </if>
      <if test="record.authenticationStatus != null">
        authentication_status = #{record.authenticationStatus,jdbcType=INTEGER},
      </if>
      <if test="record.submitTime != null">
        submit_time = #{record.submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.submitAppkey != null">
        submit_appkey = #{record.submitAppkey,jdbcType=VARCHAR},
      </if>
      <if test="record.reviewUser != null">
        review_user = #{record.reviewUser,jdbcType=VARCHAR},
      </if>
      <if test="record.reviewTime != null">
        review_time = #{record.reviewTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reviewItems != null">
        review_items = #{record.reviewItems,jdbcType=VARCHAR},
      </if>
      <if test="record.reviewIds != null">
        review_ids = #{record.reviewIds,jdbcType=VARCHAR},
      </if>
      <if test="record.reviewRemark != null">
        review_remark = #{record.reviewRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.reviewItemNames != null">
        review_item_names = #{record.reviewItemNames,jdbcType=VARCHAR},
      </if>
      <if test="record.reviewMode != null">
        review_mode = #{record.reviewMode,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createOperId != null">
        create_oper_id = #{record.createOperId,jdbcType=BIGINT},
      </if>
      <if test="record.createOperName != null">
        create_oper_name = #{record.createOperName,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateOperId != null">
        update_oper_id = #{record.updateOperId,jdbcType=BIGINT},
      </if>
      <if test="record.updateOperName != null">
        update_oper_name = #{record.updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ${siacSchema}.member_identity_document
    set id = #{record.id,jdbcType=BIGINT},
    mid = #{record.mid,jdbcType=VARCHAR},
    identity_no = #{record.identityNo,jdbcType=VARCHAR},
    identity_type = #{record.identityType,jdbcType=INTEGER},
    name = #{record.name,jdbcType=VARCHAR},
    expire_type = #{record.expireType,jdbcType=INTEGER},
    expiration_date = #{record.expirationDate,jdbcType=VARCHAR},
    identity_card_img_url = #{record.identityCardImgUrl,jdbcType=VARCHAR},
    reverse_identity_card_img_url = #{record.reverseIdentityCardImgUrl,jdbcType=VARCHAR},
    hold_idcard_pic_url = #{record.holdIdcardPicUrl,jdbcType=VARCHAR},
    face_recognition_img_url = #{record.faceRecognitionImgUrl,jdbcType=VARCHAR},
    cert_input_type = #{record.certInputType,jdbcType=INTEGER},
    authentication_status = #{record.authenticationStatus,jdbcType=INTEGER},
    submit_time = #{record.submitTime,jdbcType=TIMESTAMP},
    submit_appkey = #{record.submitAppkey,jdbcType=VARCHAR},
    review_user = #{record.reviewUser,jdbcType=VARCHAR},
    review_time = #{record.reviewTime,jdbcType=TIMESTAMP},
    review_items = #{record.reviewItems,jdbcType=VARCHAR},
    review_ids = #{record.reviewIds,jdbcType=VARCHAR},
    review_remark = #{record.reviewRemark,jdbcType=VARCHAR},
    review_item_names = #{record.reviewItemNames,jdbcType=VARCHAR},
    review_mode = #{record.reviewMode,jdbcType=INTEGER},
    status = #{record.status,jdbcType=INTEGER},
    create_time = #{record.createTime,jdbcType=TIMESTAMP},
    create_oper_id = #{record.createOperId,jdbcType=BIGINT},
    create_oper_name = #{record.createOperName,jdbcType=VARCHAR},
    update_time = #{record.updateTime,jdbcType=TIMESTAMP},
    update_oper_id = #{record.updateOperId,jdbcType=BIGINT},
    update_oper_name = #{record.updateOperName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.extracme.evcard.membership.core.model.MemberIdentityDocument">
    update ${siacSchema}.member_identity_document
    <set>
      <if test="mid != null">
        mid = #{mid,jdbcType=VARCHAR},
      </if>
      <if test="identityNo != null">
        identity_no = #{identityNo,jdbcType=VARCHAR},
      </if>
      <if test="identityType != null">
        identity_type = #{identityType,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="expireType != null">
        expire_type = #{expireType,jdbcType=INTEGER},
      </if>
      <if test="expirationDate != null">
        expiration_date = #{expirationDate,jdbcType=VARCHAR},
      </if>
      <if test="identityCardImgUrl != null">
        identity_card_img_url = #{identityCardImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="reverseIdentityCardImgUrl != null">
        reverse_identity_card_img_url = #{reverseIdentityCardImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="holdIdcardPicUrl != null">
        hold_idcard_pic_url = #{holdIdcardPicUrl,jdbcType=VARCHAR},
      </if>
      <if test="faceRecognitionImgUrl != null">
        face_recognition_img_url = #{faceRecognitionImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="certInputType != null">
        cert_input_type = #{certInputType,jdbcType=INTEGER},
      </if>
      <if test="authenticationStatus != null">
        authentication_status = #{authenticationStatus,jdbcType=INTEGER},
      </if>
      <if test="submitTime != null">
        submit_time = #{submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="submitAppkey != null">
        submit_appkey = #{submitAppkey,jdbcType=VARCHAR},
      </if>
      <if test="reviewUser != null">
        review_user = #{reviewUser,jdbcType=VARCHAR},
      </if>
      <if test="reviewTime != null">
        review_time = #{reviewTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reviewItems != null">
        review_items = #{reviewItems,jdbcType=VARCHAR},
      </if>
      <if test="reviewIds != null">
        review_ids = #{reviewIds,jdbcType=VARCHAR},
      </if>
      <if test="reviewRemark != null">
        review_remark = #{reviewRemark,jdbcType=VARCHAR},
      </if>
      <if test="reviewItemNames != null">
        review_item_names = #{reviewItemNames,jdbcType=VARCHAR},
      </if>
      <if test="reviewMode != null">
        review_mode = #{reviewMode,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createOperId != null">
        create_oper_id = #{createOperId,jdbcType=BIGINT},
      </if>
      <if test="createOperName != null">
        create_oper_name = #{createOperName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateOperId != null">
        update_oper_id = #{updateOperId,jdbcType=BIGINT},
      </if>
      <if test="updateOperName != null">
        update_oper_name = #{updateOperName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.extracme.evcard.membership.core.model.MemberIdentityDocument">
    update ${siacSchema}.member_identity_document
    set mid = #{mid,jdbcType=VARCHAR},
    identity_no = #{identityNo,jdbcType=VARCHAR},
    identity_type = #{identityType,jdbcType=INTEGER},
    name = #{name,jdbcType=VARCHAR},
    expire_type = #{expireType,jdbcType=INTEGER},
    expiration_date = #{expirationDate,jdbcType=VARCHAR},
    identity_card_img_url = #{identityCardImgUrl,jdbcType=VARCHAR},
    reverse_identity_card_img_url = #{reverseIdentityCardImgUrl,jdbcType=VARCHAR},
    hold_idcard_pic_url = #{holdIdcardPicUrl,jdbcType=VARCHAR},
    face_recognition_img_url = #{faceRecognitionImgUrl,jdbcType=VARCHAR},
    cert_input_type = #{certInputType,jdbcType=INTEGER},
    authentication_status = #{authenticationStatus,jdbcType=INTEGER},
    submit_time = #{submitTime,jdbcType=TIMESTAMP},
    submit_appkey = #{submitAppkey,jdbcType=VARCHAR},
    review_user = #{reviewUser,jdbcType=VARCHAR},
    review_time = #{reviewTime,jdbcType=TIMESTAMP},
    review_items = #{reviewItems,jdbcType=VARCHAR},
    review_ids = #{reviewIds,jdbcType=VARCHAR},
    review_remark = #{reviewRemark,jdbcType=VARCHAR},
    review_item_names = #{reviewItemNames,jdbcType=VARCHAR},
    review_mode = #{reviewMode,jdbcType=INTEGER},
    status = #{status,jdbcType=INTEGER},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    create_oper_id = #{createOperId,jdbcType=BIGINT},
    create_oper_name = #{createOperName,jdbcType=VARCHAR},
    update_time = #{updateTime,jdbcType=TIMESTAMP},
    update_oper_id = #{updateOperId,jdbcType=BIGINT},
    update_oper_name = #{updateOperName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectOneByMid" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ${siacSchema}.member_identity_document
    where mid = #{mid,jdbcType=VARCHAR} and status = 0
    limit 1
  </select>

  <select id="selectOneOtherByIdentityNo" parameterType="java.lang.String" resultType="com.extracme.evcard.membership.core.dto.MemberStatusDto">
    select
    d.mid as mid,
    m.mobile_phone as mobilePhone,
    m.AUTH_ID as authId,
    m.account_status as accountStatus,
    d.authentication_status as identityReviewStatus,
    m.license_review_status as licenseReviewStatus,
    m.review_status as reviewStatus,
    m.authentication_status as authenticationStatus
    from ${siacSchema}.member_identity_document d
    left join ${siacSchema}.membership_info m on d.mid = m.mid and m.membership_type = 0
    where d.identity_no = #{identityNo,jdbcType=VARCHAR} and d.status = 0
    <if test="mid != null and mid!=''">
      AND d.mid != #{mid,jdbcType=VARCHAR}
    </if>
    AND m.account_status != 2
    <if test="idTypes != null and idTypes.size>0 ">
      AND d.identity_type in
      <foreach collection="idTypes" item="item" separator="," open="(" close=")">
        #{item}
      </foreach>
    </if>
    limit 1
  </select>

</mapper>