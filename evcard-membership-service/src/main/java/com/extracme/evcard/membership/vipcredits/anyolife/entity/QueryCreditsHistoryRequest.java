package com.extracme.evcard.membership.vipcredits.anyolife.entity;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 积分账户履历查询参数
 * <AUTHOR>
 * @Discription
 * @date 2020/8/12
 */
@Data
@NoArgsConstructor
public class QueryCreditsHistoryRequest extends BaseCreditsRequest {
    /**
     * 页码，默认1
     */
    private Integer page;

    /**
     * 页面大小，默认10
     */
    private Integer pageSize;

    /**
     * 查询时间范围：开始时间yyyyMMddHHmmss
     */
    private String begDate;

    /**
     * 查询时间范围：结束时间yyyyMMddHHmmss
     */
    private String endDate;

    /**
     * 积分变动类别 01消费 02获取
     */
    private String creditType;

    /**
     * 1过期 2不过期 缺省则查询全部
     */
    private String expireType;

    public QueryCreditsHistoryRequest(String channel, String uid){
        this.setUid(uid);
        this.setChannel(channel);
    }

}
