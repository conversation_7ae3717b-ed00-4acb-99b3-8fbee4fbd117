package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.dto.LicenseReAuthListQueryDto;
import com.extracme.evcard.membership.core.model.DriverLicenseElementsReauthenticateList;

import java.util.List;

public interface DriverLicenseElementsReauthenticateListMapper {
    int deleteByPrimaryKey(Long id);

    int insert(DriverLicenseElementsReauthenticateList record);

    int insertSelective(DriverLicenseElementsReauthenticateList record);

    DriverLicenseElementsReauthenticateList selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DriverLicenseElementsReauthenticateList record);

    int updateByPrimaryKey(DriverLicenseElementsReauthenticateList record);

    int disableLastRecordsByUserId(DriverLicenseElementsReauthenticateList record);

    DriverLicenseElementsReauthenticateList selectLatestRecordByUserId(Long id);

    int updateWhenReAuthByPrimaryKey(DriverLicenseElementsReauthenticateList record);

    List<DriverLicenseElementsReauthenticateList> selectReAuthenticateList(LicenseReAuthListQueryDto queryDto);
}