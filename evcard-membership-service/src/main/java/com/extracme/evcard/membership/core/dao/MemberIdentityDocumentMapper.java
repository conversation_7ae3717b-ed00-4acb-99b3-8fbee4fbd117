package com.extracme.evcard.membership.core.dao;

import com.extracme.evcard.membership.core.dto.MemberStatusDto;
import com.extracme.evcard.membership.core.model.MemberIdentityDocument;
import com.extracme.evcard.membership.core.model.MemberIdentityDocumentExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MemberIdentityDocumentMapper {
    int deleteByPrimaryKey(Long id);

    int insert(MemberIdentityDocument record);

    int insertSelective(MemberIdentityDocument record);

    List<MemberIdentityDocument> selectByExample(MemberIdentityDocumentExample example);

    MemberIdentityDocument selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MemberIdentityDocument record, @Param("example") MemberIdentityDocumentExample example);

    int updateByExample(@Param("record") MemberIdentityDocument record, @Param("example") MemberIdentityDocumentExample example);

    int updateByPrimaryKeySelective(MemberIdentityDocument record);

    int updateByPrimaryKey(MemberIdentityDocument record);

    MemberIdentityDocument selectOneByMid(String mid);

    MemberStatusDto selectOneOtherByIdentityNo(@Param("identityNo") String identityNo, @Param("idTypes")List<Integer> idTypes,
                                               @Param("mid")String mid);
}