spring.profiles=local
apollo.bootstrap.namespaces=config,aliyunConfig,third

#datasource
spring.datasource.druid.url = ******************************************************************************************************************************************************************************************************
spring.datasource.druid.username = membership
spring.datasource.druid.password = sg5q3Pi8

#dubbo
dubbo.registry.address = zookeeper://************:2181
dubbo.registry.timeout=100000
dubbo.registry.register=true

dubbo.provider.timeout=14000
dubbo.provider.threadpool=fixed
dubbo.provider.threads=100
dubbo.provider.cluster=failfast
dubbo.provider.loadbalance=roundrobin

#elasticjob
elasticjob.regCenter.serverLists = ************:2181

#apollo
apollo.meta = http://apollo-st.evcard.vip:48080

#logger
spring.logging.filename = evcard-membership
logging.level.org.apache.dubbo = debug
logging.level.org.apache.zookeeper = warn
logging.level.root = info
logging.level.dao = info


host = evcard-st-wan.redis.rds.aliyuncs.com
port = 6379
pwd = Wp4uJK*Vc3v2
timeout = 10000
maxWaitMillis = -1
minIdle = 8
maxIdle = 16
maxTotal = 100

#contract
md.inner.api.baseUrl=https://md-dev.evcard.vip

