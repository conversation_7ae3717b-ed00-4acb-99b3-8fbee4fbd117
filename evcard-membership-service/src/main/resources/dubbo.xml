<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
	http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">


<!-- dubbo应用 此处根据服务端和消费端自行配置,提供方应用信息，用于计算依赖关系 -->
    <dubbo:application name="${dubbo.provider.application.name}" owner="${dubbo.owner}" organization="${dubbo.organization}" logger="slf4j"/>

	<dubbo:service interface="com.extracme.evcard.membership.credit.service.IMemberShipTagServ" ref="memberShipTagServ"/>
    <dubbo:service interface="com.extracme.evcard.membership.invitation.service.IMemberShipInvitationServ" ref="memberShipInvitationServ"/>
    <dubbo:service interface="com.extracme.evcard.membership.core.service.IMemberShipService" ref="memberShipService"/>
    <dubbo:service interface="com.extracme.evcard.membership.contract.service.IMemberShipContractServ" ref="memberShipContractServ" timeout="50000" />
    <dubbo:service interface="com.extracme.evcard.membership.core.service.IMemberOperateServ" ref="memberOperateServ"/>
    <dubbo:service interface="com.extracme.evcard.membership.core.service.IMemberDepositService" ref="memberDepositService" protocol="dubbo"/>
    <dubbo:service interface="com.extracme.evcard.membership.core.service.IMemberFeedbackService" ref="memberFeedbackService"/>
    <dubbo:service interface="com.extracme.evcard.membership.core.service.IThirdPartMemberService" ref="thirdPartMemberService"/>
    <dubbo:service interface="com.extracme.evcard.membership.core.service.IMembershipWrapService" ref="membershipWrapService"/>
    <dubbo:service interface="com.extracme.evcard.membership.core.service.IMemberCardService" ref="memberCardService"/>
    <dubbo:service interface="com.extracme.evcard.membership.core.service.IBaseInfoService" ref="baseInfoService"/>
    <dubbo:service interface="com.extracme.evcard.membership.core.service.IPlatformService" ref="platformService"/>
    <dubbo:service interface="com.extracme.evcard.membership.core.service.agency.IAgencyDiscountService" ref="agencyDiscountService"/>
    <dubbo:service interface="com.extracme.evcard.membership.core.service.agency.IMembershipAgencyService" ref="membershipAgencyService"/>
    <dubbo:service interface="com.extracme.evcard.membership.core.service.IMemberForIdsService" ref="memberForIdsService"/>
    <dubbo:service interface="com.extracme.evcard.membership.core.service.IMemberReviewService" ref="memberReviewService"/>
    <dubbo:service interface="com.extracme.evcard.membership.credit.service.IMemberPointsService" ref="memberPointsService"/>
    <dubbo:service interface="com.extracme.evcard.membership.core.service.IMemberProvisionService" ref="memberProvisionService"/>
    <dubbo:service interface="com.extracme.evcard.membership.core.service.IWeChatH5Service" ref="weChatH5Service"/>
    <dubbo:service interface="com.extracme.evcard.membership.core.service.IMemberActivityService" ref="memberActivityService"/>

    <dubbo:service interface="com.extracme.evcard.membership.core.service.IMemberFaceVerifyService" ref="memberFaceVerifyService"/>
    <dubbo:service interface="com.extracme.evcard.membership.core.service.IMemberCertificationService" ref="memberCertificationService"/>
    <dubbo:service interface="com.extracme.evcard.membership.core.service.IChannelBlacklistService" ref="channelBlacklistService"/>
    <dubbo:service interface="com.extracme.evcard.membership.core.service.IShortlinkManagementService" ref="shortlinkManagementService">
        <dubbo:method name="batchCreateWxQrCode" timeout="600000" />
    </dubbo:service>

    <dubbo:reference interface="com.extracme.evcard.rpc.vehicle.service.IVehicleModelService" id="vehicleModelService" check="false"/>

    <dubbo:reference interface="com.extracme.evcard.rpc.pay.service.IMemAccountService" id="memAccountService"  check="false"/>

    <dubbo:reference interface="com.extracme.evcard.rpc.coupon.service.ICouponServ" id="couponServ"  check="false"/>

    <dubbo:reference interface="com.extracme.evcard.rpc.messagepush.service.IMessagepushServ" id="messageServ" protocol="dubbo" check="false"/>

    <!-- 支付服务-阿里  -->
    <dubbo:reference interface="com.extracme.evcard.rpc.pay.service.IAliService" id="aliService" check="false"/>
    <dubbo:reference interface="com.extracme.evcard.rpc.pay.service.IBillService" id="billService" check="false"/>
<!--    <dubbo:reference interface="com.extracme.evcard.rpc.pay.service.ISuperviseService" id="superviseService" protocol="dubbo" check="false"/>-->

    <dubbo:reference interface="com.extracme.evcard.rpc.shop.service.IShopService" id="shopService" check="false"/>
    <!-- 企业会员相关服务 -->
    <dubbo:reference interface="com.extracme.evcard.bvm.service.IAgencyPriceService" id="agencyPriceService" check="false"/>
    <!-- 企业会员相关服务 -->
    <dubbo:reference interface="com.extracme.evcard.bvm.service.IAgencyMemberService" id="agencyMemberService" check="false"/>

    <!--车辆服务-->
    <dubbo:reference interface="com.extracme.evcard.rpc.vehicle.service.IVehicleService" id="vehicleService" check="false"/>
    <!-- 车辆故障上报 -->
    <dubbo:reference interface="com.extracme.evcard.rpc.vehicle.service.IVehicleFeedbackService" id="vehicleFeedbackService" check="false"/>
    <!-- 充电桩故障上报 -->
    <dubbo:reference interface="com.extracme.evcard.rpc.shop.service.IShopFeedbackService" id="shopFeedbackService" check="false"/>
    <!-- 神策埋点 -->
    <dubbo:reference interface="com.extracme.evcard.rpc.messagepush.service.ISensorsdataService" id="sensorsdataService" check="false"/>
    <dubbo:reference interface="com.extracme.evcard.rpc.messagepush.service.ISensorsdataOldService" id="sensorsdataOldService" protocol="dubbo" check="false"/>

    <!-- 活动相关服务 -->
    <dubbo:reference interface="com.extracme.evcard.activity.dubboService.IDubboShareService" id="dubboShareService" check="false"/>
    <dubbo:reference interface="com.extracme.evcard.activity.dubboService.IUserThirdBindService" id="userThirdBindService" check="false"/>
    <dubbo:reference interface="com.extracme.evcard.activity.dubboService.IRegisterRewardService" id="registerRewardService" check="false"/>


    <!-- 订单相关服务 -->
    <dubbo:reference interface="com.extracme.evcard.rpc.order.service.IOrderService" id="orderService" check="false"/>
    <dubbo:reference interface="com.extracme.evcard.rpc.order.service.IPickupOrderService" id="pickupOrderService" check="false"/>
    <dubbo:reference interface="com.extracme.evcard.rpc.order.service.ICancelOrderService" id="cancelOrderService" check="false"/>
    <dubbo:reference interface="com.extracme.evcard.rpc.order.service.IReturnVehicleService" id="returnVehicleService" check="false"/>
    <dubbo:reference interface="com.extracme.evcard.rpc.order.service.IOrderPriceService" id="orderPriceService" check="false"/>
    <dubbo:reference interface="com.extracme.evcard.rpc.base.service.IOrgService" id="orgService" check="false"/>
    <dubbo:reference interface="com.extracme.evcard.rpc.order.service.IntergationOrderService" id="intergationOrderService" check="false"/>
    <!-- 调度相关服务 -->
    <dubbo:reference interface="com.evcard.ids.provider.api.service.IdsMembershipInfoServiceProvider" id="idsMembershipInfoServiceProvider" check="false"/>
    <dubbo:reference interface="com.evcard.ids.provider.api.service.IdsSendVehicleServiceProvider" id="idsSendVehicleServiceProvider" protocol="dubbo" check="false"/>
    <!-- 风控订单相关服务 -->
    <dubbo:reference interface="com.extracme.evcard.rpc.riskorder.service.IRiskOrderService" id="riskOrderService" check="false"/>
    <!--维修相关服务 -->
    <dubbo:reference interface="com.evcard.mtc.provider.service.IMtcRepairTaskService" id="mtcRepairTaskService" check="false"/>
    <!--维修相关服务 -->
    <dubbo:reference interface="com.extracme.evcard.ccs.provider.service.IRefundBaseInfoService" id="refundBaseInfoService" check="false"/>
    <dubbo:reference interface="com.extracme.evcard.sts.rpc.service.AppConfigRpcService" id="appConfigRpcService" check="false"/>
    <!-- 单点 -->
    <dubbo:reference interface="com.extracme.evcard.sso.service.SsoUserService" id="ssoUserService" check="false"/>
    <dubbo:reference interface="com.extracme.evcard.saic.service.ISaicMemberService" id="saicMemberService" protocol="dubbo" check="false"/>

    <dubbo:reference interface="com.extracme.evcard.flow.service.IUserCarbonEmissionsService" id="userCarbonEmissionsService" protocol="dubbo" check="false"/>
    <dubbo:reference interface="com.extracme.evcard.rpc.vipcard.service.ISuixiangCardCdkService" id="suixiangCardCdkService" check="false"/>
</beans>
