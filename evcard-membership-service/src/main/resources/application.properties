spring.profiles.active=siac
spring.application.name=evcard-membership-rpc
spring.main.allow-bean-definition-overriding=true
spring.main.web-application-type=none
server.shutdown=graceful

logging.config=classpath:logback-spring.xml
logging.file.max-history=30
logging.file.max-size=100MB
logging.queue.size=512

#datasource
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.druid.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.druid.max-active=100
spring.datasource.druid.initial-size=1
spring.datasource.druid.filters=dbauditlog
#spring.datasource.druid.max-wait\u00C8\u00B1\u00CA\u00A1\u00D6\u00B5\u00CE\u00AA-1 \u00BF\u00C9\u00C4\u00DC\u00B5\u00BC\u00D6\u00C2\u00BB\u00F1\u00C8\u00A1\u00C1\u00AC\u00BD\u00D3\u00CF\u00DF\u00B3\u00CC\u00D2\u00BB\u00D6\u00B1\u00B5\u00C8\u00B4\u00FD
#spring.datasource.druid.max-wait=10000
spring.datasource.druid.min-idle=1
spring.datasource.druid.time-between-eviction-runs-millis=300000
spring.datasource.druid.min-evictable-idle-time-millis=300000
spring.datasource.druid.validation-query=select 'x'
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-on-return=false
spring.datasource.druid.pool-prepared-statements=true
spring.datasource.druid.max-open-prepared-statements=50
spring.datasource.druid.max-pool-prepared-statement-per-connection-size=20


#dubbo
dubbo.application.logger=slf4j
dubbo.scan.base-packages=com.extracme.evcard.membership.core.service,com.extracme.evcard.membership.credit.service,com.extracme.evcard.membership.invitation.service,com.extracme.evcard.membership.contract.service
dubbo.registry.register=true
dubbo.registry.file=./dubboregistry/dubbo-registry.properties
dubbo.protocol.id=dubbo
dubbo.protocol.name=dubbo
dubbo.protocol.heartbeat=15000
dubbo.provider.timeout=14000
dubbo.provider.threadpool=fixed
dubbo.provider.threads=100
dubbo.provider.protocol=dubbo
dubbo.provider.cluster=failfast
dubbo.provider.loadbalance=roundrobin
dubbo.provider.server=netty
dubbo.consumer.lazy=true
dubbo.consumer.check=false
dubbo.consumer.cluster=failfast
dubbo.provider.port=20889
#\u5F85\u786E\u8BA4\u670D\u52A1\u7AEF\u8BBE\u7F6Econnections\u662F\u5426\u6709\u6548&\u5408\u7406\u7684\u53D6\u503C
dubbo.consumer.connections=1
dubbo.provider.application.name=evcard-membership-service
dubbo.owner=wuyibo
dubbo.organization=extracme
dubbo.registry.address=zookeeper://**************:2181
dubbo.registry.timeout=40000


#mybatis
mybatis.mapper-locations=classpath*:com/extracme/evcard/membership/**/dao/*.xml
mybatis.configuration.variables.issSchema=iss
mybatis.configuration.variables.siacSchema=siac
mybatis.configuration.variables.isvSchema=isv
mybatis.configuration.variables.iplatSchema=iplat
mybatis.configuration.variables.orgIdCheck=0

mybatis.configuration.cache-enabled=true
mybatis.configuration.lazy-loading-enabled=false
mybatis.configuration.multiple-result-sets-enabled=true
mybatis.configuration.use-column-label=true
mybatis.configuration.use-generated-keys=true
mybatis.configuration.auto-mapping-behavior=partial
mybatis.configuration.default-executor-type=simple
mybatis.configuration.default-statement-timeout=25
mybatis.configuration.safe-row-bounds-enabled=false
mybatis.configuration.map-underscore-to-camel-case=false
mybatis.configuration.local-cache-scope=session
mybatis.configuration.jdbc-type-for-null=other
mybatis.configuration.lazy-load-trigger-methods=equals,clone,hashCode,toString
mybatis.configuration.logPrefix=dao.


logging.level.org.apache.dubbo=warn
logging.level.org.apache.zookeeper=warn
logging.level.root=debug

#redis
#spring.redis.timeout=10000
#spring.redis.database=0
#spring.redis.jedis.pool.max-active=20
#spring.redis.jedis.pool.max-idle=5
#spring.redis.jedis.pool.min-idle=2


#elasticjob
elasticjob.regCenter.serverLists=**************:2181
elasticjob.regCenter.namespace=elastic-job


#apollo
app.id= ${spring.application.name}
apollo.cluster=evcard
#apollo enable during spring boot applyInitializers
apollo.bootstrap.enabled=true
apollo.bootstrap.namespaces=redis,config,application,aliyunConfig,third
apollo.bootstrap.eagerLoad.enabled=true

md.inner.api.searchContractById=/mdadmin/order/inner/searchContractById

md.inner.api.queryOrderPayInfo=/mdadmin/pay/inner/queryOrderPayInfo

md.inner.api.getOrderCountByStatusUrl=/mdadmin/order/inner/getOrderCountByStatus

md.inner.api.searchCityConfiguration=/mdadmin/store/inner/searchCityConfiguration

md.inner.api.getMemberDepositInfos=/mdadmin/deposit/inner/getMemberDepositInfos

md.inner.api.saveBlackListLogUrl=/mdadmin/order/saveBlackListLog

oss.url.pre=https://evcard.oss-cn-shanghai.aliyuncs.com/


