spring.profiles=siac

#datasource
#spring.datasource.druid.url=jdbc\:mysql\://*************\:4195/iss?useUnicode\=true&characterEncoding\=UTF-8&autoReconnect\=true&failOverReadOnly\=false&zeroDateTimeBehavior\=convertToNull&allowMultiQueries=true
#spring.datasource.druid.username=membership
#spring.datasource.druid.password=sg5q3Pi8

#dubbo
dubbo.registry.address=zookeeper://zk-st.evcard.vip:2181

#redis
#spring.redis.host=************
#spring.redis.port=6379

#elasticjob
elasticjob.regCenter.serverLists=zk-st.evcard.vip:2181

#apollo
apollo.meta=http://apollo-st.evcard.vip:48080

#logger
logging.level.org.apache.dubbo=warn
logging.level.org.apache.zookeeper=warn
logging.level.root=debug
logging.level.dao=debug

#contract
md.inner.api.baseUrl=https://md-st.evcard.vip/