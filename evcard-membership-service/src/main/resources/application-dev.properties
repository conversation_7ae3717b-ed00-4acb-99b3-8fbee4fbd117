spring.profiles=dev

#datasource
spring.datasource.druid.url=jdbc\:mysql\://evcard-dev-lan.mysql.rds.aliyuncs.com\:3306/siac?useUnicode\=true&characterEncoding\=UTF-8&autoReconnect\=true&failOverReadOnly\=false&zeroDateTimeBehavior\=convertToNull
spring.datasource.druid.username=devuser
spring.datasource.druid.password=blk2ZsEB

#dubbo
dubbo.registry.address=zookeeper://zk-dev.evcard.vip:2181

#redis
#spring.redis.host=************
#spring.redis.port=6379

#elasticjob
elasticjob.regCenter.serverLists=zk-dev.evcard.vip:2181

#apollo
apollo.meta= http://apollo-test.evcard.vip:58080

#md.topic
ons.md.contract.topic=MD_CONTRACT_TOPIC_DEV
#md.contract
md.inner.api.baseUrl=https://md-dev.evcard.vip/