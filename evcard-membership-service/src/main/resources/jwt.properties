# JWT配置文件
# JWT密钥（生产环境应使用更安全的密钥管理方式）
jwt.secret=evcard-membership-jwt-secret-key-2025-very-long-and-secure-key-for-production-use

# JWT签发者
jwt.issuer=evcard-membership

# 访问令牌有效期（秒）- 默认2小时
jwt.access-token.expiration=7200

# 刷新令牌有效期（秒）- 默认30天
jwt.refresh-token.expiration=2592000

# 是否启用JWT（默认启用）
jwt.enabled=true

# 是否允许多设备登录
jwt.multi-device.enabled=true

# 单用户最大会话数
jwt.max-sessions-per-user=5

# 黑名单默认过期时间（秒）- 默认1天
jwt.blacklist.default-expiry=86400

# Token即将过期提醒时间（分钟）
jwt.expiry-warning.minutes=30
