<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN" "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd" >
<generatorConfiguration >
  <!-- <classPathEntry location="libs/mysql-connector-java-5.1.35.jar"/> -->
  <classPathEntry location="C:\Users\<USER>\.m2\repository\mysql\mysql-connector-java\8.0.29\mysql-connector-java-8.0.29.jar" />
  <context id="context1" >
    <commentGenerator>
      <property name="suppressDate" value="true"/>
      <property name="suppressAllComments" value="true" />
    </commentGenerator>
    <jdbcConnection driverClass="com.mysql.jdbc.Driver" connectionURL="************************************************************" userId="devuser" password="blk2ZsEB" />
    <javaModelGenerator targetPackage="com.extracme.evcard.membership.core.model" targetProject="src\main\java" />
    <sqlMapGenerator targetPackage="com.extracme.evcard.membership.core.dao" targetProject="src\main\java"/>
    <javaClientGenerator targetPackage="com.extracme.evcard.membership.core.dao" targetProject="src\main\java" type="XMLMAPPER" />
    <table schema="siac" tableName="membership_ocr_call_log"  enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false">
    </table>
  </context>
</generatorConfiguration>
