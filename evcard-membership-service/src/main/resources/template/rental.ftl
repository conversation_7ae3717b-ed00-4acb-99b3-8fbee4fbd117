<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>
    <#if bo.type == 5>
        结算单
    <#elseif bo.type == 6>
        租车单
    </#if>
    </title>
</head>
<body>
<div>
    <div align="center" style="font-weight: bold;">
        <#if bo.type == 5>
            结算单
        <#elseif bo.type == 6>
        租车单
        </#if>
    </div>

    <p style="text-align: right;margin-right: 20px;">订单号：${bo.orderBaseInfo.orderNo}</p>

    <#if bo.orderBaseInfo.thirdOrderNo?has_content>
        <p style="text-align: right;margin-right: 20px;">渠道单号：${bo.orderBaseInfo.thirdOrderNo}</p>
    </#if>

    <table align="center">
        <tr>
            <th colspan="2" bgcolor="#F2F2F2">承租人信息</th>
        </tr>
        <tr>
            <td style="width: 200px;">姓名</td>
            <td style="width: 330px;">${bo.name}</td>
        </tr>
        <tr>
            <td>联系电话</td>
            <td>${bo.mobile}</td>
        </tr>
        <tr>
            <td>证件类型</td>
            <td>
            <#if bo.idCardType == 1>
                居民身份证
            <#elseif bo.idCardType== 2>
                外籍护照
            <#elseif bo.idCardType== 3>
                港澳通行证
            <#elseif bo.idCardType== 4>
                台湾通行证
            <#elseif bo.idCardType== 5>
                军人身份证
            </#if>
            </td>
        </tr>
        <tr>
            <td>证件号码</td>
            <td>${bo.idCardNo}</td>
        </tr>
        <tr>
            <th colspan="2" bgcolor="#F2F2F2">订单信息</th>
        </tr>
        <tr>
            <td>车牌号</td>
            <td>${bo.orderBaseInfo.vehicleNo}</td>
        </tr>
        <tr>
            <td>车辆信息</td>
            <td>${bo.orderBaseInfo.carInfoName}</td>
        </tr>
        <tr>
            <td>取车时间</td>
            <td>${bo.orderBaseInfo.pickUpDate}</td>
        </tr>
        <tr>
            <td>还车时间</td>
            <td>${bo.orderBaseInfo.returnDate}</td>
        </tr>
        <tr>
            <td>租期</td>
            <td>${bo.orderBaseInfo.rentDay}</td>
        </tr>
        <tr>
            <td>取车门店/地点</td>
            <td>${bo.orderBaseInfo.pickUpStoreName}</td>
        </tr>
        <tr>
            <td>还车门店/地点</td>
            <td>${bo.orderBaseInfo.returnStoreName}</td>
        </tr>
        <tr>
            <td>取车方式</td>
            <td>${bo.orderBaseInfo.pickCarWay}</td>
        </tr>
        <tr>
            <td>还车方式</td>
            <td>${bo.orderBaseInfo.returnCarWay}</td>
        </tr>

        <tr>
            <td>燃油类型</td>
            <td>
            <#if bo.orderBaseInfo.oilType == 0>
                纯电动
            <#elseif bo.orderBaseInfo.oilType == 1>
                油电混动
            <#elseif bo.orderBaseInfo.oilType == 2>
                燃油电池
            <#elseif bo.orderBaseInfo.oilType == 3>
                燃油
            <#elseif bo.orderBaseInfo.oilType == 4>
                氢能源
            </#if>
            </td>
        </tr>

         <#if bo.orderBaseInfo.oilType == 0 || bo.orderBaseInfo.oilType == 1 || bo.orderBaseInfo.oilType == 2>
         <tr>
             <td>额定电池包容量</td>
             <td>${bo.orderBaseInfo.electricCapacity}</td>
         </tr>
         </#if>

         <#if bo.orderBaseInfo.oilType == 1 || bo.orderBaseInfo.oilType == 2 || bo.orderBaseInfo.oilType == 3>
         <tr>
             <td>额定油箱容量</td>
             <td>${bo.orderBaseInfo.oilCapacity}</td>
         </tr>
         </#if>

          <tr>
              <#if bo.type == 5>
                 <td>收车活体检测</td>
              <#elseif bo.type == 6>
                  <td>发车活体检测</td>
              </#if>

            <#if bo.orderBaseInfo.faceLiveResult == 0>
                <td>未检测</td>
            <#elseif bo.orderBaseInfo.faceLiveResult == 1>
                <td>通过（${bo.orderBaseInfo.faceLiveTime}）</td>
            <#elseif bo.orderBaseInfo.faceLiveResult == 2>
                <td>未通过（${bo.orderBaseInfo.faceLiveTime}）</td>
            </#if>
          </tr>


        <tr>
            <th colspan="2" bgcolor="#F2F2F2">押金信息</th>
        </tr>
        <tr>
            <td>车辆押金</td>
            <td>${bo.depositBaseInfo.vehicleDepositDepositWay}</td>
        </tr>
        <tr>
            <td>违章押金</td>
            <td>${bo.depositBaseInfo.illegalDepositDepositWay}</td>
        </tr>
        <tr>
            <th colspan="2" bgcolor="#F2F2F2">费用信息</th>
        </tr>

        <#if bo.orderFeesBaseInfos?? && bo.orderFeesBaseInfos?size != 0>
            <#list bo.orderFeesBaseInfos as item>
             <tr>
                 <td>${item.name}</td>
                 <td>${item.amount}元</td>
             </tr>
            </#list>
        </#if>



        <td colspan="2">
            <span>本人已阅读并同意《租车单》《验车单》以及《车辆租赁合同》的全部内容，并特别注意《车辆租赁合同》中第1.4、2.2、2.5、2.8、2.10、2.12、3.5、3.11、3.12、4.2、4.3、7.2、7.4条的条款约定，出租方及其工作人员已对全部租车文件中的条款作出了必要、全面的解释和说明。</span><br>
            <span>乙方：</span><img src="" alt="" style="width: 200px;margin-bottom: -60px;"><br>
            <p style="margin-top: 80px;margin-bottom: 30px;">日期：${bo.todayDate}</p>
        </td>
    </table>
</div>
</body>
<style>
    table {
        border-collapse: collapse;
    }
    th, td {
        border: 1px solid black;
        padding: 8px;
        text-align: left;
    }
</style>
</html>