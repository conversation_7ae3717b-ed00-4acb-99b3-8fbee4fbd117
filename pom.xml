<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.extracme.evcard</groupId>
		<artifactId>evcard</artifactId>
		<version>3.0.1</version>
	</parent>
	<artifactId>evcard-membership-rpc</artifactId>
	<version>4.12.62</version>
	<packaging>pom</packaging>

	<modules>
		<module>evcard-membership-service</module>
		<module>evcard-membership-service-api</module>
	</modules>

	<dependencies>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>1.2.83</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.13.3</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
			<version>2.13.3</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
			<version>2.13.3</version>
		</dependency>
	</dependencies>

	<repositories>
		<repository>
			<id>nexus</id>
			<name>Nexus</name>
			<url>http://artifactory.evcard.vip:8081/artifactory/maven-virtual/</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
	</repositories>

    <pluginRepositories>
		<pluginRepository>
			<id>nexus</id>
			<name>Nexus</name>
			<url>http://artifactory.evcard.vip:8081/artifactory/maven-virtual/</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</pluginRepository>
	</pluginRepositories>
</project>
