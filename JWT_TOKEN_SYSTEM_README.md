# JWT Token体系重构文档

## 概述

本次重构完全重新设计了用户登录token体系，从原有的简单UUID token升级为基于JWT的安全token机制，同时保持向后兼容性。

## 主要改进

### 安全性提升
- ✅ 实现JWT token机制，支持签名验证
- ✅ 添加token过期时间管理（访问令牌2小时，刷新令牌30天）
- ✅ 实现token刷新机制（refresh token）
- ✅ 添加token签名验证，防止篡改
- ✅ 实现token撤销/黑名单机制，防止重放攻击
- ✅ 支持用户级别和设备级别的token撤销

### 实用性提升
- ✅ 支持多设备登录管理
- ✅ 提供token验证拦截器
- ✅ 实现用户权限信息在token中的安全存储
- ✅ 支持设备类型识别（移动端、网页端、小程序、API等）
- ✅ 提供清晰的token生命周期管理

### 架构改进
- ✅ 统一的token管理服务
- ✅ 模块化设计，易于扩展
- ✅ 完整的错误处理和日志记录
- ✅ 向后兼容现有系统

## 核心组件

### 1. JWT工具类 (`JwtTokenUtil`)
- 负责JWT token的生成、验证和解析
- 支持自定义声明和过期时间
- 提供安全的签名验证

### 2. Token黑名单管理器 (`TokenBlacklistManager`)
- 基于Redis的token黑名单机制
- 支持单个token、用户级别、设备级别的撤销
- 自动清理过期的黑名单记录

### 3. 增强Token生成器 (`EnhancedTokenGenerator`)
- 整合JWT和传统token生成逻辑
- 自动提取设备信息和登录环境
- 提供向后兼容的API

### 4. Token服务 (`ITokenService` & `TokenServiceImpl`)
- 统一的token管理服务
- 支持token生成、验证、刷新、撤销
- 多设备会话管理

### 5. Token验证拦截器 (`TokenValidationInterceptor`)
- 自动验证HTTP请求中的JWT token
- 提取用户信息到请求上下文
- 支持多种token传递方式

## 配置说明

### JWT配置 (`jwt.properties`)
```properties
# JWT密钥
jwt.secret=evcard-membership-jwt-secret-key-2025-very-long-and-secure-key-for-production-use

# 访问令牌有效期（秒）- 默认2小时
jwt.access-token.expiration=7200

# 刷新令牌有效期（秒）- 默认30天
jwt.refresh-token.expiration=2592000

# 是否启用JWT（默认启用）
jwt.enabled=true

# 是否允许多设备登录
jwt.multi-device.enabled=true

# 单用户最大会话数
jwt.max-sessions-per-user=5
```

## API接口

### 1. Token验证
```http
POST /api/token/validate
Content-Type: application/json

{
    "accessToken": "eyJhbGciOiJIUzI1NiJ9..."
}
```

### 2. Token刷新
```http
POST /api/token/refresh
Content-Type: application/json

{
    "refreshToken": "eyJhbGciOiJIUzI1NiJ9..."
}
```

### 3. 用户登出
```http
POST /api/token/logout
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...
Content-Type: application/json

{
    "reason": "用户主动登出"
}
```

### 4. 获取活跃会话
```http
GET /api/token/sessions
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...
```

### 5. 踢出设备
```http
POST /api/token/kickout
Authorization: Bearer eyJhbGciOiJIUzI1NiJ9...
Content-Type: application/json

{
    "deviceId": "device_12345",
    "reason": "安全原因"
}
```

## 使用示例

### 1. 在登录流程中生成JWT token
```java
@Autowired
private EnhancedTokenGenerator tokenGenerator;

// 在ThirdLoginAbstract中已经集成
public String getToken(ThirdLoginContext context) throws BusinessException {
    // 自动根据配置选择JWT或传统token
    // 如果jwt.enabled=true，将生成JWT token
    // 如果jwt.enabled=false，将生成传统token（向后兼容）
}
```

### 2. 验证token
```java
@Autowired
private ITokenService tokenService;

public void validateUserToken(String accessToken) {
    TokenValidationResult result = tokenService.validateAccessToken(accessToken);
    if (result.isValid()) {
        Long userId = result.getUserId();
        String mid = result.getMid();
        // 使用用户信息
    } else {
        // 处理验证失败
        throw new BusinessException(-1, result.getErrorMessage());
    }
}
```

### 3. 在拦截器中使用
```java
// TokenValidationInterceptor已经自动处理token验证
// 在Controller中可以直接获取用户信息
@RestController
public class UserController {
    
    @GetMapping("/user/info")
    public UserInfo getUserInfo(HttpServletRequest request) {
        Long userId = TokenValidationInterceptor.getUserIdFromRequest(request);
        String mid = TokenValidationInterceptor.getUserMidFromRequest(request);
        // 使用用户信息
    }
}
```

## 数据库变更

### 新增表：user_token_session
```sql
-- 用户Token会话表（支持多设备登录）
CREATE TABLE user_token_session (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    mid VARCHAR(64) NOT NULL COMMENT '用户MID',
    app_key VARCHAR(64) NOT NULL COMMENT '应用标识',
    device_id VARCHAR(128) NOT NULL COMMENT '设备ID',
    device_type VARCHAR(32) NOT NULL COMMENT '设备类型',
    device_info TEXT COMMENT '设备信息',
    access_token_jti VARCHAR(64) NOT NULL COMMENT '访问令牌JTI',
    refresh_token_jti VARCHAR(64) NOT NULL COMMENT '刷新令牌JTI',
    access_token_expire_time DATETIME NOT NULL COMMENT '访问令牌过期时间',
    refresh_token_expire_time DATETIME NOT NULL COMMENT '刷新令牌过期时间',
    last_active_time DATETIME COMMENT '最后活跃时间',
    login_ip VARCHAR(64) COMMENT '登录IP',
    login_location VARCHAR(128) COMMENT '登录地理位置',
    status INT DEFAULT 0 COMMENT '状态：0正常 1已注销 2已过期 3被踢出',
    logout_reason VARCHAR(256) COMMENT '注销原因',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_deleted INT DEFAULT 0 COMMENT '是否删除：0未删除 1已删除',
    
    INDEX idx_user_id (user_id),
    INDEX idx_device_id (device_id),
    INDEX idx_access_token_jti (access_token_jti),
    INDEX idx_refresh_token_jti (refresh_token_jti),
    INDEX idx_status (status),
    INDEX idx_expire_time (access_token_expire_time, refresh_token_expire_time)
);
```

## 迁移策略

### 阶段1：并行运行（当前阶段）
- JWT功能已实现，但默认关闭
- 通过配置`jwt.enabled=false`继续使用传统token
- 可以在测试环境开启JWT进行验证

### 阶段2：逐步迁移
- 在部分应用中启用JWT：`jwt.enabled=true`
- 监控JWT token的性能和稳定性
- 收集用户反馈

### 阶段3：全面切换
- 所有应用启用JWT
- 保留传统token验证逻辑作为降级方案
- 逐步清理传统token相关代码

## 监控和运维

### 1. 关键指标监控
- Token生成成功率
- Token验证成功率
- Token刷新成功率
- 黑名单命中率
- 活跃会话数量

### 2. 日志记录
- 所有token操作都有详细日志
- 包含用户ID、设备ID、操作类型、结果等信息
- 便于问题排查和安全审计

### 3. 定时任务
- 每小时清理过期会话
- 每天深度清理过期记录
- 自动维护系统性能

## 安全考虑

### 1. 密钥管理
- 生产环境应使用更安全的密钥管理方式
- 定期轮换JWT签名密钥
- 密钥长度至少256位

### 2. 传输安全
- 所有token传输必须使用HTTPS
- 避免在URL参数中传递token
- 优先使用Authorization头

### 3. 存储安全
- 客户端应安全存储refresh token
- 定期清理本地存储的过期token
- 避免在日志中记录完整token

## 性能优化

### 1. Redis优化
- 黑名单使用Redis存储，支持高并发
- 设置合理的过期时间，自动清理
- 使用连接池提高性能

### 2. 数据库优化
- 会话表添加合适的索引
- 定期清理过期记录
- 考虑分表策略应对大数据量

### 3. 缓存策略
- 用户信息可以缓存减少数据库查询
- Token验证结果可以短时间缓存
- 合理设置缓存过期时间

## 故障处理

### 1. JWT验证失败
- 检查密钥配置是否正确
- 确认token格式是否正确
- 查看详细错误日志

### 2. Redis连接问题
- 黑名单功能可能受影响
- 实现降级策略，临时跳过黑名单检查
- 及时修复Redis连接

### 3. 数据库问题
- 会话管理功能可能受影响
- 可以临时关闭会话记录功能
- 保证核心token验证功能正常

## 后续规划

### 1. 功能增强
- 支持token权限范围（scope）
- 实现更细粒度的权限控制
- 添加token使用统计和分析

### 2. 安全加强
- 实现token绑定IP地址
- 添加异常登录检测
- 支持双因子认证

### 3. 性能优化
- 实现token验证缓存
- 优化数据库查询性能
- 支持分布式部署

## 总结

本次JWT token体系重构大幅提升了系统的安全性和可用性，同时保持了良好的向后兼容性。通过模块化设计和完善的配置管理，系统可以平滑地从传统token迁移到JWT token，为后续的功能扩展和安全加强奠定了坚实的基础。
